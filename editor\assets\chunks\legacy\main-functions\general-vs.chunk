
precision highp float;
#include <legacy/input-standard>
#include <builtin/uniforms/cc-global>
#include <legacy/local-batch>
#include <legacy/input-standard>
#include <legacy/fog-vs>
#include <legacy/shadow-map-vs>

in vec4 a_color;
#if HAS_SECOND_UV
  in vec2 a_texCoord1;
#endif

out vec3 v_position;
out vec3 v_normal;
out vec3 v_tangent;
out vec3 v_bitangent;
out vec2 v_uv;
out vec2 v_uv1;
out vec4 v_color;

vec4 vert () {
  StandardVertInput In;
  CCVertInput(In);

  mat4 matWorld, matWorldIT;
  CCGetWorldMatrixFull(matWorld, matWorldIT);

  vec4 pos = matWorld * In.position;

  v_position = pos.xyz;
  v_normal = normalize((matWorldIT * vec4(In.normal, 0.0)).xyz);
  v_tangent = normalize((matWorld * vec4(In.tangent.xyz, 0.0)).xyz);
  v_bitangent = cross(v_normal, v_tangent) * In.tangent.w; // note the cross order

  v_uv = a_texCoord;
  #if HAS_SECOND_UV
    v_uv1 = a_texCoord1;
  #endif
  v_color = a_color;

  CC_TRANSFER_FOG(pos);
  CC_TRANSFER_SHADOW(pos);

  return cc_matProj * (cc_matView * matWorld) * In.position;
}
