System.register(["./gc-object-CKHc4SnS.js","./index-C5lmLqDW.js","./global-exports-CR3GRnjt.js"],(function(t){"use strict";var e,n,i,o;return{setters:[function(t){e=t._,n=t.a},function(t){i=t.V},function(t){o=t.c}],execute:function(){var _,r,s=t("E",function(){function t(t,e){this.target=null,this.currentTarget=null,this.eventPhase=0,this.propagationStopped=!1,this.propagationImmediateStopped=!1,this.type=t,this.bubbles=!!e}var e=t.prototype;return e.unuse=function(){this.type=t.NO_TYPE,this.target=null,this.currentTarget=null,this.eventPhase=t.NONE,this.propagationStopped=!1,this.propagationImmediateStopped=!1},e.reuse=function(t,e){this.type=t,this.bubbles=e||!1},e.isStopped=function(){return this.propagationStopped||this.propagationImmediateStopped},e.getCurrentTarget=function(){return this.currentTarget},e.getType=function(){return this.type},t}());s.NO_TYPE="no_type",s.TOUCH="touch",s.MOUSE="mouse",s.KEYBOARD="keyboard",s.ACCELERATION="acceleration",s.NONE=0,s.CAPTURING_PHASE=1,s.AT_TARGET=2,s.BUBBLING_PHASE=3,o.Event=s,t("S",_),function(t){t.TOUCH_START="touch-start",t.TOUCH_MOVE="touch-move",t.TOUCH_END="touch-end",t.TOUCH_CANCEL="touch-cancel",t.MOUSE_DOWN="mouse-down",t.MOUSE_MOVE="mouse-move",t.MOUSE_UP="mouse-up",t.MOUSE_WHEEL="mouse-wheel",t.MOUSE_ENTER="mouse-enter",t.MOUSE_LEAVE="mouse-leave",t.KEY_DOWN="keydown",t.KEY_UP="keyup",t.DEVICEMOTION="devicemotion",t.TRANSFORM_CHANGED="transform-changed",t.SCENE_CHANGED_FOR_PERSISTS="scene-changed-for-persists",t.SIZE_CHANGED="size-changed",t.ANCHOR_CHANGED="anchor-changed",t.COLOR_CHANGED="color-changed",t.CHILD_ADDED="child-added",t.CHILD_REMOVED="child-removed",t.PARENT_CHANGED="parent-changed",t.NODE_DESTROYED="node-destroyed",t.LAYER_CHANGED="layer-changed",t.SIBLING_ORDER_CHANGED="sibling-order-changed"}(_||t("S",_={})),t("I",r),function(t){t.TOUCH_START="touch-start",t.TOUCH_MOVE="touch-move",t.TOUCH_END="touch-end",t.TOUCH_CANCEL="touch-cancel",t.MOUSE_DOWN="mouse-down",t.MOUSE_MOVE="mouse-move",t.MOUSE_UP="mouse-up",t.MOUSE_LEAVE="mouse-leave-window",t.MOUSE_ENTER="mouse-enter-window",t.MOUSE_WHEEL="mouse-wheel",t.KEY_DOWN="keydown",t.KEY_PRESSING="key-pressing",t.KEY_UP="keyup",t.DEVICEMOTION="devicemotion",t.GAMEPAD_INPUT="gamepad-input",t.GAMEPAD_CHANGE="gamepad-change",t.HANDLE_INPUT="handle-input",t.HANDLE_POSE_INPUT="handle-pose-input",t.HMD_POSE_INPUT="hmd-pose-input",t.HANDHELD_POSE_INPUT="handheld-pose-input"}(r||t("I",r={})),o.SystemEventType=_;var u=t("a",function(t){function n(e,n){var i;return(i=t.call(this,_.DEVICEMOTION,n)||this).acc=e,i}return e(n,t),n}(s));s.EventAcceleration=u;var c=t("b",function(t){function i(e,n,i){var o;return"boolean"==typeof n&&(n=n?_.KEY_DOWN:_.KEY_UP),(o=t.call(this,n,i)||this).rawEvent=void 0,o._isPressed=n!==_.KEY_UP,"number"==typeof e?o.keyCode=e:(o.keyCode=e.keyCode,o.rawEvent=e),o.windowId=0,o}return e(i,t),n(i,[{key:"isPressed",get:function(){return this._isPressed}}]),i}(s));s.EventKeyboard=c;var h=t("c",function(t){function _(e,n,i,o){var r;return(r=t.call(this,e,n)||this).movementX=0,r.movementY=0,r.windowId=0,r.preventSwallow=!1,r._button=_.BUTTON_MISSING,r._x=0,r._y=0,r._prevX=0,r._prevY=0,r._scrollX=0,r._scrollY=0,r._eventType=e,i&&(r._prevX=i.x,r._prevY=i.y),r.windowId=null!=o?o:r.windowId,r}e(_,t);var r=_.prototype;return r.setScrollData=function(t,e){this._scrollX=t,this._scrollY=e},r.getScrollX=function(){return this._scrollX},r.getScrollY=function(){return this._scrollY},r.setLocation=function(t,e){this._x=t,this._y=e},r.getLocation=function(t){return t||(t=new i),i.set(t,this._x,this._y),t},r.getLocationInView=function(t){return t||(t=new i),i.set(t,this._x,o.view._designResolutionSize.height-this._y),t},r.getUILocation=function(t){return t||(t=new i),i.set(t,this._x,this._y),o.view._convertToUISpace(t),t},r.getPreviousLocation=function(t){return t||(t=new i),i.set(t,this._prevX,this._prevY),t},r.getUIPreviousLocation=function(t){return t||(t=new i),i.set(t,this._prevX,this._prevY),o.view._convertToUISpace(t),t},r.getDelta=function(t){return t||(t=new i),i.set(t,this._x-this._prevX,this._y-this._prevY),t},r.getDeltaX=function(){return this._x-this._prevX},r.getDeltaY=function(){return this._y-this._prevY},r.getUIDelta=function(t){t||(t=new i);var e=o.view;return i.set(t,(this._x-this._prevX)/e.getScaleX(),(this._y-this._prevY)/e.getScaleY()),t},r.getUIDeltaX=function(){return(this._x-this._prevX)/o.view.getScaleX()},r.getUIDeltaY=function(){return(this._y-this._prevY)/o.view.getScaleY()},r.setButton=function(t){this._button=t},r.getButton=function(){return this._button},r.getLocationX=function(){return this._x},r.getLocationY=function(){return this._y},r.getUILocationX=function(){var t=o.view,e=t.getViewportRect();return(this._x-e.x)/t.getScaleX()},r.getUILocationY=function(){var t=o.view,e=t.getViewportRect();return(this._y-e.y)/t.getScaleY()},n(_,[{key:"eventType",get:function(){return this._eventType}}]),_}(s));h.BUTTON_MISSING=-1,h.BUTTON_LEFT=0,h.BUTTON_RIGHT=2,h.BUTTON_MIDDLE=1,h.BUTTON_4=3,h.BUTTON_5=4,h.BUTTON_6=5,h.BUTTON_7=6,h.BUTTON_8=7,s.EventMouse=h;var E,a=new i,T=t("d",function(t){function n(e,n,i,o){var _;return void 0===o&&(o=[]),(_=t.call(this,i,n)||this).touch=null,_.simulate=!1,_.windowId=0,_.preventSwallow=!1,_._eventCode=i,_._touches=e||[],_._allTouches=o,_}e(n,t);var o=n.prototype;return o.getEventCode=function(){return this._eventCode},o.getTouches=function(){return this._touches},o.getAllTouches=function(){return this._allTouches},o.setLocation=function(t,e){this.touch&&this.touch.setTouchInfo(this.touch.getID(),t,e)},o.getLocation=function(t){return this.touch?this.touch.getLocation(t):new i},o.getUILocation=function(t){return this.touch?this.touch.getUILocation(t):new i},o.getLocationInView=function(t){return this.touch?this.touch.getLocationInView(t):new i},o.getPreviousLocation=function(t){return this.touch?this.touch.getPreviousLocation(t):new i},o.getStartLocation=function(t){return this.touch?this.touch.getStartLocation(t):new i},o.getUIStartLocation=function(t){return this.touch?this.touch.getUIStartLocation(t):new i},o.getID=function(){return this.touch?this.touch.getID():null},o.getDelta=function(t){return this.touch?this.touch.getDelta(t):new i},o.getUIDelta=function(t){return this.touch?this.touch.getUIDelta(t):new i},o.getDeltaX=function(){return this.touch?this.touch.getDelta(a).x:0},o.getDeltaY=function(){return this.touch?this.touch.getDelta(a).y:0},o.getLocationX=function(){return this.touch?this.touch.getLocationX():0},o.getLocationY=function(){return this.touch?this.touch.getLocationY():0},n}(s));T.MAX_TOUCHES=5,s.EventTouch=T,t("e",function(t){function n(e,n){var i;return(i=t.call(this,e,!1)||this).gamepad=n,i}return e(n,t),n}(s)),t("f",function(t){function n(e,n){var i;return(i=t.call(this,e,!1)||this).handleInputDevice=n,i}return e(n,t),n}(s)),t("g",function(t){function n(e,n){var i;return(i=t.call(this,e,!1)||this).hmdInputDevice=n,i}return e(n,t),n}(s)),t("h",function(t){function n(e,n){var i;return(i=t.call(this,e,!1)||this).handheldInputDevice=n,i}return e(n,t),n}(s)),t("K",E),function(t){t[t.NONE=0]="NONE",t[t.MOBILE_BACK=6]="MOBILE_BACK",t[t.BACKSPACE=8]="BACKSPACE",t[t.TAB=9]="TAB",t[t.ENTER=13]="ENTER",t[t.SHIFT_LEFT=16]="SHIFT_LEFT",t[t.CTRL_LEFT=17]="CTRL_LEFT",t[t.ALT_LEFT=18]="ALT_LEFT",t[t.PAUSE=19]="PAUSE",t[t.CAPS_LOCK=20]="CAPS_LOCK",t[t.ESCAPE=27]="ESCAPE",t[t.SPACE=32]="SPACE",t[t.PAGE_UP=33]="PAGE_UP",t[t.PAGE_DOWN=34]="PAGE_DOWN",t[t.END=35]="END",t[t.HOME=36]="HOME",t[t.ARROW_LEFT=37]="ARROW_LEFT",t[t.ARROW_UP=38]="ARROW_UP",t[t.ARROW_RIGHT=39]="ARROW_RIGHT",t[t.ARROW_DOWN=40]="ARROW_DOWN",t[t.INSERT=45]="INSERT",t[t.DELETE=46]="DELETE",t[t.DIGIT_0=48]="DIGIT_0",t[t.DIGIT_1=49]="DIGIT_1",t[t.DIGIT_2=50]="DIGIT_2",t[t.DIGIT_3=51]="DIGIT_3",t[t.DIGIT_4=52]="DIGIT_4",t[t.DIGIT_5=53]="DIGIT_5",t[t.DIGIT_6=54]="DIGIT_6",t[t.DIGIT_7=55]="DIGIT_7",t[t.DIGIT_8=56]="DIGIT_8",t[t.DIGIT_9=57]="DIGIT_9",t[t.KEY_A=65]="KEY_A",t[t.KEY_B=66]="KEY_B",t[t.KEY_C=67]="KEY_C",t[t.KEY_D=68]="KEY_D",t[t.KEY_E=69]="KEY_E",t[t.KEY_F=70]="KEY_F",t[t.KEY_G=71]="KEY_G",t[t.KEY_H=72]="KEY_H",t[t.KEY_I=73]="KEY_I",t[t.KEY_J=74]="KEY_J",t[t.KEY_K=75]="KEY_K",t[t.KEY_L=76]="KEY_L",t[t.KEY_M=77]="KEY_M",t[t.KEY_N=78]="KEY_N",t[t.KEY_O=79]="KEY_O",t[t.KEY_P=80]="KEY_P",t[t.KEY_Q=81]="KEY_Q",t[t.KEY_R=82]="KEY_R",t[t.KEY_S=83]="KEY_S",t[t.KEY_T=84]="KEY_T",t[t.KEY_U=85]="KEY_U",t[t.KEY_V=86]="KEY_V",t[t.KEY_W=87]="KEY_W",t[t.KEY_X=88]="KEY_X",t[t.KEY_Y=89]="KEY_Y",t[t.KEY_Z=90]="KEY_Z",t[t.NUM_0=96]="NUM_0",t[t.NUM_1=97]="NUM_1",t[t.NUM_2=98]="NUM_2",t[t.NUM_3=99]="NUM_3",t[t.NUM_4=100]="NUM_4",t[t.NUM_5=101]="NUM_5",t[t.NUM_6=102]="NUM_6",t[t.NUM_7=103]="NUM_7",t[t.NUM_8=104]="NUM_8",t[t.NUM_9=105]="NUM_9",t[t.NUM_MULTIPLY=106]="NUM_MULTIPLY",t[t.NUM_PLUS=107]="NUM_PLUS",t[t.NUM_SUBTRACT=109]="NUM_SUBTRACT",t[t.NUM_DECIMAL=110]="NUM_DECIMAL",t[t.NUM_DIVIDE=111]="NUM_DIVIDE",t[t.F1=112]="F1",t[t.F2=113]="F2",t[t.F3=114]="F3",t[t.F4=115]="F4",t[t.F5=116]="F5",t[t.F6=117]="F6",t[t.F7=118]="F7",t[t.F8=119]="F8",t[t.F9=120]="F9",t[t.F10=121]="F10",t[t.F11=122]="F11",t[t.F12=123]="F12",t[t.NUM_LOCK=144]="NUM_LOCK",t[t.SCROLL_LOCK=145]="SCROLL_LOCK",t[t.SEMICOLON=186]="SEMICOLON",t[t.EQUAL=187]="EQUAL",t[t.COMMA=188]="COMMA",t[t.DASH=189]="DASH",t[t.PERIOD=190]="PERIOD",t[t.SLASH=191]="SLASH",t[t.BACK_QUOTE=192]="BACK_QUOTE",t[t.BRACKET_LEFT=219]="BRACKET_LEFT",t[t.BACKSLASH=220]="BACKSLASH",t[t.BRACKET_RIGHT=221]="BRACKET_RIGHT",t[t.QUOTE=222]="QUOTE",t[t.SHIFT_RIGHT=2e3]="SHIFT_RIGHT",t[t.CTRL_RIGHT=2001]="CTRL_RIGHT",t[t.ALT_RIGHT=2002]="ALT_RIGHT",t[t.NUM_ENTER=2003]="NUM_ENTER"}(E||t("K",E={}));var I=new i,p=t("T",function(){function t(t,e,n){void 0===n&&(n=0),this._point=new i,this._prevPoint=new i,this._lastModified=0,this._id=0,this._startPoint=new i,this._startPointCaptured=!1,this.setTouchInfo(n,t,e)}var e=t.prototype;return e.getLocation=function(t){return t||(t=new i),t.set(this._point.x,this._point.y),t},e.getLocationX=function(){return this._point.x},e.getLocationY=function(){return this._point.y},e.getUILocation=function(t){return t||(t=new i),t.set(this._point.x,this._point.y),o.view._convertToUISpace(t),t},e.getUILocationX=function(){var t=o.view,e=t.getViewportRect();return(this._point.x-e.x)/t.getScaleX()},e.getUILocationY=function(){var t=o.view,e=t.getViewportRect();return(this._point.y-e.y)/t.getScaleY()},e.getPreviousLocation=function(t){return t||(t=new i),t.set(this._prevPoint.x,this._prevPoint.y),t},e.getUIPreviousLocation=function(t){return t||(t=new i),t.set(this._prevPoint.x,this._prevPoint.y),o.view._convertToUISpace(t),t},e.getStartLocation=function(t){return t||(t=new i),t.set(this._startPoint.x,this._startPoint.y),t},e.getUIStartLocation=function(t){return t||(t=new i),t.set(this._startPoint.x,this._startPoint.y),o.view._convertToUISpace(t),t},e.getDelta=function(t){return t||(t=new i),t.set(this._point),t.subtract(this._prevPoint),t},e.getUIDelta=function(t){t||(t=new i),I.set(this._point),I.subtract(this._prevPoint);var e=o.view;return t.set(e.getScaleX(),e.getScaleY()),i.divide(t,I,t),t},e.getLocationInView=function(t){return t||(t=new i),t.set(this._point.x,o.view._designResolutionSize.height-this._point.y),t},e.getPreviousLocationInView=function(t){return t||(t=new i),t.set(this._prevPoint.x,o.view._designResolutionSize.height-this._prevPoint.y),t},e.getStartLocationInView=function(t){return t||(t=new i),t.set(this._startPoint.x,o.view._designResolutionSize.height-this._startPoint.y),t},e.getID=function(){return this._id},e.setTouchInfo=function(t,e,n){void 0===t&&(t=0),void 0===e&&(e=0),void 0===n&&(n=0),this._prevPoint=this._point,this._point=new i(e||0,n||0),this._id=t,this._startPointCaptured||(this._startPoint=new i(this._point),this._startPointCaptured=!0)},e.setPoint=function(t,e){"object"==typeof t?(this._point.x=t.x,this._point.y=t.y):(this._point.x=t||0,this._point.y=e||0),this._lastModified=o.game.frameStartTime},e.setPrevPoint=function(t,e){this._prevPoint="object"==typeof t?new i(t.x,t.y):new i(t||0,e||0),this._lastModified=o.game.frameStartTime},e.clone=function(){var e=this.getID();this.getStartLocation(I);var n=new t(I.x,I.y,e);return this.getLocation(I),n.setPoint(I.x,I.y),this.getPreviousLocation(I),n.setPrevPoint(I),n},n(t,[{key:"lastModified",get:function(){return this._lastModified}}]),t}());o.Touch=p}}}));
