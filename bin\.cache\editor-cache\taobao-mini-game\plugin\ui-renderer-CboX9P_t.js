System.register(["./gc-object-D18ulfCO.js","./index-Y4La_nfG.js","./global-exports-CLZKKIY2.js","./scene-ArUG4OfI.js","./component-CsuvAQKv.js","./factory-BOc5khhM.js","./deprecated-D5UVm7fE.js","./debug-view-BP17WHcy.js","./device-manager-B8lyUDPi.js","./buffer-barrier-q_79u36H.js","./director-8iUu7HD2.js","./deprecated-Bf8XgTPJ.js","./prefab-BQYc0LyR.js","./pipeline-state-manager-DQyhxoC_.js","./node-event-DTNosVQv.js","./renderer-CZheciPr.js"],(function(t){"use strict";var e,i,n,r,s,a,o,h,f,u,c,l,_,d,y,g,D,v,p,E,m,A,S,R,x,C,B,b,k,I,M,w,O,T,P,N,F,L,H,V,U,z,G,j,W,Y,K,q,X,Z,J,Q,$,tt,et;return{setters:[function(t){e=t.k,i=t.g,n=t.w,r=t.a,s=t.P,a=t._,o=t.h,h=t.I,f=t.z,u=t.a5,c=t.r,l=t.b},function(t){_=t.m,d=t.C,y=t.V,g=t.b,D=t.M,v=t.k,p=t.c,E=t.d,m=t.$,A=t.N,S=t.F,R=t.a,x=t.i,C=t.L,B=t.z,b=t.s,k=t.t,I=t.H,M=t.J},function(t){w=t.c},function(t){O=t.d,T=t.b},function(t){P=t.C},null,null,null,null,function(t){N=t.a,F=t.A,L=t.F,H=t.c,V=t.B,U=t.b,z=t.M,G=t.I,j=t.ag,W=t.a6,Y=t.ah,K=t.m,q=t.af,X=t.ai,Z=t.aj},function(t){J=t.d,Q=t.D,$=t.u},null,null,null,function(t){tt=t.N},function(t){et=t.R}],execute:function(){t({a:dt,g:_t});var it,nt=N.ATTR_POSITION,rt=N.ATTR_COLOR,st=N.ATTR_TEX_COORD,at=N.ATTR_COLOR2,ot=t("m",[new F(nt,L.RGB32F)]),ht=t("v",[new F(nt,L.RGB32F),new F(rt,L.RGBA32F)]),ft=t("d",[new F(nt,L.RGB32F),new F(st,L.RG32F),new F(rt,L.RGBA32F)]),ut=t("j",[new F(nt,L.RGB32F),new F(st,L.RG32F),new F(rt,L.RGBA8,!0)]),ct=[new F(nt,L.RGB32F),new F(st,L.RG32F),new F(rt,L.RGBA32F),new F(at,L.RGBA32F)],lt=t("k",[new F(nt,L.RGB32F),new F(st,L.RG32F),new F(rt,L.RGBA8,!0),new F(at,L.RGBA8,!0)]);function _t(t){for(var e=0,i=0;i<t.length;i++){var n=t[i];e+=H[n.format].count}return e}function dt(t){for(var e=0,i=0;i<t.length;i++){var n=t[i];e+=H[n.format].size}return e}w.internal.vfmtPosUvColor=ft,w.internal.vfmtPosUvTwoColor=ct,w.internal.vfmtPosUvColor4B=ut,w.internal.vfmtPosUvTwoColor4B=lt,t("o",Object.freeze({__proto__:null,getAttributeStride:dt,getComponentPerVertex:_t,vfmt:ot,vfmtPosColor:ht,vfmtPosUvColor:ft,vfmtPosUvColor4B:ut,vfmtPosUvTwoColor:ct,vfmtPosUvTwoColor4B:lt})),function(t){t[t.byteOffset=0]="byteOffset",t[t.vertexOffset=1]="vertexOffset",t[t.indexOffset=2]="indexOffset",t[t.dirty=3]="dirty",t[t.count=4]="count"}(it||(it={}));var yt,gt,Dt,vt,pt,Et,mt=t("n",function(){var t=s.prototype;function s(){this._byteOffset=0,this._vertexOffset=0,this._indexOffset=0,this._dirty=!1,this._floatsPerVertex=0,this._vData=null,this._iData=null,this._vertexFormatBytes=0,this._initVDataCount=0,this._initIDataCount=0,this._attributes=null,this._iaPool=[],this._iaInfo=null,this._nextFreeIAHandle=0,this.initSharedBuffer(),this.syncSharedBufferToNative()}return t.initSharedBuffer=function(){},t.syncSharedBufferToNative=function(){},t.initialize=function(t,n,r,s){this._initVDataCount=r,this._initIDataCount=s,this._attributes=n,this.floatsPerVertex=dt(n)>>2,e(this._initVDataCount/this._floatsPerVertex<65536,i(9005)),this.vData&&this.iData||(this.vData=new Float32Array(this._initVDataCount),this.iData=new Uint16Array(this._initIDataCount)),this._iaPool.push(this.createNewIA(t))},t.reset=function(){this._nextFreeIAHandle=0,this.dirty=!1},t.destroy=function(){this.reset(),this._attributes=null,this._iaInfo=null,this.vData=null,this.iData=null;for(var t=0;t<this._iaPool.length;++t){var e=this._iaPool[t],i=e.vertexBuffers[0];i&&i.destroy();var n=e.indexBuffer;n&&n.destroy(),e.ia.destroy()}this._iaPool.length=0},t.setDirty=function(){this.dirty=!0},t.request=function(){return n(9002),!1},t.requireFreeIA=function(t){return this._iaPool.length<=this._nextFreeIAHandle&&this._iaPool.push(this.createNewIA(t)),this._iaPool[this._nextFreeIAHandle++].ia},t.recycleIA=function(t){for(var e=this._iaPool,i=0;i<this._nextFreeIAHandle;++i)if(t===e[i].ia){var n=e[i];return e[i]=e[--this._nextFreeIAHandle],void(e[this._nextFreeIAHandle]=n)}},t.checkCapacity=function(t,e){var i=(this.vertexOffset+t)*this._floatsPerVertex,n=this.indexOffset+e;return!(i>this._initVDataCount||n>this._initIDataCount)},t.uploadBuffers=function(){if(0!==this.byteOffset&&this._dirty){var t=_.__isWebIOS14OrIPadOS14Env,e=t?this._nextFreeIAHandle:1;if(t&&e/this._iaPool.length<.5){for(var i=e/.5,n=this._iaPool.length-1;n>=i;n--){var r=this._iaPool[n];r.vertexBuffers[0]&&r.vertexBuffers[0].destroy(),r.indexBuffer&&r.indexBuffer.destroy(),r.ia.destroy()}this._iaPool.length=i}for(var s=this.byteOffset,a=this.indexOffset,o=0;o<e;++o){var h=this._iaPool[o],f=new Float32Array(this.vData.buffer,0,s>>2),u=new Uint16Array(this.iData.buffer,0,a),c=h.vertexBuffers[0];s>c.size&&c.resize(s),c.update(f),2*a>h.indexBuffer.size&&h.indexBuffer.resize(2*a),h.indexBuffer.update(u)}this.dirty=!1}},t.createNewIA=function(t){var e,i,n;if(_.__isWebIOS14OrIPadOS14Env||!this._iaPool[0]){var r=this._vertexFormatBytes=this._floatsPerVertex*Float32Array.BYTES_PER_ELEMENT,s=Uint16Array.BYTES_PER_ELEMENT,a=t.createBuffer(new V(U.VERTEX|U.TRANSFER_DST,z.HOST|z.DEVICE,r,r));n=t.createBuffer(new V(U.INDEX|U.TRANSFER_DST,z.HOST|z.DEVICE,s,s)),i=[a],this._iaInfo=new G(this._attributes,i,n),e=t.createInputAssembler(this._iaInfo)}else e=t.createInputAssembler(this._iaInfo),i=this._iaInfo.vertexBuffers,n=this._iaInfo.indexBuffer;return{ia:e,vertexBuffers:i,indexBuffer:n}},r(s,[{key:"attributes",get:function(){return this._attributes}},{key:"vertexFormatBytes",get:function(){return this._vertexFormatBytes}},{key:"byteOffset",get:function(){return this._byteOffset},set:function(t){this._byteOffset=t}},{key:"vertexOffset",get:function(){return this._vertexOffset},set:function(t){this._vertexOffset=t}},{key:"indexOffset",get:function(){return this._indexOffset},set:function(t){this._indexOffset=t}},{key:"dirty",get:function(){return this._dirty},set:function(t){this._dirty=t}},{key:"floatsPerVertex",get:function(){return this._floatsPerVertex},set:function(t){this._floatsPerVertex=t}},{key:"vData",get:function(){return this._vData},set:function(t){this._vData=t}},{key:"iData",get:function(){return this._iData},set:function(t){this._iData=t}},{key:"nativeObj",get:function(){return this._nativeObj}},{key:"sharedBuffer",get:function(){return this._sharedBuffer}}]),s}()),At=function(){function t(t,e){this._buffers=[],this._device=t,this._attributes=e,this._floatsPerVertex=dt(e)>>2,this._vertexFormatBytes=this._floatsPerVertex*Float32Array.BYTES_PER_ELEMENT}var e=t.prototype;return e.initialize=function(){},e.reset=function(){},e.request=function(){},e.appendBuffers=function(){},e.uploadBuffers=function(){},e.destroy=function(){this._attributes.length=0},r(t,[{key:"attributes",get:function(){return this._attributes}},{key:"vertexFormatBytes",get:function(){return this._vertexFormatBytes}},{key:"floatsPerVertex",get:function(){return this._floatsPerVertex}}]),t}(),St=new s((function(){return{offset:0,length:0}}),32),Rt=function(){function t(t,i,n,r,s,a){this.vertexAccessor=t,this.bufferId=i,this.meshBuffer=n,this.vertexOffset=r,this.vb=s,this.indexCount=a,this._ib=new Uint16Array(a),e(n===t.getMeshBuffer(i))}return t.prototype.setIndexBuffer=function(){},r(t,[{key:"ib",get:function(){return this._ib}}]),t}(),xt=t("i",function(t){function e(i,n,r,s){var a;return(a=t.call(this,i,n)||this)._freeLists=[],a._vCount=0,a._iCount=0,a._id=0,a._vCount=r||Math.floor(1024*f.BATCHER2D_MEM_INCREMENT/a._vertexFormatBytes),a._iCount=s||a._vCount*e.IB_SCALE,a._id=e.generateID(),a._allocateBuffer(),a}a(e,t);var i=e.prototype;return i.destroy=function(){for(var e=0;e<this._buffers.length;++e){this._buffers[e].destroy();for(var i=this._freeLists[e],n=0;n<i.length;++n)St.free(i[n])}this._buffers.length=0,this._freeLists.length=0,t.prototype.destroy.call(this)},i.reset=function(){for(var t=0;t<this._buffers.length;++t){var e=this._buffers[t];e.indexOffset=0,e.reset()}},i.getVertexBuffer=function(t){return this._buffers[t].vData},i.getIndexBuffer=function(t){return this._buffers[t].iData},i.getMeshBuffer=function(t){return this._buffers[t]},i.uploadBuffers=function(){for(var t=0;t<this._buffers.length;++t){var e=this._freeLists[t][0],i=this._buffers[t];(!e||e.length<i.vData.byteLength)&&i.uploadBuffers()}},i.appendIndices=function(t,e){var i=this._buffers[t];if(e.length){var n=i.indexOffset+e.length;if(i.iData.length<n){var r=Math.floor(1.25*n),s=new Uint16Array(r);s.set(i.iData),i.iData=s}i.iData.set(e,i.indexOffset),i.indexOffset+=e.length}},i.allocateChunk=function(t,e){var i=t*this.vertexFormatBytes;if(t>this._vCount||e>this._iCount)return o(9004,i),null;for(var n,r=null,s=0,a=-1,h=null,f=0;f<this._buffers.length;++f){r=this._buffers[f],n=this._freeLists[f];for(var u=0;u<n.length;++u)if(n[u].length>=i){h=n[u],s=f,a=u;break}if(h)break}if(h||(s=this._allocateBuffer(),(r=this._buffers[s])&&(a=0,h=this._freeLists[s][a])),h){var c=h.offset/this.vertexFormatBytes,l=new Float32Array(r.vData.buffer,h.offset,i>>2).fill(0);return this._allocateChunkFromEntry(s,a,h,i),new Rt(this,s,r,c,l,e)}return null},i.recycleChunk=function(t){var e=this._freeLists[t.bufferId],i=this._buffers[t.bufferId],n=t.vertexOffset*this.vertexFormatBytes,r=t.vb.byteLength;if(0!==r){for(var s=!1,a=0,o=null,h=e[a];h&&h.offset<n;)o=h,h=e[++a];if(o&&0==n-(o.offset+o.length)&&(o.length+=r,n=o.offset,r=o.length,h&&h.offset-(n+r)==0&&(o.length+=h.length,e.splice(a,1),St.free(h),h=null),s=!0),!s&&h){if(0==h.offset-(n+r))h.offset=n,h.length+=r;else{var f=St.alloc();f.offset=n,f.length=r,e.splice(a,0,f)}s=!0}if(s)n+r===i.byteOffset&&(i.byteOffset=n);else{var u=St.alloc();u.offset=n,u.length=r,e.push(u)}}},i._allocateChunkFromEntry=function(t,e,i,n){var r=i.length-n,s=i.offset+n,a=this._buffers[t];a.byteOffset<s&&(a.byteOffset=s),h(r>=0,9004,t,i.offset,i.length),0===r?(this._freeLists[t].splice(e,1),St.free(i)):(i.offset+=n,i.length=r)},i._allocateBuffer=function(){h(this._buffers.length===this._freeLists.length,9003);var t=new mt,e=this._vCount*this._floatsPerVertex;t.initialize(this._device,this._attributes,e,this._iCount),this._buffers.push(t);var i=St.alloc();i.offset=0,i.length=t.vData.byteLength;var n=[i];return this._freeLists.push(n),J.root.batcher2D.syncMeshBuffersToNative(this.id,this._buffers),this._buffers.length-1},e.generateID=function(){return e.ID_COUNT++},r(e,[{key:"id",get:function(){return this._id}}]),e}(At));xt.IB_SCALE=4,xt.ID_COUNT=0,function(t){t[t.DrawInfoType=0]="DrawInfoType",t[t.VertDirty=1]="VertDirty",t[t.BooleanValues=2]="BooleanValues",t[t.Stride=3]="Stride",t[t.Count=4]="Count"}(yt||(yt={})),function(t){t[t.BufferID=0]="BufferID",t[t.AccessorID=1]="AccessorID",t[t.Count=2]="Count"}(gt||(gt={})),function(t){t[t.VertexOffset=0]="VertexOffset",t[t.IndexOffset=1]="IndexOffset",t[t.VBCount=2]="VBCount",t[t.IBCount=3]="IBCount",t[t.DataHash=4]="DataHash",t[t.Count=5]="Count"}(Dt||(Dt={})),t("f",vt),function(t){t[t.COMP=0]="COMP",t[t.MODEL=1]="MODEL",t[t.MIDDLEWARE=2]="MIDDLEWARE",t[t.SUB_NODE=3]="SUB_NODE"}(vt||t("f",vt={})),t("e",function(){function t(t){this._accId=-1,this._bufferId=-1,this._vertexOffset=0,this._indexOffset=0,this._vb=null,this._ib=null,this._vData=null,this._iData=null,this._vertDirty=!1,this._vbCount=0,this._ibCount=0,this._dataHash=0,this._isMeshBuffer=!1,this._material=null,this._texture=null,this._sampler=null,this._stride=0,this._useLocal=!1,this._model=null,this._drawInfoType=vt.COMP,this._subNode=null,this._render2dBuffer=null,this.init(t);var e=this._nativeObj.getAttrSharedBufferForJS(),i=0;this._uint8SharedBuffer=new Uint8Array(e,i,yt.Count),i+=yt.Count*Uint8Array.BYTES_PER_ELEMENT,this._uint16SharedBuffer=new Uint16Array(e,i,gt.Count),i+=gt.Count*Uint16Array.BYTES_PER_ELEMENT,this._uint32SharedBuffer=new Uint32Array(e,i,Dt.Count)}var e=t.prototype;return e.init=function(){},e.clear=function(){this._bufferId=0,this._vertexOffset=0,this._indexOffset=0,this._vertDirty=!1},e.setAccId=function(t){this._accId=t},e.setBufferId=function(t){this._bufferId=t},e.setAccAndBuffer=function(t,e){this._bufferId=e,this._accId=t},e.setVertexOffset=function(t){this._vertexOffset=t},e.setIndexOffset=function(t){this._indexOffset=t},e.setVB=function(){},e.setIB=function(){},e.setVData=function(){},e.setIData=function(){},e.setVBCount=function(t){this._vbCount=t},e.setIBCount=function(){},e.setVertDirty=function(t){this._vertDirty=t},e.setDataHash=function(t){this._dataHash=t},e.setIsMeshBuffer=function(t){this._isMeshBuffer=t},e.setVertexPositionInWorld=function(){},e.setMaterial=function(t){this._material=t},e.setTexture=function(t){this._texture=t},e.setSampler=function(t){this._sampler=t},e.setModel=function(){},e.setDrawInfoType=function(t){this._drawInfoType=t},e.setSubNode=function(t){this._subNode=t},e.setStride=function(t){this._stride=t},e.initRender2dBuffer=function(){},e.fillRender2dBuffer=function(){},r(t,[{key:"nativeObj",get:function(){return this._nativeObj}},{key:"render2dBuffer",get:function(){return this._render2dBuffer}}]),t}()),t("S",pt),function(t){t[t.DISABLED=0]="DISABLED",t[t.CLEAR=1]="CLEAR",t[t.ENTER_LEVEL=2]="ENTER_LEVEL",t[t.ENABLED=3]="ENABLED",t[t.EXIT_LEVEL=4]="EXIT_LEVEL",t[t.CLEAR_INVERTED=5]="CLEAR_INVERTED",t[t.ENTER_LEVEL_INVERTED=6]="ENTER_LEVEL_INVERTED"}(pt||t("S",pt={})),function(t){t[t.stencilTest=0]="stencilTest",t[t.func=1]="func",t[t.stencilMask=2]="stencilMask",t[t.writeMask=3]="writeMask",t[t.failOp=4]="failOp",t[t.zFailOp=5]="zFailOp",t[t.passOp=6]="passOp",t[t.ref=7]="ref",t[t.count=8]="count"}(Et||(Et={}));var Ct,Bt,bt,kt,It,Mt=t("l",function(){function t(){this._maskStack=[],this._stencilPattern={stencilTest:!0,func:j.ALWAYS,stencilMask:65535,writeMask:65535,failOp:Y.KEEP,zFailOp:Y.KEEP,passOp:Y.KEEP,ref:1},this._stage=pt.DISABLED,this.stencilStateMap=new Map,this.stencilStateMapWithDepth=new Map}var e=t.prototype;return e.pushMask=function(t){this._maskStack.push(t)},e.clear=function(t){return t.stencilStage!==pt.ENTER_LEVEL?pt.CLEAR_INVERTED:pt.CLEAR},e.enableMask=function(){this.stage=pt.ENABLED},e.exitMask=function(){0!==this._maskStack.length&&(this._maskStack.pop(),0===this._maskStack.length?this.stage=pt.DISABLED:this.stage=pt.ENABLED)},e.getWriteMask=function(){return 1<<this._maskStack.length-1},e.getExitWriteMask=function(){return 1<<this._maskStack.length},e.getStencilRef=function(){for(var t=0,e=0;e<this._maskStack.length;++e)t+=1<<e;return t},e.getMaskStackSize=function(){return this._maskStack.length},e.reset=function(){this._maskStack.length=0,this.stage=pt.DISABLED},e.destroy=function(){this.stencilStateMap.forEach((function(t){t.destroy()})),this.stencilStateMap.clear()},e.getStencilStage=function(t,e){var i=0,n=!1,r=!1,s=j.LESS,a=this.stencilStateMap;if(e&&e.passes[0]){var o=e.passes[0].depthStencilState,h=0,f=0;o.depthTest&&(h=1),o.depthWrite&&(f=1),i=h|f<<1|o.depthFunc<<2|t<<6|this._maskStack.length<<9,n=o.depthTest,r=o.depthWrite,s=o.depthFunc,a=this.stencilStateMapWithDepth}else i=t<<16|this._maskStack.length;if(a&&a.has(i))return a.get(i);this.setStateFromStage(t);var u=this._stencilPattern,c=new W(n,r,s,u.stencilTest,u.func,u.stencilMask,u.writeMask,u.failOp,u.zFailOp,u.passOp,u.ref,u.stencilTest,u.func,u.stencilMask,u.writeMask,u.failOp,u.zFailOp,u.passOp,u.ref);return a.set(i,c),c},e.getStencilHash=function(t){return t<<8|this._maskStack.length},e.setStateFromStage=function(t){var e=this._stencilPattern;t===pt.DISABLED?(e.stencilTest=!1,e.func=j.ALWAYS,e.failOp=Y.KEEP,e.stencilMask=e.writeMask=65535,e.ref=1):(e.stencilTest=!0,t===pt.ENABLED?(e.func=j.EQUAL,e.failOp=Y.KEEP,e.stencilMask=e.ref=this.getStencilRef(),e.writeMask=this.getWriteMask()):t===pt.CLEAR?(e.func=j.NEVER,e.failOp=Y.ZERO,e.writeMask=e.stencilMask=e.ref=this.getWriteMask()):t===pt.CLEAR_INVERTED||t===pt.ENTER_LEVEL?(e.func=j.NEVER,e.failOp=Y.REPLACE,e.writeMask=e.stencilMask=e.ref=this.getWriteMask()):t===pt.ENTER_LEVEL_INVERTED&&(e.func=j.NEVER,e.failOp=Y.ZERO,e.writeMask=e.stencilMask=e.ref=this.getWriteMask()))},r(t,[{key:"stage",get:function(){return this._stage},set:function(t){this._stage=t}},{key:"pattern",get:function(){return this._stencilPattern}}]),t}());Mt.sharedManager=null,Mt.sharedManager=new Mt,t("b",Ct),function(t){t[t.STATIC=0]="STATIC",t[t.DYNAMIC=1]="DYNAMIC",t[t.CROSSED=2]="CROSSED"}(Ct||t("b",Ct={})),function(t){t[t.localOpacity=0]="localOpacity",t[t.count=1]="count"}(Bt||(Bt={})),function(t){t[t.colorR=0]="colorR",t[t.colorG=1]="colorG",t[t.colorB=2]="colorB",t[t.colorA=3]="colorA",t[t.maskMode=4]="maskMode",t[t.count=5]="count"}(bt||(bt={})),function(t){t[t.colorDirty=0]="colorDirty",t[t.enabled=1]="enabled",t[t.useLocal=2]="useLocal",t[t.count=3]="count"}(kt||(kt={})),function(t){t[t.NONE=0]="NONE",t[t.MASK=1]="MASK",t[t.MASK_INVERTED=2]="MASK_INVERTED",t[t.MASK_NODE=3]="MASK_NODE",t[t.MASK_NODE_INVERTED=4]="MASK_NODE_INVERTED"}(It||(It={}));var wt,Ot,Tt,Pt,Nt,Ft,Lt,Ht,Vt,Ut,zt,Gt,jt,Wt,Yt,Kt,qt,Xt,Zt=t("R",function(){function t(){this._renderEntityType=Ct.STATIC,this._dynamicDrawInfoArr=[],this._node=null,this._renderTransform=null,this._stencilStage=pt.DISABLED,this._useLocal=!1,this._maskMode=It.NONE,this._color=d.WHITE.clone(),this._localOpacity=255,this._colorDirty=!0,this._enabled=!1}var e=t.prototype;return e.addDynamicRenderDrawInfo=function(){},e.removeDynamicRenderDrawInfo=function(){},e.clearDynamicRenderDrawInfos=function(){},e.clearStaticRenderDrawInfos=function(){},e.setDynamicRenderDrawInfo=function(){},e.setMaskMode=function(t){this._maskMode=t},e.getStaticRenderDrawInfo=function(){return null},e.setNode=function(t){this._node=t},e.setRenderTransform=function(t){this._renderTransform=t},e.setStencilStage=function(t){this._stencilStage=t},e.setUseLocal=function(t){this._useLocal=t},e.initSharedBuffer=function(){},r(t,[{key:"nativeObj",get:function(){return this._nativeObj}},{key:"renderDrawInfoArr",get:function(){return this._dynamicDrawInfoArr}},{key:"renderEntityType",get:function(){return this._renderEntityType}},{key:"color",get:function(){return this._color},set:function(t){this._color=t}},{key:"localOpacity",get:function(){return this._localOpacity},set:function(t){this._localOpacity=t}},{key:"colorDirty",get:function(){return this._colorDirty},set:function(t){this._colorDirty=t}},{key:"enabled",get:function(){return this._enabled},set:function(t){this._enabled=t}}]),t}()),Jt=dt(ft)>>2,Qt=t("B",function(){function t(t){void 0===t&&(t=ft),this.chunk=null,this._renderDrawInfo=null,this._material=null,this._dataHash=0,this._isMeshBuffer=!1,this._vc=0,this._ic=0,this._floatStride=0,this._vertexFormat=ft,this._drawInfoType=vt.COMP,this._multiOwner=!1,this._batcher=null,this._floatStride=t===ft?Jt:dt(t)>>2,this._vertexFormat=t}var e=t.prototype;return e.isValid=function(){return this._ic>0&&this.chunk.vertexAccessor},e.initRenderDrawInfo=function(t,e){void 0===e&&(e=vt.COMP)},e.removeRenderDrawInfo=function(){},e.setRenderDrawInfoAttributes=function(){},r(t,[{key:"vertexCount",get:function(){return this._vc}},{key:"indexCount",get:function(){return this._ic}},{key:"stride",get:function(){return this._floatStride<<2}},{key:"floatStride",get:function(){return this._floatStride}},{key:"vertexFormat",get:function(){return this._vertexFormat}},{key:"drawInfoType",get:function(){return this._drawInfoType},set:function(t){this._drawInfoType=t}},{key:"renderDrawInfo",get:function(){return this._renderDrawInfo}},{key:"material",get:function(){return this._material},set:function(t){this._material=t}},{key:"dataHash",get:function(){return this._dataHash},set:function(t){this._dataHash=t}},{key:"multiOwner",get:function(){return this._multiOwner},set:function(t){this._multiOwner=t}},{key:"batcher",get:function(){return this._batcher||(this._batcher=J.root.batcher2D),this._batcher}}]),t}()),$t=t("h",function(t){function e(e,i){var n;return void 0===e&&(e=ft),void 0===i&&(i=null),(n=t.call(this,e)||this)._vertDirty=!0,n._textureHash=0,n.indices=null,n.layer=0,n.nodeDirty=!0,n.passDirty=!0,n.textureDirty=!0,n.hashDirty=!0,n._data=[],n._frame=null,n._accessor=null,n.vertexRow=1,n.vertexCol=1,i||(i=n.batcher.switchBufferAccessor(n._vertexFormat)),n._accessor=i,n}a(e,t),e.add=function(t,i){void 0===t&&(t=ft),void 0===i&&(i=null);var n=new e(t,i);return i||(i=J.root.batcher2D.switchBufferAccessor(n._vertexFormat)),n._accessor=i,n},e.remove=function(t){t.clear(),t._accessor=null};var i=e.prototype;return i.resize=function(t,e){t===this._vc&&e===this._ic&&this.chunk||(this._vc=t,this._ic=e,this.chunk&&(this._accessor.recycleChunk(this.chunk),this.chunk=null),this.chunk=this._accessor.allocateChunk(t,e),this.updateHash())},i.setRenderDrawInfoAttributes=function(){},i.fillDrawInfoAttributes=function(){},i.syncRender2dBuffer=function(){},i.resizeAndCopy=function(t,e){if(t!==this._vc||e!==this._ic||!this.chunk){this._vc=t,this._ic=e;var i=this.chunk;this.chunk=this._accessor.allocateChunk(t,e),i&&(this.chunk.vb.set(i.vb),this._accessor.recycleChunk(i)),this.updateHash()}},i.getMeshBuffer=function(){return this.chunk&&this._accessor?this._accessor.getMeshBuffer(this.chunk.bufferId):null},i.updateNode=function(t){this.layer=t.node.layer,this.nodeDirty=!1,this.hashDirty=!0},i.updatePass=function(t){this.material=t.getRenderMaterial(0),this.passDirty=!1,this.hashDirty=!0},i.updateTexture=function(t){this.frame=t,this.textureHash=t.getHash(),this.textureDirty=!1,this.hashDirty=!0},i.updateHash=function(){var t=""+(this.chunk?this.chunk.bufferId:-1)+this.layer+" "+this.textureHash;this.dataHash=K(t,666),this.hashDirty=!1},i.updateRenderData=function(t,e){if(this.passDirty&&(this.material=t.getRenderMaterial(0),this.passDirty=!1,this.hashDirty=!0),this.nodeDirty){var i=t.node.scene?t._getRenderScene():null;this.layer=t.node.layer,null!==i&&(this.nodeDirty=!1),this.hashDirty=!0}this.textureDirty&&(this.frame=e,this.textureHash=e.getHash(),this.textureDirty=!1,this.hashDirty=!0),this.hashDirty&&this.updateHash()},i.clear=function(){this.resize(0,0),this._data.length=0,this.indices=null,this.vertDirty=!0,this.material=null,this.nodeDirty=!0,this.passDirty=!0,this.textureDirty=!0,this.hashDirty=!0,this.layer=0,this.frame=null,this.textureHash=0,this.dataHash=0},e.createStaticVBAccessor=function(t,e,i){var n=J.root.device;return new xt(n,t,e,i)},r(e,[{key:"dataLength",get:function(){return this._data.length},set:function(t){var e=this._data;if(e.length!==t){for(var i=e.length;i<t;i++)e.push({x:0,y:0,z:0,u:0,v:0,color:d.WHITE.clone()});e.length=t}this.syncRender2dBuffer()}},{key:"data",get:function(){return this._data}},{key:"vertDirty",get:function(){return this._vertDirty},set:function(t){this._vertDirty=t}},{key:"textureHash",get:function(){return this._textureHash},set:function(t){this._textureHash=t}},{key:"frame",get:function(){return this._frame},set:function(t){this._frame=t}},{key:"accessor",get:function(){return this._accessor}}]),e}(Qt)),te=(t("M",function(t){function i(e){var i;return void 0===e&&(e=ft),(i=t.call(this,e)||this)._isMeshBuffer=!0,i.vertexStart=0,i.vertexRange=0,i.indexStart=0,i.indexRange=0,i.lastFilledIndex=0,i.lastFilledVertex=0,i.frame=null,i._byteLength=0,i._vertexBuffers=[],i._indexBuffer=null,i._iaPool=null,i._iaInfo=null,i.vData=new Float32Array(256*i.stride),i.iData=new Uint16Array(1536),i}a(i,t),i.add=function(t){void 0===t&&(t=ft);var e=new i;return e._floatStride=t===ft?Jt:dt(t)>>2,e._vertexFormat=t,e},i.remove=function(t){t.clear()};var n=i.prototype;return n.request=function(t,e){var i=this._byteLength+t*this.stride;return!!this.reserve(t,e)&&(this._vc+=t,this._ic+=e,this._byteLength=i,this.vertexRange=this._vc,this.indexRange=this._ic,!0)},n.reserve=function(t,e){var i=this._byteLength+t*this.stride,n=this.indexCount+e;if(t+this.vertexCount>65535)return!1;var r=this.vData.byteLength,s=this.iData.length,a=this.vData.length,o=this.iData.length;if(i>r||n>s){for(;r<i||s<n;)r=4*(a*=2),s=o*=2;this._reallocBuffer(a,o)}return!0},n.resize=function(t,i){var n=t*this.stride;e(t>=0&&i>=0&&n<=this.vData.byteLength&&i<=this.iData.length),this._vc=t,this._ic=i,this._byteLength=n,this.updateRange(0,t,0,i)},n.updateRange=function(t,i,n,r){e(i>=0&&r>=0&&i<=this._vc&&r<=this._ic),this.vertexStart=t,this.indexStart=n,this.vertexRange=i,this.indexRange=r},n.requestIA=function(t){this._initIAInfo(t);var e=this._iaPool.add();return e.firstIndex=this.indexStart,e.indexCount=this.indexRange,e},n.uploadBuffers=function(){if(0!==this._byteLength&&this._vertexBuffers[0]&&this._indexBuffer){var t=this._ic,e=new Float32Array(this.vData.buffer,0,this._byteLength>>2),i=new Uint16Array(this.iData.buffer,0,t),n=this._vertexBuffers[0];this._byteLength>n.size&&n.resize(this._byteLength),n.update(e);var r=t<<1;r>this._indexBuffer.size&&this._indexBuffer.resize(r),this._indexBuffer.update(i)}},n.freeIAPool=function(){this._iaPool&&this._iaPool.reset()},n.reset=function(){this._vc=0,this._ic=0,this._byteLength=0,this.vertexStart=0,this.vertexRange=0,this.indexStart=0,this.indexRange=0,this.lastFilledIndex=0,this.lastFilledVertex=0,this.material=null,this.freeIAPool()},n.clear=function(){this.reset(),this._iaPool&&this._iaPool.destroy(),this._vertexBuffers[0]&&(this._vertexBuffers[0].destroy(),this._vertexBuffers=[]),this._iaInfo=null,this.vData=new Float32Array(256*this.stride),this.iData=new Uint16Array(1536)},n._initIAInfo=function(t){var e=this;if(!this._iaInfo){var i=this.stride,n=this._vertexBuffers;n.length||n.push(t.createBuffer(new V(U.VERTEX|U.TRANSFER_DST,z.DEVICE,i,i)));var r=Uint16Array.BYTES_PER_ELEMENT;this._indexBuffer||(this._indexBuffer=t.createBuffer(new V(U.INDEX|U.TRANSFER_DST,z.DEVICE,r,r))),this._iaInfo=new G(this._vertexFormat,n,this._indexBuffer),this._iaPool=new u((function(){return t.createInputAssembler(e._iaInfo)}),1,(function(t){t.destroy()}))}},n._reallocBuffer=function(t,e){var i=this.vData;this.vData=new Float32Array(t),i&&this.vData.set(i,0);var n=this.iData;this.iData=new Uint16Array(e),n&&this.iData.set(n,0)},n.setRenderDrawInfoAttributes=function(){},n.particleInitRenderDrawInfo=function(){},r(i,[{key:"formatByte",get:function(){return this.stride},set:function(){}},{key:"floatStride",get:function(){return this._floatStride}},{key:"vDataOffset",get:function(){return this._byteLength>>>2}}]),i}(Qt)),new y),ee=new y,ie=new g,ne=new D,re=new D,se=new D,ae=new D(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0),oe=new v,he=t("c",p("cc.UITransform")(wt=x(110)(wt=E((Nt=function(t){function e(){var e;return(e=t.call(this)||this)._priority=0,e._contentSize=Tt&&Tt(),e._anchorPoint=Pt&&Pt(),e}a(e,t);var i=e.prototype;return i.__preload=function(){this.node._uiProps.uiTransformComp=this},i.onLoad=function(){this.node.parent&&e.insertChangeMap(this.node.parent)},i.onEnable=function(){this.node.on(tt.PARENT_CHANGED,this._parentChanged,this),this._markRenderDataDirty()},i.onDisable=function(){this.node.off(tt.PARENT_CHANGED,this._parentChanged,this)},i.onDestroy=function(){this.node._uiProps.uiTransformComp=null},i.setContentSize=function(t,e){var i,n,r=this._contentSize;if(void 0===e){if(m(t.width,r.width,C)&&m(t.height,r.height,C))return;i=t.width,n=t.height}else{if(m(t,r.width,C)&&m(e,r.height,C))return;i=t,n=e}r.width=i,r.height=n,this.node.emit(tt.SIZE_CHANGED),this._markRenderDataDirty()},i.setAnchorPoint=function(t,e){var i=this._anchorPoint;if(void 0===e){if(t.x===i.x&&t.y===i.y)return;i.x=t.x,i.y=t.y}else{if(t===i.x&&e===i.y)return;i.x=t,i.y=e}this.node.emit(tt.ANCHOR_CHANGED,this._anchorPoint),this._markRenderDataDirty()},i.isHit=function(t){for(var e=this._contentSize.width,i=this._contentSize.height,n=te,r=ee,s=this._getRenderScene().cameras,a=0;a<s.length;a++){var o=s[a];if(o.visibility&this.node.layer){o.node.getWorldRT(ne);var h=ne.m12,f=ne.m13,u=A.center;if(ne.m12=u.x-(ne.m00*h+ne.m04*f),ne.m13=u.y-(ne.m01*h+ne.m05*f),D.invert(ne,ne),y.transformMat4(n,t,ne),this.node.getWorldMatrix(se),D.invert(ne,se),!D.strictEquals(ne,ae)){y.transformMat4(r,n,ne),r.x+=this._anchorPoint.x*e,r.y+=this._anchorPoint.y*i;var c=!1;if(r.x>=0&&r.y>=0&&r.x<=e&&r.y<=i&&(c=this._maskTest(n)),c)return!0}}}return!1},i.hitTest=function(t,e){void 0===e&&(e=0);for(var i=this._contentSize.width,n=this._contentSize.height,r=ie,s=te,a=ee,o=this._getRenderScene().cameras,h=0;h<o.length;h++){var f=o[h];if(f.visibility&this.node.layer&&(!f.window||f.window.swapchain)&&f.systemWindowId===e&&(g.set(r,t.x,t.y,0),f.screenToWorld(r,r),y.set(s,r.x,r.y),this.node.getWorldMatrix(se),D.invert(ne,se),!D.strictEquals(ne,ae))){y.transformMat4(a,s,ne),a.x+=this._anchorPoint.x*i,a.y+=this._anchorPoint.y*n;var u=!1;if(a.x>=0&&a.y>=0&&a.x<=i&&a.y<=n&&(u=this._maskTest(s)),u)return!0}}return!1},i._maskTest=function(t){var e,i,n=null==(e=this.node)||null==(i=e.eventProcessor)?void 0:i.maskList;if(n)for(var r=this.node,s=n.length,a=0,o=0;r&&o<s;++a,r=r.parent){var h=n[o];if(a===h.index){if(r!==h.comp.node){n.length=o;break}var f=h.comp;if(f&&f._enabled&&!f.isHit(t))return!1;o++}else if(a>h.index){n.length=o;break}}return!0},i.convertToNodeSpaceAR=function(t,e){return this.node.getWorldMatrix(se),D.invert(ne,se),e||(e=new g),g.transformMat4(e,t,ne)},i.convertToWorldSpaceAR=function(t,e){return this.node.getWorldMatrix(se),e||(e=new g),g.transformMat4(e,t,se)},i.getBoundingBox=function(){var t=new v;return this._selfBoundingBox(t),D.fromSRT(re,this.node.rotation,this.node.position,this.node.scale),t.transformMat4(re),t},i.getBoundingBoxToWorld=function(){for(var t=new v,i=this.node.children,n=0;n<i.length;++n){var r=i[n];if(r&&r.active){var s=r.getComponent(e);s&&s.contentSize.width&&s.contentSize.height&&(s._selfBoundingBox(oe),oe.transformMat4(r.worldMatrix),0===t.width?t.set(oe):v.union(t,t,oe))}}return this._contentSize.width&&this._contentSize.height&&(this._selfBoundingBox(oe),oe.transformMat4(this.node.worldMatrix),0===t.width?t.set(oe):v.union(t,t,oe)),t},i.getBoundingBoxTo=function(t){var i=new v,n=this.node.children;D.invert(ne,t);for(var r=0;r<n.length;++r){var s=n[r];if(s&&s.active){var a=s.getComponent(e);a&&a.contentSize.width&&a.contentSize.height&&(a._selfBoundingBox(oe),D.multiply(re,s.worldMatrix,ne),oe.transformMat4(re),0===i.width?i.set(oe):v.union(i,i,oe))}}return this._contentSize.width&&this._contentSize.height&&(this._selfBoundingBox(oe),D.multiply(re,this.node.worldMatrix,ne),oe.transformMat4(re),0===i.width?i.set(oe):v.union(i,i,oe)),i},i.getComputeAABB=function(t){var e=this._contentSize.width,i=this._contentSize.height;oe.set(-this._anchorPoint.x*e,-this._anchorPoint.y*i,e,i),oe.transformMat4(this.node.worldMatrix);var n=oe.x+.5*oe.width,r=oe.y+.5*oe.height,s=this.node.worldPosition.z,a=oe.width/2,o=oe.height/2;return null!=t?(S.set(t,n,r,s,a,o,.001),t):new S(n,r,s,a,o,.001)},i._selfBoundingBox=function(t){var e=this._contentSize.width,i=this._contentSize.height;return t.set(-this._anchorPoint.x*e,-this._anchorPoint.y*i,e,i),t},i._parentChanged=function(){this.node.getComponent("cc.RenderRoot2D")||this.node.parent&&e.insertChangeMap(this.node.parent)},i._markRenderDataDirty=function(){var t=this.node._uiProps.uiComp;t&&t._markForUpdateRenderData()},e.insertChangeMap=function(t){var i=t.uuid;e.priorityChangeNodeMap.has(i)||e.priorityChangeNodeMap.set(i,t)},e._sortChildrenSibling=function(t){var e=t.children;e&&e.sort((function(t,e){var i=t._getUITransformComp(),n=e._getUITransformComp(),r=(i?i._priority:0)-(n?n._priority:0);return 0===r?t.siblingIndex-e.siblingIndex:r}))},e._sortSiblings=function(){e.priorityChangeNodeMap.forEach((function(t){e._sortChildrenSibling(t),t._updateSiblingIndex(),t.emit("childrenSiblingOrderChanged")})),e.priorityChangeNodeMap.clear()},e._cleanChangeMap=function(){e.priorityChangeNodeMap.clear()},r(e,[{key:"contentSize",get:function(){return this._contentSize},set:function(t){this._contentSize.equals(t)||(this._contentSize.set(t),this.node.emit(tt.SIZE_CHANGED),this._markRenderDataDirty())}},{key:"width",get:function(){return this._contentSize.width},set:function(t){this._contentSize.width!==t&&(this._contentSize.width=t,this.node.emit(tt.SIZE_CHANGED),this._markRenderDataDirty())}},{key:"height",get:function(){return this._contentSize.height},set:function(t){this.contentSize.height!==t&&(this._contentSize.height=t,this.node.emit(tt.SIZE_CHANGED),this._markRenderDataDirty())}},{key:"anchorPoint",get:function(){return this._anchorPoint},set:function(t){this._anchorPoint.equals(t)||(this._anchorPoint.set(t),this.node.emit(tt.ANCHOR_CHANGED,this._anchorPoint),this._markRenderDataDirty())}},{key:"anchorX",get:function(){return this._anchorPoint.x},set:function(t){this._anchorPoint.x!==t&&(this._anchorPoint.x=t,this.node.emit(tt.ANCHOR_CHANGED,this._anchorPoint),this._markRenderDataDirty())}},{key:"anchorY",get:function(){return this._anchorPoint.y},set:function(t){this._anchorPoint.y!==t&&(this._anchorPoint.y=t,this.node.emit(tt.ANCHOR_CHANGED,this._anchorPoint),this._markRenderDataDirty())}},{key:"priority",get:function(){return this._priority},set:function(t){this._priority!==t&&(this.node.getComponent("cc.RenderRoot2D")?n(6706):(this._priority=t,this.node.parent&&e.insertChangeMap(this.node.parent)))}},{key:"visibility",get:function(){var t=J.root.batcher2D.getFirstRenderCamera(this.node);return t?t.visibility:0}},{key:"cameraPriority",get:function(){var t=J.root.batcher2D.getFirstRenderCamera(this.node);return t?t.priority:0}}]),e}(P),Nt.EventType=tt,Nt.priorityChangeNodeMap=new Map,Tt=R((Ot=Nt).prototype,"_contentSize",[b],(function(){return new B(100,100)})),Pt=R(Ot.prototype,"_anchorPoint",[b],(function(){return new y(.5,.5)})),wt=Ot))||wt)||wt)||wt);J.on(Q.AFTER_UPDATE,he._sortSiblings),J.on(Q.BEFORE_SCENE_LAUNCH,he._cleanChangeMap),c(q),c(X),c(Z),t("I",Xt),function(t){t[t.ADD_COLOR=0]="ADD_COLOR",t[t.ADD_COLOR_AND_TEXTURE=1]="ADD_COLOR_AND_TEXTURE",t[t.GRAYSCALE=2]="GRAYSCALE",t[t.USE_ALPHA_SEPARATED=3]="USE_ALPHA_SEPARATED",t[t.USE_ALPHA_SEPARATED_AND_GRAY=4]="USE_ALPHA_SEPARATED_AND_GRAY"}(Xt||t("I",Xt={}));var fe=t("U",(Ft=p("cc.UIRenderer"),Lt=M(he),Ht=k(T),Vt=k(T),Ft(Ut=Lt((qt=function(t){function e(){var e;return(e=t.call(this)||this)._renderData=null,e._materials=Gt&&Gt(),e._customMaterial=jt&&jt(),e._srcBlendFactor=Wt&&Wt(),e._dstBlendFactor=Yt&&Yt(),e._color=Kt&&Kt(),e._stencilStage=pt.DISABLED,e._assembler=null,e._postAssembler=null,e._renderDataFlag=!0,e._renderFlag=!0,e._instanceMaterialType=-1,e._srcBlendFactorCache=q.SRC_ALPHA,e._dstBlendFactorCache=q.ONE_MINUS_SRC_ALPHA,e._dirtyVersion=-1,e._internalId=-1,e._flagChangedVersion=-1,e._useVertexOpacity=!1,e._lastParent=null,e._renderEntity=e.createRenderEntity(),e}a(e,t);var i=e.prototype;return i.setRenderData=function(t){this._renderData=t},i.onLoad=function(){this._renderEntity.setNode(this.node)},i.__preload=function(){this.node._uiProps.uiComp=this,this._flushAssembler&&this._flushAssembler()},i.onEnable=function(){this.node.on(tt.ANCHOR_CHANGED,this._nodeStateChange,this),this.node.on(tt.SIZE_CHANGED,this._nodeStateChange,this),this.node.on(tt.PARENT_CHANGED,this._colorDirty,this),!this._renderData&&this._flushAssembler&&this._flushAssembler(),this.updateMaterial(),this._colorDirty(),$.addRenderer(this),this._markForUpdateRenderData()},i.onRestore=function(){this.updateMaterial(),this._markForUpdateRenderData()},i.onDisable=function(){this.node.off(tt.ANCHOR_CHANGED,this._nodeStateChange,this),this.node.off(tt.SIZE_CHANGED,this._nodeStateChange,this),this.node.off(tt.PARENT_CHANGED,this._colorDirty,this),this.destroyRenderData(),$.removeRenderer(this),this._renderFlag=!1,this._renderEntity.enabled=!1},i.onDestroy=function(){if(this._renderEntity.setNode(null),this.node._uiProps.uiComp===this&&(this.node._uiProps.uiComp=null),this.destroyRenderData(),this._materialInstances)for(var t=0;t<this._materialInstances.length;t++){var e=this._materialInstances[t];e&&e.destroy()}},i.markForUpdateRenderData=function(t){void 0===t&&(t=!0),this._markForUpdateRenderData(t)},i._markForUpdateRenderData=function(t){if(void 0===t&&(t=!0),t){var e=this._renderData;e&&(e.vertDirty=!0),$.markDirtyRenderer(this)}},i.requestRenderData=function(t){void 0===t&&(t=vt.COMP);var e=$t.add();return e.initRenderDrawInfo(this,t),this._renderData=e,e},i.destroyRenderData=function(){this._renderData&&(this._renderData.removeRenderDrawInfo(this),$t.remove(this._renderData),this._renderData=null)},i.updateRenderer=function(){var t=this._assembler;t&&t.updateRenderData&&t.updateRenderData(this),this._renderFlag=this._canRender(),this._renderEntity.enabled=this._renderFlag},i.fillBuffers=function(t){this._renderFlag&&this._render(t)},i.postUpdateAssembler=function(t){this._postAssembler&&this._renderFlag&&this._postRender(t)},i._render=function(){},i._postRender=function(){},i._canRender=function(){return null!==this.getSharedMaterial(0)&&this._enabled&&this._color.a>0},i._postCanRender=function(){},i.updateMaterial=function(){if(this._customMaterial)this.getSharedMaterial(0)!==this._customMaterial&&this.setSharedMaterial(this._customMaterial,0);else{var t=this._updateBuiltinMaterial();this.setSharedMaterial(t,0),this.stencilStage!==pt.ENTER_LEVEL&&this.stencilStage!==pt.ENTER_LEVEL_INVERTED||this.getMaterialInstance(0).recompileShaders({USE_ALPHA_TEST:!0}),this._updateBlendFunc()}},i._updateColor=function(){this.node._uiProps.colorDirty=!0,this.setEntityColorDirty(!0),this.setEntityColor(this._color),this.setEntityOpacity(this.node._uiProps.localOpacity);var t=this._assembler;if(t){t.updateColor&&t.updateColor(this);var e=this._renderFlag;if(this._renderFlag=this._canRender(),this.setEntityEnabled(this._renderFlag),e!==this._renderFlag){var i=this.renderData;i&&(i.vertDirty=!0)}}},e.setEntityColorDirtyRecursively=function(t,i){var n=t._uiProps.uiComp;n&&n.color&&(n._renderEntity.colorDirty=i);for(var r=0;r<t.children.length;r++)e.setEntityColorDirtyRecursively(t.children[r],i)},i.setEntityColorDirty=function(){},i.setEntityColor=function(){},i.setEntityOpacity=function(){},i.setEntityEnabled=function(){},i._updateBlendFunc=function(){var t=this.getRenderMaterial(0).passes[0].blendState.targets[0];if(this._dstBlendFactorCache=t.blendDst,this._srcBlendFactorCache=t.blendSrc,this._dstBlendFactorCache!==this._dstBlendFactor||this._srcBlendFactorCache!==this._srcBlendFactor){(t=this.getMaterialInstance(0).passes[0].blendState.targets[0]).blend=!0,t.blendDstAlpha=q.ONE_MINUS_SRC_ALPHA,t.blendDst=this._dstBlendFactor,t.blendSrc=this._srcBlendFactor;var e=this.getMaterialInstance(0).passes[0];e.blendState.setTarget(0,t),e._updatePassHash(),this._dstBlendFactorCache=this._dstBlendFactor,this._srcBlendFactorCache=this._srcBlendFactor}},i._nodeStateChange=function(){this._renderData&&this._markForUpdateRenderData();for(var t=0;t<this.node.children.length;++t){var i=this.node.children[t].getComponent(e);i&&i._markForUpdateRenderData()}},i._colorDirty=function(){this.node._uiProps.colorDirty=!0,this.setEntityColorDirty(!0)},i._onMaterialModified=function(e,i){this._renderData&&(this._markForUpdateRenderData(),this._renderData.passDirty=!0),t.prototype._onMaterialModified.call(this,e,i)},i._updateBuiltinMaterial=function(){var t;switch(this._instanceMaterialType){case Xt.ADD_COLOR:t=O.get("ui-base-material");break;case Xt.GRAYSCALE:t=O.get("ui-sprite-gray-material");break;case Xt.USE_ALPHA_SEPARATED:t=O.get("ui-sprite-alpha-sep-material");break;case Xt.USE_ALPHA_SEPARATED_AND_GRAY:t=O.get("ui-sprite-gray-alpha-sep-material");break;default:t=O.get("ui-sprite-material")}return t},i.setNodeDirty=function(){this._renderData&&(this._renderData.nodeDirty=!0)},i.setTextureDirty=function(){this._renderData&&(this._renderData.textureDirty=!0)},i.createRenderEntity=function(){return new Zt(Ct.STATIC)},r(e,[{key:"sharedMaterials",get:function(){return this._materials},set:function(t){for(var e=0;e<t.length;e++)t[e]!==this._materials[e]&&this.setSharedMaterial(t[e],e);if(t.length<this._materials.length){for(var i=t.length;i<this._materials.length;i++)this.setSharedMaterial(null,i);this._materials.splice(t.length)}}},{key:"customMaterial",get:function(){return this._customMaterial},set:function(t){this._customMaterial=t,this.updateMaterial()}},{key:"color",get:function(){return this._color},set:function(t){this._color.equals(t)||(this._color.set(t),this._updateColor())}},{key:"renderData",get:function(){return this._renderData}},{key:"useVertexOpacity",get:function(){return this._useVertexOpacity}},{key:"stencilStage",get:function(){return this._stencilStage},set:function(t){this._stencilStage=t,this._renderEntity.setStencilStage(t)}},{key:"srcBlendFactor",get:function(){return this._srcBlendFactor},set:function(t){this._srcBlendFactor=t}},{key:"batcher",get:function(){return J.root.batcher2D}},{key:"renderEntity",get:function(){return this._renderEntity}}]),e}(et),qt.BlendState=q,qt.Assembler=null,qt.PostAssembler=null,l((zt=qt).prototype,"sharedMaterials",[I],Object.getOwnPropertyDescriptor(zt.prototype,"sharedMaterials"),zt.prototype),l(zt.prototype,"customMaterial",[Ht],Object.getOwnPropertyDescriptor(zt.prototype,"customMaterial"),zt.prototype),Gt=R(zt.prototype,"_materials",[I],(function(){return[]})),jt=R(zt.prototype,"_customMaterial",[Vt],(function(){return null})),Wt=R(zt.prototype,"_srcBlendFactor",[b],(function(){return q.SRC_ALPHA})),Yt=R(zt.prototype,"_dstBlendFactor",[b],(function(){return q.ONE_MINUS_SRC_ALPHA})),Kt=R(zt.prototype,"_color",[b],(function(){return d.WHITE.clone()})),Ut=zt))||Ut)||Ut));w.internal.UIRenderer=fe}}}));
