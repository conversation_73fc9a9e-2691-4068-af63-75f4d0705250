System.register(["./gc-object-D18ulfCO.js","./global-exports-CLZKKIY2.js","./index-Y4La_nfG.js","./device-manager-B8lyUDPi.js","./buffer-barrier-q_79u36H.js"],(function(_){"use strict";var E,O,T,F,A,S,I,N,R,L,n,e,M,D,P,C,t,U,B,a,c,i,r,o,G,u,H,s,w,l,W,V,d;return{setters:[function(_){E=_.E,O=_.B,T=_.t,F=_.s,A=_.S,S=_.w,I=_.k,N=_.g,R=_.v,L=_.u,n=_.a},function(_){e=_.l,M=_.c},null,null,function(_){D=_.g,P=_.h,C=_.S,t=_.U,U=_.i,B=_.j,a=_.k,c=_.l,i=_.V,r=_.n,o=_.F,G=_.o,u=_.T,H=_.d,s=_.e,w=_.p,l=_.q,W=_.r,V=_.s,d=_.t}],execute:function(){_({F:xE,G:kE,i:KE,l:Q_,z:jE});var f,g,m,X,b,Y,p,J,v,h,Z,y,k,j,x={NONE:0,IGNORE_RAYCAST:1<<20,GIZMOS:1<<21,EDITOR:1<<22,UI_3D:1<<23,SCENE_GIZMO:1<<24,UI_2D:1<<25,PROFILER:1<<28,DEFAULT:1<<30,ALL:4294967295},K=_("L",function(){function _(){}return _.init=function(){var E=F.querySettings(A.ENGINE,"customLayers");if(E)for(var O=0;O<E.length;O++){var T=E[O];_.addLayer(T.name,T.bit)}},_.makeMaskInclude=function(_){return _.reduce((function(_,E){return _|E}),0)},_.makeMaskExclude=function(E){return~_.makeMaskInclude(E)},_.addLayer=function(T,F){if(void 0!==F)if(F>19||F<0)S(16365);else{var A=1<<F;I(!_.Enum[T],N(2104,T)),_.Enum[T]=A,R(_.Enum,String(A),T),_.BitMask[T]=A,R(_.BitMask,String(A),T),O.update(_.BitMask),E.update(_.Enum)}else S(16364)},_.deleteLayer=function(T){if(T>19||T<0)S(16366);else{var F=1<<T;delete _.Enum[_.Enum[F]],delete _.Enum[F],delete _.BitMask[_.BitMask[F]],delete _.BitMask[F],O.update(_.BitMask),E.update(_.Enum)}},_.nameToLayer=function(E){return void 0===E?(S(16367),-1):L(_.Enum[E])},_.layerToName=function(E){return E>31||E<0?(S(16368),""):_.Enum[1<<E]},_}());K.Enum=E(x),K.BitMask=O(T({},x)),e.Layers=K;var Q,z,q=_("_","MainFlow"),$=_("Y","ForwardFlow"),__=_("Z","ShadowFlow");_("$",Q),function(_){_[_.DEFAULT=100]="DEFAULT",_[_.UI=200]="UI"}(Q||_("$",Q={})),M.RenderPassStage=Q,_("R",z),function(_){_[_.MIN=0]="MIN",_[_.MAX=255]="MAX",_[_.DEFAULT=128]="DEFAULT"}(z||_("R",z={}));var E_,O_=_("N",{bindings:[],layouts:{}}),T_=_("H",{bindings:[],layouts:{}});_("J",E_),function(_){_[_.UBO_GLOBAL=0]="UBO_GLOBAL",_[_.UBO_CAMERA=1]="UBO_CAMERA",_[_.UBO_SHADOW=2]="UBO_SHADOW",_[_.UBO_CSM=3]="UBO_CSM",_[_.SAMPLER_SHADOWMAP=4]="SAMPLER_SHADOWMAP",_[_.SAMPLER_ENVIRONMENT=5]="SAMPLER_ENVIRONMENT",_[_.SAMPLER_SPOT_SHADOW_MAP=6]="SAMPLER_SPOT_SHADOW_MAP",_[_.SAMPLER_DIFFUSEMAP=7]="SAMPLER_DIFFUSEMAP",_[_.COUNT=8]="COUNT"}(E_||_("J",E_={}));var F_,A_=E_.SAMPLER_SHADOWMAP,S_=E_.COUNT-A_;_("M",F_),function(_){_[_.UBO_LOCAL=0]="UBO_LOCAL",_[_.UBO_FORWARD_LIGHTS=1]="UBO_FORWARD_LIGHTS",_[_.UBO_SKINNING_ANIMATION=2]="UBO_SKINNING_ANIMATION",_[_.UBO_SKINNING_TEXTURE=3]="UBO_SKINNING_TEXTURE",_[_.UBO_MORPH=4]="UBO_MORPH",_[_.UBO_UI_LOCAL=5]="UBO_UI_LOCAL",_[_.UBO_SH=6]="UBO_SH",_[_.SAMPLER_JOINTS=7]="SAMPLER_JOINTS",_[_.SAMPLER_MORPH_POSITION=8]="SAMPLER_MORPH_POSITION",_[_.SAMPLER_MORPH_NORMAL=9]="SAMPLER_MORPH_NORMAL",_[_.SAMPLER_MORPH_TANGENT=10]="SAMPLER_MORPH_TANGENT",_[_.SAMPLER_LIGHTMAP=11]="SAMPLER_LIGHTMAP",_[_.SAMPLER_SPRITE=12]="SAMPLER_SPRITE",_[_.SAMPLER_REFLECTION_PROBE_CUBE=13]="SAMPLER_REFLECTION_PROBE_CUBE",_[_.SAMPLER_REFLECTION_PROBE_PLANAR=14]="SAMPLER_REFLECTION_PROBE_PLANAR",_[_.SAMPLER_REFLECTION_PROBE_DATA_MAP=15]="SAMPLER_REFLECTION_PROBE_DATA_MAP",_[_.COUNT=16]="COUNT"}(F_||_("M",F_={}));var I_,N_=F_.SAMPLER_JOINTS,R_=F_.COUNT-N_,L_=F_.COUNT-N_-R_;_("S",I_),function(_){_[_.GLOBAL=0]="GLOBAL",_[_.MATERIAL=1]="MATERIAL",_[_.LOCAL=2]="LOCAL",_[_.COUNT=3]="COUNT"}(I_||_("S",I_={}));var n_,e_=_("k",new r([A_,0,N_,0],[S_,0,R_,0],[0,0,0,0],[0,0,0,0],[0,0,0,0],[0,0,L_,0],[0,0,0,0],[0,2,1,3]));_("U",n_),function(_){_[_.TIME_OFFSET=0]="TIME_OFFSET",_[_.SCREEN_SIZE_OFFSET=4]="SCREEN_SIZE_OFFSET",_[_.NATIVE_SIZE_OFFSET=8]="NATIVE_SIZE_OFFSET",_[_.PROBE_INFO_OFFSET=12]="PROBE_INFO_OFFSET",_[_.DEBUG_VIEW_MODE_OFFSET=16]="DEBUG_VIEW_MODE_OFFSET",_[_.COUNT=20]="COUNT",_[_.SIZE=80]="SIZE"}(n_||_("U",n_={}));var M_,D_=_("Q",(function(){}));f=D_,D_.TIME_OFFSET=n_.TIME_OFFSET,D_.SCREEN_SIZE_OFFSET=n_.SCREEN_SIZE_OFFSET,D_.NATIVE_SIZE_OFFSET=n_.NATIVE_SIZE_OFFSET,D_.PROBE_INFO_OFFSET=n_.PROBE_INFO_OFFSET,D_.DEBUG_VIEW_MODE_OFFSET=n_.DEBUG_VIEW_MODE_OFFSET,D_.COUNT=n_.COUNT,D_.SIZE=n_.SIZE,D_.NAME="CCGlobal",D_.BINDING=E_.UBO_GLOBAL,D_.DESCRIPTOR=new D(f.BINDING,P.UNIFORM_BUFFER,1,C.ALL),D_.LAYOUT=new t(I_.GLOBAL,f.BINDING,f.NAME,[new U("cc_time",B.FLOAT4,1),new U("cc_screenSize",B.FLOAT4,1),new U("cc_nativeSize",B.FLOAT4,1),new U("cc_probeInfo",B.FLOAT4,1),new U("cc_debug_view_mode",B.FLOAT4,1)],1),O_.layouts[D_.NAME]=D_.LAYOUT,O_.bindings[D_.BINDING]=D_.DESCRIPTOR,_("a",M_),function(_){_[_.MAT_VIEW_OFFSET=0]="MAT_VIEW_OFFSET",_[_.MAT_VIEW_INV_OFFSET=16]="MAT_VIEW_INV_OFFSET",_[_.MAT_PROJ_OFFSET=32]="MAT_PROJ_OFFSET",_[_.MAT_PROJ_INV_OFFSET=48]="MAT_PROJ_INV_OFFSET",_[_.MAT_VIEW_PROJ_OFFSET=64]="MAT_VIEW_PROJ_OFFSET",_[_.MAT_VIEW_PROJ_INV_OFFSET=80]="MAT_VIEW_PROJ_INV_OFFSET",_[_.CAMERA_POS_OFFSET=96]="CAMERA_POS_OFFSET",_[_.SURFACE_TRANSFORM_OFFSET=100]="SURFACE_TRANSFORM_OFFSET",_[_.SCREEN_SCALE_OFFSET=104]="SCREEN_SCALE_OFFSET",_[_.EXPOSURE_OFFSET=108]="EXPOSURE_OFFSET",_[_.MAIN_LIT_DIR_OFFSET=112]="MAIN_LIT_DIR_OFFSET",_[_.MAIN_LIT_COLOR_OFFSET=116]="MAIN_LIT_COLOR_OFFSET",_[_.AMBIENT_SKY_OFFSET=120]="AMBIENT_SKY_OFFSET",_[_.AMBIENT_GROUND_OFFSET=124]="AMBIENT_GROUND_OFFSET",_[_.GLOBAL_FOG_COLOR_OFFSET=128]="GLOBAL_FOG_COLOR_OFFSET",_[_.GLOBAL_FOG_BASE_OFFSET=132]="GLOBAL_FOG_BASE_OFFSET",_[_.GLOBAL_FOG_ADD_OFFSET=136]="GLOBAL_FOG_ADD_OFFSET",_[_.NEAR_FAR_OFFSET=140]="NEAR_FAR_OFFSET",_[_.VIEW_PORT_OFFSET=144]="VIEW_PORT_OFFSET",_[_.COUNT=148]="COUNT",_[_.SIZE=592]="SIZE"}(M_||_("a",M_={}));var P_,C_=_("T",(function(){}));g=C_,C_.MAT_VIEW_OFFSET=M_.MAT_VIEW_OFFSET,C_.MAT_VIEW_INV_OFFSET=M_.MAT_VIEW_INV_OFFSET,C_.MAT_PROJ_OFFSET=M_.MAT_PROJ_OFFSET,C_.MAT_PROJ_INV_OFFSET=M_.MAT_PROJ_INV_OFFSET,C_.MAT_VIEW_PROJ_OFFSET=M_.MAT_VIEW_PROJ_OFFSET,C_.MAT_VIEW_PROJ_INV_OFFSET=M_.MAT_VIEW_PROJ_INV_OFFSET,C_.CAMERA_POS_OFFSET=M_.CAMERA_POS_OFFSET,C_.SURFACE_TRANSFORM_OFFSET=M_.SURFACE_TRANSFORM_OFFSET,C_.SCREEN_SCALE_OFFSET=M_.SCREEN_SCALE_OFFSET,C_.EXPOSURE_OFFSET=M_.EXPOSURE_OFFSET,C_.MAIN_LIT_DIR_OFFSET=M_.MAIN_LIT_DIR_OFFSET,C_.MAIN_LIT_COLOR_OFFSET=M_.MAIN_LIT_COLOR_OFFSET,C_.AMBIENT_SKY_OFFSET=M_.AMBIENT_SKY_OFFSET,C_.AMBIENT_GROUND_OFFSET=M_.AMBIENT_GROUND_OFFSET,C_.GLOBAL_FOG_COLOR_OFFSET=M_.GLOBAL_FOG_COLOR_OFFSET,C_.GLOBAL_FOG_BASE_OFFSET=M_.GLOBAL_FOG_BASE_OFFSET,C_.GLOBAL_FOG_ADD_OFFSET=M_.GLOBAL_FOG_ADD_OFFSET,C_.NEAR_FAR_OFFSET=M_.NEAR_FAR_OFFSET,C_.VIEW_PORT_OFFSET=M_.VIEW_PORT_OFFSET,C_.COUNT=M_.COUNT,C_.SIZE=M_.SIZE,C_.NAME="CCCamera",C_.BINDING=E_.UBO_CAMERA,C_.DESCRIPTOR=new D(g.BINDING,P.UNIFORM_BUFFER,1,C.ALL),C_.LAYOUT=new t(I_.GLOBAL,g.BINDING,g.NAME,[new U("cc_matView",B.MAT4,1),new U("cc_matViewInv",B.MAT4,1),new U("cc_matProj",B.MAT4,1),new U("cc_matProjInv",B.MAT4,1),new U("cc_matViewProj",B.MAT4,1),new U("cc_matViewProjInv",B.MAT4,1),new U("cc_cameraPos",B.FLOAT4,1),new U("cc_surfaceTransform",B.FLOAT4,1),new U("cc_screenScale",B.FLOAT4,1),new U("cc_exposure",B.FLOAT4,1),new U("cc_mainLitDir",B.FLOAT4,1),new U("cc_mainLitColor",B.FLOAT4,1),new U("cc_ambientSky",B.FLOAT4,1),new U("cc_ambientGround",B.FLOAT4,1),new U("cc_fogColor",B.FLOAT4,1),new U("cc_fogBase",B.FLOAT4,1),new U("cc_fogAdd",B.FLOAT4,1),new U("cc_nearFar",B.FLOAT4,1),new U("cc_viewPort",B.FLOAT4,1)],1),O_.layouts[C_.NAME]=C_.LAYOUT,O_.bindings[C_.BINDING]=C_.DESCRIPTOR,_("b",P_),function(_){_[_.MAT_LIGHT_VIEW_OFFSET=0]="MAT_LIGHT_VIEW_OFFSET",_[_.MAT_LIGHT_VIEW_PROJ_OFFSET=16]="MAT_LIGHT_VIEW_PROJ_OFFSET",_[_.SHADOW_INV_PROJ_DEPTH_INFO_OFFSET=32]="SHADOW_INV_PROJ_DEPTH_INFO_OFFSET",_[_.SHADOW_PROJ_DEPTH_INFO_OFFSET=36]="SHADOW_PROJ_DEPTH_INFO_OFFSET",_[_.SHADOW_PROJ_INFO_OFFSET=40]="SHADOW_PROJ_INFO_OFFSET",_[_.SHADOW_NEAR_FAR_LINEAR_SATURATION_INFO_OFFSET=44]="SHADOW_NEAR_FAR_LINEAR_SATURATION_INFO_OFFSET",_[_.SHADOW_WIDTH_HEIGHT_PCF_BIAS_INFO_OFFSET=48]="SHADOW_WIDTH_HEIGHT_PCF_BIAS_INFO_OFFSET",_[_.SHADOW_LIGHT_PACKING_NBIAS_NULL_INFO_OFFSET=52]="SHADOW_LIGHT_PACKING_NBIAS_NULL_INFO_OFFSET",_[_.SHADOW_COLOR_OFFSET=56]="SHADOW_COLOR_OFFSET",_[_.PLANAR_NORMAL_DISTANCE_INFO_OFFSET=60]="PLANAR_NORMAL_DISTANCE_INFO_OFFSET",_[_.COUNT=64]="COUNT",_[_.SIZE=256]="SIZE"}(P_||_("b",P_={}));var t_,U_=_("K",(function(){}));m=U_,U_.MAT_LIGHT_VIEW_OFFSET=P_.MAT_LIGHT_VIEW_OFFSET,U_.MAT_LIGHT_VIEW_PROJ_OFFSET=P_.MAT_LIGHT_VIEW_PROJ_OFFSET,U_.SHADOW_INV_PROJ_DEPTH_INFO_OFFSET=P_.SHADOW_INV_PROJ_DEPTH_INFO_OFFSET,U_.SHADOW_PROJ_DEPTH_INFO_OFFSET=P_.SHADOW_PROJ_DEPTH_INFO_OFFSET,U_.SHADOW_PROJ_INFO_OFFSET=P_.SHADOW_PROJ_INFO_OFFSET,U_.SHADOW_NEAR_FAR_LINEAR_SATURATION_INFO_OFFSET=P_.SHADOW_NEAR_FAR_LINEAR_SATURATION_INFO_OFFSET,U_.SHADOW_WIDTH_HEIGHT_PCF_BIAS_INFO_OFFSET=P_.SHADOW_WIDTH_HEIGHT_PCF_BIAS_INFO_OFFSET,U_.SHADOW_LIGHT_PACKING_NBIAS_NULL_INFO_OFFSET=P_.SHADOW_LIGHT_PACKING_NBIAS_NULL_INFO_OFFSET,U_.SHADOW_COLOR_OFFSET=P_.SHADOW_COLOR_OFFSET,U_.PLANAR_NORMAL_DISTANCE_INFO_OFFSET=P_.PLANAR_NORMAL_DISTANCE_INFO_OFFSET,U_.COUNT=P_.COUNT,U_.SIZE=P_.SIZE,U_.NAME="CCShadow",U_.BINDING=E_.UBO_SHADOW,U_.DESCRIPTOR=new D(m.BINDING,P.UNIFORM_BUFFER,1,C.ALL),U_.LAYOUT=new t(I_.GLOBAL,m.BINDING,m.NAME,[new U("cc_matLightView",B.MAT4,1),new U("cc_matLightViewProj",B.MAT4,1),new U("cc_shadowInvProjDepthInfo",B.FLOAT4,1),new U("cc_shadowProjDepthInfo",B.FLOAT4,1),new U("cc_shadowProjInfo",B.FLOAT4,1),new U("cc_shadowNFLSInfo",B.FLOAT4,1),new U("cc_shadowWHPBInfo",B.FLOAT4,1),new U("cc_shadowLPNNInfo",B.FLOAT4,1),new U("cc_shadowColor",B.FLOAT4,1),new U("cc_planarNDInfo",B.FLOAT4,1)],1),O_.layouts[U_.NAME]=U_.LAYOUT,O_.bindings[U_.BINDING]=U_.DESCRIPTOR,_("O",t_),function(_){_[_.CSM_LEVEL_COUNT=4]="CSM_LEVEL_COUNT",_[_.CSM_VIEW_DIR_0_OFFSET=0]="CSM_VIEW_DIR_0_OFFSET",_[_.CSM_VIEW_DIR_1_OFFSET=16]="CSM_VIEW_DIR_1_OFFSET",_[_.CSM_VIEW_DIR_2_OFFSET=32]="CSM_VIEW_DIR_2_OFFSET",_[_.CSM_ATLAS_OFFSET=48]="CSM_ATLAS_OFFSET",_[_.MAT_CSM_VIEW_PROJ_OFFSET=64]="MAT_CSM_VIEW_PROJ_OFFSET",_[_.CSM_PROJ_DEPTH_INFO_OFFSET=128]="CSM_PROJ_DEPTH_INFO_OFFSET",_[_.CSM_PROJ_INFO_OFFSET=144]="CSM_PROJ_INFO_OFFSET",_[_.CSM_SPLITS_INFO_OFFSET=160]="CSM_SPLITS_INFO_OFFSET",_[_.COUNT=164]="COUNT",_[_.SIZE=656]="SIZE"}(t_||_("O",t_={}));var B_=_("V",(function(){}));X=B_,B_.CSM_LEVEL_COUNT=t_.CSM_LEVEL_COUNT,B_.CSM_VIEW_DIR_0_OFFSET=t_.CSM_VIEW_DIR_0_OFFSET,B_.CSM_VIEW_DIR_1_OFFSET=t_.CSM_VIEW_DIR_1_OFFSET,B_.CSM_VIEW_DIR_2_OFFSET=t_.CSM_VIEW_DIR_2_OFFSET,B_.CSM_ATLAS_OFFSET=t_.CSM_ATLAS_OFFSET,B_.MAT_CSM_VIEW_PROJ_OFFSET=t_.MAT_CSM_VIEW_PROJ_OFFSET,B_.CSM_PROJ_DEPTH_INFO_OFFSET=t_.CSM_PROJ_DEPTH_INFO_OFFSET,B_.CSM_PROJ_INFO_OFFSET=t_.CSM_PROJ_INFO_OFFSET,B_.CSM_SPLITS_INFO_OFFSET=t_.CSM_SPLITS_INFO_OFFSET,B_.COUNT=t_.COUNT,B_.SIZE=t_.SIZE,B_.NAME="CCCSM",B_.BINDING=E_.UBO_CSM,B_.DESCRIPTOR=new D(X.BINDING,P.UNIFORM_BUFFER,1,C.FRAGMENT),B_.LAYOUT=new t(I_.GLOBAL,X.BINDING,X.NAME,[new U("cc_csmViewDir0",B.FLOAT4,X.CSM_LEVEL_COUNT),new U("cc_csmViewDir1",B.FLOAT4,X.CSM_LEVEL_COUNT),new U("cc_csmViewDir2",B.FLOAT4,X.CSM_LEVEL_COUNT),new U("cc_csmAtlas",B.FLOAT4,X.CSM_LEVEL_COUNT),new U("cc_matCSMViewProj",B.MAT4,X.CSM_LEVEL_COUNT),new U("cc_csmProjDepthInfo",B.FLOAT4,X.CSM_LEVEL_COUNT),new U("cc_csmProjInfo",B.FLOAT4,X.CSM_LEVEL_COUNT),new U("cc_csmSplitsInfo",B.FLOAT4,1)],1),O_.layouts[B_.NAME]=B_.LAYOUT,O_.bindings[B_.BINDING]=B_.DESCRIPTOR;var a_="cc_shadowMap",c_=_("W",E_.SAMPLER_SHADOWMAP),i_=new D(c_,P.SAMPLER_TEXTURE,1,C.FRAGMENT),r_=new a(I_.GLOBAL,c_,a_,B.SAMPLER2D,1);O_.layouts[a_]=r_,O_.bindings[c_]=i_;var o_="cc_environment",G_=_("a0",E_.SAMPLER_ENVIRONMENT),u_=new D(G_,P.SAMPLER_TEXTURE,1,C.FRAGMENT),H_=new a(I_.GLOBAL,G_,o_,B.SAMPLER_CUBE,1);O_.layouts[o_]=H_,O_.bindings[G_]=u_;var s_="cc_diffuseMap",w_=_("a1",E_.SAMPLER_DIFFUSEMAP),l_=new D(w_,P.SAMPLER_TEXTURE,1,C.FRAGMENT),W_=new a(I_.GLOBAL,w_,s_,B.SAMPLER_CUBE,1);O_.layouts[s_]=W_,O_.bindings[w_]=l_;var V_,d_="cc_spotShadowMap",f_=_("X",E_.SAMPLER_SPOT_SHADOW_MAP),g_=new D(f_,P.SAMPLER_TEXTURE,1,C.FRAGMENT),m_=new a(I_.GLOBAL,f_,d_,B.SAMPLER2D,1);O_.layouts[d_]=m_,O_.bindings[f_]=g_,_("c",V_),function(_){_[_.MAT_WORLD_OFFSET=0]="MAT_WORLD_OFFSET",_[_.MAT_WORLD_IT_OFFSET=16]="MAT_WORLD_IT_OFFSET",_[_.LIGHTINGMAP_UVPARAM=32]="LIGHTINGMAP_UVPARAM",_[_.LOCAL_SHADOW_BIAS=36]="LOCAL_SHADOW_BIAS",_[_.REFLECTION_PROBE_DATA1=40]="REFLECTION_PROBE_DATA1",_[_.REFLECTION_PROBE_DATA2=44]="REFLECTION_PROBE_DATA2",_[_.REFLECTION_PROBE_BLEND_DATA1=48]="REFLECTION_PROBE_BLEND_DATA1",_[_.REFLECTION_PROBE_BLEND_DATA2=52]="REFLECTION_PROBE_BLEND_DATA2",_[_.COUNT=56]="COUNT",_[_.SIZE=224]="SIZE",_[_.BINDING=F_.UBO_LOCAL]="BINDING"}(V_||_("c",V_={}));var X_=_("D",(function(){}));b=X_,X_.MAT_WORLD_OFFSET=V_.MAT_WORLD_OFFSET,X_.MAT_WORLD_IT_OFFSET=V_.MAT_WORLD_IT_OFFSET,X_.LIGHTINGMAP_UVPARAM=V_.LIGHTINGMAP_UVPARAM,X_.LOCAL_SHADOW_BIAS=V_.LOCAL_SHADOW_BIAS,X_.REFLECTION_PROBE_DATA1=V_.REFLECTION_PROBE_DATA1,X_.REFLECTION_PROBE_DATA2=V_.REFLECTION_PROBE_DATA2,X_.REFLECTION_PROBE_BLEND_DATA1=V_.REFLECTION_PROBE_BLEND_DATA1,X_.REFLECTION_PROBE_BLEND_DATA2=V_.REFLECTION_PROBE_BLEND_DATA2,X_.COUNT=V_.COUNT,X_.SIZE=V_.SIZE,X_.NAME="CCLocal",X_.BINDING=V_.BINDING,X_.DESCRIPTOR=new D(V_.BINDING,P.UNIFORM_BUFFER,1,C.VERTEX|C.FRAGMENT|C.COMPUTE,c.READ_ONLY,i.BUFFER),X_.LAYOUT=new t(I_.LOCAL,V_.BINDING,b.NAME,[new U("cc_matWorld",B.MAT4,1),new U("cc_matWorldIT",B.MAT4,1),new U("cc_lightingMapUVParam",B.FLOAT4,1),new U("cc_localShadowBias",B.FLOAT4,1),new U("cc_reflectionProbeData1",B.FLOAT4,1),new U("cc_reflectionProbeData2",B.FLOAT4,1),new U("cc_reflectionProbeBlendData1",B.FLOAT4,1),new U("cc_reflectionProbeBlendData2",B.FLOAT4,1)],1),T_.layouts[X_.NAME]=X_.LAYOUT,T_.bindings[V_.BINDING]=X_.DESCRIPTOR;var b_=_("d",(function(){}));Y=b_,b_.WORLD_BOUND_CENTER=0,b_.WORLD_BOUND_HALF_EXTENTS=Y.WORLD_BOUND_CENTER+4,b_.COUNT=Y.WORLD_BOUND_HALF_EXTENTS+4,b_.SIZE=4*Y.COUNT,b_.NAME="CCWorldBound",b_.BINDING=F_.UBO_LOCAL,b_.DESCRIPTOR=new D(Y.BINDING,P.UNIFORM_BUFFER,1,C.VERTEX|C.COMPUTE,c.READ_ONLY,i.BUFFER),b_.LAYOUT=new t(I_.LOCAL,Y.BINDING,Y.NAME,[new U("cc_worldBoundCenter",B.FLOAT4,1),new U("cc_worldBoundHalfExtents",B.FLOAT4,1)],1),T_.layouts[b_.NAME]=b_.LAYOUT,T_.bindings[b_.BINDING]=b_.DESCRIPTOR;var Y_,p_=_("t","a_matWorld0"),J_=_("u","a_sh_linear_const_r"),v_=function(){};p=v_,v_.BATCHING_COUNT=10,v_.MAT_WORLDS_OFFSET=0,v_.COUNT=16*p.BATCHING_COUNT,v_.SIZE=4*p.COUNT,v_.NAME="CCLocalBatched",v_.BINDING=F_.UBO_LOCAL,v_.DESCRIPTOR=new D(p.BINDING,P.UNIFORM_BUFFER,1,C.VERTEX|C.COMPUTE,c.READ_ONLY,i.BUFFER),v_.LAYOUT=new t(I_.LOCAL,p.BINDING,p.NAME,[new U("cc_matWorlds",B.MAT4,p.BATCHING_COUNT)],1),T_.layouts[v_.NAME]=v_.LAYOUT,T_.bindings[v_.BINDING]=v_.DESCRIPTOR,_("A",Y_),function(_){_[_.LIGHTS_PER_PASS=1]="LIGHTS_PER_PASS",_[_.LIGHT_POS_OFFSET=0]="LIGHT_POS_OFFSET",_[_.LIGHT_COLOR_OFFSET=4]="LIGHT_COLOR_OFFSET",_[_.LIGHT_SIZE_RANGE_ANGLE_OFFSET=8]="LIGHT_SIZE_RANGE_ANGLE_OFFSET",_[_.LIGHT_DIR_OFFSET=12]="LIGHT_DIR_OFFSET",_[_.LIGHT_BOUNDING_SIZE_VS_OFFSET=16]="LIGHT_BOUNDING_SIZE_VS_OFFSET",_[_.COUNT=20]="COUNT",_[_.SIZE=80]="SIZE"}(Y_||_("A",Y_={}));var h_=_("B",(function(){}));J=h_,h_.LIGHTS_PER_PASS=Y_.LIGHTS_PER_PASS,h_.LIGHT_POS_OFFSET=Y_.LIGHT_POS_OFFSET,h_.LIGHT_COLOR_OFFSET=Y_.LIGHT_COLOR_OFFSET,h_.LIGHT_SIZE_RANGE_ANGLE_OFFSET=Y_.LIGHT_SIZE_RANGE_ANGLE_OFFSET,h_.LIGHT_DIR_OFFSET=Y_.LIGHT_DIR_OFFSET,h_.LIGHT_BOUNDING_SIZE_VS_OFFSET=Y_.LIGHT_BOUNDING_SIZE_VS_OFFSET,h_.COUNT=Y_.COUNT,h_.SIZE=Y_.SIZE,h_.NAME="CCForwardLight",h_.BINDING=F_.UBO_FORWARD_LIGHTS,h_.DESCRIPTOR=new D(J.BINDING,P.DYNAMIC_UNIFORM_BUFFER,1,C.FRAGMENT,c.READ_ONLY,i.BUFFER),h_.LAYOUT=new t(I_.LOCAL,J.BINDING,J.NAME,[new U("cc_lightPos",B.FLOAT4,Y_.LIGHTS_PER_PASS),new U("cc_lightColor",B.FLOAT4,Y_.LIGHTS_PER_PASS),new U("cc_lightSizeRangeAngle",B.FLOAT4,Y_.LIGHTS_PER_PASS),new U("cc_lightDir",B.FLOAT4,Y_.LIGHTS_PER_PASS),new U("cc_lightBoundingSizeVS",B.FLOAT4,Y_.LIGHTS_PER_PASS)],1),T_.layouts[h_.NAME]=h_.LAYOUT,T_.bindings[h_.BINDING]=h_.DESCRIPTOR;var Z_=_("E",(function(){}));Z_.LIGHTS_PER_PASS=10;var y_=_("p",(function(){}));v=y_,y_.JOINTS_TEXTURE_INFO_OFFSET=0,y_.COUNT=v.JOINTS_TEXTURE_INFO_OFFSET+4,y_.SIZE=4*v.COUNT,y_.NAME="CCSkinningTexture",y_.BINDING=F_.UBO_SKINNING_TEXTURE,y_.DESCRIPTOR=new D(v.BINDING,P.UNIFORM_BUFFER,1,C.VERTEX,c.READ_ONLY,i.BUFFER),y_.LAYOUT=new t(I_.LOCAL,v.BINDING,v.NAME,[new U("cc_jointTextureInfo",B.FLOAT4,1)],1),T_.layouts[y_.NAME]=y_.LAYOUT,T_.bindings[y_.BINDING]=y_.DESCRIPTOR;var k_=_("m",(function(){}));h=k_,k_.JOINTS_ANIM_INFO_OFFSET=0,k_.COUNT=h.JOINTS_ANIM_INFO_OFFSET+4,k_.SIZE=4*h.COUNT,k_.NAME="CCSkinningAnimation",k_.BINDING=F_.UBO_SKINNING_ANIMATION,k_.DESCRIPTOR=new D(h.BINDING,P.UNIFORM_BUFFER,1,C.VERTEX,c.READ_ONLY,i.BUFFER),k_.LAYOUT=new t(I_.LOCAL,h.BINDING,h.NAME,[new U("cc_jointAnimInfo",B.FLOAT4,1)],1),T_.layouts[k_.NAME]=k_.LAYOUT,T_.bindings[k_.BINDING]=k_.DESCRIPTOR;var j_,x_=_("I","a_jointAnimInfo"),K_=_("n",function(){function _(){}return _.initLayout=function(E){_._jointUniformCapacity=E,_._count=12*E,_._size=4*_._count,_.LAYOUT.members[0].count=3*E},n(_,null,[{key:"JOINT_UNIFORM_CAPACITY",get:function(){return _._jointUniformCapacity}},{key:"COUNT",get:function(){return _._count}},{key:"SIZE",get:function(){return _._size}}]),_}());function Q_(_){K_.initLayout(_),T_.layouts[K_.NAME]=K_.LAYOUT,T_.bindings[K_.BINDING]=K_.DESCRIPTOR}Z=K_,K_._jointUniformCapacity=0,K_._count=0,K_._size=0,K_.NAME="CCSkinning",K_.BINDING=F_.UBO_SKINNING_TEXTURE,K_.DESCRIPTOR=new D(Z.BINDING,P.UNIFORM_BUFFER,1,C.VERTEX,c.READ_ONLY,i.BUFFER),K_.LAYOUT=new t(I_.LOCAL,Z.BINDING,Z.NAME,[new U("cc_joints",B.FLOAT4,1)],1),_("e",j_),function(_){_[_.MAX_MORPH_TARGET_COUNT=60]="MAX_MORPH_TARGET_COUNT",_[_.OFFSET_OF_WEIGHTS=0]="OFFSET_OF_WEIGHTS",_[_.OFFSET_OF_DISPLACEMENT_TEXTURE_WIDTH=240]="OFFSET_OF_DISPLACEMENT_TEXTURE_WIDTH",_[_.OFFSET_OF_DISPLACEMENT_TEXTURE_HEIGHT=244]="OFFSET_OF_DISPLACEMENT_TEXTURE_HEIGHT",_[_.OFFSET_OF_VERTICES_COUNT=248]="OFFSET_OF_VERTICES_COUNT",_[_.COUNT_BASE_4_BYTES=64]="COUNT_BASE_4_BYTES",_[_.SIZE=256]="SIZE"}(j_||_("e",j_={}));var z_=_("f",(function(){}));y=z_,z_.MAX_MORPH_TARGET_COUNT=j_.MAX_MORPH_TARGET_COUNT,z_.OFFSET_OF_WEIGHTS=j_.OFFSET_OF_WEIGHTS,z_.OFFSET_OF_DISPLACEMENT_TEXTURE_WIDTH=j_.OFFSET_OF_DISPLACEMENT_TEXTURE_WIDTH,z_.OFFSET_OF_DISPLACEMENT_TEXTURE_HEIGHT=j_.OFFSET_OF_DISPLACEMENT_TEXTURE_HEIGHT,z_.OFFSET_OF_VERTICES_COUNT=j_.OFFSET_OF_VERTICES_COUNT,z_.COUNT_BASE_4_BYTES=j_.COUNT_BASE_4_BYTES,z_.SIZE=j_.SIZE,z_.NAME="CCMorph",z_.BINDING=F_.UBO_MORPH,z_.DESCRIPTOR=new D(y.BINDING,P.UNIFORM_BUFFER,1,C.VERTEX,c.READ_ONLY,i.BUFFER),z_.LAYOUT=new t(I_.LOCAL,y.BINDING,y.NAME,[new U("cc_displacementWeights",B.FLOAT4,j_.MAX_MORPH_TARGET_COUNT/4),new U("cc_displacementTextureInfo",B.FLOAT4,1)],1),T_.layouts[z_.NAME]=z_.LAYOUT,T_.bindings[z_.BINDING]=z_.DESCRIPTOR;var q_,$_=function(){};k=$_,$_.NAME="CCUILocal",$_.BINDING=F_.UBO_UI_LOCAL,$_.DESCRIPTOR=new D(k.BINDING,P.DYNAMIC_UNIFORM_BUFFER,1,C.VERTEX,c.READ_ONLY,i.BUFFER),$_.LAYOUT=new t(I_.LOCAL,k.BINDING,k.NAME,[new U("cc_local_data",B.FLOAT4,1)],1),T_.layouts[$_.NAME]=$_.LAYOUT,T_.bindings[$_.BINDING]=$_.DESCRIPTOR,_("s",q_),function(_){_[_.SH_LINEAR_CONST_R_OFFSET=0]="SH_LINEAR_CONST_R_OFFSET",_[_.SH_LINEAR_CONST_G_OFFSET=4]="SH_LINEAR_CONST_G_OFFSET",_[_.SH_LINEAR_CONST_B_OFFSET=8]="SH_LINEAR_CONST_B_OFFSET",_[_.SH_QUADRATIC_R_OFFSET=12]="SH_QUADRATIC_R_OFFSET",_[_.SH_QUADRATIC_G_OFFSET=16]="SH_QUADRATIC_G_OFFSET",_[_.SH_QUADRATIC_B_OFFSET=20]="SH_QUADRATIC_B_OFFSET",_[_.SH_QUADRATIC_A_OFFSET=24]="SH_QUADRATIC_A_OFFSET",_[_.COUNT=28]="COUNT",_[_.SIZE=112]="SIZE",_[_.BINDING=F_.UBO_SH]="BINDING"}(q_||_("s",q_={}));var _E=function(){};j=_E,_E.SH_LINEAR_CONST_R_OFFSET=q_.SH_LINEAR_CONST_R_OFFSET,_E.SH_LINEAR_CONST_G_OFFSET=q_.SH_LINEAR_CONST_G_OFFSET,_E.SH_LINEAR_CONST_B_OFFSET=q_.SH_LINEAR_CONST_B_OFFSET,_E.SH_QUADRATIC_R_OFFSET=q_.SH_QUADRATIC_R_OFFSET,_E.SH_QUADRATIC_G_OFFSET=q_.SH_QUADRATIC_G_OFFSET,_E.SH_QUADRATIC_B_OFFSET=q_.SH_QUADRATIC_B_OFFSET,_E.SH_QUADRATIC_A_OFFSET=q_.SH_QUADRATIC_A_OFFSET,_E.COUNT=q_.COUNT,_E.SIZE=q_.SIZE,_E.NAME="CCSH",_E.BINDING=q_.BINDING,_E.DESCRIPTOR=new D(q_.BINDING,P.UNIFORM_BUFFER,1,C.FRAGMENT,c.READ_ONLY,i.BUFFER),_E.LAYOUT=new t(I_.LOCAL,q_.BINDING,j.NAME,[new U("cc_sh_linear_const_r",B.FLOAT4,1),new U("cc_sh_linear_const_g",B.FLOAT4,1),new U("cc_sh_linear_const_b",B.FLOAT4,1),new U("cc_sh_quadratic_r",B.FLOAT4,1),new U("cc_sh_quadratic_g",B.FLOAT4,1),new U("cc_sh_quadratic_b",B.FLOAT4,1),new U("cc_sh_quadratic_a",B.FLOAT4,1)],1),T_.layouts[_E.NAME]=_E.LAYOUT,T_.bindings[q_.BINDING]=_E.DESCRIPTOR;var EE="cc_jointTexture",OE=_("q",F_.SAMPLER_JOINTS),TE=new D(OE,P.SAMPLER_TEXTURE,1,C.VERTEX,c.READ_ONLY,i.TEX2D),FE=new a(I_.LOCAL,OE,EE,B.SAMPLER2D,1);T_.layouts[EE]=FE,T_.bindings[OE]=TE;var AE="cc_realtimeJoint",SE=_("o",F_.SAMPLER_JOINTS),IE=new D(SE,P.SAMPLER_TEXTURE,1,C.VERTEX,c.READ_ONLY,i.TEX2D),NE=new a(I_.LOCAL,SE,AE,B.SAMPLER2D,1);T_.layouts[AE]=NE,T_.bindings[SE]=IE;var RE="cc_PositionDisplacements",LE=_("j",F_.SAMPLER_MORPH_POSITION),nE=new D(LE,P.SAMPLER_TEXTURE,1,C.VERTEX,c.READ_ONLY,i.TEX2D),eE=new a(I_.LOCAL,LE,RE,B.SAMPLER2D,1);T_.layouts[RE]=eE,T_.bindings[LE]=nE;var ME="cc_NormalDisplacements",DE=_("h",F_.SAMPLER_MORPH_NORMAL),PE=new D(DE,P.SAMPLER_TEXTURE,1,C.VERTEX,c.READ_ONLY,i.TEX2D),CE=new a(I_.LOCAL,DE,ME,B.SAMPLER2D,1);T_.layouts[ME]=CE,T_.bindings[DE]=PE;var tE="cc_TangentDisplacements",UE=_("g",F_.SAMPLER_MORPH_TANGENT),BE=new D(UE,P.SAMPLER_TEXTURE,1,C.VERTEX,c.READ_ONLY,i.TEX2D),aE=new a(I_.LOCAL,UE,tE,B.SAMPLER2D,1);T_.layouts[tE]=aE,T_.bindings[UE]=BE;var cE="cc_lightingMap",iE=_("v",F_.SAMPLER_LIGHTMAP),rE=new D(iE,P.SAMPLER_TEXTURE,1,C.FRAGMENT,c.READ_ONLY,i.TEX2D),oE=new a(I_.LOCAL,iE,cE,B.SAMPLER2D,1);T_.layouts[cE]=oE,T_.bindings[iE]=rE;var GE="cc_spriteTexture",uE=F_.SAMPLER_SPRITE,HE=new D(uE,P.SAMPLER_TEXTURE,1,C.FRAGMENT,c.READ_ONLY,i.TEX2D),sE=new a(I_.LOCAL,uE,GE,B.SAMPLER2D,1);T_.layouts[GE]=sE,T_.bindings[uE]=HE;var wE="cc_reflectionProbeCubemap",lE=_("w",F_.SAMPLER_REFLECTION_PROBE_CUBE),WE=new D(lE,P.SAMPLER_TEXTURE,1,C.FRAGMENT,c.READ_ONLY,i.TEXCUBE),VE=new a(I_.LOCAL,lE,wE,B.SAMPLER_CUBE,1);T_.layouts[wE]=VE,T_.bindings[lE]=WE;var dE="cc_reflectionProbePlanarMap",fE=_("x",F_.SAMPLER_REFLECTION_PROBE_PLANAR),gE=new D(fE,P.SAMPLER_TEXTURE,1,C.FRAGMENT,c.READ_ONLY,i.TEX2D),mE=new a(I_.LOCAL,fE,dE,B.SAMPLER2D,1);T_.layouts[dE]=mE,T_.bindings[fE]=gE;var XE="cc_reflectionProbeDataMap",bE=_("y",F_.SAMPLER_REFLECTION_PROBE_DATA_MAP),YE=new D(bE,P.SAMPLER_TEXTURE,1,C.FRAGMENT,c.READ_ONLY,i.TEX2D),pE=new a(I_.LOCAL,bE,XE,B.SAMPLER2D,1);T_.layouts[XE]=pE,T_.bindings[bE]=YE;var JE=F_.SAMPLER_REFLECTION_PROBE_DATA_MAP+1;new D(JE,P.SAMPLER_TEXTURE,1,C.FRAGMENT,c.READ_ONLY,i.TEXCUBE),new a(I_.LOCAL,JE,"cc_reflectionProbeBlendCubemap",B.SAMPLER_CUBE,1);var vE,hE=_("C",K.makeMaskExclude([K.BitMask.UI_2D,K.BitMask.GIZMOS,K.BitMask.EDITOR,K.BitMask.SCENE_GIZMO,K.BitMask.PROFILER])),ZE=K.makeMaskExclude([K.BitMask.UI_2D,K.BitMask.PROFILER]),yE=K.Enum.ALL;function kE(_){if(vE)return vE;var E=new u(H.TEX2D,s.NONE,jE(_)?o.R32F:o.RGBA8,16,16,w.NONE,1,1,l.X1,1);return vE=_.createTexture(E)}function jE(_){return(_.getFormatFeatures(o.R32F)&(G.RENDER_TARGET|G.SAMPLED_TEXTURE))==(G.RENDER_TARGET|G.SAMPLED_TEXTURE)&&!(_.gfxAPI===W.WEBGL)}function xE(_){return(_.getFormatFeatures(o.RGBA16F)&(G.RENDER_TARGET|G.SAMPLED_TEXTURE))==(G.RENDER_TARGET|G.SAMPLED_TEXTURE)}function KE(){return!(!M.rendering||!M.rendering.enableEffectImport)}_("r",Object.freeze({__proto__:null,CAMERA_DEFAULT_MASK:hE,CAMERA_EDITOR_MASK:ZE,ENABLE_PROBE_BLEND:!1,INST_JOINT_ANIM_INFO:x_,INST_MAT_WORLD:p_,INST_SH:J_,JOINT_UNIFORM_CAPACITY:30,MODEL_ALWAYS_MASK:yE,get ModelLocalBindings(){return F_},PIPELINE_FLOW_FORWARD:$,PIPELINE_FLOW_MAIN:q,PIPELINE_FLOW_SHADOW:__,PIPELINE_FLOW_SMAA:"SMAAFlow",PIPELINE_FLOW_TONEMAP:"ToneMapFlow",get PipelineGlobalBindings(){return E_},get RenderPassStage(){return Q},get RenderPriority(){return z},get SetIndex(){return I_},UBOCSM:B_,get UBOCSMEnum(){return t_},UBOCamera:C_,get UBOCameraEnum(){return M_},UBODeferredLight:Z_,UBOForwardLight:h_,get UBOForwardLightEnum(){return Y_},UBOGlobal:D_,get UBOGlobalEnum(){return n_},UBOLocal:X_,UBOLocalBatched:v_,get UBOLocalEnum(){return V_},UBOMorph:z_,get UBOMorphEnum(){return j_},UBOSH:_E,get UBOSHEnum(){return q_},UBOShadow:U_,get UBOShadowEnum(){return P_},UBOSkinning:K_,UBOSkinningAnimation:k_,UBOSkinningTexture:y_,UBOUILocal:$_,UBOWorldBound:b_,UNIFORM_DIFFUSEMAP_BINDING:w_,UNIFORM_ENVIRONMENT_BINDING:G_,UNIFORM_JOINT_TEXTURE_BINDING:OE,UNIFORM_LIGHTMAP_TEXTURE_BINDING:iE,UNIFORM_NORMAL_MORPH_TEXTURE_BINDING:DE,UNIFORM_POSITION_MORPH_TEXTURE_BINDING:LE,UNIFORM_REALTIME_JOINT_TEXTURE_BINDING:SE,UNIFORM_REFLECTION_PROBE_BLEND_CUBEMAP_BINDING:JE,UNIFORM_REFLECTION_PROBE_CUBEMAP_BINDING:lE,UNIFORM_REFLECTION_PROBE_DATA_MAP_BINDING:bE,UNIFORM_REFLECTION_PROBE_TEXTURE_BINDING:fE,UNIFORM_SHADOWMAP_BINDING:c_,UNIFORM_SPOT_SHADOW_MAP_TEXTURE_BINDING:f_,UNIFORM_SPRITE_TEXTURE_BINDING:uE,UNIFORM_TANGENT_MORPH_TEXTURE_BINDING:UE,bindingMappingInfo:e_,getDefaultShadowTexture:kE,globalDescriptorSetLayout:O_,isEnableEffect:KE,localDescriptorSetLayout:T_,localDescriptorSetLayout_ResizeMaxJoints:Q_,supportsR16HalfFloatTexture:function(_){return(_.getFormatFeatures(o.R16F)&(G.RENDER_TARGET|G.SAMPLED_TEXTURE))==(G.RENDER_TARGET|G.SAMPLED_TEXTURE)},supportsR32FloatTexture:jE,supportsRGBA16HalfFloatTexture:xE,supportsRGBA32FloatTexture:function(_){return(_.getFormatFeatures(o.RGBA32F)&(G.RENDER_TARGET|G.SAMPLED_TEXTURE))==(G.RENDER_TARGET|G.SAMPLED_TEXTURE)}})),_("P",function(){function _(){}return _.getOrCreatePipelineState=function(_,E,O,T,F){var A=E.hash^T.hash^F.attributesHash^O.typedID,S=this._PSOHashMap.get(A);if(!S){var I=E.pipelineLayout,N=new V(F.attributes),R=new d(O,I,T,N,E.rasterizerState,E.depthStencilState,E.blendState,E.primitive,E.dynamicStates);S=_.createPipelineState(R),this._PSOHashMap.set(A,S)}return S},_}())._PSOHashMap=new Map}}}));
