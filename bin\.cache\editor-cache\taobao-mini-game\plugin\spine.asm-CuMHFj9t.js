System.register([],(function(r){"use strict";return{execute:function(){var n;r("default",(n="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0,function(r={}){var e,f,i;e||(e=void 0!==r?r:{}),e.ready=new Promise(((r,n)=>{f=r,i=n}));var t,u=Object.assign({},e),o="";function a(r){return e.locateFile?e.locateFile(r,o):o+r}"undefined"!=typeof document&&document.currentScript&&(o=document.currentScript.src),n&&(o=n),o=0!==o.indexOf("blob:")?o.substr(0,o.replace(/[?#].*/,"").lastIndexOf("/")+1):"",t=(r,n)=>{var e=new XMLHttpRequest;e.open("GET",J,!0),e.responseType="arraybuffer",e.onload=()=>{200==e.status||0==e.status&&e.response?r(e.response):n()},e.onerror=n,e.send(null)};var c,b=e.printErr||console.error.bind(console);function k(r){this.exports=function(r){var n=new ArrayBuffer(16),e=new Int32Array(n),f=new Float32Array(n),i=new Float64Array(n);function t(r){return e[r]}function u(r,n){e[r]=n}function o(){return i[0]}function a(r){i[0]=r}function c(){throw new Error("abort")}function b(r){f[2]=r}function k(){return f[2]}return function(r){var n=r.a,e=n.a,f=e.buffer;e.grow=function(r){r|=0;var n=0|$a(),t=n+r|0;if(n<t&&t<65536){var u=new ArrayBuffer(m(t,65536));new Int8Array(u).set(i),i=new Int8Array(u),s=new Int16Array(u),v=new Int32Array(u),l=new Uint8Array(u),h=new Uint16Array(u),d=new Uint32Array(u),p=new Float32Array(u),y=new Float64Array(u),f=u,e.buffer=f}return n};var i=new Int8Array(f),s=new Int16Array(f),v=new Int32Array(f),l=new Uint8Array(f),h=new Uint16Array(f),d=new Uint32Array(f),p=new Float32Array(f),y=new Float64Array(f),m=Math.imul,w=Math.fround,g=Math.abs,F=Math.clz32,A=Math.floor,T=Math.sqrt,$=n.b,I=n.c,C=n.d,P=n.e,E=n.f,O=n.g,R=n.h,S=n.i,W=n.j,G=n.k,U=n.l,j=n.m,H=n.n,L=n.o,M=n.p,_=n.q,z=n.r,x=n.s,J=n.t,K=n.u,B=n.v,N=n.w,q=n.x,D=n.y,V=87856,Z=0;function Y(){var r=0,n=0;D(21520,4855),O(21521,5283,4,0,-1),O(21522,3978,4,0,-1),O(21523,6130,4,0,-1),O(21524,5231,4,0,-1),O(21525,6073,4,0,-1),O(21526,5969,4,0,-1),O(21527,5935,4,0,-1),O(21528,6015,4,0,-1),O(21529,6045,4,0,-1),O(21530,3574,4,0,-1),O(21531,3790,4,0,-1),O(21532,5248,4,0,-1),C(21533,21534,21535,0,11240,385,11243,0,11243,0,5303,11245,386),P(21533,1,11248,11240,387,388),r=Wt(8),v[r+4>>2]=0,v[r>>2]=389,$(21533,4929,4,11280,11296,390,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=391,$(21533,4931,2,11304,11312,392,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=393,$(21533,2249,3,11316,11328,394,0|r,0,0),C(21539,21540,21541,0,11240,395,11243,0,11243,0,2377,11245,396),P(21539,1,11336,11240,397,398),r=Wt(8),v[r+4>>2]=0,v[r>>2]=399,$(21539,4929,4,11344,11360,400,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=401,$(21539,4931,2,11368,11312,402,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=403,$(21539,2249,3,11376,11388,404,0|r,0,0),r=Wt(4),v[r>>2]=405,$(21539,2242,4,11408,11360,1596,0|r,0,0),C(21544,21545,21546,0,11240,406,11243,0,11243,0,2357,11245,407),P(21544,1,11424,11240,408,409),r=Wt(8),v[r+4>>2]=0,v[r>>2]=410,$(21544,4929,4,11440,11296,411,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=412,$(21544,4931,2,11456,11312,413,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=414,$(21544,2249,3,11464,11328,415,0|r,0,0),r=Wt(4),v[r>>2]=416,$(21544,2242,4,11488,11296,1597,0|r,0,0),C(21547,21548,21549,0,11240,417,11243,0,11243,0,2143,11245,418),P(21547,1,11504,11240,419,420),r=Wt(8),v[r+4>>2]=0,v[r>>2]=421,$(21547,4929,4,11520,11296,422,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=423,$(21547,4931,2,11536,11312,424,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=425,$(21547,2249,3,11544,11328,426,0|r,0,0),r=Wt(4),v[r>>2]=427,$(21547,2242,4,11568,11296,1598,0|r,0,0),C(21550,21551,21552,0,11240,428,11243,0,11243,0,1760,11245,429),P(21550,1,11584,11240,430,431),r=Wt(8),v[r+4>>2]=0,v[r>>2]=432,$(21550,4929,4,11616,11296,433,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=434,$(21550,4931,2,11632,11312,435,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=436,$(21550,2249,3,11640,11328,437,0|r,0,0),r=Wt(4),v[r>>2]=438,$(21550,2242,4,11664,11296,1599,0|r,0,0),C(21554,21555,21556,0,11240,439,11243,0,11243,0,2125,11245,440),P(21554,1,11680,11240,441,442),r=Wt(8),v[r+4>>2]=0,v[r>>2]=443,$(21554,4929,4,11696,11296,444,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=445,$(21554,4931,2,11712,11312,446,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=447,$(21554,2249,3,11720,11328,448,0|r,0,0),r=Wt(4),v[r>>2]=449,$(21554,2242,4,11744,11296,1600,0|r,0,0),C(21557,21558,21559,0,11240,450,11243,0,11243,0,2391,11245,451),P(21557,1,11760,11240,452,453),r=Wt(8),v[r+4>>2]=0,v[r>>2]=454,$(21557,4929,4,11776,11296,455,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=456,$(21557,4931,2,11792,11312,457,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=458,$(21557,2249,3,11800,11328,459,0|r,0,0),r=Wt(4),v[r>>2]=460,$(21557,2242,4,11824,11296,1601,0|r,0,0),C(21560,21561,21562,0,11240,461,11243,0,11243,0,1498,11245,462),P(21560,1,11840,11240,463,464),r=Wt(8),v[r+4>>2]=0,v[r>>2]=465,$(21560,4929,4,11856,11296,466,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=467,$(21560,4931,2,11872,11312,468,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=469,$(21560,2249,3,11880,11328,470,0|r,0,0),r=Wt(4),v[r>>2]=471,$(21560,2242,4,11904,11296,1602,0|r,0,0),C(21564,21565,21566,0,11240,472,11243,0,11243,0,4867,11245,473),P(21564,1,11920,11240,474,475),r=Wt(8),v[r+4>>2]=0,v[r>>2]=476,$(21564,4929,4,11936,11296,477,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=478,$(21564,4931,2,11952,11312,479,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=480,$(21564,2249,3,11960,11328,481,0|r,0,0),r=Wt(4),v[r>>2]=482,$(21564,2242,4,11984,11296,1603,0|r,0,0),C(21567,21568,21569,0,11240,483,11243,0,11243,0,3283,11245,484),P(21567,1,12e3,11240,485,486),r=Wt(8),v[r+4>>2]=0,v[r>>2]=487,$(21567,4929,4,12016,11296,488,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=489,$(21567,4931,2,12032,11312,490,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=491,$(21567,2249,3,12040,11328,492,0|r,0,0),C(21570,21571,21572,0,11240,493,11243,0,11243,0,3500,11245,494),P(21570,1,12052,11240,495,496),r=Wt(8),v[r+4>>2]=0,v[r>>2]=497,$(21570,4929,4,12064,11296,498,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=499,$(21570,4931,2,12080,11312,500,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=501,$(21570,2249,3,12088,11328,502,0|r,0,0),C(21574,21575,21576,0,11240,503,11243,0,11243,0,3340,11245,504),P(21574,1,12100,11240,505,506),r=Wt(8),v[r+4>>2]=0,v[r>>2]=507,$(21574,4929,4,12112,11296,508,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=509,$(21574,4931,2,12128,11312,510,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=511,$(21574,2249,3,12136,11328,512,0|r,0,0),C(21578,21579,21580,0,11240,513,11243,0,11243,0,3386,11245,514),P(21578,1,12148,11240,515,516),r=Wt(8),v[r+4>>2]=0,v[r>>2]=517,$(21578,4929,4,12160,11296,518,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=519,$(21578,4931,2,12176,11312,520,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=521,$(21578,2249,3,12184,11328,522,0|r,0,0),C(21581,21582,21583,0,11240,523,11243,0,11243,0,3449,11245,524),P(21581,1,12196,11240,525,526),r=Wt(8),v[r+4>>2]=0,v[r>>2]=527,$(21581,4929,4,12208,11296,528,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=529,$(21581,4931,2,12224,11312,530,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=531,$(21581,2249,3,12232,11328,532,0|r,0,0),C(21584,21585,21586,0,11240,533,11243,0,11243,0,3360,11245,534),P(21584,1,12244,11240,535,536),r=Wt(8),v[r+4>>2]=0,v[r>>2]=537,$(21584,4929,4,12256,11296,538,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=539,$(21584,4931,2,12272,11312,540,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=541,$(21584,2249,3,12280,11328,542,0|r,0,0),C(21587,21588,21589,0,11240,543,11243,0,11243,0,3127,11245,544),P(21587,1,12292,11240,545,546),r=Wt(8),v[r+4>>2]=0,v[r>>2]=547,$(21587,4929,4,12304,11296,548,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=549,$(21587,4931,2,12320,11312,550,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=551,$(21587,2249,3,12328,11328,552,0|r,0,0),C(21591,21592,21593,0,11240,553,11243,0,11243,0,3262,11245,554),P(21591,1,12340,11240,555,556),r=Wt(8),v[r+4>>2]=0,v[r>>2]=557,$(21591,4929,4,12352,11296,558,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=559,$(21591,4931,2,12368,11312,560,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=561,$(21591,2249,3,12376,11328,562,0|r,0,0),C(21595,21596,21597,0,11240,563,11243,0,11243,0,3479,11245,564),P(21595,1,12388,11240,565,566),r=Wt(8),v[r+4>>2]=0,v[r>>2]=567,$(21595,4929,4,12400,11296,568,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=569,$(21595,4931,2,12416,11312,570,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=571,$(21595,2249,3,12424,11328,572,0|r,0,0),C(21599,21600,21601,0,11240,573,11243,0,11243,0,3224,11245,574),P(21599,1,12436,11240,575,576),r=Wt(8),v[r+4>>2]=0,v[r>>2]=577,$(21599,4929,4,12448,11296,578,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=579,$(21599,4931,2,12464,11312,580,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=581,$(21599,2249,3,12472,11328,582,0|r,0,0),C(21603,21604,21605,0,11240,583,11243,0,11243,0,3241,11245,584),P(21603,1,12484,11240,585,586),r=Wt(8),v[r+4>>2]=0,v[r>>2]=587,$(21603,4929,4,12496,11296,588,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=589,$(21603,4931,2,12512,11312,590,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=591,$(21603,2249,3,12520,11328,592,0|r,0,0),C(21607,21608,21609,0,11240,593,11243,0,11243,0,3174,11245,594),P(21607,1,12532,11240,595,596),r=Wt(8),v[r+4>>2]=0,v[r>>2]=597,$(21607,4929,4,12544,11296,598,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=599,$(21607,4931,2,12560,11312,600,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=601,$(21607,2249,3,12568,11328,602,0|r,0,0),C(21610,21611,21612,0,11240,603,11243,0,11243,0,3421,11245,604),P(21610,1,12580,11240,605,606),r=Wt(8),v[r+4>>2]=0,v[r>>2]=607,$(21610,4929,4,12592,11296,608,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=609,$(21610,4931,2,12608,11312,610,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=611,$(21610,2249,3,12616,11328,612,0|r,0,0),C(21613,21614,21615,0,11240,613,11243,0,11243,0,3143,11245,614),P(21613,1,12628,11240,615,616),r=Wt(8),v[r+4>>2]=0,v[r>>2]=617,$(21613,4929,4,12640,11296,618,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=619,$(21613,4931,2,12656,11312,620,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=621,$(21613,2249,3,12664,11328,622,0|r,0,0),C(21616,21617,21618,0,11240,623,11243,0,11243,0,3198,11245,624),P(21616,1,12676,11240,625,626),r=Wt(8),v[r+4>>2]=0,v[r>>2]=627,$(21616,4929,4,12688,11296,628,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=629,$(21616,4931,2,12704,11312,630,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=631,$(21616,2249,3,12712,11328,632,0|r,0,0),C(21619,21620,21621,0,11240,633,11243,0,11243,0,3299,11245,634),P(21619,1,12724,11240,635,636),r=Wt(8),v[r+4>>2]=0,v[r>>2]=637,$(21619,4929,4,12736,11296,638,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=639,$(21619,4931,2,12752,11312,640,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=641,$(21619,2249,3,12760,11328,642,0|r,0,0),r=Wt(4),v[r>>2]=643,$(21619,2242,4,12784,11296,1604,0|r,0,0),C(21622,21623,21624,0,11240,644,11243,0,11243,0,3100,11245,645),P(21622,1,12800,11240,646,647),r=Wt(8),v[r+4>>2]=0,v[r>>2]=648,$(21622,4929,4,12816,11296,649,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=650,$(21622,4931,2,12832,11312,651,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=652,$(21622,2249,3,12840,11328,653,0|r,0,0),C(21626,21627,21628,0,11240,654,11243,0,11243,0,3319,11245,655),P(21626,1,12852,11240,656,657),r=Wt(8),v[r+4>>2]=0,v[r>>2]=658,$(21626,4929,4,12864,11296,659,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=660,$(21626,4931,2,12880,11312,661,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=662,$(21626,2249,3,12888,11328,663,0|r,0,0),C(21629,21630,21631,0,11240,664,11243,0,11243,0,3079,11245,665),P(21629,1,12900,11240,666,667),r=Wt(8),v[r+4>>2]=0,v[r>>2]=668,$(21629,4929,4,12928,11296,669,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=670,$(21629,4931,2,12944,11312,671,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=672,$(21629,2249,3,12952,11328,673,0|r,0,0),C(21633,21634,21635,0,11240,674,11243,0,11243,0,3052,11245,675),P(21633,1,12964,11240,676,677),r=Wt(8),v[r+4>>2]=0,v[r>>2]=678,$(21633,4929,4,12992,11296,679,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=680,$(21633,4931,2,13008,11312,681,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=682,$(21633,2249,3,13016,11328,683,0|r,0,0),C(21636,21637,21638,0,11240,684,11243,0,11243,0,6953,11245,685),P(21636,1,13028,11240,686,687),P(21636,3,13032,13044,688,689),r=Wt(4),v[r>>2]=0,n=Wt(4),v[n>>2]=0,I(21636,1391,21542,13049,690,0|r,21542,13053,691,0|n),r=Wt(4),v[r>>2]=4,n=Wt(4),v[n>>2]=4,I(21636,1217,21542,13049,690,0|r,21542,13053,691,0|n),r=Wt(8),v[r+4>>2]=0,v[r>>2]=692,$(21636,2242,4,13072,13088,693,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=694,$(21636,4696,2,13096,13049,695,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=696,$(21636,4936,2,13104,11312,697,0|r,0,0),C(21639,21640,21641,0,11240,698,11243,0,11243,0,3548,11245,699),P(21639,1,13112,11240,700,701),P(21639,5,13120,13140,702,703),r=Wt(8),v[r+4>>2]=0,v[r>>2]=704,$(21639,2242,6,13152,13176,705,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=706,$(21639,6332,6,13152,13176,705,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=707,$(21639,3772,2,13184,11312,708,0|r,0,0),r=Wt(4),v[r>>2]=4,n=Wt(4),v[n>>2]=4,I(21639,3765,21542,13049,709,0|r,21542,13053,710,0|n),r=Wt(4),v[r>>2]=8,n=Wt(4),v[n>>2]=8,I(21639,4927,21542,13049,709,0|r,21542,13053,710,0|n),r=Wt(4),v[r>>2]=12,n=Wt(4),v[n>>2]=12,I(21639,6352,21542,13049,709,0|r,21542,13053,710,0|n),r=Wt(4),v[r>>2]=16,n=Wt(4),v[n>>2]=16,I(21639,6567,21542,13049,709,0|r,21542,13053,710,0|n),C(4305,7481,7475,0,11240,711,11243,0,11243,0,4305,11245,712),r=Wt(8),v[r+4>>2]=1,v[r>>2]=4,$(4305,1198,3,13192,13204,713,0|r,1,0),C(21642,21643,21644,0,11240,714,11243,0,11243,0,2253,11245,715),P(21642,1,13212,11240,716,717),C(6413,8011,8005,0,11240,718,11243,0,11243,0,6413,11245,719),P(6413,2,13216,11312,720,721),r=Wt(8),v[r+4>>2]=0,v[r>>2]=722,I(6413,5773,21520,11312,723,0|r,0,0,0,0),r=Wt(4),v[r>>2]=16,n=Wt(4),v[n>>2]=16,I(6413,3633,21537,11312,724,0|r,21537,13224,725,0|n),r=Wt(4),v[r>>2]=20,n=Wt(4),v[n>>2]=20,I(6413,6286,21645,11312,726,0|r,21645,13224,727,0|n),C(6392,7961,7955,6413,11240,728,11240,729,11240,730,6392,11245,731),P(6392,2,13232,11312,732,733),r=Wt(4),v[r>>2]=734,$(6392,2780,2,13240,11312,1605,0|r,0,0),r=Wt(4),v[r>>2]=40,n=Wt(4),v[n>>2]=40,I(6392,2246,21646,11312,735,0|r,21646,13224,736,0|n),r=Wt(4),v[r>>2]=44,n=Wt(4),v[n>>2]=44,I(6392,3998,21543,11312,737,0|r,21543,13224,738,0|n),r=Wt(4),v[r>>2]=48,n=Wt(4),v[n>>2]=48,I(6392,2581,21645,11312,739,0|r,21645,13224,740,0|n),r=Wt(4),v[r>>2]=49,n=Wt(4),v[n>>2]=49,I(6392,4833,21645,11312,739,0|r,21645,13224,740,0|n),r=Wt(4),v[r>>2]=50,n=Wt(4),v[n>>2]=50,I(6392,4494,21645,11312,739,0|r,21645,13224,740,0|n),r=Wt(4),v[r>>2]=52,n=Wt(4),v[n>>2]=52,I(6392,1231,21542,13049,741,0|r,21542,13053,742,0|n),r=Wt(4),v[r>>2]=56,n=Wt(4),v[n>>2]=56,I(6392,2590,21542,13049,741,0|r,21542,13053,742,0|n),C(6409,7985,7979,6413,11240,743,11240,744,11240,745,6409,11245,746),P(6409,2,13248,11312,747,748),r=Wt(4),v[r>>2]=749,$(6409,2780,2,13256,11312,1606,0|r,0,0),r=Wt(4),v[r>>2]=40,n=Wt(4),v[n>>2]=40,I(6409,2246,21647,11312,750,0|r,21647,13224,751,0|n),r=Wt(4),v[r>>2]=44,n=Wt(4),v[n>>2]=44,I(6409,5922,21527,11312,752,0|r,21527,13224,753,0|n),r=Wt(4),v[r>>2]=48,n=Wt(4),v[n>>2]=48,I(6409,6003,21528,11312,754,0|r,21528,13224,755,0|n),r=Wt(4),v[r>>2]=52,n=Wt(4),v[n>>2]=52,I(6409,6034,21529,11312,756,0|r,21529,13224,757,0|n),r=Wt(4),v[r>>2]=56,n=Wt(4),v[n>>2]=56,I(6409,4065,21542,13049,758,0|r,21542,13053,759,0|n),r=Wt(4),v[r>>2]=60,n=Wt(4),v[n>>2]=60,I(6409,3948,21542,13049,758,0|r,21542,13053,759,0|n),r=Wt(4),v[r>>2]=64,n=Wt(4),v[n>>2]=64,I(6409,4921,21542,13049,758,0|r,21542,13053,759,0|n),r=Wt(4),v[r>>2]=68,n=Wt(4),v[n>>2]=68,I(6409,1269,21542,13049,758,0|r,21542,13053,759,0|n),r=Wt(4),v[r>>2]=72,n=Wt(4),v[n>>2]=72,I(6409,1279,21542,13049,758,0|r,21542,13053,759,0|n),C(21648,21649,21650,0,11240,760,11243,0,11243,0,2976,11245,761),P(21648,1,13264,11240,762,763),r=Wt(8),v[r+4>>2]=0,v[r>>2]=764,$(21648,5116,4,13280,11296,765,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=766,$(21648,1682,4,13296,13088,767,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=768,$(21648,2071,6,13312,13176,769,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=770,$(21648,3872,3,13336,11328,771,0|r,0,0),r=Wt(4),v[r>>2]=772,$(21648,1668,4,13360,13088,1607,0|r,0,0),r=Wt(4),v[r>>2]=773,$(21648,2053,6,13376,13176,1608,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=774,$(21648,4740,2,13400,13049,775,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=776,$(21648,2190,2,13400,13049,775,0|r,0,0),C(21652,21602,21653,0,11240,777,11243,0,11243,0,1817,11245,778),P(21652,3,13408,13420,779,780),r=Wt(4),v[r>>2]=781,I(21652,6354,21654,11312,782,0|r,0,0,0,0),r=Wt(4),v[r>>2]=12,n=Wt(4),v[n>>2]=12,I(21652,5013,21543,11312,783,0|r,21543,13224,784,0|n),r=Wt(4),v[r>>2]=16,n=Wt(4),v[n>>2]=16,I(21652,5022,21542,13049,785,0|r,21542,13053,786,0|n),r=Wt(4),v[r>>2]=20,n=Wt(4),v[n>>2]=20,I(21652,5033,21520,11312,787,0|r,21520,13224,788,0|n),r=Wt(4),v[r>>2]=8,I(21652,5688,21542,13049,785,0|r,0,0,0,0),r=Wt(4),v[r>>2]=32,n=Wt(4),v[n>>2]=32,I(21652,5681,21542,13049,785,0|r,21542,13053,786,0|n),r=Wt(4),v[r>>2]=36,n=Wt(4),v[n>>2]=36,I(21652,6107,21542,13049,785,0|r,21542,13053,786,0|n),C(21654,21598,21655,0,11240,789,11243,0,11243,0,6428,11245,790),P(21654,2,13428,11312,791,792),r=Wt(8),v[r+4>>2]=0,v[r>>2]=793,I(21654,5773,21520,11312,794,0|r,0,0,0,0),r=Wt(4),v[r>>2]=16,n=Wt(4),v[n>>2]=16,I(21654,5013,21543,11312,795,0|r,21543,13224,796,0|n),r=Wt(4),v[r>>2]=20,n=Wt(4),v[n>>2]=20,I(21654,5022,21542,13049,797,0|r,21542,13053,798,0|n),r=Wt(4),v[r>>2]=24,n=Wt(4),v[n>>2]=24,I(21654,5033,21520,11312,799,0|r,21520,13224,800,0|n),r=Wt(4),v[r>>2]=36,n=Wt(4),v[n>>2]=36,I(21654,4765,21520,11312,799,0|r,21520,13224,800,0|n),r=Wt(4),v[r>>2]=48,n=Wt(4),v[n>>2]=48,I(21654,5681,21542,13049,797,0|r,21542,13053,798,0|n),r=Wt(4),v[r>>2]=52,n=Wt(4),v[n>>2]=52,I(21654,6107,21542,13049,797,0|r,21542,13053,798,0|n),C(2042,7288,7282,0,11240,801,11243,0,11243,0,2042,11245,802),r=Wt(8),v[r+4>>2]=0,v[r>>2]=803,I(2042,5773,21520,11312,804,0|r,0,0,0,0),C(1877,7147,7141,2042,11240,805,11240,806,11240,807,1877,11245,808),r=Wt(8),v[r+4>>2]=0,v[r>>2]=809,I(1877,6270,21543,11312,810,0|r,0,0,0,0),r=Wt(4),v[r>>2]=811,$(1877,2780,2,13436,11312,1609,0|r,0,0),r=Wt(4),v[r>>2]=812,$(1877,2910,2,13444,11312,1610,0|r,0,0),r=Wt(4),v[r>>2]=52,n=Wt(4),v[n>>2]=52,I(1877,4703,21537,11312,813,0|r,21537,13224,814,0|n),r=Wt(4),v[r>>2]=56,n=Wt(4),v[n>>2]=56,I(1877,1961,1877,11312,815,0|r,1877,13224,816,0|n),r=Wt(8),v[r+4>>2]=0,v[r>>2]=817,$(1877,2939,8,13456,13488,818,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=819,$(1877,3822,3,13500,13224,820,0|r,0,0),C(1855,7118,7112,1877,11240,821,11240,822,11240,823,1855,11245,824),P(1855,2,13512,11312,825,826),r=Wt(8),v[r+4>>2]=0,v[r>>2]=803,I(1855,5773,21520,11312,827,0|r,0,0,0,0),r=Wt(8),v[r+4>>2]=1,v[r>>2]=12,$(1855,1168,2,13520,11312,828,0|r,0,0),C(2017,7262,7256,1877,11240,829,11240,830,11240,831,2017,11245,832),P(2017,2,13528,11312,833,834),r=Wt(8),v[r+4>>2]=0,v[r>>2]=835,n=Wt(8),v[n+4>>2]=0,v[n>>2]=836,I(2017,1614,21647,11312,837,0|r,21647,13224,838,0|n),r=Wt(8),v[r+4>>2]=1,v[r>>2]=12,$(2017,1168,2,13536,11312,839,0|r,0,0),C(1999,7240,7234,1877,11240,840,11240,841,11240,842,1999,11245,843),P(1999,2,13544,11312,844,845),r=Wt(4),v[r>>2]=168,n=Wt(4),v[n>>2]=168,I(1999,4749,21520,11312,846,0|r,21520,13224,847,0|n),rt(3018,848),rt(3011,849),nt(2841,850),r=Wt(4),v[r>>2]=851,I(1999,3520,21639,11312,852,0|r,0,0,0,0),r=Wt(4),v[r>>2]=196,n=Wt(4),v[n>>2]=196,I(1999,4734,21542,13049,853,0|r,21542,13053,854,0|n),r=Wt(4),v[r>>2]=200,n=Wt(4),v[n>>2]=200,I(1999,2183,21542,13049,853,0|r,21542,13053,854,0|n);r=Wt(4),v[r>>2]=224,n=Wt(4),v[n>>2]=224,I(1999,4723,21543,11312,855,0|r,21543,13224,856,0|n),nt(2877,857),r=Wt(8),v[r+4>>2]=0,v[r>>2]=858,$(1999,3031,2,13568,13576,859,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=860,$(1999,4800,2,13580,11312,861,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=862,$(1999,4786,3,13588,13224,863,0|r,0,0),r=Wt(8),v[r+4>>2]=1,v[r>>2]=12,$(1999,1168,2,13600,11312,864,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=865,$(1999,4814,2,13580,11312,861,0|r,0,0),C(1981,7218,7212,1877,11240,866,11240,867,11240,868,1981,11245,869),P(1981,2,13608,11312,870,871),r=Wt(4),v[r>>2]=872,$(1981,2730,2,13616,11312,1613,0|r,0,0),r=Wt(4),v[r>>2]=80,n=Wt(4),v[n>>2]=80,I(1981,6279,21645,11312,873,0|r,21645,13224,874,0|n),r=Wt(4),v[r>>2]=81,n=Wt(4),v[n>>2]=81,I(1981,6318,21645,11312,873,0|r,21645,13224,874,0|n),r=Wt(8),v[r+4>>2]=1,v[r>>2]=12,$(1981,1168,2,13624,11312,875,0|r,0,0),C(1897,7171,7165,2042,11240,876,11240,877,11240,878,1897,11245,879),P(1897,2,13632,11312,880,881),r=Wt(4),v[r>>2]=20,n=Wt(4),v[n>>2]=20,I(1897,1391,21542,13049,882,0|r,21542,13053,883,0|n),r=Wt(4),v[r>>2]=24,n=Wt(4),v[n>>2]=24,I(1897,1217,21542,13049,882,0|r,21542,13053,883,0|n),r=Wt(4),v[r>>2]=28,n=Wt(4),v[n>>2]=28,I(1897,4056,21542,13049,882,0|r,21542,13053,883,0|n),r=Wt(4),v[r>>2]=884,$(1897,3957,5,13648,13668,1614,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=885,$(1897,4122,3,13676,11388,886,0|r,0,0),r=Wt(8),v[r+4>>2]=1,v[r>>2]=12,$(1897,1168,2,13688,11312,887,0|r,0,0),C(1944,7194,7188,2042,11240,888,11240,889,11240,890,1944,11245,891),P(1944,2,13696,11312,892,893),r=Wt(4),v[r>>2]=32,n=Wt(4),v[n>>2]=32,I(1944,1391,21542,13049,894,0|r,21542,13053,895,0|n),r=Wt(4),v[r>>2]=36,n=Wt(4),v[n>>2]=36,I(1944,1217,21542,13049,894,0|r,21542,13053,895,0|n),r=Wt(4),v[r>>2]=44,n=Wt(4),v[n>>2]=44,I(1944,6726,21542,13049,894,0|r,21542,13053,895,0|n),r=Wt(4),v[r>>2]=48,n=Wt(4),v[n>>2]=48,I(1944,6633,21542,13049,894,0|r,21542,13053,895,0|n),r=Wt(4),v[r>>2]=40,n=Wt(4),v[n>>2]=40,I(1944,4056,21542,13049,894,0|r,21542,13053,895,0|n),r=Wt(4),v[r>>2]=52,n=Wt(4),v[n>>2]=52,I(1944,4734,21542,13049,894,0|r,21542,13053,895,0|n),r=Wt(4),v[r>>2]=56,n=Wt(4),v[n>>2]=56,I(1944,2183,21542,13049,894,0|r,21542,13053,895,0|n),r=Wt(4),v[r>>2]=896,I(1944,3520,21639,11312,897,0|r,0,0,0,0),r=Wt(4),v[r>>2]=116,n=Wt(4),v[n>>2]=116,I(1944,4749,21520,11312,898,0|r,21520,13224,899,0|n),et(2215,900),r=Wt(8),v[r+4>>2]=0,v[r>>2]=901,$(1944,3004,7,13712,13740,902,0|r,0,0),et(3011,903),r=Wt(8),v[r+4>>2]=0,v[r>>2]=904,$(1944,2233,2,13752,13576,905,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=906,$(1944,2939,6,13760,13784,907,0|r,0,0),r=Wt(8),v[r+4>>2]=1,v[r>>2]=12,$(1944,1168,2,13792,11312,908,0|r,0,0),C(3667,7406,7400,0,11240,909,11243,0,11243,0,3667,11245,910),r=Wt(8),v[r+4>>2]=1,v[r>>2]=32,$(3667,2014,4,13808,13824,911,0|r,1,0),r=Wt(8),v[r+4>>2]=1,v[r>>2]=28,$(3667,1894,4,13840,13824,912,0|r,1,0),r=Wt(8),v[r+4>>2]=1,v[r>>2]=24,$(3667,1978,4,13856,13824,913,0|r,1,0),r=Wt(8),v[r+4>>2]=1,v[r>>2]=20,$(3667,1852,4,13872,13824,914,0|r,1,0),r=Wt(8),v[r+4>>2]=1,v[r>>2]=16,$(3667,1996,5,13888,13908,915,0|r,1,0),r=Wt(8),v[r+4>>2]=1,v[r>>2]=12,$(3667,1941,5,13920,13908,916,0|r,1,0),C(3662,7377,7371,3667,11240,917,11240,918,11240,919,3662,11245,920),P(3662,2,13940,11312,921,922),r=Wt(8),v[r+4>>2]=1,v[r>>2]=12,$(3662,1941,5,13952,13908,923,0|r,0,0),r=Wt(8),v[r+4>>2]=1,v[r>>2]=16,$(3662,1996,5,13984,13908,924,0|r,0,0),r=Wt(8),v[r+4>>2]=1,v[r>>2]=20,$(3662,1852,4,14016,13824,925,0|r,0,0),r=Wt(8),v[r+4>>2]=1,v[r>>2]=24,$(3662,1978,4,14032,13824,926,0|r,0,0),r=Wt(8),v[r+4>>2]=1,v[r>>2]=28,$(3662,1894,4,14048,13824,927,0|r,0,0),r=Wt(8),v[r+4>>2]=1,v[r>>2]=32,$(3662,2014,4,14064,13824,928,0|r,0,0),C(21659,21660,21661,0,11240,929,11243,0,11243,0,5905,11245,930),P(21659,2,14080,11312,931,932),r=Wt(4),v[r>>2]=933,$(21659,5812,2,14088,11312,1616,0|r,0,0),r=Wt(4),v[r>>2]=44,n=Wt(4),v[n>>2]=44,I(21659,3554,21530,11312,934,0|r,21530,13224,935,0|n),r=Wt(4),v[r>>2]=48,n=Wt(4),v[n>>2]=48,I(21659,3564,21530,11312,934,0|r,21530,13224,935,0|n),r=Wt(4),v[r>>2]=52,n=Wt(4),v[n>>2]=52,I(21659,3784,21531,11312,936,0|r,21531,13224,937,0|n),r=Wt(4),v[r>>2]=56,n=Wt(4),v[n>>2]=56,I(21659,3778,21531,11312,936,0|r,21531,13224,937,0|n),r=Wt(4),v[r>>2]=60,n=Wt(4),v[n>>2]=60,I(21659,4734,21543,11312,938,0|r,21543,13224,939,0|n),r=Wt(4),v[r>>2]=64,n=Wt(4),v[n>>2]=64,I(21659,2183,21543,11312,938,0|r,21543,13224,939,0|n),C(21662,21663,21664,0,11240,940,11243,0,11243,0,4351,11245,941),r=Wt(4),v[r>>2]=942,$(21662,5812,2,14096,11312,1617,0|r,0,0),r=Wt(4),v[r>>2]=20,n=Wt(4),v[n>>2]=20,I(21662,1391,21543,11312,943,0|r,21543,13224,944,0|n),r=Wt(4),v[r>>2]=24,n=Wt(4),v[n>>2]=24,I(21662,1217,21543,11312,943,0|r,21543,13224,944,0|n),r=Wt(4),v[r>>2]=68,n=Wt(4),v[n>>2]=68,I(21662,1301,21543,11312,943,0|r,21543,13224,944,0|n),r=Wt(4),v[r>>2]=72,n=Wt(4),v[n>>2]=72,I(21662,5081,21645,11312,945,0|r,21645,13224,946,0|n),r=Wt(4),v[r>>2]=76,n=Wt(4),v[n>>2]=76,I(21662,2893,21543,11312,943,0|r,21543,13224,944,0|n),C(21665,21666,21667,0,11240,947,11243,0,11243,0,3684,11245,948),C(21668,21658,21669,0,11240,949,11243,0,11243,0,2991,11245,950),P(21668,4,14112,13824,951,952),r=Wt(8),v[r+4>>2]=0,v[r>>2]=953,$(21668,4370,3,14128,11328,954,0|r,0,0),C(4282,7430,7424,4305,11240,955,11240,956,11240,957,1393,11245,958),P(4282,2,14140,11312,959,960),C(4299,7454,7448,4305,11240,961,11240,962,11240,963,1402,11245,964),P(4299,2,14148,11312,965,966),C(21647,21577,21670,0,11240,967,11243,0,11243,0,6359,11245,968),P(21647,4,14160,13824,969,970),r=Wt(8),v[r+4>>2]=0,v[r>>2]=971,I(21647,1301,21543,11312,972,0|r,0,0,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=973,I(21647,5773,21520,11312,974,0|r,0,0,0,0),r=Wt(4),v[r>>2]=975,I(21647,6508,21646,11312,976,0|r,0,0,0,0),r=Wt(4),v[r>>2]=977,I(21647,3520,21639,11312,978,0|r,0,0,0,0),r=Wt(4),v[r>>2]=979,I(21647,3544,21639,11312,978,0|r,0,0,0,0),r=Wt(4),v[r>>2]=80,n=Wt(4),v[n>>2]=80,I(21647,6063,21525,11312,980,0|r,21525,13224,981,0|n),C(5833,7913,7907,0,11240,982,11243,0,11243,0,5833,11245,983),r=Wt(8),v[r+4>>2]=1,v[r>>2]=12,$(5833,5116,2,14176,13576,984,0|r,1,0),r=Wt(8),v[r+4>>2]=1,v[r>>2]=16,$(5833,4990,2,14184,11312,985,0|r,1,0),r=Wt(8),v[r+4>>2]=1,v[r>>2]=16,n=Wt(8),v[n+4>>2]=1,v[n>>2]=20,I(5833,4983,21645,11312,986,0|r,21645,13224,987,0|n),C(1728,7076,7070,5833,11240,988,11240,989,11240,990,1728,11245,991),P(1728,3,14192,11328,992,993),r=Wt(4),v[r>>2]=994,I(1728,6354,6392,11312,995,0|r,0,0,0,0),r=Wt(4),v[r>>2]=996,$(1728,2780,2,14204,11312,1618,0|r,0,0),r=Wt(4),v[r>>2]=40,n=Wt(4),v[n>>2]=40,I(1728,2246,5364,11312,997,0|r,5364,13224,998,0|n),r=Wt(4),v[r>>2]=24,n=Wt(4),v[n>>2]=24,I(1728,3998,21543,11312,999,0|r,21543,13224,1e3,0|n),r=Wt(4),v[r>>2]=28,n=Wt(4),v[n>>2]=28,I(1728,2581,21645,11312,1001,0|r,21645,13224,1002,0|n),r=Wt(4),v[r>>2]=29,n=Wt(4),v[n>>2]=29,I(1728,4833,21645,11312,1001,0|r,21645,13224,1002,0|n),r=Wt(4),v[r>>2]=32,n=Wt(4),v[n>>2]=32,I(1728,1231,21542,13049,1003,0|r,21542,13053,1004,0|n),r=Wt(4),v[r>>2]=36,n=Wt(4),v[n>>2]=36,I(1728,2590,21542,13049,1003,0|r,21542,13053,1004,0|n),R(1728,6964,8,14224,14256,1005,1006,0),R(1728,6946,9,14272,14308,1007,1008,0),C(1745,7096,7090,5833,11240,1009,11240,1010,11240,1011,1745,11245,1012),P(1745,3,14320,11328,1013,1014),r=Wt(4),v[r>>2]=1015,I(1745,6354,6409,11312,1016,0|r,0,0,0,0),r=Wt(4),v[r>>2]=1017,$(1745,2780,2,14332,11312,1619,0|r,0,0),r=Wt(4),v[r>>2]=24,n=Wt(4),v[n>>2]=24,I(1745,2246,21656,11312,1018,0|r,21656,13224,1019,0|n),r=Wt(4),v[r>>2]=28,n=Wt(4),v[n>>2]=28,I(1745,3948,21542,13049,1020,0|r,21542,13053,1021,0|n),r=Wt(4),v[r>>2]=32,n=Wt(4),v[n>>2]=32,I(1745,4921,21542,13049,1020,0|r,21542,13053,1021,0|n),r=Wt(4),v[r>>2]=36,n=Wt(4),v[n>>2]=36,I(1745,1269,21542,13049,1020,0|r,21542,13053,1021,0|n),r=Wt(4),v[r>>2]=40,n=Wt(4),v[n>>2]=40,I(1745,1279,21542,13049,1020,0|r,21542,13053,1021,0|n),C(6368,7930,7924,6413,11240,1022,11240,1023,11240,1024,6368,11245,1025),P(6368,2,14340,11312,1026,1027),r=Wt(4),v[r>>2]=1028,$(6368,2780,2,14348,11312,1620,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1029,I(6368,2246,21646,11312,1030,0|r,0,0,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1031,I(6368,1269,21542,13049,1032,0|r,0,0,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1033,I(6368,1279,21542,13049,1032,0|r,0,0,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1034,I(6368,1292,21542,13049,1032,0|r,0,0,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1035,I(6368,1260,21542,13049,1032,0|r,0,0,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1036,I(6368,4065,21542,13049,1032,0|r,0,0,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1037,I(6368,6675,21542,13049,1032,0|r,0,0,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1038,I(6368,6569,21542,13049,1032,0|r,0,0,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1039,I(6368,6733,21542,13049,1032,0|r,0,0,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1040,I(6368,6640,21542,13049,1032,0|r,0,0,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1041,I(6368,6601,21542,13049,1032,0|r,0,0,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1042,I(6368,4999,21645,11312,1043,0|r,0,0,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1044,I(6368,4588,21645,11312,1043,0|r,0,0,0,0),C(1704,7049,7043,5833,11240,1045,11240,1046,11240,1047,1704,11245,1048),P(1704,3,14356,11328,1049,1050),r=Wt(4),v[r>>2]=1051,I(1704,6354,6368,11312,1052,0|r,0,0,0,0),r=Wt(4),v[r>>2]=1053,$(1704,2780,2,14368,11312,1621,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1054,I(1704,2246,5364,11312,1055,0|r,0,0,0,0),r=Wt(4),v[r>>2]=28,n=Wt(4),v[n>>2]=28,I(1704,1269,21542,13049,1056,0|r,21542,13053,1057,0|n),r=Wt(4),v[r>>2]=32,n=Wt(4),v[n>>2]=32,I(1704,1279,21542,13049,1056,0|r,21542,13053,1057,0|n),r=Wt(4),v[r>>2]=36,n=Wt(4),v[n>>2]=36,I(1704,1292,21542,13049,1056,0|r,21542,13053,1057,0|n),r=Wt(4),v[r>>2]=40,n=Wt(4),v[n>>2]=40,I(1704,1260,21542,13049,1056,0|r,21542,13053,1057,0|n),C(5364,7502,7496,5833,11240,1058,11240,1059,11240,1060,5364,11245,1061),P(5364,4,14384,13824,1062,1063),r=Wt(4),v[r>>2]=1064,I(5364,6354,21646,11312,1065,0|r,0,0,0,0),r=Wt(4),v[r>>2]=1066,I(5364,3838,21651,11312,1067,0|r,0,0,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1068,I(5364,1834,5364,11312,1069,0|r,0,0,0,0),r=Wt(4),v[r>>2]=1070,$(5364,4444,2,14400,11312,1622,0|r,0,0),r=Wt(4),v[r>>2]=32,n=Wt(4),v[n>>2]=32,I(5364,1391,21542,13049,1071,0|r,21542,13053,1072,0|n),r=Wt(4),v[r>>2]=36,n=Wt(4),v[n>>2]=36,I(5364,1217,21542,13049,1071,0|r,21542,13053,1072,0|n),r=Wt(4),v[r>>2]=40,n=Wt(4),v[n>>2]=40,I(5364,4056,21542,13049,1071,0|r,21542,13053,1072,0|n),r=Wt(4),v[r>>2]=44,n=Wt(4),v[n>>2]=44,I(5364,6726,21542,13049,1071,0|r,21542,13053,1072,0|n),r=Wt(4),v[r>>2]=48,n=Wt(4),v[n>>2]=48,I(5364,6633,21542,13049,1071,0|r,21542,13053,1072,0|n),r=Wt(4),v[r>>2]=52,n=Wt(4),v[n>>2]=52,I(5364,6700,21542,13049,1071,0|r,21542,13053,1072,0|n),r=Wt(4),v[r>>2]=56,n=Wt(4),v[n>>2]=56,I(5364,6594,21542,13049,1071,0|r,21542,13053,1072,0|n),r=Wt(4),v[r>>2]=60,n=Wt(4),v[n>>2]=60,I(5364,1390,21542,13049,1071,0|r,21542,13053,1072,0|n),r=Wt(4),v[r>>2]=64,n=Wt(4),v[n>>2]=64,I(5364,1216,21542,13049,1071,0|r,21542,13053,1072,0|n),r=Wt(4),v[r>>2]=68,n=Wt(4),v[n>>2]=68,I(5364,4055,21542,13049,1071,0|r,21542,13053,1072,0|n),r=Wt(4),v[r>>2]=72,n=Wt(4),v[n>>2]=72,I(5364,6725,21542,13049,1071,0|r,21542,13053,1072,0|n),r=Wt(4),v[r>>2]=76,n=Wt(4),v[n>>2]=76,I(5364,6632,21542,13049,1071,0|r,21542,13053,1072,0|n),r=Wt(4),v[r>>2]=80,n=Wt(4),v[n>>2]=80,I(5364,6699,21542,13049,1071,0|r,21542,13053,1072,0|n),r=Wt(4),v[r>>2]=84,n=Wt(4),v[n>>2]=84,I(5364,6593,21542,13049,1071,0|r,21542,13053,1072,0|n),r=Wt(4),v[r>>2]=88,n=Wt(4),v[n>>2]=88,I(5364,6260,21645,11312,1073,0|r,21645,13224,1074,0|n),r=Wt(4),v[r>>2]=92,n=Wt(4),v[n>>2]=92,I(5364,6567,21542,13049,1071,0|r,21542,13053,1072,0|n),r=Wt(4),v[r>>2]=96,n=Wt(4),v[n>>2]=96,I(5364,6352,21542,13049,1071,0|r,21542,13053,1072,0|n),r=Wt(4),v[r>>2]=104,n=Wt(4),v[n>>2]=104,I(5364,6350,21542,13049,1071,0|r,21542,13053,1072,0|n),r=Wt(4),v[r>>2]=108,n=Wt(4),v[n>>2]=108,I(5364,6348,21542,13049,1071,0|r,21542,13053,1072,0|n),r=Wt(4),v[r>>2]=100,n=Wt(4),v[n>>2]=100,I(5364,6761,21542,13049,1071,0|r,21542,13053,1072,0|n),r=Wt(4),v[r>>2]=112,n=Wt(4),v[n>>2]=112,I(5364,6668,21542,13049,1071,0|r,21542,13053,1072,0|n),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1075,$(5364,4473,2,14408,13576,1076,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1077,$(5364,4671,9,14416,14452,1078,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1079,$(5364,5130,2,14408,13576,1076,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1080,$(5364,6707,2,14464,13049,1081,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1082,$(5364,6614,2,14464,13049,1081,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1083,$(5364,6746,2,14464,13049,1081,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1084,$(5364,6653,2,14464,13049,1081,0|r,0,0),ft(4594,1085),ft(6176,1086),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1087,$(5364,4080,3,14484,13204,1088,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1089,$(5364,4101,3,14484,13204,1088,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1090,$(5364,6189,3,14496,13053,1091,0|r,0,0),C(21646,21573,21672,0,11240,1092,11243,0,11243,0,6517,11245,1093),P(21646,4,14512,13824,1094,1095),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1096,I(21646,1301,21543,11312,1097,0|r,0,0,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1098,I(21646,5773,21520,11312,1099,0|r,0,0,0,0),r=Wt(4),v[r>>2]=20,n=Wt(4),v[n>>2]=20,I(21646,1834,21646,11312,1100,0|r,21646,13224,1101,0|n),r=Wt(4),v[r>>2]=24,n=Wt(4),v[n>>2]=24,I(21646,4696,21542,13049,1102,0|r,21542,13053,1103,0|n),r=Wt(4),v[r>>2]=28,n=Wt(4),v[n>>2]=28,I(21646,1391,21542,13049,1102,0|r,21542,13053,1103,0|n),r=Wt(4),v[r>>2]=32,n=Wt(4),v[n>>2]=32,I(21646,1217,21542,13049,1102,0|r,21542,13053,1103,0|n),r=Wt(4),v[r>>2]=36,n=Wt(4),v[n>>2]=36,I(21646,4056,21542,13049,1102,0|r,21542,13053,1103,0|n),r=Wt(4),v[r>>2]=40,n=Wt(4),v[n>>2]=40,I(21646,6726,21542,13049,1102,0|r,21542,13053,1103,0|n),r=Wt(4),v[r>>2]=44,n=Wt(4),v[n>>2]=44,I(21646,6633,21542,13049,1102,0|r,21542,13053,1103,0|n),r=Wt(4),v[r>>2]=48,n=Wt(4),v[n>>2]=48,I(21646,6700,21542,13049,1102,0|r,21542,13053,1103,0|n),r=Wt(4),v[r>>2]=52,n=Wt(4),v[n>>2]=52,I(21646,6594,21542,13049,1102,0|r,21542,13053,1103,0|n),r=Wt(4),v[r>>2]=56,n=Wt(4),v[n>>2]=56,I(21646,5955,21526,11312,1104,0|r,21526,13224,1105,0|n),r=Wt(4),v[r>>2]=60,n=Wt(4),v[n>>2]=60,I(21646,6286,21645,11312,1106,0|r,21645,13224,1107,0|n),C(21656,21590,21673,0,11240,1108,11243,0,11243,0,1617,11245,1109),P(21656,3,14528,11328,1110,1111),r=Wt(4),v[r>>2]=1112,I(21656,6354,21647,11312,1113,0|r,0,0,0,0),r=Wt(4),v[r>>2]=1114,I(21656,5343,5364,11312,1115,0|r,0,0,0,0),r=Wt(4),v[r>>2]=1116,I(21656,3520,21639,11312,1117,0|r,0,0,0,0),r=Wt(4),v[r>>2]=1118,I(21656,3544,21639,11312,1117,0|r,0,0,0,0),r=Wt(4),v[r>>2]=1119,$(21656,4509,2,14540,11312,1624,0|r,0,0),r=Wt(4),v[r>>2]=1120,$(21656,3860,2,14548,11312,1625,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1121,$(21656,1927,2,14556,11312,1122,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1123,$(21656,1913,3,14564,13224,1124,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1125,$(21656,5701,3,14576,13053,1126,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1127,$(21656,5719,2,14588,13049,1128,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1129,$(21656,5130,2,14596,13576,1130,0|r,0,0),C(21657,21594,21674,0,11240,1131,11243,0,11243,0,4427,11245,1132),P(21657,2,14604,11312,1133,1134),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1135,I(21657,5773,21520,11312,1136,0|r,0,0,0,0),r=Wt(4),v[r>>2]=1137,$(21657,2780,2,14612,11312,1626,0|r,0,0),r=Wt(4),v[r>>2]=1138,$(21657,2454,2,14620,11312,1627,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1139,$(21657,1913,5,14640,14660,1140,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1141,$(21657,4424,3,14668,13224,1142,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1143,$(21657,4386,3,14668,13224,1142,0|r,0,0),r=Wt(4),v[r>>2]=1144,$(21657,1572,3,14680,11328,1628,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1145,$(21657,1927,4,14704,13824,1146,0|r,0,0),r=Wt(4),v[r>>2]=1147,$(21657,2558,2,14720,11312,1629,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1148,$(21657,2036,4,14736,11296,1149,0|r,0,0),r=Wt(4),v[r>>2]=1150,$(21657,1550,3,14752,11328,1630,0|r,0,0),C(21675,21632,21676,0,11240,1151,11243,0,11243,0,1069,11245,1152),P(21675,4,14768,13824,1153,1154),r=Wt(4),v[r>>2]=0,n=Wt(4),v[n>>2]=0,I(21675,1307,21537,11312,1155,0|r,21537,13224,1156,0|n),r=Wt(4),v[r>>2]=1157,$(21675,5812,2,14784,11312,1631,0|r,0,0),r=Wt(4),v[r>>2]=1158,$(21675,1927,2,14792,11312,1632,0|r,0,0),C(21678,21679,21680,0,11240,1159,11243,0,11243,0,4904,11245,1160),P(21678,1,14800,11240,1161,1162),r=Wt(4),v[r>>2]=1163,I(21678,2960,21539,11312,1164,0|r,0,0,0,0),r=Wt(4),v[r>>2]=1165,I(21678,2854,21560,11312,1166,0|r,0,0,0,0),r=Wt(4),v[r>>2]=1167,I(21678,3041,21539,11312,1164,0|r,0,0,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1168,$(21678,1520,4,14816,13824,1169,0|r,0,0);r=Wt(8),v[r+4>>2]=0,v[r>>2]=1170,$(21678,1589,3,14832,13224,1171,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1172,$(21678,6146,2,14844,13576,1173,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1174,$(21678,4893,2,14852,11312,1175,0|r,0,0),C(21681,21682,21683,0,11240,1176,11243,0,11243,0,6476,11245,1177),P(21681,1,14860,11240,1178,1179),r=Wt(4),v[r>>2]=4,n=Wt(4),v[n>>2]=4,I(21681,5773,21520,11312,1180,0|r,21520,13224,1181,0|n),r=Wt(4),v[r>>2]=1182,$(21681,2780,2,14864,11312,1633,0|r,0,0),r=Wt(4),v[r>>2]=1183,$(21681,2445,2,14872,11312,1634,0|r,0,0),r=Wt(4),v[r>>2]=1184,$(21681,2691,2,14880,11312,1635,0|r,0,0),r=Wt(4),v[r>>2]=64,n=Wt(4),v[n>>2]=64,I(21681,4395,21657,11312,1185,0|r,21657,13224,1186,0|n),r=Wt(4),v[r>>2]=1187,$(21681,2536,2,14888,11312,1636,0|r,0,0),r=Wt(4),v[r>>2]=1188,$(21681,2671,2,14896,11312,1637,0|r,0,0),r=Wt(4),v[r>>2]=1189,$(21681,2493,2,14904,11312,1638,0|r,0,0),r=Wt(4),v[r>>2]=1190,$(21681,2469,2,14912,11312,1639,0|r,0,0),r=Wt(4),v[r>>2]=1191,$(21681,2510,2,14920,11312,1640,0|r,0,0),r=Wt(4),v[r>>2]=148,n=Wt(4),v[n>>2]=148,I(21681,1391,21542,13049,1192,0|r,21542,13053,1193,0|n),r=Wt(4),v[r>>2]=152,n=Wt(4),v[n>>2]=152,I(21681,1217,21542,13049,1192,0|r,21542,13053,1193,0|n),r=Wt(4),v[r>>2]=156,n=Wt(4),v[n>>2]=156,I(21681,4734,21542,13049,1192,0|r,21542,13053,1193,0|n),r=Wt(4),v[r>>2]=160,n=Wt(4),v[n>>2]=160,I(21681,2183,21542,13049,1192,0|r,21542,13053,1193,0|n),r=Wt(4),v[r>>2]=164,n=Wt(4),v[n>>2]=164,I(21681,4319,21520,11312,1180,0|r,21520,13224,1181,0|n),r=Wt(4),v[r>>2]=176,n=Wt(4),v[n>>2]=176,I(21681,4828,21520,11312,1180,0|r,21520,13224,1181,0|n),r=Wt(4),v[r>>2]=204,n=Wt(4),v[n>>2]=204,I(21681,2613,21542,13049,1192,0|r,21542,13053,1193,0|n),r=Wt(4),v[r>>2]=208,n=Wt(4),v[n>>2]=208,I(21681,4754,21520,11312,1180,0|r,21520,13224,1181,0|n),r=Wt(4),v[r>>2]=220,n=Wt(4),v[n>>2]=220,I(21681,4765,21520,11312,1180,0|r,21520,13224,1181,0|n),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1194,$(21681,5360,3,14928,11328,1195,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1196,$(21681,1376,3,14940,11328,1197,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1198,$(21681,1605,3,14952,11328,1199,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1200,$(21681,1317,3,14940,11328,1197,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1201,$(21681,4415,3,14964,11328,1202,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1203,$(21681,1813,3,14976,11328,1204,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1205,$(21681,4239,3,14988,11328,1206,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1207,$(21681,1724,3,15e3,11328,1208,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1209,$(21681,1700,3,15012,11328,1210,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1211,$(21681,1741,3,15024,11328,1212,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1213,$(21681,1331,3,14940,11328,1197,0|r,0,0),C(21684,21606,21685,0,11240,1214,11243,0,11243,0,4256,11245,1215),P(21684,4,15040,15056,1216,1217),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1218,I(21684,5773,21520,11312,1219,0|r,0,0,0,0),r=Wt(4),v[r>>2]=1220,$(21684,2789,2,15068,11312,1641,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1221,$(21684,5483,3,15076,11328,1222,0|r,0,0),r=Wt(4),v[r>>2]=32,n=Wt(4),v[n>>2]=32,I(21684,4143,21542,13049,1223,0|r,21542,13053,1224,0|n),C(5672,7897,7891,0,11240,1225,11243,0,11243,0,5672,11245,1226),r=Wt(8),v[r+4>>2]=1,v[r>>2]=16,$(5672,6336,2,15088,11312,1227,0|r,1,0),C(5620,7808,7802,5672,11240,1228,11240,1229,11240,1230,5620,11245,1231),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1232,$(5620,1648,2,15096,11312,1233,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1234,$(5620,3732,3,15104,13224,1235,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1236,$(5620,6307,3,15104,13224,1235,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1237,$(5620,4952,7,15120,15148,1238,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1239,$(5620,2109,4,15168,15184,1240,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1241,$(5620,5270,3,15192,11388,1242,0|r,0,0),C(5649,7851,7845,5620,11240,1243,11240,1244,11240,1245,5649,11245,1246),P(5649,2,15204,11312,1247,1248),S(5649,6768,21543,11020,11240,1249,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1250,$(5649,5764,6,15216,15240,1251,0|r,0,0),C(5667,7876,7870,5649,11240,1252,11240,1253,11240,1254,5667,11245,1255),P(5667,2,15248,11312,1256,1257),C(5530,7727,7721,5649,11240,1258,11240,1259,11240,1260,5530,11245,1261),P(5530,2,15256,11312,1262,1263),C(5634,7829,7823,5620,11240,1264,11240,1265,11240,1266,5634,11245,1267),P(5634,2,15264,11312,1268,1269),r=Wt(4),v[r>>2]=20,n=Wt(4),v[n>>2]=20,I(5634,1366,21543,11312,1270,0|r,21543,13224,1271,0|n),r=Wt(4),v[r>>2]=1272,$(5634,2802,2,15272,11312,1642,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1273,$(5634,5764,5,15280,13668,1274,0|r,0,0),C(5498,7681,7675,5620,11240,1275,11240,1276,11240,1277,5498,11245,1278),P(5498,2,15300,11312,1279,1280),S(5498,6768,21543,9332,11240,1249,0,0),r=Wt(4),v[r>>2]=20,n=Wt(4),v[n>>2]=20,I(5498,1307,21543,11312,1281,0|r,21543,13224,1282,0|n),r=Wt(4),v[r>>2]=1283,$(5498,2802,2,15308,11312,1643,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1284,$(5498,5764,8,15328,15360,1285,0|r,0,0),C(5495,7657,7651,5620,11240,1286,11240,1287,11240,1288,5495,11245,1289),P(5495,2,15372,11312,1290,1291),S(5495,6768,21543,9332,11240,1249,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1292,n=Wt(8),v[n+4>>2]=0,v[n>>2]=1293,I(5495,1307,21543,11312,1294,0|r,21543,13224,1295,0|n),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1296,$(5495,5764,11,15392,15436,1297,0|r,0,0),C(5464,7631,7625,5672,11240,1298,11240,1299,11240,1300,5464,11245,1301),P(5464,2,15452,11312,1302,1303),r=Wt(4),v[r>>2]=4,n=Wt(4),v[n>>2]=4,I(5464,1307,21537,11312,1304,0|r,21537,13224,1305,0|n),r=Wt(4),v[r>>2]=1306,$(5464,2802,2,15460,11312,1644,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1307,$(5464,2812,2,15468,11312,1308,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1309,$(5464,1648,2,15476,11312,1310,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1311,$(5464,5764,5,15488,15508,1312,0|r,0,0),C(5575,7786,7780,5620,11240,1313,11240,1314,11240,1315,5575,11245,1316),P(5575,2,15516,11312,1317,1318),r=Wt(4),v[r>>2]=20,n=Wt(4),v[n>>2]=20,I(5575,1307,21543,11312,1319,0|r,21543,13224,1320,0|n),r=Wt(4),v[r>>2]=56,n=Wt(4),v[n>>2]=56,I(5575,1841,1877,11312,1321,0|r,1877,13224,1322,0|n),r=Wt(4),v[r>>2]=1323,$(5575,2802,2,15524,11312,1645,0|r,0,0),r=Wt(4),v[r>>2]=1324,$(5575,2922,2,15532,11312,1646,0|r,0,0),r=Wt(4),v[r>>2]=1325,$(5575,5764,5,15552,15508,1647,0|r,0,0),C(5450,7610,7604,5672,11240,1326,11240,1327,11240,1328,5450,11245,1329),P(5450,2,15572,11312,1330,1331),r=Wt(4),v[r>>2]=1332,$(5450,2802,2,15580,11312,1648,0|r,0,0),r=Wt(4),v[r>>2]=1333,$(5450,2536,2,15588,11312,1649,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1334,$(5450,1648,2,15596,11312,1335,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1336,$(5450,5764,4,15616,11296,1337,0|r,0,0),C(5512,7702,7696,5672,11240,1338,11240,1339,11240,1340,5512,11245,1341),P(5512,2,15632,11312,1342,1343),r=Wt(4),v[r>>2]=1344,$(5512,2802,2,15640,11312,1650,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1345,$(5512,1648,2,15648,11312,1346,0|r,0,0),r=Wt(4),v[r>>2]=1347,$(5512,2599,2,15656,11312,1651,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1348,$(5512,5764,5,15664,15508,1349,0|r,0,0),C(5429,7582,7576,5620,11240,1350,11240,1351,11240,1352,5429,11245,1353),P(5429,2,15684,11312,1354,1355),S(5429,6768,21543,9684,11240,1249,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1356,$(5429,5764,9,15696,15732,1357,0|r,0,0),C(5401,7547,7541,5620,11240,1358,11240,1359,11240,1360,5401,11245,1361),P(5401,2,15744,11312,1362,1363),S(5401,6768,21543,10988,11240,1249,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1364,$(5401,5764,8,15760,15360,1365,0|r,0,0),C(5544,7748,7742,5620,11240,1366,11240,1367,11240,1368,5544,11245,1369),P(5544,2,15792,11312,1370,1371),S(5544,6768,21543,10988,11240,1249,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1372,$(5544,5764,5,15808,13668,1373,0|r,0,0),C(5375,7514,7508,5620,11240,1374,11240,1375,11240,1376,5375,11245,1377),P(5375,2,15828,11312,1378,1379),S(5375,6768,21543,9940,11240,1249,0,0),C(21687,21625,21688,0,11240,1380,11243,0,11243,0,1089,11245,1381),P(21687,1,15836,11240,1382,1383),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1384,I(21687,4164,21684,11312,1385,0|r,0,0,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1386,I(21687,1397,21687,11312,1387,0|r,0,0,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1388,I(21687,4519,21687,11312,1387,0|r,0,0,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1389,I(21687,3829,21687,11312,1387,0|r,0,0,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1390,I(21687,1355,21543,11312,1391,0|r,0,0,0,0),r=Wt(4),v[r>>2]=36,n=Wt(4),v[n>>2]=36,I(21687,3767,21645,11312,1392,0|r,21645,13224,1393,0|n),r=Wt(4),v[r>>2]=37,n=Wt(4),v[n>>2]=37,I(21687,2419,21645,11312,1392,0|r,21645,13224,1393,0|n),r=Wt(4),v[r>>2]=40,n=Wt(4),v[n>>2]=40,I(21687,6201,21542,13049,1394,0|r,21542,13053,1395,0|n),r=Wt(4),v[r>>2]=44,n=Wt(4),v[n>>2]=44,I(21687,6216,21542,13049,1394,0|r,21542,13053,1395,0|n),r=Wt(4),v[r>>2]=48,n=Wt(4),v[n>>2]=48,I(21687,6236,21542,13049,1394,0|r,21542,13053,1395,0|n),r=Wt(4),v[r>>2]=52,n=Wt(4),v[n>>2]=52,I(21687,1530,21542,13049,1394,0|r,21542,13053,1395,0|n),r=Wt(4),v[r>>2]=56,n=Wt(4),v[n>>2]=56,I(21687,6154,21542,13049,1394,0|r,21542,13053,1395,0|n),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1396,n=Wt(8),v[n+4>>2]=0,v[n>>2]=1397,I(21687,1469,21542,13049,1398,0|r,21542,13053,1399,0|n),r=Wt(4),v[r>>2]=68,n=Wt(4),v[n>>2]=68,I(21687,1213,21542,13049,1394,0|r,21542,13053,1395,0|n),r=Wt(4),v[r>>2]=72,n=Wt(4),v[n>>2]=72,I(21687,5754,21542,13049,1394,0|r,21542,13053,1395,0|n),r=Wt(4),v[r>>2]=84,n=Wt(4),v[n>>2]=84,I(21687,6167,21542,13049,1394,0|r,21542,13053,1395,0|n),r=Wt(4),v[r>>2]=88,n=Wt(4),v[n>>2]=88,I(21687,5875,21542,13049,1394,0|r,21542,13053,1395,0|n),r=Wt(4),v[r>>2]=92,n=Wt(4),v[n>>2]=92,I(21687,6526,21542,13049,1394,0|r,21542,13053,1395,0|n),r=Wt(4),v[r>>2]=96,n=Wt(4),v[n>>2]=96,I(21687,5693,21542,13049,1394,0|r,21542,13053,1395,0|n),r=Wt(4),v[r>>2]=100,n=Wt(4),v[n>>2]=100,I(21687,4152,21542,13049,1394,0|r,21542,13053,1395,0|n),r=Wt(4),v[r>>2]=112,n=Wt(4),v[n>>2]=112,I(21687,6121,21523,11312,1400,0|r,21523,13224,1401,0|n),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1402,$(21687,5737,2,15840,13049,1403,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1404,$(21687,5070,2,15848,11312,1405,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1406,$(21687,2617,2,15856,13576,1407,0|r,0,0),C(21689,21690,21691,0,11240,1408,11243,0,11243,0,6489,11245,1409),P(21689,2,15864,11312,1410,1411),r=Wt(4),v[r>>2]=8,n=Wt(4),v[n>>2]=8,I(21689,1235,21542,13049,1412,0|r,21542,13053,1413,0|n),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1414,I(21689,6463,21681,11312,1415,0|r,0,0,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1416,$(21689,1246,5,15872,15892,1417,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1418,$(21689,4626,5,15904,15892,1419,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1420,$(21689,1253,4,15936,15952,1421,0|r,0,0),C(21692,21693,21694,0,11240,1422,11243,0,11243,0,5091,11245,1423),P(21692,2,15960,11312,1424,1425),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1426,I(21692,6354,21689,11312,1427,0|r,0,0,0,0),r=Wt(4),v[r>>2]=1428,$(21692,2700,2,15968,11312,1652,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1429,n=Wt(8),v[n+4>>2]=0,v[n>>2]=1430,I(21692,5875,21542,13049,1431,0|r,21542,13053,1432,0|n),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1433,$(21692,5116,3,15976,13053,1434,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1435,$(21692,1198,3,15988,11328,1436,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1437,$(21692,2710,2,16e3,13576,1438,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1439,$(21692,4615,3,16008,13224,1440,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1441,$(21692,4210,5,16032,13908,1442,0|r,0,0),r=Wt(4),v[r>>2]=1443,$(21692,4637,5,16064,13908,1653,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1444,$(21692,4253,6,16096,16120,1445,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1446,$(21692,4654,6,16128,16120,1447,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1448,$(21692,4174,4,16160,15056,1449,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1450,$(21692,4192,5,16176,16196,1451,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1452,$(21692,2652,3,15976,13053,1434,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1453,$(21692,1823,3,16204,11328,1454,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1455,$(21692,5045,2,16e3,13576,1438,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1456,$(21692,5058,2,16e3,13576,1438,0|r,0,0),C(21651,21695,21696,0,11240,1457,11243,0,11243,0,3907,11245,1458),P(21651,2,16216,11312,1459,1460),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1461,I(21651,6354,21681,11312,1462,0|r,0,0,0,0),r=Wt(4),v[r>>2]=1463,$(21651,2780,2,16224,11312,1654,0|r,0,0),Qi(2445,1464),Qi(3649,1465),r=Wt(4),v[r>>2]=1466,$(21651,2493,2,16240,11312,1656,0|r,0,0),r=Wt(4),v[r>>2]=1467,$(21651,2469,2,16248,11312,1657,0|r,0,0),r=Wt(4),v[r>>2]=1468,$(21651,2510,2,16256,11312,1658,0|r,0,0),r=Wt(4),v[r>>2]=1469,$(21651,1409,2,16264,11312,1659,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1470,I(21651,4381,21657,11312,1471,0|r,0,0,0,0),r=Wt(4),v[r>>2]=1472,I(21651,3520,21639,11312,1473,0|r,0,0,0,0),r=Wt(4),v[r>>2]=160,n=Wt(4),v[n>>2]=160,I(21651,5688,21542,13049,1474,0|r,21542,13053,1475,0|n),r=Wt(4),v[r>>2]=164,n=Wt(4),v[n>>2]=164,I(21651,6726,21542,13049,1474,0|r,21542,13053,1475,0|n),r=Wt(4),v[r>>2]=168,n=Wt(4),v[n>>2]=168,I(21651,6633,21542,13049,1474,0|r,21542,13053,1475,0|n),r=Wt(4),v[r>>2]=172,n=Wt(4),v[n>>2]=172,I(21651,1391,21542,13049,1474,0|r,21542,13053,1475,0|n),r=Wt(4),v[r>>2]=176,n=Wt(4),v[n>>2]=176,I(21651,1217,21542,13049,1474,0|r,21542,13053,1475,0|n),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1476,$(21651,5893,2,16272,13576,1477,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1478,$(21651,4473,2,16272,13576,1477,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1479,$(21651,5130,2,16272,13576,1477,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1480,$(21651,5165,2,16272,13576,1477,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1481,$(21651,5145,2,16272,13576,1477,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1482,$(21651,5348,2,16280,11312,1483,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1484,$(21651,5360,3,16288,11328,1485,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1486,$(21651,1376,3,16300,11328,1487,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1488,$(21651,1605,3,16312,11328,1489,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1490,$(21651,1317,3,16300,11328,1487,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1491,$(21651,5798,3,16324,13224,1492,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1493,$(21651,4407,3,16336,13224,1494,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1495,$(21651,5778,4,16352,13824,1496,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1497,$(21651,1927,4,16368,13824,1498,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1499,$(21651,1913,4,16384,11296,1500,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1501,$(21651,1724,3,16400,11328,1502,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1503,$(21651,1700,3,16412,11328,1504,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1505,$(21651,1741,3,16424,11328,1506,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1507,$(21651,5116,3,16436,13053,1508,0|r,0,0),C(2295,7357,7351,0,11240,1509,11243,0,11243,0,2295,11245,1510),r=Wt(8),v[r+4>>2]=1,v[r>>2]=12,$(2295,4432,3,16448,13224,1511,0|r,1,0),r=Wt(4),v[r>>2]=1512,$(2295,4463,4,16464,16480,1660,0|r,0,0),r=Wt(8),v[r+4>>2]=1,v[r>>2]=20,$(2295,6142,2,16488,13576,1513,0|r,1,0),C(2271,7306,7300,2295,11240,1514,11240,1515,11240,1516,2311,11245,1517),P(2271,3,16496,13044,1518,1519),r=Wt(4),v[r>>2]=4,n=Wt(4),v[n>>2]=4,I(2271,6683,21542,13049,1520,0|r,21542,13053,1521,0|n),r=Wt(4),v[r>>2]=8,n=Wt(4),v[n>>2]=8,I(2271,6577,21542,13049,1520,0|r,21542,13053,1521,0|n),r=Wt(8),v[r+4>>2]=1,v[r>>2]=12,$(2271,4432,3,16508,13224,1522,0|r,0,0),r=Wt(4),v[r>>2]=1523,$(2271,4463,4,16464,16480,1660,0|r,0,0),r=Wt(8),v[r+4>>2]=1,v[r>>2]=20,$(2271,6142,2,16520,13576,1524,0|r,0,0),C(2290,7332,7326,2295,11240,1525,11240,1526,11240,1527,2339,11245,1528),P(2290,3,16528,13420,1529,1530),r=Wt(8),v[r+4>>2]=1,v[r>>2]=12,$(2290,4432,3,16540,13224,1531,0|r,0,0),r=Wt(4),v[r>>2]=1532,$(2290,4463,4,16464,16480,1660,0|r,0,0),r=Wt(8),v[r+4>>2]=1,v[r>>2]=20,$(2290,6142,2,16552,13576,1533,0|r,0,0),r=Wt(4),v[r>>2]=4,n=Wt(4),v[n>>2]=4,I(2290,6691,21542,13049,1534,0|r,21542,13053,1535,0|n),r=Wt(4),v[r>>2]=8,n=Wt(4),v[n>>2]=8,I(2290,6585,21542,13049,1534,0|r,21542,13053,1535,0|n),r=Wt(4),v[r>>2]=12,n=Wt(4),v[n>>2]=12,I(2290,2432,21542,13049,1534,0|r,21542,13053,1535,0|n),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1536,n=Wt(8),v[n+4>>2]=0,v[n>>2]=1537,I(2290,5820,21542,13049,1538,0|r,21542,13053,1539,0|n),r=Wt(4),v[r>>2]=20,n=Wt(4),v[n>>2]=20,I(2290,6761,21542,13049,1534,0|r,21542,13053,1535,0|n),r=Wt(4),v[r>>2]=24,n=Wt(4),v[n>>2]=24,I(2290,6668,21542,13049,1534,0|r,21542,13053,1535,0|n),C(21697,21698,21699,0,11240,1540,11243,0,11243,0,4554,11245,1541),r=Wt(4),v[r>>2]=32,n=Wt(4),v[n>>2]=32,I(21697,1634,21553,11312,1542,0|r,21553,13224,1543,0|n),r=Wt(4),v[r>>2]=36,n=Wt(4),v[n>>2]=36,I(21697,1641,21553,11312,1542,0|r,21553,13224,1543,0|n),r=Wt(4),v[r>>2]=40,n=Wt(4),v[n>>2]=40,I(21697,3122,21553,11312,1542,0|r,21553,13224,1543,0|n),r=Wt(4),v[r>>2]=44,n=Wt(4),v[n>>2]=44,I(21697,3278,21553,11312,1542,0|r,21553,13224,1543,0|n),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1544,$(21697,2747,2,16560,11312,1545,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1546,$(21697,6438,2,16568,11312,1547,0|r,0,0),C(21538,21700,21701,0,11240,1548,11243,0,11243,0,5322,11245,1549),r=Wt(4),v[r>>2]=0,n=Wt(4),v[n>>2]=0,I(21538,5206,21553,11312,1550,0|r,21553,13224,1551,0|n),r=Wt(4),v[r>>2]=4,n=Wt(4),v[n>>2]=4,I(21538,2207,21553,11312,1550,0|r,21553,13224,1551,0|n),r=Wt(4);v[r>>2]=8,n=Wt(4),v[n>>2]=8,I(21538,1634,21553,11312,1550,0|r,21553,13224,1551,0|n),r=Wt(4),v[r>>2]=12,n=Wt(4),v[n>>2]=12,I(21538,2225,21553,11312,1550,0|r,21553,13224,1551,0|n),r=Wt(4),v[r>>2]=16,n=Wt(4),v[n>>2]=16,I(21538,1641,21553,11312,1550,0|r,21553,13224,1551,0|n),C(21702,21703,21704,0,11240,1552,11243,0,11243,0,6090,11245,1553),P(21702,1,16576,11240,1554,1555),r=Wt(4),v[r>>2]=0,n=Wt(4),v[n>>2]=0,I(21702,5885,21645,11312,1556,0|r,21645,13224,1557,0|n),r=Wt(4),v[r>>2]=4,n=Wt(4),v[n>>2]=4,I(21702,5123,21542,13049,1558,0|r,21542,13053,1559,0|n),r=Wt(4),v[r>>2]=1,n=Wt(4),v[n>>2]=1,I(21702,5843,21645,11312,1556,0|r,21645,13224,1557,0|n),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1560,$(21702,3847,3,16580,11328,1561,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1562,$(21702,4210,5,16592,16612,1563,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1564,$(21702,4407,3,16620,13224,1565,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1566,$(21702,4223,3,16632,13053,1567,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1568,$(21702,6446,2,16644,11312,1569,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1570,$(21702,6547,3,16652,13224,1571,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1572,$(21702,1773,3,16652,13224,1571,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1573,$(21702,3526,6,16672,16696,1574,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1575,$(21702,2308,3,16704,13224,1576,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1577,$(21702,2336,3,16716,13224,1578,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1579,$(21702,2324,2,16728,13576,1580,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1581,$(21702,5088,2,16736,11312,1582,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1583,$(21702,1246,5,16752,15892,1584,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1585,$(21702,3617,3,16772,13224,1586,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1587,$(21702,3595,4,16784,11296,1588,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1589,$(21702,5990,3,16652,13224,1571,0|r,0,0),r=Wt(4),v[r>>2]=1590,$(21702,2759,2,16800,11312,1661,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1591,$(21702,4334,6,16816,13784,1592,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1593,$(21702,1173,2,16728,13576,1580,0|r,0,0),r=Wt(8),v[r+4>>2]=0,v[r>>2]=1594,$(21702,5191,4,16848,11296,1595,0|r,0,0)}function X(r){var n,e=0,f=0,i=0,t=0,u=0,o=0,a=0,c=0,b=0,k=0,s=0,h=0;V=n=V-16|0;r:{n:{e:{f:{i:{t:{u:{o:{a:{c:{b:{k:{s:{v:{if((r|=0)>>>0<=244){if(3&(e=(o=v[5447])>>>(f=(a=r>>>0<11?16:r+11&-8)>>>3|0)|0)){e=21828+(r=(f=f+(1&~e)|0)<<3)|0,i=v[r+21836>>2],(0|e)!=(0|(r=v[i+8>>2]))?(v[r+12>>2]=e,v[e+8>>2]=r):(s=21788,h=ot(f)&o,v[s>>2]=h),r=i+8|0,e=f<<3,v[i+4>>2]=3|e,v[4+(e=e+i|0)>>2]=1|v[e+4>>2];break r}if((k=v[5449])>>>0>=a>>>0)break v;if(e){e=21828+(r=(i=nu(0-(r=(0-(r=2<<f)|r)&e<<f)&r))<<3)|0,t=v[r+21836>>2],(0|e)!=(0|(r=v[t+8>>2]))?(v[r+12>>2]=e,v[e+8>>2]=r):(o=ot(i)&o,v[5447]=o),v[t+4>>2]=3|a,i=(r=i<<3)-a|0,v[4+(f=t+a|0)>>2]=1|i,v[r+t>>2]=i,k&&(e=21828+(-8&k)|0,u=v[5452],(r=1<<(k>>>3))&o?r=v[e+8>>2]:(v[5447]=r|o,r=e),v[e+8>>2]=u,v[r+12>>2]=u,v[u+12>>2]=e,v[u+8>>2]=r),r=t+8|0,v[5452]=f,v[5449]=i;break r}if(!(b=v[5448]))break v;for(f=v[22092+(nu(0-b&b)<<2)>>2],u=(-8&v[f+4>>2])-a|0,e=f;(r=v[e+16>>2])||(r=v[e+20>>2]);)u=(i=(e=(-8&v[r+4>>2])-a|0)>>>0<u>>>0)?e:u,f=i?r:f,e=r;if(c=v[f+24>>2],(0|(i=v[f+12>>2]))!=(0|f)){r=v[f+8>>2],v[r+12>>2]=i,v[i+8>>2]=r;break n}if(!(r=v[(e=f+20|0)>>2])){if(!(r=v[f+16>>2]))break s;e=f+16|0}for(;t=e,i=r,(r=v[(e=r+20|0)>>2])||(e=i+16|0,r=v[i+16>>2]););v[t>>2]=0;break n}if(a=-1,!(r>>>0>4294967231)&&(a=-8&(r=r+11|0),b=v[5448])){u=0-a|0,o=0,a>>>0<256||(o=31,a>>>0>16777215||(o=62+((a>>>38-(r=F(r>>>8|0))&1)-(r<<1)|0)|0));l:{h:{if(e=v[22092+(o<<2)>>2])for(r=0,f=a<<(31!=(0|o)?25-(o>>>1|0):0);;){if(!((t=(-8&v[e+4>>2])-a|0)>>>0>=u>>>0||(i=e,u=t,t))){u=0,r=e;break h}if(t=v[e+20>>2],e=v[16+((f>>>29&4)+e|0)>>2],r=t?(0|t)==(0|e)?r:t:r,f<<=1,!e)break}else r=0;if(!(r|i)){if(i=0,!(r=(0-(r=2<<o)|r)&b))break v;r=v[22092+(nu(r&0-r)<<2)>>2]}if(!r)break l}for(;u=(f=(e=(-8&v[r+4>>2])-a|0)>>>0<u>>>0)?e:u,i=f?r:i,r=(e=v[r+16>>2])||v[r+20>>2];);}if(!(!i|v[5449]-a>>>0<=u>>>0)){if(o=v[i+24>>2],(0|i)!=(0|(f=v[i+12>>2]))){r=v[i+8>>2],v[r+12>>2]=f,v[f+8>>2]=r;break e}if(!(r=v[(e=i+20|0)>>2])){if(!(r=v[i+16>>2]))break k;e=i+16|0}for(;t=e,f=r,(r=v[(e=r+20|0)>>2])||(e=f+16|0,r=v[f+16>>2]););v[t>>2]=0;break e}}}if((r=v[5449])>>>0>=a>>>0){i=v[5452],(e=r-a|0)>>>0>=16?(v[4+(f=i+a|0)>>2]=1|e,v[r+i>>2]=e,v[i+4>>2]=3|a):(v[i+4>>2]=3|r,v[4+(r=r+i|0)>>2]=1|v[r+4>>2],f=0,e=0),v[5449]=e,v[5452]=f,r=i+8|0;break r}if((c=v[5450])>>>0>a>>>0){e=c-a|0,v[5450]=e,r=(f=v[5453])+a|0,v[5453]=r,v[r+4>>2]=1|e,v[f+4>>2]=3|a,r=f+8|0;break r}if(r=0,b=a+47|0,v[5565]?f=v[5567]:(v[5568]=-1,v[5569]=-1,v[5566]=4096,v[5567]=4096,v[5565]=n+12&-16^1431655768,v[5570]=0,v[5558]=0,f=4096),(e=(t=b+f|0)&(u=0-f|0))>>>0<=a>>>0)break r;if((i=v[5557])&&i>>>0<(o=(f=v[5555])+e|0)>>>0|f>>>0>=o>>>0)break r;v:{if(!(4&l[22232])){l:{h:{d:{p:{if(i=v[5453])for(r=22236;;){if((f=v[r>>2])>>>0<=i>>>0&i>>>0<f+v[r+4>>2]>>>0)break p;if(!(r=v[r+8>>2]))break}if(-1==(0|(f=Ff(0))))break l;if(o=e,(r=(i=v[5566])-1|0)&f&&(o=(e-f|0)+(r+f&0-i)|0),o>>>0<=a>>>0)break l;if((i=v[5557])&&i>>>0<(u=(r=v[5555])+o|0)>>>0|r>>>0>=u>>>0)break l;if((0|f)!=(0|(r=Ff(o))))break d;break v}if((0|(f=Ff(o=u&t-c)))==(v[r>>2]+v[r+4>>2]|0))break h;r=f}if(-1==(0|r))break l;if(a+48>>>0<=o>>>0){f=r;break v}if(-1==(0|Ff(f=(f=v[5567])+(b-o|0)&0-f)))break l;o=f+o|0,f=r;break v}if(-1!=(0|f))break v}v[5558]=4|v[5558]}if(-1==(0|(f=Ff(e)))|-1==(0|(r=Ff(0)))|r>>>0<=f>>>0)break a;if((o=r-f|0)>>>0<=a+40>>>0)break a}r=v[5555]+o|0,v[5555]=r,r>>>0>d[5556]&&(v[5556]=r);v:{if(t=v[5453]){for(r=22236;;){if(((i=v[r>>2])+(e=v[r+4>>2])|0)==(0|f))break v;if(!(r=v[r+8>>2]))break}break b}for((r=v[5451])>>>0<=f>>>0&&r||(v[5451]=f),r=0,v[5560]=o,v[5559]=f,v[5455]=-1,v[5456]=v[5565],v[5562]=0;e=21828+(i=r<<3)|0,v[i+21836>>2]=e,v[i+21840>>2]=e,32!=(0|(r=r+1|0)););e=(i=o-40|0)-(r=f+8&7?-8-f&7:0)|0,v[5450]=e,r=r+f|0,v[5453]=r,v[r+4>>2]=1|e,v[4+(f+i|0)>>2]=40,v[5454]=v[5569];break c}if(8&v[r+12>>2]|f>>>0<=t>>>0|i>>>0>t>>>0)break b;v[r+4>>2]=e+o,f=(r=t+8&7?-8-t&7:0)+t|0,v[5453]=f,r=(e=v[5450]+o|0)-r|0,v[5450]=r,v[f+4>>2]=1|r,v[4+(e+t|0)>>2]=40,v[5454]=v[5569];break c}i=0;break n}f=0;break e}d[5451]>f>>>0&&(v[5451]=f),e=f+o|0,r=22236;b:{k:{s:{for(;;){if((0|e)!=v[r>>2]){if(r=v[r+8>>2])continue;break s}break}if(!(8&l[r+12|0]))break k}for(r=22236;;){if((e=v[r>>2])>>>0<=t>>>0&&(u=e+v[r+4>>2]|0)>>>0>t>>>0)break b;r=v[r+8>>2]}}if(v[r>>2]=f,v[r+4>>2]=v[r+4>>2]+o,v[4+(b=(f+8&7?-8-f&7:0)+f|0)>>2]=3|a,r=(o=e+(e+8&7?-8-e&7:0)|0)-(c=a+b|0)|0,(0|t)==(0|o)){v[5453]=c,r=v[5450]+r|0,v[5450]=r,v[c+4>>2]=1|r;break f}if(v[5452]==(0|o)){v[5452]=c,r=v[5449]+r|0,v[5449]=r,v[c+4>>2]=1|r,v[r+c>>2]=r;break f}if(1!=(3&(u=v[o+4>>2])))break i;if(t=-8&u,u>>>0<=255){if((0|(f=v[o+12>>2]))==(0|(e=v[o+8>>2]))){s=21788,h=v[5447]&ot(u>>>3|0),v[s>>2]=h;break t}v[e+12>>2]=f,v[f+8>>2]=e;break t}if(a=v[o+24>>2],(0|o)!=(0|(f=v[o+12>>2]))){e=v[o+8>>2],v[e+12>>2]=f,v[f+8>>2]=e;break u}if(!(u=v[(e=o+20|0)>>2])){if(!(u=v[o+16>>2]))break o;e=o+16|0}for(;i=e,(u=v[(e=(f=u)+20|0)>>2])||(e=f+16|0,u=v[f+16>>2]););v[i>>2]=0;break u}for(e=(i=o-40|0)-(r=f+8&7?-8-f&7:0)|0,v[5450]=e,r=r+f|0,v[5453]=r,v[r+4>>2]=1|e,v[4+(f+i|0)>>2]=40,v[5454]=v[5569],v[(i=(r=(u+(u-39&7?39-u&7:0)|0)-47|0)>>>0<t+16>>>0?t:r)+4>>2]=27,r=v[5562],v[i+16>>2]=v[5561],v[i+20>>2]=r,r=v[5560],v[i+8>>2]=v[5559],v[i+12>>2]=r,v[5561]=i+8,v[5560]=o,v[5559]=f,v[5562]=0,r=i+24|0;v[r+4>>2]=7,e=r+8|0,r=r+4|0,e>>>0<u>>>0;);if((0|i)!=(0|t))if(v[i+4>>2]=-2&v[i+4>>2],u=i-t|0,v[t+4>>2]=1|u,v[i>>2]=u,u>>>0<=255)e=21828+(-8&u)|0,(f=v[5447])&(r=1<<(u>>>3))?r=v[e+8>>2]:(v[5447]=r|f,r=e),v[e+8>>2]=t,v[r+12>>2]=t,v[t+12>>2]=e,v[t+8>>2]=r;else{r=31,u>>>0<=16777215&&(r=62+((u>>>38-(r=F(u>>>8|0))&1)-(r<<1)|0)|0),v[t+28>>2]=r,v[t+16>>2]=0,v[t+20>>2]=0,e=22092+(r<<2)|0;b:{if((i=v[5448])&(f=1<<r)){for(r=u<<(31!=(0|r)?25-(r>>>1|0):0),i=v[e>>2];;){if((0|u)==(-8&v[(e=i)+4>>2]))break b;if(f=r>>>29|0,r<<=1,!(i=v[16+(f=(4&f)+e|0)>>2]))break}v[f+16>>2]=t}else v[5448]=f|i,v[e>>2]=t;v[t+24>>2]=e,v[t+12>>2]=t,v[t+8>>2]=t;break c}r=v[e+8>>2],v[r+12>>2]=t,v[e+8>>2]=t,v[t+24>>2]=0,v[t+12>>2]=e,v[t+8>>2]=r}}if(!((r=v[5450])>>>0<=a>>>0)){e=r-a|0,v[5450]=e,r=(f=v[5453])+a|0,v[5453]=r,v[r+4>>2]=1|e,v[f+4>>2]=3|a,r=f+8|0;break r}}v[5446]=48,r=0;break r}f=0}if(a){i=v[o+28>>2];u:{if(v[(e=22092+(i<<2)|0)>>2]==(0|o)){if(v[e>>2]=f,f)break u;s=21792,h=v[5448]&ot(i),v[s>>2]=h;break t}if(v[a+(v[a+16>>2]==(0|o)?16:20)>>2]=f,!f)break t}v[f+24>>2]=a,(e=v[o+16>>2])&&(v[f+16>>2]=e,v[e+24>>2]=f),(e=v[o+20>>2])&&(v[f+20>>2]=e,v[e+24>>2]=f)}}r=r+t|0,u=v[4+(o=t+o|0)>>2]}if(v[o+4>>2]=-2&u,v[c+4>>2]=1|r,v[r+c>>2]=r,r>>>0<=255)e=21828+(-8&r)|0,(f=v[5447])&(r=1<<(r>>>3))?r=v[e+8>>2]:(v[5447]=r|f,r=e),v[e+8>>2]=c,v[r+12>>2]=c,v[c+12>>2]=e,v[c+8>>2]=r;else{u=31,r>>>0<=16777215&&(u=62+((r>>>38-(e=F(r>>>8|0))&1)-(e<<1)|0)|0),v[c+28>>2]=u,v[c+16>>2]=0,v[c+20>>2]=0,e=22092+(u<<2)|0;i:{if((i=v[5448])&(f=1<<u)){for(u=r<<(31!=(0|u)?25-(u>>>1|0):0),f=v[e>>2];;){if(e=f,(-8&v[f+4>>2])==(0|r))break i;if(f=u>>>29|0,u<<=1,!(f=v[16+(i=(4&f)+e|0)>>2]))break}v[i+16>>2]=c}else v[5448]=f|i,v[e>>2]=c;v[c+24>>2]=e,v[c+12>>2]=c,v[c+8>>2]=c;break f}r=v[e+8>>2],v[r+12>>2]=c,v[e+8>>2]=c,v[c+24>>2]=0,v[c+12>>2]=e,v[c+8>>2]=r}}r=b+8|0;break r}e:if(o){e=v[i+28>>2];f:{if(v[(r=22092+(e<<2)|0)>>2]==(0|i)){if(v[r>>2]=f,f)break f;b=ot(e)&b,v[5448]=b;break e}if(v[o+(v[o+16>>2]==(0|i)?16:20)>>2]=f,!f)break e}v[f+24>>2]=o,(r=v[i+16>>2])&&(v[f+16>>2]=r,v[r+24>>2]=f),(r=v[i+20>>2])&&(v[f+20>>2]=r,v[r+24>>2]=f)}e:if(u>>>0<=15)r=u+a|0,v[i+4>>2]=3|r,v[4+(r=r+i|0)>>2]=1|v[r+4>>2];else if(v[i+4>>2]=3|a,v[4+(t=i+a|0)>>2]=1|u,v[t+u>>2]=u,u>>>0<=255)e=21828+(-8&u)|0,(f=v[5447])&(r=1<<(u>>>3))?r=v[e+8>>2]:(v[5447]=r|f,r=e),v[e+8>>2]=t,v[r+12>>2]=t,v[t+12>>2]=e,v[t+8>>2]=r;else{r=31,u>>>0<=16777215&&(r=62+((u>>>38-(r=F(u>>>8|0))&1)-(r<<1)|0)|0),v[t+28>>2]=r,v[t+16>>2]=0,v[t+20>>2]=0,e=22092+(r<<2)|0;f:{if((f=1<<r)&b){for(r=u<<(31!=(0|r)?25-(r>>>1|0):0),a=v[e>>2];;){if((-8&v[(e=a)+4>>2])==(0|u))break f;if(f=r>>>29|0,r<<=1,!(a=v[16+(f=(4&f)+e|0)>>2]))break}v[f+16>>2]=t}else v[5448]=f|b,v[e>>2]=t;v[t+24>>2]=e,v[t+12>>2]=t,v[t+8>>2]=t;break e}r=v[e+8>>2],v[r+12>>2]=t,v[e+8>>2]=t,v[t+24>>2]=0,v[t+12>>2]=e,v[t+8>>2]=r}r=i+8|0;break r}n:if(c){e=v[f+28>>2];e:{if(v[(r=22092+(e<<2)|0)>>2]==(0|f)){if(v[r>>2]=i,i)break e;s=21792,h=ot(e)&b,v[s>>2]=h;break n}if(v[c+(v[c+16>>2]==(0|f)?16:20)>>2]=i,!i)break n}v[i+24>>2]=c,(r=v[f+16>>2])&&(v[i+16>>2]=r,v[r+24>>2]=i),(r=v[f+20>>2])&&(v[i+20>>2]=r,v[r+24>>2]=i)}u>>>0<=15?(r=u+a|0,v[f+4>>2]=3|r,v[4+(r=r+f|0)>>2]=1|v[r+4>>2]):(v[f+4>>2]=3|a,v[4+(i=f+a|0)>>2]=1|u,v[i+u>>2]=u,k&&(e=21828+(-8&k)|0,t=v[5452],(r=1<<(k>>>3))&o?r=v[e+8>>2]:(v[5447]=r|o,r=e),v[e+8>>2]=t,v[r+12>>2]=t,v[t+12>>2]=e,v[t+8>>2]=r),v[5452]=i,v[5449]=u),r=f+8|0}return V=n+16|0,0|r}function Q(r,n,e,f,t){var o,a=0,c=0,b=0,s=0,h=0,d=w(0),y=0,m=0,g=0,F=0,A=0,T=0,$=0,I=0,C=0,P=0,E=0,O=0,R=0,S=0,W=0,G=0,U=0,j=0,H=0,L=0,M=0,_=0,z=0,x=0,J=0,K=0,B=0,N=0,q=0,D=0,Z=0,Y=0,X=0,Q=0,rr=0,nr=0,er=0;V=o=V-32|0;r:{n:{e:{if(e){if($=jn(n,1))break e;break r}if(y=Ae(a=ut(68),e=ht(c=o+20|0,e=(e=jn(n,1))?v[(v[f+200>>2]+(e<<2)|0)-4>>2]:0,0)),gi(e),(0|(a=jn(n,1)))>0)for(c=y+36|0,e=0;s=jn(n,1),Un(c,v[f+28>>2]+(s<<2)|0),(0|a)!=(0|(e=e+1|0)););if((0|(a=jn(n,1)))>0)for(c=y+52|0,e=0;s=jn(n,1),v[o+20>>2]=v[v[f+112>>2]+(s<<2)>>2],Un(c,o+20|0),(0|a)!=(0|(e=e+1|0)););if((0|(a=jn(n,1)))>0)for(c=y+52|0,e=0;s=jn(n,1),v[o+20>>2]=v[v[f+128>>2]+(s<<2)>>2],Un(c,o+20|0),(0|a)!=(0|(e=e+1|0)););if((0|(a=jn(n,1)))>0)for(c=y+52|0,e=0;s=jn(n,1),v[o+20>>2]=v[v[f+144>>2]+(s<<2)>>2],Un(c,o+20|0),(0|a)!=(0|(e=e+1|0)););$=jn(n,1);break n}y=Ae(a=ut(68),e=ht(o+20|0,2155,0)),gi(e)}if(!((0|$)<=0))for(;;){if(P=jn(n,1),I=0,(0|(E=jn(n,1)))>0)for(;;){a=C=ht(a=o+20|0,e=(e=jn(n,1))?v[(v[f+200>>2]+(e<<2)|0)-4>>2]:0,0),V=s=V-80|0,m=ht(c=s+68|0,e=(e=jn(n,1))?v[(v[f+200>>2]+(e<<2)|0)-4>>2]:0,0),v[m+4>>2]||te(m,a),a=v[n+4>>2],v[n+4>>2]=a+1,e=0;n:{e:switch(l[0|a]){case 0:c=ht(a=s+56|0,e=(e=jn(n,1))?v[(v[f+200>>2]+(e<<2)|0)-4>>2]:0,0),v[c+4>>2]||te(c,m),a=v[n+4>>2],v[n+4>>2]=a+1,h=l[0|a],v[n+4>>2]=a+2,b=l[a+1|0],v[n+4>>2]=a+3,F=l[a+2|0],v[n+4>>2]=a+4,g=l[a+3|0],v[n+4>>2]=a+5,A=l[a+4|0],v[n+4>>2]=a+6,T=l[a+5|0],v[n+4>>2]=a+7,W=l[a+6|0],v[n+4>>2]=a+8,G=l[a+7|0],v[n+4>>2]=a+9,U=l[a+8|0],v[n+4>>2]=a+10,j=l[a+9|0],v[n+4>>2]=a+11,H=l[a+10|0],v[n+4>>2]=a+12,L=l[a+11|0],v[n+4>>2]=a+13,M=l[a+12|0],v[n+4>>2]=a+14,_=l[a+13|0],v[n+4>>2]=a+15,z=l[a+14|0],v[n+4>>2]=a+16,x=l[a+15|0],v[n+4>>2]=a+17,J=l[a+16|0],v[n+4>>2]=a+18,K=l[a+17|0],v[n+4>>2]=a+19,B=l[a+18|0],v[n+4>>2]=a+20,N=l[a+19|0],v[n+4>>2]=a+21,q=l[a+20|0],v[n+4>>2]=a+22,D=l[a+21|0],v[n+4>>2]=a+23,Z=l[a+22|0],v[n+4>>2]=a+24,Y=l[a+23|0],v[n+4>>2]=a+25,X=l[a+24|0],v[n+4>>2]=a+26,Q=l[a+25|0],v[n+4>>2]=a+27,rr=l[a+26|0],e=a+28|0,v[n+4>>2]=e,a=l[a+27|0],l[21364]||(v[5337]=0,v[5338]=0,v[5339]=0,v[5340]=0,v[5336]=9372,i[21364]=1,e=v[n+4>>2]),v[n+4>>2]=e+1,p[5337]=w(l[0|e])/w(255),v[n+4>>2]=e+2,p[5338]=w(l[e+1|0])/w(255),v[n+4>>2]=e+3,p[5339]=w(l[e+2|0])/w(255),v[n+4>>2]=e+4,p[5340]=w(l[e+3|0])/w(255),e=v[r+4>>2],O=Ue(s+44|0,m),R=Ue(s+32|0,c),e=0|Ta[v[v[e>>2]+12>>2]](e,y,O,R),gi(R),gi(O),e&&(te(e+116|0,c),v[e+40>>2]=g|(F|b<<8|h<<16)<<8,d=p[r+36>>2],v[e+48>>2]=(K<<8|J<<16|B)<<8|N,v[e+44>>2]=(_<<8|M<<16|z)<<8|x,p[e+56>>2]=d*(u(2,a|(Q<<8|X<<16|rr)<<8),k()),p[e+52>>2]=d*(u(2,(D<<8|q<<16|Z)<<8|Y),k()),p[e+36>>2]=d*(u(2,(j<<8|U<<16|H)<<8|L),k()),p[e+32>>2]=d*(u(2,(T<<8|A<<16|W)<<8|G),k()),a=v[5338],v[e+148>>2]=v[5337],v[e+152>>2]=a,a=v[5340],v[e+156>>2]=v[5339],v[e+160>>2]=a,Jr(e),a=v[r+4>>2],Ta[v[v[a>>2]+36>>2]](a,e)),gi(c);break n;case 1:a=jn(n,1),e=v[r+4>>2],c=Ue(s+56|0,m),e=0|Ta[v[v[e>>2]+20>>2]](e,y,c),gi(c),Cr(r,n,e,a),t&&(v[n+4>>2]=v[n+4>>2]+4),a=v[r+4>>2],Ta[v[v[a>>2]+36>>2]](a,e);break n;case 2:h=ht(a=s+56|0,e=(e=jn(n,1))?v[(v[f+200>>2]+(e<<2)|0)-4>>2]:0,0),v[h+4>>2]||te(h,m),e=v[r+4>>2],c=Ue(s+44|0,m),b=Ue(s+32|0,h),a=0|Ta[v[v[e>>2]+16>>2]](e,y,c,b),gi(b),gi(c),e=a,a||(e=zr(ut(236),m)),te(e+168|0,h),b=aa(e),c=v[n+4>>2],v[n+4>>2]=c+1,p[b+4>>2]=w(l[0|c])/w(255),v[n+4>>2]=c+2,p[b+8>>2]=w(l[c+1|0])/w(255),v[n+4>>2]=c+3,p[b+12>>2]=w(l[c+2|0])/w(255),v[n+4>>2]=c+4,p[b+16>>2]=w(l[c+3|0])/w(255),Vr(n,(c=jn(n,1))<<1,w(1),e+120|0),fe(n,e+136|0),Cr(r,n,e,c),lr(e),nr=e,er=jn(n,1)<<1,v[nr+224>>2]=er,t?(fe(n,e+152|0),c=v[n+4>>2],v[n+4>>2]=c+1,b=l[0|c],v[n+4>>2]=c+2,F=l[c+1|0],v[n+4>>2]=c+3,g=l[c+2|0],v[n+4>>2]=c+4,d=p[r+36>>2],p[e+196>>2]=d*(u(2,l[c+3|0]|(g|F<<8|b<<16)<<8),k()),v[n+4>>2]=c+5,b=l[c+4|0],v[n+4>>2]=c+6,F=l[c+5|0],v[n+4>>2]=c+7,g=l[c+6|0],v[n+4>>2]=c+8,d=w(d*(u(2,l[c+7|0]|(g|F<<8|b<<16)<<8),k()))):(v[e+196>>2]=0,d=w(0)),p[e+200>>2]=d,a?(a=v[r+4>>2],Ta[v[v[a>>2]+36>>2]](a,e)):(Ta[v[v[e>>2]+4>>2]](e),e=0),gi(h);break n;case 3:h=ht(a=s+56|0,e=(e=jn(n,1))?v[(v[f+200>>2]+(e<<2)|0)-4>>2]:0,0),v[h+4>>2]||te(h,m),e=v[r+4>>2],c=Ue(a=s+44|0,m),b=Ue(s+32|0,h),e=0|Ta[v[v[e>>2]+16>>2]](e,y,c,b),gi(b),gi(c),te(e+168|0,h),b=aa(e),c=v[n+4>>2],v[n+4>>2]=c+1,p[b+4>>2]=w(l[0|c])/w(255),v[n+4>>2]=c+2,p[b+8>>2]=w(l[c+1|0])/w(255),v[n+4>>2]=c+3,p[b+12>>2]=w(l[c+2|0])/w(255),v[n+4>>2]=c+4,p[b+16>>2]=w(l[c+3|0])/w(255),c=ht(a,c=(c=jn(n,1))?v[(v[f+200>>2]+(c<<2)|0)-4>>2]:0,0),b=ht(b=s+32|0,a=(a=jn(n,1))?v[(v[f+200>>2]+(a<<2)|0)-4>>2]:0,0),a=v[n+4>>2],v[n+4>>2]=a+1,F=l[0|a],t&&(v[n+4>>2]=a+2,g=l[a+1|0],v[n+4>>2]=a+3,A=l[a+2|0],v[n+4>>2]=a+4,T=l[a+3|0],v[n+4>>2]=a+5,d=p[r+36>>2],p[e+196>>2]=d*(u(2,l[a+4|0]|(T|A<<8|g<<16)<<8),k()),v[n+4>>2]=a+6,g=l[a+5|0],v[n+4>>2]=a+7,A=l[a+6|0],v[n+4>>2]=a+8,T=l[a+7|0],v[n+4>>2]=a+9,p[e+200>>2]=d*(u(2,l[a+8|0]|(T|A<<8|g<<16)<<8),k())),F=Ui(T=ut(40),e,a=Ue(s+20|0,c),P,A=Ue(g=s+8|0,b),!!(0|F)),gi(A),gi(a),v[s+8>>2]=F,Un(r+8|0,g),gi(b),gi(c),gi(h);break n;case 4:if(e=v[r+4>>2],a=Ue(c=s+56|0,m),e=0|Ta[v[v[e>>2]+24>>2]](e,y,a),gi(a),a=v[n+4>>2],v[n+4>>2]=a+1,h=0,i[e+80|0]=0!=l[0|a],v[n+4>>2]=a+2,i[e+81|0]=0!=l[a+1|0],Cr(r,n,e,a=jn(n,1)),v[s+56>>2]=0,wn(e- -64|0,b=(0|a)/3|0,c),(0|a)>=3)for(F=v[e+76>>2],a=v[n+4>>2];v[n+4>>2]=a+1,g=l[0|a],v[n+4>>2]=a+2,A=l[a+1|0],v[n+4>>2]=a+3,T=l[a+2|0],c=a+4|0,v[n+4>>2]=c,p[F+(h<<2)>>2]=p[r+36>>2]*(u(2,l[a+3|0]|(T|A<<8|g<<16)<<8),k()),a=c,(0|b)!=(0|(h=h+1|0)););t&&(v[n+4>>2]=v[n+4>>2]+4),a=v[r+4>>2],Ta[v[v[a>>2]+36>>2]](a,e);break n;case 5:e=v[r+4>>2],a=Ue(s+56|0,m),e=0|Ta[v[v[e>>2]+28>>2]](e,y,a),gi(a),a=v[n+4>>2],v[n+4>>2]=a+1,c=l[0|a],v[n+4>>2]=a+2,h=l[a+1|0],v[n+4>>2]=a+3,b=l[a+2|0],v[n+4>>2]=a+4,v[e+28>>2]=l[a+3|0]|(b|h<<8|c<<16)<<8,v[n+4>>2]=a+5,c=l[a+4|0],v[n+4>>2]=a+6,h=l[a+5|0],v[n+4>>2]=a+7,b=l[a+6|0],v[n+4>>2]=a+8,d=p[r+36>>2],p[e+20>>2]=d*(u(2,l[a+7|0]|(b|h<<8|c<<16)<<8),k()),v[n+4>>2]=a+9,c=l[a+8|0],v[n+4>>2]=a+10,h=l[a+9|0],v[n+4>>2]=a+11,b=l[a+10|0],v[n+4>>2]=a+12,p[e+24>>2]=d*(u(2,l[a+11|0]|(b|h<<8|c<<16)<<8),k()),t&&(v[n+4>>2]=a+16),a=v[r+4>>2],Ta[v[v[a>>2]+36>>2]](a,e);break n;case 6:break e;default:break n}a=jn(n,1),c=jn(n,1),e=v[r+4>>2],Cr(r,n,e=0|Ta[v[v[e>>2]+32>>2]](e,y,m),c),v[e+64>>2]=v[v[f+44>>2]+(a<<2)>>2],t&&(v[n+4>>2]=v[n+4>>2]+4),a=v[r+4>>2],Ta[v[v[a>>2]+36>>2]](a,e)}if(gi(m),V=s+80|0,e&&(It(y,P,a=Ue(o+8|0,C),e),gi(a)),gi(C),(0|E)==(0|(I=I+1|0)))break}if((0|(S=S+1|0))==(0|$))break}}return V=o+32|0,y}function rr(r,n,e){r|=0,n|=0,e|=0;var f,t=0,u=0,o=0,a=0,c=0,b=0,k=0,s=0,h=0,d=w(0),y=w(0),m=0,g=0,F=w(0),A=w(0),T=w(0),$=0,I=0,C=w(0),P=w(0),E=0,O=0,R=w(0),S=w(0),W=w(0),G=w(0),U=w(0),j=w(0),H=0,L=0,M=0;if(V=f=V-16|0,!v[r+224>>2]){v[r+224>>2]=e,u=v[e+52>>2],v[f+12>>2]=0,wn(b=r+128|0,u,f+12|0),Ji(e,n,0,u,b,0,2),nn(b),e=0,V=c=V-16|0,n=v[b+4>>2],v[40+(a=r+4|0)>>2]=0,ya(g=a+36|0,k=n>>>1|0),v[c+12>>2]=0,gn(g,k,c+12|0);r:if(n>>>0>=2){for(u=k>>>0<=1?1:k,s=v[a+48>>2];v[s+(e<<2)>>2]=e,(0|u)!=(0|(e=e+1|0)););if(Yf(u=a+52|0,k),e=0,i[c+11|0]=0,On(u,k,c+11|0),n>>>0<2)break r;for(u=k>>>0<=1?1:k,s=v[a- -64>>2];L=e+s|0,M=re(e,k,b,g),i[0|L]=M,(0|u)!=(0|(e=e+1|0)););}else Yf(e=a+52|0,k),i[c+11|0]=0,On(e,k,c+11|0);if(v[a+72>>2]=0,ya($=a+68|0,(((0|k)<=2?2:k)<<2)-8|0),n>>>0>=8)for(t=v[a- -64>>2];;){I=v[b+12>>2],h=v[a+48>>2],n=0,e=1,s=k=(o=k)-1|0;r:{for(;;){n:{u=n,n=e;e:if(!l[t+u|0]){if((0|s)==(0|(e=(0|(e=n+1|0))!=(0|o)?e:0)))break n;for(m=I+(v[h+(n<<2)>>2]<<3)|0,C=p[m>>2],E=I+(v[h+(u<<2)>>2]<<3)|0,T=p[E>>2],O=I+(v[h+(s<<2)>>2]<<3)|0,R=p[O>>2],y=p[m+4>>2],d=p[E+4>>2],G=w(y-d),A=p[O+4>>2],U=w(d-A),j=w(A-y);;){if(l[e+t|0]&&(m=I+(v[h+(e<<2)>>2]<<3)|0,P=p[m>>2],F=p[m+4>>2],!(!(w(w(P*j)+w(w(C*w(F-A))+w(R*w(y-F))))>=w(0))|!(w(w(P*U)+w(w(R*w(F-d))+w(T*w(A-F))))>=w(0)))&&w(w(P*G)+w(w(T*w(F-y))+w(C*w(d-F))))>=w(0)))break e;if((0|s)==(0|(e=(e+1>>>0)%(o>>>0)|0)))break}break n}if(n){e=(0|(e=n+1|0))!=(0|o)?e:0,s=u;continue}for(;;){if(!l[t+u|0])break n;if(n=0,!(u=u-1|0))break r}}break}n=u}if(Un($,h+(((I=n+k|0)>>>0)%(o>>>0)<<2)|0),Un($,(e=n<<2)+v[a+48>>2]|0),Un($,v[a+48>>2]+((n+1>>>0)%(o>>>0)<<2)|0),u=v[a+40>>2]-1|0,v[a+40>>2]=u,n>>>0<u>>>0)for(s=v[a+48>>2],o=v[s+e>>2],e=n;m=s+(e<<2)|0,t=s+((e=e+1|0)<<2)|0,v[m>>2]=v[t>>2],v[t>>2]=o,(0|e)!=(0|u););if(s=v[a+56>>2]-1|0,v[a+56>>2]=s,t=v[a+64>>2],n>>>0<s>>>0)for(o=l[n+t|0],e=n;m=l[0|(h=(u=e+1|0)+t|0)],i[0|h]=o,i[e+t|0]=m,(0|s)!=(0|(e=u)););if(L=(e=(I-1>>>0)%(k>>>0)|0)+t|0,M=re(e,k,b,g),i[0|L]=M,L=(n=(0|n)!=(0|k)?n:0)+t|0,M=re(n,k,b,g),i[0|L]=M,!(k>>>0>3))break}if(3==(0|k)&&(Un($,v[a+48>>2]+8|0),Un($,v[a+48>>2]),Un($,v[a+48>>2]+4|0)),e=$,n=0,V=t=(V=c+16|0)-48|0,u=v[a+8>>2])for(s=a+84|0;pe(s,v[v[a+16>>2]+(n<<2)>>2]),(0|u)!=(0|(n=n+1|0)););if(n=0,v[a+8>>2]=0,u=v[a+24>>2])for(s=a+104|0;pe(s,v[v[a+32>>2]+(n<<2)>>2]),(0|u)!=(0|(n=n+1|0)););if(s=a+4|0,v[a+24>>2]=0,n=ea(k=a+104|0),v[t+44>>2]=n,v[n+4>>2]=0,n=fa($=a+84|0),v[t+40>>2]=n,v[n+4>>2]=0,E=v[e+4>>2]){for(g=a+20|0,I=-1,u=0,n=0;o=v[e+12>>2]+(n<<2)|0,h=(c=v[o>>2])<<1,v[t+36>>2]=h,m=v[o+4>>2],v[t+32>>2]=m<<1,O=v[o+8>>2],v[t+28>>2]=O<<1,c=(o=v[b+12>>2])+(c<<3)|0,p[t+24>>2]=p[c>>2],p[t+20>>2]=p[c+4>>2],c=o+(m<<3)|0,p[t+16>>2]=p[c>>2],p[t+12>>2]=p[c+4>>2],F=p[(o=o+(O<<3)|0)>>2],p[t+8>>2]=F,y=p[o+4>>2],p[t+4>>2]=y,c=v[t+40>>2],m=v[c+4>>2],(0|h)!=(0|I)||(o=v[c+12>>2],d=p[(h=o+(m<<2)|0)-16>>2],C=p[h-12>>2],A=w(p[h-4>>2]-C),T=w(p[h-8>>2]-d),(0|(w(w(w(T*C)+w(w(F*A)-w(y*T)))-w(d*A))>=w(0)?1:-1))!=(0|u)||(d=w(p[o>>2]-F),A=w(d*y),y=w(p[o+4>>2]-y),(0|(w(w(A+w(w(p[o+8>>2]*y)-w(p[o+12>>2]*d)))-w(F*y))>=w(0)?1:-1))!=(0|u)))?(m?(Un(s,t+40|0),Un(g,t+44|0)):(pe($,c),pe(k,v[t+44>>2])),u=fa($),v[t+40>>2]=u,v[u+4>>2]=0,Gn(u,t+24|0),Gn(v[t+40>>2],t+20|0),Gn(v[t+40>>2],t+16|0),Gn(v[t+40>>2],t+12|0),Gn(v[t+40>>2],t+8|0),Gn(v[t+40>>2],t+4|0),u=ea(k),v[t+44>>2]=u,v[u+4>>2]=0,Un(u,t+36|0),Un(v[t+44>>2],t+32|0),Un(v[t+44>>2],t+28|0),y=p[t+20>>2],F=w(p[t+12>>2]-y),d=p[t+24>>2],A=w(p[t+16>>2]-d),u=w(w(w(A*y)+w(w(p[t+8>>2]*F)-w(p[t+4>>2]*A)))-w(d*F))>=w(0)?1:-1,I=v[t+36>>2]):(Gn(c,t+8|0),Gn(v[t+40>>2],t+4|0),Un(v[t+44>>2],t+28|0)),E>>>0>(n=n+3|0)>>>0;);v[v[t+40>>2]+4>>2]&&(Un(s,t+40|0),Un(g,t+44|0))}if(o=v[a+8>>2]){for(e=0;;){if(n=v[(u=e<<2)+v[a+32>>2]>>2],v[t+44>>2]=n,b=v[n+4>>2])for(n=v[n+12>>2],c=v[(n+(b<<2)|0)-4>>2],h=v[n>>2],n=v[u+v[a+16>>2]>>2],v[t+40>>2]=n,u=v[n+4>>2]<<2,n=v[n+12>>2],F=p[(u=u+n|0)-8>>2],d=p[u-4>>2],A=p[u-12>>2],y=w(d-A),C=p[u-16>>2],T=w(F-C),R=p[n>>2],P=p[n+4>>2],G=w(w(w(T*A)+w(w(R*y)-w(P*T)))-w(C*y)),U=p[n+12>>2],j=p[n+8>>2],n=0;y=d,(0|n)!=(0|e)?(u=v[(g=n<<2)+v[a+32>>2]>>2],3!=v[u+4>>2]||(b=v[u+12>>2],I=v[b+4>>2],m=v[b>>2],v[t+36>>2]=v[b+8>>2],b=v[g+v[a+16>>2]>>2],g=(v[b+4>>2]<<2)+v[b+12>>2]|0,d=p[g-8>>2],p[t+32>>2]=d,T=p[g-4>>2],p[t+28>>2]=T,(0|h)!=(0|m)|(0|c)!=(0|I)||(g=G>=w(0),S=w(F-C),W=w(y-A),(0|g)==(0|!(w(w(w(S*A)+w(w(d*W)-w(T*S)))-w(C*W))>=w(0)))||(S=w(R-d),W=w(S*T),T=w(P-T),g^w(w(W+w(w(j*T)-w(U*S)))-w(d*T))>=w(0))))?d=y:(n=0,v[b+4>>2]=0,v[u+4>>2]=0,Gn(v[t+40>>2],t+32|0),Gn(v[t+40>>2],t+28|0),Un(v[t+44>>2],t+36|0),d=p[t+28>>2],A=y,C=F,F=p[t+32>>2])):n=e,o>>>0>(n=n+1|0)>>>0;);if((0|o)==(0|(e=e+1|0)))break}if(!((0|(u=v[a+8>>2]))<=0))for(;;){if(o=v[(c=(e=u-1|0)<<2)+v[a+16>>2]>>2],v[t+40>>2]=o,!v[o+4>>2]){if(b=v[a+8>>2]-1|0,v[a+8>>2]=b,b>>>0>(n=e)>>>0){for(;o=v[a+16>>2],g=v[(h=o+(n<<2)|0)>>2],m=o,o=(n=n+1|0)<<2,v[h>>2]=v[m+o>>2],v[o+v[a+16>>2]>>2]=g,(0|n)!=(0|b););o=v[t+40>>2]}if(pe($,o),o=v[c+v[a+32>>2]>>2],v[t+44>>2]=o,b=v[a+24>>2]-1|0,v[a+24>>2]=b,b>>>0>(n=e)>>>0){for(;o=v[a+32>>2],h=v[(c=o+(n<<2)|0)>>2],m=o,o=(n=n+1|0)<<2,v[c>>2]=v[m+o>>2],v[o+v[a+32>>2]>>2]=h,(0|n)!=(0|b););o=v[t+44>>2]}pe(k,o)}if(n=(0|u)>1,u=e,!n)break}}if(V=t+48|0,e=s,v[r+228>>2]=e,v[e+4>>2])for(;nn(n=v[v[e+12>>2]+(H<<2)>>2]),Gn(n,v[n+12>>2]),Gn(n,v[n+12>>2]+4|0),e=v[r+228>>2],(u=v[e+4>>2])>>>0>(H=H+1|0)>>>0;);else u=0}return V=f+16|0,0|u}function nr(r){var n,e,f,t=0,u=0,o=0,a=0,c=0,b=0,k=0,h=0,d=0,p=0,y=0,m=0;if(v[124+(r|=0)>>2]=0,v[r+108>>2]=0,u=v[r+12>>2])for(;a=v[v[r+20>>2]+(t<<2)>>2],h=l[v[a+4>>2]+60|0],i[a+116|0]=h,i[a+117|0]=1^h,(0|u)!=(0|(t=t+1|0)););if((h=v[r+136>>2])&&(u=v[h+40>>2]))for(t=0;;){for(a=v[v[r+20>>2]+(v[v[v[h+48>>2]+(t<<2)>>2]+4>>2]<<2)>>2];s[a+116>>1]=256,a=v[a+12>>2];);if((0|u)==(0|(t=t+1|0)))break}r:if(h=(f=v[r+92>>2])+((n=v[r+76>>2])+(e=v[r+60>>2])|0)|0)for(a=0;;){n:{e:{f:if(e){if(t=0,d=1,u=v[v[r+68>>2]>>2],v[v[u+4>>2]+16>>2]!=(0|a)){for(;;){if((0|e)==(0|(t=t+1|0)))break f;if(u=v[v[r+68>>2]+(t<<2)>>2],v[v[u+4>>2]+16>>2]==(0|a))break}d=t>>>0<e>>>0}V=c=V-16|0;i:{t:if(l[v[u+40>>2]+117|0]){u:{if(l[v[u+4>>2]+20|0]){if(!(t=v[r+136>>2]))break t;if(!(o=v[t+56>>2])){i[u+44|0]=0;break i}if(k=v[u+4>>2],b=v[t- -64>>2],(0|k)!=v[b>>2]){for(t=0;(0|o)!=(0|(t=t+1|0))&(0|k)!=v[b+(t<<2)>>2];);if(t=t>>>0<o>>>0,i[u+44|0]=t,t)break u;break i}}i[u+44|0]=1}Qf(r,v[u+40>>2]),Qf(r,k=v[v[u+20>>2]>>2]);u:if(!((t=v[u+12>>2])>>>0<2)){if(o=v[(v[u+20>>2]+(t<<2)|0)-4>>2],v[c+12>>2]=o,b=v[r+108>>2]){if(p=v[r+116>>2],(0|o)==v[p>>2])break u;for(t=0;(0|b)!=(0|(t=t+1|0))&(0|o)!=v[p+(t<<2)>>2];);if(t>>>0<b>>>0)break u}Un(r+120|0,c+12|0)}v[c+8>>2]=u,Un(r+104|0,c+8|0),kf(k+16|0),i[v[(v[u+20>>2]+(v[u+12>>2]<<2)|0)-4>>2]+116|0]=1;break i}i[u+44|0]=0}if(V=c+16|0,a=a+1|0,d)break e}f:if(n){if(t=0,d=1,u=v[v[r+84>>2]>>2],v[v[u+4>>2]+16>>2]!=(0|a)){for(;;){if((0|n)==(0|(t=t+1|0)))break f;if(u=v[v[r+84>>2]+(t<<2)>>2],v[v[u+4>>2]+16>>2]==(0|a))break}d=t>>>0<n>>>0}V=c=V-16|0;i:{t:{u:if(l[v[u+24>>2]+117|0]){o:{if(l[v[u+4>>2]+20|0]){if(!(t=v[r+136>>2]))break u;if(!(o=v[t+56>>2])){i[u+44|0]=0;break i}if(k=v[u+4>>2],b=v[t- -64>>2],(0|k)!=v[b>>2]){for(t=0;(0|o)!=(0|(t=t+1|0))&(0|k)!=v[b+(t<<2)>>2];);if(t=t>>>0<o>>>0,i[u+44|0]=t,t)break o;break i}}i[u+44|0]=1}if(Qf(r,v[u+24>>2]),o=v[u+12>>2],l[v[u+4>>2]+85|0]){if(!o)break t;for(p=r+120|0,b=0;;){t=v[v[u+20>>2]+(b<<2)>>2],v[c+12>>2]=t,Qf(r,v[t+12>>2]);o:{if(k=v[r+108>>2]){if(t=0,y=v[c+12>>2],m=v[r+116>>2],(0|y)==v[m>>2])break o;for(;(0|k)!=(0|(t=t+1|0))&v[(t<<2)+m>>2]!=(0|y););if(t>>>0<k>>>0)break o}Un(p,c+12|0)}if((0|o)==(0|(b=b+1|0)))break}}else{if(!o)break t;for(t=0;Qf(r,v[v[u+20>>2]+(t<<2)>>2]),(0|o)!=(0|(t=t+1|0)););}if(v[c+8>>2]=u,Un(r+104|0,c+8|0),!o)break i;for(t=0;kf(v[v[u+20>>2]+(t<<2)>>2]+16|0),(0|o)!=(0|(t=t+1|0)););if(!o)break i;for(u=v[u+20>>2],t=0;i[v[u+(t<<2)>>2]+116|0]=1,(0|o)!=(0|(t=t+1|0)););break i}i[u+44|0]=0;break i}v[c+8>>2]=u,Un(r+104|0,c+8|0)}if(V=c+16|0,a=a+1|0,d)break e}if(t=0,!f)break n;for(;;){if(u=v[v[r+100>>2]+(t<<2)>>2],v[v[u+4>>2]+16>>2]!=(0|a)){if((0|f)!=(0|(t=t+1|0)))continue;break n}break}V=c=V-16|0;f:{i:{t:if(l[v[v[u+24>>2]+8>>2]+117|0]){u:{if(l[v[u+4>>2]+20|0]){if(!(t=v[r+136>>2]))break t;if(!(o=v[t+56>>2])){i[u+140|0]=0;break f}if(b=v[u+4>>2],d=v[t- -64>>2],(0|b)!=v[d>>2]){for(t=0;(0|o)!=(0|(t=t+1|0))&(0|b)!=v[d+(t<<2)>>2];);if(t=t>>>0<o>>>0,i[u+140|0]=t,t)break u;break f}}i[u+140|0]=1}if(b=v[u+24>>2],d=v[v[b+4>>2]+4>>2],o=v[b+8>>2],(t=v[r+136>>2])?(Yr(r,t,d,o),k=v[r+136>>2]):k=0,t=v[r+4>>2],!(p=v[t+64>>2])|(0|k)==(0|p)||(Yr(r,p,d,o),t=v[r+4>>2]),k=v[t+52>>2])for(t=0;Yr(r,v[v[v[r+4>>2]+60>>2]+(t<<2)>>2],d,o),(0|k)!=(0|(t=t+1|0)););if((t=v[b+60>>2])&&Si(0|Ta[v[v[t>>2]+8>>2]](t),21212)&&Mn(r,t,o),!(o=v[u+12>>2])){v[c+12>>2]=u,Un(r+104|0,c+12|0);break f}for(t=0;Qf(r,v[v[u+20>>2]+(t<<2)>>2]),(0|o)!=(0|(t=t+1|0)););break i}i[u+140|0]=0;break f}if(v[c+12>>2]=u,Un(r+104|0,c+12|0),o){for(t=0;kf(v[v[u+20>>2]+(t<<2)>>2]+16|0),(0|o)!=(0|(t=t+1|0)););if(o)for(u=v[u+20>>2],t=0;i[v[u+(t<<2)>>2]+116|0]=1,(0|o)!=(0|(t=t+1|0)););}}V=c+16|0,a=a+1|0}if(h>>>0>a>>>0)continue;break r}if(!(h>>>0>(a=a+1|0)>>>0))break}if(h=v[r+12>>2])for(a=0;Qf(r,v[v[r+20>>2]+(a<<2)>>2]),(0|h)!=(0|(a=a+1|0)););}function er(r,n,e,f,i,t){var u,o,a,c=0,b=0,k=0,l=0,d=w(0),y=0,F=w(0),A=w(0),T=0,$=0,I=0,C=w(0),P=0,E=w(0),O=0,R=0,S=w(0),W=0,G=0,U=w(0),j=0,H=w(0),L=w(0),M=w(0),_=w(0),z=w(0),x=w(0),J=0,K=w(0),B=w(0),N=w(0),q=0,D=0,Z=0,Y=w(0),X=w(0),Q=w(0),rr=w(0),nr=w(0),er=w(0),fr=w(0),ir=w(0),tr=w(0),ur=w(0),or=0,ar=w(0),cr=w(0),br=w(0),kr=0;V=u=V-16|0,o=v[r+228>>2],a=v[o+4>>2],v[r+196>>2]=0,v[r+164>>2]=0,v[r+180>>2]=0;r:if(f)for(q=r+192|0,D=r+176|0,Z=r+160|0,O=r+144|0;;){n:{if(a)for(k=m(h[(c=(G<<1)+e|0)>>1],t)<<2,Y=p[k+i>>2],l=m(h[c+4>>1],t)<<2,X=p[l+i>>2],c=m(h[c+2>>1],t)<<2,Q=p[c+i>>2],rr=p[(y=k+4|0)+i>>2],nr=p[(T=l+4|0)+i>>2],er=p[(b=c+4|0)+i>>2],K=p[n+b>>2],U=p[n+T>>2],fr=w(K-U),B=p[n+k>>2],H=p[n+l>>2],ir=w(B-H),N=p[n+c>>2],tr=w(H-N),x=p[n+y>>2],ur=w(w(1)/w(w(fr*ir)+w(tr*w(x-U)))),br=w(U-x),J=0;;){$=v[r+164>>2],j=v[v[o+12>>2]+(J<<2)>>2],I=0,V=b=V-48|0,p[b+40>>2]=x,p[b+44>>2]=B,p[b+36>>2]=N,p[b+32>>2]=K,p[b+28>>2]=H,p[b+24>>2]=U,c=r+208|0,l=2&v[j+4>>2],v[(k=l?O:c)+4>>2]=0,Gn(k,y=b+44|0),Gn(k,T=b+40|0),Gn(k,b+36|0),Gn(k,b+32|0),Gn(k,b+28|0),Gn(k,b+24|0),Gn(k,y),Gn(k,T),v[(c=l?c:O)+4>>2]=0,kr=v[j+4>>2]-4|0,y=0;e:{f:{for(;;){if(P=v[j+12>>2],F=p[(R=P+(l=y<<2)|0)>>2],p[b+20>>2]=F,d=p[P+(4|l)>>2],p[b+16>>2]=d,or=v[k+4>>2]-2|0){for(l=k,L=p[P+((T=y+2|0)<<2)>>2],ar=w(F-L),F=p[R+12>>2],cr=w(-w(d-F)),k=0;;){P=v[l+12>>2],d=p[P+(4|(R=k<<2))>>2],A=p[(R=P+R|0)>>2],E=p[P+((k=k+2|0)<<2)>>2],p[b+12>>2]=E,C=p[R+12>>2],p[b+8>>2]=C,S=w(w(ar*w(C-F))+w(w(E-L)*cr));i:if(w(w(ar*w(d-F))+w(w(A-L)*cr))>w(0)){if(S>w(0)){Gn(c,b+12|0),Gn(c,b+8|0);break i}if(S=w(C-d),C=p[b+20>>2],M=w(L-C),_=p[b+16>>2],E=w(E-A),z=w(w(S*M)-w(w(F-_)*E)),w(g(z))>w(9.999999974752427e-7)){d=w(w(w(E*w(_-d))-w(w(C-A)*S))/z),p[b+4>>2]=w(M*d)+C,Gn(c,I=b+4|0),A=p[b+16>>2],p[b+4>>2]=w(w(F-A)*d)+A,Gn(c,I),I=1;break i}Gn(c,b+20|0),Gn(c,b+16|0),I=1}else I=1,S>w(0)&&(S=w(C-d),C=p[b+20>>2],M=w(L-C),_=p[b+16>>2],E=w(E-A),z=w(w(S*M)-w(w(F-_)*E)),w(g(z))>w(9.999999974752427e-7)?(d=w(w(w(E*w(_-d))-w(w(C-A)*S))/z),p[b+4>>2]=w(M*d)+C,Gn(c,P=b+4|0),A=p[b+16>>2],p[b+4>>2]=w(w(F-A)*d)+A,Gn(c,P)):(Gn(c,b+20|0),Gn(c,b+16|0)),Gn(c,b+12|0),Gn(c,b+8|0));if(!(k>>>0<or>>>0))break}if(v[c+4>>2]){if(Gn(c,v[c+12>>2]),Gn(c,v[c+12>>2]+4|0),(0|y)==(0|kr))break f;v[l+4>>2]=0,y=T,k=c,c=l;continue}}break}v[O+4>>2]=0,I=1;break e}if((0|c)==(0|O))c=v[O+4>>2],v[b+20>>2]=0,wn(O,c-2|0,b+20|0);else{if(k=0,v[O+4>>2]=0,!(l=v[c+4>>2]-2|0))break e;for(;Gn(O,v[c+12>>2]+(k<<2)|0),(0|l)!=(0|(k=k+1|0)););}}if(V=b+48|0,!I)break n;if(k=v[r+148>>2]){for(c=0,v[u+12>>2]=0,wn(Z,l=(-2&k)+$|0,y=u+12|0),v[u+12>>2]=0,wn(q,l,y),l=v[r+204>>2],y=v[r+172>>2],T=v[r+156>>2];F=p[(I=c<<2)+T>>2],j=4+(b=$<<2)|0,d=p[T+(4|I)>>2],p[j+y>>2]=d,p[b+y>>2]=F,A=w(F-H),d=w(d-U),F=w(ur*w(w(fr*A)+w(tr*d))),d=w(ur*w(w(br*A)+w(ir*d))),A=w(w(w(1)-F)-d),p[l+j>>2]=w(nr*A)+w(w(rr*F)+w(er*d)),p[b+l>>2]=w(X*A)+w(w(Y*F)+w(Q*d)),$=$+2|0,k>>>0>(c=c+2|0)>>>0;);if($=v[r+180>>2],s[u+12>>1]=0,Fn(D,(m(k=k>>>1|0,3)+$|0)-6|0,u+12|0),(y=k-1|0)>>>0>=2)for(T=v[r+188>>2],c=1;s[(l=T+($<<1)|0)>>1]=W,b=c+W|0,s[l+2>>1]=b,s[l+4>>1]=b+1,$=$+3|0,(0|y)!=(0|(c=c+1|0)););W=k+W|0}if((0|a)==(0|(J=J+1|0)))break}if((G=G+3|0)>>>0<f>>>0)continue;break r}if(v[u+12>>2]=0,wn(Z,c=$+6|0,l=u+12|0),v[u+12>>2]=0,wn(q,c,l),k=v[r+172>>2],p[k+(y=20+(c=$<<2)|0)>>2]=U,p[(T=c+16|0)+k>>2]=H,p[(b=c+12|0)+k>>2]=K,p[($=c+8|0)+k>>2]=N,p[(I=c+4|0)+k>>2]=x,p[c+k>>2]=B,k=v[r+204>>2],p[k+y>>2]=nr,p[k+T>>2]=X,p[b+k>>2]=er,p[k+$>>2]=Q,p[k+I>>2]=rr,p[c+k>>2]=Y,c=v[r+180>>2],s[u+12>>1]=0,Fn(D,c+3|0,l),c=v[r+188>>2]+(c<<1)|0,s[c>>1]=W,s[c+4>>1]=W+2,s[c+2>>1]=W+1,W=W+3|0,!((G=G+3|0)>>>0<f>>>0))break}V=u+16|0}function fr(r,n){var e,f=0,i=0,o=0,a=0,c=0,s=0,l=0,h=0,d=0,p=0,F=0,T=0,$=0,I=0,C=0,P=0,E=0,O=0,R=0,S=0,W=0,G=0;V=e=V-16|0,b(r);r:if((o=2147483647&(P=t(2)))>>>0<=1305022426){if(C=(h=+r)+-1.5707963109016418*(f=.6366197723675814*h+6755399441055744-6755399441055744)+-1.5893254773528196e-8*f,y[n>>3]=C,a=C<-.7853981852531433,o=g(f)<2147483648?~~f:-2147483648,a){f+=-1,y[n>>3]=h+-1.5707963109016418*f+-1.5893254773528196e-8*f,o=o-1|0;break r}if(!(C>.7853981852531433))break r;f+=1,y[n>>3]=h+-1.5707963109016418*f+-1.5893254773528196e-8*f,o=o+1|0}else if(o>>>0>=2139095040)y[n>>3]=w(r-r),o=0;else{if(i=o,o=(o>>>23|0)-150|0,y[e+8>>3]=(u(2,i-(o<<23)|0),k()),E=e+8|0,V=c=V-560|0,l=o+m($=(0|(i=(o-3|0)/24|0))>0?i:0,-24)|0,(0|(p=v[4408]))>=0)for(o=p+1|0,i=$;y[(c+320|0)+(a<<3)>>3]=(0|i)<0?0:+v[17648+(i<<2)>>2],i=i+1|0,(0|o)!=(0|(a=a+1|0)););for(F=l-24|0,o=0,a=(0|p)>0?p:0;;){for(i=0,f=0;f=y[(i<<3)+E>>3]*y[(c+320|0)+(o-i<<3)>>3]+f,1!=(0|(i=i+1|0)););if(y[(o<<3)+c>>3]=f,i=(0|o)==(0|a),o=o+1|0,i)break}W=47-l|0,O=48-l|0,R=(0|l)<25,G=l-25|0,o=p;n:{for(;;){if(f=y[(o<<3)+c>>3],i=0,a=o,!(d=(0|o)<=0))for(;T=(c+480|0)+(i<<2)|0,s=g(h=5.960464477539063e-8*f)<2147483648?~~h:-2147483648,s=g(f=-16777216*(h=+(0|s))+f)<2147483648?~~f:-2147483648,v[T>>2]=s,f=y[((a=a-1|0)<<3)+c>>3]+h,(0|(i=i+1|0))!=(0|o););f=Hn(f,F),f+=-8*A(.125*f),f-=+(0|(T=g(f)<2147483648?~~f:-2147483648));e:{f:{i:{if(R){if(F)break i;s=v[476+((o<<2)+c|0)>>2]>>23}else I=i=(o<<2)+c|0,i=(s=v[i+476>>2])-((a=s>>O)<<O)|0,v[I+476>>2]=i,T=a+T|0,s=i>>W;if((0|s)<=0)break e;break f}if(s=2,!(f>=.5)){s=0;break e}}if(i=0,a=0,!d)for(;S=v[(I=(c+480|0)+(i<<2)|0)>>2],d=16777215,a||(d=16777216,S)?(v[I>>2]=d-S,a=1):a=0,(0|(i=i+1|0))!=(0|o););f:if(!R){i=8388607;i:switch(0|G){case 1:i=4194303;break;case 0:break i;default:break f}v[476+(d=(o<<2)+c|0)>>2]=v[d+476>>2]&i}T=T+1|0,2==(0|s)&&(f=1-f,s=2,a&&(f-=Hn(1,F)))}if(0!=f)break;if(i=1,d=0,a=o,!((0|o)<=(0|p))){for(;d=v[(c+480|0)+((a=a-1|0)<<2)>>2]|d,(0|a)>(0|p););if(d){for(l=F;l=l-24|0,!v[(c+480|0)+((o=o-1|0)<<2)>>2];);break n}}for(;a=i,i=i+1|0,!v[(c+480|0)+(p-a<<2)>>2];);for(a=o+a|0;;){for(y[(c+320|0)+((o=o+1|0)<<3)>>3]=v[17648+(o+$<<2)>>2],i=0,f=0;f=y[(i<<3)+E>>3]*y[(c+320|0)+(o-i<<3)>>3]+f,1!=(0|(i=i+1|0)););if(y[(o<<3)+c>>3]=f,!((0|o)<(0|a)))break}o=a}(f=Hn(f,24-l|0))>=16777216?(F=(c+480|0)+(o<<2)|0,i=g(h=5.960464477539063e-8*f)<2147483648?~~h:-2147483648,a=g(f=-16777216*+(0|i)+f)<2147483648?~~f:-2147483648,v[F>>2]=a,o=o+1|0):(i=g(f)<2147483648?~~f:-2147483648,l=F),v[(c+480|0)+(o<<2)>>2]=i}if(f=Hn(1,l),(0|o)>=0){for(a=o;y[((i=a)<<3)+c>>3]=f*+v[(c+480|0)+(i<<2)>>2],a=i-1|0,f*=5.960464477539063e-8,i;);for(a=o;;){if(f=0,i=0,(0|(F=(0|(l=o-a|0))>(0|p)?p:l))>=0)for(;f=y[20416+(i<<3)>>3]*y[(i+a<<3)+c>>3]+f,$=(0|i)!=(0|F),i=i+1|0,$;);if(y[(c+160|0)+(l<<3)>>3]=f,i=(0|a)>0,a=a-1|0,!i)break}}if(f=0,(0|o)>=0)for(;a=o,o=o-1|0,f+=y[(c+160|0)+(a<<3)>>3],a;);y[e>>3]=s?-f:f,V=c+560|0,o=7&T,f=y[e>>3],(0|P)<0?(y[n>>3]=-f,o=0-o|0):y[n>>3]=f}return V=e+16|0,o}function ir(r,n){r|=0,n|=0;var e,f=0,t=0,u=0,o=0,a=0,c=0,b=0,k=0,s=w(0),h=0,d=0,y=0,m=w(0),g=0,F=0,A=w(0),T=0,$=0,I=w(0);if(V=e=V-16|0,l[r+88|0]){if(i[r+88|0]=0,t=v[r+80>>2])for(;f=v[t+12>>2],Ta[v[v[t>>2]+4>>2]](t),t=f,f;);if(v[r+80>>2]=0,v[r+84>>2]=0,g=v[r+44>>2]){for(;;){if(f=v[v[r+52>>2]+(d<<2)>>2]){for(;t=f,f=v[f+24>>2];);for(;;){if(f=v[t+28>>2],3!=v[t+112>>2]||!f){b=0,V=a=V-16|0,y=v[t+16>>2],c=v[y+8>>2],f=v[t+28>>2],v[a+12>>2]=0,gn(t+116|0,c,o=a+12|0),v[a+12>>2]=0,$n(t+132|0,c,o);r:if(!f|!l[f+37|0]){if(c)for(T=r+76|0;;){o=v[(k=b<<2)+v[y+16>>2]>>2],$=0|Ta[v[v[o>>2]+16>>2]](o),v[a+12>>2]=$;n:{if(u=v[r+80>>2])for(;;){if(v[u+4>>2]==(0|$)){u=0;break n}if(!(u=v[u+12>>2]))break}if(u=1,i[a+11|0]=1,_n(T,a+12|0,a+11|0),f&&!Fu(0|Ta[v[v[o>>2]+8>>2]](o),20988)&&!Fu(0|Ta[v[v[o>>2]+8>>2]](o),21096)&&!Fu(0|Ta[v[v[o>>2]+8>>2]](o),21108)&&(o=f,Pf(v[f+16>>2],v[a+12>>2]))){for(;;){if(u=2,!(o=v[o+28>>2]))break n;if(!Pf(v[o+16>>2],v[a+12>>2]))break}if(p[t+100>>2]>w(0)){if(v[k+v[t+128>>2]>>2]=3,v[k+v[t+144>>2]>>2]=t,c>>>0>(b=b+1|0)>>>0)continue;break r}}}if(v[k+v[t+128>>2]>>2]=u,!(c>>>0>(b=b+1|0)>>>0))break}}else{if(!c)break r;for(o=r+76|0,f=0;;){u=v[(b=f<<2)+v[y+16>>2]>>2],k=0|Ta[v[v[u>>2]+16>>2]](u),v[a+12>>2]=k;n:{if(u=v[r+80>>2])for(;;){if((0|k)==v[u+4>>2])break n;if(!(u=v[u+12>>2]))break}i[a+11|0]=1,_n(o,a+12|0,a+11|0)}if(v[b+v[t+128>>2]>>2]=2,(0|c)==(0|(f=f+1|0)))break}}V=a+16|0,f=v[t+28>>2]}if(!(t=f))break}}if((0|g)==(0|(d=d+1|0)))break}if(t=v[r+80>>2])for(;f=v[t+12>>2],Ta[v[v[t>>2]+4>>2]](t),t=f,f;);if(v[r+80>>2]=0,v[r+84>>2]=0,!((0|(f=v[r+44>>2]))<=0))for(;;){if(o=f,f=f-1|0,t=v[v[r+52>>2]+(f<<2)>>2])for(;;){if(u=0,V=a=V-16|0,b=v[t+16>>2],d=v[b+8>>2])for(y=r+76|0;;){c=v[(k=u<<2)+v[b+16>>2]>>2];r:if(Fu(0|Ta[v[v[c>>2]+8>>2]](c),20988)){g=v[k+v[b+16>>2]>>2],T=v[g+4>>2];n:{if(c=v[r+80>>2])for(;;){if((0|T)==v[c+4>>2])break n;if(!(c=v[c+12>>2]))break}v[a+12>>2]=v[g+4>>2],i[a+11|0]=1,_n(y,a+12|0,a+11|0);break r}c=k+v[t+128>>2]|0,v[c>>2]=4|v[c>>2]}if((0|d)==(0|(u=u+1|0)))break}if(V=a+16|0,!(t=v[t+24>>2]))break}if(!((0|o)>1))break}}}if(d=v[r+44>>2])for(a=r+56|0;;){if(!(!(f=v[v[r+52>>2]+(F<<2)>>2])|p[f+68>>2]>w(0))){t=F?v[f+112>>2]:1,m=p[f+92>>2],v[f+24>>2]?m=w(m*dr(r,f,n,t)):p[f+72>>2]>=p[f+84>>2]&&(m=v[f+20>>2]?m:w(0)),s=p[f+52>>2],I=p[f+60>>2];r:if(l[f+36|0]){if((A=w(p[f+56>>2]-s))==w(0))break r;s=w(s+Er(p[f+72>>2],A))}else s=(s=w(s+p[f+72>>2]))<(A=p[f+56>>2])?s:A;c=v[f+16>>2],o=v[c+8>>2];r:if(!(!F&m==w(1))&3!=(0|t)){if(b=f+148|0,(y=v[f+152>>2])||(v[e+12>>2]=0,wn(b,o<<1,e+12|0)),o)for(h=0;k=3&v[(u=h<<2)+v[f+128>>2]>>2]?0:t,u=v[u+v[c+16>>2]>>2],Fu(0|Ta[v[v[u>>2]+8>>2]](u),21308)?pr(u,n,s,m,k,b,h<<1,!y):Ta[v[v[u>>2]+12>>2]](u,n,I,s,a,m,k,0),(0|o)!=(0|(h=h+1|0)););}else{if(!o)break r;for(h=0;u=v[v[c+16>>2]+(h<<2)>>2],Ta[v[v[u>>2]+12>>2]](u,n,I,s,a,m,t,0),(0|o)!=(0|(h=h+1|0)););}Br(r,f,s),v[r+60>>2]=0,p[f+64>>2]=s,p[f+80>>2]=p[f+72>>2],h=1}if((0|d)==(0|(F=F+1|0)))break}return mr(v[r+72>>2]),V=e+16|0,1&h}function tr(r,n,e,f,t){var u,o,a=0,c=0,b=0,k=0,s=0,h=0,d=0,y=0,m=0,g=0,F=0,A=w(0),T=w(0),$=0,I=0,C=0,P=0,E=0;if(V=u=V+-64|0,v[u+60>>2]=n,(0|(o=ee(f)))>0&&(d=47!=(0|(c=l[(f+o|0)-1|0]))&92!=(0|c)),v[u+56>>2]=0,ue(u+60|0,c=n+e|0,u+48|0))for($=r+4|0,I=r+20|0,C=1+(o+d|0)|0,m=u+44|0,g=u+36|0,s=12|(n=u+16|0),h=4|n,F=u+52|0;;){if((0|(a=v[u+52>>2]))!=(0|(e=v[u+48>>2])))if(v[u+56>>2])zi((n=ut(112))+4|0,0,108),v[n>>2]=9016,Tt(n+8|0),v[n+108>>2]=0,v[n+100>>2]=0,v[n+104>>2]=0,v[n+96>>2]=8744,v[n+92>>2]=0,v[n+84>>2]=0,v[n+88>>2]=0,v[n+80>>2]=8744,v[u>>2]=n,v[n+4>>2]=v[u+56>>2],b=v[5280],n=a-e|0,e=Ri(0|Ta[v[v[b>>2]+12>>2]](b,n+1|0,8590,308),e,n),i[n+e|0]=0,n=ht(u+4|0,e,1),te(v[u>>2]+8|0,n),gi(n),xf(u+60|0,c,u+48|0),n=90,Ie(5008,e=v[u+48>>2],a=v[u+52>>2]-e|0)&&(n=0,Ie(5185,e,a)&&(n=$u(e,F))),e=v[u>>2],v[e+76>>2]=n,i[e+72|0]=90==(0|n),sn(u+60|0,c,u+16|0),n=$u(v[u+16>>2],h),v[v[u>>2]+20>>2]=n,n=$u(v[u+24>>2],s),v[v[u>>2]+24>>2]=n,sn(u+60|0,c,u+16|0),n=$u(v[u+16>>2],h),v[v[u>>2]+28>>2]=n,e=$u(v[u+24>>2],s),n=v[u>>2],v[n+32>>2]=e,a=v[n+20>>2],k=v[u+56>>2],A=w(v[k+60>>2]),p[n+36>>2]=w(0|a)/A,b=v[n+24>>2],T=w(v[k+64>>2]),p[n+40>>2]=w(0|b)/T,l[n+72|0]?(b=b+v[n+28>>2]|0,e=e+a|0):(b=e+b|0,e=a+v[n+28>>2]|0),p[n+48>>2]=w(0|b)/T,p[n+44>>2]=w(0|e)/A,4==(0|sn(u+60|0,c,u+16|0))&&(v[u+4>>2]=0,gn(n+80|0,4,u+4|0),n=$u(v[u+16>>2],h),v[v[v[u>>2]+92>>2]>>2]=n,n=$u(v[u+24>>2],s),v[v[v[u>>2]+92>>2]+4>>2]=n,n=$u(v[u+32>>2],g),v[v[v[u>>2]+92>>2]+8>>2]=n,n=$u(v[u+40>>2],m),e=v[u>>2],v[v[e+92>>2]+12>>2]=n,4==(0|sn(u+60|0,c,u+16|0))&&(v[u+4>>2]=0,gn(e+96|0,4,u+4|0),n=$u(v[u+16>>2],h),v[v[v[u>>2]+108>>2]>>2]=n,n=$u(v[u+24>>2],s),v[v[v[u>>2]+108>>2]+4>>2]=n,n=$u(v[u+32>>2],g),v[v[v[u>>2]+108>>2]+8>>2]=n,n=$u(v[u+40>>2],m),v[v[v[u>>2]+108>>2]+12>>2]=n,sn(u+60|0,c,u+16|0))),n=$u(v[u+16>>2],h),v[v[u>>2]+60>>2]=n,n=$u(v[u+24>>2],s),v[v[u>>2]+64>>2]=n,sn(n=u+60|0,c,u+16|0),e=$u(v[u+16>>2],h),p[v[u>>2]+52>>2]=0|e,e=$u(v[u+24>>2],s),p[v[u>>2]+56>>2]=0|e,xf(n,c,u+48|0),n=$u(v[u+48>>2],F),v[v[u>>2]+68>>2]=n,Un(I,u);else{for(n=v[5280],a=a-e|0,n=Ri(0|Ta[v[v[n>>2]+12>>2]](n,a+1|0,8590,308),e,a),i[n+a|0]=0,e=ee(n),a=v[5280],b=Ri(0|Ta[v[v[a>>2]+12>>2]](a,e+C|0,8590,116),f,o),d&&(i[b+o|0]=47),yn((b+o|0)+d|0,n),P=u,E=Ze(e=ut(68),n=ht(u+4|0,n,1)),v[P+56>>2]=E,gi(n),sn(u+60|0,c,u+16|0),n=$u(v[u+16>>2],h),v[v[u+56>>2]+60>>2]=n,n=$u(v[u+24>>2],s),a=v[u+56>>2],v[a+64>>2]=n,sn(u+60|0,c,u+16|0),e=v[u+16>>2],k=v[u+20>>2]-e|0,n=8;;){if(n){if(Ie(v[8912+((n=n-1|0)<<2)>>2],e,k))continue}else n=0;break}for(v[a+40>>2]=n,sn(u+60|0,c,u+16|0),e=v[u+16>>2],k=v[u+20>>2]-e|0,n=8;;){if(n){if(Ie(v[8944+((n=n-1|0)<<2)>>2],e,k))continue}else n=0;break}for(v[a+44>>2]=n,e=v[u+24>>2],k=v[u+28>>2]-e|0,n=8;;){if(n){if(Ie(v[8944+((n=n-1|0)<<2)>>2],e,k))continue}else n=0;break}v[a+48>>2]=n,xf(u+60|0,c,u+48|0),v[a+52>>2]=1,v[a+56>>2]=1;r:if(Ie(5338,k=v[u+48>>2],y=v[u+52>>2]-k|0)){e=a+56|0,n=a+52|0;n:{e:{if(1==(0|y))switch(l[0|k]-120|0){case 0:break n;case 1:break e;default:break r}if(Ie(1056,k,y))break r;v[a+52>>2]=2}n=e}v[n>>2]=2}t?((n=v[r+36>>2])&&(e=ht(u+4|0,b,0),Ta[v[v[n>>2]+8>>2]](n,a,e),gi(e)),n=v[5280],Ta[v[v[n>>2]+20>>2]](n,b,8590,156)):(n=ht(u+4|0,b,1),te(v[u+56>>2]+28|0,n),gi(n)),Un($,u+56|0)}else v[u+56>>2]=0;if(!ue(u+60|0,c,u+48|0))break}V=u- -64|0}function ur(r,n){var e=0,f=0,t=0,u=0,o=0,a=0,c=0,b=0;r:{n:{e:{f:{i:{if((0|(e=i[0|n]))<=90)switch(e-34|0){case 11:case 14:case 15:case 16:case 17:case 18:case 19:case 20:case 21:case 22:case 23:break n;case 0:break i;default:break r}if((0|e)<=109){if(91==(0|e))break f;if(102!=(0|e))break r;if(Ie(n+1|0,5186,4))break r;return v[r+8>>2]=0,n+5|0}if(123==(0|e))break e;if(116!=(0|e)){if(110!=(0|e))break r;if(Ie(n+1|0,4536,3))break r;return v[r+8>>2]=2,n+4|0}if(Ie(n+1|0,5009,3))break r;return v[r+20>>2]=1,v[r+8>>2]=1,n+4|0}return wr(r,n)}for(t=r,v[r+8>>2]=5,n=n+1|0;n=(r=n)+1|0,((e=l[0|r])-1&255)>>>0<32;);f:{i:{if(93!=(0|e)){for(Le(e=Wt(32),0),v[t+4>>2]=e;n=r,r=r+1|0,(l[0|n]-1&255)>>>0<32;);if(!(n=ur(e,n)))break i;for(;n=(r=n)+1|0,(l[0|r]-1&255)>>>0<32;);for(n=1;;){t:{if(v[t+12>>2]=n,44!=(0|(n=l[0|r]))){if(93!=(0|n))break t;n=r+1|0;break f}for(Le(f=Wt(32),0),v[e>>2]=f;n=l[r+1|0],r=r+1|0,(n-1&255)>>>0<32;);if(!(n=ur(f,r)))break i;for(;n=(r=n)+1|0,(l[0|r]-1&255)>>>0<32;);n=v[t+12>>2]+1|0,e=f;continue}break}v[5290]=r,n=0}break f}n=0}return n}for(o=r,v[r+8>>2]=6,e=n+1|0;e=(r=e)+1|0,((n=l[0|r])-1&255)>>>0<32;);e:{f:{i:if(125!=(0|n)){for(Le(f=Wt(32),0),v[o+4>>2]=f;n=r,r=r+1|0,(l[0|n]-1&255)>>>0<32;);if(r=0,!(e=wr(f,n)))break e;for(;e=(r=e)+1|0,(l[0|r]-1&255)>>>0<32;);if(v[f+28>>2]=v[f+16>>2],e=0,v[f+16>>2]=0,58!=l[0|r])break f;for(;n=l[r+1|0],r=r+1|0,(n-1&255)>>>0<32;);if(n=ur(f,r)){for(;n=(r=n)+1|0,(l[0|r]-1&255)>>>0<32;);for(e=1;;){t:{if(v[o+12>>2]=e,44!=(0|(n=l[0|r]))){if(125!=(0|n))break t;r=r+1|0;break e}for(Le(t=Wt(32),0),v[f>>2]=t;n=l[r+1|0],r=r+1|0,(n-1&255)>>>0<32;);if(e=0,!(n=wr(t,r)))break i;for(;n=(r=n)+1|0,(l[0|r]-1&255)>>>0<32;);if(v[t+28>>2]=v[t+16>>2],v[t+16>>2]=0,58!=l[0|r]){v[5290]=r,r=0;break e}for(;n=l[r+1|0],r=r+1|0,(n-1&255)>>>0<32;);if(!(n=ur(t,r)))break i;for(;n=(r=n)+1|0,(l[0|r]-1&255)>>>0<32;);e=v[o+12>>2]+1|0,f=t;continue}break}e=0,v[5290]=r}}r=e;break e}v[5290]=r,r=0}return r}if(f=r,t=n,a=l[0|n],((n=l[0|(e=(45==(0|a))+n|0)])-48&255)>>>0>9)r=e;else for(;u=10*u+ +((255&n)-48|0),n=l[e+1|0],e=r=e+1|0,(n-48&255)>>>0<10;);if(46==(255&n)){if(n=r+1|0,((e=l[r+1|0])-48&255)>>>0>9)r=n;else for(;c=10*c+ +((255&e)-48|0),o=o+1|0,e=l[n+1|0],n=r=n+1|0,(e-48&255)>>>0<10;);u+=c/(+(Tf(o)>>>0)+4294967296*+(Z>>>0)),n=l[0|r]}if(b=c=45==(0|a)?-u:u,101==(255&n|32)){if(n=(e=45==(0|(a=l[r+1|0]))|43==(0|a))?r+2|0:r+1|0,o=0,((e=l[(e?2:1)+r|0])-48&255)>>>0>9)r=n;else for(;o=(m(o,10)+(255&e)|0)-48|0,e=l[n+1|0],n=r=n+1|0,(e-48&255)>>>0<10;);b=c/(u=+(Tf(o)>>>0)+4294967296*+(Z>>>0)),45!=(0|a)&&(b=c*u)}return u=b,(0|r)==(0|t)?(v[5290]=t,r=0):(v[f+8>>2]=3,n=g(u)<2147483648?~~u:-2147483648,v[f+20>>2]=n,p[f+24>>2]=u),r}return v[5290]=n,0}function or(r,n,e,f,i,t,u,o){r|=0,n|=0,e=w(e),f=w(f),i|=0,t|=0,u=w(u),o=w(o);var a,c,b,k,s=w(0),h=w(0),d=w(0),y=w(0),m=w(0),F=w(0),A=0,$=w(0),I=w(0),C=w(0),P=w(0),E=w(0),O=w(0),R=0,S=w(0),W=w(0),G=w(0),U=w(0),j=w(0),H=w(0);if(A=v[r+12>>2],o!=w(0)){if(l[r+88|0]||Tr(r),l[n+88|0]||Tr(n),P=p[n+60>>2],G=p[r+64>>2],U=p[r+60>>2],h=p[n+72>>2],$=p[A+96>>2],y=p[A+104>>2],I=p[A+92>>2],C=p[v[n+4>>2]+24>>2],d=p[A+108>>2],R=(O=p[r+72>>2])<w(0),a=(s=p[r+76>>2])<w(0),F=R?w(-O):O,j=a?w(-s):s,(S=(s=w(F-j))<w(0)?w(-s):s)<=w(9999999747378752e-20)?(W=p[n+64>>2],s=w(p[r+112>>2]+w(w(p[r+104>>2]*P)+w(W*p[r+108>>2]))),m=w(p[r+100>>2]+w(w(p[r+92>>2]*P)+w(W*p[r+96>>2])))):(s=w(w(p[r+104>>2]*P)+p[r+112>>2]),m=w(w(p[r+92>>2]*P)+p[r+100>>2])),E=w(m-p[A+100>>2]),s=w(s-p[A+112>>2]),m=w(w(1)/w(w(I*d)-w(y*$))),H=w(w(w(w(d*E)-w($*s))*m)-U),s=w(w(w(w(s*I)-w(y*E))*m)-G),+(s=w(T(w(w(H*H)+w(s*s)))))<1e-4)return Ar(r,e,f,0,t,0,o),void br(n,P,W,w(0),p[n+72>>2],p[n+76>>2],p[n+80>>2],p[n+84>>2]);k=0-(c=R?-1:1)|0,E=(b=h<w(0))?w(-h):h,C=w(E*C),h=w(e-p[A+100>>2]),f=w(f-p[A+112>>2]),e=w(w(w(w(h*d)-w($*f))*m)-U),h=w(w(w(w(f*I)-w(y*h))*m)-G),d=w(w(e*e)+w(h*h)),u!=w(0)&&(f=w(w(w(F*w(E+w(1)))*w(.5))*u),$=w(T(d)),(u=w(f+w(w($-s)-w(C*F))))>w(0)&&(d=f,f=(f=w(u/w(f+f)))>w(1)?w(0):w(f+w(-1)),f=w(w(-w(u-w(d*w(w(1)-w(f*f)))))/$),e=w(w(f*e)+e),h=w(w(f*h)+h),d=w(w(e*e)+w(h*h)))),A=a?k:c,E=w(R?180:0),f=w(F*C),S<=w(9999999747378752e-20)?(u=w(-1),(F=w(w(w(d-w(s*s))-w(f*f))/w(f*w(s+s))))<w(-1)||(u=(R=F>w(1))?w(1):F,!R|!t||(u=w(1),O=w(O*w(w(w(w(w(T(d))/w(f+s))+w(-1))*o)+w(1))))),s=w(w(f*u)+s),d=w(Qr(u)*w(0|i)),f=w(f*Ur(d)),u=Or(w(w(h*s)-w(f*e)),w(w(e*s)+w(h*f)))):($=w(0),S=Or(h,e),C=w(j*C),e=w(C*C),u=w(w(e*w(-2))*s),h=w(f*f),y=w(e-h),I=w(w(w(e*w(s*s))+w(h*d))-w(h*e)),(m=w(w(u*u)+w(w(y*w(-4))*I)))>=w(0)&&(m=w(T(m)),u=w(w(u+(u<w(0)?w(-m):m))*w(-.5)),y=w(u/y),u=w(I/u),y=w(g(y))<w(g(u))?y:u,(u=w(y*y))<=d)?(e=w(w(T(w(d-u)))*w(0|i)),u=w(S-Or(e,y)),d=Or(w(e/j),w(w(y-s)/F))):(y=w(f+s),I=w(y*y),u=w(s-f),F=w(u*u),m=w(3.1415927410125732),!(!((e=w(w(s*w(-f))/w(h-e)))>=w(-1))|!(e<=w(1)))&&(h=Ur(e=Qr(e)),s=w(w(f*Wr(e))+s),h=w(C*h),F>(f=w(w(s*s)+w(h*h)))&&(m=e,F=f,$=h,u=s),f>I)||(e=w(0),s=y,f=I,h=w(0)),t=w(w(F+f)*w(.5))>=d,f=w(0|i),u=w(S-Or(w((t?$:h)*f),t?u:s)),d=w((t?m:e)*f))),e=w(b?180:0),s=u,f=w(0|A),u=w(Or(W,P)*f),(s=w(w(w(w(s-u)*w(57.2957763671875))+E)-p[r+68>>2]))>w(180)?s=w(s+w(-360)):s<w(-180)&&(s=w(s+w(360))),br(r,U,G,w(w(s*o)+p[r+40>>2]),O,p[r+76>>2],w(0),w(0)),s=w(w(d+u)*w(57.2957763671875)),u=p[n+80>>2],f=w(w(w(s-u)*f)+e),e=p[n+68>>2],(s=w(f-e))>w(180)?s=w(s+w(-360)):s<w(-180)&&(s=w(s+w(360))),br(n,P,W,w(w(s*o)+e),p[n+72>>2],p[n+76>>2],u,p[n+84>>2])}else Mi(n)}function ar(r){var n=0,e=0,f=0,i=0,t=0,u=0,o=0,a=0,c=0;r:if(r|=0){t=(f=r-8|0)+(r=-8&(n=v[r-4>>2]))|0;n:if(!(1&n)){if(!(3&n))break r;if((f=f-(n=v[f>>2])|0)>>>0<d[5451])break r;r=r+n|0;e:{f:{if(v[5452]!=(0|f)){if(n>>>0<=255){if(i=n>>>3|0,(0|(n=v[f+12>>2]))==(0|(e=v[f+8>>2]))){a=21788,c=v[5447]&ot(i),v[a>>2]=c;break n}v[e+12>>2]=n,v[n+8>>2]=e;break n}if(u=v[f+24>>2],(0|f)!=(0|(n=v[f+12>>2]))){e=v[f+8>>2],v[e+12>>2]=n,v[n+8>>2]=e;break e}if(!(e=v[(i=f+20|0)>>2])){if(!(e=v[f+16>>2]))break f;i=f+16|0}for(;o=i,(e=v[(i=(n=e)+20|0)>>2])||(i=n+16|0,e=v[n+16>>2]););v[o>>2]=0;break e}if(3&~(n=v[t+4>>2]))break n;return v[5449]=r,v[t+4>>2]=-2&n,v[f+4>>2]=1|r,void(v[t>>2]=r)}n=0}if(u){e=v[f+28>>2];e:{if(v[(i=22092+(e<<2)|0)>>2]==(0|f)){if(v[i>>2]=n,n)break e;a=21792,c=v[5448]&ot(e),v[a>>2]=c;break n}if(v[u+(v[u+16>>2]==(0|f)?16:20)>>2]=n,!n)break n}v[n+24>>2]=u,(e=v[f+16>>2])&&(v[n+16>>2]=e,v[e+24>>2]=n),(e=v[f+20>>2])&&(v[n+20>>2]=e,v[e+24>>2]=n)}}if(!(f>>>0>=t>>>0)&&1&(n=v[t+4>>2])){n:{e:{f:{i:{if(!(2&n)){if(v[5453]==(0|t)){if(v[5453]=f,r=v[5450]+r|0,v[5450]=r,v[f+4>>2]=1|r,v[5452]!=(0|f))break r;return v[5449]=0,void(v[5452]=0)}if(v[5452]==(0|t))return v[5452]=f,r=v[5449]+r|0,v[5449]=r,v[f+4>>2]=1|r,void(v[r+f>>2]=r);if(r=(-8&n)+r|0,n>>>0<=255){if(i=n>>>3|0,(0|(n=v[t+12>>2]))==(0|(e=v[t+8>>2]))){a=21788,c=v[5447]&ot(i),v[a>>2]=c;break e}v[e+12>>2]=n,v[n+8>>2]=e;break e}if(u=v[t+24>>2],(0|t)!=(0|(n=v[t+12>>2]))){e=v[t+8>>2],v[e+12>>2]=n,v[n+8>>2]=e;break f}if(!(e=v[(i=t+20|0)>>2])){if(!(e=v[t+16>>2]))break i;i=t+16|0}for(;o=i,(e=v[(i=(n=e)+20|0)>>2])||(i=n+16|0,e=v[n+16>>2]););v[o>>2]=0;break f}v[t+4>>2]=-2&n,v[f+4>>2]=1|r,v[r+f>>2]=r;break n}n=0}if(u){e=v[t+28>>2];f:{if(v[(i=22092+(e<<2)|0)>>2]==(0|t)){if(v[i>>2]=n,n)break f;a=21792,c=v[5448]&ot(e),v[a>>2]=c;break e}if(v[u+(v[u+16>>2]==(0|t)?16:20)>>2]=n,!n)break e}v[n+24>>2]=u,(e=v[t+16>>2])&&(v[n+16>>2]=e,v[e+24>>2]=n),(e=v[t+20>>2])&&(v[n+20>>2]=e,v[e+24>>2]=n)}}if(v[f+4>>2]=1|r,v[r+f>>2]=r,v[5452]==(0|f))return void(v[5449]=r)}if(r>>>0<=255)return n=21828+(-8&r)|0,(e=v[5447])&(r=1<<(r>>>3))?r=v[n+8>>2]:(v[5447]=r|e,r=n),v[n+8>>2]=f,v[r+12>>2]=f,v[f+12>>2]=n,void(v[f+8>>2]=r);e=31,r>>>0<=16777215&&(e=62+((r>>>38-(n=F(r>>>8|0))&1)-(n<<1)|0)|0),v[f+28>>2]=e,v[f+16>>2]=0,v[f+20>>2]=0,n=22092+(e<<2)|0;n:{e:{if((i=v[5448])&(o=1<<e)){for(e=r<<(31!=(0|e)?25-(e>>>1|0):0),n=v[n>>2];;){if(i=n,(-8&v[n+4>>2])==(0|r))break e;if(n=e>>>29|0,e<<=1,!(n=v[16+(o=i+(4&n)|0)>>2]))break}v[o+16>>2]=f,v[f+24>>2]=i}else v[5448]=i|o,v[n>>2]=f,v[f+24>>2]=n;v[f+12>>2]=f,v[f+8>>2]=f;break n}r=v[i+8>>2],v[r+12>>2]=f,v[i+8>>2]=f,v[f+24>>2]=0,v[f+12>>2]=i,v[f+8>>2]=r}r=v[5455]-1|0,v[5455]=r||-1}}}function cr(r,n){var e,f=0,i=0,t=0,u=0,o=0,a=0,c=0;e=r+n|0;r:{n:if(!(1&(f=v[r+4>>2]))){if(!(3&f))break r;n=(f=v[r>>2])+n|0;e:{f:{i:{if((0|(r=r-f|0))!=v[5452]){if(f>>>0<=255){if((0|(i=v[r+8>>2]))!=(0|(t=v[r+12>>2])))break i;a=21788,c=v[5447]&ot(f>>>3|0),v[a>>2]=c;break n}if(u=v[r+24>>2],(0|(f=v[r+12>>2]))!=(0|r)){i=v[r+8>>2],v[i+12>>2]=f,v[f+8>>2]=i;break e}if(!(i=v[(t=r+20|0)>>2])){if(!(i=v[r+16>>2]))break f;t=r+16|0}for(;o=t,(i=v[(t=(f=i)+20|0)>>2])||(t=f+16|0,i=v[f+16>>2]););v[o>>2]=0;break e}if(3&~(f=v[e+4>>2]))break n;return v[5449]=n,v[e+4>>2]=-2&f,v[r+4>>2]=1|n,void(v[e>>2]=n)}v[i+12>>2]=t,v[t+8>>2]=i;break n}f=0}if(u){i=v[r+28>>2];e:{if(v[(t=22092+(i<<2)|0)>>2]==(0|r)){if(v[t>>2]=f,f)break e;a=21792,c=v[5448]&ot(i),v[a>>2]=c;break n}if(v[u+(v[u+16>>2]==(0|r)?16:20)>>2]=f,!f)break n}v[f+24>>2]=u,(i=v[r+16>>2])&&(v[f+16>>2]=i,v[i+24>>2]=f),(i=v[r+20>>2])&&(v[f+20>>2]=i,v[i+24>>2]=f)}}n:{e:{f:{i:{if(!(2&(f=v[e+4>>2]))){if(v[5453]==(0|e)){if(v[5453]=r,n=v[5450]+n|0,v[5450]=n,v[r+4>>2]=1|n,v[5452]!=(0|r))break r;return v[5449]=0,void(v[5452]=0)}if(v[5452]==(0|e))return v[5452]=r,n=v[5449]+n|0,v[5449]=n,v[r+4>>2]=1|n,void(v[r+n>>2]=n);if(n=(-8&f)+n|0,f>>>0<=255){if(t=f>>>3|0,(0|(f=v[e+12>>2]))==(0|(i=v[e+8>>2]))){a=21788,c=v[5447]&ot(t),v[a>>2]=c;break e}v[i+12>>2]=f,v[f+8>>2]=i;break e}if(u=v[e+24>>2],(0|e)!=(0|(f=v[e+12>>2]))){i=v[e+8>>2],v[i+12>>2]=f,v[f+8>>2]=i;break f}if(!(i=v[(t=e+20|0)>>2])){if(!(i=v[e+16>>2]))break i;t=e+16|0}for(;o=t,(i=v[(t=(f=i)+20|0)>>2])||(t=f+16|0,i=v[f+16>>2]););v[o>>2]=0;break f}v[e+4>>2]=-2&f,v[r+4>>2]=1|n,v[r+n>>2]=n;break n}f=0}if(u){i=v[e+28>>2];f:{if(v[(t=22092+(i<<2)|0)>>2]==(0|e)){if(v[t>>2]=f,f)break f;a=21792,c=v[5448]&ot(i),v[a>>2]=c;break e}if(v[u+(v[u+16>>2]==(0|e)?16:20)>>2]=f,!f)break e}v[f+24>>2]=u,(i=v[e+16>>2])&&(v[f+16>>2]=i,v[i+24>>2]=f),(i=v[e+20>>2])&&(v[f+20>>2]=i,v[i+24>>2]=f)}}if(v[r+4>>2]=1|n,v[r+n>>2]=n,v[5452]==(0|r))return void(v[5449]=n)}if(n>>>0<=255)return f=21828+(-8&n)|0,(i=v[5447])&(n=1<<(n>>>3))?n=v[f+8>>2]:(v[5447]=n|i,n=f),v[f+8>>2]=r,v[n+12>>2]=r,v[r+12>>2]=f,void(v[r+8>>2]=n);i=31,n>>>0<=16777215&&(i=62+((n>>>38-(f=F(n>>>8|0))&1)-(f<<1)|0)|0),v[r+28>>2]=i,v[r+16>>2]=0,v[r+20>>2]=0,f=22092+(i<<2)|0;n:{if((t=v[5448])&(o=1<<i)){for(i=n<<(31!=(0|i)?25-(i>>>1|0):0),f=v[f>>2];;){if(t=f,(-8&v[f+4>>2])==(0|n))break n;if(f=i>>>29|0,i<<=1,!(f=v[16+(o=t+(4&f)|0)>>2]))break}v[o+16>>2]=r,v[r+24>>2]=t}else v[5448]=t|o,v[f>>2]=r,v[r+24>>2]=f;return v[r+12>>2]=r,void(v[r+8>>2]=r)}n=v[t+8>>2],v[n+12>>2]=r,v[t+8>>2]=r,v[r+24>>2]=0,v[r+12>>2]=t,v[r+8>>2]=n}}function br(r,n,e,f,t,u,o,a){r|=0,n=w(n),e=w(e),f=w(f),t=w(t),u=w(u),o=w(o),a=w(a);var c,b=w(0),k=w(0),s=w(0),l=w(0),h=0,d=w(0),y=w(0),m=w(0),F=0,A=w(0);if(i[r+88|0]=1,p[r+84>>2]=a,p[r+80>>2]=o,p[r+76>>2]=u,p[r+72>>2]=t,p[r+68>>2]=f,p[r+64>>2]=e,p[r+60>>2]=n,c=v[r+8>>2],d=p[c+168>>2],y=p[c+164>>2],!(h=v[r+12>>2]))return o=w(w(f+o)*w(.01745329238474369)),F=r,A=w(w(Ur(o)*t)*d),p[F+104>>2]=A,F=r,A=w(w(Wr(o)*t)*y),p[F+92>>2]=A,f=w(w(w(f+w(90))+a)*w(.01745329238474369)),F=r,A=w(w(Ur(f)*u)*d),p[F+108>>2]=A,F=r,A=w(w(Wr(f)*u)*y),p[F+96>>2]=A,p[r+100>>2]=w(n*y)+p[c+172>>2],void(p[r+112>>2]=w(e*d)+p[c+176>>2]);b=p[h+104>>2],s=p[h+108>>2],k=p[h+92>>2],l=p[h+96>>2],p[r+100>>2]=w(w(k*n)+w(l*e))+p[h+100>>2],p[r+112>>2]=w(w(b*n)+w(s*e))+p[h+112>>2];r:{n:{e:{f:{i:{switch(0|(h=v[v[r+4>>2]+56>>2])){case 3:case 4:break n;case 2:break e;case 1:break f;case 0:break i}f=p[r+108>>2],n=p[r+104>>2],u=p[r+96>>2],t=p[r+92>>2];break r}return e=Ur(n=w(w(f+o)*w(.01745329238474369))),n=w(Wr(n)*t),e=w(e*t),p[r+104>>2]=w(b*n)+w(e*s),p[r+92>>2]=w(k*n)+w(e*l),e=Ur(n=w(w(w(f+w(90))+a)*w(.01745329238474369))),n=w(Wr(n)*u),e=w(e*u),p[r+108>>2]=w(b*n)+w(e*s),void(p[r+96>>2]=w(k*n)+w(e*l))}e=w(w(f+o)*w(.01745329238474369)),n=w(Ur(e)*t),t=w(Wr(e)*t),e=w(w(w(f+w(90))+a)*w(.01745329238474369)),f=w(Ur(e)*u),u=w(Wr(e)*u);break r}n=w(f+o),(e=w(w(k*k)+w(b*b)))>w(9999999747378752e-20)?(e=w(w(g(w(w(k*s)-w(b*l))))/e),s=w(k*e),l=w(b*e),e=w(Or(b,k)*w(57.2957763671875))):(b=w(0),k=w(0),e=w(w(Or(s,l)*w(-57.2957763671875))+w(90))),o=Ur(n=w(w(n-e)*w(.01745329238474369))),m=w(Wr(n)*t),t=w(o*t),n=w(w(b*m)+w(s*t)),t=w(w(k*m)-w(t*l)),f=Ur(e=w(w(w(w(f+a)-e)+w(90))*w(.01745329238474369))),e=w(Wr(e)*u),u=w(f*u),f=w(w(b*e)+w(s*u)),u=w(w(k*e)-w(u*l));break r}n=Ur(e=w(f*w(.01745329238474369))),f=Wr(e),m=e=w(w(w(k*f)+w(n*l))/y),n=w(w(w(b*f)+w(n*s))/d),f=(e=w(T(w(w(e*e)+w(n*n)))))>w(9999999747378752e-21)?w(w(1)/e):e,e=w(m*f),n=w(n*f),f=w(T(w(w(e*e)+w(n*n)))),b=3==(0|h)&&y<w(0)^d<w(0)^w(w(k*s)-w(b*l))<w(0)?w(-f):f,f=Ur(k=w(Or(n,e)+w(1.5707963705062866))),s=Ur(a=w(w(a+w(90))*w(.01745329238474369))),a=w(Wr(a)*u),u=w(s*u),s=w(b*f),f=w(w(n*a)+w(u*s)),l=Ur(o=w(o*w(.01745329238474369))),o=w(Wr(o)*t),t=w(l*t),n=w(w(n*o)+w(t*s)),m=w(e*a),a=w(b*Wr(k)),u=w(m+w(u*a)),t=w(w(e*o)+w(t*a))}p[r+108>>2]=d*f,p[r+104>>2]=d*n,p[r+96>>2]=y*u,p[r+92>>2]=y*t}function kr(r,n){var e,f=0,i=0,t=0,u=0,o=0,a=0,c=0;if(V=e=V-16|0,v[r+4>>2]=n,v[r+120>>2]=9260,v[r+116>>2]=0,v[r+108>>2]=0,v[r+112>>2]=0,v[r+104>>2]=10296,v[r+100>>2]=0,v[r+92>>2]=0,v[r+96>>2]=0,v[r+88>>2]=10280,v[r+84>>2]=0,v[r+76>>2]=0,v[r+80>>2]=0,v[r+72>>2]=10264,v[r+68>>2]=0,v[r+60>>2]=0,v[r+64>>2]=0,v[r+56>>2]=10248,v[r+52>>2]=0,v[r+44>>2]=0,v[r+48>>2]=0,v[r+40>>2]=10232,v[r+36>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[r+24>>2]=10232,v[r+20>>2]=0,v[r+12>>2]=0,v[r+16>>2]=0,v[r+8>>2]=9260,v[r>>2]=10216,v[r+132>>2]=0,v[r+136>>2]=0,v[r+124>>2]=0,v[r+128>>2]=0,v[r+140>>2]=9372,v[r+144>>2]=1065353216,v[r+148>>2]=1065353216,v[r+152>>2]=1065353216,v[r+156>>2]=1065353216,vf(r+140|0),v[r+176>>2]=0,v[r+168>>2]=1065353216,v[r+172>>2]=0,v[r+160>>2]=0,v[r+164>>2]=1065353216,u=r+8|0,f=v[r+4>>2],ya(u,v[(n=f)+20>>2]),v[n+20>>2])for(n=0;i=v[v[f+28>>2]+(n<<2)>>2],(t=v[i+20>>2])?(t=v[v[r+20>>2]+(v[t+4>>2]<<2)>>2],a=e,c=me(ut(120),i,r,t),v[a+12>>2]=c,Un(t+16|0,e+12|0)):(a=e,c=me(ut(120),i,r,0),v[a+12>>2]=c),Un(u,e+12|0),(n=n+1|0)>>>0<d[f+20>>2];);if(i=r+24|0,f=v[r+4>>2],ya(i,v[(n=f)+36>>2]),ya(t=r+40|0,v[n+36>>2]),v[n+36>>2])for(n=0;u=v[v[f+44>>2]+(n<<2)>>2],o=v[v[r+20>>2]+(v[v[u+20>>2]+4>>2]<<2)>>2],a=e,c=Cn(ut(84),u,o),v[a+12>>2]=c,Un(i,u=e+12|0),Un(t,u),(n=n+1|0)>>>0<d[f+36>>2];);if(i=r+56|0,f=v[r+4>>2],ya(i,v[(n=f)+104>>2]),v[n+104>>2])for(n=0;t=v[v[f+112>>2]+(n<<2)>>2],a=e,c=un(ut(48),t,r),v[a+12>>2]=c,Un(i,e+12|0),(n=n+1|0)>>>0<d[f+104>>2];);if(i=r+72|0,f=v[r+4>>2],ya(i,v[(n=f)+120>>2]),v[n+120>>2])for(n=0;t=v[v[f+128>>2]+(n<<2)>>2],a=e,c=on(ut(48),t,r),v[a+12>>2]=c,Un(i,e+12|0),(n=n+1|0)>>>0<d[f+120>>2];);if(i=r+88|0,f=v[r+4>>2],ya(i,v[(n=f)+136>>2]),v[n+136>>2])for(n=0;t=v[v[f+144>>2]+(n<<2)>>2],a=e,c=$r(ut(144),t,r),v[a+12>>2]=c,Un(i,e+12|0),(n=n+1|0)>>>0<d[f+136>>2];);return nr(r),V=e+16|0,r}function sr(r,n,e,f){var i,t=0,u=0,o=0,a=0,c=0,b=0,k=w(0),s=0,l=0;if(V=i=V-32|0,d[r+8>>2]<=n>>>0){v[i+24>>2]=0,v[i+16>>2]=0,v[i+20>>2]=0,v[i+12>>2]=10832,o=v[r+8>>2],t=n+1|0,v[r+8>>2]=t,d[r+12>>2]<t>>>0&&(k=w(w(t>>>0)*w(1.75)),t=(t=w(g(k))<w(2147483648)?~~k:-2147483648)>>>0<=8?8:t,v[r+12>>2]=t,a=v[5280],s=r,l=0|Ta[v[v[a>>2]+16>>2]](a,v[r+16>>2],t<<4,8590,85),v[s+16>>2]=l,t=v[r+8>>2]),a=i+12|0;r:if(t>>>0<=o>>>0){if(t>>>0>=o>>>0)break r;for(;u=v[r+16>>2]+(t<<4)|0,Ta[v[v[u>>2]>>2]](u),(0|o)!=(0|(t=t+1|0)););}else for(;;){if(t=v[r+16>>2]+(o<<4)|0,v[t>>2]=10832,v[t+4>>2]=v[a+4>>2],u=v[a+8>>2],v[t+12>>2]=0,v[t+8>>2]=u,u&&(c=v[5280],s=t,l=0|Ta[v[v[c>>2]+12>>2]](c,m(u,20),8590,210),v[s+12>>2]=l,v[t+4>>2]))for(u=0;c=(b=m(u,20))+v[t+12>>2]|0,b=b+v[a+12>>2]|0,v[c>>2]=v[b>>2],Ue(c+4|0,b+4|0),v[c+16>>2]=v[b+16>>2],(u=u+1|0)>>>0<d[t+4>>2];);if(!((o=o+1|0)>>>0<d[r+8>>2]))break}le(a)}r:{n:{e:{if(t=v[r+16>>2]+(n<<4)|0,v[t+4>>2])for(r=0;;){if(ei(4+(v[t+12>>2]+m(r,20)|0)|0,e))break e;if(!((r=r+1|0)>>>0<d[t+4>>2]))break}Co(f);break n}if(Co(f),!((0|r)<0)){r=m(r,20),_i(v[16+(r+v[t+12>>2]|0)>>2]),v[16+(r+v[t+12>>2]|0)>>2]=f;break r}}v[i+12>>2]=n,o=Ue(i+16|0,e),v[i+28>>2]=f,r=i+12|0,V=n=V-32|0,(0|(e=v[t+4>>2]))!=v[t+8>>2]?(v[t+4>>2]=e+1,e=v[t+12>>2]+m(e,20)|0,v[e>>2]=v[r>>2],Ue(e+4|0,r+4|0),v[e+16>>2]=v[r+16>>2]):(v[n+12>>2]=v[r>>2],e=Ue(n+16|0,r+4|0),v[n+28>>2]=v[r+16>>2],k=w(w(d[t+4>>2])*w(1.75)),r=(r=w(g(k))<w(2147483648)?~~k:-2147483648)>>>0<=8?8:r,v[t+8>>2]=r,f=v[5280],r=0|Ta[v[v[f>>2]+16>>2]](f,v[t+12>>2],m(r,20),8590,113),v[t+12>>2]=r,f=v[t+4>>2],v[t+4>>2]=f+1,r=r+m(f,20)|0,v[r>>2]=v[n+12>>2],Ue(r+4|0,e),v[r+16>>2]=v[n+28>>2],gi(e)),V=n+32|0,gi(o)}V=i+32|0}function vr(r,n,e,f,i,t,u){var o,a=0,c=w(0),b=w(0),k=0,s=0,l=w(0),h=w(0),d=w(0),y=0,g=0,F=w(0),A=w(0),T=0,$=w(0),I=0,C=0;o=m(f>>>1|0,u)+t|0,k=v[n+8>>2];r:{n:{e:{if(v[r+24>>2]){if(e)break e;f=0;break n}if(t>>>0>=o>>>0)break r;for(c=p[k+108>>2],b=p[k+104>>2],l=p[k+96>>2],h=p[k+92>>2],d=p[k+112>>2],$=p[k+100>>2],r=v[12+(v[n+72>>2]?n+68|0:r+36|0)>>2];n=(t<<2)+i|0,F=p[(f=r+(e<<2)|0)>>2],A=p[f+4>>2],p[n+4>>2]=d+w(w(F*b)+w(c*A)),p[n>>2]=$+w(w(F*h)+w(l*A)),e=e+2|0,o>>>0>(t=t+u|0)>>>0;);break r}for(y=v[r+32>>2],f=0;f=(g=v[y+(a<<2)>>2])+f|0,a=1+(a+g|0)|0,(s=s+2|0)>>>0<e>>>0;);}if(k=v[k+8>>2],v[n+72>>2]){if(!(t>>>0>=o>>>0))for(s=m(f,3),e=f<<1,g=v[n+80>>2],I=v[r+48>>2],n=v[r+32>>2];;){if(f=a+1|0,(0|(r=v[n+(a<<2)>>2]))<=0)c=w(0),a=f,b=w(0);else for(a=(0|(r=r+f|0))>(0|(a=a+2|0))?r:a,C=v[k+20>>2],b=w(0),c=w(0);r=v[(v[n+(f<<2)>>2]<<2)+C>>2],T=g+(e<<2)|0,l=w(p[(y=(s<<2)+I|0)>>2]+p[T>>2]),h=w(p[y+4>>2]+p[T+4>>2]),d=p[y+8>>2],b=w(w(w(p[r+112>>2]+w(w(l*p[r+104>>2])+w(h*p[r+108>>2])))*d)+b),c=w(w(w(p[r+100>>2]+w(w(l*p[r+92>>2])+w(h*p[r+96>>2])))*d)+c),e=e+2|0,s=s+3|0,(0|a)!=(0|(f=f+1|0)););if(p[(r=(t<<2)+i|0)>>2]=c,p[r+4>>2]=b,!(o>>>0>(t=t+u|0)>>>0))break}}else{if(t>>>0>=o>>>0)break r;for(s=m(f,3),y=v[r+48>>2],n=v[r+32>>2];;){if(f=a+1|0,(0|(r=v[n+(a<<2)>>2]))<=0)c=w(0),b=w(0),a=f;else for(a=(0|(r=r+f|0))>(0|(e=a+2|0))?r:e,g=v[k+20>>2],b=w(0),c=w(0);r=v[g+(v[n+(f<<2)>>2]<<2)>>2],l=p[(e=y+(s<<2)|0)>>2],h=p[e+4>>2],d=p[e+8>>2],b=w(w(w(p[r+112>>2]+w(w(l*p[r+104>>2])+w(h*p[r+108>>2])))*d)+b),c=w(w(w(p[r+100>>2]+w(w(l*p[r+92>>2])+w(h*p[r+96>>2])))*d)+c),s=s+3|0,(0|a)!=(0|(f=f+1|0)););if(p[(r=(t<<2)+i|0)>>2]=c,p[r+4>>2]=b,!(o>>>0>(t=t+u|0)>>>0))break}}}}function lr(r){r|=0;var n,e=w(0),f=0,i=w(0),t=0,u=w(0),o=w(0),a=0,c=0,b=w(0),k=w(0),s=w(0),l=0;V=n=V-16|0,(0|(c=v[r+108>>2]))!=(0|(t=v[r+124>>2]))&&(v[n+12>>2]=0,wn(r+104|0,t,n+12|0),c=v[r+124>>2]),e=p[r+184>>2],i=p[r+180>>2];r:{n:{if(270!=(0|(t=v[r+232>>2]))){if(180!=(0|t)){if(90!=(0|t))break n;if((0|c)<=0)break r;for(u=p[r+84>>2],o=w(u/w(p[r+192>>2]-e)),b=p[r+92>>2],u=w(e-w(w(w(b-p[r+76>>2])-u)/o)),k=p[r+88>>2],e=w(k/w(p[r+188>>2]-i)),s=i,i=p[r+96>>2],k=w(s-w(w(w(i-p[r+80>>2])-k)/e)),o=w(b/o),e=w(i/e),t=v[r+116>>2],a=v[r+132>>2],r=0;l=4|(f=r<<2),p[t+f>>2]=w(p[a+l>>2]*e)+k,p[t+l>>2]=w(w(w(1)-p[f+a>>2])*o)+u,(0|c)>(0|(r=r+2|0)););break r}if((0|c)<=0)break r;for(u=p[r+84>>2],o=w(u/w(p[r+188>>2]-i)),s=i,i=p[r+92>>2],u=w(s-w(w(w(i-p[r+76>>2])-u)/o)),b=w(p[r+88>>2]/w(p[r+192>>2]-e)),k=w(p[r+96>>2]/b),i=w(i/o),e=w(e-w(p[r+80>>2]/b)),t=v[r+116>>2],a=v[r+132>>2],r=0;p[(f=r<<2)+t>>2]=w(w(w(1)-p[f+a>>2])*i)+u,p[(f|=4)+t>>2]=w(w(w(1)-p[f+a>>2])*k)+e,(0|c)>(0|(r=r+2|0)););break r}if((0|c)<=0)break r;for(o=w(p[r+88>>2]/w(p[r+192>>2]-e)),u=w(p[r+92>>2]/o),b=w(p[r+84>>2]/w(p[r+188>>2]-i)),k=w(p[r+96>>2]/b),e=w(e-w(p[r+76>>2]/o)),i=w(i-w(p[r+80>>2]/b)),t=v[r+116>>2],a=v[r+132>>2],r=0;l=4|(f=r<<2),p[t+f>>2]=w(w(w(1)-p[a+l>>2])*k)+i,p[t+l>>2]=w(p[f+a>>2]*u)+e,(0|c)>(0|(r=r+2|0)););break r}if(!((0|c)<=0))for(u=p[r+88>>2],o=w(u/w(p[r+192>>2]-e)),s=e,e=p[r+96>>2],u=w(s-w(w(w(e-p[r+80>>2])-u)/o)),e=w(e/o),o=w(p[r+84>>2]/w(p[r+188>>2]-i)),b=w(p[r+92>>2]/o),i=w(i-w(p[r+76>>2]/o)),t=v[r+116>>2],a=v[r+132>>2],r=0;p[(f=r<<2)+t>>2]=w(p[f+a>>2]*b)+i,p[(f|=4)+t>>2]=w(p[f+a>>2]*e)+u,(0|c)>(0|(r=r+2|0)););}V=n+16|0}function hr(r,n,e){var f,i=0,t=0,u=0,o=0,a=0,c=0,b=0,k=0;if(V=f=V-48|0,v[f+40>>2]=0,v[f+44>>2]=0,v[f+36>>2]=17084,(0|(a=v[n+4>>2]))>0)for(;;){u=(i=m(t,12))+v[n+12>>2]|0,c=i+v[e+12>>2]|0;r:{n:{if(i=v[f+40>>2])for(;;){if(ei(o=i+4|0,u))break n;if(!(i=v[i+28>>2]))break}i=ut(36),v[i>>2]=17100,o=Tt(i+4|0),b=Tt(i+16|0),v[i+28>>2]=0,v[i+32>>2]=0,te(o,u),te(b,c),(u=v[f+40>>2])&&(v[u+32>>2]=i,v[i+28>>2]=u),v[f+40>>2]=i,v[f+44>>2]=v[f+44>>2]+1;break r}te(o,u),te(i+16|0,c)}if((0|a)==(0|(t=t+1|0)))break}if(c=v[r+52>>2])for(;;){Xt(f+24|0,v[v[r+60>>2]+(k<<2)>>2]),t=v[f+28>>2],n=v[f+24>>2];r:if(!(t>>>0>=(i=v[n+4>>2])>>>0))for(;;){if(e=v[f+32>>2],u=v[n+12>>2],e>>>0>=d[4+(u+(t<<4)|0)>>2]){n:{e:{for(;;){if((0|i)==(0|(t=t+1|0)))break e;if(v[4+(u+(t<<4)|0)>>2])break}n=t;break n}n=i}if(v[f+28>>2]=n,i=i>>>0<=t>>>0,e=0,t=n,i)break r}n=v[12+(u+(t<<4)|0)>>2],v[f+32>>2]=e+1,i=68,n=v[16+(n+m(e,20)|0)>>2],(Fu(0|Ta[v[v[n>>2]+8>>2]](n),21200)||(i=24,Fu(0|Ta[v[v[n>>2]+8>>2]](n),21296)))&&(a=v[n+i>>2]);n:if(a){i=a+20|0;e:{if(t=v[f+40>>2])for(;;){if(ei(t+4|0,i))break e;if(!(t=v[t+28>>2]))break}n=Ue(f+12|0,i),e=v[n+4>>2],t=(u=ee(8027))+e|0,v[n+4>>2]=t,o=v[5280],b=v[n+8>>2],t=0|Ta[v[v[o>>2]+16>>2]](o,b,t+1|0,8590,140),v[n+8>>2]=t,Ri(e+t|0,8027!=(0|b)?8027:t,u+1|0),Pe(n,i),v[f>>2]=v[f+20>>2],H(20831,17040,0|f),gi(n);break n}n=f+12|0;e:{f:{if(e=v[f+40>>2])for(;;){if(ei(e+4|0,i))break f;if(!(e=v[e+28>>2]))break}ht(n,0,0);break e}Ue(n,e+16|0)}te(a+8|0,n),gi(n)}if(t=v[f+28>>2],n=v[f+24>>2],!(t>>>0<(i=v[n+4>>2])>>>0))break}if((0|c)==(0|(k=k+1|0)))break}Af(f+36|0),V=f+48|0}function dr(r,n,e,f){var i,t,u,o,a,c=w(0),b=0,k=w(0),s=w(0),h=0,d=w(0),y=0,m=0,g=0,F=w(0),A=w(0),T=w(0),$=w(0),I=0,C=0;V=u=V-16|0,i=v[n+24>>2],v[i+24>>2]&&dr(r,i,e,f),(c=p[n+100>>2])!=w(0)?(s=(c=w(p[n+96>>2]/c))>w(1)?w(1):c,h=1,1!=(0|f)&&(h=v[i+112>>2])):(s=w(1),h=1!=(0|f)?f:0),f=r+56|0,b=p[i+40>>2]>s,k=p[i+52>>2],F=p[i+60>>2],A=p[i+48>>2],T=p[i+44>>2];r:if(l[i+36|0]){if((c=w(p[i+56>>2]-k))==w(0))break r;k=w(k+Er(p[i+72>>2],c))}else k=(c=w(k+p[i+72>>2]))<(d=p[i+56>>2])?c:d;a=b?f:0,d=w(p[i+92>>2]*p[n+104>>2]),$=w(w(w(1)-s)*d),o=v[i+16>>2],t=v[o+8>>2];r:if(3!=(0|h)){if(I=i+148|0,(C=v[i+152>>2])||(v[u+12>>2]=0,wn(I,t<<1,u+12|0)),v[i+108>>2]=0,t)for(;;){b=v[(m=g<<2)+v[o+16>>2]>>2],f=0,c=$;n:{e:{switch(3&v[v[i+128>>2]+m>>2]){case 0:if(f=h,!(s<T)&&Fu(0|Ta[v[v[b>>2]+8>>2]](b),20988)&&(f=0,4&l[v[i+128>>2]+m|0]))break n;if(s<A)break e;if(!Fu(0|Ta[v[v[b>>2]+8>>2]](b),21096))break e;break n;case 2:c=d;break e;case 1:break e}y=v[v[i+144>>2]+m>>2],c=w(w(1)-w(p[y+96>>2]/p[y+100>>2])),c=w(d*(c<w(0)?w(0):c))}if(p[i+108>>2]=c+p[i+108>>2],Fu(0|Ta[v[v[b>>2]+8>>2]](b),21308))pr(b,e,k,c,f,I,g<<1,!C);else{y=1;e:if(!f)if(Fu(0|Ta[v[v[b>>2]+8>>2]](b),20988)){if(!(s<T|4&l[v[i+128>>2]+m|0]))break e;y=0}else y=!(s<A)|1^Fu(0|Ta[v[v[b>>2]+8>>2]](b),21096);Ta[v[v[b>>2]+12>>2]](b,e,F,k,a,c,f,y)}}if((0|(g=g+1|0))==(0|t))break}}else{if(!t)break r;for(f=0;h=v[v[o+16>>2]+(f<<2)>>2],Ta[v[v[h>>2]+12>>2]](h,e,F,k,a,$,3,1),(0|t)!=(0|(f=f+1|0)););}return p[n+100>>2]>w(0)&&Br(r,i,k),v[r+60>>2]=0,p[i+64>>2]=k,p[i+80>>2]=p[i+72>>2],V=u+16|0,s}function pr(r,n,e,f,i,t,u,o){var a,c=w(0),b=0,k=w(0),s=0,l=w(0);if(o&&(v[v[t+12>>2]+(u<<2)>>2]=0),f!=w(1)){a=v[v[n+20>>2]+(v[r+20>>2]<<2)>>2];r:if(0|Ta[v[v[a>>2]+16>>2]](a)){if(s=v[r+36>>2],p[s>>2]>e){n:switch(0|i){case 0:return void(p[a+40>>2]=p[v[a+4>>2]+36>>2]);case 1:break n;default:break r}l=p[a+40>>2],e=p[v[a+4>>2]+36>>2]}else n=i?a+40|0:v[a+4>>2]+36|0,l=p[n>>2],n=s+(v[r+28>>2]<<2)|0,p[n-8>>2]<=e?e=w(p[v[a+4>>2]+36>>2]+p[n-4>>2]):(n=(s=(i=Se(r+24|0,e,2))<<2)+v[r+36>>2]|0,k=p[n-4>>2],c=e,e=p[n>>2],e=vn(r,(i>>1)-1|0,w(w(1)-w(w(c-e)/w(p[n-8>>2]-e)))),c=w(p[4+(v[r+36>>2]+s|0)>>2]-k),b=16384.499999999996-+w(c/w(360)),r=g(b)<2147483648?~~b:-2147483648,e=w(p[v[a+4>>2]+36>>2]+w(w(w(c-w(0|m(16384-r|0,360)))*e)+k)),b=16384.499999999996-+w(e/w(360)),r=g(b)<2147483648?~~b:-2147483648,e=w(e-w(0|m(16384-r|0,360))));e=w(e-l),b=+w(e/w(-360))+16384.499999999996,r=g(b)<2147483648?~~b:-2147483648,c=w(0),(k=w(e-w(0|m(16384-r|0,360))))!=w(0)?(e=k,o||(r=v[t+12>>2]+(u<<2)|0,c=p[r>>2],e=p[r+4>>2]),r=c>=w(0),n=k>w(0),!(w(g(e))<=w(90))|tu(e)==tu(k)||(w(g(c))>w(180)&&(c=w(w(tu(c)*w(360))+c)),r=n),e=w(w(k+c)-Er(c,w(360))),(0|r)!=(0|n)&&(e=w(w(tu(c)*w(360))+e)),r=v[t+12>>2],p[r+(u<<2)>>2]=e):(r=v[t+12>>2],e=p[r+(u<<2)>>2]),p[4+((u<<2)+r|0)>>2]=k,e=w(w(e*f)+l),b=+w(e/w(-360))+16384.499999999996,r=g(b)<2147483648?~~b:-2147483648,p[a+40>>2]=e-w(0|m(16384-r|0,360))}}else Ta[v[v[r>>2]+12>>2]](r,n,w(0),e,0,w(1),i,0)}function yr(r,n,e,f){var i,t=0,u=0,o=0,a=0,c=w(0),b=0,k=0,s=0;if(V=i=V+-64|0,v[i+60>>2]=0,v[i+52>>2]=0,v[i+56>>2]=0,v[i+48>>2]=8776,v[e+52>>2]=f,n=En(n,2901),(u=v[n+12>>2])&&(v[i+56>>2]=u,t=v[5280],k=i,s=0|Ta[v[v[t>>2]+16>>2]](t,0,u<<2,8590,101),v[k+60>>2]=s),v[i+12>>2]=0,wn(i+48|0,u,i+12|0),n=v[n+4>>2])for(t=0,o=v[i+60>>2];p[o+(t<<2)>>2]=p[n+24>>2],t=t+1|0,n=v[n>>2];);if((0|f)!=(0|u)){if(v[i+44>>2]=0,v[i+36>>2]=0,v[i+40>>2]=0,v[i+28>>2]=0,v[i+20>>2]=0,v[i+24>>2]=0,v[i+32>>2]=8776,v[i+16>>2]=10768,v[i+12>>2]=10752,f&&(v[i+24>>2]=m(f,3),n=v[5280],n=0|Ta[v[v[n>>2]+16>>2]](n,0,m(f,12),8590,101),v[i+40>>2]=m(f,9),v[i+28>>2]=n,n=v[5280],k=i,s=0|Ta[v[v[n>>2]+16>>2]](n,0,m(f,36),8590,101),v[k+44>>2]=s),n=i+32|0,f=i+16|0,u)for(t=0;;){if(c=p[v[i+60>>2]+(t<<2)>>2],o=w(g(c))<w(2147483648)?~~c:-2147483648,v[i+8>>2]=o,Wn(f,i+8|0),(t=t+1|0)>>>0<(b=t+(o<<2)|0)>>>0)for(;c=p[(o=t<<2)+v[i+60>>2]>>2],a=w(g(c))<w(2147483648)?~~c:-2147483648,v[i+8>>2]=a,Wn(f,a=i+8|0),p[i+8>>2]=p[4+(o+v[i+60>>2]|0)>>2]*p[r+24>>2],Gn(n,a),p[i+8>>2]=p[8+(o+v[i+60>>2]|0)>>2]*p[r+24>>2],Gn(n,a),Gn(n,12+(o+v[i+60>>2]|0)|0),b>>>0>(t=t+4|0)>>>0;);if(!(t>>>0<u>>>0))break}v[e+40>>2]=0,Df(e+36|0,n),v[e+24>>2]=0,Vf(e+20|0,f),v[i+12>>2]=10752,To(n),Ou(f)}else{if(!(!f|p[r+24>>2]==w(1)))for(n=0,t=v[i+60>>2];p[(u=t+(n<<2)|0)>>2]=p[r+24>>2]*p[u>>2],(0|f)!=(0|(n=n+1|0)););v[e+40>>2]=0,Df(e+36|0,i+48|0)}To(i+48|0),V=i- -64|0}function mr(r){var n=0,e=0,f=0,t=0,u=0,o=0;if(!l[r+28|0]){if(i[r+28|0]=1,v[r+8>>2]){for(e=v[r+20>>2];;){t=v[r+16>>2]+(u<<4)|0,n=v[t+8>>2];r:{n:{e:{f:{i:switch(0|(f=v[t+4>>2])){case 5:break n;case 3:break e;case 2:break f;case 0:case 1:case 4:break i;default:break r}if((o=v[n+168>>2])?Ta[v[v[o>>2]+8>>2]](o,e,f,n,0):Ta[v[n+164>>2]](e,f,n,0),!(f=v[e+96>>2])){Ta[v[e+92>>2]](e,v[t+4>>2],n,0);break r}Ta[v[v[f>>2]+8>>2]](f,e,v[t+4>>2],n,0);break r}(f=v[n+168>>2])?Ta[v[v[f>>2]+8>>2]](f,e,2,n,0):Ta[v[n+164>>2]](e,2,n,0),(f=v[e+96>>2])?Ta[v[v[f>>2]+8>>2]](f,e,v[t+4>>2],n,0):Ta[v[e+92>>2]](e,v[t+4>>2],n,0)}(t=v[n+168>>2])?Ta[v[v[t>>2]+8>>2]](t,e,3,n,0):Ta[v[n+164>>2]](e,3,n,0),(t=v[e+96>>2])?Ta[v[v[t>>2]+8>>2]](t,e,3,n,0):Ta[v[e+92>>2]](e,3,n,0),v[n+16>>2]=0,v[n+20>>2]=0,v[n+24>>2]=0,v[n+28>>2]=0,(t=v[n+12>>2])&&(f=v[n+8>>2])&&Ta[0|t](f),v[n+168>>2]=0,v[n+164>>2]=9,v[n+8>>2]=0,v[n+12>>2]=0,v[n+152>>2]=0,v[n+136>>2]=0,v[n+120>>2]=0,pe(v[r+24>>2],n);break r}(f=v[n+168>>2])?Ta[v[v[f>>2]+8>>2]](f,e,5,n,v[t+12>>2]):Ta[v[n+164>>2]](e,5,n,v[t+12>>2]),(f=v[e+96>>2])?Ta[v[v[f>>2]+8>>2]](f,e,v[t+4>>2],n,v[t+12>>2]):Ta[v[e+92>>2]](e,v[t+4>>2],n,v[t+12>>2])}if(!((n=v[r+8>>2])>>>0>(u=u+1|0)>>>0))break}if(n)for(e=0;n=v[r+16>>2]+(~e+n<<4)|0,Ta[v[v[n>>2]>>2]](n),(n=v[r+8>>2])>>>0>(e=e+1|0)>>>0;);}i[r+28|0]=0,v[r+8>>2]=0}}function wr(r,n){var e,f=0,t=0,u=0,o=0,a=0;V=e=V-16|0;r:if(34!=l[0|n])v[5290]=n,u=0;else{for(f=n=n+1|0;!(!(o=l[0|f])|34==(0|o));)t=t+1|0,f=(92==(0|o)?2:1)+f|0;if(o=v[5280],u=0,!(o=0|Ta[v[v[o>>2]+8>>2]](o,t+1|0,8590,265)))break r;for(f=o;;){n:{e:if(92==(0|(u=l[0|n]))){u=n+1|0;f:{i:{t:{u:{o:{a:{switch((a=i[n+1|0])-110|0){case 1:case 2:case 3:case 5:break i;case 7:break t;case 6:break u;case 4:break o;case 0:break a}c:switch(a-98|0){case 0:i[0|f]=8;break f;case 4:break c;default:break i}i[0|f]=12;break f}i[0|f]=10;break f}i[0|f]=13;break f}i[0|f]=9;break f}if(Jn(n+2|0,e+12|0),u=n+5|0,!(t=v[e+12>>2]))break e;if(56320==(0|(a=-1024&t)))break e;t:{u:{o:{if(55296!=(0|a)){if(n=1,t>>>0<128)break t;if(n=2,t>>>0<2048)break u;if(n=3,t>>>0<65536)break o}else{if(92!=l[n+6|0]|117!=l[n+7|0])break e;if(Jn(n+8|0,e+8|0),u=n+11|0,(n=v[e+8>>2])-57344>>>0<4294966272)break e;t=65536+(1023&n|t<<10&1047552)|0,v[e+12>>2]=t}i[f+3|0]=63&t|128,t=v[e+12>>2]>>>6|0,v[e+12>>2]=t,n=4}i[f+2|0]=63&t|128,t=v[e+12>>2]>>>6|0,v[e+12>>2]=t}i[f+1|0]=63&t|128,t=v[e+12>>2]>>>6|0,v[e+12>>2]=t}i[0|f]=l[n+9724|0]|t,f=n+f|0;break e}i[0|f]=a}f=f+1|0}else{if(!u|34==(0|u))break n;i[0|f]=u,f=f+1|0,u=n}n=u+1|0;continue}break}i[0|f]=0,u=l[0|n],v[r+8>>2]=4,v[r+16>>2]=o,u=(34==(0|u))+n|0}return V=e+16|0,u}function gr(r,n,e,f){var t,u=0,o=0,a=0,c=0,b=0,k=0,s=0,h=0,d=0,p=0,y=0,m=0,w=0,g=0,F=0;V=t=V-16|0;r:{n:{e:{if((0|e)<=36){if(u=l[0|r])break e;o=r;break n}v[5446]=28,f=0;break r}o=r;e:{for(;;){if(!co(u<<24>>24))break e;if(u=l[o+1|0],o=o+1|0,!u)break}break n}e:switch((u&=255)-43|0){case 0:case 2:break e;default:break n}s=45==(0|u)?-1:0,o=o+1|0}n:if(16!=(16|e)|48!=l[0|o])d=e||10;else{if(w=1,88==(223&l[o+1|0])){o=o+2|0,d=16;break n}o=o+1|0,d=e||8}for(e=0;u=-48,(((a=i[0|o])-48&255)>>>0<10||(u=-87,(a-97&255)>>>0<26||(u=-55,!((a-65&255)>>>0>25))))&&!((0|(a=u+a|0))>=(0|d));)c=je(h,0,0,0),b=Z,F=je(k,0,d,0),u=Z,p=je(0,0,k,0),y=Z,c=c+(y=(u=u+p|0)>>>0<p>>>0?y+1|0:y)|0,p=je(h,0,d,0)+u|0,m=Z,m=(u=u>>>0>p>>>0?m+1|0:m)+c|0,v[t+8>>2]=m,b=c>>>0<y>>>0?b+1|0:b,v[t+12>>2]=u>>>0>m>>>0?b+1|0:b,v[t>>2]=F,v[t+4>>2]=p,u=1,v[t+8>>2]|v[t+12>>2]||(b=je(k,h,d,0),-1==(0|(c=Z))&~a>>>0<b>>>0||(h=(k=a+b|0)>>>0<a>>>0?c+1|0:c,w=1,u=e)),o=o+1|0,e=u;n&&(v[n>>2]=w?o:r);n:{if(e)v[5446]=68,s=(r=1&f)?0:s,k=f,h=0;else{if(!h&f>>>0>k>>>0)break n;r=1&f}if(!(r|s)){v[5446]=68,f=(r=f)-1|0,g=0-!r|0;break r}if(!(!h&f>>>0>=k>>>0)){v[5446]=68;break r}}f=(r=s^k)-s|0,g=((n=s>>31)^h)-((r>>>0<s>>>0)+n|0)|0}return V=t+16|0,Z=g,f}function Fr(r,n){var e=0,f=0,i=0,c=0,s=0,l=w(0),h=0,d=0;b(n),c=mu(i=t(2));r:{n:{e:{b(r);f:{if((e=t(2))-2139095040>>>0>=2164260864){if(c)break f;break n}if(!c)break e}if(l=w(1),1065353216==(0|e))break r;if(!(s=i<<1))break r;if(!(s>>>0<4278190081&(e<<=1)>>>0<=4278190080))return w(r+n);if(2130706432==(0|e))break r;return w(e>>>0>2130706431^(0|i)>=0?0:n*n)}if(mu(e)){if(l=w(r*r),(0|e)<0&&(l=1==(0|Zf(i))?w(-l):l),(0|i)>=0)break r;return Ft(w(w(1)/l))}if((0|e)<0){if(!(i=Zf(i)))return r=w(r-r),w(r/r);s=(1==(0|i))<<16,e&=2147483647}e>>>0>8388607||(e=(2147483647&(b(w(r*w(8388608))),t(2)))-192937984|0)}if(c=(i=e-1060306944|0)>>>15&240,u(2,e-(-8388608&i)|0),a(+(f=((h=(f=+k()*y[c+20480>>3]-1)*f)*h*(.288457581109214*f-.36092606229713164)+((.480898481472577*f-.7213474675006291)*h+(1.4426950408774342*f+(y[c+20488>>3]+ +(i>>23)))))*+n)),e=0|t(1),t(0),!(1079967744==(0|(e&=2147450880))|e>>>0<1079967744)){if(f>127.99999995700433)return Eu(s,w(15845632502852868e13));if(f<=-150)return Eu(s,w(2524354896707238e-44))}f-=(d=(h=y[2179])+f)-h,f=(y[2180]*f+y[2181])*f*f+y[2182]*f+1,a(+d),t(1),e=s+(c=0|t(0))|0,i=c=v[(s=17176+((31&c)<<3)|0)>>2],e=v[s+4>>2]+(e<<15)|0,u(0,0|i),u(1,0|(i>>>0<i>>>0?e+1:e)),l=w(f*+o())}return l}function Ar(r,n,e,f,i,t,u){r|=0,n=w(n),e=w(e),f|=0,i|=0,t|=0,u=w(u);var o=w(0),a=w(0),c=0,b=w(0),k=w(0),s=w(0),h=w(0),d=w(0),y=w(0);h=p[r+68>>2],s=w(-p[r+80>>2]),c=v[r+12>>2],k=p[c+108>>2],o=p[c+104>>2],b=p[c+96>>2],a=p[c+92>>2],l[r+88|0]||Tr(r),h=w(s-h);r:{switch(v[v[r+4>>2]+56>>2]-1|0){case 0:c=r+112|0,o=e,a=w(n-p[r+100>>2]);break r;case 1:b=w(w(g(w(w(a*k)-w(o*b))))/w(w(a*a)+w(o*o))),k=w(a*b),b=w(b*w(-o)),h=w(w(Or(o,a)*w(57.2957763671875))+h)}s=w(e-p[c+112>>2]),d=w(n-p[c+100>>2]),y=w(w(s*a)-w(o*d)),a=w(w(a*k)-w(o*b)),o=w(y/a),c=r- -64|0,a=w(w(w(w(d*k)-w(b*s))/a)-p[r+60>>2])}s=w(o-p[c>>2]),o=w(w(Or(s,a)*w(57.2957763671875))+h),(o=(k=p[r+72>>2])<w(0)?w(o+w(180)):o)>w(180)?o=w(o+w(-360)):o<w(-180)&&(o=w(o+w(360))),b=p[r+76>>2];r:if(f|i){c=f,n=(f=v[v[r+4>>2]+56>>2]-3>>>0<2)?w(n-p[r+100>>2]):a,a=w(n*n),n=f?w(e-p[r+112>>2]):s;n:{if(!((e=w(T(w(a+w(n*n)))))<(n=w(k*p[v[r+4>>2]+24>>2]))&&c)){if(!i|!(n>w(9999999747378752e-20)))break r;if(n<e)break n;break r}if(!(n>w(9999999747378752e-20)))break r}n=w(w(w(w(e/n)+w(-1))*u)+w(1)),k=w(k*n),b=w(b*(t?n:w(1)))}br(r,p[r+60>>2],p[r+64>>2],w(w(o*u)+p[r+68>>2]),k,b,p[r+80>>2],p[r+84>>2])}function Tr(r){var n,e=w(0),f=w(0),t=w(0),u=w(0),o=w(0),a=w(0),c=w(0),b=w(0),k=w(0),s=0,l=w(0);return i[r+88|0]=1,(n=v[r+12>>2])?(e=p[n+112>>2],o=p[n+100>>2],f=p[n+108>>2],u=p[n+92>>2],c=p[n+104>>2],t=p[n+96>>2],v[r+80>>2]=0,a=w(p[r+112>>2]-e),e=w(w(1)/w(w(u*f)-w(c*t))),o=w(p[r+100>>2]-o),p[r+64>>2]=w(w(u*a)*e)-w(e*w(c*o)),p[r+60>>2]=w(w(f*o)*e)-w(e*w(t*a)),f=w(f*e),o=p[r+92>>2],a=w(-e),k=w(t*a),b=p[r+104>>2],t=w(w(f*o)+w(k*b)),e=w(u*e),a=w(c*a),u=w(w(e*b)+w(a*o)),c=w(T(w(w(t*t)+w(u*u)))),p[r+72>>2]=c,o=p[r+108>>2],b=a,a=p[r+96>>2],e=w(w(e*o)+w(b*a)),f=w(w(f*a)+w(k*o)),c>w(9999999747378752e-20)?(o=w(w(t*e)-w(u*f)),p[r+76>>2]=o/c,s=r,l=w(Or(w(w(t*f)+w(u*e)),o)*w(57.2957763671875)),p[s+84>>2]=l,s=r,l=w(Or(u,t)*w(57.2957763671875)),void(p[s+68>>2]=l)):(v[r+84>>2]=0,v[r+72>>2]=0,p[r+76>>2]=T(w(w(f*f)+w(e*e))),s=r,l=w(w(Or(e,f)*w(-57.2957763671875))+w(90)),void(p[s+68>>2]=l))):(p[r+60>>2]=p[r+100>>2],p[r+64>>2]=p[r+112>>2],e=p[r+104>>2],f=p[r+92>>2],v[r+80>>2]=0,p[r+72>>2]=T(w(w(f*f)+w(e*e))),s=r,l=w(Or(e,f)*w(57.2957763671875)),p[s+68>>2]=l,t=p[r+96>>2],u=p[r+108>>2],p[r+76>>2]=T(w(w(t*t)+w(u*u))),s=r,l=w(Or(w(w(f*t)+w(e*u)),w(w(f*u)-w(e*t)))*w(57.2957763671875)),void(p[s+84>>2]=l))}function $r(r,n,e){var f,t,u,o=w(0),a=0,c=0;if(V=t=V-16|0,f=Ho(r),v[f+8>>2]=9260,v[f+4>>2]=n,v[f>>2]=9892,r=0,v[f+20>>2]=0,v[f+12>>2]=0,v[f+16>>2]=0,a=f,c=ku(e,Qo(v[n+40>>2])),v[a+24>>2]=c,p[f+28>>2]=p[n+60>>2],p[f+32>>2]=p[n+64>>2],p[f+36>>2]=p[n+68>>2],o=p[n+72>>2],v[f+124>>2]=8776,v[f+120>>2]=0,v[f+112>>2]=0,v[f+116>>2]=0,v[f+108>>2]=8776,v[f+104>>2]=0,v[f+96>>2]=0,v[f+100>>2]=0,v[f+92>>2]=8776,v[f+88>>2]=0,v[f+80>>2]=0,v[f+84>>2]=0,v[f+76>>2]=8776,v[f+72>>2]=0,v[(n=f- -64|0)>>2]=0,v[n+4>>2]=0,v[f+60>>2]=8776,v[f+56>>2]=0,v[f+48>>2]=0,v[f+52>>2]=0,v[f+44>>2]=8776,p[f+40>>2]=o,i[f+133|0]=0,i[f+134|0]=0,i[f+135|0]=0,i[f+136|0]=0,i[f+137|0]=0,i[f+138|0]=0,i[f+139|0]=0,i[f+140|0]=0,v[f+128>>2]=0,v[f+132>>2]=0,ya(u=f+8|0,v[v[f+4>>2]+28>>2]),n=v[f+4>>2],v[n+28>>2])for(;a=t,c=wu(e,Qo(v[v[n+36>>2]+(r<<2)>>2])),v[a+12>>2]=c,Un(u,t+12|0),r=r+1|0,n=v[f+4>>2],r>>>0<d[n+28>>2];);return v[t+8>>2]=0,wn(f+124|0,10,t+8|0),V=t+16|0,f}function Ir(r,n){r|=0,n=w(n);var e,f=0,i=0,t=w(0),u=w(0),o=0,a=w(0),c=0,b=w(0);if(e=v[r+44>>2])for(n=w(p[r+100>>2]*n);;){c=v[r+52>>2]+(o<<2)|0;r:if(f=v[c>>2]){if(p[f+60>>2]=p[f+64>>2],u=p[f+80>>2],p[f+76>>2]=u,a=p[f+88>>2],t=w(n*a),(b=p[f+68>>2])>w(0)){if(t=w(b-t),p[f+68>>2]=t,t>w(0))break r;v[f+68>>2]=0,t=w(-t)}n:{if(i=v[f+20>>2]){if(!((u=w(u-p[i+68>>2]))>=w(0)))break n;if(v[i+68>>2]=0,p[i+72>>2]=(a!=w(0)?w(w(n+w(u/a))*p[i+88>>2]):w(0))+p[i+72>>2],p[f+72>>2]=t+p[f+72>>2],Tn(r,o,i,1),!(f=v[i+24>>2]))break r;for(;p[i+96>>2]=n+p[i+96>>2],i=f,f=v[f+24>>2];);break r}if(!(v[f+24>>2]|!(u>=p[f+84>>2]))){v[c>>2]=0,Bi(v[r+72>>2],f),If(r,f);break r}}if(v[f+24>>2]&&mn(r,f,n)&&(i=v[f+24>>2],v[f+24>>2]=0,i))for(v[i+28>>2]=0;Bi(v[r+72>>2],i),i=v[i+24>>2];);p[f+72>>2]=t+p[f+72>>2]}if((0|e)==(0|(o=o+1|0)))break}mr(v[r+72>>2])}function Cr(r,n,e,f){var i,t,o=0,a=0,c=0,b=0,s=w(0),h=0,d=0,y=0,g=0,F=0;if(V=i=V-16|0,s=p[r+36>>2],r=f<<1,v[e+52>>2]=r,o=v[n+4>>2],v[n+4>>2]=o+1,t=e+36|0,l[0|o]){if(ya(t,m(f,18)),ya(o=e+20|0,m(f,6)),!((0|f)<=0))for(;;){if(h=jn(n,1),v[i+12>>2]=h,Wn(o,i+12|0),d=0,(0|h)>0)for(;g=i,F=jn(n,1),v[g+12>>2]=F,Wn(o,e=i+12|0),r=v[n+4>>2],v[n+4>>2]=r+1,a=l[0|r],v[n+4>>2]=r+2,c=l[r+1|0],v[n+4>>2]=r+3,b=l[r+2|0],v[n+4>>2]=r+4,p[i+12>>2]=s*(u(2,l[r+3|0]|(b|c<<8|a<<16)<<8),k()),Gn(t,e),r=v[n+4>>2],v[n+4>>2]=r+1,a=l[0|r],v[n+4>>2]=r+2,c=l[r+1|0],v[n+4>>2]=r+3,b=l[r+2|0],v[n+4>>2]=r+4,p[i+12>>2]=s*(u(2,l[r+3|0]|(b|c<<8|a<<16)<<8),k()),Gn(t,e),r=v[n+4>>2],v[n+4>>2]=r+1,a=l[0|r],v[n+4>>2]=r+2,c=l[r+1|0],v[n+4>>2]=r+3,b=l[r+2|0],v[n+4>>2]=r+4,v[i+12>>2]=l[r+3|0]|(b|c<<8|a<<16)<<8,Gn(t,e),(0|(d=d+1|0))!=(0|h););if((0|(y=y+1|0))==(0|f))break}}else Vr(n,r,s,t);V=i+16|0}function Pr(r){return v[r+208>>2]=8776,v[r+204>>2]=0,v[r+196>>2]=0,v[r+200>>2]=0,v[r+192>>2]=8776,v[r+188>>2]=0,v[r+180>>2]=0,v[r+184>>2]=0,v[r+176>>2]=9852,v[r+172>>2]=0,v[r+164>>2]=0,v[r+168>>2]=0,v[r+160>>2]=8776,v[r+156>>2]=0,v[r+148>>2]=0,v[r+152>>2]=0,v[r+144>>2]=8776,v[r+140>>2]=0,v[r+132>>2]=0,v[r+136>>2]=0,v[r+128>>2]=8776,v[r+124>>2]=0,v[r+116>>2]=0,v[r+120>>2]=0,v[r+112>>2]=10456,v[r+108>>2]=10504,v[r+104>>2]=0,v[r+96>>2]=0,v[r+100>>2]=0,v[r+92>>2]=10440,v[r+88>>2]=10488,v[r+84>>2]=0,v[r+76>>2]=0,v[r+80>>2]=0,v[r+72>>2]=8744,v[r+68>>2]=0,v[r+60>>2]=0,v[r+64>>2]=0,v[r+56>>2]=10472,v[r+52>>2]=0,v[r+44>>2]=0,v[r+48>>2]=0,v[r+40>>2]=8744,v[r+36>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[r+24>>2]=10456,v[r+20>>2]=0,v[r+12>>2]=0,v[r+16>>2]=0,v[r+8>>2]=10440,v[r+4>>2]=11080,v[r>>2]=10424,v[r+220>>2]=0,v[r+224>>2]=0,v[r+212>>2]=0,v[r+216>>2]=0,ya(r+144|0,128),ya(r+160|0,128),ma(r+176|0,128),ya(r+192|0,128),r}function Er(r,n){var e=0,f=0,i=0,o=0,a=0,c=0;if(b(n),!((e=(o=t(2))<<1)&&(b(r),255!=(0|(i=(c=t(2))>>>23&255))&&(2147483647&(b(n),t(2)))>>>0<2139095041)))return r=w(r*n),w(r/r);if((f=c<<1)>>>0<=e>>>0)return(0|f)==(0|e)?w(r*w(0)):r;if(a=o>>>23&255,i)f=8388607&c|8388608;else{if(i=0,(0|(f=c<<9))>=0)for(;i=i-1|0,(0|(f<<=1))>=0;);f=c<<1-i}if(a)e=8388607&o|8388608;else{if(a=0,(0|(e=o<<9))>=0)for(;a=a-1|0,(0|(e<<=1))>=0;);e=o<<1-a}if(o=e,(0|i)>(0|a)){for(;;){if(!((0|(e=f-o|0))<0||(f=e)))return w(r*w(0));if(f<<=1,!((0|a)<(0|(i=i-1|0))))break}i=a}if(!((0|(e=f-o|0))<0||(f=e)))return w(r*w(0));if(f>>>0>8388607)e=f;else for(;i=i-1|0,o=f>>>0<4194304,f=e=f<<1,o;);return u(2,-2147483648&c|((0|i)>0?e-8388608|i<<23:e>>>1-i)),k()}function Or(r,n){var e,f,i=0,o=0,a=w(0);if(!((2147483647&(b(r),t(2)))>>>0<2139095041&(2147483647&(b(n),t(2)))>>>0<=2139095040))return w(r+n);if(b(n),1065353216==(0|(i=t(2))))return Rr(r);f=i>>>30&2,b(r),e=f|(o=t(2))>>>31;r:{n:{e:{if(!(o&=2147483647)){f:switch(e-2|0){case 0:return w(3.1415927410125732);case 1:break f;default:break e}return w(-3.1415927410125732)}if(2139095040!=(0|(i&=2147483647))){if(!i|!(2139095040!=(0|o)&o>>>0<=i+218103808>>>0))break r;switch(f&&(a=w(0),i>>>0>o+218103808>>>0)||(a=Rr(w(g(w(r/n))))),r=a,0|e){case 1:return w(-r);case 2:return w(w(3.1415927410125732)-w(r+w(8.742277657347586e-8)));case 0:break e}return w(w(r+w(8.742277657347586e-8))+w(-3.1415927410125732))}if(2139095040==(0|o))break n;r=p[17572+(e<<2)>>2]}return r}return p[17556+(e<<2)>>2]}return u(2,-2147483648&(b(r),t(2))|1070141403),k()}function Rr(r){var n,e,f=0,i=w(0),o=w(0),a=w(0);if(b(r),(n=2147483647&(e=t(2)))>>>0>=1283457024)return(2147483647&(b(r),t(2)))>>>0>2139095040?r:(u(2,-2147483648&(b(r),t(2))|1070141402),k());r:{n:{if(n>>>0<=1054867455){if(f=-1,n>>>0>=964689920)break n;break r}if(r=w(g(r)),n>>>0<=1066926079){if(n>>>0<=1060110335){r=w(w(w(r+r)+w(-1))/w(r+w(2))),f=0;break n}r=w(w(r+w(-1))/w(r+w(1))),f=1}else n>>>0<=1075576831?(r=w(w(r+w(-1.5))/w(w(r*w(1.5))+w(1))),f=2):(r=w(w(-1)/r),f=3)}if(o=w(r*r),i=w(o*o),a=w(i*w(w(i*w(-.106480173766613))+w(-.19999158382415771))),i=w(o*w(w(i*w(w(i*w(.06168760731816292))+w(.14253635704517365)))+w(.333333283662796))),n>>>0<=1054867455)return w(r-w(r*w(a+i)));r=w(p[17600+(f<<=2)>>2]-w(w(w(r*w(a+i))-p[f+17616>>2])-r)),r=(0|e)<0?w(-r):r}return r}function Sr(r,n){var e,f,i=0,t=0,u=0,o=0,a=w(0);if(V=f=V-16|0,e=df(r,n),v[e+40>>2]=9464,v[e+24>>2]=8776,v[e+20>>2]=0,v[e>>2]=9436,v[e+44>>2]=0,v[e+48>>2]=0,v[e+36>>2]=0,v[e+28>>2]=0,v[e+32>>2]=0,v[e+52>>2]=0,v[e+56>>2]=0,ya(r=e+24|0,n),da(e+40|0,n),v[f>>2]=0,wn(r,n,f),(0|n)>0)for(r=0;v[f+12>>2]=0,v[f+4>>2]=0,v[f+8>>2]=0,v[f>>2]=8776,V=u=V-16|0,(0|(i=v[e+44>>2]))!=v[e+48>>2]?(v[e+44>>2]=i+1,Bn(v[e+52>>2]+(i<<4)|0,f)):(o=Bn(u,f),a=w(w(d[e+44>>2])*w(1.75)),t=(i=w(g(a))<w(2147483648)?~~a:-2147483648)>>>0<=8?8:i,v[e+48>>2]=t,i=v[5280],t=0|Ta[v[v[i>>2]+16>>2]](i,v[e+52>>2],t<<4,8590,113),v[e+52>>2]=t,i=v[e+44>>2],v[e+44>>2]=i+1,Bn(t+(i<<4)|0,o),To(o)),V=u+16|0,To(f),(0|n)!=(0|(r=r+1|0)););return V=f+16|0,e}function Wr(r){var n,e,f=w(0),i=0,u=0;V=n=V-16|0,b(r);r:if((e=2147483647&(i=t(2)))>>>0<=1061752794){if(f=w(1),e>>>0<964689920)break r;f=ri(+r)}else if(e>>>0<=1081824209){if(e>>>0>=1075235812){f=w(-ri(+r+((0|i)<0?3.141592653589793:-3.141592653589793)));break r}if(u=+r,(0|i)<0){f=zf(u+1.5707963267948966);break r}f=zf(1.5707963267948966-u)}else if(e>>>0<=1088565717){if(e>>>0>=1085271520){f=ri(+r+((0|i)<0?6.283185307179586:-6.283185307179586));break r}if((0|i)<0){f=zf(-4.71238898038469-+r);break r}f=zf(+r-4.71238898038469)}else if(f=w(r-r),!(e>>>0>=2139095040)){switch(3&fr(r,n+8|0)){case 0:f=ri(y[n+8>>3]);break r;case 1:f=zf(-y[n+8>>3]);break r;case 2:f=w(-ri(y[n+8>>3]));break r}f=zf(y[n+8>>3])}return V=n+16|0,f}function Gr(r){var n=0,e=0,f=0,t=0,u=0;if(n=v[12+(r|=0)>>2])for(;De(v[v[r+20>>2]+(e<<2)>>2]),(0|n)!=(0|(e=e+1|0)););if(t=v[r+60>>2])for(u=v[r+68>>2],e=0;n=v[u+(e<<2)>>2],f=v[n+4>>2],v[n+24>>2]=v[f+44>>2],i[n+28|0]=l[f+48|0],i[n+29|0]=l[f+49|0],p[n+32>>2]=p[f+52>>2],p[n+36>>2]=p[f+56>>2],(0|t)!=(0|(e=e+1|0)););if(t=v[r+76>>2])for(u=v[r+84>>2],e=0;n=v[u+(e<<2)>>2],f=v[n+4>>2],p[n+28>>2]=p[f+44>>2],p[n+32>>2]=p[f+48>>2],p[n+36>>2]=p[f+52>>2],p[n+40>>2]=p[f+56>>2],(0|t)!=(0|(e=e+1|0)););if(f=v[r+92>>2])for(t=v[r+100>>2],e=0;r=v[t+(e<<2)>>2],n=v[r+4>>2],p[r+28>>2]=p[n+60>>2],p[r+32>>2]=p[n+64>>2],p[r+36>>2]=p[n+68>>2],p[r+40>>2]=p[n+72>>2],(0|f)!=(0|(e=e+1|0)););}function Ur(r){var n,e,f=0,i=0;V=n=V-16|0,b(r);r:if((e=2147483647&(i=t(2)))>>>0<=1061752794){if(e>>>0<964689920)break r;r=zf(+r)}else if(e>>>0<=1081824209){if(f=+r,e>>>0<=1075235811){if((0|i)<0){r=w(-ri(f+1.5707963267948966));break r}r=ri(f+-1.5707963267948966);break r}r=zf(-(((0|i)>=0?-3.141592653589793:3.141592653589793)+f))}else if(e>>>0<=1088565717){if(e>>>0<=1085271519){if(f=+r,(0|i)<0){r=ri(f+4.71238898038469);break r}r=w(-ri(f+-4.71238898038469));break r}r=zf(+r+((0|i)<0?6.283185307179586:-6.283185307179586))}else if(e>>>0>=2139095040)r=w(r-r);else{switch(3&fr(r,n+8|0)){case 0:r=zf(y[n+8>>3]);break r;case 1:r=ri(y[n+8>>3]);break r;case 2:r=zf(-y[n+8>>3]);break r}r=w(-ri(y[n+8>>3]))}return V=n+16|0,r}function jr(r,n){n|=0;var e=0,f=0,i=0,t=0,u=0,o=0,a=0;if((0|(t=v[136+(r|=0)>>2]))!=(0|n)){r:if(n)if(t){if(!(e=v[t+24>>2]))break r;for(a=n+16|0;;){if(u=v[t+32>>2],d[4+(u+(f<<4)|0)>>2]<=o>>>0){n:{e:{for(;;){if((0|(f=f+1|0))==(0|e))break e;if(v[4+(u+(f<<4)|0)>>2])break}i=f;break n}i=e}if(e=e>>>0<=f>>>0,o=0,f=i,e)break r}if(i=v[12+(u+(f<<4)|0)>>2]+m(o,20)|0,e=v[i>>2],u=v[v[r+36>>2]+(e<<2)>>2],v[u+60>>2]==v[i+16>>2]&&(i=Xn(a,e,i+4|0))&&hi(u,i),o=o+1|0,!(f>>>0<(e=v[t+24>>2])>>>0))break}}else if(i=v[r+28>>2])for(;f=v[v[r+36>>2]+(e<<2)>>2],t=No(v[f+4>>2]),v[t+4>>2]&&(t=Pt(n,e,t))&&hi(f,t),(0|i)!=(0|(e=e+1|0)););v[r+136>>2]=n,nr(r)}}function Hr(r,n){var e,f,i=0,t=0,u=0,o=0,a=w(0);if(V=f=V-16|0,e=jo(r),v[e+20>>2]=9508,v[e+4>>2]=8776,v[e>>2]=9480,v[e+32>>2]=0,v[e+24>>2]=0,v[e+28>>2]=0,v[e+16>>2]=0,v[e+8>>2]=0,v[e+12>>2]=0,ya(r=e+4|0,n),da(e+20|0,n),v[f>>2]=0,wn(r,n,f),(0|n)>0)for(r=0;v[f+12>>2]=0,v[f+4>>2]=0,v[f+8>>2]=0,v[f>>2]=8744,V=u=V-16|0,(0|(i=v[e+24>>2]))!=v[e+28>>2]?(v[e+24>>2]=i+1,Nn(v[e+32>>2]+(i<<4)|0,f)):(o=Nn(u,f),a=w(w(d[e+24>>2])*w(1.75)),t=(i=w(g(a))<w(2147483648)?~~a:-2147483648)>>>0<=8?8:i,v[e+28>>2]=t,i=v[5280],t=0|Ta[v[v[i>>2]+16>>2]](i,v[e+32>>2],t<<4,8590,113),v[e+32>>2]=t,i=v[e+24>>2],v[e+24>>2]=i+1,Nn(t+(i<<4)|0,o),oo(o)),V=u+16|0,oo(f),(0|n)!=(0|(r=r+1|0)););return V=f+16|0,e}function Lr(r,n,e){var f,i=0,t=0,o=0,a=0,c=0,b=0,s=0,h=0,d=0,p=0,y=0,m=0,w=0,g=0,F=0;switch(f=v[r+4>>2],v[r+4>>2]=f+1,l[0|f]-1|0){case 0:return void yt(e,n);case 1:v[r+4>>2]=f+2,i=l[f+1|0],v[r+4>>2]=f+3,t=l[f+2|0],v[r+4>>2]=f+4,o=l[f+3|0],v[r+4>>2]=f+5,a=l[f+4|0],v[r+4>>2]=f+6,c=l[f+5|0],v[r+4>>2]=f+7,b=l[f+6|0],v[r+4>>2]=f+8,s=l[f+7|0],v[r+4>>2]=f+9,h=l[f+8|0],v[r+4>>2]=f+10,d=l[f+9|0],v[r+4>>2]=f+11,p=l[f+10|0],v[r+4>>2]=f+12,y=l[f+11|0],v[r+4>>2]=f+13,m=l[f+12|0],v[r+4>>2]=f+14,w=l[f+13|0],v[r+4>>2]=f+15,g=l[f+14|0],v[r+4>>2]=f+16,F=l[f+15|0],v[r+4>>2]=f+17,xr(e,n,(u(2,(t<<8|i<<16|o)<<8|a),k()),(u(2,(b<<8|c<<16|s)<<8|h),k()),(u(2,(p<<8|d<<16|y)<<8|m),k()),(u(2,l[f+16|0]|(g<<8|w<<16|F)<<8),k()))}}function Mr(r){return v[r>>2]=10520,Tt(r+4|0),v[r+52>>2]=0,v[r+56>>2]=0,v[r+48>>2]=10552,v[r+44>>2]=0,v[r+36>>2]=0,v[r+40>>2]=0,v[r+32>>2]=10536,v[r+28>>2]=0,v[r+20>>2]=0,v[r+24>>2]=0,v[r+16>>2]=9676,v[r+60>>2]=0,v[r+64>>2]=0,v[r+132>>2]=10632,v[r+128>>2]=0,v[r+120>>2]=0,v[r+124>>2]=0,v[r+116>>2]=10616,v[r+112>>2]=0,v[r+104>>2]=0,v[r+108>>2]=0,v[r+100>>2]=10600,v[r+96>>2]=0,v[r+88>>2]=0,v[r+92>>2]=0,v[r+84>>2]=10584,v[r+80>>2]=0,v[r+72>>2]=0,v[r+76>>2]=0,v[r+68>>2]=10568,v[r+160>>2]=0,v[r+152>>2]=0,v[r+156>>2]=0,v[r+144>>2]=0,v[r+148>>2]=0,v[r+136>>2]=0,v[r+140>>2]=0,Tt(r+164|0),Tt(r+176|0),v[r+192>>2]=0,v[r+196>>2]=0,v[r+188>>2]=10648,v[r+200>>2]=0,v[r+204>>2]=0,Tt(r+208|0),Tt(r+220|0),r}function _r(r,n){var e;return V=e=V-16|0,r=wt(r,n),v[r+52>>2]=0,v[r+56>>2]=0,v[r+48>>2]=1065353216,v[r+40>>2]=0,v[r+44>>2]=1065353216,v[r+32>>2]=0,v[r+36>>2]=0,v[r+20>>2]=10116,v[r>>2]=10092,v[r+24>>2]=0,v[r+28>>2]=0,v[r+100>>2]=8776,v[r+84>>2]=8776,v[r+60>>2]=0,v[r+64>>2]=0,v[r+68>>2]=0,v[r+72>>2]=0,v[r+76>>2]=0,v[r+80>>2]=0,v[r+112>>2]=0,v[r+104>>2]=0,v[r+108>>2]=0,v[r+96>>2]=0,v[r+88>>2]=0,v[r+92>>2]=0,Tt(r+116|0),v[r+136>>2]=0,v[r+140>>2]=0,v[r+128>>2]=0,v[r+132>>2]=0,v[r+156>>2]=1065353216,v[r+160>>2]=1065353216,v[r+148>>2]=1065353216,v[r+152>>2]=1065353216,v[r+144>>2]=9372,vf(r+144|0),v[e+12>>2]=0,wn(r+84|0,8,e+12|0),v[e+8>>2]=0,wn(r+100|0,8,e+8|0),V=e+16|0,r}function zr(r,n){return r=Te(r,n),v[r+76>>2]=0,v[r+80>>2]=0,v[r+64>>2]=9836,v[r>>2]=9812,v[r+68>>2]=0,v[r+72>>2]=0,v[r+152>>2]=9852,v[r+136>>2]=9852,v[r+120>>2]=8776,v[r+104>>2]=8776,v[r+84>>2]=0,v[r+88>>2]=0,v[r+92>>2]=0,v[r+96>>2]=0,v[r+100>>2]=0,v[r+164>>2]=0,v[r+156>>2]=0,v[r+160>>2]=0,v[r+148>>2]=0,v[r+140>>2]=0,v[r+144>>2]=0,v[r+132>>2]=0,v[r+124>>2]=0,v[r+128>>2]=0,v[r+116>>2]=0,v[r+108>>2]=0,v[r+112>>2]=0,Tt(r+168|0),v[r+196>>2]=0,v[r+200>>2]=0,v[r+188>>2]=0,v[r+192>>2]=0,v[r+180>>2]=0,v[r+184>>2]=0,v[r+216>>2]=1065353216,v[r+220>>2]=1065353216,v[r+208>>2]=1065353216,v[r+212>>2]=1065353216,v[r+204>>2]=9372,vf(r+204|0),v[r+232>>2]=0,i[r+228|0]=0,v[r+224>>2]=0,r}function xr(r,n,e,f,i,t){r|=0,n|=0,e=w(e),f=w(f),i=w(i),t=w(t);var u,o=w(0),a=w(0),c=w(0),b=w(0),k=0;if(u=v[r+16>>2],r=m(n,19),v[u+(r<<2)>>2]=1073741824,(n=r+1|0)>>>0<(r=r+19|0)>>>0)for(o=w(w(i-w(e+e))*w(.029999999329447746)),c=w(w(w(w(e-i)*w(3))+w(1))*w(.006000000052154064)),i=w(w(o+o)+c),a=w(w(t-w(f+f))*w(.029999999329447746)),b=w(w(w(w(f-t)*w(3))+w(1))*w(.006000000052154064)),t=w(w(a+a)+b),a=f=w(w(b*w(.1666666716337204))+w(w(f*w(.30000001192092896))+a)),o=e=w(w(c*w(.1666666716337204))+w(w(e*w(.30000001192092896))+o));p[(k=(n<<2)+u|0)>>2]=e,p[k+4>>2]=f,a=w(t+a),f=w(a+f),o=w(o+i),e=w(e+o),i=w(c+i),t=w(b+t),r>>>0>(n=n+2|0)>>>0;);}function Jr(r){r|=0;var n,e=w(0),f=w(0),i=w(0),t=w(0),u=w(0),o=w(0),a=w(0),c=w(0),b=w(0),k=w(0),s=w(0),l=w(0),h=w(0);i=p[r+52>>2],e=p[r+44>>2],u=p[r+76>>2],c=p[r+60>>2],b=p[r+68>>2],f=Ur(o=w(p[r+40>>2]*w(.01745329238474369))),t=p[r+56>>2],a=p[r+48>>2],k=p[r+80>>2],s=p[r+64>>2],o=Wr(o),l=p[r+32>>2],h=p[r+72>>2],u=w(e*w(i/u)),i=w(w(e*w(i*w(-.5)))+w(u*c)),n=v[r+96>>2],e=w(a*w(t*w(-.5))),t=w(a*w(t/k)),e=w(e+w(s*t)),a=p[r+36>>2],c=w(w(o*e)+a),u=w(w(b*u)+i),b=w(f*u),p[n+28>>2]=c+b,k=w(l+w(i*o)),t=w(w(h*t)+e),s=w(f*t),p[n+8>>2]=k-s,i=w(i*f),p[n+4>>2]=c+i,f=w(e*f),p[n>>2]=k-f,e=w(l+w(u*o)),p[n+24>>2]=e-f,f=w(a+w(t*o)),p[n+20>>2]=f+b,p[n+16>>2]=e-s,p[n+12>>2]=f+i}function Kr(r){r|=0;var n,e,f=0,t=0;return zr(n=ut(236),na(r)),f=v[r+68>>2],(e=v[n+72>>2])&&(!(t=v[n+68>>2])|(0|f)==(0|t)||Ta[0|e](t)),v[n+72>>2]=0,v[n+68>>2]=f,p[n+180>>2]=p[r+180>>2],p[n+184>>2]=p[r+184>>2],p[n+188>>2]=p[r+188>>2],p[n+192>>2]=p[r+192>>2],i[n+228|0]=l[r+228|0],v[n+232>>2]=v[r+232>>2],p[n+76>>2]=p[r+76>>2],p[n+80>>2]=p[r+80>>2],p[n+84>>2]=p[r+84>>2],p[n+88>>2]=p[r+88>>2],p[n+92>>2]=p[r+92>>2],p[n+96>>2]=p[r+96>>2],te(n+168|0,r+168|0),p[n+208>>2]=p[r+208>>2],p[n+212>>2]=p[r+212>>2],p[n+216>>2]=p[r+216>>2],p[n+220>>2]=p[r+220>>2],vf(n+204|0),v[n+56>>2]=v[r+56>>2],Kn(n,(f=v[r+100>>2])||r),lr(n),0|n}function Br(r,n,e){var f,i,t=0,u=0,o=w(0),a=w(0),c=0,b=w(0),k=w(0),s=w(0);V=f=V-16|0,i=v[r+60>>2],o=p[n+56>>2],b=p[n+52>>2],a=w(o-b),k=Er(p[n+76>>2],a);r:if(i){for(;;){if(u=v[v[r+68>>2]+(t<<2)>>2],(s=p[u+8>>2])<k)break r;if(o<s||(c=v[r+72>>2],lt(f,5,n,u),Zr(c+4|0,f)),(0|i)==(0|(t=t+1|0)))break}t=i}r:{n:{if(l[n+36|0]){if(a==w(0))break n;if(Er(p[n+72>>2],a)<k)break n;break r}if(!(e>=o)|!(p[n+60>>2]<o))break r}u=v[r+72>>2],lt(f,4,n,0),Zr(u+4|0,f)}if(t>>>0<i>>>0)for(;u=v[v[r+68>>2]+(t<<2)>>2],p[u+8>>2]<b||(c=v[r+72>>2],lt(f,5,n,u),Zr(c+4|0,f)),(0|i)!=(0|(t=t+1|0)););V=f+16|0}function Nr(){C(21705,21706,21707,0,11240,1662,11243,0,11243,0,4540,11245,1663),R(21705,2163,1,16864,11245,1664,1665,0),R(21705,1181,1,16864,11245,1664,1666,0),R(21705,1100,2,16868,11312,1667,1668,0),R(21705,1118,1,16864,11245,1664,1669,0),R(21705,6797,2,16876,11312,1670,1671,0),R(21705,3916,5,16896,13908,1672,1673,0),R(21705,1134,5,16928,13908,1674,1675,0),R(21705,6859,3,16948,13224,1676,1677,0),R(21705,6826,2,16960,13576,1678,1679,0),R(21705,3895,2,16272,13576,1680,1681,0),R(21705,6776,1,15064,11240,1682,1683,0),R(21705,5211,1,16968,11240,1684,1685,0),R(21705,1079,1,15836,11240,1382,1686,0),R(21705,1797,1,16972,11240,1687,1688,0)}function qr(r,n,e,f){var t,u=0,o=0,a=0,c=0,b=0;if(V=t=V-16|0,v[r>>2]=1032,v[r+4>>2]=1048,v[r+8>>2]=v[e+4>>2],u=v[e+8>>2],v[r+16>>2]=0,v[r+12>>2]=u,u&&(o=v[5280],c=r,b=0|Ta[v[v[o>>2]+12>>2]](o,u<<2,8590,210),v[c+16>>2]=b,o=v[r+8>>2]))for(u=0;v[(a=u<<2)+v[r+16>>2]>>2]=v[v[e+12>>2]+a>>2],(0|o)!=(0|(u=u+1|0)););if(p[r+32>>2]=f,v[r+24>>2]=0,v[r+28>>2]=0,v[r+20>>2]=8600,Ue(r+36|0,n),v[e+4>>2]>0)for(u=r+20|0,n=0;o=v[v[e+12>>2]+(n<<2)>>2],c=t,b=0|Ta[v[v[o>>2]+16>>2]](o),v[c+12>>2]=b,i[t+11|0]=1,_n(u,t+12|0,t+11|0),(0|(n=n+1|0))<v[e+4>>2];);return V=t+16|0,r}function Dr(r,n,e,f,i,u,o,a,c,k,s,l){var h=w(0),d=w(0),y=w(0),m=w(0),g=0,F=w(0),A=w(0),T=w(0),$=w(0);r:{if(!(r<w(9999999747378752e-21))&2143289344!=(0|(b(r),t(2)))){if(g=(k=v[k+12>>2])+(s<<2)|0,d=w(r*r),F=w(d*r),$=w(c*F),c=w(w(1)-r),y=w(c*r),h=w(y*w(3)),A=w(h*r),m=w(c*c),T=w(c*m),c=w(c*h),h=w($+w(w(o*A)+w(w(e*T)+w(c*i)))),p[g+4>>2]=h,a=w(w(a*F)+w(w(u*A)+w(w(n*T)+w(c*f)))),p[g>>2]=a,!l)break r;+r<.001?r=Or(w(i-e),w(f-n)):(r=w(y*i),e=w(h-w(w(o*d)+w(w(e*m)+w(r+r)))),r=w(y*f),r=Or(e,w(a-w(w(u*d)+w(w(n*m)+w(r+r))))))}else k=v[k+12>>2],p[(l=k+(s<<2)|0)>>2]=n,p[l+4>>2]=e,r=Or(w(i-e),w(f-n));p[8+((s<<2)+k|0)>>2]=r}}function Vr(r,n,e,f){var i,t=0,o=0,a=0,c=0,b=0,s=0;V=i=V-16|0,v[i+12>>2]=0,wn(f,n,i+12|0);r:if(e==w(1)){if(!((0|n)<=0))for(t=v[r+4>>2],a=v[f+12>>2],f=0;v[r+4>>2]=t+1,c=l[0|t],v[r+4>>2]=t+2,b=l[t+1|0],v[r+4>>2]=t+3,s=l[t+2|0],o=t+4|0,v[r+4>>2]=o,v[a+(f<<2)>>2]=l[t+3|0]|(s|b<<8|c<<16)<<8,t=o,(0|(f=f+1|0))!=(0|n););}else{if((0|n)<=0)break r;for(t=v[r+4>>2],a=v[f+12>>2],f=0;v[r+4>>2]=t+1,c=l[0|t],v[r+4>>2]=t+2,b=l[t+1|0],v[r+4>>2]=t+3,s=l[t+2|0],o=t+4|0,v[r+4>>2]=o,p[a+(f<<2)>>2]=(u(2,l[t+3|0]|(s|b<<8|c<<16)<<8),k()*e),t=o,(0|(f=f+1|0))!=(0|n););}V=i+16|0}function Zr(r,n){var e,f=0,i=w(0),t=0;V=e=V-16|0,(0|(f=v[r+4>>2]))!=v[r+8>>2]?(v[r+4>>2]=f+1,r=v[r+12>>2]+(f<<4)|0,v[r>>2]=8664,f=v[n+8>>2],v[r+4>>2]=v[n+4>>2],v[r+8>>2]=f,v[r+12>>2]=v[n+12>>2]):(v[e>>2]=8664,v[e+12>>2]=v[n+12>>2],t=v[n+8>>2],v[e+4>>2]=v[n+4>>2],v[e+8>>2]=t,i=w(w(f>>>0)*w(1.75)),n=(n=w(g(i))<w(2147483648)?~~i:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,f=v[5280],n=0|Ta[v[v[f>>2]+16>>2]](f,v[r+12>>2],n<<4,8590,113),v[r+12>>2]=n,f=r,r=v[r+4>>2],v[f+4>>2]=r+1,v[(r=n+(r<<4)|0)>>2]=8664,n=v[e+8>>2],v[r+4>>2]=v[e+4>>2],v[r+8>>2]=n,v[r+12>>2]=v[e+12>>2]),V=e+16|0}function Yr(r,n,e,f){var i,t=0,u=0,o=0,a=0,c=0;V=i=V-32|0,Xt(i+20|0,n),n=v[i+24>>2],t=v[i+20>>2];r:if(!(n>>>0>=(u=v[t+4>>2])>>>0))for(c=i+4|0;;){if(o=v[i+28>>2],a=v[t+12>>2],o>>>0>=d[4+(a+(n<<4)|0)>>2]){n:{e:{for(;;){if((0|u)==(0|(n=n+1|0)))break e;if(v[4+((n<<4)+a|0)>>2])break}t=n;break n}t=u}if(v[i+24>>2]=t,u=n>>>0>=u>>>0,o=0,n=t,u)break r}if(n=v[12+((n<<4)+a|0)>>2],v[i+28>>2]=o+1,n=n+m(o,20)|0,v[i>>2]=v[n>>2],t=Ue(c,n+4|0),n=v[n+16>>2],v[i+16>>2]=n,v[i>>2]==(0|e)&&Mn(r,n,f),gi(t),n=v[i+24>>2],t=v[i+20>>2],!(n>>>0<(u=v[t+4>>2])>>>0))break}V=i+32|0}function Xr(r,n,e,f,i){var t,u=w(0),o=w(0),a=w(0),c=w(0),b=w(0),k=w(0),s=w(0),l=w(0);b=p[n+100>>2],k=p[n+92>>2],s=p[n+96>>2],t=(f<<2)+e|0,a=p[n+112>>2],r=v[r+96>>2],u=p[r+24>>2],c=p[n+104>>2],l=p[n+108>>2],o=p[r+28>>2],p[t+4>>2]=a+w(w(u*c)+w(l*o)),p[t>>2]=b+w(w(u*k)+w(s*o)),f=((n=f+i|0)<<2)+e|0,u=p[r>>2],o=p[r+4>>2],p[f+4>>2]=a+w(w(c*u)+w(l*o)),p[f>>2]=b+w(w(u*k)+w(s*o)),f=((n=n+i|0)<<2)+e|0,u=p[r+8>>2],o=p[r+12>>2],p[f+4>>2]=a+w(w(c*u)+w(l*o)),p[f>>2]=b+w(w(u*k)+w(s*o)),n=(n+i<<2)+e|0,u=a,a=p[r+16>>2],o=w(c*a),c=p[r+20>>2],p[n+4>>2]=u+w(o+w(l*c)),p[n>>2]=b+w(w(a*k)+w(s*c))}function Qr(r){var n,e,f=w(0),i=w(0);if(b(r),(n=2147483647&(e=t(2)))>>>0>=1065353216)return w(1065353216==(0|n)?(0|e)>=0?0:3.141592502593994:w(0)/w(r-r));r:{if(n>>>0<=1056964607){if(f=w(1.570796251296997),n>>>0<847249409)break r;return w(w(w(w(7.549789415861596e-8)-w(r*Kf(w(r*r))))-r)+w(1.570796251296997))}if((0|e)<0)return r=w(w(r+w(1))*w(.5)),f=w(T(r)),r=w(w(1.570796251296997)-w(f+w(w(f*Kf(r))+w(-7.549789415861596e-8)))),w(r+r);f=w(w(w(1)-r)*w(.5)),u(2,-4096&(b(i=w(T(f))),t(2))),r=k(),r=w(w(w(i*Kf(f))+w(w(f-w(r*r))/w(i+r)))+r),f=w(r+r)}return f}function rn(r){var n=0,e=0,f=0,i=0;if(v[4+(r|=0)>>2]=8712,v[r>>2]=8696,n=v[r+44>>2])for(;;){if(f=v[v[r+52>>2]+(i<<2)>>2]){if(n=v[f+24>>2])for(;e=v[n+24>>2],Ta[v[v[n>>2]+4>>2]](n),n=e;);if(n=v[f+20>>2])for(;e=v[n+20>>2],Ta[v[v[n>>2]+4>>2]](n),n=e;);Ta[v[v[f>>2]+4>>2]](f),n=v[r+44>>2]}if(!((i=i+1|0)>>>0<n>>>0))break}return(n=v[r+72>>2])&&Ta[v[v[n>>2]+4>>2]](n),Cf(r+76|0),Fo(r+56|0),lo(r+40|0),kt(r+20|0),v[r+4>>2]=8728,(n=v[r+12>>2])&&(e=v[r+8>>2])&&Ta[0|n](e),0|r}function nn(r){var n,e,f=0,i=0,t=0,u=w(0),o=w(0),a=w(0),c=0;n=v[r+12>>2],u=p[n>>2],i=v[r+4>>2],o=w(w(p[((e=i-2|0)<<2)+n>>2]*p[n+4>>2])-w(p[((i<<2)+n|0)-4>>2]*u));r:{if(f=i-3|0){for(r=0;a=w(u*p[12+((t=r<<2)+n|0)>>2]),u=p[((r=r+2|0)<<2)+n>>2],o=w(o+w(a-w(p[(4|t)+n>>2]*u))),r>>>0<f>>>0;);if(i>>>0<2|o<w(0))break r;i=i>>>1|0}else if(i=1,o<w(0))break r;for(r=0;o=p[(f=(t=r<<2)+n|0)>>2],u=p[(t=(4|t)+n|0)>>2],c=f,f=(e-r<<2)+n|0,p[c>>2]=p[f>>2],p[t>>2]=p[f+4>>2],p[f+4>>2]=u,p[f>>2]=o,i>>>0>(r=r+2|0)>>>0;);}}function en(r){var n,e=0,f=0,i=0,t=0,u=0,o=0;V=n=V-32|0,v[(r|=0)>>2]=10800;r:if(e=v[r+24>>2])for(o=n+16|0;;){if(u=v[r+32>>2],d[4+(u+(f<<4)|0)>>2]<=t>>>0){n:{e:{for(;;){if((0|(f=f+1|0))==(0|e))break e;if(v[4+((f<<4)+u|0)>>2])break}i=f;break n}i=e}if(e=e>>>0<=f>>>0,t=0,f=i,e)break r}if(i=v[12+((f<<4)+u|0)>>2]+m(t,20)|0,v[n+12>>2]=v[i>>2],e=Ue(o,i+4|0),i=v[i+16>>2],v[n+28>>2]=i,_i(i),gi(e),t=t+1|0,!(f>>>0<(e=v[r+24>>2])>>>0))break}return no(r+52|0),mo(r+36|0),v[r+16>>2]=10784,Qu(r+20|0),gi(r+4|0),V=n+32|0,0|r}function fn(r,n,e,f,t){var u=0,o=w(0),a=0;return(u=v[r+28>>2])?(a=u-1|0,u=v[v[r+36>>2]+(a<<2)>>2],v[r+28>>2]=a):bn(u=ut(172)),v[u+40>>2]=0,v[u+44>>2]=0,i[u+37|0]=0,i[u+36|0]=f,v[u+16>>2]=e,v[u+32>>2]=n,v[u+48>>2]=0,v[u+52>>2]=0,o=p[e+32>>2],v[u+104>>2]=1065353216,v[u+84>>2]=2139095039,v[u+88>>2]=1065353216,v[u+76>>2]=-1082130432,v[u+80>>2]=-1082130432,v[u+68>>2]=0,v[u+72>>2]=0,v[u+60>>2]=-1082130432,v[u+64>>2]=-1082130432,p[u+56>>2]=o,v[u+92>>2]=1065353216,v[u+96>>2]=0,o=t?Ln(v[r+16>>2],v[t+16>>2],e):w(0),p[u+100>>2]=o,u}function tn(r,n,e){r|=0,n|=0,e|=0;var f=0,i=w(0),t=0,u=0,o=0;f=v[r+4>>2],v[r+4>>2]=n,d[r+8>>2]<n>>>0&&(i=w(w(n>>>0)*w(1.75)),n=(n=w(g(i))<w(2147483648)?~~i:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,t=v[5280],u=r,o=0|Ta[v[v[t>>2]+16>>2]](t,v[r+12>>2],m(n,12),8590,85),v[u+12>>2]=o,n=v[r+4>>2]);r:if(n>>>0<=f>>>0){if(n>>>0>=f>>>0)break r;for(;e=v[r+12>>2]+m(n,12)|0,Ta[v[v[e>>2]>>2]](e),(0|f)!=(0|(n=n+1|0)););}else for(;Ue(v[r+12>>2]+m(f,12)|0,e),(f=f+1|0)>>>0<d[r+4>>2];);}function un(r,n,e){var f,t,u,o=0,a=0;if(V=t=V-16|0,f=Ho(r),v[f+8>>2]=9260,v[f+4>>2]=n,v[f>>2]=9620,r=0,v[f+20>>2]=0,v[f+12>>2]=0,v[f+16>>2]=0,v[f+24>>2]=v[n+44>>2],i[f+28|0]=l[n+48|0],i[f+29|0]=l[n+49|0],p[f+32>>2]=p[n+52>>2],p[f+36>>2]=p[n+56>>2],n=wu(e,Qo(v[n+40>>2])),i[f+44|0]=0,v[f+40>>2]=n,ya(u=f+8|0,v[v[f+4>>2]+28>>2]),n=v[f+4>>2],v[n+28>>2])for(;o=t,a=wu(e,Qo(v[v[n+36>>2]+(r<<2)>>2])),v[o+12>>2]=a,Un(u,t+12|0),r=r+1|0,n=v[f+4>>2],r>>>0<d[n+28>>2];);return V=t+16|0,f}function on(r,n,e){var f,t,u,o=w(0),a=0,c=0;if(V=t=V-16|0,f=Ho(r),v[f+8>>2]=9260,v[f+4>>2]=n,v[f>>2]=10940,r=0,v[f+20>>2]=0,v[f+12>>2]=0,v[f+16>>2]=0,a=f,c=wu(e,Qo(v[n+40>>2])),v[a+24>>2]=c,p[f+28>>2]=p[n+44>>2],p[f+32>>2]=p[n+48>>2],p[f+36>>2]=p[n+52>>2],o=p[n+56>>2],i[f+44|0]=0,p[f+40>>2]=o,ya(u=f+8|0,v[v[f+4>>2]+28>>2]),n=v[f+4>>2],v[n+28>>2])for(;a=t,c=wu(e,Qo(v[v[n+36>>2]+(r<<2)>>2])),v[a+12>>2]=c,Un(u,t+12|0),r=r+1|0,n=v[f+4>>2],r>>>0<d[n+28>>2];);return V=t+16|0,f}function an(r,n,e,f,i){r|=0,n|=0,e|=0,f|=0,i=w(i);var t=0,u=0,o=w(0),a=w(0);r:if(t=xe(r,n)){for(;t=v[(u=t)+20>>2];);if(t=fn(r,n,e,f,u),v[u+20>>2]=t,!(i<=w(0)))break r;if((a=w(p[u+56>>2]-p[u+52>>2]))!=w(0)){o=p[u+72>>2],l[u+36|0]?(o=w(o/a),n=w(g(o))<w(2147483648)?~~o:-2147483648,i=w(w(a*w(n+1|0))+i)):i=w((o<a?a:o)+i),i=w(i-Ln(v[r+16>>2],v[u+16>>2],e));break r}i=p[u+72>>2]}else Tn(r,n,t=fn(r,n,e,f,0),1),mr(v[r+72>>2]);return p[t+68>>2]=i,0|t}function cn(r,n,e){r|=0,n|=0,e|=0;var f=0,i=0,t=w(0),u=0,o=0;if(i=v[r+4>>2],v[r+4>>2]=n,d[r+8>>2]<n>>>0&&(t=w(w(n>>>0)*w(1.75)),n=(n=w(g(t))<w(2147483648)?~~t:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,f=v[5280],u=r,o=0|Ta[v[v[f>>2]+16>>2]](f,v[r+12>>2],m(n,20),8590,85),v[u+12>>2]=o,n=v[r+4>>2]),n>>>0>i>>>0)for(;f=v[e+4>>2],n=v[r+12>>2]+m(i,20)|0,v[n>>2]=v[e>>2],v[n+4>>2]=f,v[n+16>>2]=v[e+16>>2],f=v[e+12>>2],v[n+8>>2]=v[e+8>>2],v[n+12>>2]=f,(i=i+1|0)>>>0<d[r+4>>2];);}function bn(r){return v[r+16>>2]=0,v[r+20>>2]=0,v[r+8>>2]=0,v[r+12>>2]=0,v[r+4>>2]=8648,v[r>>2]=8632,v[r+24>>2]=0,v[r+28>>2]=0,s[r+30>>1]=0,s[r+32>>1]=0,s[r+34>>1]=0,s[r+36>>1]=0,zi(r+40|0,0,48),v[r+92>>2]=0,v[r+96>>2]=0,v[r+88>>2]=1065353216,v[r+100>>2]=0,v[r+104>>2]=0,v[r+168>>2]=0,v[r+164>>2]=9,v[r+160>>2]=0,v[r+152>>2]=0,v[r+156>>2]=0,v[r+148>>2]=8776,v[r+144>>2]=0,v[r+136>>2]=0,v[r+140>>2]=0,v[r+132>>2]=8760,v[r+128>>2]=0,v[r+120>>2]=0,v[r+124>>2]=0,v[r+116>>2]=8744,v[r+108>>2]=0,v[r+112>>2]=2,r}function kn(r,n){return v[r+16>>2]=n,v[r+8>>2]=0,v[r+12>>2]=0,v[r+68>>2]=0,v[r+60>>2]=0,v[r+64>>2]=0,v[r+56>>2]=8824,v[r+52>>2]=0,v[r+44>>2]=0,v[r+48>>2]=0,v[r+40>>2]=8760,v[r+36>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[r+24>>2]=8760,v[r+20>>2]=8808,v[r+4>>2]=8712,v[r>>2]=8696,n=ut(32),i[n+28|0]=0,v[n+24>>2]=r+20,v[n+20>>2]=r,v[n+16>>2]=0,v[n+8>>2]=0,v[n+12>>2]=0,v[n+4>>2]=8792,v[n>>2]=8680,v[r+80>>2]=0,v[r+84>>2]=0,v[r+76>>2]=8600,v[r+72>>2]=n,i[r+88|0]=0,v[r+96>>2]=0,v[r+100>>2]=1065353216,v[r+92>>2]=9,r}function sn(r,n,e){var f,i,t=0,u=0,o=0,a=0;V=f=V-16|0,v[f+8>>2]=0,v[f+12>>2]=0,ue(r,n,f+8|0),i=v[f+12>>2],n=v[f+8>>2];r:{for(;;){if(r=0,(0|n)==(0|i))break r;if(r=l[0|n],n=n+1|0,58==(0|r))break}for(v[f+8>>2]=n,r=n;;){v[(a=(t<<3)+e|0)>>2]=n;n:{for(;;){if(u=r,(0|r)==(0|i)){r=n,o=t;break n}if(r=u+1|0,44==l[0|u])break}if(o=3,v[4+((t<<3)+e|0)>>2]=u-1,qn(a),n=r,3!=(0|(t=t+1|0)))continue}break}v[4+(n=(o<<3)+e|0)>>2]=i,v[n>>2]=r,qn(n),r=o+1|0}return V=f+16|0,r}function vn(r,n,e){r|=0,n|=0,e=w(e);var f,i=w(0),t=w(0),u=0,o=0,a=w(0);if(e=Au(e),f=v[r+16>>2],r=m(n,19),(i=p[f+(r<<2)>>2])==w(0))return w(e);if(i!=w(1)){if((u=r+1|0)>>>0<(o=r+19|0)>>>0){for(r=u;;){if(n=r,e<=(t=p[(r<<2)+f>>2]))return i=w(0),(0|n)!=(0|u)&&(a=p[(r=(n<<2)+f|0)-4>>2],i=p[r-8>>2]),w(w(a+w(w(w(e-i)*w(p[4+((n<<2)+f|0)>>2]-a))/w(t-i))));if(!(o>>>0>(r=n+2|0)>>>0))break}r=n+1|0}i=p[(r<<2)+f>>2],e=w(i+w(w(w(e-t)*w(w(1)-i))/w(w(1)-t)))}else e=w(0);return w(e)}function ln(r,n){var e,f,i,t=0,u=0,o=0,a=0;if(V=f=V-16|0,e=jo(r),v[e+24>>2]=9212,v[e+8>>2]=8776,r=0,v[e+4>>2]=0,v[e>>2]=9184,v[e+36>>2]=0,v[e+28>>2]=0,v[e+32>>2]=0,v[e+20>>2]=0,v[e+12>>2]=0,v[e+16>>2]=0,ya(t=e+8|0,n),d[8+(i=e+24|0)>>2]<n>>>0&&(v[i+8>>2]=n,u=v[5280],o=i,a=0|Ta[v[v[u>>2]+16>>2]](u,v[i+12>>2],m(n,12),8590,101),v[o+12>>2]=a),v[f+4>>2]=0,wn(t,n,f+4|0),(0|n)>0)for(;An(i,t=Tt(f+4|0)),gi(t),(0|n)!=(0|(r=r+1|0)););return V=f+16|0,e}function hn(r){var n,e,f,i,t,u,o,a,c=0,b=0;if(v[(r|=0)>>2]=10520,Rn(n=r+16|0),Rn(e=r+32|0),Rn(f=r+48|0),v[r+64>>2]=0,Rn(i=r+68|0),Rn(t=r+84|0),Rn(u=r+100|0),Rn(o=r+116|0),Rn(a=r+132|0),v[r+192>>2])for(;b=v[5280],Ta[v[v[b>>2]+20>>2]](b,v[v[r+200>>2]+(c<<2)>>2],8590,74),(c=c+1|0)>>>0<d[r+192>>2];);return gi(r+220|0),gi(r+208|0),Bu(r+188|0),gi(r+176|0),gi(r+164|0),uo(a),Nu(o),qu(u),Du(t),Zu(i),Yu(f),Xu(e),mo(n),gi(r+4|0),0|r}function dn(r,n){var e=0,f=0,i=0,t=0,u=w(0);if(ma(r,v[n+4>>2]+v[r+4>>2]|0),v[n+4>>2])for(;i=v[n+12>>2]+(t<<1)|0,(0|(e=v[r+4>>2]))!=v[r+8>>2]?s[v[r+12>>2]+(e<<1)>>1]=h[i>>1]:(i=h[i>>1],u=w(w(e>>>0)*w(1.75)),e=(e=w(g(u))<w(2147483648)?~~u:-2147483648)>>>0<=8?8:e,v[r+8>>2]=e,f=v[5280],f=0|Ta[v[v[f>>2]+16>>2]](f,v[r+12>>2],e<<1,8590,113),v[r+12>>2]=f,e=v[r+4>>2],s[f+(e<<1)>>1]=i),v[r+4>>2]=e+1,(t=t+1|0)>>>0<d[n+4>>2];);}function pn(r){var n=0,e=0,f=0,t=0,u=0;if(f=v[124+(r|=0)>>2])for(u=v[r+132>>2];n=v[(e<<2)+u>>2],i[n+88|0]=1,p[n+60>>2]=p[n+32>>2],t=v[n+40>>2],v[n+64>>2]=v[n+36>>2],v[n+68>>2]=t,t=v[n+48>>2],v[n+72>>2]=v[n+44>>2],v[n+76>>2]=t,t=v[n+56>>2],v[n+80>>2]=v[n+52>>2],v[n+84>>2]=t,(0|f)!=(0|(e=e+1|0)););if(e=v[r+108>>2])for(n=0;f=v[v[r+116>>2]+(n<<2)>>2],Ta[v[v[f>>2]+12>>2]](f),(0|e)!=(0|(n=n+1|0)););}function yn(r,n){var e=0,f=0;r:{if(3&((f=r)^n))e=l[0|n];else{if(3&n)for(;;){if(e=l[0|n],i[0|f]=e,!e)break r;if(f=f+1|0,!(3&(n=n+1|0)))break}if(!(~(e=v[n>>2])&e-16843009&-2139062144))for(;v[f>>2]=e,e=v[n+4>>2],f=f+4|0,n=n+4|0,!(e-16843009&~e&-2139062144););}if(i[0|f]=e,255&e)for(;e=l[n+1|0],i[f+1|0]=e,f=f+1|0,n=n+1|0,e;);}return r}function mn(r,n,e){var f,i=0,t=w(0),u=w(0),o=0;if(!(f=v[n+24>>2]))return 1;i=mn(r,f,e),p[f+60>>2]=p[f+64>>2],p[f+76>>2]=p[f+80>>2];r:{if((t=p[n+96>>2])>w(0)&&(u=p[n+100>>2])<=t){if(u!=w(0)&p[f+108>>2]!=w(0))break r;return v[n+24>>2]=v[f+24>>2],(o=v[f+24>>2])&&(v[o+28>>2]=n),p[n+104>>2]=p[f+104>>2],Bi(v[r+72>>2],f),i}p[f+72>>2]=w(e*p[f+88>>2])+p[f+72>>2],p[n+96>>2]=t+e,i=0}return i}function wn(r,n,e){r|=0,n|=0,e|=0;var f=0,i=w(0),t=0,u=0,o=0;if(f=v[r+4>>2],v[r+4>>2]=n,d[r+8>>2]<n>>>0&&(i=w(w(n>>>0)*w(1.75)),n=(n=w(g(i))<w(2147483648)?~~i:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,t=v[5280],u=r,o=0|Ta[v[v[t>>2]+16>>2]](t,v[r+12>>2],n<<2,8590,85),v[u+12>>2]=o,n=v[r+4>>2]),n>>>0>f>>>0)for(r=v[r+12>>2];p[r+(f<<2)>>2]=p[e>>2],(0|(f=f+1|0))!=(0|n););}function gn(r,n,e){r|=0,n|=0,e|=0;var f=0,i=w(0),t=0,u=0,o=0;if(f=v[r+4>>2],v[r+4>>2]=n,d[r+8>>2]<n>>>0&&(i=w(w(n>>>0)*w(1.75)),n=(n=w(g(i))<w(2147483648)?~~i:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,t=v[5280],u=r,o=0|Ta[v[v[t>>2]+16>>2]](t,v[r+12>>2],n<<2,8590,85),v[u+12>>2]=o,n=v[r+4>>2]),n>>>0>f>>>0)for(r=v[r+12>>2];v[r+(f<<2)>>2]=v[e>>2],(0|(f=f+1|0))!=(0|n););}function Fn(r,n,e){r|=0,n|=0,e|=0;var f=0,i=w(0),t=0,u=0,o=0;if(f=v[r+4>>2],v[r+4>>2]=n,d[r+8>>2]<n>>>0&&(i=w(w(n>>>0)*w(1.75)),n=(n=w(g(i))<w(2147483648)?~~i:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,t=v[5280],u=r,o=0|Ta[v[v[t>>2]+16>>2]](t,v[r+12>>2],n<<1,8590,85),v[u+12>>2]=o,n=v[r+4>>2]),n>>>0>f>>>0)for(r=v[r+12>>2];s[r+(f<<1)>>1]=h[e>>1],(0|(f=f+1|0))!=(0|n););}function An(r,n){var e,f=0,i=0,t=w(0);V=e=V-16|0,(0|(f=v[r+4>>2]))!=v[r+8>>2]?(v[r+4>>2]=f+1,Ue(v[r+12>>2]+m(f,12)|0,n)):(f=Ue(e+4|0,n),t=w(w(d[r+4>>2])*w(1.75)),n=(n=w(g(t))<w(2147483648)?~~t:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,i=v[5280],n=0|Ta[v[v[i>>2]+16>>2]](i,v[r+12>>2],m(n,12),8590,113),v[r+12>>2]=n,i=r,r=v[r+4>>2],v[i+4>>2]=r+1,Ue(n+m(r,12)|0,f),gi(f)),V=e+16|0}function Tn(r,n,e,f){var t,u,o=w(0);V=u=V-16|0,t=xe(r,n),v[v[r+52>>2]+(n<<2)>>2]=e,t&&(f&&(n=v[r+72>>2],lt(u,1,t,0),Zr(n+4|0,u)),v[e+24>>2]=t,v[t+28>>2]=e,v[e+96>>2]=0,v[t+24>>2]&&(o=p[t+100>>2])>w(0)&&(o=w(p[t+96>>2]/o),p[e+104>>2]=p[e+104>>2]*(o>w(1)?w(1):o)),v[t+152>>2]=0),n=v[r+72>>2],V=r=V-16|0,lt(r,0,e,0),Zr(n+4|0,r),i[v[n+20>>2]+88|0]=1,V=r+16|0,V=u+16|0}function $n(r,n,e){r|=0,n|=0,e|=0;var f=0,i=w(0),t=0,u=0,o=0;if(f=v[r+4>>2],v[r+4>>2]=n,d[r+8>>2]<n>>>0&&(i=w(w(n>>>0)*w(1.75)),n=(n=w(g(i))<w(2147483648)?~~i:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,t=v[5280],u=r,o=0|Ta[v[v[t>>2]+16>>2]](t,v[r+12>>2],n<<2,8590,85),v[u+12>>2]=o,n=v[r+4>>2]),n>>>0>f>>>0)for(;v[v[r+12>>2]+(f<<2)>>2]=v[e>>2],(0|(f=f+1|0))!=(0|n););}function In(){B(21536,6255),K(21645,4530,1,1,0),O(22296,3707,1,-128,127),O(22297,3700,1,-128,127),O(22298,3698,1,0,255),O(22299,1492,2,-32768,32767),O(21563,1483,2,0,65535),O(21543,1793,4,-2147483648,2147483647),O(21553,1784,4,0,-1),O(22300,4850,4,-2147483648,2147483647),O(21537,4841,4,0,-1),Tu(22301,2407,-2147483648,2147483647),Tu(22302,2406,0,-1),M(21542,2351,4),M(22303,5826,8),J(21686,4565)}function Cn(r,n,e){return v[r+8>>2]=e,v[r+4>>2]=n,v[r>>2]=10864,e=v[e+8>>2],v[r+28>>2]=1065353216,v[r+32>>2]=1065353216,v[r+20>>2]=1065353216,v[r+24>>2]=1065353216,v[r+16>>2]=9372,v[r+12>>2]=e,vf(r+16|0),v[r+40>>2]=0,v[r+44>>2]=0,v[r+36>>2]=9372,v[r+48>>2]=0,v[r+52>>2]=0,vf(r+36|0),n=l[n+64|0],v[r+80>>2]=0,v[r+72>>2]=0,v[r+76>>2]=0,v[r+68>>2]=8776,v[r+60>>2]=0,v[r+64>>2]=0,i[r+56|0]=n,Sn(r),r}function Pn(r,n,e){var f=0,i=0;r:{if(f=v[r+4>>2])for(;;){if(Gi(f+4|0,n))break r;if(!(f=v[f+20>>2]))break}return f=ut(28),v[f>>2]=8888,_t(f+4|0,0,0),v[f+20>>2]=0,v[f+24>>2]=0,i=v[n+8>>2],v[f+8>>2]=v[n+4>>2],v[f+12>>2]=i,p[f+16>>2]=p[e>>2],(n=v[r+4>>2])&&(v[n+24>>2]=f,v[f+20>>2]=n),v[r+4>>2]=f,void(v[r+8>>2]=v[r+8>>2]+1)}r=v[n+8>>2],v[f+8>>2]=v[n+4>>2],v[f+12>>2]=r,p[f+16>>2]=p[e>>2]}function En(r,n){var e=0,f=0,i=0,t=0,u=0;for(r=r+4|0;;){if(r=v[r>>2]){if(!n|!(f=v[r+28>>2]))e=-1,n>>>0>f>>>0||(e=(0|n)!=(0|f));else{i=n,u=0;r:if(e=l[0|f]){for(;;){if((t=l[0|i])&&((0|e)==(0|t)||(0|ho(e))==(0|ho(t)))){if(i=i+1|0,e=l[f+1|0],f=f+1|0,e)continue;break r}break}u=e}e=ho(255&u)-ho(l[0|i])|0}if(e)continue}break}return r}function On(r,n,e){var f=0,t=w(0),u=0,o=0,a=0;if(f=v[r+4>>2],v[r+4>>2]=n,d[r+8>>2]<n>>>0&&(t=w(w(n>>>0)*w(1.75)),n=(n=w(g(t))<w(2147483648)?~~t:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,u=v[5280],o=r,a=0|Ta[v[v[u>>2]+16>>2]](u,v[r+12>>2],n,8590,85),v[o+12>>2]=a,n=v[r+4>>2]),n>>>0>f>>>0)for(r=v[r+12>>2];i[r+f|0]=l[0|e],(0|(f=f+1|0))!=(0|n););}function Rn(r){var n=0,e=0,f=0,i=0,t=0,u=0,o=0,a=0;if((0|(e=v[r+4>>2]))>0)for(i=e;;){if(t=i-1|0,(n=v[v[r+12>>2]+(t<<2)>>2])&&(Ta[v[v[n>>2]+4>>2]](n),e=v[r+4>>2]),e=e-1|0,v[r+4>>2]=e,e>>>0>(n=t)>>>0)for(;f=v[r+12>>2],o=v[(u=f+(n<<2)|0)>>2],a=f,f=(n=n+1|0)<<2,v[u>>2]=v[a+f>>2],v[f+v[r+12>>2]>>2]=o,(0|n)!=(0|e););if(n=(0|i)>1,i=t,!n)break}}function Sn(r){r|=0;var n=0,e=w(0);n=Xo(v[r+4>>2]),p[r+20>>2]=p[n+4>>2],p[r+24>>2]=p[n+8>>2],p[r+28>>2]=p[n+12>>2],p[r+32>>2]=p[n+16>>2],vf(r+16|0),n=No(v[r+4>>2]);r:{if(v[n+4>>2]){if(v[r+60>>2]=0,(0|(n=Ef(v[r+12>>2],v[v[r+4>>2]+4>>2],n)))==v[r+60>>2])break r;v[r+60>>2]=n}else{if(!v[r+60>>2])break r;v[r+60>>2]=0}e=p[v[r+12>>2]+160>>2],v[r+72>>2]=0,p[r+64>>2]=e}}function Wn(r,n){var e=0,f=w(0),i=0;if((0|(e=v[r+4>>2]))==v[r+8>>2])return i=v[n>>2],f=w(w(e>>>0)*w(1.75)),n=(n=w(g(f))<w(2147483648)?~~f:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,e=v[5280],n=0|Ta[v[v[e>>2]+16>>2]](e,v[r+12>>2],n<<2,8590,113),v[r+12>>2]=n,e=r,r=v[r+4>>2],v[e+4>>2]=r+1,void(v[n+(r<<2)>>2]=i);v[r+4>>2]=e+1,v[v[r+12>>2]+(e<<2)>>2]=v[n>>2]}function Gn(r,n){var e=0,f=w(0),i=w(0);(0|(e=v[r+4>>2]))!=v[r+8>>2]?p[v[r+12>>2]+(e<<2)>>2]=p[n>>2]:(i=p[n>>2],f=w(w(e>>>0)*w(1.75)),n=(n=w(g(f))<w(2147483648)?~~f:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,e=v[5280],n=0|Ta[v[v[e>>2]+16>>2]](e,v[r+12>>2],n<<2,8590,113),v[r+12>>2]=n,e=v[r+4>>2],p[n+(e<<2)>>2]=i),v[r+4>>2]=e+1}function Un(r,n){var e=0,f=w(0),i=0;(0|(e=v[r+4>>2]))!=v[r+8>>2]?v[v[r+12>>2]+(e<<2)>>2]=v[n>>2]:(i=v[n>>2],f=w(w(e>>>0)*w(1.75)),n=(n=w(g(f))<w(2147483648)?~~f:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,e=v[5280],n=0|Ta[v[v[e>>2]+16>>2]](e,v[r+12>>2],n<<2,8590,113),v[r+12>>2]=n,e=v[r+4>>2],v[n+(e<<2)>>2]=i),v[r+4>>2]=e+1}function jn(r,n){var e,f=0,t=0;return e=v[r+4>>2],v[r+4>>2]=e+1,t=127&(f=i[0|e]),(0|f)>=0||(v[r+4>>2]=e+2,t|=(f=i[e+1|0])<<7&16256,(0|f)>=0||(v[r+4>>2]=e+3,t|=(f=i[e+2|0])<<14&2080768,(0|f)>=0||(v[r+4>>2]=e+4,t|=(f=i[e+3|0])<<21&266338304,(0|f)>=0||(v[r+4>>2]=e+5,t=l[e+4|0]<<28|t)))),n?t:0-(1&t)^t>>>1}function Hn(r,n){r:if((0|n)>=1024){if(r*=898846567431158e293,n>>>0<2047){n=n-1023|0;break r}r*=898846567431158e293,n=((0|n)>=3069?3069:n)-2046|0}else(0|n)>-1023||(r*=2004168360008973e-307,n>>>0>4294965304?n=n+969|0:(r*=2004168360008973e-307,n=((0|n)<=-2960?-2960:n)+1938|0));return u(0,0),u(1,n+1023<<20),r*+o()}function Ln(r,n,e){r|=0,n|=0,e|=0;var f,i=w(0);V=f=V-16|0,_t(f+4|0,n,e);r:{n:{e:{if(e=v[r+16>>2])for(;;){if(Gi(e+4|0,f+4|0))break e;if(!(e=v[e+20>>2]))break}r=r+8|0;break n}if(!(e=v[r+16>>2]))break r;for(;;){if(!Gi(e+4|0,f+4|0)){if(e=v[e+20>>2])continue;break r}break}r=e+16|0}i=p[r>>2]}return V=f+16|0,w(i)}function Mn(r,n,e){var f=0,i=0;if(n&&Si(0|Ta[v[v[n>>2]+8>>2]](n),21212))if(i=v[n+24>>2])for(e=0;;){if(f=e<<2,(e=e+1|0)>>>0<(f=e+v[f+v[n+32>>2]>>2]|0)>>>0){for(;Qf(r,v[v[r+20>>2]+(v[v[n+32>>2]+(e<<2)>>2]<<2)>>2]),(0|f)!=(0|(e=e+1|0)););e=f}if(!(e>>>0<i>>>0))break}else Qf(r,e)}function _n(r,n,e){var f=0,t=0;r:{if(f=v[r+4>>2])for(t=v[n>>2];;){if(v[f+4>>2]==(0|t))break r;if(!(f=v[f+12>>2]))break}return f=ut(20),v[f+12>>2]=0,v[f+16>>2]=0,v[f>>2]=8616,v[f+4>>2]=v[n>>2],i[f+8|0]=l[0|e],(n=v[r+4>>2])&&(v[n+16>>2]=f,v[f+12>>2]=n),v[r+4>>2]=f,void(v[r+8>>2]=v[r+8>>2]+1)}v[f+4>>2]=t,i[f+8|0]=l[0|e]}function zn(r,n){var e=0,f=0,i=0,t=0;if(v[r>>2]=9212,v[r+4>>2]=v[n+4>>2],e=v[n+8>>2],v[r+12>>2]=0,v[r+8>>2]=e,e&&(f=v[5280],i=r,t=0|Ta[v[v[f>>2]+12>>2]](f,m(e,12),8590,210),v[i+12>>2]=t,v[r+4>>2]))for(e=0;Ue((f=m(e,12))+v[r+12>>2]|0,f+v[n+12>>2]|0),(e=e+1|0)>>>0<d[r+4>>2];);return r}function xn(r,n,e){var f=0,i=0,t=0,u=0;if(v[r>>2]=e,v[r+4>>2]=v[n+4>>2],e=v[n+8>>2],v[r+12>>2]=0,v[r+8>>2]=e,e&&(f=v[5280],t=r,u=0|Ta[v[v[f>>2]+12>>2]](f,e<<2,8590,210),v[t+12>>2]=u,f=v[r+4>>2]))for(e=0;v[(i=e<<2)+v[r+12>>2]>>2]=v[v[n+12>>2]+i>>2],(0|f)!=(0|(e=e+1|0)););return r}function Jn(r,n){var e=0,f=0,i=0,t=0;for(v[n>>2]=0;;){r:{n:{e:{if(((f=l[r+i|0])-58&255)>>>0<=245){if(f-65>>>0<6|f-97>>>0<6)break e;return void(v[n>>2]=0)}e<<=4,t=-48;break n}if(e<<=4,v[n>>2]=e,t=-55,!((f-65&255)>>>0<6)&&(t=-87,(f-97&255)>>>0>5))break r}e|=(f<<24>>24)+t,v[n>>2]=e}if(4==(0|(i=i+1|0)))break}}function Kn(r,n){n|=0;var e=0;v[100+(r|=0)>>2]=n,n&&(v[r+24>>2]=0,Vf(r+20|0,n+20|0),v[r+40>>2]=0,Df(r+36|0,n+36|0),e=v[n+52>>2],v[r+124>>2]=0,v[r+52>>2]=e,Df(r+120|0,n+120|0),v[r+140>>2]=0,dn(r+136|0,n+136|0),e=v[n+224>>2],v[r+156>>2]=0,v[r+224>>2]=e,dn(r+152|0,n+152|0),p[r+196>>2]=p[n+196>>2],p[r+200>>2]=p[n+200>>2])}function Bn(r,n){var e=0,f=0,i=0,t=0;if(v[r>>2]=8776,v[r+4>>2]=v[n+4>>2],e=v[n+8>>2],v[r+12>>2]=0,v[r+8>>2]=e,e&&(f=v[5280],e=0|Ta[v[v[f>>2]+12>>2]](f,e<<2,8590,210),v[r+12>>2]=e,f=v[r+4>>2]))for(t=v[n+12>>2],n=0;p[e+(i=n<<2)>>2]=p[t+i>>2],(0|f)!=(0|(n=n+1|0)););return r}function Nn(r,n){var e=0,f=0,i=0,t=0;if(v[r>>2]=8744,v[r+4>>2]=v[n+4>>2],e=v[n+8>>2],v[r+12>>2]=0,v[r+8>>2]=e,e&&(f=v[5280],e=0|Ta[v[v[f>>2]+12>>2]](f,e<<2,8590,210),v[r+12>>2]=e,f=v[r+4>>2]))for(t=v[n+12>>2],n=0;v[e+(i=n<<2)>>2]=v[t+i>>2],(0|f)!=(0|(n=n+1|0)););return r}function qn(r){var n=0,e=0,f=0,i=0;e=v[r+4>>2];r:{n:if(n=v[r>>2],co(l[0|n])){for(;;){if(n>>>0>=e>>>0)break n;if(f=n+1|0,v[r>>2]=f,i=l[n+1|0],n=f,!co(i))break}break r}f=n}if((0|e)!=(0|f)){for(;e=(n=e)-1|0,v[r+4>>2]=e,!(e>>>0<f>>>0||13!=l[0|e]););v[r+4>>2]=n}}function Dn(r,n,e,f){return v[r+4>>2]=n,v[r>>2]=10880,Ue(r+8|0,e),v[r+36>>2]=1065353216,v[r+40>>2]=1065353216,v[r+28>>2]=1065353216,v[r+32>>2]=1065353216,v[r+24>>2]=9372,v[r+20>>2]=f,vf(r+24|0),v[r+48>>2]=0,v[r+52>>2]=0,v[r+44>>2]=9372,v[r+56>>2]=0,v[r+60>>2]=0,vf(r+44|0),i[r+64|0]=0,Tt(r+68|0),v[r+80>>2]=0,r}function Vn(r,n,e,f){var i,t=0,u=0,o=0;return V=i=V-16|0,o=1,(t=xe(r,n))?p[t+80>>2]!=w(-1)?If(r,t):(v[v[r+52>>2]+(n<<2)>>2]=v[t+24>>2],u=v[r+72>>2],o=0,lt(i,1,t,0),Zr(u+4|0,i),Bi(v[r+72>>2],t),If(r,t),t=v[t+24>>2]):t=0,Tn(r,u=n,n=fn(r,n,e,f,t),o),mr(v[r+72>>2]),V=i+16|0,n}function Zn(r,n,e,f,i){var t,u,o,a=0,c=0;if(v[r+4>>2]=0,v[r>>2]=16984,Tt(r+8|0),u=Tt(r+20|0),t=Wt(16),v[r+4>>2]=t,r=Wt((o=je(n,0,24,0),Z?-1:o)),n)for(c=r+m(n,24)|0,a=r;v[a+20>>2]=0,(0|c)!=(0|(a=a+24|0)););v[t+8>>2]=n,v[t>>2]=r,v[t+12>>2]=f,v[t+4>>2]=e,te(u,i)}function Yn(r,n){n|=0;var e=0,f=0;if(!(d[44+(r|=0)>>2]<=n>>>0)&&(e=v[v[r+52>>2]+(n<<2)>>2])){if(Bi(v[r+72>>2],e),If(r,e),n=v[e+24>>2])for(f=e;Bi(v[r+72>>2],n),v[f+24>>2]=0,v[f+28>>2]=0,f=n,n=v[n+24>>2];);v[v[r+52>>2]+(v[e+32>>2]<<2)>>2]=0,mr(v[r+72>>2])}}function Xn(r,n,e){var f=0,i=0,t=0;if(!(d[r+8>>2]<=n>>>0)&&(i=v[r+16>>2]+(n<<4)|0,v[i+4>>2])){r:{for(;;){if(ei(4+(v[i+12>>2]+m(f,20)|0)|0,e))break r;if(!((f=f+1|0)>>>0<d[i+4>>2]))break}return 0}(0|f)<0||(t=v[16+(v[12+(v[r+16>>2]+(n<<4)|0)>>2]+m(f,20)|0)>>2])}return t}function Qn(r,n,e,f,i,t){r|=0,n=w(n),e=w(e),f=w(f),i=w(i),t|=0;var u=0;r=v[r+112>>2],t?(p[r+28>>2]=e,p[r+24>>2]=n,p[r+20>>2]=i,p[r+16>>2]=n,p[r+4>>2]=e,p[r>>2]=f,u=r+8|0,t=3):(p[r+28>>2]=e,p[r+24>>2]=f,p[r+20>>2]=e,p[r+16>>2]=n,p[r+12>>2]=i,p[r+8>>2]=n,u=r,t=1),p[u>>2]=f,p[r+(t<<2)>>2]=i}function re(r,n,e,f){var i,t,u=w(0),o=w(0),a=w(0);return e=v[e+12>>2],f=v[f+12>>2],i=e+(v[f+((r+1|0)%(0|n)<<2)>>2]<<3)|0,t=e+(v[f+(r<<2)>>2]<<3)|0,u=p[t+4>>2],r=e+(v[f+(((r+n|0)-1|0)%(0|n)<<2)>>2]<<3)|0,o=p[r+4>>2],a=p[i+4>>2],!(w(w(p[i>>2]*w(u-o))+w(w(p[r>>2]*w(a-u))+w(p[t>>2]*w(o-a))))>=w(0))}function ne(r,n,e,f){return v[r+4>>2]=n,v[r>>2]=9276,Ue(r+8|0,e),v[r+24>>2]=0,v[r+28>>2]=0,v[r+20>>2]=f,v[r+32>>2]=0,v[r+36>>2]=0,v[r+48>>2]=0,v[r+52>>2]=0,v[r+40>>2]=1065353216,v[r+44>>2]=1065353216,i[r+53|0]=0,i[r+54|0]=0,i[r+55|0]=0,i[r+56|0]=0,i[r+57|0]=0,i[r+58|0]=0,i[r+59|0]=0,i[r+60|0]=0,r}function ee(r){var n=0,e=0,f=0;r:{n:if(3&(n=r)){if(!l[0|r])return 0;for(;;){if(!(3&(n=n+1|0)))break n;if(!l[0|n])break}break r}for(;e=n,n=n+4|0,!(~(f=v[e>>2])&f-16843009&-2139062144););for(;e=(n=e)+1|0,l[0|n];);}return n-r|0}function fe(r,n){var e,f,i=0,t=0,u=0,o=0,a=0;if(V=e=V-16|0,f=jn(r,1),s[e+14>>1]=0,Fn(n,f,e+14|0),(0|f)>0)for(i=v[r+4>>2],a=v[n+12>>2];v[r+4>>2]=i+1,u=(t<<1)+a|0,o=l[0|i]<<8,s[u>>1]=o,n=i+2|0,v[r+4>>2]=n,s[u>>1]=l[i+1|0]|o,i=n,(0|(t=t+1|0))!=(0|f););V=e+16|0}function ie(r,n,e){var f=0,i=0,t=w(0),u=w(0),o=w(0);return i=je(v[5576],v[5577],1284865837,1481765933),f=Z,f=(i=i+1|0)?f:f+1|0,v[5576]=i,v[5577]=f,t=w(w(f>>>1|0)*w(4.656612873077393e-10)),o=w(e-r),u=w(n-r),t<=w(o/u)?w(w(T(w(o*w(u*t))))+r):w(n-w(T(w(w(n-e)*w(u*w(w(1)-t))))))}function te(r,n){var e=0,f=0;if((0|r)!=(0|n)){if((e=v[r+8>>2])&&(f=v[5280],Ta[v[v[f>>2]+20>>2]](f,e,8590,106)),!v[n+8>>2])return v[r+4>>2]=0,void(v[r+8>>2]=0);v[r+4>>2]=v[n+4>>2],e=r,r=v[5280],r=0|Ta[v[v[r>>2]+12>>2]](r,v[n+4>>2]+1|0,8590,113),v[e+8>>2]=r,Ri(r,v[n+8>>2],v[n+4>>2]+1|0)}}function ue(r,n,e){var f=0,i=0;if((0|(f=v[r>>2]))==(0|n))return 0;v[e>>2]=f,i=n;r:if((0|(f=v[r>>2]))!=(0|n)){for(;;){if(i=f,10==l[0|f])break r;if(f=f+1|0,v[r>>2]=f,(0|n)==(0|f))break}i=n}return v[e+4>>2]=i,qn(e),(0|(e=n))!=(0|(n=v[r>>2]))&&(v[r>>2]=n+1),1}function oe(r,n,e){var f,t,u=0,o=0;if(V=f=V-272|0,t=yn(f+16|0,n),e){u=ee(n),n=ee(n=u+t|0)+n|0;r:if(u=255-u|0)for(;;){if(!(o=l[0|e]))break r;if(i[0|n]=o,n=n+1|0,e=e+1|0,!(u=u-1|0))break}i[0|n]=0}te(n=r+24|0,r=ht(f+4|0,t,0)),gi(r),V=f+272|0}function ae(r){var n=0,e=0,f=0;if(v[44+(r|=0)>>2]=0,e=v[r+28>>2]){for(f=r+40|0;Un(f,v[r+36>>2]+(n<<2)|0),(0|e)!=(0|(n=n+1|0)););if(e=v[r+28>>2])for(n=0;Sn(v[v[r+36>>2]+(n<<2)>>2]),(0|e)!=(0|(n=n+1|0)););}}function ce(r){var n=0,e=0,f=0;if(v[(r|=0)>>2]=8904,v[r+36>>2]&&(e=v[r+8>>2]))for(;f=v[r+36>>2],Ta[v[v[f>>2]+12>>2]](f,v[v[v[r+16>>2]+(n<<2)>>2]+8>>2]),(0|e)!=(0|(n=n+1|0)););return Rn(n=r+4|0),Rn(e=r+20|0),bo(e),ko(n),0|r}function be(r){var n=0,e=0;if(v[(r|=0)>>2]=9212,n=v[r+4>>2])for(;n=v[r+12>>2]+m(~e+n|0,12)|0,Ta[v[v[n>>2]>>2]](n),(n=v[r+4>>2])>>>0>(e=e+1|0)>>>0;);return v[r+4>>2]=0,(n=v[r+12>>2])&&(e=v[5280],Ta[v[v[e>>2]+20>>2]](e,n,8590,219)),0|r}function ke(r){var n=0;return v[r+12>>2]=0,(n=v[r+24>>2])&&Ta[v[v[n>>2]+4>>2]](n),(n=v[r+20>>2])&&Ta[v[v[n>>2]+4>>2]](n),(n=v[r+16>>2])&&Ta[v[v[n>>2]+4>>2]](n),(n=v[r+8>>2])&&Ta[v[v[n>>2]+4>>2]](n),(n=v[r+32>>2])&&ar(Uo(n)),zu(r+80|0),Uu(r- -64|0),r}function se(r,n,e,f,i){n|=0,e|=0,f|=0,i|=0;var t,u,o=0;return V=t=V-32|0,n=((o=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&o?v[v[n>>2]+r>>2]:r,o=n,u=e,n=$e(t+20|0,f+4|0,v[f>>2],0),e=$e(t+8|0,i+4|0,v[i>>2],0),r=0|Ta[0|r](o,u,n,e),gi(e),gi(n),V=t+32|0,0|r}function ve(r,n){var e=0,f=0;if((0|(e=v[r+8>>2]))!=(0|n)){if(e&&(f=v[5280],Ta[v[v[f>>2]+20>>2]](f,e,8590,122)),!n)return v[r+4>>2]=0,void(v[r+8>>2]=0);e=ee(n),v[r+4>>2]=e,f=v[5280],e=0|Ta[v[v[f>>2]+12>>2]](f,e+1|0,8590,129),v[r+8>>2]=e,Ri(e,n,v[r+4>>2]+1|0)}}function le(r){var n=0,e=0;if(v[(r|=0)>>2]=10832,n=v[r+4>>2])for(;gi(4+(v[r+12>>2]+m(~e+n|0,20)|0)|0),(e=e+1|0)>>>0<(n=v[r+4>>2])>>>0;);return v[r+4>>2]=0,(n=v[r+12>>2])&&(e=v[5280],Ta[v[v[e>>2]+20>>2]](e,n,8590,219)),0|r}function he(r,n){var e=0;if(v[r>>2]=n,n=v[r+4>>2])for(;n=v[r+12>>2]+(~e+n<<4)|0,Ta[v[v[n>>2]>>2]](n),(n=v[r+4>>2])>>>0>(e=e+1|0)>>>0;);return v[r+4>>2]=0,(n=v[r+12>>2])&&(e=v[5280],Ta[v[v[e>>2]+20>>2]](e,n,8590,219)),r}function de(r,n){var e;return V=e=V-16|0,r=jo(r),v[r+20>>2]=8824,v[r+4>>2]=8776,v[r>>2]=9556,v[r+32>>2]=0,v[r+24>>2]=0,v[r+28>>2]=0,v[r+16>>2]=0,v[r+8>>2]=0,v[r+12>>2]=0,v[e+12>>2]=0,wn(r+4|0,n,e+12|0),v[e+8>>2]=0,$n(r+20|0,n,e+8|0),V=e+16|0,r}function pe(r,n){var e,f,i=0,t=0;V=e=V-16|0,v[e+12>>2]=n;r:{if(f=v[r+8>>2]){if(t=v[r+16>>2],v[t>>2]==(0|n))break r;for(;(0|f)!=(0|(i=i+1|0))&v[(i<<2)+t>>2]!=(0|n););if(i>>>0<f>>>0)break r}Un(r+4|0,e+12|0)}V=e+16|0}function ye(r,n){var e,f=w(0);return V=e=V-16|0,f=w(-1),ee(r)>>>1>>>0<=n>>>0||(r=(n<<1)+r|0,i[e+13|0]=l[0|r],r=l[r+1|0],i[e+15|0]=0,i[e+14|0]=r,r=gr(e+13|0,e+8|0,16,-1),l[v[e+8>>2]]||(f=w(w(0|r)/w(255)))),V=e+16|0,f}function me(r,n,e,f){return r=Ho(r),v[r+16>>2]=9260,v[r+12>>2]=f,v[r+8>>2]=e,v[r+4>>2]=n,v[r>>2]=9228,zi(r+20|0,0,69),s[r+116>>1]=0,v[r+108>>2]=1065353216,v[r+112>>2]=0,v[r+100>>2]=0,v[r+104>>2]=0,v[r+92>>2]=1065353216,v[r+96>>2]=0,De(r),r}function we(r,n,e,f,i){var t=w(0),u=w(0),o=w(0),a=0,c=w(0);f=v[f+12>>2]+(i<<2)|0,n=v[n+12>>2]+(e<<2)|0,u=p[n+12>>2],o=p[n+8>>2],t=Or(w(u-p[n+4>>2]),w(o-p[n>>2])),p[f+8>>2]=t,a=f,c=w(u+w(r*Ur(t))),p[a+4>>2]=c,a=f,c=w(o+w(r*Wr(t))),p[a>>2]=c}function ge(r,n,e,f,i){r|=0,n|=0,e|=0,f|=0,i=w(i);var t,u=0;V=t=V-32|0,n=((u=v[r+4>>2])>>1)+n|0,r=v[r>>2],r=1&u?v[v[n>>2]+r>>2]:r,u=n,n=$e(t+20|0,e+4|0,v[e>>2],0),e=$e(t+8|0,f+4|0,v[f>>2],0),Ta[0|r](u,n,e,i),gi(e),gi(n),V=t+32|0}function Fe(r){var n=0,e=0;for(n=v[r+4>>2];n&&(e=v[n>>2],Fe(n),ar(n),n=e););return(n=v[r+16>>2])&&(e=v[5280],Ta[v[v[e>>2]+20>>2]](e,n,8590,164)),(n=v[r+28>>2])&&(e=v[5280],Ta[v[v[e>>2]+20>>2]](e,n,8590,168)),r}function Ae(r,n){return v[r>>2]=10800,Ue(r+4|0,n),v[r+32>>2]=0,v[r+24>>2]=0,v[r+28>>2]=0,v[r+20>>2]=10816,v[r+16>>2]=10784,v[r- -64>>2]=0,v[r+56>>2]=0,v[r+60>>2]=0,v[r+52>>2]=10848,v[r+48>>2]=0,v[r+40>>2]=0,v[r+44>>2]=0,v[r+36>>2]=9676,r}function Te(r,n){return r=wt(r,n),v[r+36>>2]=8776,v[r+20>>2]=10768,v[r>>2]=11160,v[r+40>>2]=0,v[r+44>>2]=0,v[r+32>>2]=0,v[r+24>>2]=0,v[r+28>>2]=0,v[r+48>>2]=0,v[r+52>>2]=0,v[r+56>>2]=r,n=v[5366],v[5366]=n+1,v[r+60>>2]=n<<11&134215680,r}function $e(r,n,e,f){return v[r>>2]=10896,n?(v[r+4>>2]=e,f?(v[r+8>>2]=n,r):(f=v[5280],e=0|Ta[v[v[f>>2]+12>>2]](f,e+1|0,8590,48),v[r+8>>2]=e,i[e+v[r+4>>2]|0]=0,Ri(v[r+8>>2],n,v[r+4>>2]),r)):(v[r+4>>2]=0,v[r+8>>2]=0,r)}function Ie(r,n,e){var f=0,i=0,t=0;if(!e)return 0;r:if(f=l[0|r]){for(;;){if(!((0|(i=l[0|n]))!=(0|f)|!i)&&(e=e-1|0)){if(n=n+1|0,f=l[r+1|0],r=r+1|0,f)continue;break r}break}t=f}return(255&t)-l[0|n]|0}function Ce(r,n,e,f){if(r|=0,n|=0,e=w(e),f|=0,p[v[r+16>>2]+(n<<2)>>2]=e,r=v[r+32>>2]+(n<<4)|0,v[r+4>>2]=0,n=0,ya(r,v[f+4>>2]+v[r+4>>2]|0),v[f+4>>2])for(;Un(r,v[f+12>>2]+(n<<2)|0),(n=n+1|0)>>>0<d[f+4>>2];);}function Pe(r,n){var e,f,i,t,u,o=0;return o=(e=v[r+4>>2])+(f=v[n+4>>2])|0,v[r+4>>2]=o,u=v[n+8>>2],i=v[5280],t=v[r+8>>2],o=0|Ta[v[v[i>>2]+16>>2]](i,t,o+1|0,8590,150),v[r+8>>2]=o,Ri(o+e|0,(0|u)==(0|t)?o:v[n+8>>2],f+1|0),r}function Ee(r,n,e,f){var i=w(0),t=w(0),u=w(0),o=0,a=w(0);n=v[n+12>>2],t=p[n+4>>2],e=v[e+12>>2]+(f<<2)|0,u=p[n>>2],i=Or(w(p[n+12>>2]-t),w(p[n+8>>2]-u)),p[e+8>>2]=i,o=e,a=w(t+w(r*Ur(i))),p[o+4>>2]=a,o=e,a=w(u+w(r*Wr(i))),p[o>>2]=a}function Oe(r,n,e,f){n|=0,e|=0,f|=0;var i,t=0;V=i=V-32|0,n=((t=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&t?v[v[n>>2]+r>>2]:r,t=n,n=$e(i+20|0,e+4|0,v[e>>2],0),e=$e(i+8|0,f+4|0,v[f>>2],0),Ta[0|r](t,n,e),gi(e),gi(n),V=i+32|0}function Re(r){var n=0;r:{n:if(n=En(r,4381)){if(r=v[n+16>>2])return!uf(r,5008);r=1;e:switch(v[n+8>>2]){case 0:case 2:return 0;case 1:break r;case 3:break e;default:break n}return p[n+24>>2]!=w(0)}r=0}return r}function Se(r,n,e){var f=0,i=0,t=0,u=0,o=0;if(f=(v[r+4>>2]/(0|e)|0)-2|0){for(o=v[r+12>>2],r=0,i=f;i=(r=(u=p[(m(i=1+(t=i>>>1|0)|0,e)<<2)+o>>2]<=n)?i:r)+(f=u?f:t)|0,(0|r)!=(0|f););e=m(r+1|0,e)}return e}function We(r){var n,e=0;return v[64+(r|=0)>>2]=9836,v[r>>2]=9812,gi(r+168|0),Ao(r+152|0),Ao(r+136|0),To(r+120|0),To(r+104|0),v[r+64>>2]=8728,(n=v[r+72>>2])&&(e=v[r+68>>2])&&Ta[0|n](e),0|st(r)}function Ge(r,n,e,f,i,t,u,o,a,c){r|=0,n|=0,e=w(e),f=w(f),i=w(i),t=w(t),u=w(u),o=w(o),a=w(a),c=w(c),r=v[r+32>>2]+(n<<5)|0,p[r>>2]=e,p[r+4>>2]=f,p[r+8>>2]=i,p[r+12>>2]=t,p[r+16>>2]=u,p[r+20>>2]=o,p[r+24>>2]=a,p[r+28>>2]=c}function Ue(r,n){var e=0;return v[r>>2]=10896,v[n+8>>2]?(v[r+4>>2]=v[n+4>>2],e=v[5280],e=0|Ta[v[v[e>>2]+12>>2]](e,v[n+4>>2]+1|0,8590,67),v[r+8>>2]=e,Ri(e,v[n+8>>2],v[n+4>>2]+1|0),r):(v[r+4>>2]=0,v[r+8>>2]=0,r)}function je(r,n,e,f){var i,t,u,o,a=0,c=0;return o=m(a=e>>>16|0,c=r>>>16|0),a=(65535&(c=((u=m(i=65535&e,t=65535&r))>>>16|0)+m(c,i)|0))+m(a,t)|0,Z=(m(n,e)+o|0)+m(r,f)+(c>>>16)+(a>>>16)|0,65535&u|a<<16}function He(r,n){var e,f=0,i=0,t=0,u=0;if(!(f=v[r+4>>2]-2|0))return 1;for(e=v[r+12>>2],r=0,i=f;i=(r=(u=p[((i=1+(t=i>>>1|0)|0)<<2)+e>>2]<=n)?i:r)+(f=u?f:t)|0,(0|r)!=(0|f););return r+1|0}function Le(r,n){var e=0;if(v[r>>2]=0,v[r+4>>2]=0,v[r+24>>2]=0,v[r+28>>2]=0,v[r+16>>2]=0,v[r+20>>2]=0,v[r+8>>2]=0,v[r+12>>2]=0,n){for(;n=(e=n)+1|0,(l[0|e]-1&255)>>>0<32;);ur(r,e)}return r}function Me(r){var n,e,f,i,t;return v[(r|=0)>>2]=10216,Rn(n=r+8|0),Rn(e=r+24|0),Rn(f=r+56|0),Rn(i=r+72|0),Rn(t=r+88|0),go(r+120|0),Su(r+104|0),Wu(t),Gu(i),Hu(f),ro(r+40|0),ro(e),go(n),0|r}function _e(r,n){var e,f;return V=e=V-16|0,r=df(r,n),v[r+20>>2]=8776,v[r>>2]=11052,v[r+24>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[r+36>>2]=0,ya(f=r+20|0,n=m(n,3)),v[e+12>>2]=0,wn(f,n,e+12|0),V=e+16|0,r}function ze(r,n,e,f){n|=0,e|=0,f|=0;var i,t=0;return V=i=V-16|0,n=((t=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&t?v[v[n>>2]+r>>2]:r,t=n,n=$e(i+4|0,f+4|0,v[f>>2],0),r=0|Ta[0|r](t,e,n),gi(n),V=i+16|0,0|r}function xe(r,n){var e,f=0,i=0;if(V=e=V-16|0,d[r+44>>2]<=n>>>0)for(f=r+40|0;v[e+12>>2]=0,Un(f,e+12|0),d[r+44>>2]<=n>>>0;);else i=v[v[r+52>>2]+(n<<2)>>2];return V=e+16|0,i}function Je(r,n){var e,f;return V=e=V-16|0,r=df(r,n),v[r+20>>2]=8776,v[r>>2]=11100,v[r+24>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[r+36>>2]=0,ya(f=r+20|0,n<<=3),v[e+12>>2]=0,wn(f,n,e+12|0),V=e+16|0,r}function Ke(r,n,e){var f;if(f=En(r,4946)){if(v[f+8>>2]==v[2430]&&!uf(v[f+16>>2],6299))return void yt(n,e);xr(n,e,Lt(r,4946,w(0)),Lt(r,6961,w(0)),Lt(r,6943,w(1)),Lt(r,6931,w(1)))}}function Be(r,n,e){n|=0,e|=0;var f,i=0;return V=f=V-16|0,n=((i=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&i?v[v[n>>2]+r>>2]:r,i=n,n=$e(f+4|0,e+4|0,v[e>>2],0),r=0|Ta[0|r](i,n),gi(n),V=f+16|0,0|r}function Ne(r){var n,e,f=0;if(f=v[72+(r|=0)>>2],e=l[f+28|0],i[f+28|0]=1,n=v[r+44>>2]){for(f=0;Yn(r,f),(0|n)!=(0|(f=f+1|0)););f=v[r+72>>2]}v[r+44>>2]=0,i[f+28|0]=e,mr(f)}function qe(r,n){n|=0;var e=0,f=0;r:{n:if(v[88+(r|=0)>>2]){for(;;){if(!ei(Yo(f=v[v[r+96>>2]+(e<<2)>>2]),n)){if((e=e+1|0)>>>0<d[r+88>>2])continue;break n}break}break r}f=0}return 0|f}function De(r){var n;n=v[4+(r|=0)>>2],p[r+32>>2]=p[n+28>>2],p[r+36>>2]=p[n+32>>2],p[r+40>>2]=p[n+36>>2],p[r+44>>2]=p[n+40>>2],p[r+48>>2]=p[n+44>>2],p[r+52>>2]=p[n+48>>2],p[r+56>>2]=p[n+52>>2]}function Ve(r,n,e,f){n|=0,e|=0,f|=0;var i,t=0;V=i=V-16|0,n=((t=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&t?v[v[n>>2]+r>>2]:r,t=n,n=$e(i+4|0,f+4|0,v[f>>2],0),Ta[0|r](t,e,n),gi(n),V=i+16|0}function Ze(r,n){return v[r+8>>2]=0,v[r+12>>2]=0,v[r+4>>2]=9e3,v[r>>2]=8984,Ue(r+16|0,n),Tt(r+28|0),v[r+64>>2]=0,v[r+56>>2]=1,v[r+60>>2]=0,v[r+48>>2]=1,v[r+52>>2]=1,v[r+40>>2]=6,v[r+44>>2]=1,r}function Ye(){var r,n=0;V=r=V-16|0,l[20896]||(v[5221]=0,v[5222]=0,v[5220]=1048,v[5223]=0,i[20896]=1),l[20948]||(qr(20900,n=ht(r+4|0,6893,0),20880,w(0)),gi(n),i[20948]=1),V=r+16|0}function Xe(r){var n,e=0;return v[4+(r|=0)>>2]=8648,v[r>>2]=8632,To(r+148|0),lo(r+132|0),oo(r+116|0),v[r+4>>2]=8728,(n=v[r+12>>2])&&(e=v[r+8>>2])&&Ta[0|n](e),0|r}function Qe(r,n){return r=at(r,n),v[r+24>>2]=9676,v[r>>2]=9656,v[r+52>>2]=1065353216,v[r+56>>2]=0,i[r+50|0]=0,s[r+48>>1]=0,v[r+44>>2]=1,v[r+28>>2]=0,v[r+32>>2]=0,v[r+36>>2]=0,v[r+40>>2]=0,r}function rf(r,n,e,f){var i;return V=i=V-16|0,r=df(r,n),v[r+20>>2]=8776,v[r>>2]=f,v[r+24>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[r+36>>2]=0,v[i+12>>2]=0,wn(r+20|0,m(n,e),i+12|0),V=i+16|0,r}function nf(r,n){n|=0;var e,f=0;if(e=v[24+(r|=0)>>2])for(;;){if(ei(v[v[r+32>>2]+(f<<2)>>2]+8|0,n))return v[v[r+32>>2]+(f<<2)>>2];if((0|e)==(0|(f=f+1|0)))break}return 0}function ef(r,n){var e;return V=e=V-16|0,r=df(r,n),v[r+24>>2]=8776,v[r+20>>2]=0,v[r>>2]=9344,v[r+36>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[e+12>>2]=0,wn(r+24|0,m(n,5),e+12|0),V=e+16|0,r}function ff(r,n){var e;return V=e=V-16|0,r=df(r,n),v[r+24>>2]=8776,v[r+20>>2]=0,v[r>>2]=10132,v[r+36>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[e+12>>2]=0,wn(r+24|0,n<<1,e+12|0),V=e+16|0,r}function tf(r,n){var e;return V=e=V-16|0,r=df(r,n),v[r+20>>2]=8776,v[r>>2]=9996,v[r+24>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[r+36>>2]=0,v[e+12>>2]=0,wn(r+20|0,n<<1,e+12|0),V=e+16|0,r}function uf(r,n){var e=0,f=0;r:if(!(!(e=l[0|r])|(0|(f=l[0|n]))!=(0|e)))for(;;){if(f=l[n+1|0],!(e=l[r+1|0]))break r;if(n=n+1|0,r=r+1|0,(0|e)!=(0|f))break}return e-f|0}function of(r,n,e,f,i,t,u,o){r|=0,n|=0,e=w(e),f=w(f),i=w(i),t|=0,u|=0,o|=0,r=v[r+32>>2]+m(n,24)|0,p[r>>2]=e,p[r+4>>2]=f,p[r+8>>2]=i,p[r+12>>2]=0|t,p[r+16>>2]=u>>>0,p[r+20>>2]=o>>>0}function af(r,n){var e=0,f=0;r:if(v[r+4>>2]){for(;;){if(f=v[v[r+12>>2]+(e<<2)>>2],!ei(Qo(v[f+4>>2]),n)){if((e=e+1|0)>>>0<d[r+4>>2])continue;break r}break}return f}return 0}function cf(r,n,e){n|=0,e|=0;var f,i=0;V=f=V-16|0,n=((i=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&i?v[v[n>>2]+r>>2]:r,i=n,n=$e(f+4|0,e+4|0,v[e>>2],0),Ta[0|r](i,n),gi(n),V=f+16|0}function bf(r,n){n|=0;var e=0;return e=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(e=v[e+v[n>>2]>>2]),e=0|Ta[0|e](n),n=X((r=v[e+4>>2])+4|0),v[n>>2]=r,Ri(n+4|0,v[e+8>>2],r),0|n}function kf(r){var n,e=0,f=0;if(n=v[r+4>>2])for(;e=v[v[r+12>>2]+(f<<2)>>2],l[e+117|0]&&(l[e+116|0]&&kf(e+16|0),i[e+116|0]=0),(0|n)!=(0|(f=f+1|0)););}function sf(r,n,e){return v[r+36>>2]=0,v[r+32>>2]=0,v[r+24>>2]=0,v[r+28>>2]=0,v[r+20>>2]=9048,v[r+16>>2]=0,v[r+8>>2]=0,v[r+12>>2]=0,v[r+4>>2]=9032,v[r>>2]=8904,tr(r,n,e,8590,0),r}function vf(r){r|=0;var n=0,e=w(0);return n=r,e=Au(p[r+4>>2]),p[n+4>>2]=e,n=r,e=Au(p[r+8>>2]),p[n+8>>2]=e,n=r,e=Au(p[r+12>>2]),p[n+12>>2]=e,n=r,e=Au(p[r+16>>2]),p[n+16>>2]=e,0|r}function lf(r,n,e,f){var i;return r|=0,n|=0,e|=0,f|=0,V=i=V-32|0,v[i+28>>2]=n,n=$e(i+16|0,e+4|0,v[e>>2],0),v[i+12>>2]=f,r=0|Ta[0|r](i+28|0,n,i+12|0),gi(n),V=i+32|0,0|r}function hf(r){var n,e=0;return v[(r|=0)>>2]=10312,Rn(n=r+8|0),v[r+12>>2]=0,l[r+40|0]&&(e=v[r+4>>2])&&Ta[v[v[e>>2]+4>>2]](e),gi(r+24|0),eo(n),0|r}function df(r,n){var e;return V=e=V-16|0,r=jo(r),v[r+4>>2]=8776,v[r>>2]=9408,v[r+16>>2]=0,v[r+8>>2]=0,v[r+12>>2]=0,v[e+12>>2]=0,wn(r+4|0,m(n,19)-19|0,e+12|0),V=e+16|0,r}function pf(r,n){var e=0,f=0;r:if(v[r+4>>2]){for(;;){if(!ei(na(f=v[v[r+12>>2]+(e<<2)>>2]),n)){if((e=e+1|0)>>>0<d[r+4>>2])continue;break r}break}return f}return 0}function yf(r,n){var e=0,f=0;r:if(v[r+4>>2]){for(;;){if(!ei(Qo(f=v[v[r+12>>2]+(e<<2)>>2]),n)){if((e=e+1|0)>>>0<d[r+4>>2])continue;break r}break}return f}return 0}function mf(r,n,e,f){var i,t;n|=0,e|=0,f|=0,V=i=V-16|0,n=((t=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&t?v[v[n>>2]+r>>2]:r,v[i+12>>2]=f,Ta[0|r](n,e,i+12|0),V=i+16|0}function wf(r,n){var e,f,i;V=e=V-16|0,n=(f=+G(v[n>>2],21520,e+12|0))<4294967296&f>=0?~~f>>>0:0,i=v[e+12>>2],$e(r,n+4|0,v[n>>2],0),W(0|i),V=e+16|0}function gf(r,n,e,f,i,t,u,o){r|=0,n|=0,e|=0,f=w(f),i=w(i),t=w(t),u=w(u),o=w(o);var a=0;a=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(a=v[a+v[n>>2]>>2]),Ta[0|a](n,e,f,i,t,u,o)}function Ff(r){var n,e;return(r=(n=v[5198])+(e=r+7&-8)|0)>>>0<=n>>>0&&e||r>>>0>$a()<<16>>>0&&!(0|x(0|r))?(v[5446]=48,-1):(v[5198]=r,n)}function Af(r){var n=0,e=0;if(v[(r|=0)>>2]=17084,n=v[r+4>>2])for(;e=v[n+28>>2],Ta[v[v[n>>2]+4>>2]](n),n=e;);return v[r+4>>2]=0,v[r+8>>2]=0,0|r}function Tf(r){var n=0,e=0,f=0,i=0,t=0;if(n=10,f=1,r)for(;f=je((e=1&r)?n:1,e?i:0,f,t),t=Z,e=r>>>0>1,n=je(n,i,n,i),i=Z,r=r>>>1|0,e;);return Z=t,f}function $f(r){var n=0,e=0;return(n=jn(r,1))?(e=v[5280],e=0|Ta[v[v[e>>2]+8>>2]](e,n,8590,373),n=n-1|0,e=Ri(e,v[r+4>>2],n),v[r+4>>2]=n+v[r+4>>2],i[n+e|0]=0,e):0}function If(r,n){var e,f=0,i=0;if(V=e=V-16|0,f=v[n+20>>2])for(;i=v[r+72>>2],lt(e,3,f,0),Zr(i+4|0,e),f=v[f+20>>2];);v[n+20>>2]=0,V=e+16|0}function Cf(r){var n=0,e=0;if(v[(r|=0)>>2]=8600,n=v[r+4>>2])for(;e=v[n+12>>2],Ta[v[v[n>>2]+4>>2]](n),n=e;);return v[r+4>>2]=0,v[r+8>>2]=0,0|r}function Pf(r,n){n|=0;var e=0;e=0;r:if(r=v[24+(r|=0)>>2]){for(;;){if(e=r,v[r+4>>2]==(0|n))break r;if(!(r=v[r+12>>2]))break}e=0}return 0|!!(0|e)}function Ef(r,n,e){n|=0,e|=0;var f=0;if(!(f=v[136+(r|=0)>>2])||!(f=Pt(f,n,e))){if(!v[v[r+4>>2]+64>>2])return 0;f=Pt(v[v[r+4>>2]+64>>2],n,e)}return 0|f}function Of(r,n){var e;n|=0,!(e=v[224+(r|=0)>>2])|v[e+64>>2]!=v[n+4>>2]||(v[r+224>>2]=0,v[r+228>>2]=0,v[r+196>>2]=0,v[r+164>>2]=0,v[r+180>>2]=0,v[r+132>>2]=0)}function Rf(r,n){var e=0,f=0;(0|(e=v[r+8>>2]))!=(0|n)&&(e&&(f=v[5280],Ta[v[v[f>>2]+20>>2]](f,e,8590,86)),n?e=ee(n):(n=0,e=0),v[r+8>>2]=n,v[r+4>>2]=e)}function Sf(r,n){var e,f;return n|=0,V=e=V-16|0,f=e+4|0,Ta[v[(r|=0)>>2]](f,n),n=X((r=v[e+8>>2])+4|0),v[n>>2]=r,Ri(n+4|0,v[e+12>>2],r),gi(f),V=e+16|0,0|n}function Wf(r){var n,e=0;return v[(r|=0)>>2]=10664,Rn(n=r+8|0),l[r+28|0]&&(e=v[r+4>>2])&&Ta[v[v[e>>2]+4>>2]](e),gi(r+32|0),eo(n),0|r}function Gf(r,n){var e=0;return(e=v[r+8>>2])?(n=e-1|0,e=v[v[r+16>>2]+(n<<2)>>2],v[r+8>>2]=n,e):(r=ut(16),v[r+12>>2]=0,v[r+4>>2]=0,v[r+8>>2]=0,v[r>>2]=n,r)}function Uf(r,n){var e,f=0;if(e=v[r+4>>2])for(;;){if(ei(Qo(v[v[v[r+12>>2]+(f<<2)>>2]+4>>2]),n))return f;if((0|e)==(0|(f=f+1|0)))break}return-1}function jf(r,n,e,f){var i;r|=0,n|=0,e|=0,f=w(f),V=i=V-16|0,n=qe(v[r+4>>2],n),e=qe(v[r+4>>2],e),p[i+12>>2]=f,_t(i,n,e),Pn(r+12|0,i,i+12|0),V=i+16|0}function Hf(r,n){var e=0;if(v[r>>2]=n,n=v[r+4>>2])for(;e=v[n+20>>2],Ta[v[v[n>>2]+4>>2]](n),n=e;);return v[r+4>>2]=0,v[r+8>>2]=0,r}function Lf(r,n,e,f,i,t,u){r|=0,n|=0,e=w(e),f=w(f),i=w(i),t=w(t),u=w(u),r=v[r+32>>2]+m(n,20)|0,p[r>>2]=e,p[r+16>>2]=u,p[r+12>>2]=t,p[r+8>>2]=i,p[r+4>>2]=f}function Mf(r,n,e,f,i,t,u){r|=0,n|=0,e=w(e),f=w(f),i=w(i),t=w(t),u=w(u),r=v[r+36>>2]+m(n,20)|0,p[r>>2]=e,p[r+16>>2]=u,p[r+12>>2]=t,p[r+8>>2]=i,p[r+4>>2]=f}function _f(r){var n,e;return v[(r|=0)>>2]=11080,Rn(n=r+4|0),Rn(e=r+20|0),bt(r+104|0),ct(r+84|0),oo(r+68|0),Ku(r+52|0),oo(r+36|0),to(e),io(n),0|r}function zf(r){var n,e;return w((e=(n=r*r)*r)*n*n*(2718311493989822e-21*n-.00019839334836096632)+(e*(.008333329385889463*n-.16666666641626524)+r))}function xf(r,n,e){var f=0;for(ue(r,n,e),r=v[e+4>>2],n=v[e>>2];;){if((0|r)==(0|n))return;if(f=l[0|n],n=n+1|0,58==(0|f))break}v[e>>2]=n,qn(e)}function Jf(r,n){var e,f=0;if(e=v[r+4>>2])for(;;){if(ei(Qo(v[v[r+12>>2]+(f<<2)>>2]),n))return f;if((0|e)==(0|(f=f+1|0)))break}return-1}function Kf(r){return w(w(w(w(r*w(w(r*w(-.008656363002955914))+w(-.04274342209100723)))+w(.16666586697101593))*r)/w(w(r*w(-.7066296339035034))+w(1)))}function Bf(r){var n,e=0;return v[(r|=0)>>2]=16984,e=v[r+4>>2],(!(n=v[e>>2])||(ar(n),e=v[r+4>>2]))&&ar(e),gi(r+20|0),gi(r+8|0),0|r}function Nf(r,n,e){var f=0,i=0,t=0;d[r+8>>2]<n>>>0&&(v[r+8>>2]=n,f=v[5280],i=r,t=0|Ta[v[v[f>>2]+16>>2]](f,v[r+12>>2],n<<e,8590,101),v[i+12>>2]=t)}function qf(r,n,e){var f,i,t;V=f=V-16|0,n=v[n>>2],v[f+8>>2]=v[e>>2],e=r,r=0|_(21543,f+8|0),i=e,t=0|U(0|n,0|r),v[i>>2]=t,E(0|r),V=f+16|0}function Df(r,n){var e=0;if(ya(r,v[n+4>>2]+v[r+4>>2]|0),v[n+4>>2])for(;Gn(r,v[n+12>>2]+(e<<2)|0),(e=e+1|0)>>>0<d[n+4>>2];);}function Vf(r,n){var e=0;if(ya(r,v[n+4>>2]+v[r+4>>2]|0),v[n+4>>2])for(;Wn(r,v[n+12>>2]+(e<<2)|0),(e=e+1|0)>>>0<d[n+4>>2];);}function Zf(r){var n=0,e=0;return n=0,(e=r>>>23&255)>>>0<127||(n=2,e>>>0>150||(n=0,(e=1<<150-e)-1&r||(n=r&e?1:2))),n}function Yf(r,n){var e=0,f=0,i=0;d[r+8>>2]<n>>>0&&(v[r+8>>2]=n,e=v[5280],f=r,i=0|Ta[v[v[e>>2]+16>>2]](e,v[r+12>>2],n,8590,101),v[f+12>>2]=i)}function Xf(r,n){return r=Te(r,n),v[r+64>>2]=8776,v[r>>2]=9868,v[r+68>>2]=0,v[r+72>>2]=0,s[r+74>>1]=0,s[r+76>>1]=0,s[r+78>>1]=0,s[r+80>>1]=0,r}function Qf(r,n){var e,f=0;V=e=V-16|0,l[n+116|0]||((f=v[n+12>>2])&&Qf(r,f),i[n+116|0]=1,v[e+12>>2]=n,Un(r+104|0,e+12|0)),V=e+16|0}function ri(r){var n;return w((r*=r)*(n=r*r)*(2439044879627741e-20*r-.001388676377460993)+.04166662332373906*n+-.499999997251031*r+1)}function ni(r,n,e,f,i){r|=0,n|=0,e|=0,f=w(f),i=w(i);var t=0;t=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(t=v[t+v[n>>2]>>2]),Ta[0|t](n,e,f,i)}function ei(r,n){var e,f;return(0|(e=v[r+8>>2]))==(0|(f=v[n+8>>2]))?1:!(r=!f|!e|v[r+4>>2]!=v[n+4>>2]?1:uf(e,f))}function fi(r,n){var e,f=0;for(f=ee(r)+1|0,e=255&n;n=0,f&&(0|e)!=l[0|(n=(f=f-1|0)+r|0)];);return n}function ii(r,n,e){return v[r+12>>2]=0,v[r+16>>2]=0,p[r+8>>2]=n,v[r+4>>2]=e,v[r>>2]=9524,Tt(r+20|0),v[r+32>>2]=1065353216,v[r+36>>2]=0,r}function ti(r,n,e,f,i){p[f>>2]=p[r+100>>2]+w(w(n*p[r+92>>2])+w(p[r+96>>2]*e)),p[i>>2]=p[r+112>>2]+w(w(n*p[r+104>>2])+w(p[r+108>>2]*e))}function ui(r,n){r|=0,v[24+(n|=0)>>2]=0,Vf(n+20|0,r+20|0),v[n+40>>2]=0,Df(n+36|0,r+36|0),v[n+52>>2]=v[r+52>>2],v[n+56>>2]=v[r+56>>2]}function oi(r,n,e){var f;r|=0,n=w(n),e=w(e),V=f=V-16|0,p[f+12>>2]=n,p[f+8>>2]=e,Ta[v[v[r>>2]+16>>2]](r,f+12|0,f+8|0),V=f+16|0}function ai(r,n,e){var f;return r|=0,n=w(n),e=w(e),V=f=V-16|0,p[f+12>>2]=n,p[f+8>>2]=e,r=0|Ta[0|r](f+12|0,f+8|0),V=f+16|0,0|r}function ci(r,n){return v[r>>2]=9540,Ue(r+4|0,n),v[r+16>>2]=0,v[r+20>>2]=0,Tt(r+24|0),Tt(r+36|0),v[r+48>>2]=1065353216,v[r+52>>2]=0,r}function bi(r,n,e){r|=0,n|=0,e=w(e);var f=0;return f=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),w(w(Ta[0|f](n,e)))}function ki(r,n,e){n|=0,e|=0;var f=0;return f=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),v[Ta[0|f](n,e)>>2]}function si(r,n,e){n|=0,e|=0;var f=0;return f=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),w(w(Ta[0|f](n,e)))}function vi(r,n,e,f){n|=0,e|=0,f|=0;var i=0;i=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),Ta[0|i](n,e,f)}function li(r,n,e){n|=0,e|=0;var f=0;return f=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),0|Ta[0|f](n,e)}function hi(r,n){r|=0,n|=0;var e=w(0);v[r+60>>2]!=(0|n)&&(v[r+60>>2]=n,e=p[v[r+12>>2]+160>>2],v[r+72>>2]=0,p[r+64>>2]=e)}function di(r,n,e){n|=0,e|=0,r=v[12+(r|=0)>>2]+(n<<4)|0,n=v[e+8>>2],v[r+4>>2]=v[e+4>>2],v[r+8>>2]=n,v[r+12>>2]=v[e+12>>2]}function pi(r,n){n|=0;var e=0;return e=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(e=v[e+v[n>>2]>>2]),w(w(Ta[0|e](n)))}function yi(r,n,e){var f;r|=0,n|=0,V=f=V-16|0,e=$e(f+4|0,4+(e|=0)|0,v[e>>2],0),te(v[r>>2]+n|0,e),gi(e),V=f+16|0}function mi(r){v[224+(r|=0)>>2]&&(v[r+224>>2]=0,v[r+228>>2]=0,v[r+196>>2]=0,v[r+164>>2]=0,v[r+180>>2]=0,v[r+132>>2]=0)}function wi(r,n){var e;return r|=0,V=e=V-16|0,n=$e(e+4|0,4+(n|=0)|0,v[n>>2],0),r=0|Ta[0|r](n),gi(n),V=e+16|0,0|r}function gi(r){var n,e=0;return v[(r|=0)>>2]=10896,(n=v[r+8>>2])&&(e=v[5280],Ta[v[v[e>>2]+20>>2]](e,n,8590,187)),0|r}function Fi(r,n,e,f){var i;n|=0,e|=0,f|=0,V=i=V-16|0,r=v[(r|=0)>>2],v[i+12>>2]=f,Ta[0|r](n,e,i+12|0),V=i+16|0}function Ai(r,n,e){r|=0,n|=0,e=w(e);var f=0;f=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),Ta[0|f](n,e)}function Ti(r,n){var e=0;return v[r+4>>2]=0,v[r>>2]=n,(n=v[r+12>>2])&&(e=v[5280],Ta[v[v[e>>2]+20>>2]](e,n,8590,219)),r}function $i(r,n,e){n|=0,e|=0;var f=0;f=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),Ta[0|f](n,e)}function Ii(r,n){var e;return n|=0,e=v[(r|=0)>>2]+n|0,n=X((r=v[e+4>>2])+4|0),v[n>>2]=r,Ri(n+4|0,v[e+8>>2],r),0|n}function Ci(r,n){n|=0;var e=0;return e=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(e=v[e+v[n>>2]>>2]),0|Ta[0|e](n)}function Pi(r,n,e,f){var i;V=i=V-16|0,te(r+32|0,Pe(r=Ue(i+4|0,e),f)),gi(r),n&&ar(Fe(n)),V=i+16|0}function Ei(r,n,e){var f;return r|=0,n=w(n),e|=0,V=f=V-16|0,p[f+12>>2]=n,r=0|Ta[0|r](f+12|0,e),V=f+16|0,0|r}function Oi(r){var n,e;return n=Wt(32),e=v[r+4>>2],Zn(n,v[e+8>>2],v[e+4>>2],v[e+12>>2],r+20|0),te(n+8|0,r+8|0),n}function Ri(r,n,e){var f=0;if(e)for(f=r;i[0|f]=l[0|n],f=f+1|0,n=n+1|0,e=e-1|0;);return r}function Si(r,n){var e=0;for(n=v[n+4>>2];(e=uf(v[r+4>>2],n))&&(r=v[r+8>>2]););return!e}function Wi(r,n,e,f,i){r|=0,n|=0,e=w(e),f=w(f),i=w(i),r=v[r+32>>2]+m(n,12)|0,p[r>>2]=e,p[r+8>>2]=i,p[r+4>>2]=f}function Gi(r,n){return ei(v[r+4>>2]+36|0,v[n+4>>2]+36|0)?ei(v[r+8>>2]+36|0,v[n+8>>2]+36|0):0}function Ui(r,n,e,f,t,u){return v[r+4>>2]=n,v[r>>2]=9740,Ue(r+8|0,e),v[r+20>>2]=f,Ue(r+24|0,t),i[r+36|0]=u,r}function ji(r,n){n|=0;var e=0;e=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(e=v[e+v[n>>2]>>2]),Ta[0|e](n)}function Hi(r,n){var e;return r|=0,n|=0,V=e=V-16|0,v[e+12>>2]=n,r=0|Ta[0|r](e+12|0),V=e+16|0,0|r}function Li(r,n){return v[r+8>>2]=0,v[r+4>>2]=n,v[r+16>>2]=0,v[r+20>>2]=0,v[r+12>>2]=8872,v[r>>2]=8840,r}function Mi(r){br(r|=0,p[r+32>>2],p[r+36>>2],p[r+40>>2],p[r+44>>2],p[r+48>>2],p[r+52>>2],p[r+56>>2])}function _i(r){r&&(v[r+16>>2]=v[r+16>>2]-1,v[r+16>>2]||Ta[v[v[r>>2]+4>>2]](r))}function zi(r,n,e){var f=0;if(e)for(f=r;i[0|f]=n,f=f+1|0,e=e-1|0;);return r}function xi(r,n,e,f){r|=0,n|=0,e=w(e),f|=0,p[v[r+20>>2]+(n<<2)>>2]=e,te(v[r+36>>2]+m(n,12)|0,f)}function Ji(r,n,e,f,i,t,u){t|=0,u|=0,vr(r|=0,n|=0,e|=0,f|=0,v[12+(i|=0)>>2],t,u)}function Ki(r){var n;return v[(r|=0)>>2]=1032,Rn(n=r+4|0),gi(r+36|0),Cf(r+20|0),so(n),0|r}function Bi(r,n){var e;V=e=V-16|0,lt(e,2,n,0),Zr(r+4|0,e),i[v[r+20>>2]+88|0]=1,V=e+16|0}function Ni(r,n,e){n|=0,e|=0,p[(n<<=2)+v[16+(r|=0)>>2]>>2]=p[e+8>>2],v[n+v[r+32>>2]>>2]=e}function qi(r,n,e,f){r|=0,n|=0,e=w(e),f=w(f),r=v[r+32>>2]+(n<<3)|0,p[r>>2]=e,p[r+4>>2]=f}function Di(r,n,e,f){r|=0,n|=0,e=w(e),f=w(f),r=v[r+36>>2]+(n<<3)|0,p[r>>2]=e,p[r+4>>2]=f}function Vi(r,n){return r=wt(r,n),v[r+28>>2]=0,v[r+20>>2]=0,v[r+24>>2]=0,v[r>>2]=10052,r}function Zi(r,n,e,f){p[v[r+36>>2]+(n<<2)>>2]=e,r=v[r+52>>2]+(n<<4)|0,v[r+4>>2]=0,Df(r,f)}function Yi(r){var n;return n=ut(16),v[n+12>>2]=0,v[n+4>>2]=0,v[n+8>>2]=0,v[n>>2]=r,n}function Xi(r){var n;return v[(r|=0)>>2]=9556,Rn(n=r+20|0),Fo(n),To(r+4|0),0|r}function Qi(r,n){var e;e=Wt(4),v[e>>2]=n,$(21651,0|r,2,16232,11312,1655,0|e,0,0)}function rt(r,n){var e;e=Wt(4),v[e>>2]=n,$(1999,0|r,2,13552,11312,1611,0|e,0,0)}function nt(r,n){var e;e=Wt(4),v[e>>2]=n,$(1999,0|r,2,13560,11312,1612,0|e,0,0)}function et(r,n){var e;e=Wt(4),v[e>>2]=n,$(1944,0|r,2,13704,11312,1615,0|e,0,0)}function ft(r,n){var e;e=Wt(4),v[e>>2]=n,$(5364,0|r,3,14472,13224,1623,0|e,0,0)}function it(r,n){return r=at(r,n),v[r+24>>2]=9676,v[r>>2]=10976,zi(r+28|0,0,58),r}function tt(r,n){return r=at(r,n),v[r+24>>2]=9676,v[r>>2]=9928,zi(r+28|0,0,48),r}function ut(r){var n;return n=v[5280],0|Ta[v[v[n>>2]+12>>2]](n,r,8590,40)}function ot(r){var n;return(-1>>>(n=31&r)&-2)<<n|(-1<<(r=0-r&31)&-2)>>>r}function at(r,n){return v[r>>2]=9388,Ue(r+4|0,n),i[r+20|0]=0,v[r+16>>2]=0,r}function ct(r){var n;return v[(r|=0)>>2]=10488,Rn(n=r+4|0),io(n),0|r}function bt(r){var n;return v[(r|=0)>>2]=10504,Rn(n=r+4|0),to(n),0|r}function kt(r){var n;return v[(r|=0)>>2]=8808,Rn(n=r+4|0),lo(n),0|r}function st(r){return v[(r|=0)>>2]=11160,To(r+36|0),Ou(r+20|0),0|Bt(r)}function vt(r,n,e){return r|=0,n=w(n),e=w(e),p[r+4>>2]=e,p[r>>2]=n,0|r}function lt(r,n,e,f){v[r+12>>2]=f,v[r+8>>2]=e,v[r+4>>2]=n,v[r>>2]=8664}function ht(r,n,e){return $e(r,n,n?ee(n):0,e)}function dt(r,n,e){n|=0,e|=0,v[v[12+(r|=0)>>2]+(n<<2)>>2]=v[e>>2]}function pt(r){var n;n=v[5280],Ta[v[v[n>>2]+20>>2]](n,r,8590,62)}function yt(r,n){n|=0,v[v[16+(r|=0)>>2]+m(n,76)>>2]=1065353216}function mt(r,n){return v[r>>2]=9136,v[r+4>>2]=n,v[r>>2]=9064,r}function wt(r,n){return v[r>>2]=9112,Ue(r+4|0,n),v[r+16>>2]=0,r}function gt(r,n,e,f){n|=0,e|=0,f|=0,Ta[v[(r|=0)>>2]](n,e,f)}function Ft(r){var n;return p[12+(n=V-16|0)>>2]=r,p[n+12>>2]}function At(r){return v[4+(0|Ta[v[v[(r|=0)>>2]+8>>2]](r))>>2]}function Tt(r){return v[r+4>>2]=0,v[r+8>>2]=0,v[r>>2]=10896,r}function $t(r,n){return r=Te(r,n),v[r+64>>2]=0,v[r>>2]=9316,r}function It(r,n,e,f){sr(16+(r|=0)|0,n|=0,e|=0,f|=0)}function Ct(r){return v[4+(0|Ta[v[v[(r|=0)>>2]>>2]](r))>>2]}function Pt(r,n,e){return 0|Xn(16+(r|=0)|0,n|=0,e|=0)}function Et(r){return v[(r|=0)>>2]=11052,To(r+20|0),0|Kt(r)}function Ot(r,n){return r=mt(r,n),v[r+8>>2]=n,v[r>>2]=17e3,r}function Rt(r){return v[(r|=0)>>2]=9996,To(r+20|0),0|Kt(r)}function St(r,n,e){return n|=0,e|=0,0|Ta[0|(r|=0)](n,e)}function Wt(r){return(r=X(r>>>0<=1?1:r))||(L(),c()),r}function Gt(r,n,e){return(r=En(r,n))&&(e=v[r+20>>2]),e}function Ut(r,n){var e;e=tf(r,n),v[e>>2]=10024}function jt(r,n,e){v[r+8>>2]=e,v[r+4>>2]=n,v[r>>2]=10076}function Ht(r,n,e){return(r=En(r,n))&&(e=v[r+16>>2]),e}function Lt(r,n,e){return(r=En(r,n))&&(e=p[r+24>>2]),e}function Mt(r,n){return n|=0,v[12+(r|=0)>>2]+(n<<4)|0}function _t(r,n,e){v[r+8>>2]=e,v[r+4>>2]=n,v[r>>2]=8856}function zt(r,n,e){r|=0,n|=0,e=w(e),p[v[r>>2]+n>>2]=e}function xt(r,n){return n|=0,v[12+(r|=0)>>2]+(n<<2)|0}function Jt(r,n){r|=0,n=w(n),p[r+160>>2]=p[r+160>>2]+n}function Kt(r){return v[(r|=0)>>2]=9408,To(r+4|0),0|r}function Bt(r){return v[(r|=0)>>2]=9112,gi(r+4|0),0|r}function Nt(r,n){v[r+8>>2]=0,v[r+4>>2]=n,v[r>>2]=10076}function qt(r,n){return n|=0,w(p[v[(r|=0)>>2]+n>>2])}function Dt(r){return v[(r|=0)>>2]=9388,gi(r+4|0),0|r}function Vt(r){return v[(r|=0)>>2]=8680,ao(r+4|0),0|r}function Zt(r,n,e){n|=0,e|=0,v[v[(r|=0)>>2]+n>>2]=e}function Yt(r,n,e){n|=0,e|=0,i[v[(r|=0)>>2]+n|0]=e}function Xt(r,n){v[r+4>>2]=0,v[r+8>>2]=0,v[r>>2]=n+20}function Qt(r,n){return n|=0,0|Ta[v[(r|=0)>>2]](n)}function ru(r){(r|=0)&&Ta[v[v[r>>2]+16>>2]](r)}function nu(r){return r?31-F(r-1^r)|0:32}function eu(r,n){return 0|pf(100+(r|=0)|0,n|=0)}function fu(r,n){return 0|pf(116+(r|=0)|0,n|=0)}function iu(r,n){return 0|pf(132+(r|=0)|0,n|=0)}function tu(r){return r<w(0)?w(-1):r>w(0)?w(1):w(0)}function uu(r,n){return n|=0,v[v[(r|=0)>>2]+n>>2]}function ou(r,n){return n|=0,l[v[(r|=0)>>2]+n|0]}function au(r,n){return 0|Jf(32+(r|=0)|0,n|=0)}function cu(r,n){return 0|pf(48+(r|=0)|0,n|=0)}function bu(r,n){n|=0,jr(r|=0,cu(v[r+4>>2],n))}function ku(r,n){return 0|af(24+(r|=0)|0,n|=0)}function su(r,n){return 0|yf(32+(r|=0)|0,n|=0)}function vu(r,n){return 0|Jf(16+(r|=0)|0,n|=0)}function lu(r){(r|=0)&&Ta[v[v[r>>2]+4>>2]](r)}function hu(r,n){return 0|pf(68+(r|=0)|0,n|=0)}function du(r,n){return 0|yf(16+(r|=0)|0,n|=0)}function pu(r,n){return r=_e(r,n),v[r>>2]=10160,r}function yu(r,n){return r=_e(r,n),v[r>>2]=10188,r}function mu(r){return 16777216+(r<<1)>>>0<16777217}function wu(r,n){return 0|af(8+(r|=0)|0,n|=0)}function gu(r,n){return r=Te(r,n),v[r>>2]=9292,r}function Fu(r,n){return!uf(v[r+4>>2],v[n+4>>2])}function Au(r){return r<w(0)?w(0):r>w(1)?w(1):r}function Tu(r,n,e,f){z(0|r,0|n,8,0,0|e,-1,0|f)}function $u(r,n){return gr(r,n,10,-2147483648)}function Iu(r){return v[v[4+(r|=0)>>2]+16>>2]}function Cu(r){return 0!=v[224+(r|=0)>>2]|0}function Pu(r){v[r+4>>2]=v[5571],v[5571]=r}function Eu(r,n){return w(Ft(r?w(-n):n)*n)}function Ou(r){return 0|Ti(r|=0,10768)}function Ru(r,n){n|=0,v[28+(r|=0)>>2]=n}function Su(r){return 0|Ti(r|=0,10296)}function Wu(r){return 0|Ti(r|=0,10280)}function Gu(r){return 0|Ti(r|=0,10264)}function Uu(r){return 0|Ti(r|=0,11260)}function ju(r){return 0|Ti(r|=0,12912)}function Hu(r){return 0|Ti(r|=0,10248)}function Lu(r){return 0|Ti(r|=0,11596)}function Mu(r){return w(p[60+(r|=0)>>2])}function _u(r,n){n|=0,v[36+(r|=0)>>2]=n}function zu(r){return 0|Hf(r|=0,17052)}function xu(r){return 0|Hf(r|=0,17116)}function Ju(r){return 0|Ti(r|=0,10392)}function Ku(r){return 0|Ti(r|=0,10472)}function Bu(r){return 0|Ti(r|=0,10648)}function Nu(r){return 0|Ti(r|=0,10616)}function qu(r){return 0|Ti(r|=0,10600)}function Du(r){return 0|Ti(r|=0,10584)}function Vu(r){return 0|Ti(r|=0,10408)}function Zu(r){return 0|Ti(r|=0,10568)}function Yu(r){return 0|Ti(r|=0,10552)}function Xu(r){return 0|Ti(r|=0,10536)}function Qu(r){return 0|he(r|=0,10816)}function ro(r){return 0|Ti(r|=0,10232)}function no(r){return 0|Ti(r|=0,10848)}function eo(r){return 0|Ti(r|=0,10344)}function fo(r){return 0|he(r|=0,12976)}function io(r){return 0|Ti(r|=0,10440)}function to(r){return 0|Ti(r|=0,10456)}function uo(r){return 0|Ti(r|=0,10632)}function oo(r){return 0|Ti(r|=0,8744)}function ao(r){return 0|he(r|=0,8792)}function co(r){return 32==(0|r)|r-9>>>0<5}function bo(r){return 0|Ti(r|=0,9048)}function ko(r){return 0|Ti(r|=0,9032)}function so(r){return 0|Ti(r|=0,1048)}function vo(r){return 0|Hf(r|=0,8872)}function lo(r){return 0|Ti(r|=0,8760)}function ho(r){return r-65>>>0<26?32|r:r}function po(r){return 0|he(r|=0,9464)}function yo(r){return 0|he(r|=0,9508)}function mo(r){return 0|Ti(r|=0,9676)}function wo(r,n){n|=0,i[44+(r|=0)|0]=n}function go(r){return 0|Ti(r|=0,9260)}function Fo(r){return 0|Ti(r|=0,8824)}function Ao(r){return 0|Ti(r|=0,9852)}function To(r){return 0|Ti(r|=0,8776)}function $o(r){return 0|Ta[0|(r|=0)]()}function Io(r){return v[60+(r|=0)>>2]}function Co(r){v[r+16>>2]=v[r+16>>2]+1}function Po(r){return v[12+(r|=0)>>2]}function Eo(r){return v[24+(r|=0)>>2]}function Oo(r){return v[20+(r|=0)>>2]}function Ro(r){return v[16+(r|=0)>>2]}function So(r,n){return rf(r,n,6,9696)}function Wo(r,n){return rf(r,n,5,11e3)}function Go(r,n){return rf(r,n,3,9952)}function Uo(r){return be(r+16|0),Lu(r)}function jo(r){return v[r>>2]=10912,r}function Ho(r){return v[r>>2]=11128,r}function Lo(r){return l[44+(r|=0)|0]}function Mo(r,n){return xn(r,n,12912)}function _o(r){return v[8+(r|=0)>>2]}function zo(r){return v[4+(r|=0)>>2]}function xo(r,n){return xn(r,n,10392)}function Jo(r){return(r|=0)- -64|0}function Ko(r){return 100+(r|=0)|0}function Bo(r){return 104+(r|=0)|0}function No(r){return 68+(r|=0)|0}function qo(r){return 20+(r|=0)|0}function Do(r){return 40+(r|=0)|0}function Vo(r){return 16+(r|=0)|0}function Zo(r){return 84+(r|=0)|0}function Yo(r){return 36+(r|=0)|0}function Xo(r){return 24+(r|=0)|0}function Qo(r){return 8+(r|=0)|0}function ra(r){(r|=0)&&ar(r)}function na(r){return 4+(r|=0)|0}function ea(r){return Gf(r,8744)}function fa(r){return Gf(r,8776)}function ia(r){Gr(r|=0),ae(r)}function ta(r){return 0|r}function ua(r){pt(st(r|=0))}function oa(r){pt(Et(r|=0))}function aa(r){return r+204|0}function ca(r){return r+140|0}function ba(r,n){v[r+16>>2]=n}function ka(r){return r+144|0}function sa(){}function va(r,n){i[r+20|0]=n}function la(r){return r+44|0}function ha(r){i[r+64|0]=1}function da(r,n){Nf(r,n,4)}function pa(r){pt(r|=0)}function ya(r,n){Nf(r,n,2)}function ma(r,n){Nf(r,n,1)}function wa(r){ar(r|=0)}function ga(){c()}function Fa(){}var Aa,Ta=(Aa=[null,Ki,function(r){Ki(r|=0),pt(r)},so,function(r){pt(so(r|=0))},Cf,function(r){pt(Cf(r|=0))},ta,pa,function(){},function(){so(20880)},function(){Ki(20900)},Xe,function(r){Xe(r|=0),pt(r)},function(r){return 0|Xe((r|=0)-4|0)},function(r){Xe(r=(r|=0)-4|0),pt(r)},pa,Vt,function(r){Vt(r|=0),pt(r)},rn,function(r){rn(r|=0),pt(r)},function(r){return 0|rn((r|=0)-4|0)},function(r){rn(r=(r|=0)-4|0),pt(r)},function(r){var n,e=0;return v[(r|=0)>>2]=8728,(n=v[r+8>>2])&&(e=v[r+4>>2])&&Ta[0|n](e),0|r},function(r){var n,e=0;v[(r|=0)>>2]=8728,(n=v[r+8>>2])&&(e=v[r+4>>2])&&Ta[0|n](e),ar(r)},oo,function(r){pt(oo(r|=0))},lo,function(r){pt(lo(r|=0))},To,function(r){pt(To(r|=0))},ao,function(r){pt(ao(r|=0))},kt,function(r){pt(kt(r|=0))},Fo,function(r){pt(Fo(r|=0))},function(r){return v[(r|=0)>>2]=8840,vo(r+12|0),0|r},function(r){v[(r|=0)>>2]=8840,vo(r+12|0),pt(r)},pa,vo,function(r){pt(vo(r|=0))},function(r){return v[(r|=0)>>2]=8888,0|r},function(r){v[(r|=0)>>2]=8888,pt(r)},ce,function(r){ce(r|=0),pt(r)},function(r){var n,e=0;return v[4+(r|=0)>>2]=9e3,v[r>>2]=8984,gi(r+28|0),gi(r+16|0),v[r+4>>2]=8728,(n=v[r+12>>2])&&(e=v[r+8>>2])&&Ta[0|n](e),0|r},function(r){var n,e=0;v[4+(r|=0)>>2]=9e3,v[r>>2]=8984,gi(r+28|0),gi(r+16|0),v[r+4>>2]=8728,(n=v[r+12>>2])&&(e=v[r+8>>2])&&Ta[0|n](e),pt(r)},function(r){var n,e;return v[(r|=0)>>2]=9e3,v[(n=r-4|0)>>2]=8984,gi(r+24|0),gi(r+12|0),v[r>>2]=8728,(e=v[r+8>>2])&&(r=v[r+4>>2])&&Ta[0|e](r),0|n},function(r){var n,e;v[(r|=0)>>2]=9e3,v[(n=r-4|0)>>2]=8984,gi(r+24|0),gi(r+12|0),v[r>>2]=8728,(e=v[r+8>>2])&&(r=v[r+4>>2])&&Ta[0|e](r),pt(n)},function(r){return v[(r|=0)>>2]=9016,oo(r+96|0),oo(r+80|0),gi(r+8|0),0|r},function(r){v[(r|=0)>>2]=9016,oo(r+96|0),oo(r+80|0),gi(r+8|0),pt(r)},ko,function(r){pt(ko(r|=0))},bo,function(r){pt(bo(r|=0))},Fa,ta,pa,function(){return 20952},function(r,n,e,f){return n|=0,e|=0,f|=0,(r=nf(v[4+(r|=0)>>2],f))?(n=_r(ut(164),e),(f=v[n+28>>2])&&(!(e=v[n+24>>2])|(0|r)==(0|e)||Ta[0|f](e)),v[n+28>>2]=0,v[n+24>>2]=r,Qn(n,p[r+36>>2],p[r+40>>2],p[r+44>>2],p[r+48>>2],l[r+72|0]),p[n+60>>2]=p[r+52>>2],p[n+64>>2]=p[r+56>>2],p[n+68>>2]=v[r+28>>2],p[n+72>>2]=v[r+32>>2],p[n+76>>2]=v[r+60>>2],p[n+80>>2]=v[r+64>>2],0|n):0},function(r,n,e,f){return n|=0,e|=0,f|=0,(n=nf(v[4+(r|=0)>>2],f))?(r=zr(ut(236),e),(f=v[r+72>>2])&&(!(e=v[r+68>>2])|(0|n)==(0|e)||Ta[0|f](e)),v[r+72>>2]=0,v[r+68>>2]=n,p[r+180>>2]=p[n+36>>2],p[r+184>>2]=p[n+40>>2],p[r+188>>2]=p[n+44>>2],p[r+192>>2]=p[n+48>>2],i[r+228|0]=l[n+72|0],v[r+232>>2]=v[n+76>>2],p[r+76>>2]=p[n+52>>2],p[r+80>>2]=p[n+56>>2],p[r+84>>2]=v[n+28>>2],p[r+88>>2]=v[n+32>>2],p[r+92>>2]=v[n+60>>2],p[r+96>>2]=v[n+64>>2],0|r):0},function(r,n,e){return e|=0,0|gu(ut(64),e)},function(r,n,e){return e|=0,0|Xf(ut(84),e)},function(r,n,e){return e|=0,0|Vi(ut(32),e)},function(r,n,e){return e|=0,0|$t(ut(68),e)},sa,Fa,Bt,ga,function(){return 20964},function(){L(),c()},Fa,ga,function(){return 20976},Fa,function(r){return v[(r|=0)>>2]=9184,be(r+24|0),To(r+8|0),0|r},function(r){v[(r|=0)>>2]=9184,be(r+24|0),To(r+8|0),pt(r)},function(){return 20988},function(r,n,e,f,i,t,u,o){r|=0,n|=0,f=w(f),i|=0,u|=0,o|=0;var a=0;r:if(a=v[v[n+36>>2]+(v[r+4>>2]<<2)>>2],i=v[a+8>>2],0|Ta[v[v[i>>2]+16>>2]](i)){n:{e:{if(1!=(0|o)|u)if(o=v[r+20>>2],p[o>>2]>f){if(u>>>0>1)break r;if(i=v[a+4>>2],!v[i+72>>2])break e;i=i+68|0}else{if(i=v[r+12>>2]-1|0,p[o+(i<<2)>>2]<=f||(i=Se(r+8|0,f,1)-1|0),u=v[r+36>>2],!v[4+(u+m(i,12)|0)>>2])break e;i=u+m(i,12)|0}else{if(i=v[a+4>>2],!v[i+72>>2])break e;i=i+68|0}r=Ef(n,v[r+4>>2],i);break n}r=0}hi(a,r)}},function(r){return v[4+(r|=0)>>2]+67108864|0},be,function(r){pt(be(r|=0))},Fa,function(r){return v[(r|=0)>>2]=9228,go(r+16|0),0|r},function(r){v[(r|=0)>>2]=9228,go(r+16|0),pt(r)},function(){return 21e3},Mi,function(r){return l[117+(r|=0)|0]},function(r,n){n|=0,i[117+(r|=0)|0]=n},go,function(r){pt(go(r|=0))},function(r){return v[(r|=0)>>2]=9276,gi(r+8|0),0|r},function(r){v[(r|=0)>>2]=9276,gi(r+8|0),pt(r)},Fa,st,ua,function(){return 21012},function(r){var n;return r|=0,gu(n=ut(64),na(r)),ui(r,n),0|n},Fa,ua,function(){return 21024},function(r){var n;return r|=0,$t(n=ut(68),na(r)),ui(r,n),v[n+64>>2]=v[r+64>>2],0|n},Fa,function(r){return v[(r|=0)>>2]=9344,To(r+24|0),0|Kt(r)},function(r){v[(r|=0)>>2]=9344,To(r+24|0),pt(Kt(r))},function(){return 21036},function(r,n,e,f,i,t,u,o){r|=0,n|=0,e=w(e),f=w(f),i|=0,t=w(t),u|=0,o|=0;var a=w(0),c=w(0),b=w(0),k=0,s=0,l=w(0);V=i=V-32|0,n=v[v[n+36>>2]+(v[r+20>>2]<<2)>>2],o=v[n+8>>2];r:if(0|Ta[v[v[o>>2]+16>>2]](o))if(o=v[r+36>>2],p[o>>2]>f){n:switch(0|u){case 0:r=v[n+4>>2],p[n+20>>2]=p[r+28>>2],p[n+24>>2]=p[r+32>>2],p[n+28>>2]=p[r+36>>2],p[n+32>>2]=p[r+40>>2],vf(n+16|0);break r;case 1:break n;default:break r}r=v[n+4>>2],v[i+12>>2]=9372,u=v[r+40>>2],v[i+24>>2]=v[r+36>>2],v[i+28>>2]=u,u=v[r+32>>2],v[i+16>>2]=v[r+28>>2],v[i+20>>2]=u,e=p[i+24>>2],f=p[i+16>>2],a=p[i+28>>2],c=p[n+24>>2],p[n+24>>2]=w(w(p[i+20>>2]-c)*t)+c,c=e,e=p[n+28>>2],p[n+28>>2]=w(w(c-e)*t)+e,e=p[n+32>>2],p[n+32>>2]=w(w(a-e)*t)+e,e=p[n+20>>2],p[n+20>>2]=w(w(f-e)*t)+e,vf(n+16|0)}else o=o+(v[r+28>>2]<<2)|0,p[o-20>>2]<=f?(f=p[o-4>>2],e=p[o-8>>2],c=p[o-12>>2],a=p[o-16>>2]):(o=(s=(k=Se(r+24|0,f,5))<<2)+v[r+36>>2]|0,b=p[o-16>>2],c=p[o-12>>2],e=p[o-8>>2],l=p[o-4>>2],a=f,f=p[o>>2],a=vn(r,((k>>>0)/5|0)-1|0,w(w(1)-w(w(a-f)/w(p[o-20>>2]-f)))),r=v[r+36>>2]+s|0,f=w(l+w(a*w(p[r+16>>2]-l))),e=w(e+w(a*w(p[r+12>>2]-e))),c=w(c+w(a*w(p[r+8>>2]-c))),a=w(b+w(a*w(p[r+4>>2]-b)))),r=n+16|0,t!=w(1)?(u||(u=Xo(v[n+4>>2]),p[n+20>>2]=p[u+4>>2],p[n+24>>2]=p[u+8>>2],p[n+28>>2]=p[u+12>>2],p[n+32>>2]=p[u+16>>2],vf(r)),b=p[n+32>>2],p[n+32>>2]=b+w(w(f-b)*t),f=p[n+28>>2],p[n+28>>2]=f+w(w(e-f)*t),e=p[n+24>>2],p[n+24>>2]=e+w(w(c-e)*t),e=p[n+20>>2],p[n+20>>2]=e+w(w(a-e)*t),vf(r)):(p[n+32>>2]=f,p[n+28>>2]=e,p[n+24>>2]=c,p[n+20>>2]=a,vf(r));V=i+32|0},function(r){return v[20+(r|=0)>>2]+83886080|0},pa,Fa,Fa,Dt,function(r){Dt(r|=0),pt(r)},function(){return 21060},Fa,Kt,ga,function(){return 21072},Fa,function(r){return v[(r|=0)>>2]=9436,po(r+40|0),To(r+24|0),0|Kt(r)},function(r){v[(r|=0)>>2]=9436,po(r+40|0),To(r+24|0),pt(Kt(r))},function(){return 21084},function(r,n,e,f,i,t,u,o){r|=0,n|=0,e=w(e),f=w(f),i|=0,t=w(t),u|=0,o|=0;var a,c=0,b=0,k=0,s=w(0),l=0;V=a=V-16|0,n=v[v[n+36>>2]+(v[r+20>>2]<<2)>>2],i=v[n+8>>2];r:if(0|Ta[v[v[i>>2]+16>>2]](i)&&(o=v[n+60>>2])&&!(!Si(0|Ta[v[v[o>>2]+8>>2]](o),21452)|v[o+56>>2]!=v[r+56>>2])){k=v[n+72>>2]?u:0,u=n+68|0,i=v[v[r+52>>2]+4>>2];n:if(p[v[r+36>>2]>>2]>f){e:switch(0|k){case 0:v[n+72>>2]=0;break r;case 2:case 3:break r;case 1:break e;default:break n}if(t==w(1)){v[n+72>>2]=0;break r}if(v[a+12>>2]=0,wn(u,i,a+12|0),!v[o+24>>2]){if(!i)break r;for(n=v[n+80>>2],u=v[o+48>>2],r=0;e=p[(c=(o=r<<2)+n|0)>>2],p[c>>2]=w(w(p[u+o>>2]-e)*t)+e,(0|i)!=(0|(r=r+1|0)););break r}if(!i)break r;for(e=w(w(1)-t),n=v[n+80>>2],r=0;p[(u=n+(r<<2)|0)>>2]=e*p[u>>2],(0|i)!=(0|(r=r+1|0)););break r}if(v[a+8>>2]=0,wn(u,i,a+8|0),u=v[r+28>>2]-1|0,p[v[r+36>>2]+(u<<2)>>2]<=f){if(r=v[r+52>>2],t==w(1)){if(3==(0|k)){if(v[o+24>>2]){if(!i)break r;for(n=v[n+80>>2],u=v[12+(r+(u<<4)|0)>>2],r=0;p[(c=(o=r<<2)+n|0)>>2]=p[u+o>>2]+p[c>>2],(0|i)!=(0|(r=r+1|0)););break r}if(!i)break r;for(c=v[n+80>>2],o=v[o+48>>2],u=v[12+(r+(u<<4)|0)>>2],r=0;p[(b=(n=r<<2)+c|0)>>2]=w(p[n+u>>2]-p[n+o>>2])+p[b>>2],(0|i)!=(0|(r=r+1|0)););break r}Ri(v[n+80>>2],v[12+(r+(u<<4)|0)>>2],i<<2);break r}n:switch(0|k){case 0:if(v[o+24>>2]){if(!i)break r;for(n=v[n+80>>2],u=v[12+(r+(u<<4)|0)>>2],r=0;p[(o=r<<2)+n>>2]=p[u+o>>2]*t,(0|i)!=(0|(r=r+1|0)););break r}if(!i)break r;for(c=v[n+80>>2],o=v[o+48>>2],u=v[12+(r+(u<<4)|0)>>2],r=0;e=p[(n=r<<2)+o>>2],p[n+c>>2]=w(w(p[n+u>>2]-e)*t)+e,(0|i)!=(0|(r=r+1|0)););break r;case 1:case 2:if(!i)break r;for(n=v[n+80>>2],u=v[12+(r+(u<<4)|0)>>2],r=0;e=p[(c=(o=r<<2)+n|0)>>2],p[c>>2]=w(w(p[u+o>>2]-e)*t)+e,(0|i)!=(0|(r=r+1|0)););break r;case 3:break n;default:break r}if(v[o+24>>2]){if(!i)break r;for(n=v[n+80>>2],u=v[12+(r+(u<<4)|0)>>2],r=0;p[(c=(o=r<<2)+n|0)>>2]=w(p[u+o>>2]*t)+p[c>>2],(0|i)!=(0|(r=r+1|0)););break r}if(!i)break r;for(c=v[n+80>>2],o=v[o+48>>2],u=v[12+(r+(u<<4)|0)>>2],r=0;p[(b=(n=r<<2)+c|0)>>2]=w(w(p[n+u>>2]-p[n+o>>2])*t)+p[b>>2],(0|i)!=(0|(r=r+1|0)););}else if(c=He(r+24|0,f),u=v[r+52>>2],l=r,b=c-1|0,r=v[r+36>>2],e=p[r+(c<<2)>>2],e=vn(l,b,w(w(1)-w(w(f-e)/w(p[r+(b<<2)>>2]-e)))),t!=w(1)){n:switch(0|k){case 0:if(v[o+24>>2]){if(!i)break r;for(o=v[n+80>>2],c=v[12+(u+(c<<4)|0)>>2],u=v[12+(u+(b<<4)|0)>>2],r=0;f=p[(n=r<<2)+u>>2],p[n+o>>2]=w(w(w(p[n+c>>2]-f)*e)+f)*t,(0|i)!=(0|(r=r+1|0)););break r}if(!i)break r;for(k=v[n+80>>2],o=v[o+48>>2],c=v[12+(u+(c<<4)|0)>>2],u=v[12+(u+(b<<4)|0)>>2],n=0;f=p[(r=n<<2)+u>>2],s=w(w(w(p[r+c>>2]-f)*e)+f),f=p[r+o>>2],p[r+k>>2]=w(w(s-f)*t)+f,(0|i)!=(0|(n=n+1|0)););break r;case 1:case 2:if(!i)break r;for(o=v[n+80>>2],c=v[12+(u+(c<<4)|0)>>2],u=v[12+(u+(b<<4)|0)>>2],r=0;b=(n=r<<2)+o|0,f=p[n+u>>2],s=w(w(w(p[n+c>>2]-f)*e)+f),f=p[b>>2],p[b>>2]=w(w(s-f)*t)+f,(0|i)!=(0|(r=r+1|0)););break r;case 3:break n;default:break r}if(v[o+24>>2]){if(!i)break r;for(o=v[n+80>>2],c=v[12+(u+(c<<4)|0)>>2],u=v[12+(u+(b<<4)|0)>>2],r=0;b=(n=r<<2)+o|0,f=p[n+u>>2],p[b>>2]=w(w(w(w(p[n+c>>2]-f)*e)+f)*t)+p[b>>2],(0|i)!=(0|(r=r+1|0)););}else if(i)for(k=v[n+80>>2],o=v[o+48>>2],c=v[12+(u+(c<<4)|0)>>2],u=v[12+(u+(b<<4)|0)>>2],n=0;b=(r=n<<2)+k|0,f=p[r+u>>2],p[b>>2]=w(w(w(w(w(p[r+c>>2]-f)*e)+f)-p[r+o>>2])*t)+p[b>>2],(0|i)!=(0|(n=n+1|0)););}else{if(3!=(0|k)){if(!i)break r;for(o=v[n+80>>2],c=v[12+(u+(c<<4)|0)>>2],u=v[12+(u+(b<<4)|0)>>2],r=0;f=p[(n=r<<2)+u>>2],p[n+o>>2]=w(w(p[n+c>>2]-f)*e)+f,(0|i)!=(0|(r=r+1|0)););break r}if(v[o+24>>2]){if(!i)break r;for(o=v[n+80>>2],c=v[12+(u+(c<<4)|0)>>2],u=v[12+(u+(b<<4)|0)>>2],r=0;b=(n=r<<2)+o|0,f=p[n+u>>2],p[b>>2]=p[b>>2]+w(w(w(p[n+c>>2]-f)*e)+f),(0|i)!=(0|(r=r+1|0)););break r}if(!i)break r;for(k=v[n+80>>2],o=v[o+48>>2],c=v[12+(u+(c<<4)|0)>>2],u=v[12+(u+(b<<4)|0)>>2],n=0;b=(r=n<<2)+k|0,f=p[r+u>>2],p[b>>2]=w(w(w(w(p[r+c>>2]-f)*e)+f)-p[r+o>>2])+p[b>>2],(0|i)!=(0|(n=n+1|0)););}}V=a+16|0},function(r){return 100663296+(v[v[56+(r|=0)>>2]+60>>2]+v[r+20>>2]|0)|0},po,function(r){pt(po(r|=0))},Fa,function(r){return v[(r|=0)>>2]=9480,yo(r+20|0),To(r+4|0),0|r},function(r){v[(r|=0)>>2]=9480,yo(r+20|0),To(r+4|0),pt(r)},function(){return 21096},function(r,n,e,f,i,t,u,o){r|=0,n|=0,f=w(f),i|=0,i=n+40|0;r:if(1!=(0|(o|=0))|(u|=0)){if(o=v[r+16>>2],p[o>>2]>f){if(u>>>0>1)break r;if(u=0,v[n+44>>2]=0,ya(i,v[n+28>>2]),!(r=v[n+28>>2]))break r;for(;Un(i,v[n+36>>2]+(u<<2)|0),(0|r)!=(0|(u=u+1|0)););}else if(u=v[r+8>>2]-1|0,p[o+(u<<2)>>2]<=f||(u=He(r+4|0,f)-1|0),r=v[r+32>>2]+(u<<4)|0,o=v[r+4>>2])for(u=0;v[(i=u<<2)+v[n+52>>2]>>2]=v[v[n+36>>2]+(v[i+v[r+12>>2]>>2]<<2)>>2],(0|o)!=(0|(u=u+1|0)););else if(u=0,v[n+44>>2]=0,r=v[n+28>>2])for(;Un(i,v[n+36>>2]+(u<<2)|0),(0|r)!=(0|(u=u+1|0)););}else{if(u=0,v[n+44>>2]=0,ya(i,v[n+28>>2]),!(r=v[n+28>>2]))break r;for(;Un(i,v[n+36>>2]+(u<<2)|0),(0|r)!=(0|(u=u+1|0)););}},function(){return 134217728},yo,function(r){pt(yo(r|=0))},function(r){return v[(r|=0)>>2]=9524,gi(r+20|0),0|r},function(r){v[(r|=0)>>2]=9524,gi(r+20|0),pt(r)},function(r){return v[(r|=0)>>2]=9540,gi(r+36|0),gi(r+24|0),gi(r+4|0),0|r},function(r){v[(r|=0)>>2]=9540,gi(r+36|0),gi(r+24|0),gi(r+4|0),pt(r)},Fa,Xi,function(r){Xi(r|=0),pt(r)},function(){return 21108},function(r,n,e,f,i,t,u,o){r|=0,n|=0,e=w(e),f=w(f),i|=0,t=w(t),u|=0,o|=0;var a=0,c=0;r:if(i){if(a=v[r+8>>2],e>f)Ta[v[v[r>>2]+12>>2]](r,n,e,w(34028234663852886e22),i,t,u,o),o=v[r+16>>2],e=w(-1);else if(o=v[r+16>>2],p[(o+(a<<2)|0)-4>>2]<=e)break r;if(!((t=p[o>>2])>f)){o=0;n:if(!(e<t))for(n=(u=He(r+4|0,e))&u>>31,c=v[r+16>>2],e=p[c+(u<<2)>>2];;){if((0|(o=u))<=0){o=n;break n}if(e!=p[((u=o-1|0)<<2)+c>>2])break}if(!(o>>>0>=a>>>0))for(;;){if(!(p[(n=o<<2)+v[r+16>>2]>>2]<=f))break r;if(Un(i,n+v[r+32>>2]|0),(0|a)==(0|(o=o+1|0)))break}}}},function(){return 117440512},ta,ga,Fa,function(r){return v[(r|=0)>>2]=9620,go(r+8|0),0|r},function(r){v[(r|=0)>>2]=9620,go(r+8|0),pt(r)},function(){return 21124},function(r){var n=0,e=0,f=0;switch(v[12+(r|=0)>>2]-1|0){case 0:return n=v[r+40>>2],void Ar(v[v[r+20>>2]>>2],p[n+100>>2],p[n+112>>2],l[r+28|0],l[r+29|0],l[v[r+4>>2]+50|0],p[r+32>>2]);case 1:n=v[r+20>>2],e=v[n>>2],f=v[n+4>>2],n=v[r+40>>2],or(e,f,p[n+100>>2],p[n+112>>2],v[r+24>>2],l[r+29|0],p[r+36>>2],p[r+32>>2])}},Lo,wo,Iu,Fa,function(r){return v[(r|=0)>>2]=9656,mo(r+24|0),0|Dt(r)},function(r){v[(r|=0)>>2]=9656,mo(r+24|0),pt(Dt(r))},function(){return 21136},mo,function(r){pt(mo(r|=0))},Fa,function(r){return v[(r|=0)>>2]=9696,To(r+20|0),0|Kt(r)},function(r){v[(r|=0)>>2]=9696,To(r+20|0),pt(Kt(r))},function(){return 21148},function(r,n,e,f,t,u,o,a){r|=0,n|=0,e=w(e),f=w(f),t|=0,u=w(u),o|=0,a|=0;var c=0,b=w(0),k=w(0);r:{n:{e:{n=v[v[n+68>>2]+(v[r+36>>2]<<2)>>2];f:if(0|Ta[v[v[n>>2]+16>>2]](n)){if(t=v[r+32>>2],p[t>>2]>f){i:switch(0|o){case 0:r=v[n+4>>2],p[n+32>>2]=p[r+52>>2],p[n+36>>2]=p[r+56>>2];break e;case 1:break i;default:break f}r=v[n+4>>2],e=p[n+32>>2],p[n+32>>2]=w(w(p[r+52>>2]-e)*u)+e,e=p[n+36>>2],p[n+36>>2]=w(w(p[r+56>>2]-e)*u)+e;break e}if(c=v[r+24>>2],p[(t+(c<<2)|0)-24>>2]<=f){if(!o){if(r=t+(c<<2)|0,t=v[n+4>>2],e=p[t+52>>2],p[n+32>>2]=w(w(p[r-20>>2]-e)*u)+e,e=p[t+56>>2],p[n+36>>2]=w(w(p[r-16>>2]-e)*u)+e,1==(0|a))break n;break r}if(r=t+(c<<2)|0,e=p[n+32>>2],p[n+32>>2]=w(w(p[r-20>>2]-e)*u)+e,e=p[n+36>>2],p[n+36>>2]=w(w(p[r-16>>2]-e)*u)+e,a)break f;break r}if(t=Se(r+20|0,f,6),c=v[r+32>>2]+(t<<2)|0,e=p[c-16>>2],b=p[c-20>>2],k=f,f=p[c>>2],f=vn(r,((0|t)/6|0)-1|0,w(w(1)-w(w(k-f)/w(p[c-24>>2]-f)))),!o){if(c=v[r+32>>2],k=w(w(w(p[4+(o=c+(t<<2)|0)>>2]-b)*f)+b),t=v[n+4>>2],b=p[t+52>>2],p[n+32>>2]=w(w(k-b)*u)+b,f=w(w(w(p[o+8>>2]-e)*f)+e),e=p[t+56>>2],p[n+36>>2]=w(w(f-e)*u)+e,1==(0|a))break n;return e=p[(c+(v[r+24>>2]<<2)|0)-12>>2],r=w(g(e))<w(2147483648)?~~e:-2147483648,v[n+24>>2]=r,i[n+28|0]=p[o-8>>2]!=w(0),void(i[n+29|0]=p[o-4>>2]!=w(0))}r=v[r+32>>2]+(t<<2)|0,k=w(w(w(p[r+4>>2]-b)*f)+b),b=p[n+32>>2],p[n+32>>2]=w(w(k-b)*u)+b,f=w(w(w(p[r+8>>2]-e)*f)+e),e=p[n+36>>2],p[n+36>>2]=w(w(f-e)*u)+e,a||(e=p[r-12>>2],t=w(g(e))<w(2147483648)?~~e:-2147483648,v[n+24>>2]=t,i[n+28|0]=p[r-8>>2]!=w(0),i[n+29|0]=p[r-4>>2]!=w(0))}return}return v[n+24>>2]=v[r+44>>2],i[n+28|0]=l[r+48|0],void(i[n+29|0]=l[r+49|0])}return v[n+24>>2]=v[t+44>>2],i[n+28|0]=l[t+48|0],void(i[n+29|0]=l[t+49|0])}e=p[r-12>>2],t=w(g(e))<w(2147483648)?~~e:-2147483648,v[n+24>>2]=t,i[n+28|0]=p[r-8>>2]!=w(0),i[n+29|0]=p[r-4>>2]!=w(0)},function(r){return v[36+(r|=0)>>2]+150994944|0},function(r){return v[(r|=0)>>2]=9740,gi(r+24|0),gi(r+8|0),0|r},function(r){v[(r|=0)>>2]=9740,gi(r+24|0),gi(r+8|0),pt(r)},Fa,Fa,Fa,function(){return 21176},function(r,n){return r|=0,(n=w(n))<=w(.5)?w(w(Fr(w(n+n),w(v[r+4>>2]))*w(.5))):(n=w(n+w(-1)),r=v[r+4>>2],w(w(w(Fr(w(n+n),w(0|r))/w(1&r?2:-2))+w(1))))},function(r,n,e,f){return r|=0,n=w(n),e=w(e),f=w(f),w(w(w(w(e-n)*w(Ta[v[v[r>>2]+4>>2]](r,f)))+n))},ta,wa,function(){return 21188},function(r,n){return r|=0,n=w(n),r=v[r+4>>2],w(w(w(Fr(w(n+w(-1)),w(0|r))*w(1&r?1:-1))+w(1)))},wa,Fa,We,function(r){We(r|=0),pt(r)},function(){return 21200},function(r){var n,e,f=0,t=0;return v[100+(r|=0)>>2]?0|Kr(r):(zr(n=ut(236),na(r)),f=v[r+68>>2],(e=v[n+72>>2])&&(!(t=v[n+68>>2])|(0|f)==(0|t)||Ta[0|e](t)),v[n+72>>2]=0,v[n+68>>2]=f,p[n+180>>2]=p[r+180>>2],p[n+184>>2]=p[r+184>>2],p[n+188>>2]=p[r+188>>2],p[n+192>>2]=p[r+192>>2],i[n+228|0]=l[r+228|0],v[n+232>>2]=v[r+232>>2],p[n+76>>2]=p[r+76>>2],p[n+80>>2]=p[r+80>>2],p[n+84>>2]=p[r+84>>2],p[n+88>>2]=p[r+88>>2],p[n+92>>2]=p[r+92>>2],p[n+96>>2]=p[r+96>>2],te(n+168|0,r+168|0),p[n+208>>2]=p[r+208>>2],p[n+212>>2]=p[r+212>>2],p[n+216>>2]=p[r+216>>2],p[n+220>>2]=p[r+220>>2],vf(n+204|0),ui(r,n),v[n+124>>2]=0,Df(n+120|0,r+120|0),v[n+108>>2]=0,Df(n+104|0,r+104|0),v[n+140>>2]=0,dn(n+136|0,r+136|0),f=v[r+224>>2],v[n+156>>2]=0,v[n+224>>2]=f,dn(f=n+152|0,f),p[n+196>>2]=p[r+196>>2],p[n+200>>2]=p[r+200>>2],0|n)},function(r){return 0|We((r|=0)-64|0)},function(r){We(r=(r|=0)-64|0),pt(r)},Ao,function(r){pt(Ao(r|=0))},Fa,function(r){return v[(r|=0)>>2]=9868,To(r- -64|0),0|st(r)},function(r){v[(r|=0)>>2]=9868,To(r- -64|0),pt(st(r))},function(){return 21212},function(r){var n;return r|=0,Xf(n=ut(84),na(r)),ui(r,n),v[n+68>>2]=0,Df(n- -64|0,r- -64|0),i[n+80|0]=l[r+80|0],i[n+81|0]=l[r+81|0],0|n},Fa,function(r){return v[(r|=0)>>2]=9892,To(r+124|0),To(r+108|0),To(r+92|0),To(r+76|0),To(r+60|0),To(r+44|0),go(r+8|0),0|r},function(r){v[(r|=0)>>2]=9892,To(r+124|0),To(r+108|0),To(r+92|0),To(r+76|0),To(r+60|0),To(r+44|0),go(r+8|0),pt(r)},function(){return 21224},function(r){r|=0;var n,e=w(0),f=0,t=w(0),u=w(0),o=0,a=w(0),c=w(0),b=0,k=0,s=0,h=0,d=0,y=w(0),g=w(0),F=0,A=w(0),$=w(0),I=w(0),C=0,P=w(0),E=w(0),O=0,R=0,S=0,W=w(0),G=0,U=0,j=w(0),H=w(0),L=w(0),M=w(0),_=w(0),z=w(0),x=0,J=0,K=0,B=0,N=0,q=w(0),D=0,Z=0,Y=w(0);if(V=n=V-16|0,(k=v[v[r+24>>2]+60>>2])&&Si(0|Ta[v[v[k>>2]+8>>2]](k),21212)&&(q=p[r+40>>2],M=p[r+36>>2],q>w(0)|M>w(0))){K=v[r+12>>2],x=v[r+4>>2],s=v[x+48>>2],G=v[x+52>>2],v[n+12>>2]=0,wn(r+44|0,C=!!(0|G)+K|0,n+12|0),a=p[r+32>>2];r:if(2==(0|G)|2!=(0|s)){if(2==(0|G)&&(v[n+8>>2]=0,wn(r+108|0,K,n+8|0)),O=C-1|0)for(b=v[x+48>>2];;){o=v[(h=f<<2)+v[r+20>>2]>>2];n:if((c=p[v[o+4>>2]+24>>2])<w(9999999747378752e-21)){if(e=w(0),2!=(0|G))break n;v[h+v[r+120>>2]>>2]=0}else if(2!=(0|s))e=w(c*p[o+92>>2]),t=w(e*e),e=w(c*p[o+104>>2]),e=w(T(w(t+w(e*e)))),2==(0|G)&&(p[h+v[r+120>>2]>>2]=e),e=w(w(w(a+(b?w(-0):c))*e)/c);else{if(e=a,2!=(0|G))break n;e=w(c*p[o+92>>2]),t=w(e*e),e=w(c*p[o+104>>2]),p[h+v[r+120>>2]>>2]=T(w(t+w(e*e))),e=a}if(f=f+1|0,p[v[r+56>>2]+(f<<2)>>2]=e,!(f>>>0<O>>>0))break}}else{if(C>>>0<2)break r;for(o=v[r+56>>2],f=1;p[o+(f<<2)>>2]=a,(0|C)!=(0|(f=f+1|0)););}D=!G,U=1==v[x+44>>2],B=2==(0|s),V=R=V-16|0,h=r,u=p[r+28>>2],d=v[r+24>>2],v[R+12>>2]=0,wn(O=r+60|0,m(C,3)+2|0,R+12|0),F=r+76|0,b=(0|(f=v[k+52>>2]))/6|0,J=l[k+80|0];r:if(l[k+81|0]){if(J?(v[R+12>>2]=0,wn(F,S=f+2|0,R+12|0),Ji(k,d,2,r=f-2|0,F,0,2),Ji(k,d,0,2,F,r,2),r=v[h+88>>2],p[(f=r+(f<<2)|0)>>2]=p[r>>2],p[f+4>>2]=p[r+4>>2]):(v[R+12>>2]=0,wn(F,S=f-4|0,R+12|0),Ji(k,d,2,S,F,0,2),b=b-1|0),f=0,v[R+12>>2]=0,wn(h+92|0,b,R+12|0),s=v[h+88>>2],a=p[s+4>>2],c=p[s>>2],(0|b)>0)for(d=v[h+104>>2],o=2,e=a,t=c;P=p[(r=(N=o<<2)+s|0)>>2],_=p[r+8>>2],c=p[r+16>>2],a=w(w(w(w(w(P-_)*w(3))-t)+c)*w(.09375)),A=w(w(P-t)*w(.75)),t=w(w(w(t-w(P+P))+_)*w(.1875)),y=w(w(a*w(.1666666716337204))+w(A+t)),t=w(w(t+t)+a),W=w(y+t),t=w(a+t),j=w(W+t),A=w(j+w(a+t)),E=p[s+(4|N)>>2],z=p[r+12>>2],a=p[r+20>>2],t=w(w(w(w(w(E-z)*w(3))-e)+a)*w(.09375)),$=w(w(E-e)*w(.75)),e=w(w(w(e-w(E+E))+z)*w(.1875)),g=w(w(t*w(.1666666716337204))+w($+e)),$=w(w(e+e)+t),e=w(g+$),L=w(t+$),$=w(e+L),t=w($+w(t+L)),I=w(w(w(w(I+w(T(w(w(y*y)+w(g*g)))))+w(T(w(w(W*W)+w(e*e)))))+w(T(w(w(j*j)+w($*$)))))+w(T(w(w(A*A)+w(t*t))))),p[d+(f<<2)>>2]=I,o=o+6|0,j=a,e=a,W=c,t=c,(0|b)!=(0|(f=f+1|0)););if(e=I,e=U?e:w(e/p[(v[k+76>>2]+(b<<2)|0)-4>>2]),!(!B|(0|C)<2))for(f=v[h+56>>2],r=1;p[(o=f+(r<<2)|0)>>2]=I*p[o>>2],(0|C)!=(0|(r=r+1|0)););if(!((0|C)<=0))for(y=w(u*e),S=S-4|0,s=-1,f=0,r=0,k=0,b=0,u=w(0);;){Y=p[v[h+56>>2]+(b<<2)>>2],y=w(y+Y);n:{e:{if(J)o=0,e=Er(y,I),e=w(e+(e<w(0)?I:w(-0)));else{if(y<w(0)){Ee(y,F,O,k);break n}if(y>I)break e;o=r,e=y}for(d=v[h+104>>2];o=(r=o)+1|0,(t=p[(U=d+(r<<2)|0)>>2])<e;);if(r?(A=e,e=p[U-4>>2],e=w(w(A-e)/w(t-e))):e=w(e/t),A=e,(0|r)!=(0|s)){for(d=v[h+136>>2],f=v[h+88>>2]+m(r,24)|0,P=p[f+8>>2],_=p[f+16>>2],c=p[f>>2],W=p[f+24>>2],$=w(w(w(w(w(P-_)*w(3))-c)+W)*w(.006000000052154064)),g=w(w(w(c-w(P+P))+_)*w(.029999999329447746)),e=w(w($*w(.1666666716337204))+w(w(w(P-c)*w(.30000001192092896))+g)),E=p[f+12>>2],z=p[f+20>>2],a=p[f+4>>2],j=p[f+28>>2],L=w(w(w(w(w(E-z)*w(3))-a)+j)*w(.006000000052154064)),u=w(w(w(a-w(E+E))+z)*w(.029999999329447746)),t=w(w(L*w(.1666666716337204))+w(w(w(E-a)*w(.30000001192092896))+u)),H=w(T(w(w(e*e)+w(t*t)))),p[d>>2]=H,u=w(w(u+u)+L),g=w(w(g+g)+$),f=1;e=w(g+e),t=w(u+t),H=w(H+w(T(w(w(e*e)+w(t*t))))),p[(f<<2)+d>>2]=H,u=w(L+u),g=w($+g),8!=(0|(f=f+1|0)););e=w(g+e),t=w(u+t),H=w(H+w(T(w(w(e*e)+w(t*t))))),p[d+32>>2]=H,e=w(w($+g)+e),$=w(e*e),e=w(w(L+u)+t),u=w(H+w(T(w($+w(e*e))))),p[d+36>>2]=u,s=r,f=0}else d=v[h+136>>2];for(o=f,e=w(A*u);o=(f=o)+1|0,(t=p[(U=(f<<2)+d|0)>>2])<e;);f?(A=e,e=p[U-4>>2],e=w(w(w(A-e)/w(t-e))+w(0|f))):e=w(e/t),Dr(w(e*w(.10000000149011612)),c,a,P,E,_,z,W,j,O,k,!!(0|b)&Y<w(9999999747378752e-21)|D);break n}we(w(y-I),F,S,O,k)}if(k=k+3|0,(0|C)==(0|(b=b+1|0)))break}}else{if(o=(J?-1:-2)+b|0,a=p[v[k+76>>2]+(o<<2)>>2],!(!B|(0|C)<2))for(s=v[h+56>>2],r=1;p[(b=s+(r<<2)|0)>>2]=a*p[b>>2],(0|C)!=(0|(r=r+1|0)););if(v[R+12>>2]=0,wn(F,8,R+12|0),(0|C)<=0)break r;for(u=w(u*(U?a:w(1))),U=f-4|0,B=f-6|0,s=-1,r=0,b=0;;){t=p[v[h+56>>2]+(S<<2)>>2],u=w(u+t);n:{if(J)e=Er(u,a),e=w(e+(e<w(0)?a:w(-0))),f=0;else{if(u<w(0)){-2!=(0|s)&&Ji(k,d,2,4,F,0,2),Ee(u,F,O,b),s=-2;break n}if(u>a){-3!=(0|s)&&Ji(k,d,B,4,F,0,2),we(w(u-a),F,0,O,b),s=-3;break n}f=r,e=u}for(N=v[k+76>>2];f=(r=f)+1|0,(c=p[(Z=N+(r<<2)|0)>>2])<e;);r?(A=e,e=p[Z-4>>2],e=w(w(A-e)/w(c-e))):e=w(e/c),(0|r)!=(0|s)&&(!J|(0|r)!=(0|o)?(Ji(k,d,m(r,6)+2|0,8,F,0,2),s=r):(Ji(k,d,U,4,F,0,2),Ji(k,d,0,4,F,4,2),s=o)),f=v[h+88>>2],Dr(e,p[f>>2],p[f+4>>2],p[f+8>>2],p[f+12>>2],p[f+16>>2],p[f+20>>2],p[f+24>>2],p[f+28>>2],O,b,!!(0|S)&t<w(9999999747378752e-21)|D)}if(b=b+3|0,(0|C)==(0|(S=S+1|0)))break}}if(V=R+16|0,r=v[O+12>>2],e=p[r+4>>2],a=p[r>>2],(W=p[x+56>>2])==w(0)?r=1==(0|G):(r=v[v[h+24>>2]+8>>2],W=w(W*(w(w(p[r+92>>2]*p[r+108>>2])-w(p[r+104>>2]*p[r+96>>2]))>w(0)?w(.01745329238474369):w(-.01745329238474369))),r=0),K)for(f=0,s=3;o=v[(k=f<<2)+v[h+20>>2]>>2],c=p[o+100>>2],p[o+100>>2]=w(w(a-c)*q)+c,c=p[o+112>>2],p[o+112>>2]=w(w(e-c)*q)+c,b=v[O+12>>2]+(s<<2)|0,c=p[b>>2],y=w(c-a),a=p[b+4>>2],e=w(a-e),2==(0|G)&&(t=p[k+v[h+120>>2]>>2])>=w(9999999747378752e-21)&&(t=w(w(w(w(w(T(w(w(y*y)+w(e*e))))/t)+w(-1))*M)+w(1)),p[o+92>>2]=t*p[o+92>>2],p[o+104>>2]=t*p[o+104>>2]),M>w(0)?(j=p[o+108>>2],t=p[o+104>>2],A=p[o+96>>2],I=p[o+92>>2],u=p[b-4>>2],G&&(u=p[b+8>>2],p[4+(k+v[h+56>>2]|0)>>2]<w(9999999747378752e-21)||(u=Or(e,y))),u=w(u-Or(t,I)),r?(P=Wr(u),E=Ur(u),g=p[v[o+4>>2]+24>>2],e=w(w(w(w(g*w(w(E*I)+w(t*P)))-e)*M)+a),a=w(w(w(w(g*w(w(P*I)-w(t*E)))-y)*M)+c)):(u=w(W+u),e=a,a=c),u>w(3.1415927410125732)?u=w(u+w(-6.2831854820251465)):u<w(-3.1415927410125732)&&(u=w(u+w(6.2831854820251465))),c=Wr(u=w(M*u)),u=Ur(u),p[o+108>>2]=w(u*A)+w(j*c),p[o+104>>2]=w(u*I)+w(t*c),p[o+96>>2]=w(c*A)-w(j*u),p[o+92>>2]=w(c*I)-w(t*u)):(e=a,a=c),i[o+88|0]=0,s=s+3|0,(0|K)!=(0|(f=f+1|0)););}V=n+16|0},function(r){return l[140+(r|=0)|0]},function(r,n){n|=0,i[140+(r|=0)|0]=n},Iu,Fa,function(r){return v[(r|=0)>>2]=9928,mo(r+24|0),0|Dt(r)},function(r){v[(r|=0)>>2]=9928,mo(r+24|0),pt(Dt(r))},function(){return 21236},Fa,function(r){return v[(r|=0)>>2]=9952,To(r+20|0),0|Kt(r)},function(r){v[(r|=0)>>2]=9952,To(r+20|0),pt(Kt(r))},function(){return 21248},function(r,n,e,f,i,t,u,o){r|=0,n|=0,e=w(e),f=w(f),i|=0,t=w(t),u|=0,o|=0;var a=w(0),c=0,b=w(0);r:if(n=v[v[n+100>>2]+(v[r+36>>2]<<2)>>2],0|Ta[v[v[n>>2]+16>>2]](n)){if(i=v[r+32>>2],p[i>>2]>f){n:switch(0|u){case 0:return r=v[n+4>>2],p[n+36>>2]=p[r+68>>2],void(p[n+40>>2]=p[r+72>>2]);case 1:break n;default:break r}return r=v[n+4>>2],e=p[n+36>>2],p[n+36>>2]=w(w(p[r+68>>2]-e)*t)+e,e=p[n+40>>2],void(p[n+40>>2]=w(w(p[r+72>>2]-e)*t)+e)}if(i=i+(v[r+24>>2]<<2)|0,p[i-12>>2]<=f?(f=p[i-4>>2],e=p[i-8>>2]):(i=(c=(o=Se(r+20|0,f,3))<<2)+v[r+32>>2]|0,e=p[i-8>>2],b=p[i-4>>2],a=f,f=p[i>>2],a=vn(r,((0|o)/3|0)-1|0,w(w(1)-w(w(a-f)/w(p[i-12>>2]-f)))),r=v[r+32>>2]+c|0,f=w(b+w(a*w(p[r+8>>2]-b))),e=w(e+w(a*w(p[r+4>>2]-e)))),!u)return a=e,r=v[n+4>>2],e=p[r+68>>2],p[n+36>>2]=w(w(a-e)*t)+e,e=p[r+72>>2],void(p[n+40>>2]=w(w(f-e)*t)+e);a=e,e=p[n+36>>2],p[n+36>>2]=w(w(a-e)*t)+e,e=p[n+40>>2],p[n+40>>2]=w(w(f-e)*t)+e}},function(r){return v[36+(r|=0)>>2]+218103808|0},Fa,Rt,function(r){Rt(r|=0),pt(r)},function(){return 21260},function(r,n,e,f,i,t,u,o){r|=0,n|=0,e=w(e),f=w(f),i|=0,t=w(t),u|=0,o|=0;var a=0,c=w(0);r:if(n=v[v[n+100>>2]+(v[r+36>>2]<<2)>>2],0|Ta[v[v[n>>2]+16>>2]](n)){if(i=v[r+32>>2],p[i>>2]>f){n:switch(0|u){case 0:return void(p[n+28>>2]=p[v[n+4>>2]+60>>2]);case 1:break n;default:break r}return e=p[n+28>>2],void(p[n+28>>2]=w(w(p[v[n+4>>2]+60>>2]-e)*t)+e)}if(i=i+(v[r+24>>2]<<2)|0,p[i-8>>2]<=f?e=p[i-4>>2]:(i=(a=(o=Se(r+20|0,f,2))<<2)+v[r+32>>2]|0,e=p[i-4>>2],c=f,f=p[i>>2],e=w(e+w(vn(r,((0|o)/2|0)-1|0,w(w(1)-w(w(c-f)/w(p[i-8>>2]-f))))*w(p[4+(v[r+32>>2]+a|0)>>2]-e)))),!u)return f=e,e=p[v[n+4>>2]+60>>2],void(p[n+28>>2]=w(w(f-e)*t)+e);f=e,e=p[n+28>>2],p[n+28>>2]=w(w(f-e)*t)+e}},function(r){return v[36+(r|=0)>>2]+184549376|0},Fa,function(r){pt(Rt(r|=0))},function(){return 21272},function(r,n,e,f,i,t,u,o){r|=0,n|=0,e=w(e),f=w(f),i|=0,t=w(t),u|=0,o|=0;var a=0,c=w(0);r:if(n=v[v[n+100>>2]+(v[r+36>>2]<<2)>>2],0|Ta[v[v[n>>2]+16>>2]](n)){if(i=v[r+32>>2],p[i>>2]>f){n:switch(0|u){case 0:return void(p[n+32>>2]=p[v[n+4>>2]+64>>2]);case 1:break n;default:break r}return e=p[n+32>>2],void(p[n+32>>2]=w(w(p[v[n+4>>2]+64>>2]-e)*t)+e)}if(a=v[r+24>>2],o=v[2493],p[i+(a-o<<2)>>2]<=f?e=p[i+(a+v[2495]<<2)>>2]:(i=Se(r+20|0,f,o),a=v[r+32>>2],e=p[a+(i+v[2495]<<2)>>2],c=f,f=p[a+(i<<2)>>2],e=w(e+w(vn(r,((0|i)/(0|o)|0)-1|0,w(w(1)-w(w(c-f)/w(p[a+(i+v[2494]<<2)>>2]-f))))*w(p[v[r+32>>2]+(i+v[2496]<<2)>>2]-e)))),!u)return f=e,e=p[v[n+4>>2]+64>>2],void(p[n+32>>2]=w(w(f-e)*t)+e);f=e,e=p[n+32>>2],p[n+32>>2]=w(w(f-e)*t)+e}},function(r){return v[36+(r|=0)>>2]+201326592|0},Fa,function(r){pt(Bt(r|=0))},function(){return 21284},function(r){var n;return r|=0,Vi(n=ut(32),na(r)),p[n+20>>2]=p[r+20>>2],p[n+24>>2]=p[r+24>>2],p[n+28>>2]=p[r+28>>2],0|n},pa,Fa,function(r){var n,e=0;return v[20+(r|=0)>>2]=10116,v[r>>2]=10092,gi(r+116|0),To(r+100|0),To(r+84|0),v[r+20>>2]=8728,(n=v[r+28>>2])&&(e=v[r+24>>2])&&Ta[0|n](e),0|Bt(r)},function(r){var n,e=0;v[20+(r|=0)>>2]=10116,v[r>>2]=10092,gi(r+116|0),To(r+100|0),To(r+84|0),v[r+20>>2]=8728,(n=v[r+28>>2])&&(e=v[r+24>>2])&&Ta[0|n](e),pt(Bt(r))},function(){return 21296},function(r){r|=0;var n,e,f,i=0,t=w(0);return _r(n=ut(164),na(r)),p[n+68>>2]=p[r+68>>2],p[n+72>>2]=p[r+72>>2],p[n+60>>2]=p[r+60>>2],p[n+64>>2]=p[r+64>>2],p[n+76>>2]=p[r+76>>2],p[n+80>>2]=p[r+80>>2],e=v[r+24>>2],(f=v[n+28>>2])&&(!(i=v[n+24>>2])|(0|e)==(0|i)||Ta[0|f](i)),v[n+28>>2]=0,v[n+24>>2]=e,te(n+116|0,r+116|0),p[n+32>>2]=p[r+32>>2],p[n+36>>2]=p[r+36>>2],p[n+44>>2]=p[r+44>>2],p[n+48>>2]=p[r+48>>2],p[n+40>>2]=p[r+40>>2],p[n+52>>2]=p[r+52>>2],t=p[r+56>>2],v[n+104>>2]=0,p[n+56>>2]=t,Df(n+100|0,r+100|0),v[n+88>>2]=0,Df(n+84|0,r+84|0),p[n+148>>2]=p[r+148>>2],p[n+152>>2]=p[r+152>>2],p[n+156>>2]=p[r+156>>2],p[n+160>>2]=p[r+160>>2],vf(n+144|0),0|n},function(r){var n,e;return v[(r|=0)>>2]=10116,v[(n=r-20|0)>>2]=10092,gi(r+96|0),To(r+80|0),To(r- -64|0),v[r>>2]=8728,(e=v[r+8>>2])&&(r=v[r+4>>2])&&Ta[0|e](r),0|Bt(n)},function(r){var n,e;v[(r|=0)>>2]=10116,v[(n=r-20|0)>>2]=10092,gi(r+96|0),To(r+80|0),To(r- -64|0),v[r>>2]=8728,(e=v[r+8>>2])&&(r=v[r+4>>2])&&Ta[0|e](r),pt(Bt(n))},Fa,function(r){return v[(r|=0)>>2]=10132,To(r+24|0),0|Kt(r)},function(r){v[(r|=0)>>2]=10132,To(r+24|0),pt(Kt(r))},function(){return 21308},function(r,n,e,f,i,t,u,o){r|=0,n|=0,e=w(e),f=w(f),i|=0,t=w(t),u|=0,o|=0;var a=0,c=w(0),b=0;r:if(n=v[v[n+20>>2]+(v[r+20>>2]<<2)>>2],l[n+117|0]){if(i=v[r+36>>2],p[i>>2]>f){n:switch(0|u){case 0:return void(p[n+40>>2]=p[v[n+4>>2]+36>>2]);case 1:break n;default:break r}return e=p[n+40>>2],f=w(p[v[n+4>>2]+36>>2]-e),a=+w(f/w(-360))+16384.499999999996,r=g(a)<2147483648?~~a:-2147483648,void(p[n+40>>2]=w(w(f-w(0|m(16384-r|0,360)))*t)+e)}if(i=i+(v[r+28>>2]<<2)|0,p[i-8>>2]<=f){f=p[i-4>>2];n:{e:switch(0|u){case 3:e=p[n+40>>2];break n;case 0:return void(p[n+40>>2]=w(f*t)+p[v[n+4>>2]+36>>2]);case 1:case 2:break e;default:break r}e=p[n+40>>2],f=w(f+w(p[v[n+4>>2]+36>>2]-e)),a=+w(f/w(-360))+16384.499999999996,r=g(a)<2147483648?~~a:-2147483648,f=w(f-w(0|m(16384-r|0,360)))}return void(p[n+40>>2]=w(f*t)+e)}i=(b=(o=Se(r+24|0,f,2))<<2)+v[r+36>>2]|0,e=p[i-4>>2],c=f,f=p[i>>2],f=vn(r,(o>>1)-1|0,w(w(1)-w(w(c-f)/w(p[i-8>>2]-f)))),c=w(p[4+(v[r+36>>2]+b|0)>>2]-e),a=+w(c/w(-360))+16384.499999999996,r=g(a)<2147483648?~~a:-2147483648,f=w(w(w(c-w(0|m(16384-r|0,360)))*f)+e);n:{e:switch(0|u){case 3:e=p[n+40>>2];break n;case 0:return a=+w(f/w(-360))+16384.499999999996,r=g(a)<2147483648?~~a:-2147483648,void(p[n+40>>2]=w(w(f-w(0|m(16384-r|0,360)))*t)+p[v[n+4>>2]+36>>2]);case 1:case 2:break e;default:break r}e=p[n+40>>2],f=w(f+w(p[v[n+4>>2]+36>>2]-e))}a=+w(f/w(-360))+16384.499999999996,r=g(a)<2147483648?~~a:-2147483648,p[n+40>>2]=w(w(f-w(0|m(16384-r|0,360)))*t)+e}},Oo,Fa,Et,oa,function(){return 21320},function(r,n,e,f,i,t,u,o){r|=0,n|=0,e=w(e),f=w(f),i|=0,t=w(t),u|=0,o|=0;var a=w(0),c=w(0),b=0,k=w(0),s=w(0),h=0,d=w(0),y=0,m=w(0);r:{n=v[v[n+20>>2]+(v[r+36>>2]<<2)>>2];n:if(l[n+117|0]){if(i=v[r+32>>2],p[i>>2]>f){e:switch(0|u){case 0:return r=v[n+4>>2],p[n+44>>2]=p[r+40>>2],void(p[n+48>>2]=p[r+44>>2]);case 1:break e;default:break n}return r=v[n+4>>2],e=p[n+44>>2],p[n+44>>2]=w(w(p[r+40>>2]-e)*t)+e,e=p[n+48>>2],void(p[n+48>>2]=w(w(p[r+44>>2]-e)*t)+e)}if(b=v[r+24>>2],h=v[2755],p[i+(b-h<<2)>>2]<=f?(r=v[n+4>>2],c=p[r+44>>2],f=w(p[i+(b+v[2758]<<2)>>2]*c),a=p[r+40>>2],e=w(p[i+(b+v[2757]<<2)>>2]*a)):(i=Se(r+20|0,f,h),b=v[r+32>>2],e=p[b+(i+v[2757]<<2)>>2],a=p[b+(i+v[2758]<<2)>>2],k=f,f=p[b+(i<<2)>>2],s=vn(r,((0|i)/(0|h)|0)-1|0,w(w(1)-w(w(k-f)/w(p[b+(i+v[2756]<<2)>>2]-f)))),b=v[n+4>>2],c=p[b+44>>2],r=v[r+32>>2],f=w(c*w(a+w(s*w(p[r+(i+v[2760]<<2)>>2]-a)))),a=p[b+40>>2],e=w(w(e+w(s*w(p[r+(i+v[2759]<<2)>>2]-e)))*a)),t==w(1))return 3==(0|u)?(p[n+44>>2]=p[n+44>>2]+w(e-a),void(p[n+48>>2]=p[n+48>>2]+w(f-c))):(p[n+48>>2]=f,void(p[n+44>>2]=e));if(1==(0|o)){e:switch(0|u){case 0:return y=n,m=w(w(w(w(w(g(e))*tu(a))-a)*t)+a),p[y+44>>2]=m,y=n,m=w(w(w(w(w(g(f))*tu(c))-c)*t)+c),void(p[y+48>>2]=m);case 1:case 2:return c=p[n+48>>2],a=p[n+44>>2],y=n,m=w(a+w(w(w(w(g(e))*tu(a))-a)*t)),p[y+44>>2]=m,y=n,m=w(c+w(w(w(w(g(f))*tu(c))-c)*t)),void(p[y+48>>2]=m);case 3:break e;default:break n}return c=p[n+48>>2],k=w(g(e)),e=p[n+44>>2],a=w(k*tu(e)),p[n+44>>2]=e+w(w(a-p[v[n+4>>2]+40>>2])*t),y=n,m=w(c+w(w(w(w(g(f))*tu(c))-p[v[n+4>>2]+44>>2])*t)),void(p[y+48>>2]=m)}e:switch(0|u){case 0:c=tu(e),s=p[v[n+4>>2]+44>>2];break r;case 1:case 2:a=p[n+44>>2],c=tu(e),s=p[n+48>>2];break r;case 3:break e;default:break n}c=tu(e),a=tu(f),r=v[n+4>>2],p[n+44>>2]=w(c*w(g(p[n+44>>2])))+w(w(e-w(c*w(g(p[r+40>>2]))))*t),p[n+48>>2]=w(a*w(g(p[n+48>>2])))+w(w(f-w(a*w(g(p[r+44>>2]))))*t)}return}d=tu(f),k=e,e=w(c*w(g(a))),p[n+44>>2]=w(w(k-e)*t)+e,e=w(d*w(g(s))),p[n+48>>2]=w(w(f-e)*t)+e},function(r){return v[36+(r|=0)>>2]+33554432|0},Fa,oa,function(){return 21332},function(r,n,e,f,i,t,u,o){r|=0,n|=0,e=w(e),f=w(f),i|=0,t=w(t),u|=0,o|=0;var a=w(0),c=0,b=w(0);r:if(n=v[v[n+20>>2]+(v[r+36>>2]<<2)>>2],l[n+117|0]){if(i=v[r+32>>2],p[i>>2]>f){n:switch(0|u){case 0:return r=v[n+4>>2],p[n+52>>2]=p[r+48>>2],void(p[n+56>>2]=p[r+52>>2]);case 1:break n;default:break r}return r=v[n+4>>2],e=p[n+52>>2],p[n+52>>2]=w(w(p[r+48>>2]-e)*t)+e,e=p[n+56>>2],void(p[n+56>>2]=w(w(p[r+52>>2]-e)*t)+e)}o=v[r+24>>2],c=v[2755],p[i+(o-c<<2)>>2]<=f?(f=p[i+(o+v[2758]<<2)>>2],e=p[i+(o+v[2757]<<2)>>2]):(i=Se(r+20|0,f,c),o=v[r+32>>2],e=p[o+(i+v[2757]<<2)>>2],b=p[o+(i+v[2758]<<2)>>2],a=f,f=p[o+(i<<2)>>2],a=vn(r,((0|i)/(0|c)|0)-1|0,w(w(1)-w(w(a-f)/w(p[o+(i+v[2756]<<2)>>2]-f)))),r=v[r+32>>2],f=w(b+w(a*w(p[r+(i+v[2760]<<2)>>2]-b))),e=w(e+w(a*w(p[r+(i+v[2759]<<2)>>2]-e))));n:switch(0|u){case 0:return r=v[n+4>>2],p[n+52>>2]=w(e*t)+p[r+48>>2],void(p[n+56>>2]=w(f*t)+p[r+52>>2]);case 1:case 2:return r=v[n+4>>2],a=w(e+p[r+48>>2]),e=p[n+52>>2],p[n+52>>2]=w(w(a-e)*t)+e,e=p[n+56>>2],void(p[n+56>>2]=w(w(w(f+p[r+52>>2])-e)*t)+e);case 3:break n;default:break r}p[n+52>>2]=w(e*t)+p[n+52>>2],p[n+56>>2]=w(f*t)+p[n+56>>2]}},function(r){return v[36+(r|=0)>>2]+50331648|0},Me,function(r){Me(r|=0),pt(r)},ro,function(r){pt(ro(r|=0))},Hu,function(r){pt(Hu(r|=0))},Gu,function(r){pt(Gu(r|=0))},Wu,function(r){pt(Wu(r|=0))},Su,function(r){pt(Su(r|=0))},Fa,hf,function(r){hf(r|=0),pt(r)},pa,eo,function(r){pt(eo(r|=0))},function(r){return v[(r|=0)>>2]=10360,Ju(r+36|0),Vu(r+20|0),Ju(r+4|0),0|r},function(r){v[(r|=0)>>2]=10360,Ju(r+36|0),Vu(r+20|0),Ju(r+4|0),pt(r)},function(r){return v[(r|=0)>>2]=10376,To(r+4|0),0|r},function(r){v[(r|=0)>>2]=10376,To(r+4|0),pt(r)},Ju,function(r){pt(Ju(r|=0))},Vu,function(r){pt(Vu(r|=0))},function(r){return v[(r|=0)>>2]=10424,To(r+208|0),To(r+192|0),Ao(r+176|0),To(r+160|0),To(r+144|0),To(r+128|0),_f(r+4|0),0|r},function(r){v[(r|=0)>>2]=10424,To(r+208|0),To(r+192|0),Ao(r+176|0),To(r+160|0),To(r+144|0),To(r+128|0),_f(r+4|0),pt(r)},io,function(r){pt(io(r|=0))},to,function(r){pt(to(r|=0))},Ku,function(r){pt(Ku(r|=0))},ct,function(r){pt(ct(r|=0))},bt,function(r){pt(bt(r|=0))},hn,function(r){hn(r|=0),pt(r)},Xu,function(r){pt(Xu(r|=0))},Yu,function(r){pt(Yu(r|=0))},Zu,function(r){pt(Zu(r|=0))},Du,function(r){pt(Du(r|=0))},qu,function(r){pt(qu(r|=0))},Nu,function(r){pt(Nu(r|=0))},uo,function(r){pt(uo(r|=0))},Bu,function(r){pt(Bu(r|=0))},Wf,function(r){Wf(r|=0),pt(r)},function(r){return v[(r|=0)>>2]=10752,To(r+20|0),Ou(r+4|0),0|r},function(r){v[(r|=0)>>2]=10752,To(r+20|0),Ou(r+4|0),pt(r)},Ou,function(r){pt(Ou(r|=0))},function(r){return v[(r|=0)>>2]=10784,Qu(r+4|0),0|r},function(r){v[(r|=0)>>2]=10784,Qu(r+4|0),pt(r)},en,function(r){en(r|=0),pt(r)},Qu,function(r){pt(Qu(r|=0))},le,function(r){pt(le(r|=0))},no,function(r){pt(no(r|=0))},function(r){return v[(r|=0)>>2]=10864,To(r+68|0),0|r},function(r){v[(r|=0)>>2]=10864,To(r+68|0),pt(r)},function(r){return v[(r|=0)>>2]=10880,gi(r+68|0),gi(r+8|0),0|r},function(r){v[(r|=0)>>2]=10880,gi(r+68|0),gi(r+8|0),pt(r)},gi,function(r){gi(r|=0),pt(r)},Fa,ta,ga,function(){return 21368},Fa,function(r){return v[(r|=0)>>2]=10940,go(r+8|0),0|r},function(r){v[(r|=0)>>2]=10940,go(r+8|0),pt(r)},function(){return 21380},function(r){r|=0;var n=w(0),e=0,f=0,t=w(0),u=0,o=w(0),a=w(0),c=0,b=0,k=w(0),s=w(0),h=w(0),y=w(0),F=w(0),A=w(0),$=w(0),I=w(0),C=w(0),P=w(0),E=w(0),O=w(0),R=w(0),S=w(0),W=0,G=0,U=0,j=w(0);if(f=v[r+4>>2],b=l[f+84|0],l[f+85|0]){if(b){if(k=p[r+40>>2],s=p[r+36>>2],h=p[r+32>>2],a=p[r+28>>2],u=v[r+24>>2],l[u+88|0]||Tr(u),v[r+12>>2])for(;f=v[v[r+20>>2]+(c<<2)>>2],l[f+88|0]||Tr(f),o=p[f+68>>2],o=a!=w(0)?w(w(w(p[u+68>>2]+p[v[r+4>>2]+60>>2])*a)+o):o,$=p[f+64>>2],I=p[f+60>>2],h!=w(0)&&(b=v[r+4>>2],$=w(w(w(p[u+64>>2]+p[b+68>>2])*h)+$),I=w(w(w(p[u+60>>2]+p[b+64>>2])*h)+I)),n=p[f+76>>2],t=p[f+72>>2],s!=w(0)&&(t=t>w(9999999747378752e-21)?w(t*w(w(w(w(p[u+72>>2]+w(-1))+p[v[r+4>>2]+72>>2])*s)+w(1))):t,n>w(9999999747378752e-21)&&(n=w(n*w(w(w(w(p[u+76>>2]+w(-1))+p[v[r+4>>2]+76>>2])*s)+w(1))))),C=p[f+84>>2],y=p[f+80>>2],k!=w(0)&&(C=w(w(w(p[u+84>>2]+p[v[r+4>>2]+80>>2])*k)+C)),br(f,I,$,o,t,n,y,C),(c=c+1|0)>>>0<d[r+12>>2];);return}if(y=p[r+40>>2],k=p[r+36>>2],a=p[r+32>>2],C=p[r+28>>2],b=v[r+24>>2],l[b+88|0]||Tr(b),v[r+12>>2])for(;u=v[v[r+20>>2]+(c<<2)>>2],l[u+88|0]||Tr(u),s=p[u+68>>2],C!=w(0)&&(n=w(w(p[b+68>>2]-s)+p[v[r+4>>2]+60>>2]),G=+w(n/w(-360))+16384.499999999996,f=g(G)<2147483648?~~G:-2147483648,s=w(w(w(n-w(0|m(16384-f|0,360)))*C)+s)),h=p[u+64>>2],o=p[u+60>>2],a!=w(0)&&(f=v[r+4>>2],h=w(w(w(w(p[b+64>>2]-h)+p[f+68>>2])*a)+h),o=w(w(w(w(p[b+60>>2]-o)+p[f+64>>2])*a)+o)),n=p[u+76>>2],t=p[u+72>>2],k!=w(0)&&(t=t>w(9999999747378752e-21)?w(w(w(w(w(p[b+72>>2]-t)+p[v[r+4>>2]+72>>2])*k)+t)/t):t,n>w(9999999747378752e-21)&&(n=w(w(w(w(w(p[b+76>>2]-n)+p[v[r+4>>2]+76>>2])*k)+n)/n))),I=p[u+84>>2],y!=w(0)&&($=w(w(p[b+84>>2]-I)+p[v[r+4>>2]+80>>2]),G=+w($/w(-360))+16384.499999999996,f=g(G)<2147483648?~~G:-2147483648,p[u+56>>2]=w(w($-w(0|m(16384-f|0,360)))*y)+p[u+56>>2]),br(u,o,h,s,t,n,p[u+80>>2],I),(c=c+1|0)>>>0<d[r+12>>2];);}else if(b){if(V=u=V-16|0,v[r+12>>2])for(f=v[r+4>>2],c=v[r+24>>2],F=p[c+92>>2],P=p[c+108>>2],A=p[c+104>>2],E=p[c+96>>2],n=w(w(F*P)-w(A*E))>w(0)?w(.01745329238474369):w(-.01745329238474369),C=w(p[f+80>>2]*n),I=w(p[f+60>>2]*n),R=p[r+40>>2],S=p[r+36>>2],s=p[r+32>>2],b=(h=p[r+28>>2])!=w(0),$=w(w(T(w(w(E*E)+w(P*P))))+w(-1)),o=w(w(T(w(w(F*F)+w(A*A))))+w(-1));;){e=v[v[r+20>>2]+(W<<2)>>2],h!=w(0)&&(k=p[e+108>>2],a=p[e+104>>2],y=p[e+96>>2],t=p[e+92>>2],(n=w(I+Or(A,F)))>w(3.1415927410125732)?n=w(n+w(-6.2831854820251465)):n<w(-3.1415927410125732)&&(n=w(n+w(6.2831854820251465))),O=Wr(n=w(h*n)),n=Ur(n),p[e+108>>2]=w(n*y)+w(k*O),p[e+104>>2]=w(n*t)+w(a*O),p[e+96>>2]=w(O*y)-w(k*n),p[e+92>>2]=w(O*t)-w(a*n)),f=b,s!=w(0)&&(f=v[r+4>>2],ti(c,p[f+64>>2],p[f+68>>2],u+12|0,u+8|0),p[e+100>>2]=w(p[u+12>>2]*s)+p[e+100>>2],p[e+112>>2]=w(p[u+8>>2]*s)+p[e+112>>2],f=1);r:{n:{e:{if(!(S>w(0))){if(R>w(0))break e;if(f)break n;break r}if(f=v[r+4>>2],n=w(w(w(o+p[f+72>>2])*S)+w(1)),p[e+92>>2]=p[e+92>>2]*n,p[e+104>>2]=n*p[e+104>>2],n=w(w(w($+p[f+76>>2])*S)+w(1)),p[e+96>>2]=p[e+96>>2]*n,p[e+108>>2]=n*p[e+108>>2],!(R>w(0)))break n}(n=w(Or(P,E)-Or(A,F)))>w(3.1415927410125732)?n=w(n+w(-6.2831854820251465)):n<w(-3.1415927410125732)&&(n=w(n+w(6.2831854820251465))),t=w(w(C+w(n+w(-1.5707963705062866)))*R),y=p[e+108>>2],n=p[e+96>>2],t=w(t+Or(y,n)),n=w(T(w(w(n*n)+w(y*y)))),U=e,j=w(Ur(t)*n),p[U+108>>2]=j,U=e,j=w(Wr(t)*n),p[U+96>>2]=j}i[e+88|0]=0}if(!((W=W+1|0)>>>0<d[r+12>>2]))break}V=u+16|0}else{if(V=u=V-16|0,v[r+12>>2])for(f=v[r+4>>2],c=v[r+24>>2],F=p[c+92>>2],P=p[c+108>>2],A=p[c+104>>2],E=p[c+96>>2],n=w(w(F*P)-w(A*E))>w(0)?w(.01745329238474369):w(-.01745329238474369),y=w(p[f+80>>2]*n),C=w(p[f+60>>2]*n),R=p[r+40>>2],S=p[r+36>>2],s=p[r+32>>2],b=(h=p[r+28>>2])!=w(0),I=w(T(w(w(E*E)+w(P*P)))),$=w(T(w(w(F*F)+w(A*A))));;){e=v[v[r+20>>2]+(W<<2)>>2],h!=w(0)&&(k=p[e+108>>2],a=p[e+96>>2],n=Or(A,F),o=p[e+104>>2],t=p[e+92>>2],(n=w(C+w(n-Or(o,t))))>w(3.1415927410125732)?n=w(n+w(-6.2831854820251465)):n<w(-3.1415927410125732)&&(n=w(n+w(6.2831854820251465))),O=Wr(n=w(h*n)),n=Ur(n),p[e+108>>2]=w(n*a)+w(k*O),p[e+104>>2]=w(n*t)+w(o*O),p[e+96>>2]=w(O*a)-w(k*n),p[e+92>>2]=w(O*t)-w(o*n)),f=b,s!=w(0)&&(f=v[r+4>>2],ti(c,p[f+64>>2],p[f+68>>2],u+12|0,u+8|0),n=p[e+100>>2],p[e+100>>2]=w(w(p[u+12>>2]-n)*s)+n,n=p[e+112>>2],p[e+112>>2]=w(w(p[u+8>>2]-n)*s)+n,f=1);r:{n:{e:{f:{if(S>w(0)){if(o=p[e+92>>2],t=p[e+104>>2],(n=w(T(w(w(o*o)+w(t*t)))))>w(9999999747378752e-21)&&(n=w(w(w(w(w($-n)+p[v[r+4>>2]+72>>2])*S)+n)/n)),k=w(t*n),p[e+104>>2]=k,t=w(o*n),p[e+92>>2]=t,a=p[e+96>>2],o=p[e+108>>2],(n=w(T(w(w(a*a)+w(o*o)))))>w(9999999747378752e-21)&&(n=w(w(w(w(w(I-n)+p[v[r+4>>2]+76>>2])*S)+n)/n)),o=w(o*n),p[e+108>>2]=o,n=w(a*n),p[e+96>>2]=n,R>w(0))break f;break n}if(!(R>w(0)))break e;t=p[e+92>>2],k=p[e+104>>2],o=p[e+108>>2],n=p[e+96>>2]}a=Or(o,n),(t=w(w(Or(P,E)-Or(A,F))-w(a-Or(k,t))))>w(3.1415927410125732)?t=w(t+w(-6.2831854820251465)):t<w(-3.1415927410125732)&&(t=w(t+w(6.2831854820251465))),o=w(T(w(w(n*n)+w(o*o)))),n=w(w(w(y+t)*R)+a),U=e,j=w(o*Ur(n)),p[U+108>>2]=j,U=e,j=w(o*Wr(n)),p[U+96>>2]=j;break n}if(!f)break r}i[e+88|0]=0}if(!((W=W+1|0)>>>0<d[r+12>>2]))break}V=u+16|0}},Lo,wo,Iu,Fa,function(r){return v[(r|=0)>>2]=10976,mo(r+24|0),0|Dt(r)},function(r){v[(r|=0)>>2]=10976,mo(r+24|0),pt(Dt(r))},function(){return 21392},Fa,function(r){return v[(r|=0)>>2]=11e3,To(r+20|0),0|Kt(r)},function(r){v[(r|=0)>>2]=11e3,To(r+20|0),pt(Kt(r))},function(){return 21404},function(r,n,e,f,i,t,u,o){r|=0,n|=0,e=w(e),f=w(f),i|=0,t=w(t),u|=0,o|=0;var a=w(0),c=w(0),b=w(0),k=0,s=w(0);r:if(n=v[v[n+84>>2]+(v[r+36>>2]<<2)>>2],0|Ta[v[v[n>>2]+16>>2]](n)){if(i=v[r+32>>2],p[i>>2]>f){n:switch(0|u){case 0:return r=v[n+4>>2],p[n+28>>2]=p[r+44>>2],p[n+32>>2]=p[r+48>>2],p[n+36>>2]=p[r+52>>2],void(p[n+40>>2]=p[r+56>>2]);case 1:break n;default:break r}return r=v[n+4>>2],e=p[n+28>>2],p[n+28>>2]=w(w(p[r+44>>2]-e)*t)+e,e=p[n+32>>2],p[n+32>>2]=w(w(p[r+48>>2]-e)*t)+e,e=p[n+36>>2],p[n+36>>2]=w(w(p[r+52>>2]-e)*t)+e,e=p[n+40>>2],void(p[n+40>>2]=w(w(p[r+56>>2]-e)*t)+e)}if(i=i+(v[r+24>>2]<<2)|0,p[i-20>>2]<=f?(f=p[i-4>>2],e=p[i-8>>2],c=p[i-12>>2],a=p[i-16>>2]):(i=(k=(o=Se(r+20|0,f,5))<<2)+v[r+32>>2]|0,b=p[i-16>>2],c=p[i-12>>2],e=p[i-8>>2],s=p[i-4>>2],a=f,f=p[i>>2],a=vn(r,((0|o)/5|0)-1|0,w(w(1)-w(w(a-f)/w(p[i-20>>2]-f)))),r=v[r+32>>2]+k|0,f=w(s+w(a*w(p[r+16>>2]-s))),e=w(e+w(a*w(p[r+12>>2]-e))),c=w(c+w(a*w(p[r+8>>2]-c))),a=w(b+w(a*w(p[r+4>>2]-b)))),!u)return b=a,r=v[n+4>>2],a=p[r+44>>2],p[n+28>>2]=w(w(b-a)*t)+a,a=p[r+48>>2],p[n+32>>2]=w(w(c-a)*t)+a,a=e,e=p[r+52>>2],p[n+36>>2]=w(w(a-e)*t)+e,e=p[r+56>>2],void(p[n+40>>2]=w(w(f-e)*t)+e);b=a,a=p[n+28>>2],p[n+28>>2]=w(w(b-a)*t)+a,a=p[n+32>>2],p[n+32>>2]=w(w(c-a)*t)+a,a=e,e=p[n+36>>2],p[n+36>>2]=w(w(a-e)*t)+e,e=p[n+40>>2],p[n+40>>2]=w(w(f-e)*t)+e}},function(r){return v[36+(r|=0)>>2]+167772160|0},Fa,function(r){Et(r|=0),pt(r)},function(){return 21416},function(r,n,e,f,i,t,u,o){r|=0,n|=0,e=w(e),f=w(f),i|=0,t=w(t),u|=0,o|=0;var a=w(0),c=0,b=w(0);r:if(n=v[v[n+20>>2]+(v[r+36>>2]<<2)>>2],l[n+117|0]){if(i=v[r+32>>2],p[i>>2]>f){n:switch(0|u){case 0:return r=v[n+4>>2],p[n+32>>2]=p[r+28>>2],void(p[n+36>>2]=p[r+32>>2]);case 1:break n;default:break r}return r=v[n+4>>2],e=p[n+32>>2],p[n+32>>2]=w(w(p[r+28>>2]-e)*t)+e,e=p[n+36>>2],void(p[n+36>>2]=w(w(p[r+32>>2]-e)*t)+e)}i=i+(v[r+24>>2]<<2)|0,p[i-12>>2]<=f?(f=p[i-4>>2],e=p[i-8>>2]):(i=(c=(o=Se(r+20|0,f,3))<<2)+v[r+32>>2]|0,e=p[i-8>>2],b=p[i-4>>2],a=f,f=p[i>>2],a=vn(r,((0|o)/3|0)-1|0,w(w(1)-w(w(a-f)/w(p[i-12>>2]-f)))),r=v[r+32>>2]+c|0,f=w(b+w(a*w(p[r+8>>2]-b))),e=w(e+w(a*w(p[r+4>>2]-e))));n:switch(0|u){case 0:return r=v[n+4>>2],p[n+32>>2]=w(e*t)+p[r+28>>2],void(p[n+36>>2]=w(f*t)+p[r+32>>2]);case 1:case 2:return r=v[n+4>>2],a=w(e+p[r+28>>2]),e=p[n+32>>2],p[n+32>>2]=w(w(a-e)*t)+e,e=p[n+36>>2],void(p[n+36>>2]=w(w(w(f+p[r+32>>2])-e)*t)+e);case 3:break n;default:break r}p[n+32>>2]=w(e*t)+p[n+32>>2],p[n+36>>2]=w(f*t)+p[n+36>>2]}},function(r){return v[36+(r|=0)>>2]+16777216|0},_f,function(r){_f(r|=0),pt(r)},Fa,function(r){return v[(r|=0)>>2]=11100,To(r+20|0),0|Kt(r)},function(r){v[(r|=0)>>2]=11100,To(r+20|0),pt(Kt(r))},function(){return 21428},function(r,n,e,f,i,t,u,o){r|=0,n|=0,e=w(e),f=w(f),i|=0,t=w(t),u|=0,o|=0;var a=0,c=w(0),b=w(0),k=w(0),s=w(0),l=w(0),h=w(0),d=0,y=0,m=0,g=0,F=w(0);r:if(n=v[v[n+36>>2]+(v[r+36>>2]<<2)>>2],i=v[n+8>>2],0|Ta[v[v[i>>2]+16>>2]](i)){if(d=n+36|0,y=n+16|0,i=Xo(v[n+4>>2]),o=la(v[n+4>>2]),a=v[r+32>>2],p[a>>2]>f){n:switch(0|u){case 0:return p[n+20>>2]=p[i+4>>2],p[n+24>>2]=p[i+8>>2],p[n+28>>2]=p[i+12>>2],p[n+32>>2]=p[i+16>>2],vf(y),p[n+40>>2]=p[o+4>>2],p[n+44>>2]=p[o+8>>2],p[n+48>>2]=p[o+12>>2],p[n+52>>2]=p[o+16>>2],void vf(d);case 1:break n;default:break r}return e=p[n+20>>2],p[n+20>>2]=w(w(e-p[i+4>>2])*t)+e,e=p[n+24>>2],p[n+24>>2]=w(w(e-p[i+8>>2])*t)+e,e=p[n+28>>2],p[n+28>>2]=w(w(e-p[i+12>>2])*t)+e,e=p[n+32>>2],p[n+32>>2]=w(w(e-p[i+16>>2])*t)+e,e=p[n+40>>2],p[n+40>>2]=w(w(e-p[o+4>>2])*t)+e,e=p[n+44>>2],p[n+44>>2]=w(w(e-p[o+8>>2])*t)+e,e=p[n+48>>2],void(p[n+48>>2]=w(w(e-p[o+12>>2])*t)+e)}if(a=a+(v[r+24>>2]<<2)|0,p[a-32>>2]<=f?(f=p[a-4>>2],b=p[a-8>>2],k=p[a-12>>2],c=p[a-16>>2],s=p[a-20>>2],l=p[a-24>>2],e=p[a-28>>2]):(a=(g=(m=Se(r+20|0,f,8))<<2)+v[r+32>>2]|0,h=p[a-28>>2],l=p[a-24>>2],s=p[a-20>>2],c=p[a-16>>2],k=p[a-12>>2],b=p[a-8>>2],F=p[a-4>>2],e=p[a>>2],e=vn(r,(m>>>3|0)-1|0,w(w(1)-w(w(f-e)/w(p[a-32>>2]-e)))),r=v[r+32>>2]+g|0,f=w(F+w(e*w(p[r+28>>2]-F))),b=w(b+w(e*w(p[r+24>>2]-b))),k=w(k+w(e*w(p[r+20>>2]-k))),c=w(c+w(e*w(p[r+16>>2]-c))),s=w(s+w(e*w(p[r+12>>2]-s))),l=w(l+w(e*w(p[r+8>>2]-l))),e=w(h+w(e*w(p[r+4>>2]-h)))),t==w(1))return p[n+32>>2]=c,p[n+28>>2]=s,p[n+24>>2]=l,p[n+20>>2]=e,vf(y),v[n+52>>2]=1065353216,p[n+48>>2]=f,p[n+44>>2]=b,p[n+40>>2]=k,void vf(d);u||(p[n+20>>2]=p[i+4>>2],p[n+24>>2]=p[i+8>>2],p[n+28>>2]=p[i+12>>2],p[n+32>>2]=p[i+16>>2],vf(y),p[n+40>>2]=p[o+4>>2],p[n+44>>2]=p[o+8>>2],p[n+48>>2]=p[o+12>>2],p[n+52>>2]=p[o+16>>2],vf(d)),h=p[n+32>>2],p[n+32>>2]=h+w(w(c-h)*t),c=p[n+28>>2],p[n+28>>2]=c+w(w(s-c)*t),c=p[n+24>>2],p[n+24>>2]=c+w(w(l-c)*t),c=p[n+20>>2],p[n+20>>2]=c+w(w(e-c)*t),vf(y),p[n+52>>2]=p[n+52>>2]+w(0),e=p[n+48>>2],p[n+48>>2]=e+w(w(f-e)*t),e=p[n+44>>2],p[n+44>>2]=e+w(w(b-e)*t),e=p[n+40>>2],p[n+40>>2]=e+w(w(k-e)*t),vf(d)}},function(r){return v[36+(r|=0)>>2]+234881024|0},Fa,ta,ga,function(){return 21440},Fa,ga,function(){return 21452},Fa,Fa,Fa,pa,function(){return 21480},sa,function(r,n,e){r|=0,n|=0,e|=0;var f=w(0),i=w(0),t=w(0),u=0,o=w(0);i=p[r+8>>2],f=p[r+4>>2],t=w(-f),u=n,o=w(ie(t,f,w(w(f-f)*w(.5)))+p[n>>2]),p[u>>2]=o,u=e,o=w(ie(t,i,w(w(i-f)*w(.5)))+p[e>>2]),p[u>>2]=o},Fa,pa,function(){return 21492},function(r,n){n|=0,p[20+(r|=0)>>2]=p[n+172>>2]+p[r+4>>2],p[r+24>>2]=p[n+176>>2]+p[r+8>>2]},function(r,n,e){r|=0,n|=0,e|=0;var f=w(0),i=w(0),t=w(0),u=w(0),o=0;t=w(p[n>>2]-p[r+20>>2]),u=w(p[e>>2]-p[r+24>>2]),(f=w(T(w(w(t*t)+w(u*u)))))<(i=p[r+12>>2])&&(o=v[r+28>>2],i=Ur(f=w(Ta[v[v[o>>2]+8>>2]](o,w(0),p[r+16>>2],w(w(i-f)/i)))),f=Wr(f),p[n>>2]=p[r+20>>2]+w(w(f*t)-w(u*i)),p[e>>2]=w(w(i*t)+w(u*f))+p[r+24>>2])},Fa,function(){return 21533},lu,$o,function(){return 0|Yi(11260)},cn,vi,zo,Ci,function(r,n){return n|=0,v[12+(r|=0)>>2]+m(n,20)|0},function(r,n,e){n|=0,e|=0;var f=0;return f=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),r=0|Ta[0|f](n,e),n=Wt(20),v[n+16>>2]=v[r+16>>2],e=v[r+12>>2],v[n+8>>2]=v[r+8>>2],v[n+12>>2]=e,e=v[r+4>>2],v[n>>2]=v[r>>2],v[n+4>>2]=e,0|n},function(){return 21539},lu,$o,function(){return 0|Yi(8776)},wn,function(r,n,e,f){var i,t;r|=0,n|=0,e|=0,f=w(f),V=i=V-16|0,n=((t=v[r+4>>2])>>1)+n|0,r=v[r>>2],r=1&t?v[v[n>>2]+r>>2]:r,p[i+12>>2]=f,Ta[0|r](n,e,i+12|0),V=i+16|0},zo,Ci,xt,function(r,n,e){n|=0,e|=0;var f=0;return f=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),w(p[Ta[0|f](n,e)>>2])},function(r,n,e){n|=0,e|=0,p[v[12+(r|=0)>>2]+(n<<2)>>2]=p[e>>2]},function(){return 21544},lu,$o,function(){return 0|Yi(9464)},function(r,n,e){r|=0,n|=0,e|=0;var f=0,i=w(0),t=0,u=0,o=0;f=v[r+4>>2],v[r+4>>2]=n,d[r+8>>2]<n>>>0&&(i=w(w(n>>>0)*w(1.75)),n=(n=w(g(i))<w(2147483648)?~~i:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,t=v[5280],u=r,o=0|Ta[v[v[t>>2]+16>>2]](t,v[r+12>>2],n<<4,8590,85),v[u+12>>2]=o,n=v[r+4>>2]);r:if(n>>>0<=f>>>0){if(n>>>0>=f>>>0)break r;for(;e=v[r+12>>2]+(n<<4)|0,Ta[v[v[e>>2]>>2]](e),(0|f)!=(0|(n=n+1|0)););}else for(;Bn(v[r+12>>2]+(f<<4)|0,e),(f=f+1|0)>>>0<d[r+4>>2];);},vi,zo,Ci,Mt,function(r,n,e){n|=0,e|=0;var f=0;return f=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),r=0|Ta[0|f](n,e),0|Bn(ut(16),r)},di,function(){return 21547},lu,$o,function(){return 0|Yi(8744)},gn,mf,zo,Ci,xt,ki,dt,function(){return 21550},lu,$o,function(){return 0|Yi(11596)},gn,mf,zo,Ci,xt,ki,dt,function(){return 21554},lu,$o,function(){return 0|Yi(9508)},function(r,n,e){r|=0,n|=0,e|=0;var f=0,i=w(0),t=0,u=0,o=0;f=v[r+4>>2],v[r+4>>2]=n,d[r+8>>2]<n>>>0&&(i=w(w(n>>>0)*w(1.75)),n=(n=w(g(i))<w(2147483648)?~~i:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,t=v[5280],u=r,o=0|Ta[v[v[t>>2]+16>>2]](t,v[r+12>>2],n<<4,8590,85),v[u+12>>2]=o,n=v[r+4>>2]);r:if(n>>>0<=f>>>0){if(n>>>0>=f>>>0)break r;for(;e=v[r+12>>2]+(n<<4)|0,Ta[v[v[e>>2]>>2]](e),(0|f)!=(0|(n=n+1|0)););}else for(;Nn(v[r+12>>2]+(f<<4)|0,e),(f=f+1|0)>>>0<d[r+4>>2];);},vi,zo,Ci,Mt,function(r,n,e){n|=0,e|=0;var f=0;return f=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),r=0|Ta[0|f](n,e),0|Nn(ut(16),r)},di,function(){return 21557},lu,$o,function(){return 0|Yi(10768)},function(r,n,e){r|=0,n|=0,e|=0;var f=0,i=w(0),t=0,u=0,o=0;if(f=v[r+4>>2],v[r+4>>2]=n,d[r+8>>2]<n>>>0&&(i=w(w(n>>>0)*w(1.75)),n=(n=w(g(i))<w(2147483648)?~~i:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,t=v[5280],u=r,o=0|Ta[v[v[t>>2]+16>>2]](t,v[r+12>>2],n<<2,8590,85),v[u+12>>2]=o,n=v[r+4>>2]),n>>>0>f>>>0)for(n=v[r+12>>2];v[n+(f<<2)>>2]=v[e>>2],(f=f+1|0)>>>0<d[r+4>>2];);},mf,zo,Ci,xt,ki,dt,function(){return 21560},lu,$o,function(){return 0|Yi(9852)},Fn,function(r,n,e,f){var i,t;n|=0,e|=0,f|=0,V=i=V-16|0,n=((t=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&t?v[v[n>>2]+r>>2]:r,s[i+14>>1]=f,Ta[0|r](n,e,i+14|0),V=i+16|0},zo,Ci,function(r,n){return n|=0,v[12+(r|=0)>>2]+(n<<1)|0},function(r,n,e){n|=0,e|=0;var f=0;return f=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),h[Ta[0|f](n,e)>>1]},function(r,n,e){n|=0,e|=0,s[v[12+(r|=0)>>2]+(n<<1)>>1]=h[e>>1]},function(){return 21564},lu,$o,function(){return 0|Yi(9212)},tn,Ve,zo,Ci,function(r,n){return n|=0,v[12+(r|=0)>>2]+m(n,12)|0},function(r,n,e){n|=0,e|=0;var f=0;return f=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),e=0|Ta[0|f](n,e),n=X((r=v[e+4>>2])+4|0),v[n>>2]=r,Ri(n+4|0,v[e+8>>2],r),0|n},function(r,n,e){n|=0,e|=0,te(v[12+(r|=0)>>2]+m(n,12)|0,e)},function(){return 21567},lu,$o,function(){return 0|Yi(9260)},$n,mf,zo,Ci,xt,ki,function(){return 21570},lu,$o,function(){return 0|Yi(9676)},$n,mf,zo,Ci,xt,ki,function(){return 21574},lu,$o,function(){return 0|Yi(10536)},$n,mf,zo,Ci,xt,ki,function(){return 21578},lu,$o,function(){return 0|Yi(10616)},$n,mf,zo,Ci,xt,ki,function(){return 21581},lu,$o,function(){return 0|Yi(10632)},$n,mf,zo,Ci,xt,ki,function(){return 21584},lu,$o,function(){return 0|Yi(10848)},$n,mf,zo,Ci,xt,ki,function(){return 21587},lu,$o,function(){return 0|Yi(10232)},$n,mf,zo,Ci,xt,ki,function(){return 21591},lu,$o,function(){return 0|Yi(10552)},$n,mf,zo,Ci,xt,ki,function(){return 21595},lu,$o,function(){return 0|Yi(10568)},$n,mf,zo,Ci,xt,ki,function(){return 21599},lu,$o,function(){return 0|Yi(8824)},$n,mf,zo,Ci,xt,ki,function(){return 21603},lu,$o,function(){return 0|Yi(10584)},$n,mf,zo,Ci,xt,ki,function(){return 21607},lu,$o,function(){return 0|Yi(10248)},$n,mf,zo,Ci,xt,ki,function(){return 21610},lu,$o,function(){return 0|Yi(10600)},$n,mf,zo,Ci,xt,ki,function(){return 21613},lu,$o,function(){return 0|Yi(10264)},$n,mf,zo,Ci,xt,ki,function(){return 21616},lu,$o,function(){return 0|Yi(10280)},$n,mf,zo,Ci,xt,ki,function(){return 21619},lu,$o,function(){return 0|Yi(1048)},$n,mf,zo,Ci,xt,ki,dt,function(){return 21622},lu,$o,function(){return 0|Yi(8760)},$n,mf,zo,Ci,xt,ki,function(){return 21626},lu,$o,function(){return 0|Yi(10296)},$n,mf,zo,Ci,xt,ki,function(){return 21629},lu,$o,function(){return 0|Yi(12912)},$n,mf,zo,Ci,xt,ki,function(){return 21633},lu,$o,function(){return 0|Yi(12976)},function(r,n,e){r|=0,n|=0,e|=0;var f=0,i=w(0),t=0,u=0,o=0;f=v[r+4>>2],v[r+4>>2]=n,d[r+8>>2]<n>>>0&&(i=w(w(n>>>0)*w(1.75)),n=(n=w(g(i))<w(2147483648)?~~i:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,t=v[5280],u=r,o=0|Ta[v[v[t>>2]+16>>2]](t,v[r+12>>2],n<<4,8590,85),v[u+12>>2]=o,n=v[r+4>>2]);r:if(n>>>0<=f>>>0){if(n>>>0>=f>>>0)break r;for(;e=v[r+12>>2]+(n<<4)|0,Ta[v[v[e>>2]>>2]](e),(0|f)!=(0|(n=n+1|0)););}else for(;Mo(v[r+12>>2]+(f<<4)|0,e),(f=f+1|0)>>>0<d[r+4>>2];);},vi,zo,Ci,Mt,function(r,n,e){n|=0,e|=0;var f=0;return f=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),r=0|Ta[0|f](n,e),0|Mo(ut(16),r)},function(){return 21636},ra,$o,function(){var r;return r=Wt(8),v[r>>2]=0,v[r+4>>2]=0,0|r},ai,function(r,n){return r|=0,n|=0,0|vt(Wt(8),p[r>>2],p[n>>2])},qt,zt,vt,function(r,n,e,f){r|=0,n|=0,e=w(e),f=w(f);var i=0;return i=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),r=0|Ta[0|i](n,e,f),n=Wt(8),i=v[r+4>>2],v[n>>2]=v[r>>2],v[n+4>>2]=i,0|n},function(r){r|=0;var n=w(0),e=w(0);return n=p[r>>2],e=w(n*n),n=p[r+4>>2],w(w(T(w(e+w(n*n)))))},pi,function(r){r|=0;var n=w(0),e=w(0),f=w(0);return n=p[r>>2],e=p[r+4>>2],f=w(w(1)/w(T(w(w(n*n)+w(e*e))))),p[r+4>>2]=e*f,p[r>>2]=n*f,0|r},function(r,n){n|=0;var e=0;return e=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(e=v[e+v[n>>2]>>2]),r=0|Ta[0|e](n),n=Wt(8),e=v[r+4>>2],v[n>>2]=v[r>>2],v[n+4>>2]=e,0|n},function(){return 21639},lu,$o,function(){var r;return r=ut(20),v[r+4>>2]=0,v[r+8>>2]=0,v[r>>2]=9372,v[r+12>>2]=0,v[r+16>>2]=0,0|r},function(r,n,e,f,i){var t;return r|=0,n=w(n),e=w(e),f=w(f),i=w(i),V=t=V-16|0,p[t+12>>2]=n,p[t+8>>2]=e,p[t+4>>2]=f,p[t>>2]=i,r=0|Ta[0|r](t+12|0,t+8|0,t+4|0,t),V=t+16|0,0|r},function(r,n,e,f){r|=0,n|=0,e|=0,f|=0;var i,t=w(0),u=w(0),o=w(0);return i=ut(20),t=p[r>>2],u=p[n>>2],o=p[e>>2],p[i+16>>2]=p[f>>2],p[i+12>>2]=o,p[i+8>>2]=u,p[i+4>>2]=t,v[i>>2]=9372,vf(i),0|i},function(r,n,e,f,i){return r|=0,n=w(n),e=w(e),f=w(f),i=w(i),p[r+16>>2]=i,p[r+12>>2]=f,p[r+8>>2]=e,p[r+4>>2]=n,vf(r),0|r},function(r,n,e,f,i,t){r|=0,n|=0,e=w(e),f=w(f),i=w(i),t=w(t);var u=0;return u=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(u=v[u+v[n>>2]>>2]),n=0|Ta[0|u](n,e,f,i,t),r=ut(20),v[r>>2]=9372,u=v[n+8>>2],v[r+4>>2]=v[n+4>>2],v[r+8>>2]=u,u=v[n+16>>2],v[r+12>>2]=v[n+12>>2],v[r+16>>2]=u,0|r},function(r,n,e,f,i){return r|=0,n=w(n),e=w(e),f=w(f),i=w(i),p[r+4>>2]=p[r+4>>2]+n,p[r+8>>2]=p[r+8>>2]+e,p[r+12>>2]=p[r+12>>2]+f,p[r+16>>2]=p[r+16>>2]+i,vf(r),0|r},vf,function(r,n){n|=0;var e=0;return e=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(e=v[e+v[n>>2]>>2]),n=0|Ta[0|e](n),r=ut(20),v[r>>2]=9372,e=v[n+8>>2],v[r+4>>2]=v[n+4>>2],v[r+8>>2]=e,e=v[n+16>>2],v[r+12>>2]=v[n+12>>2],v[r+16>>2]=e,0|r},qt,zt,Ct,ru,bi,function(){return 21642},lu,$o,function(){var r;return r=Wt(12),v[r+4>>2]=0,v[r+8>>2]=0,v[r>>2]=8728,0|r},At,lu,wi,function(r){return r|=0,0|at(ut(24),r)},na,bf,uu,Zt,ou,Yt,At,ta,ta,lu,wi,function(r){return r|=0,0|Qe(ut(60),r)},Xo,uu,Zt,uu,Zt,ou,Yt,qt,zt,At,ta,ta,lu,wi,function(r){return r|=0,0|tt(ut(76),r)},Xo,uu,Zt,uu,Zt,uu,Zt,uu,Zt,qt,zt,function(){return 21648},lu,$o,function(){var r;return r=ut(68),v[r+40>>2]=0,v[r+44>>2]=0,v[r+36>>2]=10392,v[r+32>>2]=0,v[r+24>>2]=0,v[r+28>>2]=0,v[r+20>>2]=10408,v[r+16>>2]=0,v[r+8>>2]=0,v[r+12>>2]=0,v[r+4>>2]=10392,v[r>>2]=10360,v[r+48>>2]=0,v[r+52>>2]=0,v[r+56>>2]=0,v[r+60>>2]=0,v[r- -64>>2]=0,0|r},function(r,n,e){r|=0,n|=0,e|=0;var f,i=0,t=0,u=0,o=w(0),a=0,c=w(0),b=w(0),k=w(0),s=w(0),l=0,h=0,y=0,m=0,g=0;if(V=f=V-16|0,l=v[n+28>>2],v[r+24>>2]=0,i=v[r+40>>2])for(a=r+4|0;Un(a,v[r+48>>2]+(t<<2)|0),(0|i)!=(0|(t=t+1|0)););if(v[r+40>>2]=0,l)for(m=r+36|0,g=r+20|0,t=0;a=v[v[n+36>>2]+(t<<2)>>2],i=v[a+8>>2],0|Ta[v[v[i>>2]+16>>2]](i)&&(i=v[a+60>>2])&&Si(0|Ta[v[v[i>>2]+8>>2]](i),21012)&&(v[f+12>>2]=i,Un(g,f+12|0),v[f+8>>2]=0,(i=v[r+8>>2])?(u=i-1|0,i=v[v[r+16>>2]+(u<<2)>>2],v[r+8>>2]=u):(i=ut(24),v[i+4>>2]=8776,v[i>>2]=10376,v[i+8>>2]=0,v[i+12>>2]=0,v[i+16>>2]=0,v[i+20>>2]=0,ya(i+4|0,16)),v[f+8>>2]=i,Un(m,f+8|0),u=v[f+8>>2],i=v[f+12>>2],h=v[i+52>>2],v[u+20>>2]=h,y=u+4|0,d[u+8>>2]<h>>>0&&(v[f+4>>2]=0,wn(y,h,f+4|0),i=v[f+12>>2]),vr(i,a,0,v[i+52>>2],v[y+12>>2],0,2)),(0|l)!=(0|(t=t+1|0)););if(e){if(n=0,t=v[r+40>>2])for(l=v[r+48>>2],k=w(34028234663852886e22),s=w(11754943508222875e-54),c=w(11754943508222875e-54),b=w(34028234663852886e22);;){if(e=v[l+(n<<2)>>2],(0|(a=v[e+20>>2]))>0)for(i=v[e+16>>2],e=0;k=(o=p[(u=e<<2)+i>>2])<k?k:o,s=o>s?s:o,b=(o=p[i+(4|u)>>2])<b?b:o,c=o>c?c:o,(0|a)>(0|(e=e+2|0)););if((0|t)==(0|(n=n+1|0)))break}else c=w(11754943508222875e-54),b=w(34028234663852886e22),k=w(34028234663852886e22),s=w(11754943508222875e-54);p[r+64>>2]=b,p[r+60>>2]=k,p[r+56>>2]=c,p[r+52>>2]=s}else v[r+60>>2]=2139095039,v[r+64>>2]=2139095039,v[r+52>>2]=8388608,v[r+56>>2]=8388608;V=f+16|0},vi,function(r,n,e){r|=0,n=w(n),e=w(e);var f=0;return!(p[r+56>>2]<=e)|!(p[r+52>>2]<=n)|!(p[r+60>>2]>=n)||(f=p[r+64>>2]>=e),0|f},function(r,n,e,f){r|=0,n|=0,e=w(e),f=w(f);var i=0;return i=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),0|Ta[0|i](n,e,f)},function(r,n,e,f,i){r|=0,n=w(n),e=w(e),f=w(f),i=w(i);var t=w(0),u=w(0),o=w(0),a=w(0),c=0;return(t=p[r+52>>2])>=n&f<=t|(u=p[r+56>>2])>=e&i<=u|(o=p[r+60>>2])<=n&f>=o|(a=p[r+64>>2])<=e&i>=a||(c=1,f=w(w(i-e)/w(f-n)),(i=w(w(f*w(t-n))+e))>u&i<a||(i=w(w(f*w(o-n))+e))>u&i<a||(i=w(w(w(u-e)/f)+n))>t&i<o||(c=(n=w(w(w(a-e)/f)+n))>t&n<o)),0|c},function(r,n,e,f,i,t){r|=0,n|=0,e=w(e),f=w(f),i=w(i),t=w(t);var u=0;return u=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(u=v[u+v[n>>2]>>2]),0|Ta[0|u](n,e,f,i,t)},function(r,n){n|=0;var e=0;return!(p[56+(r|=0)>>2]<p[n+64>>2])|!(p[r+52>>2]<p[n+60>>2])|!(p[r+60>>2]>p[n+52>>2])||(e=p[r+64>>2]>p[n+56>>2]),0|e},function(r,n,e){e|=0;var f,i,t,u=0,o=0,a=0,c=0,b=0;if(V=f=V-80|0,i=(u=n|=0)+((n=v[4+(r|=0)>>2])>>1)|0,r=v[r>>2],r=1&n?v[v[i>>2]+r>>2]:r,v[f+12>>2]=10360,t=xo(f+16|0,e+4|0),v[(n=f+32|0)>>2]=10408,v[n+4>>2]=v[e+24>>2],u=v[e+28>>2],v[n+12>>2]=0,v[n+8>>2]=u,u&&(o=v[5280],c=n,b=0|Ta[v[v[o>>2]+12>>2]](o,u<<2,8590,210),v[c+12>>2]=b,o=v[n+4>>2]))for(u=0;v[(a=u<<2)+v[n+12>>2]>>2]=v[v[e+32>>2]+a>>2],(0|o)!=(0|(u=u+1|0)););return u=xo(f+48|0,e+36|0),o=v[e+64>>2],v[f+72>>2]=v[e+60>>2],v[f+76>>2]=o,o=v[e+56>>2],v[f+64>>2]=v[e+52>>2],v[f+68>>2]=o,r=0|Ta[0|r](i,f+12|0),v[f+12>>2]=10360,Ju(u),Vu(n),Ju(t),V=f+80|0,0|r},function(r,n,e){r|=0,n=w(n),e=w(e);var f,i=w(0),t=0,u=w(0),o=0,a=0,c=0,b=0,k=0,s=0,l=0;r:{if(f=v[r+40>>2])for(s=r,l=v[r+48>>2];;){if(a=0,r=v[(o<<2)+l>>2],(0|(c=v[r+20>>2]))>0)for(t=c-2|0,b=v[r+16>>2],r=0;!((i=p[(4|(k=r<<2))+b>>2])<e&(u=p[4+(t=(t<<2)+b|0)>>2])>=e)&(!(e<=i)|!(e>u))||(u=w(w(e-i)/w(u-i)),i=p[b+k>>2],w(w(u*w(p[t>>2]-i))+i)<n&&(a^=1)),t=r,(0|c)>(0|(r=r+2|0)););if(r=v[v[s+32>>2]+(o<<2)>>2],1&a)break r;if((0|f)==(0|(o=o+1|0)))break}r=0}return 0|r},function(r,n,e,f,i){r|=0,n=w(n),e=w(e),f=w(f),i=w(i);var t,u=0,o=w(0),a=w(0),c=0,b=w(0),k=w(0),s=0,l=w(0),h=0,d=0,y=w(0),m=w(0),g=w(0),F=w(0),A=w(0),T=w(0),$=0,I=w(0);b=n,k=e;r:{if(t=v[r+40>>2])for($=v[r+48>>2];;){u=v[(s<<2)+$>>2];n:{if(h=v[u+20>>2])for(y=w(w(b*i)-w(k*f)),m=w(-w(k-i)),d=v[u+16>>2],n=p[(u=d+(h<<2)|0)-8>>2],e=p[u-4>>2],g=w(b-f),I=w(-g),u=0;;){if(l=e,o=n,n=p[(c=u<<2)+d>>2],a=w(o-n),e=p[(4|c)+d>>2],F=w(w(o*e)-w(n*l)),A=w(l-e),T=w(w(g*A)+w(a*m)),!(!((a=w(w(w(y*a)+w(F*I))/T))>=o&n>=a)&(!(n<=a)|!(o>=a))|!(a>=b&f>=a)&(!(f<=a)|!(a<=b))||!((o=w(w(w(y*A)+w(F*m))/T))>=l&e>=o)&(!(e<=o)|!(o<=l)))){if(c=1,o>=k&i>=o)break n;if(i<=o&&o<=k)break n}if(!(h>>>0>(u=u+2|0)>>>0))break}c=0}if(u=v[v[r+32>>2]+(s<<2)>>2],c)break r;if((0|t)==(0|(s=s+1|0)))break}u=0}return 0|u},function(r){return w(w(p[60+(r|=0)>>2]-p[r+52>>2]))},pi,function(r){return w(w(p[64+(r|=0)>>2]-p[r+56>>2]))},function(){return 21652},lu,Ei,function(r,n){return r|=0,n|=0,0|ii(ut(40),p[r>>2],n)},zo,Qt,uu,Zt,qt,zt,Ii,yi,function(){return 21654},lu,wi,function(r){return r|=0,0|ci(ut(56),r)},na,bf,uu,Zt,qt,zt,Ii,yi,At,lu,na,bf,At,ta,ta,lu,Io,Ci,qo,Yo,uu,Zt,uu,Zt,Ji,function(r,n,e,f,i,t,u,o){n|=0,e|=0,f|=0,i|=0,t|=0,u|=0,o|=0;var a=0;a=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(a=v[a+v[n>>2]>>2]),Ta[0|a](n,e,f,i,t,u,o)},ui,$i,At,ta,ta,lu,wi,function(r){return r|=0,0|gu(ut(64),r)},bf,Ci,At,ta,ta,lu,wi,function(r){return r|=0,0|$t(ut(68),r)},function(r){return v[64+(r|=0)>>2]},function(r,n){n|=0,v[64+(r|=0)>>2]=n},Ci,$i,Ci,At,ta,ta,lu,wi,function(r){return r|=0,0|zr(ut(236),r)},Ii,yi,function(r){return 120+(r|=0)|0},Bo,function(r){return 136+(r|=0)|0},function(r){return 0|aa(r|=0)},Qt,qt,zt,uu,Zt,function(r){return 152+(r|=0)|0},lr,ji,function(r){return v[100+(r|=0)>>2]},Ci,Kn,$i,Ci,Kr,At,ta,ta,lu,wi,function(r){return r|=0,0|Xf(ut(84),r)},Jo,ou,Yt,Ci,At,ta,ta,lu,wi,function(r){return r|=0,0|Vi(ut(32),r)},qt,zt,function(r,n,e,f){var i;r|=0,n|=0,e=w(e),f=w(f),V=i=V-16|0,p[i+12>>2]=e,p[i+8>>2]=f,ti(n,p[r+20>>2],p[r+24>>2],i+12|0,i+8|0),V=i+16|0},function(r,n){r|=0,n|=0;var e=w(0),f=w(0),i=w(0),t=w(0);return i=p[n+96>>2],f=Ur(e=w(p[r+28>>2]*w(.01745329238474369))),t=p[n+92>>2],e=Wr(e),w(w(Or(w(w(e*p[n+104>>2])+w(f*p[n+108>>2])),w(w(e*t)+w(f*i)))*w(57.2957763671875)))},si,Ci,At,ta,ta,lu,wi,function(r){return r|=0,0|_r(ut(164),r)},qt,zt,function(r){return 0|ka(r|=0)},Qt,Ii,yi,Zo,Qn,function(r,n,e,f,i,t,u){r|=0,n|=0,e=w(e),f=w(f),i=w(i),t=w(t),u|=0;var o=0;o=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(o=v[o+v[n>>2]>>2]),Ta[0|o](n,e,f,i,t,u)},Ko,Jr,ji,function(r,n,e,f,i){f|=0,i|=0,Xr(r|=0,n|=0,v[12+(e|=0)>>2],f,i)},function(r,n,e,f,i,t){n|=0,e|=0,f|=0,i|=0,t|=0;var u=0;u=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(u=v[u+v[n>>2]>>2]),Ta[0|u](n,e,f,i,t)},Ci,At,lu,ze,ze,ze,ze,se,se,At,ta,ta,lu,Hi,function(r){return r|=0,0|mt(ut(8),v[r>>2])},se,se,ze,ze,ze,ze,function(){return 21659},lu,wi,function(r){return r|=0,0|Ze(ut(68),r)},function(r,n){Ue(r|=0,16+(n|=0)|0)},uu,Zt,uu,Zt,uu,Zt,function(){return 21662},lu,function(r,n){Ue(r|=0,8+(n|=0)|0)},uu,Zt,ou,Yt,function(){return 21665},lu,function(){return 21668},lu,function(r,n,e,f){var t;return r|=0,e|=0,f|=0,V=t=V-32|0,n=$e(t+20|0,4+(n|=0)|0,v[n>>2],0),v[t+16>>2]=e,i[t+15|0]=f,r=0|Ta[0|r](n,t+16|0,t+15|0),gi(n),V=t+32|0,0|r},function(r,n,e){var f,t,u,o,a;return r|=0,n|=0,e|=0,f=ut(40),a=l[0|e],V=t=V-16|0,v[f+36>>2]=v[n>>2],v[f+32>>2]=0,v[f+24>>2]=0,v[f+28>>2]=0,v[f+20>>2]=9048,v[f+16>>2]=0,v[f+8>>2]=0,v[f+12>>2]=0,v[f+4>>2]=9032,v[f>>2]=8904,u=t+4|0,e=fi(n=v[r+8>>2],47),n=ei(e=ht(u,o=(n=fi(n,92))>>>0<e>>>0?e:n,0),r),gi(e),e=v[5280],n=(n=n+o|0)?n-v[r+8>>2]|0:0,e=Ri(0|Ta[v[v[e>>2]+12>>2]](e,n+1|0,8590,54),v[r+8>>2],n),i[n+e|0]=0,n=v[5280],(n=0|Ta[v[v[n>>2]+24>>2]](n,r,u))&&tr(f,n,v[t+4>>2],e,a),r=v[5280],Ta[v[v[r>>2]+20>>2]](r,n,8590,63),r=v[5280],Ta[v[v[r>>2]+20>>2]](r,e,8590,64),V=t+16|0,0|f},nf,Be,Ct,ta,ta,ru,Hi,function(r){var n;return r|=0,n=Wt(8),v[n+4>>2]=v[r>>2],v[n>>2]=9756,0|n},Ct,ta,ta,ru,Hi,function(r){var n;return r|=0,n=Wt(8),v[n+4>>2]=v[r>>2],v[n>>2]=9784,0|n},function(){return 21647},lu,function(r,n,e,f){var i;return r|=0,n|=0,e|=0,f|=0,V=i=V-16|0,v[i+12>>2]=n,n=$e(i,e+4|0,v[e>>2],0),r=0|Ta[0|r](i+12|0,n,f),gi(n),V=i+16|0,0|r},function(r,n,e){return r|=0,n|=0,e|=0,0|Dn(ut(84),v[r>>2],n,e)},zo,Ci,Qo,bf,Oo,Qt,function(r){return 0|Xo(r|=0)},Qt,function(r){return 0|la(r|=0)},uu,Zt,At,lu,ji,Ci,Ci,$i,At,ta,ta,lu,St,function(r,n){return r|=0,n|=0,0|un(ut(48),r,n)},zo,Qt,Qo,uu,Zt,uu,Zt,ou,Yt,qt,zt,function(r,n,e,f,i,t,u,o){r|=0,n|=0,e=w(e),f=w(f),i|=0,t|=0,u|=0,o=w(o),Ta[0|r](n,e,f,i,t,u,o)},Ar,function(r,n,e,f,i,t,u,o,a){r|=0,n|=0,e|=0,f=w(f),i=w(i),t|=0,u|=0,o=w(o),a=w(a),Ta[0|r](n,e,f,i,t,u,o,a)},or,At,ta,ta,lu,St,function(r,n){return r|=0,n|=0,0|$r(ut(144),r,n)},zo,Qt,Qo,uu,Zt,qt,zt,At,ta,ta,lu,wi,function(r){return r|=0,0|it(ut(88),r)},Xo,function(r){return v[40+(r|=0)>>2]},Ci,function(r){return w(p[44+(r|=0)>>2])},pi,function(r){return w(p[48+(r|=0)>>2])},function(r){return w(p[52+(r|=0)>>2])},function(r){return w(p[56+(r|=0)>>2])},Mu,function(r){return w(p[64+(r|=0)>>2])},function(r){return w(p[68+(r|=0)>>2])},function(r){return w(p[72+(r|=0)>>2])},function(r){return w(p[76+(r|=0)>>2])},function(r){return w(p[80+(r|=0)>>2])},function(r){return l[84+(r|=0)|0]},Ci,function(r){return l[85+(r|=0)|0]},At,ta,ta,lu,St,function(r,n){return r|=0,n|=0,0|on(ut(48),r,n)},zo,Qt,Qo,Eo,Ci,qt,zt,At,ta,ta,lu,function(r,n,e,f){var i;return r|=0,n|=0,e|=0,f|=0,V=i=V-16|0,v[i+12>>2]=f,r=0|Ta[0|r](n,e,i+12|0),V=i+16|0,0|r},function(r,n,e){return r|=0,n|=0,e|=0,0|me(ut(120),r,n,v[e>>2])},zo,Qt,_o,Qt,Po,Ci,Vo,qt,zt,ou,Yt,Mi,ji,br,function(r,n,e,f,i,t,u,o,a){r|=0,n|=0,e=w(e),f=w(f),i=w(i),t=w(t),u=w(u),o=w(o),a=w(a);var c=0;c=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(c=v[c+v[n>>2]>>2]),Ta[0|c](n,e,f,i,t,u,o,a)},De,function(r){return w(w(Or(p[104+(r|=0)>>2],p[r+92>>2])*w(57.2957763671875)))},pi,function(r){return w(w(Or(p[108+(r|=0)>>2],p[r+96>>2])*w(57.2957763671875)))},function(r){r|=0;var n=w(0),e=w(0);return n=p[r+92>>2],e=w(n*n),n=p[r+104>>2],w(w(T(w(e+w(n*n)))))},function(r){r|=0;var n=w(0),e=w(0);return n=p[r+96>>2],e=w(n*n),n=p[r+108>>2],w(w(T(w(e+w(n*n)))))},function(r,n){r|=0,n|=0;var e,f=w(0),i=w(0),t=w(0),u=w(0),o=w(0),a=w(0),c=w(0);V=e=V-16|0,u=p[r+92>>2],f=p[r+108>>2],o=p[r+104>>2],i=p[r+96>>2],t=w(w(1)/w(w(u*f)-w(o*i))),a=f,f=w(p[n>>2]-p[r+100>>2]),c=i,i=w(p[n+4>>2]-p[r+112>>2]),p[e+12>>2]=w(w(a*f)*t)-w(t*w(c*i)),p[e+8>>2]=w(w(u*i)*t)-w(t*w(o*f)),p[n>>2]=p[e+12>>2],p[n+4>>2]=p[e+8>>2],V=e+16|0},function(r,n){var e;V=e=V-16|0,ti(r|=0,p[(n|=0)>>2],p[n+4>>2],e+12|0,e+8|0),p[n>>2]=p[e+12>>2],p[n+4>>2]=p[e+8>>2],V=e+16|0},function(r,n){r|=0,n=w(n);var e=w(0),f=w(0),i=w(0);return f=p[r+96>>2],n=Ur(e=w(n*w(.01745329238474369))),i=p[r+108>>2],e=Wr(e),w(w(w(w(Or(w(w(n*p[r+92>>2])-w(e*p[r+104>>2])),w(w(i*e)-w(n*f)))*w(57.2957763671875))+p[r+40>>2])-p[r+52>>2]))},bi,function(r,n){r|=0,n=w(n);var e=w(0),f=w(0),i=w(0);return f=p[r+96>>2],n=Ur(e=w(w(n-w(p[r+40>>2]-p[r+52>>2]))*w(.01745329238474369))),i=p[r+92>>2],e=Wr(e),w(w(Or(w(w(e*p[r+104>>2])+w(n*p[r+108>>2])),w(w(e*i)+w(n*f)))*w(57.2957763671875)))},function(r,n){r|=0,n=w(n);var e=w(0),f=w(0),t=w(0),u=w(0),o=w(0);i[r+88|0]=0,f=p[r+108>>2],n=Wr(e=w(n*w(.01745329238474369))),t=p[r+96>>2],e=Ur(e),p[r+108>>2]=w(t*e)+w(n*f),u=p[r+92>>2],o=p[r+104>>2],p[r+104>>2]=w(e*u)+w(n*o),p[r+96>>2]=w(n*t)-w(f*e),p[r+92>>2]=w(n*u)-w(o*e)},Ai,function(){return 21646},lu,lf,function(r,n,e){return r|=0,n|=0,e|=0,0|ne(ut(64),v[r>>2],n,v[e>>2])},zo,Ci,Qo,bf,uu,Zt,qt,zt,uu,Zt,ou,Yt,function(){return 21656},lu,St,function(r,n){return r|=0,n|=0,0|Cn(ut(84),r,n)},zo,Qt,_o,Qt,Vo,Qt,Yo,No,Po,Io,Ci,hi,$i,function(r,n){r|=0,n=w(n),p[r+64>>2]=p[v[r+12>>2]+160>>2]-n},Ai,function(r){return w(w(p[v[12+(r|=0)>>2]+160>>2]-p[r+64>>2]))},pi,Sn,ji,function(){return 21657},lu,wi,function(r){return r|=0,0|Ae(ut(68),r)},na,bf,Yo,function(r){return 52+(r|=0)|0},It,function(r,n,e,f,i){n|=0,e|=0,f|=0,i|=0;var t,u=0;V=t=V-16|0,n=((u=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&u?v[v[n>>2]+r>>2]:r,u=n,n=$e(t+4|0,f+4|0,v[f>>2],0),Ta[0|r](u,e,n,i),gi(n),V=t+16|0},function(r,n){r|=0;var e=0,f=0,i=0,t=0,u=0,o=0,a=0,c=0;if(f=v[40+(n|=0)>>2])for(c=r+36|0;;){t=v[n+48>>2]+(i<<2)|0;r:{if(u=v[r+40>>2]){if(e=0,o=v[t>>2],a=v[r+48>>2],(0|o)==v[a>>2])break r;for(;(0|u)!=(0|(e=e+1|0))&(0|o)!=v[a+(e<<2)>>2];);if(e>>>0<u>>>0)break r}Un(c,t),f=v[n+40>>2]}if(!((i=i+1|0)>>>0<f>>>0))break}if(f=v[n+56>>2])for(c=r+52|0,i=0;;){t=v[n+64>>2]+(i<<2)|0;r:{if(u=v[r+56>>2]){if(e=0,o=v[t>>2],a=v[r+64>>2],(0|o)==v[a>>2])break r;for(;(0|u)!=(0|(e=e+1|0))&(0|o)!=v[a+(e<<2)>>2];);if(e>>>0<u>>>0)break r}Un(c,t),f=v[n+56>>2]}if(!((i=i+1|0)>>>0<f>>>0))break}r:if(f=v[n+24>>2])for(u=r+16|0,r=0,e=0;;){if(t=v[n+32>>2],d[4+(t+(e<<4)|0)>>2]<=r>>>0){n:{e:{for(;;){if((0|f)==(0|(e=e+1|0)))break e;if(v[4+(t+(e<<4)|0)>>2])break}r=e;break n}r=f}if(i=r,f=e>>>0>=f>>>0,r=0,e=i,f)break r}if(i=v[12+(t+(e<<4)|0)>>2]+m(r,20)|0,sr(u,v[i>>2],i+4|0,v[i+16>>2]),r=r+1|0,!((f=v[n+24>>2])>>>0>e>>>0))break}},$i,function(r,n){r|=0;var e=0,f=0,i=0,t=0,u=0,o=0,a=0,c=0;if(i=v[40+(n|=0)>>2])for(c=r+36|0;;){u=v[n+48>>2]+(e<<2)|0;r:{if(t=v[r+40>>2]){if(f=0,o=v[u>>2],a=v[r+48>>2],(0|o)==v[a>>2])break r;for(;(0|t)!=(0|(f=f+1|0))&(0|o)!=v[a+(f<<2)>>2];);if(f>>>0<t>>>0)break r}Un(c,u),i=v[n+40>>2]}if(!(i>>>0>(e=e+1|0)>>>0))break}if(i=v[n+56>>2])for(c=r+52|0,e=0;;){u=v[n+64>>2]+(e<<2)|0;r:{if(t=v[r+56>>2]){if(f=0,o=v[u>>2],a=v[r+64>>2],(0|o)==v[a>>2])break r;for(;(0|t)!=(0|(f=f+1|0))&(0|o)!=v[a+(f<<2)>>2];);if(f>>>0<t>>>0)break r}Un(c,u),i=v[n+56>>2]}if(!(i>>>0>(e=e+1|0)>>>0))break}r:if(e=v[n+24>>2])for(u=r+16|0,i=0,f=0;;){if(t=v[n+32>>2],d[4+(t+(f<<4)|0)>>2]<=i>>>0){n:{e:{for(;;){if((0|(f=f+1|0))==(0|e))break e;if(v[4+(t+(f<<4)|0)>>2])break}r=f;break n}r=e}if(e=e>>>0<=f>>>0,i=0,f=r,e)break r}if(r=v[12+(t+(f<<4)|0)>>2]+m(i,20)|0,e=v[r+16>>2],t=Fu(0|Ta[v[v[e>>2]+8>>2]](e),21200),e=v[r+16>>2],i=i+1|0,sr(u,o=v[r>>2],a=r+4|0,r=t?Kr(e):0|Ta[v[v[e>>2]+12>>2]](e)),!(f>>>0<(e=v[n+24>>2])>>>0))break}},function(r,n,e){n|=0,e|=0;var f,i=0,t=0,u=0,o=0;V=f=V-16|0,v[12+(r|=0)>>2]=0,v[r+4>>2]=0,v[r+8>>2]=0,v[r>>2]=9212,Xt(f+4|0,n),n=v[f+8>>2],t=v[f+4>>2];r:if(!(n>>>0>=(i=v[t+4>>2])>>>0))for(;;){if(u=v[f+12>>2],o=v[t+12>>2],u>>>0>=d[4+(o+(n<<4)|0)>>2]){for(;;){if((0|i)==(0|(n=n+1|0)))break r;if(v[4+((n<<4)+o|0)>>2])break}if(v[f+8>>2]=n,u=0,n>>>0>=i>>>0)break r}if(i=v[12+((n<<4)+o|0)>>2],v[f+12>>2]=u+1,i=i+m(u,20)|0,v[i>>2]==(0|e)&&(An(r,i+4|0),t=v[f+4>>2],n=v[f+8>>2]),!((i=v[t+4>>2])>>>0>n>>>0))break}V=f+16|0},Pt,ze,function(r,n){n|=0;var e,f=0,i=0,t=0;V=e=V-16|0,v[12+(r|=0)>>2]=0,v[r+4>>2]=0,v[r+8>>2]=0,v[r>>2]=12912,Xt(e+4|0,n),n=v[e+8>>2],f=v[e+4>>2];r:if(!(n>>>0>=(i=v[f+4>>2])>>>0))for(;;){if(t=v[e+12>>2],f=v[f+12>>2],t>>>0>=d[4+(f+(n<<4)|0)>>2]){for(;;){if((0|i)==(0|(n=n+1|0)))break r;if(v[4+(f+(n<<4)|0)>>2])break}if(v[e+8>>2]=n,t=0,n>>>0>=i>>>0)break r}if(n=v[12+(f+(n<<4)|0)>>2],v[e+12>>2]=t+1,v[e>>2]=n+m(t,20),Un(r,e),n=v[e+8>>2],f=v[e+4>>2],!(n>>>0<(i=v[f+4>>2])>>>0))break}V=e+16|0},function(r,n,e){n|=0,e|=0;var f=0,i=0,t=0,u=0,o=0;r:if(!(d[24+(r|=0)>>2]<=n>>>0)&&(f=v[r+32>>2]+(n<<4)|0,v[f+4>>2])){for(;;){if(!ei(4+(v[f+12>>2]+m(i,20)|0)|0,e)){if((i=i+1|0)>>>0<d[f+4>>2])continue;break r}break}if(!((0|i)<0)){if(_i(v[16+(v[12+((e=n<<4)+v[r+32>>2]|0)>>2]+m(i,20)|0)>>2]),V=n=V-32|0,r=e+v[r+32>>2]|0,e=v[r+4>>2]-1|0,v[r+4>>2]=e,e>>>0>i>>>0)for(o=n+16|0;f=(e=m(i,20))+v[r+12>>2]|0,v[n+12>>2]=v[f>>2],u=Ue(o,f+4|0),v[n+28>>2]=v[f+16>>2],f=e+(t=v[r+12>>2])|0,e=t+(t=e+20|0)|0,v[f>>2]=v[e>>2],te(f+4|0,e+4|0),v[f+16>>2]=v[e+16>>2],e=v[r+12>>2]+t|0,v[e>>2]=v[n+12>>2],te(e+4|0,u),v[e+16>>2]=v[n+28>>2],gi(u),(e=v[r+4>>2])>>>0>(i=i+1|0)>>>0;);gi(4+(v[r+12>>2]+m(e,20)|0)|0),V=n+32|0}}},Ve,function(r,n,e){n|=0,e|=0;var f,i=0,t=0,u=0,o=0;V=f=V-16|0,v[12+(r|=0)>>2]=0,v[r+4>>2]=0,v[r+8>>2]=0,v[r>>2]=12912,Xt(f+4|0,n),n=v[f+8>>2],t=v[f+4>>2];r:if(!(n>>>0>=(i=v[t+4>>2])>>>0))for(;;){if(u=v[f+12>>2],o=v[t+12>>2],u>>>0>=d[4+(o+(n<<4)|0)>>2]){for(;;){if((0|i)==(0|(n=n+1|0)))break r;if(v[4+((n<<4)+o|0)>>2])break}if(v[f+8>>2]=n,u=0,n>>>0>=i>>>0)break r}if(i=v[12+((n<<4)+o|0)>>2],v[f+12>>2]=u+1,i=i+m(u,20)|0,v[i>>2]==(0|e)&&(v[f>>2]=i,Un(r,f),t=v[f+4>>2],n=v[f+8>>2]),!((i=v[t+4>>2])>>>0>n>>>0))break}V=f+16|0},function(){return 21675},function(r){(r|=0)&&(gi(r+4|0),ar(r))},lf,function(r,n,e){var f;return r|=0,n|=0,e|=0,f=Wt(20),e=v[e>>2],v[f>>2]=v[r>>2],Ue(f+4|0,n),v[f+16>>2]=e,0|f},uu,Zt,function(r,n){Ue(r|=0,4+(n|=0)|0)},Ro,function(){return 21678},lu,$o,function(){return 0|Pr(ut(232))},function(r){return 160+(r|=0)|0},Qt,function(r){return 176+(r|=0)|0},Qt,function(r){return 192+(r|=0)|0},rr,function(r,n,e,f){n|=0,e|=0,f|=0;var i=0;return i=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),0|Ta[0|i](n,e,f)},Of,$i,mi,ji,Cu,Ci,function(){return 21681},lu,$o,function(){return 0|Mr(ut(232))},Ii,yi,Vo,function(r){return 32+(r|=0)|0},function(r){return 48+(r|=0)|0},uu,Zt,No,Zo,Ko,function(r){return 116+(r|=0)|0},function(r){return 132+(r|=0)|0},qt,zt,du,Be,vu,Be,su,Be,au,cu,Be,hu,Be,qe,Be,eu,Be,fu,Be,iu,Be,function(r,n){n|=0;var e,f=0;r:{if(e=v[136+(r|=0)>>2])for(;;){if(ei(na(v[v[r+144>>2]+(f<<2)>>2]),n))break r;if((0|e)==(0|(f=f+1|0)))break}f=-1}return 0|f},function(){return 21684},lu,function(r,n,e,f){var i;return r|=0,n|=0,e|=0,f=w(f),V=i=V-16|0,n=$e(i+4|0,n+4|0,v[n>>2],0),v[i>>2]=e,r=0|Ta[0|r](n,i,f),E(v[i>>2]),gi(n),V=i+16|0,0|r},function(r,n,e){r|=0,n|=0,e=w(e);var f,i=0,t=0,u=0,o=0,a=0,c=0,b=0;if(V=f=V-32|0,i=v[n>>2],t=0|j(4696),u=0|U(0|i,0|t),E(0|t),o=+G(0|u,21553,0|(t=f+12|0)),W(v[f+12>>2]),E(0|u),v[f+24>>2]=0,v[f+16>>2]=0,v[f+20>>2]=0,v[f+12>>2]=1048,v[f+28>>2]=0,$n(i=t,t=o<4294967296&o>=0?~~o>>>0:0,f+28|0),v[f+8>>2]=0,t)for(;V=u=V-16|0,i=v[n>>2],v[u+8>>2]=v[f+8>>2],a=i,i=0|_(21553,u+8|0),c=f,b=0|U(0|a,0|i),v[c+4>>2]=b,E(0|i),V=u+16|0,o=+G(v[f+4>>2],7897,f+28|0),W(v[f+28>>2]),a=v[f+24>>2]+(v[f+8>>2]<<2)|0,i=o<4294967296&o>=0?~~o>>>0:0,v[a>>2]=i,E(v[f+4>>2]),u=v[f+8>>2]+1|0,v[f+8>>2]=u,u>>>0<t>>>0;);return n=r,r=f+12|0,n=qr(ut(48),n,r,e),so(r),V=f+32|0,0|n},Yo,bf,na,Pf,li,qt,zt,At,lu,Ci,At,ta,ta,lu,function(r){return 1+(d[8+(r|=0)>>2]/19|0)|0},Ci,function(r,n){n|=0,v[v[16+(r|=0)>>2]+m(n,76)>>2]=0},$i,yt,xr,function(r,n,e,f,i,t,u){r|=0,n|=0,e|=0,f=w(f),i=w(i),t=w(t),u=w(u);var o=0;o=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(o=v[o+v[n>>2]>>2]),Ta[0|o](n,e,f,i,t,u)},vn,function(r,n,e,f){r|=0,n|=0,e|=0,f=w(f);var i=0;return i=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),w(w(Ta[0|i](n,e,f)))},function(r,n){return n|=0,w(p[v[16+(r|=0)>>2]+m(n,76)>>2])},si,At,ta,ta,lu,Hi,function(r){return r|=0,0|_e(ut(40),v[r>>2])},function(r){return v[(r|=0)>>2]},Wi,function(r,n,e,f,i,t){r|=0,n|=0,e|=0,f=w(f),i=w(i),t=w(t);var u=0;u=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(u=v[u+v[n>>2]>>2]),Ta[0|u](n,e,f,i,t)},At,ta,ta,lu,Hi,function(r){return r|=0,0|pu(ut(40),v[r>>2])},At,ta,ta,lu,Hi,function(r){return r|=0,0|yu(ut(40),v[r>>2])},At,ta,ta,lu,Hi,function(r){return r|=0,0|ff(ut(40),v[r>>2])},uu,Zt,Xo,Di,ni,At,ta,ta,lu,Hi,function(r){return r|=0,0|ef(ut(40),v[r>>2])},uu,Zt,Xo,Mf,gf,At,ta,ta,lu,Hi,function(r){return r|=0,0|Je(ut(40),v[r>>2])},function(r){return v[36+(r|=0)>>2]},_u,Ci,$i,Ge,function(r,n,e,f,i,t,u,o,a,c,b){r|=0,n|=0,e|=0,f=w(f),i=w(i),t=w(t),u=w(u),o=w(o),a=w(a),c=w(c),b=w(b);var k=0;k=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(k=v[k+v[n>>2]>>2]),Ta[0|k](n,e,f,i,t,u,o,a,c,b)},At,ta,ta,lu,Hi,function(r){return r|=0,0|ln(ut(40),v[r>>2])},uu,Zt,Qo,Xo,function(r,n){n|=0;var e=0;return e=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(e=v[e+v[n>>2]>>2]),r=0|Ta[0|e](n),0|zn(ut(16),r)},Po,Ci,xi,function(r,n,e,f,i){r|=0,n|=0,e|=0,f=w(f),i|=0;var t,u=0;V=t=V-16|0,n=((u=v[r+4>>2])>>1)+n|0,r=v[r>>2],r=1&u?v[v[n>>2]+r>>2]:r,u=n,n=$e(t+4|0,i+4|0,v[i>>2],0),Ta[0|r](u,e,f,n),gi(n),V=t+16|0},At,ta,ta,lu,Hi,function(r){return r|=0,0|Sr(ut(60),v[r>>2])},uu,Zt,uu,Zt,Xo,Do,function(r,n,e,f){r|=0,n|=0,e=w(e);var i,t=0,u=0,o=0;if(V=i=V-32|0,t=v[(f|=0)>>2],u=0|j(4696),t=0|U(0|t,0|u),E(0|u),o=+G(0|t,21553,0|(u=i+12|0)),W(v[i+12>>2]),E(0|t),v[i+24>>2]=0,v[i+16>>2]=0,v[i+20>>2]=0,v[i+12>>2]=8776,v[i+28>>2]=0,wn(u,t=o<4294967296&o>=0?~~o>>>0:0,i+28|0),v[i+8>>2]=0,t)for(;qf(i+4|0,f,i+8|0),o=+G(v[i+4>>2],21542,i+28|0),W(v[i+28>>2]),p[v[i+24>>2]+(v[i+8>>2]<<2)>>2]=o,E(v[i+4>>2]),u=v[i+8>>2]+1|0,v[i+8>>2]=u,u>>>0<t>>>0;);Zi(f=r,n,e,r=i+12|0),To(r),V=i+32|0},At,ta,ta,lu,Hi,function(r){return r|=0,0|de(ut(36),v[r>>2])},na,qo,_o,Ci,Ni,vi,At,ta,ta,lu,Hi,function(r){return r|=0,0|Hr(ut(36),v[r>>2])},na,_o,Ci,qo,Ce,function(r,n,e,f,i){r|=0,n|=0,e|=0,f=w(f),i|=0;var t=0;t=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(t=v[t+v[n>>2]>>2]),Ta[0|t](n,e,f,i)},At,ta,ta,lu,Hi,function(r){return r|=0,0|So(ut(40),v[r>>2])},of,function(r,n,e,f,i,t,u,o,a){r|=0,n|=0,e|=0,f=w(f),i=w(i),t=w(t),u|=0,o|=0,a|=0;var c=0;c=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(c=v[c+v[n>>2]>>2]),Ta[0|c](n,e,f,i,t,u,o,a)},At,ta,ta,lu,Hi,function(r){return r|=0,0|Wo(ut(40),v[r>>2])},Lf,gf,At,ta,ta,lu,Hi,function(r){return r|=0,0|tf(ut(40),v[r>>2])},qi,ni,At,ta,ta,lu,Hi,function(r){return r|=0,0|Go(ut(40),v[r>>2])},function(){return 21687},lu,$o,function(){return 0|bn(ut(172))},Ro,Ci,Oo,Ci,Eo,function(r){return v[28+(r|=0)>>2]},function(r){return v[32+(r|=0)>>2]},Ci,ou,Yt,qt,zt,Mu,function(r,n){r|=0,n=w(n),p[r+64>>2]=n,p[r+60>>2]=n},pi,Ai,uu,Zt,function(r){r|=0;var n=w(0),e=w(0);n=p[r+52>>2];r:{if(l[r+36|0]){if((e=w(p[r+56>>2]-n))==w(0))break r;return w(w(n+Er(p[r+72>>2],e)))}n=(n=w(p[r+72>>2]+n))<(e=p[r+56>>2])?n:e}return w(n)},pi,function(r){return p[72+(r|=0)>>2]>=w(p[r+56>>2]-p[r+52>>2])|0},Ci,function(r){v[152+(r|=0)>>2]=0},ji,function(){return 21689},lu,Hi,function(r){return r|=0,0|Li(ut(24),v[r>>2])},qt,zt,zo,Ci,jf,ge,function(r,n,e,f){var i;r|=0,n|=0,e|=0,f=w(f),V=i=V-16|0,p[i+12>>2]=f,_t(i,n,e),Pn(r+12|0,i,i+12|0),V=i+16|0},function(r,n,e,f,i){r|=0,n|=0,e|=0,f|=0,i=w(i);var t=0;t=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(t=v[t+v[n>>2]>>2]),Ta[0|t](n,e,f,i)},Ln,function(r,n,e,f){n|=0,e|=0,f|=0;var i=0;return i=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),w(w(Ta[0|i](n,e,f)))},function(){return 21692},lu,Hi,function(r){return r|=0,0|kn(ut(104),v[r>>2])},Ro,Ci,Do,function(r){return w(p[100+(r|=0)>>2])},function(r,n){r|=0,n=w(n),p[r+100>>2]=n},pi,Ai,Ir,Ai,ir,li,Ne,ji,Yn,$i,function(r,n,e,f){return e|=0,f|=0,0|Vn(r|=0,n|=0,qe(v[v[r+16>>2]+4>>2],e),f)},function(r,n,e,f,i){n|=0,e|=0,f|=0,i|=0;var t,u=0;return V=t=V-16|0,n=((u=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&u?v[v[n>>2]+r>>2]:r,u=n,n=$e(t+4|0,f+4|0,v[f>>2],0),r=0|Ta[0|r](u,e,n,i),gi(n),V=t+16|0,0|r},function(r,n,e,f){return 0|Vn(r|=0,n|=0,e|=0,f|=0)},function(r,n,e,f,i){return r|=0,n|=0,e|=0,f|=0,i=w(i),0|an(r,n,qe(v[v[r+16>>2]+4>>2],e),f,i)},function(r,n,e,f,i,t){r|=0,n|=0,e|=0,f|=0,i|=0,t=w(t);var u,o=0;return V=u=V-16|0,n=((o=v[r+4>>2])>>1)+n|0,r=v[r>>2],r=1&o?v[v[n>>2]+r>>2]:r,o=n,n=$e(u+4|0,f+4|0,v[f>>2],0),r=0|Ta[0|r](o,e,n,i,t),gi(n),V=u+16|0,0|r},an,function(r,n,e,f,i,t){r|=0,n|=0,e|=0,f|=0,i|=0,t=w(t);var u=0;return u=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(u=v[u+v[n>>2]>>2]),0|Ta[0|u](n,e,f,i,t)},function(r,n,e){return r|=0,n|=0,e=w(e),Ye(),r=Vn(r,n,20900,0),p[r+84>>2]=e,p[r+100>>2]=e,0|r},function(r,n,e,f){r|=0,n|=0,e|=0,f=w(f);var i=0;return i=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),0|Ta[0|i](n,e,f)},function(r,n,e,f){return r|=0,n|=0,e=w(e),f=w(f),Ye(),r=an(r,n,20900,0,w(f-(f<=w(0)?e:w(0)))),p[r+84>>2]=e,p[r+100>>2]=e,0|r},function(r,n,e,f,i){r|=0,n|=0,e|=0,f=w(f),i=w(i);var t=0;return t=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(t=v[t+v[n>>2]>>2]),0|Ta[0|t](n,e,f,i)},function(r,n){r|=0,n=w(n);var e,f,t=0,u=0;if(t=v[r+72>>2],f=l[t+28|0],i[t+28|0]=1,e=v[r+44>>2]){for(t=0;v[v[r+52>>2]+(t<<2)>>2]&&(Ye(),u=Vn(r,t,20900,0),p[u+84>>2]=n,p[u+100>>2]=n),(0|e)!=(0|(t=t+1|0)););t=v[r+72>>2]}i[t+28|0]=f,mr(t)},function(r,n){return n|=0,0|(d[44+(r|=0)>>2]>n>>>0?v[v[r+52>>2]+(n<<2)>>2]:0)},li,function(r){i[v[72+(r|=0)>>2]+28|0]=1},function(r){i[v[72+(r|=0)>>2]+28|0]=0},function(){return 21651},lu,Hi,function(r){return r|=0,0|kr(ut(180),v[r>>2])},zo,Ci,Qo,Xo,Do,function(r){return 56+(r|=0)|0},function(r){return 72+(r|=0)|0},function(r){return 88+(r|=0)|0},Bo,function(r){return v[136+(r|=0)>>2]},Ci,function(r){return 0|ca(r|=0)},Qt,qt,zt,nr,ji,pn,ia,Gr,ae,function(r){return v[12+(r|=0)>>2]?v[v[r+20>>2]>>2]:0},Ci,wu,Be,function(r,n){return 0|Uf(8+(r|=0)|0,n|=0)},Be,ku,Be,function(r,n){return 0|Uf(24+(r|=0)|0,n|=0)},bu,cf,jr,$i,function(r,n,e){return n|=0,e|=0,0|Ef(r|=0,au(v[r+4>>2],n),e)},function(r,n,e,f){n|=0,e|=0,f|=0;var i,t=0;return V=i=V-32|0,n=((t=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&t?v[v[n>>2]+r>>2]:r,t=n,n=$e(i+20|0,e+4|0,v[e>>2],0),e=$e(i+8|0,f+4|0,v[f>>2],0),r=0|Ta[0|r](t,n,e),gi(e),gi(n),V=i+32|0,0|r},Ef,ze,function(r,n,e){n|=0,e|=0;var f,i=0,t=0;if(f=v[28+(r|=0)>>2])for(;;){if(t=v[v[r+36>>2]+(i<<2)>>2],ei(Qo(v[t+4>>2]),n))return void hi(t,r=v[e+4>>2]?Ef(r,i,e):0);if((0|f)==(0|(i=i+1|0)))break}},Oe,function(r,n){n|=0;var e=0,f=0,i=0;r:if(f=v[60+(r|=0)>>2]){for(;;){if(i=v[v[r+68>>2]+(e<<2)>>2],!ei(na(v[i+4>>2]),n)){if((0|f)!=(0|(e=e+1|0)))continue;break r}break}return 0|i}return 0},Be,function(r,n){n|=0;var e=0,f=0,i=0;r:if(f=v[76+(r|=0)>>2]){for(;;){if(i=v[v[r+84>>2]+(e<<2)>>2],!ei(na(v[i+4>>2]),n)){if((0|f)!=(0|(e=e+1|0)))continue;break r}break}return 0|i}return 0},Be,function(r,n){n|=0;var e=0,f=0,i=0;r:if(f=v[92+(r|=0)>>2]){for(;;){if(i=v[v[r+100>>2]+(e<<2)>>2],!ei(na(v[i+4>>2]),n)){if((0|f)!=(0|(e=e+1|0)))continue;break r}break}return 0|i}return 0},Be,Jt,Ai,At,lu,$i,oi,ji,At,ta,ta,lu,ai,function(r,n){r|=0,n|=0;var e,f=w(0);return e=ut(12),f=p[r>>2],p[e+8>>2]=p[n>>2],p[e+4>>2]=f,v[e>>2]=11184,0|e},qt,zt,$i,oi,ji,At,ta,ta,lu,Ei,function(r,n){r|=0,n|=0;var e,f=w(0);return e=ut(32),f=p[r>>2],v[e+28>>2]=n,v[e+24>>2]=0,v[e+16>>2]=0,v[e+20>>2]=0,p[e+12>>2]=f,v[e+4>>2]=0,v[e+8>>2]=0,v[e>>2]=11216,0|e},$i,oi,ji,qt,zt,function(r){return w(p[16+(r|=0)>>2])},function(r,n){r|=0,n=w(n),p[r+16>>2]=n*w(.01745329238474369)},pi,Ai,function(){return 21697},function(r){(r|=0)&&ar(Uo(r))},uu,Zt,Vo,Ci,ta,Ci,function(){return 21538},ra,uu,Zt,function(){return 21702},function(r){(r|=0)&&ar(ke(r))},$o,function(){var r,n,e;return r=Wt(92),v[r+4>>2]=1065353216,s[r>>1]=256,zi(r+8|0,0,40),v[r+84>>2]=0,v[r+88>>2]=0,v[r+80>>2]=17052,v[r+76>>2]=0,v[r+68>>2]=0,v[r+72>>2]=0,v[r+64>>2]=11260,v[r+56>>2]=1065353216,v[r+60>>2]=1065353216,v[r+48>>2]=1065353216,v[r+52>>2]=1065353216,n=Wt(52),V=e=V-16|0,v[n+12>>2]=0,v[n+4>>2]=0,v[n+8>>2]=0,v[n+20>>2]=0,v[n+24>>2]=0,v[n+16>>2]=9212,v[n>>2]=11596,v[n+28>>2]=0,v[n+32>>2]=0,v[n+36>>2]=0,v[n+40>>2]=0,v[n+44>>2]=0,v[n+48>>2]=0,v[e+12>>2]=0,gn(n,6,e+12|0),V=e+16|0,v[r+32>>2]=n,0|r},ou,Yt,qt,zt,function(r,n){n|=0;var e,f=0,i=0,t=0,u=0;return(f=v[24+(r|=0)>>2])&&Ta[v[v[f>>2]+4>>2]](f),(f=v[r+20>>2])&&Ta[v[v[f>>2]+4>>2]](f),(f=v[r+16>>2])&&Ta[v[v[f>>2]+4>>2]](f),(f=v[r+8>>2])&&Ta[v[v[f>>2]+4>>2]](f),v[r+12>>2]=n,t=r,u=kr(ut(180),v[r+12>>2]),v[t+8>>2]=u,t=r,u=Li(ut(24),v[r+12>>2]),v[t+16>>2]=u,t=r,u=kn(ut(104),v[r+16>>2]),v[t+20>>2]=u,t=r,u=Pr(ut(232)),v[t+24>>2]=u,ia(v[r+8>>2]),pn(v[r+8>>2]),n=f=v[r+20>>2],(e=v[f+12>>2])&&(!(i=v[f+8>>2])|(0|r)==(0|i)||(Ta[0|e](i),n=v[r+20>>2])),v[f+12>>2]=0,v[f+8>>2]=r,v[n+96>>2]=0,v[n+92>>2]=1705,v[r+8>>2]},li,function(r,n,e,f){r|=0,n=w(n),e|=0,f|=0;var i=0,t=0;if(i=v[r+8>>2]){if(i=qe(v[i+4>>2],e),e=v[r+20>>2],!i)return Ne(e),ia(v[r+8>>2]),0;t=Vn(e,t=n<w(4294967296)&n>=w(0)?~~n>>>0:0,i,f),ir(v[r+20>>2],v[r+8>>2]),pn(v[r+8>>2])}return 0|t},function(r,n,e,f,i){r|=0,n|=0,e=w(e),f|=0,i|=0;var t,u=0;return V=t=V-16|0,n=((u=v[r+4>>2])>>1)+n|0,r=v[r>>2],r=1&u?v[v[n>>2]+r>>2]:r,u=n,n=$e(t+4|0,f+4|0,v[f>>2],0),r=0|Ta[0|r](u,e,n,i),gi(n),V=t+16|0,0|r},function(r,n){var e;n|=0,(e=v[8+(r|=0)>>2])&&(bu(e,n),ae(v[r+8>>2]))},cf,function(r,n){var e;r|=0,n=w(n),(e=v[r+8>>2])&&(Jt(e,n=w(p[r+4>>2]*n)),Ir(v[r+20>>2],n),ir(v[r+20>>2],v[r+8>>2]))},Ai,function(r){r|=0;var n,e,f,i,t,u=0,o=0,a=0,c=0,b=0,k=0,d=0,y=0,g=w(0),F=w(0),A=0,T=0,$=0,I=0,C=0,P=0,E=w(0),O=w(0),R=w(0),S=0,W=0,G=0,U=w(0),j=w(0),H=w(0);if(l[r+46|0]&&(v[r+68>>2]=0),pn(v[r+8>>2]),v[5428]=v[5427],v[5430]=v[5429],u=v[r+32>>2],V=o=V-16|0,v[o+4>>2]=0,gn(u,0,a=o+4|0),tn(u+16|0,0,a=ht(a,8590,0)),gi(a),v[u+32>>2]=0,v[u+36>>2]=0,V=o+16|0,v[v[r+32>>2]+48>>2]=l[r+44|0]?28:24,V=n=V+-64|0,i=v[r+8>>2],t=v[i+44>>2],o=l[r+44|0],v[n+48>>2]=v[5435],u=v[5434],v[n+40>>2]=v[5433],v[n+44>>2]=u,u=v[5432],v[n+32>>2]=v[5431],v[n+36>>2]=u,f=Ue(n+52|0,21744),(u=v[r+28>>2])&&Ta[v[v[u>>2]+12>>2]](u,v[r+8>>2]),e=ca(v[r+8>>2]),t)for(P=(C=o?28:24)>>>2|0,W=r+80|0,G=r- -64|0;;){u=v[v[i+52>>2]+(S<<2)>>2],v[n+28>>2]=u,k=v[u+8>>2];r:if(0|Ta[v[v[k>>2]+16>>2]](k))if(v[v[n+28>>2]+60>>2]){F=p[r+60>>2],E=p[r+56>>2],g=p[r+52>>2],O=p[r+48>>2];n:{e:{f:{i:{t:{if(a=v[v[n+28>>2]+60>>2],Fu(u=0|Ta[v[v[a>>2]+8>>2]](a),21296)){if(u=v[a+24>>2],o=v[u+4>>2],$=(b=v[o+12>>2])<<1,d=v[o+8>>2],c=v[5428],y=v[5430],!l[r+44|0])break t;if(o=0,(0|d)<=0)break e;for(;A=v[v[u+4>>2]>>2]+m(o,24)|0,I=v[A+16>>2],T=c+m(o,28)|0,v[T+12>>2]=v[A+12>>2],v[T+16>>2]=I,(0|d)!=(0|(o=o+1|0)););break e}if(Fu(u,21200)){if(u=v[a+68>>2],o=v[u+4>>2],b=(k=v[o+12>>2])<<1,d=v[o+8>>2],c=v[5428],$=v[5430],!l[r+44|0])break i;if(o=0,(0|d)<=0)break f;for(;T=v[v[u+4>>2]>>2]+m(o,24)|0,A=v[T+16>>2],y=c+m(o,28)|0,v[y+12>>2]=v[T+12>>2],v[y+16>>2]=A,(0|d)!=(0|(o=o+1|0)););break f}if(d=Fu(u,21024),u=v[r+24>>2],o=v[n+28>>2],d){rr(u,o,a);break r}Of(u,o);break r}Ri(c,v[v[u+4>>2]>>2],m(d,C));break e}Ri(c,v[v[u+4>>2]>>2],m(d,C))}o=Ri($,v[v[u+4>>2]+4>>2],b),vr(a,v[n+28>>2],0,v[a+52>>2],c,0,P),v[n+44>>2]=k,v[n+40>>2]=d,v[n+36>>2]=o,v[n+32>>2]=c,$=1,o=aa(a);break n}o=Ri(y,v[v[u+4>>2]+4>>2],$),$=0,Xr(a,k,c,0,P),v[n+44>>2]=b,v[n+40>>2]=d,v[n+36>>2]=o,v[n+32>>2]=c,o=ka(a)}U=p[o+12>>2],j=p[o+4>>2],R=p[o+8>>2],H=p[o+16>>2],te(f,u+8|0),d=v[n+28>>2],u=(F=w(w(F*H)*w(w(p[e+16>>2]*w(255))*p[d+32>>2])))<w(4294967296)&F>=w(0)?~~F>>>0:0,F=l[r+45|0]?w(u>>>0):w(255),R=w(g*R),o=(g=w(F*w(R*w(p[e+8>>2]*p[d+24>>2]))))<w(4294967296)&g>=w(0)?~~g>>>0:0,o<<=8,O=w(O*j),c=o+((a=(g=w(w(O*w(p[e+4>>2]*p[d+20>>2]))*F))<w(4294967296)&g>=w(0)?~~g>>>0:0)+(u<<24)|0)|0,E=w(E*U),k=(o=(g=w(F*w(E*w(p[e+12>>2]*p[d+28>>2]))))<w(4294967296)&g>=w(0)?~~g>>>0:0)<<16,u=0,o=0,a=0,l[d+56|0]&&(a=v[n+28>>2],u=(g=w(F*w(E*w(p[e+12>>2]*p[a+48>>2]))))<w(4294967296)&g>=w(0)?~~g>>>0:0,o=(g=w(F*w(R*w(p[e+8>>2]*p[a+44>>2]))))<w(4294967296)&g>=w(0)?~~g>>>0:0,a=(F=w(F*w(O*w(p[e+4>>2]*p[a+40>>2]))))<w(4294967296)&F>=w(0)?~~F>>>0:0),d=c+k|0,k=l[r+45|0],y=l[r+44|0],b=Cu(v[r+24>>2]),c=v[n+32>>2];n:if(y){if(k=(a+((o<<8)+(u<<16)|0)|0)+(k?-16777216:0)|0,b){if(er(v[r+24>>2],c,v[n+36>>2],v[n+44>>2],c+12|0,P),u=v[r+24>>2],!(a=v[u+180>>2])){Of(u,v[n+28>>2]);break r}if(b=v[5428],c=v[5430],o=(y=v[u+164>>2])>>1,v[n+40>>2]=o,v[n+36>>2]=c,v[n+32>>2]=b,v[n+44>>2]=a,Ri(c,v[u+188>>2],a<<1),c=v[u+204>>2],b=v[u+172>>2],T=v[n+32>>2],v[r+28>>2]){if((0|y)<2)break n;for(A=(0|o)<=1?1:o,u=0,o=0;a=T+m(o,28)|0,y=u<<2,p[a>>2]=p[y+b>>2],I=4|y,p[a+4>>2]=p[I+b>>2],p[a+12>>2]=p[c+y>>2],p[a+16>>2]=p[c+I>>2],y=v[r+28>>2],Ta[v[v[y>>2]+16>>2]](y,a,a+4|0),v[a+24>>2]=k,v[a+20>>2]=d,u=u+2|0,(0|A)!=(0|(o=o+1|0)););break n}if((0|y)<2)break n;for(A=(0|o)<=1?1:o,u=0,o=0;a=T+m(o,28)|0,y=u<<2,p[a>>2]=p[y+b>>2],I=4|y,p[a+4>>2]=p[I+b>>2],p[a+12>>2]=p[c+y>>2],F=p[c+I>>2],v[a+24>>2]=k,v[a+20>>2]=d,p[a+16>>2]=F,u=u+2|0,(0|A)!=(0|(o=o+1|0)););}else if(a=v[n+40>>2],v[r+28>>2]){if(o=0,!a)break n;for(;b=v[r+28>>2],u=c+m(o,28)|0,Ta[v[v[b>>2]+16>>2]](b,u,u+4|0),v[u+24>>2]=k,v[u+20>>2]=d,(0|a)!=(0|(o=o+1|0)););}else if(u=0,a)for(;o=c+m(u,28)|0,v[o+24>>2]=k,v[o+20>>2]=d,(0|a)!=(0|(u=u+1|0)););}else{if(b){if(er(v[r+24>>2],c,v[n+36>>2],v[n+44>>2],c+12|0,P),u=v[r+24>>2],!(a=v[u+180>>2])){Of(u,v[n+28>>2]);break r}if(k=v[5428],c=v[5430],o=(b=v[u+164>>2])>>1,v[n+40>>2]=o,v[n+36>>2]=c,v[n+32>>2]=k,v[n+44>>2]=a,Ri(c,v[u+188>>2],a<<1),c=v[u+204>>2],k=v[u+172>>2],y=v[n+32>>2],v[r+28>>2]){if((0|b)<2)break n;for(T=(0|o)<=1?1:o,o=0,u=0;a=y+m(o,24)|0,b=u<<2,p[a>>2]=p[b+k>>2],A=4|b,p[a+4>>2]=p[A+k>>2],p[a+12>>2]=p[c+b>>2],p[a+16>>2]=p[c+A>>2],b=v[r+28>>2],Ta[v[v[b>>2]+16>>2]](b,a,a+4|0),v[a+20>>2]=d,u=u+2|0,(0|T)!=(0|(o=o+1|0)););break n}if((0|b)<2)break n;for(T=(0|o)<=1?1:o,o=0,u=0;a=y+m(o,24)|0,b=u<<2,p[a>>2]=p[b+k>>2],A=4|b,p[a+4>>2]=p[A+k>>2],p[a+12>>2]=p[c+b>>2],F=p[c+A>>2],v[a+20>>2]=d,p[a+16>>2]=F,u=u+2|0,(0|T)!=(0|(o=o+1|0)););break n}if(o=v[n+40>>2],v[r+28>>2]){if(u=0,!o)break n;for(;k=v[r+28>>2],a=c+m(u,24)|0,Ta[v[v[k>>2]+16>>2]](k,a,a+4|0),v[a+20>>2]=d,(0|o)!=(0|(u=u+1|0)););break n}if(u=0,!o)break n;for(;v[20+(c+m(u,24)|0)>>2]=d,(0|o)!=(0|(u=u+1|0)););}v[5428]=v[5428]+m(v[n+40>>2],C),v[5430]=v[5430]+(v[n+44>>2]<<1),l[r+46|0]&&(u=v[r+68>>2],v[n+24>>2]=0,v[n+16>>2]=0,v[n+20>>2]=0,v[n+8>>2]=0,v[n+12>>2]=0,cn(G,u+1|0,n+8|0),u=v[r+76>>2]+m(u,20)|0,v[u>>2]=$,o=v[r+32>>2],v[u+4>>2]=v[o+32>>2],v[u+8>>2]=v[n+40>>2],v[u+12>>2]=v[o+36>>2],v[u+16>>2]=v[n+44>>2]),v[n+48>>2]=v[v[v[n+28>>2]+4>>2]+80>>2];n:if(l[r+47|0]&&(o=v[r+84>>2])){for(u=v[n+28>>2];;){if((0|u)!=v[o+4>>2]){if(o=v[o+20>>2])continue;break n}break}o=n+8|0;e:{f:{if(u=v[W+4>>2])for(a=v[n+28>>2];;){if((0|a)==v[u+4>>2])break f;if(!(u=v[u+20>>2]))break}ht(o,0,0);break e}Ue(o,u+8|0)}te(f,o),gi(o)}if(V=d=V-16|0,o=v[r+32>>2],u=n+32|0,!(a=v[o+4>>2])|v[(v[o+12>>2]+(a<<2)|0)-4>>2]!=v[u+16>>2]||!ei((v[o+28>>2]+m((a>>>0)/5|0,12)|0)-12|0,u+20|0)?(v[d+12>>2]=0,gn(o,a+5|0,d+12|0),a=v[o+12>>2]+(a<<2)|0,v[a>>2]=v[u>>2],v[a+4>>2]=v[u+4>>2],v[a+8>>2]=v[u+8>>2],v[a+12>>2]=v[u+12>>2],v[a+16>>2]=v[u+16>>2],An(o+16|0,u+20|0)):(a=v[o+12>>2]+(a<<2)|0,v[(c=a-12|0)>>2]=v[c>>2]+v[u+8>>2],v[(a=a-8|0)>>2]=v[a>>2]+v[u+12>>2]),c=v[u+12>>2])for(k=v[u+4>>2],b=v[o+32>>2],a=0;s[($=k+(a<<1)|0)>>1]=b+h[$>>1],(0|c)!=(0|(a=a+1|0)););if(k=v[u+8>>2])for(b=v[o+48>>2]>>>2|0,u=v[u>>2],a=0;v[8+(u+(m(a,b)<<2)|0)>>2]=0,(0|k)!=(0|(a=a+1|0)););v[o+32>>2]=k+v[o+32>>2],v[o+36>>2]=c+v[o+36>>2],V=d+16|0,Of(v[r+24>>2],v[n+28>>2])}else Of(v[r+24>>2],v[n+28>>2]);if((0|(S=S+1|0))==(0|t))break}return mi(v[r+24>>2]),(u=v[r+28>>2])&&Ta[v[v[u>>2]+20>>2]](u),gi(f),V=n- -64|0,u=v[5427],o=v[r+32>>2],v[o+44>>2]=v[5429],v[o+40>>2]=u,v[r+32>>2]},Ci,function(r,n){n|=0,i[45+(r|=0)|0]=n},$i,wo,function(r,n,e,f,i){r|=0,n=w(n),e=w(e),f=w(f),i=w(i),p[r+60>>2]=i,p[r+56>>2]=f,p[r+52>>2]=e,p[r+48>>2]=n},function(r,n,e,f,i,t){r|=0,n|=0,e=w(e),f=w(f),i=w(i),t=w(t);var u=0;u=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(u=v[u+v[n>>2]>>2]),Ta[0|u](n,e,f,i,t)},Ru,$i,Ru,$i,function(r){v[28+(r|=0)>>2]=0},ji,Oo,Ci,function(r,n,e,f){r|=0,n|=0,e|=0,f=w(f),jf(v[r+16>>2],n,e,f)},ge,_u,$i,function(r,n,e){r|=0,n|=0,v[8+(e|=0)>>2]||(v[r+40>>2]=n,v[e+12>>2]=0,v[e+8>>2]=r,v[e+168>>2]=0,v[e+164>>2]=1706)},vi,function(r,n){n|=0,i[46+(r|=0)|0]=n},Jo,function(r,n,e,f,t){r|=0,n|=0,e|=0,f|=0,t|=0;var u=w(0),o=w(0),a=0,c=0;if((r=v[r+8>>2])&&(r=ku(r,n))&&(n=v[r+60>>2]))if(t&&hi(r,n=0|Ta[v[v[n>>2]+12>>2]](n)),Fu(0|Ta[v[v[n>>2]+8>>2]](n),21296))for(u=w(e>>>0),p[n+68>>2]=u,o=w(f>>>0),p[n+72>>2]=o,p[n+76>>2]=u,p[n+80>>2]=o,p[n+52>>2]=u,p[n+56>>2]=o,r=0,Qn(n,w(0),w(0),w(1),w(1),0),Jr(n),f=v[n+24>>2],t&&(f=Oi(f),(t=v[n+28>>2])&&(!(e=v[n+24>>2])|(0|e)==(0|f)||Ta[0|t](e)),v[n+28>>2]=0,v[n+24>>2]=f),e=v[n+112>>2],f=v[v[f+4>>2]>>2],n=0;t=f+m(r,24)|0,a=n<<2,p[t+12>>2]=p[a+e>>2],p[t+16>>2]=p[e+(4|a)>>2],n=n+2|0,4!=(0|(r=r+1|0)););else if(Fu(0|Ta[v[v[n>>2]+8>>2]](n),21200)&&(u=w(e>>>0),p[n+84>>2]=u,o=w(f>>>0),p[n+88>>2]=o,p[n+92>>2]=u,p[n+96>>2]=o,p[n+196>>2]=u,p[n+200>>2]=o,p[n+180>>2]=0,p[n+184>>2]=0,p[n+188>>2]=1,p[n+192>>2]=1,i[n+228|0]=1,v[n+232>>2]=0,lr(n),r=v[n+68>>2],t&&(r=Oi(r),(f=v[n+72>>2])&&(!(e=v[n+68>>2])|(0|r)==(0|e)||Ta[0|f](e)),v[n+72>>2]=0,v[n+68>>2]=r),e=v[n+52>>2]))for(f=v[v[r+4>>2]>>2],t=e-1>>>1|0,e=v[n+116>>2],n=0,r=0;a=f+m(r,24)|0,c=n<<2,p[a+12>>2]=p[e+c>>2],p[a+16>>2]=p[e+(4|c)>>2],n=n+2|0,a=(0|r)!=(0|t),r=r+1|0,a;);},function(r,n,e,f,i,t){n|=0,e|=0,f|=0,i|=0,t|=0;var u,o=0;V=u=V-16|0,n=((o=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&o?v[v[n>>2]+r>>2]:r,o=n,n=$e(u+4|0,e+4|0,v[e>>2],0),Ta[0|r](o,n,f,i,t),gi(n),V=u+16|0},function(r){ke(r|=0),ar(r)},function(r,n,e){n|=0,e|=0;var f,t=0;if(V=f=V-16|0,(t=v[8+(r|=0)>>2])&&(n=ku(t,n),v[f+12>>2]=n,n)){i[r+47|0]=1;r:{n:{if(n=v[r+84>>2])for(t=v[f+12>>2];;){if((0|t)==v[n+4>>2])break n;if(!(n=v[n+20>>2]))break}n=ut(28),v[n>>2]=17068,t=Tt(n+8|0),v[n+20>>2]=0,v[n+24>>2]=0,v[n+4>>2]=v[f+12>>2],te(t,e),(e=v[r+84>>2])&&(v[e+24>>2]=n,v[n+20>>2]=e),v[r+84>>2]=n,v[r+88>>2]=v[r+88>>2]+1;break r}v[n+4>>2]=t,te(n+8|0,e)}}V=f+16|0},Oe,function(r,n,e,f){var i;r|=0,n|=0,e|=0,f=w(f),V=i=V-16|0,r=v[r>>2],p[i+12>>2]=f,Ta[0|r](n,e,i+12|0),V=i+16|0},gt,Fi,Fi,gt,Fi,function(r,n,e,f){var i;n|=0,e|=0,f|=0,V=i=V-16|0,r=v[(r|=0)>>2],s[i+14>>1]=f,Ta[0|r](n,e,i+14|0),V=i+16|0},function(r,n,e,f){var i,t;n|=0,e|=0,f|=0,V=i=V-16|0,r=v[(r|=0)>>2],t=n,n=$e(i+4|0,f+4|0,v[f>>2],0),Ta[0|r](t,e,n),gi(n),V=i+16|0},Fi,Qt,Qt,function(r,n,e,f){return r|=0,n|=0,e=w(e),f=w(f),0|Ta[v[r>>2]](n,e,f)},function(r,n,e,f,i,t){return r|=0,n|=0,e=w(e),f=w(f),i=w(i),t=w(t),0|Ta[v[r>>2]](n,e,f,i,t)},Qt,Qt,Qt,Qt,Qt,function(r,n,e,f,i){r|=0,n|=0,e|=0,f=w(f),i=w(i),Ta[v[r>>2]](n,e,f,i)},Qt,Sf,Sf,Qt,Qt,Qt,Qt,Qt,function(r,n,e){n|=0,e|=0,Ta[v[(r|=0)>>2]](n,e)},Qt,Qt,Qt,Qt,function(r,n,e){var f;return n|=0,e|=0,V=f=V-16|0,Ta[v[(r|=0)>>2]](f,n,e),r=zn(ut(16),f),be(f),V=f+16|0,0|r},function(r,n){var e;return n|=0,V=e=V-16|0,Ta[v[(r|=0)>>2]](e,n),r=Mo(ut(16),e),ju(e),V=e+16|0,0|r},function(r,n,e){var f;return n|=0,e|=0,V=f=V-16|0,Ta[v[(r|=0)>>2]](f,n,e),r=Mo(ut(16),f),ju(f),V=f+16|0,0|r},Sf,Qt,Qt,Qt,Qt,Qt,Qt,Qt,Qt,Qt,Qt,Qt,Qt,Qt,Qt,Qt,function(r,n,e,f,i){var t;r|=0,n|=0,e|=0,f=w(f),i|=0,V=t=V-16|0,r=v[r>>2],v[t+12>>2]=i,Ta[0|r](n,e,f,t+12|0),E(v[t+12>>2]),V=t+16|0},Qt,Qt,Qt,Qt,Qt,function(r,n,e,f,i){return n|=0,e|=0,f|=0,i|=0,0|Ta[v[(r|=0)>>2]](n,e,f,i)},Qt,Qt,Qt,Qt,Qt,Qt,function(r,n,e,f){r|=0,n|=0,e=w(e),f=w(f),Ta[v[r>>2]](n,e,f)},Qt,function(){return 21705},ra,function(r){Ta[0|(r|=0)]()},function(){var r=0,n=0;r=Wt(4),v[r>>2]=9584,v[r>>2]=17148,v[5280]=r,v[5427]||(n=X(3669960),v[5427]=n,r=X(1048560),v[5428]=n,v[5429]=r,v[5430]=r)},function(){var r=0;(r=v[5280])&&Ta[v[v[r>>2]+4>>2]](r),(r=v[5445])&&(ar(r),v[5445]=0),ar(v[5427]),v[5427]=0,ar(v[5429]),v[5429]=0},function(r,n){return n|=0,0|Ta[0|(r|=0)](n)},function(r){return r=Wt(r|=0),v[5445]=r,0|r},function(){var r;(r=v[5445])&&(ar(r),v[5445]=0)},wi,function(r){r|=0;var n=0,e=0;r:if(n=v[5440]){for(;;){if(!ei(n+4|0,r)){if(n=v[n+20>>2])continue;break r}break}if(n=v[5440]){for(;;){if(!ei(n+4|0,r)){if(n=v[n+20>>2])continue;break r}break}e=v[n+16>>2]}}return 0|e},function(r,n,e,f,i){var t;return r|=0,e|=0,f|=0,i|=0,V=t=V-32|0,n=$e(t+20|0,4+(n|=0)|0,v[n>>2],0),e=$e(t+8|0,e+4|0,v[e>>2],0),v[t>>2]=i,v[t+4>>2]=f,r=0|Ta[0|r](n,e,t+4|0,t),E(v[t>>2]),E(v[t+4>>2]),gi(e),gi(n),V=t+32|0,0|r},function(r,n,e,f){r|=0,n|=0,e|=0,f|=0;var t,u,o,a,c,b,k=0,h=0,y=0,g=0,F=0,A=0,T=0,$=0,I=w(0),C=0,P=w(0),O=0,R=0,S=0,L=0,M=0,_=0,z=0,x=0,J=0,K=0,B=0,N=0,q=w(0),D=0,Z=w(0),Y=0;if(V=o=V+-64|0,y=v[e>>2],k=0|j(4696),y=0|U(0|y,0|k),E(0|k),b=+G(0|y,21553,0|(k=o+48|0)),W(v[o+48>>2]),E(0|y),v[o+60>>2]=0,v[o+52>>2]=0,v[o+56>>2]=0,v[o+48>>2]=9212,v[o+44>>2]=0,v[o+36>>2]=0,v[o+40>>2]=0,v[o+32>>2]=9212,tn(h=k,y=b<4294967296&b>=0?~~b>>>0:0,k=ht(o+20|0,8590,0)),gi(k),tn(o+32|0,y,k=ht(o+20|0,8590,0)),gi(k),v[o+16>>2]=0,y)for(;qf(T=o+12|0,e,k=o+16|0),wf(C=o+20|0,T),te(v[o+60>>2]+m(v[o+16>>2],12)|0,C),gi(C),E(v[o+12>>2]),qf(T,f,k),wf(C,T),te(v[o+44>>2]+m(v[o+16>>2],12)|0,C),gi(C),E(v[o+12>>2]),k=v[o+16>>2]+1|0,v[o+16>>2]=k,k>>>0<y>>>0;);V=c=V-48|0,n=sf(ut(40),v[n+8>>2],v[n+4>>2]),n=Ot(ut(12),n),i[28+(u=c+4|0)|0]=0,v[u+4>>2]=n,v[u+20>>2]=0,v[u+24>>2]=1065353216,v[u+12>>2]=0,v[u+16>>2]=0,v[u+8>>2]=10344,v[u>>2]=10664,Tt(n=u+32|0),v[u+24>>2]=1065353216,r=v[r+8>>2],V=t=V-288|0,ve(n,8590),v[u+12>>2]=0,a=Le(Wt(32),r),k=Mr(ut(232)),(y=En(a,3838))&&(ve(k+176|0,Ht(y,4828,0)),ve(k+164|0,Ht(y,5369,0)),f=v[k+172>>2],e=v[k+168>>2],(51!=l[0|f]|(0|e)<3|46!=l[f+1|0]||56!=l[f+2|0])&&(n=zi(t+49|0,0,238),i[t+48|0]=l[10688],r=v[2671],v[t+40>>2]=v[2670],v[t+44>>2]=r,r=v[2669],v[t+32>>2]=v[2668],v[t+36>>2]=r,Ri(Ri(n,f,e)+e|0,10704,38),Pi(u,0,n=ht(t+20|0,t+32|0,0),r=ht(t+8|0,8590,0)),gi(r),gi(n)),D=k,Z=Lt(y,1391,w(0)),p[D+148>>2]=Z,D=k,Z=Lt(y,1217,w(0)),p[D+152>>2]=Z,D=k,Z=Lt(y,4734,w(0)),p[D+156>>2]=Z,D=k,Z=Lt(y,2183,w(0)),p[D+160>>2]=Z,D=k,Z=Lt(y,2613,w(30)),p[D+204>>2]=Z,ve(k+220|0,Ht(y,3816,0)),ve(k+208|0,Ht(y,2886,0))),n=En(a,2774),r=v[n+12>>2],v[t+32>>2]=0,$n(k+16|0,r,t+32|0);r:{n:{if(r=v[n+4>>2])for(f=0;;){if(g=0,(e=Ht(r,1834,0))&&(g=du(k,n=ht(t+32|0,e,0)),gi(n),!g))break n;n=ne(n=ut(64),f,e=ht(t+32|0,Ht(r,5773,0),0),g),gi(e),D=n,Z=w(Lt(r,4696,w(0))*p[u+24>>2]),p[D+24>>2]=Z,D=n,Z=w(Lt(r,1391,w(0))*p[u+24>>2]),p[D+28>>2]=Z,D=n,Z=w(Lt(r,1217,w(0))*p[u+24>>2]),p[D+32>>2]=Z,D=n,Z=Lt(r,4056,w(0)),p[D+36>>2]=Z,D=n,Z=Lt(r,6726,w(1)),p[D+40>>2]=Z,D=n,Z=Lt(r,6633,w(1)),p[D+44>>2]=Z,D=n,Z=Lt(r,6700,w(0)),p[D+48>>2]=Z,D=n,Z=Lt(r,6594,w(0)),p[D+52>>2]=Z,y=Ht(r,4463,4581),v[n+56>>2]=0;e:if(uf(y,4581)){if(e=1,uf(y,4266)&&(e=2,uf(y,4012)&&(e=3,uf(y,5856)))){if(uf(y,4035))break e;e=4}v[n+56>>2]=e}if(D=n,Y=Re(r),i[D+60|0]=Y,v[v[k+28>>2]+(f<<2)>>2]=n,f=f+1|0,!(r=v[r>>2]))break}e:{if((e=En(a,2439))&&(ya(n=k+32|0,v[e+12>>2]),r=v[e+12>>2],v[t+32>>2]=0,$n(n,r,t+32|0),r=v[e+4>>2]))for(f=0;;){if(y=du(k,n=ht(t+32|0,e=Ht(r,5343,0),0)),gi(n),!y)break e;n=Dn(n=ut(84),f,e=ht(t+32|0,Ht(r,5773,0),0),y),gi(e),(y=Ht(r,3520,0))&&(D=e=Xo(n),Z=ye(y,0),p[D+4>>2]=Z,D=e,Z=ye(y,1),p[D+8>>2]=Z,D=e,Z=ye(y,2),p[D+12>>2]=Z,D=e,Z=ye(y,3),p[D+16>>2]=Z),(y=Ht(r,4607,0))&&(D=e=la(n),Z=ye(y,0),p[D+4>>2]=Z,D=e,Z=ye(y,1),p[D+8>>2]=Z,I=ye(y,2),v[e+16>>2]=1065353216,p[e+12>>2]=I,ha(n)),(e=En(r,1841))&&(te(n+68|0,e=ht(t+32|0,v[e+16>>2],0)),gi(e));f:if(e=En(r,6115)){if(y=1,uf(e=v[e+16>>2],4974)&&(y=2,uf(e,1204))){if(uf(e,4456))break f;y=3}v[n+80>>2]=y}if(v[v[k+44>>2]+(f<<2)>>2]=n,f=f+1|0,!(r=v[r>>2]))break}if((e=En(a,4612))&&(ya(n=k+100|0,v[e+12>>2]),r=v[e+12>>2],v[t+32>>2]=0,$n(n,r,t+32|0),e=v[e+4>>2]))for(C=0;;){T=Qe(n=ut(60),r=ht(f=t+32|0,Ht(e,5773,0),0)),gi(r),ba(T,Gt(e,3633,0)),va(T,Re(e)),n=T+24|0,y=En(e,2774),ya(n,v[y+12>>2]),r=v[y+12>>2],v[t+32>>2]=0,$n(n,r,f);f:if(r=v[y+4>>2]){for(g=0;;){if(f=du(k,y=ht(t+32|0,v[r+16>>2],0)),v[(n=g<<2)+v[T+36>>2]>>2]=f,gi(y),v[n+v[T+36>>2]>>2]){if(g=g+1|0,r=v[r>>2])continue;break f}break}Ta[v[v[k>>2]+4>>2]](k),k=0,Pi(u,a,n=ht(t+32|0,8554,0),r=ht(t+20|0,v[r+16>>2],0)),gi(r),gi(n);break r}if(D=T,Y=du(k,r=ht(t+32|0,f=Ht(e,2246,0),0)),v[D+40>>2]=Y,gi(r),!v[T+40>>2]){k&&Ta[v[v[k>>2]+4>>2]](k),k=0,Pi(u,a,n=ht(t+32|0,8459,0),r=ht(t+20|0,f,0)),gi(r),gi(n);break r}if(D=T,Z=Lt(e,1231,w(1)),p[D+52>>2]=Z,D=T,Z=w(Lt(e,2590,w(0))*p[u+24>>2]),p[D+56>>2]=Z,D=T,Y=Gt(e,4961,1)?1:-1,v[D+44>>2]=Y,D=T,Y=!!(0|Gt(e,2581,0)),i[D+48|0]=Y,D=T,Y=!!(0|Gt(e,4833,0)),i[D+49|0]=Y,D=T,Y=!!(0|Gt(e,4494,0)),i[D+50|0]=Y,v[v[k+112>>2]+(C<<2)>>2]=T,C=C+1|0,!(e=v[e>>2]))break}if((e=En(a,4463))&&(ya(n=k+116|0,v[e+12>>2]),r=v[e+12>>2],v[t+32>>2]=0,$n(n,r,t+32|0),e=v[e+4>>2]))for(C=0;;){T=it(n=ut(88),r=ht(f=t+32|0,Ht(e,5773,0),0)),gi(r),ba(T,Gt(e,3633,0)),va(T,Re(e)),n=T+24|0,y=En(e,2774),ya(n,v[y+12>>2]),r=v[y+12>>2],v[t+32>>2]=0,$n(n,r,f);f:if(g=v[y+4>>2]){for(f=0;;){if(n=du(k,y=ht(t+32|0,v[g+16>>2],0)),v[(r=f<<2)+v[T+36>>2]>>2]=n,gi(y),v[r+v[T+36>>2]>>2]){if(f=f+1|0,g=v[g>>2])continue;break f}break}k&&Ta[v[v[k>>2]+4>>2]](k),k=0,Pi(u,a,n=ht(t+32|0,8505,0),r=ht(t+20|0,v[g+16>>2],0)),gi(r),gi(n);break r}if(D=T,Y=du(k,r=ht(t+32|0,f=Ht(e,2246,0),0)),v[D+40>>2]=Y,gi(r),!v[T+40>>2]){k&&Ta[v[v[k>>2]+4>>2]](k),k=0,Pi(u,a,n=ht(t+32|0,8459,0),r=ht(t+20|0,f,0)),gi(r),gi(n);break r}if(D=T,Y=!!(0|Gt(e,4588,0)),i[D+85|0]=Y,D=T,Y=!!(0|Gt(e,4999,0)),i[D+84|0]=Y,D=T,Z=Lt(e,4056,w(0)),p[D+60>>2]=Z,D=T,Z=w(Lt(e,1391,w(0))*p[u+24>>2]),p[D+64>>2]=Z,D=T,Z=w(Lt(e,1217,w(0))*p[u+24>>2]),p[D+68>>2]=Z,D=T,Z=Lt(e,6726,w(0)),p[D+72>>2]=Z,D=T,Z=Lt(e,6633,w(0)),p[D+76>>2]=Z,D=T,Z=Lt(e,6594,w(0)),p[D+80>>2]=Z,D=T,Z=Lt(e,1269,w(1)),p[D+44>>2]=Z,D=T,Z=Lt(e,1279,w(1)),p[D+48>>2]=Z,D=T,Z=Lt(e,1292,w(1)),p[D+52>>2]=Z,D=T,Z=Lt(e,1260,w(1)),p[D+56>>2]=Z,v[v[k+128>>2]+(C<<2)>>2]=T,C=C+1|0,!(e=v[e>>2]))break}if((e=En(a,4749))&&(ya(n=k+132|0,v[e+12>>2]),r=v[e+12>>2],v[t+32>>2]=0,$n(n,r,t+32|0),e=v[e+4>>2]))for(C=0;;){r=tt(r=ut(76),n=ht(y=t+32|0,Ht(e,5773,0),0)),gi(n),ba(r,Gt(e,3633,0)),va(r,Re(e)),f=r+24|0,T=En(e,2774),ya(f,v[T+12>>2]),n=v[T+12>>2],v[t+32>>2]=0,$n(f,n,y);f:if(g=v[T+4>>2]){for(f=0;;){if(y=du(k,T=ht(t+32|0,v[g+16>>2],0)),v[(n=f<<2)+v[r+36>>2]>>2]=y,gi(T),v[n+v[r+36>>2]>>2]){if(f=f+1|0,g=v[g>>2])continue;break f}break}k&&Ta[v[v[k>>2]+4>>2]](k),k=0,Pi(u,a,n=ht(t+32|0,8532,0),r=ht(t+20|0,v[g+16>>2],0)),gi(r),gi(n);break r}if(D=r,Y=su(k,n=ht(t+32|0,f=Ht(e,2246,0),0)),v[D+40>>2]=Y,gi(n),!v[r+40>>2]){k&&Ta[v[v[k>>2]+4>>2]](k),k=0,Pi(u,a,n=ht(t+32|0,8160,0),r=ht(t+20|0,f,0)),gi(r),gi(n);break r}f:{if(uf(n=Ht(e,5922,2101),6273)){if(uf(n,2101))break f;n=1}else n=0;v[r+44>>2]=n}f=0;f:{if(uf(n=Ht(e,6003,4696),4696)&&(f=1,uf(n,6273))){if(uf(n,2101))break f;f=2}v[r+48>>2]=f}f=0;f:{if(uf(n=Ht(e,6034,2093),2093)&&(f=1,uf(n,4438))){if(uf(n,5864))break f;f=2}v[r+52>>2]=f}if(D=r,Z=Lt(e,4056,w(0)),p[D+56>>2]=Z,I=Lt(e,3948,w(0)),p[r+60>>2]=I,v[r+44>>2]||(p[r+60>>2]=I*p[u+24>>2]),I=Lt(e,4921,w(0)),p[r+64>>2]=I,d[r+48>>2]<=1&&(p[r+64>>2]=I*p[u+24>>2]),D=r,Z=Lt(e,1269,w(1)),p[D+68>>2]=Z,D=r,Z=Lt(e,1279,w(1)),p[D+72>>2]=Z,v[v[k+144>>2]+(C<<2)>>2]=r,C=C+1|0,!(e=v[e>>2]))break}if(z=u+8|0,(e=En(a,2685))&&(ya(n=k+48|0,v[e+12>>2]),r=v[e+12>>2],v[t+32>>2]=0,$n(n,r,t+32|0),F=v[e+4>>2]))for(;;){if(g=Ht(F,5773,8590),l[0|g]||(g=v[F+28>>2]),O=Ae(n=ut(68),r=ht(t+32|0,g,0)),gi(r),(r=En(F,2774))&&(r=v[r+4>>2]))for(e=O+36|0;;){if(f=du(k,n=ht(t+32|0,v[r+16>>2],0)),gi(n),v[t+8>>2]=f,!f){k&&Ta[v[v[k>>2]+4>>2]](k),k=0,Pi(u,a,n=ht(t+32|0,8483,0),r=ht(t+20|0,v[r+16>>2],0)),gi(r),gi(n);break r}if(Un(e,t+8|0),!(r=v[r>>2]))break}if((r=En(F,4612))&&(r=v[r+4>>2]))for(e=O+52|0;;){if(f=eu(k,n=ht(t+32|0,v[r+16>>2],0)),gi(n),!f){k&&Ta[v[v[k>>2]+4>>2]](k),k=0,Pi(u,a,n=ht(t+32|0,8300,0),r=ht(t+20|0,v[r+16>>2],0)),gi(r),gi(n);break r}if(v[t+32>>2]=f,Un(e,t+32|0),!(r=v[r>>2]))break}if((r=En(F,4463))&&(r=v[r+4>>2]))for(e=O+52|0;;){if(f=fu(k,n=ht(t+32|0,v[r+16>>2],0)),gi(n),!f){k&&Ta[v[v[k>>2]+4>>2]](k),k=0,Pi(u,a,n=ht(t+32|0,8201,0),r=ht(t+20|0,v[r+16>>2],0)),gi(r),gi(n);break r}if(v[t+32>>2]=f,Un(e,t+32|0),!(r=v[r>>2]))break}if((r=En(F,4749))&&(r=v[r+4>>2]))for(e=O+52|0;;){if(f=iu(k,n=ht(t+32|0,v[r+16>>2],0)),gi(n),!f){k&&Ta[v[v[k>>2]+4>>2]](k),k=0,Pi(u,a,n=ht(t+32|0,8239,0),r=ht(t+20|0,v[r+16>>2],0)),gi(r),gi(n);break r}if(v[t+32>>2]=f,Un(e,t+32|0),!(r=v[r>>2]))break}if(v[v[k+60>>2]+(R<<2)>>2]=O,uf(g,2155)||(v[k+64>>2]=O),r=En(F,2546),A=v[(r||F)+4>>2])for(;;){if(C=su(k,r=ht(t+32|0,v[A+28>>2],0)),gi(r),r=v[A+4>>2])for(;;){n=Ht(r,5773,T=v[r+28>>2]),y=Ht(r,4749,n);f:{i:{t:{u:{o:{a:{c:{if(uf(e=Ht(r,5206,4327),4327)){if(!uf(e,4781))break c;if(!uf(e,4775))break c;if(!uf(e,1219))break a;if(!uf(e,4749))break o;if(!uf(e,4884))break t;if(!uf(e,1662))break u;k&&Ta[v[v[k>>2]+4>>2]](k),k=0,Pi(u,a,n=ht(t+32|0,8099,0),r=ht(t+20|0,e,0)),gi(r),gi(n);break r}if(f=v[u+4>>2],e=ht(t+32|0,n,0),n=ht(t+20|0,y,0),g=0|Ta[v[v[f>>2]+12>>2]](f,O,e,n),gi(n),gi(e),!g)break f;ve(g+116|0,y),D=g,Z=w(Lt(r,1391,w(0))*p[u+24>>2]),p[D+32>>2]=Z,D=g,Z=w(Lt(r,1217,w(0))*p[u+24>>2]),p[D+36>>2]=Z,D=g,Z=Lt(r,6726,w(1)),p[D+44>>2]=Z,D=g,Z=Lt(r,6633,w(1)),p[D+48>>2]=Z,D=g,Z=Lt(r,4056,w(0)),p[D+40>>2]=Z,D=g,Z=w(Lt(r,4734,w(32))*p[u+24>>2]),p[D+52>>2]=Z,D=g,Z=w(Lt(r,2183,w(32))*p[u+24>>2]),p[D+56>>2]=Z,(n=Ht(r,3520,0))&&(I=ye(n,0),D=ka(g),Z=I,p[D+4>>2]=Z,I=ye(n,1),D=ka(g),Z=I,p[D+8>>2]=Z,I=ye(n,2),D=ka(g),Z=I,p[D+12>>2]=Z,I=ye(n,3),D=ka(g),Z=I,p[D+16>>2]=Z),Jr(g),n=v[u+4>>2],Ta[v[v[n>>2]+36>>2]](n,g);break i}if(f=v[u+4>>2],e=ht(t+32|0,n,0),n=ht(t+20|0,y,0),g=0|Ta[v[v[f>>2]+16>>2]](f,O,e,n),gi(n),gi(e),!g)break f;if(ve(g+168|0,y),(n=Ht(r,3520,0))&&(I=ye(n,0),D=aa(g),Z=I,p[D+4>>2]=Z,I=ye(n,1),D=aa(g),Z=I,p[D+8>>2]=Z,I=ye(n,2),D=aa(g),Z=I,p[D+12>>2]=Z,I=ye(n,3),D=aa(g),Z=I,p[D+16>>2]=Z),D=g,Z=w(Lt(r,4734,w(32))*p[u+24>>2]),p[D+196>>2]=Z,D=g,Z=w(Lt(r,2183,w(32))*p[u+24>>2]),p[D+200>>2]=Z,!(e=En(r,1834))){if(e=g+136|0,f=En(r,2831),ma(e,v[f+12>>2]),n=v[f+12>>2],s[t+32>>1]=0,Fn(e,n,t+32|0),f=v[f+4>>2])for(n=v[g+148>>2],e=0;s[n+(e<<1)>>1]=v[f+20>>2],e=e+1|0,f=v[f>>2];);if(e=g+120|0,n=En(r,2415),ya(e,y=v[n+12>>2]),v[t+32>>2]=0,wn(e,y,t+32|0),f=v[n+4>>2])for(n=v[g+132>>2],e=0;p[n+(e<<2)>>2]=p[f+24>>2],e=e+1|0,f=v[f>>2];);if(yr(u,r,g,y),lr(g),D=g,Y=Gt(r,4535,0),v[D+224>>2]=Y,(f=En(r,2871))&&(ma(e=g+152|0,v[f+12>>2]),n=v[f+12>>2],s[t+32>>1]=0,Fn(e,n,t+32|0),f=v[f+4>>2]))for(n=v[g+164>>2],e=0;s[n+(e<<1)>>1]=v[f+20>>2],e=e+1|0,f=v[f>>2];);n=v[u+4>>2],Ta[v[v[n>>2]+36>>2]](n,g);break i}n=Gt(r,4502,1),n=Ui(h=ut(40),g,y=ht(t+32|0,Ht(r,4381,0),0),$=v[C+4>>2],e=ht(f=t+20|0,v[e+16>>2],0),!!(0|n)),gi(e),gi(y),v[t+20>>2]=n,Un(z,f);break i}e=v[u+4>>2],n=ht(t+32|0,n,0),g=0|Ta[v[v[e>>2]+20>>2]](e,O,n),gi(n),yr(u,r,g,Gt(r,1622,0)<<1),n=v[u+4>>2],Ta[v[v[n>>2]+36>>2]](n,g);break i}if(e=v[u+4>>2],n=ht(f=t+32|0,n,0),g=0|Ta[v[v[e>>2]+24>>2]](e,O,n),gi(n),D=g,Y=!!(0|Gt(r,6279,0)),i[D+80|0]=Y,D=g,Y=!!(0|Gt(r,6318,1)),i[D+81|0]=Y,yr(u,r,g,(n=Gt(r,1622,0))<<1),ya(e=g- -64|0,n=(0|n)/3|0),v[t+32>>2]=0,wn(e,n,f),f=v[En(r,2722)+4>>2])for(n=v[g+76>>2],e=0;p[n+(e<<2)>>2]=p[f+24>>2]*p[u+24>>2],e=e+1|0,f=v[f>>2];);n=v[u+4>>2],Ta[v[v[n>>2]+36>>2]](n,g);break i}e=v[u+4>>2],n=ht(t+32|0,n,0),g=0|Ta[v[v[e>>2]+28>>2]](e,O,n),gi(n),D=g,Z=w(Lt(r,1391,w(0))*p[u+24>>2]),p[D+20>>2]=Z,D=g,Z=w(Lt(r,1217,w(0))*p[u+24>>2]),p[D+24>>2]=Z,D=g,Z=Lt(r,4056,w(0)),p[D+28>>2]=Z,n=v[u+4>>2],Ta[v[v[n>>2]+36>>2]](n,g);break i}e=v[u+4>>2],n=ht(t+32|0,n,0),g=0|Ta[v[v[e>>2]+32>>2]](e,O,n),gi(n),(n=Ht(r,6142,0))&&(D=g,Y=su(k,n=ht(t+32|0,n,0)),v[D+64>>2]=Y,gi(n)),yr(u,r,g,Gt(r,1622,0)<<1),n=v[u+4>>2],Ta[v[v[n>>2]+36>>2]](n,g)}It(O,e=v[C+4>>2],n=ht(t+32|0,T,0),g),gi(n)}if(!(r=v[r>>2]))break}if(!(A=v[A>>2]))break}if(R=R+1|0,!(F=v[F>>2]))break}if((0|(n=v[u+12>>2]))>0)for(g=0;f=v[v[u+20>>2]+(g<<2)>>2],(r=v[f+12>>2]?cu(k,f+8|0):v[k+64>>2])&&(e=Pt(r,v[f+20>>2],f+24|0))&&(r=v[f+4>>2],v[r+56>>2]=l[f+36|0]?e:r,Kn(r,e),lr(v[f+4>>2]),r=v[u+4>>2],Ta[v[v[r>>2]+36>>2]](r,v[f+4>>2])),(0|n)!=(0|(g=g+1|0)););if(Rn(z),v[u+12>>2]=0,(e=En(a,2529))&&(ya(n=k+68|0,v[e+12>>2]),r=v[e+12>>2],v[t+32>>2]=0,$n(n,r,t+32|0),r=v[e+4>>2]))for(f=0;e=ci(e=ut(56),n=ht(t+32|0,v[r+28>>2],0)),gi(n),D=e,Y=Gt(r,1793,0),v[D+16>>2]=Y,D=e,Z=Lt(r,2351,w(0)),p[D+20>>2]=Z,ve(e+24|0,Ht(r,4860,0)),ve(e+36|0,n=Ht(r,3816,0)),n&&(D=e,Z=Lt(r,5681,w(1)),p[D+48>>2]=Z,D=e,Z=Lt(r,6107,w(0)),p[D+52>>2]=Z),v[v[k+80>>2]+(f<<2)>>2]=e,f=f+1|0,r=v[r>>2];);if((e=En(a,2641))&&(ya(n=k+84|0,v[e+12>>2]),r=v[e+12>>2],g=0,v[t+32>>2]=0,$n(n,r,t+32|0),r=v[e+4>>2]))for(;;){I=w(0),V=h=V-80|0,v[h+76>>2]=0,v[h+68>>2]=0,v[h+72>>2]=0,v[h+64>>2]=1048,x=En(r,2774),n=En(r,2439),f=r,B=En(r,4612),O=En(r,4463),(L=En(r,4749))||(L=En(f,2741)),z=En(f,4502),J=En(f,3639),N=En(f,2529),J||(J=En(f,3629));f:{i:{if(n&&($=v[n+4>>2]))for(C=v[2429],T=v[2333],y=v[2772];;){if(M=au(k,r=ht(h+48|0,v[$+28>>2],0)),gi(r),-1==(0|M)){Rn(h- -64|0),F=0,Pi(u,0,n=ht(h+48|0,8184,0),r=ht(h+32|0,v[$+28>>2],0)),gi(r),gi(n);break f}if(A=v[$+4>>2])for(;;){if(uf(r=v[A+28>>2],1841))if(uf(r,3520)){if(uf(r,3535))break i;if(_=Je(ut(40),v[A+12>>2]),v[_+36>>2]=M,e=0,r=v[A+4>>2])for(;S=Ht(r,2177,0),n=Ht(r,4607,0),Ge(_,e,Lt(r,5688,w(0)),ye(S,0),ye(S,1),ye(S,2),ye(S,3),ye(n,0),ye(n,1),ye(n,2)),Ke(r,_,e),e=e+1|0,r=v[r>>2];);v[h+48>>2]=_,Un(h- -64|0,h+48|0),n=m(y,v[A+12>>2]-1|0),r=_+32|0}else{if(S=ef(ut(40),v[A+12>>2]),v[S+20>>2]=M,e=0,r=v[A+4>>2])for(;n=Ht(r,3520,0),Mf(S,e,Lt(r,5688,w(0)),ye(n,0),ye(n,1),ye(n,2),ye(n,3)),Ke(r,S,e),e=e+1|0,r=v[r>>2];);v[h+48>>2]=S,Un(h- -64|0,h+48|0),n=m(T,v[A+12>>2]-1|0),r=S+36|0}else{if(S=ln(ut(40),v[A+12>>2]),v[S+4>>2]=M,F=0,r=v[A+4>>2])for(;e=h+48|0,n=En(r,5773),n=ht(e,n=(0|C)!=v[n+8>>2]?v[n+16>>2]:8590,0),xi(S,F,Lt(r,5688,w(0)),n),gi(n),F=F+1|0,r=v[r>>2];);v[h+48>>2]=S,Un(h- -64|0,h+48|0),n=v[A+12>>2]-1|0,r=S+20|0}if(I=I>(P=p[v[r>>2]+(n<<2)>>2])?I:P,!(A=v[A>>2]))break}if(!($=v[$>>2]))break}t:{if(x&&(A=v[x+4>>2]))for(e=v[2755];;){if(T=vu(k,r=ht(h+48|0,v[A+28>>2],0)),gi(r),-1==(0|T)){Rn(h- -64|0),F=0,Pi(u,0,n=ht(h+48|0,8574,0),r=ht(h+32|0,v[A+28>>2],0)),gi(r),gi(n);break f}if($=v[A+4>>2])for(;;){if(uf(r=v[$+28>>2],5081)){if(n=uf(r,5850),y=uf(r,5106),(r=uf(r,3761))&&!(!n|!y))break t;if(P=p[u+24>>2],n?y?(q=w(0),n=0,r||(n=yu(ut(40),v[$+12>>2]))):(q=w(0),n=_e(ut(40),v[$+12>>2])):(q=w(1),n=pu(ut(40),v[$+12>>2])),v[n+36>>2]=T,r=v[$+4>>2])for(P=y?w(1):P,F=0;Wi(n,F,Lt(r,5688,w(0)),w(P*Lt(r,1391,q)),w(P*Lt(r,1217,q))),Ke(r,n,F),F=F+1|0,r=v[r>>2];);v[h+48>>2]=n,Un(h- -64|0,h+48|0),F=m(e,v[$+12>>2]-1|0),r=n+32|0}else{if(n=ff(ut(40),v[$+12>>2]),v[n+20>>2]=T,F=0,r=v[$+4>>2])for(;Di(n,F,Lt(r,5688,w(0)),Lt(r,5820,w(0))),Ke(r,n,F),F=F+1|0,r=v[r>>2];);v[h+48>>2]=n,Un(h- -64|0,h+48|0),F=(v[$+12>>2]<<1)-2|0,r=n+36|0}if(I=I>(P=p[v[r>>2]+(F<<2)>>2])?I:P,!($=v[$>>2]))break}if(!(A=v[A>>2]))break}if(B&&(A=v[B+4>>2]))for(T=v[2421];;){y=eu(k,r=ht(h+48|0,v[A+28>>2],0)),gi(r),C=So(ut(40),v[A+12>>2]);u:if(e=v[k+104>>2])for(n=v[k+112>>2],r=0;;){if((0|y)==v[n+(r<<2)>>2]){v[C+36>>2]=r;break u}if((0|e)==(0|(r=r+1|0)))break}if(F=0,r=v[A+4>>2])for(;of(C,F,Lt(r,5688,w(0)),Lt(r,1231,w(1)),w(Lt(r,2590,w(0))*p[u+24>>2]),Gt(r,4961,1)?1:-1,!!(0|Gt(r,2581,0)),!!(0|Gt(r,4833,0))),Ke(r,C,F),F=F+1|0,r=v[r>>2];);if(v[h+48>>2]=C,Un(h- -64|0,h+48|0),I=I>(P=p[v[C+32>>2]+(m(T,v[A+12>>2]-1|0)<<2)>>2])?I:P,!(A=v[A>>2]))break}if(O&&(A=v[O+4>>2]))for(T=v[2747];;){y=fu(k,r=ht(h+48|0,v[A+28>>2],0)),gi(r),C=Wo(ut(40),v[A+12>>2]);u:if(e=v[k+120>>2])for(n=v[k+128>>2],r=0;;){if((0|y)==v[n+(r<<2)>>2]){v[C+36>>2]=r;break u}if((0|e)==(0|(r=r+1|0)))break}if(F=0,r=v[A+4>>2])for(;Lf(C,F,Lt(r,5688,w(0)),Lt(r,1269,w(1)),Lt(r,1279,w(1)),Lt(r,1292,w(1)),Lt(r,1260,w(1))),Ke(r,C,F),F=F+1|0,r=v[r>>2];);if(v[h+48>>2]=C,Un(h- -64|0,h+48|0),I=I>(P=p[v[C+32>>2]+(m(T,v[A+12>>2]-1|0)<<2)>>2])?I:P,!(A=v[A>>2]))break}u:{if(L&&(R=v[L+4>>2]))for(y=v[2493],e=v[2485];;){if(C=iu(k,r=ht(h+48|0,v[R+28>>2],0)),gi(r),!C)break u;o:{if(n=v[k+136>>2])for(r=v[k+144>>2],A=0;;){if((0|C)==v[r+(A<<2)>>2])break o;if((0|n)==(0|(A=A+1|0)))break}A=0}if($=v[R+4>>2])for(;;){if(uf(T=v[$+28>>2],3948)&&uf(T,4921)){if(!uf(T,1231)){if(n=Go(ut(40),v[$+12>>2]),v[n+36>>2]=A,F=0,r=v[$+4>>2])for(;Wi(n,F,Lt(r,5688,w(0)),Lt(r,1269,w(1)),Lt(r,1279,w(1))),Ke(r,n,F),F=F+1|0,r=v[r>>2];);v[h+48>>2]=n,Un(h- -64|0,h+48|0),I=I>(P=p[v[n+32>>2]+(m(e,v[$+12>>2]-1|0)<<2)>>2])?I:P}}else{if(r=uf(T,4921),O=ut(40),n=v[$+12>>2],r?(tf(O,n),n=!v[C+44>>2]):(Ut(O,n),n=d[C+48>>2]<2),P=p[u+24>>2],v[O+36>>2]=A,r=v[$+4>>2])for(P=n?P:w(1),F=0;qi(O,F,Lt(r,5688,w(0)),w(P*Lt(r,T,w(0)))),Ke(r,O,F),F=F+1|0,r=v[r>>2];);v[h+48>>2]=O,Un(h- -64|0,h+48|0),I=I>(P=p[v[O+32>>2]+(m(y,v[$+12>>2]-1|0)<<2)>>2])?I:P}if(!($=v[$>>2]))break}if(!(R=v[R>>2]))break}if(z&&(L=v[z+4>>2]))for(;;){if(B=cu(k,r=ht(h+48|0,v[L+28>>2],0)),gi(r),M=v[L+4>>2])for(;;){if(S=au(k,r=ht(h+48|0,v[M+28>>2],0)),gi(r),e=v[M+4>>2])for(;;){if(K=Pt(B,S,r=ht(h+48|0,v[e+28>>2],0)),gi(r),K){if(r=v[K+40>>2],_=(x=v[K+24>>2])?(r>>>0)/3<<1:r,$=Sr(ut(60),v[e+12>>2]),v[$+56>>2]=K,v[$+20>>2]=S,A=v[e+4>>2])for(O=K+36|0,R=0,z=!!(0|x)|(0|_)<=0;;){r=En(A,2901),v[h+60>>2]=0,v[h+52>>2]=0,v[h+56>>2]=0,v[h+48>>2]=8776;o:if(r){F=Gt(A,2200,0),v[h+32>>2]=0,wn(h+48|0,_,h+32|0),r=v[r+4>>2];a:if(p[u+24>>2]!=w(1)){if(r)for(n=v[h+60>>2];p[n+(F<<2)>>2]=p[r+24>>2]*p[u+24>>2],F=F+1|0,r=v[r>>2];);}else{if(!r)break a;for(n=v[h+60>>2];p[n+(F<<2)>>2]=p[r+24>>2],F=F+1|0,r=v[r>>2];);}if(!z)for(C=v[K+48>>2],r=0,T=v[h+60>>2];p[(n=T+(y=r<<2)|0)>>2]=p[y+C>>2]+p[n>>2],(0|_)!=(0|(r=r+1|0)););}else{if(x){v[h+32>>2]=0,wn(h+48|0,_,h+32|0);break o}Df(h+48|0,O)}if(r=h+48|0,Zi($,R,Lt(A,5688,w(0)),r),Ke(A,$,R),R=R+1|0,To(r),!(A=v[A>>2]))break}v[h+48>>2]=$,Un(h- -64|0,h+48|0),I=I>(P=p[(v[$+36>>2]+(v[e+12>>2]<<2)|0)-4>>2])?I:P}else Rn(h- -64|0);if(!(e=v[e>>2]))break}if(!(M=v[M>>2]))break}if(!(L=v[L>>2]))break}if(J){if(C=Hr(ut(36),v[J+12>>2]),A=v[J+4>>2])for(R=0;;){if(v[h+60>>2]=0,v[h+52>>2]=0,v[h+56>>2]=0,v[h+48>>2]=8744,e=En(A,2573)){v[h+44>>2]=0,v[h+36>>2]=0,v[h+40>>2]=0,v[h+32>>2]=8744,(0|(r=v[k+36>>2]))!=(0|(n=v[e+12>>2]))?(n=r-n|0,v[h+40>>2]=n,r=v[5280],D=h,Y=0|Ta[v[v[r>>2]+16>>2]](r,0,n<<2,8590,101),v[D+44>>2]=Y,r=v[e+12>>2],n=v[k+36>>2]):n=r,v[h+20>>2]=0,gn(h+32|0,n-r|0,h+20|0),(r=v[k+36>>2])>>>0>d[h+56>>2]&&(v[h+56>>2]=r,n=v[5280],D=h,Y=0|Ta[v[v[n>>2]+16>>2]](n,v[h+60>>2],r<<2,8590,101),v[D+60>>2]=Y,r=v[k+36>>2]),v[h+20>>2]=0,gn(h+48|0,r,h+20|0),(0|(r=v[k+36>>2]))>0&&zi(v[h+60>>2],255,r<<2),r=0,F=0;o:if($=v[e+4>>2]){for(;;){if(n=au(k,e=ht(h+20|0,Ht($,1545,0),0)),gi(e),-1!=(0|n)){if((0|r)!=(0|n)){for(e=v[h+44>>2];v[e+(F<<2)>>2]=r,F=F+1|0,(0|n)!=(0|(r=r+1|0)););r=n}if(n=Gt($,2200,0),v[v[h+60>>2]+(r+n<<2)>>2]=r,r=r+1|0,$=v[$>>2])continue;break o}break}Rn(h- -64|0),F=0,Pi(u,0,n=ht(h+20|0,8184,0),r=ht(h+8|0,Ht($,1545,0),0)),gi(r),gi(n),oo(h+32|0),oo(h+48|0);break f}if((e=v[k+36>>2])>>>0>r>>>0)for(n=v[h+44>>2];v[n+(F<<2)>>2]=r,F=F+1|0,(0|e)!=(0|(r=r+1|0)););if((0|e)>0)for(T=v[h+44>>2],y=v[h+60>>2];-1==v[(n=y+((r=e-1|0)<<2)|0)>>2]&&(F=F-1|0,v[n>>2]=v[T+(F<<2)>>2]),n=e>>>0>1,e=r,n;);oo(h+32|0)}if(r=h+48|0,Ce(C,R,Lt(A,5688,w(0)),r),R=R+1|0,oo(r),!(A=v[A>>2]))break}v[h+48>>2]=C,Un(h- -64|0,h+48|0),I=I>(P=p[(v[C+16>>2]+(v[J+12>>2]<<2)|0)-4>>2])?I:P}o:{if(N){if(e=de(ut(36),v[N+12>>2]),r=v[N+4>>2])for($=0;;){if(y=hu(k,n=ht(h+48|0,Ht(r,5773,0),0)),gi(n),!y)break o;if(D=n=ii(ut(40),Lt(r,5688,w(0)),y),Y=Gt(r,1793,v[y+16>>2]),v[D+12>>2]=Y,D=n,Z=Lt(r,2351,p[y+20>>2]),p[D+16>>2]=Z,ve(n+20|0,Ht(r,4860,v[y+32>>2])),v[y+40>>2]&&(D=n,Z=Lt(r,5681,w(1)),p[D+32>>2]=Z,D=n,Z=Lt(r,6107,w(0)),p[D+36>>2]=Z),Ni(e,$,n),$=$+1|0,!(r=v[r>>2]))break}v[h+48>>2]=e,Un(h- -64|0,h+48|0),I=I>(P=p[(v[e+16>>2]+(v[N+12>>2]<<2)|0)-4>>2])?I:P}F=qr(n=ut(48),r=ht(h+48|0,v[f+28>>2],0),h- -64|0,I),gi(r);break f}Rn(h- -64|0),F=0,Pi(u,0,n=ht(h+48|0,8331,0),r=ht(h+32|0,Ht(r,5773,0),0)),gi(r),gi(n);break f}Rn(h- -64|0),F=0,Pi(u,0,n=ht(h+48|0,8272,0),r=ht(h+32|0,v[R+28>>2],0)),gi(r),gi(n);break f}Rn(h- -64|0),F=0,Pi(u,0,n=ht(h+48|0,8125,0),r=ht(h+32|0,v[$+28>>2],0)),gi(r),gi(n);break f}Rn(h- -64|0),F=0,Pi(u,0,n=ht(h+48|0,8064,0),r=ht(h+32|0,v[A+28>>2],0)),gi(r),gi(n)}if(so(h- -64|0),V=h+80|0,F&&(v[v[k+96>>2]+(g<<2)>>2]=F,g=g+1|0),!(r=v[f>>2]))break}ar(Fe(a));break r}Ta[v[v[k>>2]+4>>2]](k),k=0,Pi(u,a,n=ht(t+32|0,8413,0),r=ht(t+20|0,e,0)),gi(r),gi(n);break r}Ta[v[v[k>>2]+4>>2]](k),k=0,Pi(u,a,n=ht(t+32|0,8435,0),r=ht(t+20|0,e,0)),gi(r),gi(n)}return V=t+288|0,r=k,v[u+36>>2]&&(v[c>>2]=v[u+40>>2],H(20831,17040,0|c)),hr(r,e=o+48|0,n=o+32|0),Wf(u),V=c+48|0,be(n),be(e),V=o- -64|0,0|r},function(r,n,e,f,i){var t;return r|=0,n|=0,f|=0,i|=0,V=t=V-32|0,e=$e(t+20|0,4+(e|=0)|0,v[e>>2],0),v[t+12>>2]=i,v[t+16>>2]=f,r=0|Ta[0|r](n,e,t+16|0,t+12|0),E(v[t+12>>2]),E(v[t+16>>2]),gi(e),V=t+32|0,0|r},function(r,n,e,f){r|=0,n|=0,e|=0,f|=0;var t,o,a,c,b=0,s=0,h=0,y=0,g=0,F=0,A=w(0),T=0,$=0,I=0,C=0,P=0,O=w(0),R=0,S=0,L=0,M=0,_=0,z=0,x=0,J=0,K=0,B=0,N=0,q=0,D=0,Z=0,Y=0,X=0,rr=w(0),nr=0,er=0,fr=0,ir=0,tr=0;if(V=o=V+-64|0,h=v[e>>2],s=0|j(4696),b=0|U(0|h,0|s),E(0|s),c=+G(0|b,21553,0|(s=o+48|0)),W(v[o+48>>2]),E(0|b),v[o+60>>2]=0,v[o+52>>2]=0,v[o+56>>2]=0,v[o+48>>2]=9212,v[o+44>>2]=0,v[o+36>>2]=0,v[o+40>>2]=0,v[o+32>>2]=9212,tn(h=s,s=c<4294967296&c>=0?~~c>>>0:0,b=ht(o+20|0,8590,0)),gi(b),tn(o+32|0,s,b=ht(o+20|0,8590,0)),gi(b),v[o+16>>2]=0,s)for(;qf(y=o+12|0,e,$=o+16|0),wf(b=o+20|0,y),te(v[o+60>>2]+m(v[o+16>>2],12)|0,b),gi(b),E(v[o+12>>2]),qf(y,f,$),wf(b,y),te(v[o+44>>2]+m(v[o+16>>2],12)|0,b),gi(b),E(v[o+12>>2]),b=v[o+16>>2]+1|0,v[o+16>>2]=b,b>>>0<s>>>0;);V=a=V-48|0,n=sf(ut(40),v[n+8>>2],v[n+4>>2]),ir=$=a+4|0,tr=Ot(ut(12),n),v[ir+4>>2]=tr,v[$+20>>2]=0,v[$+12>>2]=0,v[$+16>>2]=0,v[$+8>>2]=10344,v[$>>2]=10312,Tt($+24|0),i[$+40|0]=0,v[$+36>>2]=1065353216,v[$+36>>2]=1065353216,n=v[5445],V=t=V-16|0,b=ut(12),v[b+8>>2]=r+n,v[b+4>>2]=n,v[b>>2]=10328,v[$+12>>2]=0,Rf((e=Mr(ut(232)))+176|0,$f(b)),Rf(r=e+164|0,$f(b)),r=ei(n=ht(t+4|0,6917,0),r),gi(n);r:if(r)Ta[v[v[b>>2]+4>>2]](b),e&&Ta[v[v[e>>2]+4>>2]](e),oe($,6971,8590),e=0;else{if(r=v[b+4>>2],v[b+4>>2]=r+1,n=l[0|r],v[b+4>>2]=r+2,f=l[r+1|0],v[b+4>>2]=r+3,s=l[r+2|0],v[b+4>>2]=r+4,v[e+148>>2]=l[r+3|0]|(s|f<<8|n<<16)<<8,v[b+4>>2]=r+5,n=l[r+4|0],v[b+4>>2]=r+6,f=l[r+5|0],v[b+4>>2]=r+7,s=l[r+6|0],v[b+4>>2]=r+8,v[e+152>>2]=l[r+7|0]|(s|f<<8|n<<16)<<8,v[b+4>>2]=r+9,n=l[r+8|0],v[b+4>>2]=r+10,f=l[r+9|0],v[b+4>>2]=r+11,s=l[r+10|0],v[b+4>>2]=r+12,v[e+156>>2]=l[r+11|0]|(s|f<<8|n<<16)<<8,v[b+4>>2]=r+13,n=l[r+12|0],v[b+4>>2]=r+14,f=l[r+13|0],v[b+4>>2]=r+15,s=l[r+14|0],v[b+4>>2]=r+16,v[e+160>>2]=l[r+15|0]|(s|f<<8|n<<16)<<8,v[b+4>>2]=r+17,(y=l[r+16|0])&&(v[b+4>>2]=r+18,n=l[r+17|0],v[b+4>>2]=r+19,f=l[r+18|0],v[b+4>>2]=r+20,s=l[r+19|0],v[b+4>>2]=r+21,v[e+204>>2]=l[r+20|0]|(s|f<<8|n<<16)<<8,Rf(e+208|0,$f(b)),Rf(e+220|0,$f(b))),(0|(r=jn(b,1)))>0)for(n=e+188|0;ir=t,tr=$f(b),v[ir+4>>2]=tr,Un(n,t+4|0),(0|r)!=(0|(F=F+1|0)););if(s=jn(b,1),v[t+4>>2]=0,$n(e+16|0,s,t+4|0),(0|s)>0)for(f=0;n=$f(b),f?(r=jn(b,1),r=v[v[e+28>>2]+(r<<2)>>2]):r=0,n=ne(g=ut(64),f,h=ht(t+4|0,n,1),r),gi(h),r=v[b+4>>2],v[b+4>>2]=r+1,h=l[0|r],v[b+4>>2]=r+2,F=l[r+1|0],v[b+4>>2]=r+3,g=l[r+2|0],v[b+4>>2]=r+4,v[n+36>>2]=l[r+3|0]|(g|F<<8|h<<16)<<8,v[b+4>>2]=r+5,h=l[r+4|0],v[b+4>>2]=r+6,F=l[r+5|0],v[b+4>>2]=r+7,g=l[r+6|0],v[b+4>>2]=r+8,A=p[$+36>>2],p[n+28>>2]=A*(u(2,l[r+7|0]|(g|F<<8|h<<16)<<8),k()),v[b+4>>2]=r+9,h=l[r+8|0],v[b+4>>2]=r+10,F=l[r+9|0],v[b+4>>2]=r+11,g=l[r+10|0],v[b+4>>2]=r+12,p[n+32>>2]=A*(u(2,l[r+11|0]|(g|F<<8|h<<16)<<8),k()),v[b+4>>2]=r+13,h=l[r+12|0],v[b+4>>2]=r+14,F=l[r+13|0],v[b+4>>2]=r+15,g=l[r+14|0],v[b+4>>2]=r+16,v[n+40>>2]=l[r+15|0]|(g|F<<8|h<<16)<<8,v[b+4>>2]=r+17,h=l[r+16|0],v[b+4>>2]=r+18,F=l[r+17|0],v[b+4>>2]=r+19,g=l[r+18|0],v[b+4>>2]=r+20,v[n+44>>2]=l[r+19|0]|(g|F<<8|h<<16)<<8,v[b+4>>2]=r+21,h=l[r+20|0],v[b+4>>2]=r+22,F=l[r+21|0],v[b+4>>2]=r+23,g=l[r+22|0],v[b+4>>2]=r+24,v[n+48>>2]=l[r+23|0]|(g|F<<8|h<<16)<<8,v[b+4>>2]=r+25,h=l[r+24|0],v[b+4>>2]=r+26,F=l[r+25|0],v[b+4>>2]=r+27,g=l[r+26|0],v[b+4>>2]=r+28,v[n+52>>2]=l[r+27|0]|(g|F<<8|h<<16)<<8,v[b+4>>2]=r+29,h=l[r+28|0],v[b+4>>2]=r+30,F=l[r+29|0],v[b+4>>2]=r+31,g=l[r+30|0],v[b+4>>2]=r+32,p[n+24>>2]=A*(u(2,l[r+31|0]|(g|F<<8|h<<16)<<8),k()),ir=n,tr=jn(b,1),v[ir+56>>2]=tr,r=v[b+4>>2],v[b+4>>2]=r+1,i[n+60|0]=0!=l[0|r],y&&(v[b+4>>2]=r+5),v[v[e+28>>2]+(f<<2)>>2]=n,(0|s)!=(0|(f=f+1|0)););if(h=jn(b,1),v[t+4>>2]=0,$n(e+32|0,h,t+4|0),(0|h)>0)for(r=0;n=$f(b),f=jn(b,1),f=v[v[e+28>>2]+(f<<2)>>2],f=Dn(s=ut(84),r,n=ht(t+4|0,n,1),f),gi(n),s=Xo(f),n=v[b+4>>2],v[b+4>>2]=n+1,p[s+4>>2]=w(l[0|n])/w(255),v[b+4>>2]=n+2,p[s+8>>2]=w(l[n+1|0])/w(255),v[b+4>>2]=n+3,p[s+12>>2]=w(l[n+2|0])/w(255),v[b+4>>2]=n+4,p[s+16>>2]=w(l[n+3|0])/w(255),v[b+4>>2]=n+5,s=l[n+4|0],v[b+4>>2]=n+6,F=l[n+5|0],v[b+4>>2]=n+7,g=l[n+6|0],v[b+4>>2]=n+8,255==(s&F)&255==(g&l[n+7|0])||(n=la(f),v[n+16>>2]=1065353216,p[n+12>>2]=w(g>>>0)/w(255),p[n+8>>2]=w(F>>>0)/w(255),p[n+4>>2]=w(s>>>0)/w(255),vf(n),ha(f)),ve(s=f+68|0,n=(n=jn(b,1))?v[(v[e+200>>2]+(n<<2)|0)-4>>2]:0),ir=f,tr=jn(b,1),v[ir+80>>2]=tr,v[v[e+44>>2]+(r<<2)>>2]=f,(0|h)!=(0|(r=r+1|0)););if(s=jn(b,1),v[t+4>>2]=0,$n(e+100|0,s,t+4|0),(0|s)>0)for(r=0;;){if(n=$f(b),f=Qe(f=ut(60),n=ht(h=t+4|0,n,1)),gi(n),ba(f,jn(b,1)),n=v[b+4>>2],v[b+4>>2]=n+1,va(f,0!=l[0|n]),n=jn(b,1),v[t+4>>2]=0,$n(f+24|0,n,h),(0|n)>0)for(F=0;h=jn(b,1),v[v[f+36>>2]+(F<<2)>>2]=v[v[e+28>>2]+(h<<2)>>2],(0|n)!=(0|(F=F+1|0)););if(n=jn(b,1),v[f+40>>2]=v[v[e+28>>2]+(n<<2)>>2],n=v[b+4>>2],v[b+4>>2]=n+1,h=l[0|n],v[b+4>>2]=n+2,F=l[n+1|0],v[b+4>>2]=n+3,g=l[n+2|0],v[b+4>>2]=n+4,v[f+52>>2]=l[n+3|0]|(g|F<<8|h<<16)<<8,v[b+4>>2]=n+5,h=l[n+4|0],v[b+4>>2]=n+6,F=l[n+5|0],v[b+4>>2]=n+7,g=l[n+6|0],v[b+4>>2]=n+8,p[f+56>>2]=p[$+36>>2]*(u(2,l[n+7|0]|(g|F<<8|h<<16)<<8),k()),v[b+4>>2]=n+9,v[f+44>>2]=i[n+8|0],v[b+4>>2]=n+10,i[f+48|0]=0!=l[n+9|0],v[b+4>>2]=n+11,i[f+49|0]=0!=l[n+10|0],v[b+4>>2]=n+12,i[f+50|0]=0!=l[n+11|0],v[v[e+112>>2]+(r<<2)>>2]=f,(0|s)==(0|(r=r+1|0)))break}if(s=jn(b,1),v[t+4>>2]=0,$n(e+116|0,s,t+4|0),(0|s)>0)for(r=0;;){if(n=$f(b),f=it(f=ut(88),n=ht(h=t+4|0,n,1)),gi(n),ba(f,jn(b,1)),n=v[b+4>>2],v[b+4>>2]=n+1,va(f,0!=l[0|n]),n=jn(b,1),v[t+4>>2]=0,$n(f+24|0,n,h),(0|n)>0)for(F=0;h=jn(b,1),v[v[f+36>>2]+(F<<2)>>2]=v[v[e+28>>2]+(h<<2)>>2],(0|n)!=(0|(F=F+1|0)););if(n=jn(b,1),v[f+40>>2]=v[v[e+28>>2]+(n<<2)>>2],n=v[b+4>>2],v[b+4>>2]=n+1,i[f+85|0]=0!=l[0|n],v[b+4>>2]=n+2,i[f+84|0]=0!=l[n+1|0],v[b+4>>2]=n+3,h=l[n+2|0],v[b+4>>2]=n+4,F=l[n+3|0],v[b+4>>2]=n+5,g=l[n+4|0],v[b+4>>2]=n+6,v[f+60>>2]=l[n+5|0]|(g|F<<8|h<<16)<<8,v[b+4>>2]=n+7,h=l[n+6|0],v[b+4>>2]=n+8,F=l[n+7|0],v[b+4>>2]=n+9,g=l[n+8|0],v[b+4>>2]=n+10,A=p[$+36>>2],p[f+64>>2]=A*(u(2,l[n+9|0]|(g|F<<8|h<<16)<<8),k()),v[b+4>>2]=n+11,h=l[n+10|0],v[b+4>>2]=n+12,F=l[n+11|0],v[b+4>>2]=n+13,g=l[n+12|0],v[b+4>>2]=n+14,p[f+68>>2]=A*(u(2,l[n+13|0]|(g|F<<8|h<<16)<<8),k()),v[b+4>>2]=n+15,h=l[n+14|0],v[b+4>>2]=n+16,F=l[n+15|0],v[b+4>>2]=n+17,g=l[n+16|0],v[b+4>>2]=n+18,v[f+72>>2]=l[n+17|0]|(g|F<<8|h<<16)<<8,v[b+4>>2]=n+19,h=l[n+18|0],v[b+4>>2]=n+20,F=l[n+19|0],v[b+4>>2]=n+21,g=l[n+20|0],v[b+4>>2]=n+22,v[f+76>>2]=l[n+21|0]|(g|F<<8|h<<16)<<8,v[b+4>>2]=n+23,h=l[n+22|0],v[b+4>>2]=n+24,F=l[n+23|0],v[b+4>>2]=n+25,g=l[n+24|0],v[b+4>>2]=n+26,v[f+80>>2]=l[n+25|0]|(g|F<<8|h<<16)<<8,v[b+4>>2]=n+27,h=l[n+26|0],v[b+4>>2]=n+28,F=l[n+27|0],v[b+4>>2]=n+29,g=l[n+28|0],v[b+4>>2]=n+30,v[f+44>>2]=l[n+29|0]|(g|F<<8|h<<16)<<8,v[b+4>>2]=n+31,h=l[n+30|0],v[b+4>>2]=n+32,F=l[n+31|0],v[b+4>>2]=n+33,g=l[n+32|0],v[b+4>>2]=n+34,v[f+48>>2]=l[n+33|0]|(g|F<<8|h<<16)<<8,v[b+4>>2]=n+35,h=l[n+34|0],v[b+4>>2]=n+36,F=l[n+35|0],v[b+4>>2]=n+37,g=l[n+36|0],v[b+4>>2]=n+38,v[f+52>>2]=l[n+37|0]|(g|F<<8|h<<16)<<8,v[b+4>>2]=n+39,h=l[n+38|0],v[b+4>>2]=n+40,F=l[n+39|0],v[b+4>>2]=n+41,g=l[n+40|0],v[b+4>>2]=n+42,v[f+56>>2]=l[n+41|0]|(g|F<<8|h<<16)<<8,v[v[e+128>>2]+(r<<2)>>2]=f,(0|s)==(0|(r=r+1|0)))break}if(s=jn(b,1),v[t+4>>2]=0,$n(e+132|0,s,t+4|0),(0|s)>0)for(r=0;;){if(n=$f(b),f=tt(f=ut(76),n=ht(h=t+4|0,n,1)),gi(n),ba(f,jn(b,1)),n=v[b+4>>2],v[b+4>>2]=n+1,va(f,0!=l[0|n]),n=jn(b,1),v[t+4>>2]=0,$n(f+24|0,n,h),(0|n)>0)for(F=0;h=jn(b,1),v[v[f+36>>2]+(F<<2)>>2]=v[v[e+28>>2]+(h<<2)>>2],(0|n)!=(0|(F=F+1|0)););if(n=jn(b,1),v[f+40>>2]=v[v[e+44>>2]+(n<<2)>>2],ir=f,tr=jn(b,1),v[ir+44>>2]=tr,ir=f,tr=jn(b,1),v[ir+48>>2]=tr,ir=f,tr=jn(b,1),v[ir+52>>2]=tr,n=v[b+4>>2],v[b+4>>2]=n+1,h=l[0|n],v[b+4>>2]=n+2,F=l[n+1|0],v[b+4>>2]=n+3,g=l[n+2|0],v[b+4>>2]=n+4,v[f+56>>2]=l[n+3|0]|(g|F<<8|h<<16)<<8,v[b+4>>2]=n+5,h=l[n+4|0],v[b+4>>2]=n+6,F=l[n+5|0],v[b+4>>2]=n+7,g=l[n+6|0],v[b+4>>2]=n+8,h=l[n+7|0]|(g|F<<8|h<<16)<<8,v[f+60>>2]=h,v[f+44>>2]||(p[f+60>>2]=p[$+36>>2]*(u(2,h),k())),v[b+4>>2]=n+9,h=l[n+8|0],v[b+4>>2]=n+10,F=l[n+9|0],v[b+4>>2]=n+11,g=l[n+10|0],v[b+4>>2]=n+12,h=l[n+11|0]|(g|F<<8|h<<16)<<8,v[f+64>>2]=h,d[f+48>>2]<=1&&(p[f+64>>2]=p[$+36>>2]*(u(2,h),k())),v[b+4>>2]=n+13,h=l[n+12|0],v[b+4>>2]=n+14,F=l[n+13|0],v[b+4>>2]=n+15,g=l[n+14|0],v[b+4>>2]=n+16,v[f+68>>2]=l[n+15|0]|(g|F<<8|h<<16)<<8,v[b+4>>2]=n+17,h=l[n+16|0],v[b+4>>2]=n+18,F=l[n+17|0],v[b+4>>2]=n+19,g=l[n+18|0],v[b+4>>2]=n+20,v[f+72>>2]=l[n+19|0]|(g|F<<8|h<<16)<<8,v[v[e+144>>2]+(r<<2)>>2]=f,(0|s)==(0|(r=r+1|0)))break}if(r=Q($,b,1,e,!!(0|y)),v[t>>2]=r,r&&(v[e+64>>2]=r,Un(e+48|0,t)),r=jn(b,1))for(n=e+48|0,F=0;ir=t,tr=Q($,b,0,e,!!(0|y)),v[ir+4>>2]=tr,Un(n,t+4|0),(0|r)!=(0|(F=F+1|0)););if((0|(y=v[$+12>>2]))>0)for(r=0;;){if(f=v[v[$+20>>2]+(r<<2)>>2],!(n=v[f+12>>2]?cu(e,f+8|0):v[e+64>>2])){Ta[v[v[b>>2]+4>>2]](b),e&&Ta[v[v[e>>2]+4>>2]](e),oe($,8372,v[f+16>>2]),e=0;break r}if(!(n=Pt(n,v[f+20>>2],f+24|0))){Ta[v[v[b>>2]+4>>2]](b),e&&Ta[v[v[e>>2]+4>>2]](e),oe($,8389,v[f+32>>2]),e=0;break r}if(s=v[f+4>>2],v[s+56>>2]=l[f+36|0]?n:s,Kn(s,n),lr(v[f+4>>2]),n=v[$+4>>2],Ta[v[v[n>>2]+36>>2]](n,v[f+4>>2]),(0|y)==(0|(r=r+1|0)))break}if(Rn($+8|0),v[$+12>>2]=0,s=jn(b,1),v[t+4>>2]=0,$n(e+68|0,s,t+4|0),(0|s)>0)for(f=0;r=(r=jn(b,1))?v[(v[e+200>>2]+(r<<2)|0)-4>>2]:0,n=ci(n=ut(56),r=ht(t+4|0,r,0)),gi(r),ir=n,tr=jn(b,0),v[ir+16>>2]=tr,r=v[b+4>>2],v[b+4>>2]=r+1,y=l[0|r],v[b+4>>2]=r+2,h=l[r+1|0],v[b+4>>2]=r+3,F=l[r+2|0],v[b+4>>2]=r+4,v[n+20>>2]=l[r+3|0]|(F|h<<8|y<<16)<<8,Rf(n+24|0,$f(b)),Rf(n+36|0,$f(b)),v[n+40>>2]&&(r=v[b+4>>2],v[b+4>>2]=r+1,y=l[0|r],v[b+4>>2]=r+2,h=l[r+1|0],v[b+4>>2]=r+3,F=l[r+2|0],v[b+4>>2]=r+4,v[n+48>>2]=l[r+3|0]|(F|h<<8|y<<16)<<8,v[b+4>>2]=r+5,y=l[r+4|0],v[b+4>>2]=r+6,h=l[r+5|0],v[b+4>>2]=r+7,F=l[r+6|0],v[b+4>>2]=r+8,v[n+52>>2]=l[r+7|0]|(F|h<<8|y<<16)<<8),v[v[e+80>>2]+(f<<2)>>2]=n,(0|s)!=(0|(f=f+1|0)););if(nr=jn(b,1),v[t+4>>2]=0,$n(e+84|0,nr,t+4|0),(0|nr)>0)for(F=0;;){er=ht(t+4|0,$f(b),1),h=0,O=w(0),V=y=V+-64|0,v[y+60>>2]=0,v[y+52>>2]=0,v[y+56>>2]=0,v[y+48>>2]=1048,rr=p[$+36>>2];n:{e:if(!((0|(C=jn(b,1)))<=0))for(K=v[2333],L=v[2772],T=1;;){if(I=jn(b,1),g=0,(0|(z=jn(b,1)))>0)for(;;){r=v[b+4>>2],v[b+4>>2]=r+1,r=l[0|r],f=jn(b,1);f:{i:{t:{u:{switch(0|r){case 0:if(r=ln(ut(40),f),v[r+4>>2]=I,n=0,(0|f)<=0)break i;for(;s=v[b+4>>2],v[b+4>>2]=s+1,R=l[0|s],v[b+4>>2]=s+2,S=l[s+1|0],v[b+4>>2]=s+3,P=l[s+2|0],v[b+4>>2]=s+4,u(2,l[s+3|0]|(P|S<<8|R<<16)<<8),xi(r,n,A=k(),s=ht(P=y+32|0,s=(s=jn(b,1))?v[(v[e+200>>2]+(s<<2)|0)-4>>2]:0,0)),gi(s),(0|f)!=(0|(n=n+1|0)););break i;case 1:if(s=ef(ut(40),f),v[s+20>>2]=I,R=f-1|0,n=0,(0|f)<=0)break t;for(;r=v[b+4>>2],v[b+4>>2]=r+1,S=l[0|r],v[b+4>>2]=r+2,P=l[r+1|0],v[b+4>>2]=r+3,_=l[r+2|0],v[b+4>>2]=r+4,M=l[r+3|0],v[b+4>>2]=r+5,J=l[r+4|0],v[b+4>>2]=r+6,x=l[r+5|0],v[b+4>>2]=r+7,B=l[r+6|0],v[b+4>>2]=r+8,Mf(s,n,(u(2,M|(_|P<<8|S<<16)<<8),k()),w(w(J>>>0)/w(255)),w(w(x>>>0)/w(255)),w(w(B>>>0)/w(255)),w(w(l[r+7|0])/w(255))),(0|n)<(0|R)&&Lr(b,n,s),(0|f)!=(0|(n=n+1|0)););break t;case 2:if(s=Je(ut(40),f),v[s+36>>2]=I,R=f-1|0,n=0,(0|f)<=0)break u;for(;r=v[b+4>>2],v[b+4>>2]=r+1,S=l[0|r],v[b+4>>2]=r+2,P=l[r+1|0],v[b+4>>2]=r+3,_=l[r+2|0],v[b+4>>2]=r+4,M=l[r+3|0],v[b+4>>2]=r+5,J=l[r+4|0],v[b+4>>2]=r+6,x=l[r+5|0],v[b+4>>2]=r+7,B=l[r+6|0],v[b+4>>2]=r+8,q=l[r+7|0],v[b+4>>2]=r+10,D=l[r+9|0],v[b+4>>2]=r+11,N=l[r+10|0],v[b+4>>2]=r+12,Ge(s,n,(u(2,M|(_|P<<8|S<<16)<<8),k()),w(w(J>>>0)/w(255)),w(w(x>>>0)/w(255)),w(w(B>>>0)/w(255)),w(w(q>>>0)/w(255)),w(w(D>>>0)/w(255)),w(w(N>>>0)/w(255)),w(w(l[r+11|0])/w(255))),(0|n)<(0|R)&&Lr(b,n,s),(0|f)!=(0|(n=n+1|0)););break u}if(Rn(y+48|0),oe($,8064,v[v[v[e+44>>2]+(I<<2)>>2]+16>>2]),r=0,1&T)break n;break e}v[y+32>>2]=s,n=s+32|0,Un(y+48|0,y+32|0),r=m(R,L);break f}v[y+32>>2]=s,n=s+36|0,Un(y+48|0,y+32|0),r=m(R,K);break f}v[y+32>>2]=r,n=r+20|0,Un(y+48|0,y+32|0),r=f-1|0}if(O=(A=p[v[n>>2]+(r<<2)>>2])<O?O:A,(0|z)==(0|(g=g+1|0)))break}if(T=(0|C)>(0|(h=h+1|0)),(0|h)==(0|C))break}e:if(!((0|(C=jn(b,1)))<=0))for(h=0,K=v[2755],T=1;;){if(I=jn(b,1),g=0,(0|(L=jn(b,1)))>0)for(;;){r=v[b+4>>2],v[b+4>>2]=r+1,r=l[0|r],s=jn(b,1);f:{i:{t:{u:{o:switch(0|r){case 0:if(f=ff(ut(40),s),v[f+20>>2]=I,(0|s)>0)for(R=s-1|0,n=0;r=v[b+4>>2],v[b+4>>2]=r+1,z=l[0|r],v[b+4>>2]=r+2,S=l[r+1|0],v[b+4>>2]=r+3,P=l[r+2|0],v[b+4>>2]=r+4,_=l[r+3|0],v[b+4>>2]=r+5,M=l[r+4|0],v[b+4>>2]=r+6,J=l[r+5|0],v[b+4>>2]=r+7,x=l[r+6|0],v[b+4>>2]=r+8,Di(f,n,(u(2,_|(P|S<<8|z<<16)<<8),k()),(u(2,l[r+7|0]|(x|J<<8|M<<16)<<8),k())),(0|n)<(0|R)&&Lr(b,n,f),(0|s)!=(0|(n=n+1|0)););v[y+32>>2]=f,n=(s<<1)-2|0,Un(y+48|0,y+32|0),r=f+36|0;break f;case 2:A=w(1),f=pu(ut(40),s);break u;case 3:A=w(1),f=yu(ut(40),s);break u;case 1:break o;default:break t}A=rr,f=_e(ut(40),s)}if(v[f+36>>2]=I,R=s-1|0,n=0,(0|s)<=0)break i;for(;r=v[b+4>>2],v[b+4>>2]=r+1,z=l[0|r],v[b+4>>2]=r+2,S=l[r+1|0],v[b+4>>2]=r+3,P=l[r+2|0],v[b+4>>2]=r+4,_=l[r+3|0],v[b+4>>2]=r+5,M=l[r+4|0],v[b+4>>2]=r+6,J=l[r+5|0],v[b+4>>2]=r+7,x=l[r+6|0],v[b+4>>2]=r+8,B=l[r+7|0],v[b+4>>2]=r+9,q=l[r+8|0],v[b+4>>2]=r+10,D=l[r+9|0],v[b+4>>2]=r+11,N=l[r+10|0],v[b+4>>2]=r+12,Wi(f,n,(u(2,_|(P|S<<8|z<<16)<<8),k()),w(A*(u(2,B|(x|J<<8|M<<16)<<8),k())),w(A*(u(2,l[r+11|0]|(N|D<<8|q<<16)<<8),k()))),(0|n)<(0|R)&&Lr(b,n,f),(0|s)!=(0|(n=n+1|0)););break i}if(Rn(y+48|0),oe($,8125,v[v[v[e+28>>2]+(I<<2)>>2]+16>>2]),r=0,1&T)break n;break e}n=m(R,K),v[y+32>>2]=f,Un(y+48|0,y+32|0),r=f+32|0}if(O=(A=p[v[r>>2]+(n<<2)>>2])<O?O:A,(0|L)==(0|(g=g+1|0)))break}if(T=(0|C)>(0|(h=h+1|0)),(0|h)==(0|C))break}if((0|(T=jn(b,1)))>0)for(I=v[2421],g=0;;){if(r=jn(b,1),f=jn(b,1),s=So(ut(40),f),v[s+36>>2]=r,h=f-1|0,n=0,(0|f)>0)for(;r=v[b+4>>2],v[b+4>>2]=r+1,C=l[0|r],v[b+4>>2]=r+2,R=l[r+1|0],v[b+4>>2]=r+3,K=l[r+2|0],v[b+4>>2]=r+4,L=l[r+3|0],v[b+4>>2]=r+5,z=l[r+4|0],v[b+4>>2]=r+6,S=l[r+5|0],v[b+4>>2]=r+7,P=l[r+6|0],v[b+4>>2]=r+8,_=l[r+7|0],v[b+4>>2]=r+9,M=l[r+8|0],v[b+4>>2]=r+10,J=l[r+9|0],v[b+4>>2]=r+11,x=l[r+10|0],v[b+4>>2]=r+12,A=p[$+36>>2],B=l[r+11|0],v[b+4>>2]=r+13,q=i[r+12|0],v[b+4>>2]=r+14,D=l[r+13|0],v[b+4>>2]=r+15,of(s,n,(u(2,L|(K|R<<8|C<<16)<<8),k()),(u(2,_|(P|S<<8|z<<16)<<8),k()),w(A*(u(2,B|(x|J<<8|M<<16)<<8),k())),q,!!(0|D),0!=l[r+14|0]),(0|n)<(0|h)&&Lr(b,n,s),(0|f)!=(0|(n=n+1|0)););if(v[y+32>>2]=s,Un(y+48|0,y+32|0),O=(A=p[v[s+32>>2]+(m(h,I)<<2)>>2])<O?O:A,(0|T)==(0|(g=g+1|0)))break}if((0|(T=jn(b,1)))>0)for(I=v[2747],f=0;;){if(r=jn(b,1),s=jn(b,1),h=Wo(ut(40),s),v[h+36>>2]=r,g=s-1|0,n=0,(0|s)>0)for(;r=v[b+4>>2],v[b+4>>2]=r+1,C=l[0|r],v[b+4>>2]=r+2,R=l[r+1|0],v[b+4>>2]=r+3,K=l[r+2|0],v[b+4>>2]=r+4,L=l[r+3|0],v[b+4>>2]=r+5,z=l[r+4|0],v[b+4>>2]=r+6,S=l[r+5|0],v[b+4>>2]=r+7,P=l[r+6|0],v[b+4>>2]=r+8,_=l[r+7|0],v[b+4>>2]=r+9,M=l[r+8|0],v[b+4>>2]=r+10,J=l[r+9|0],v[b+4>>2]=r+11,x=l[r+10|0],v[b+4>>2]=r+12,B=l[r+11|0],v[b+4>>2]=r+13,q=l[r+12|0],v[b+4>>2]=r+14,D=l[r+13|0],v[b+4>>2]=r+15,N=l[r+14|0],v[b+4>>2]=r+16,Z=l[r+15|0],v[b+4>>2]=r+17,Y=l[r+16|0],v[b+4>>2]=r+18,X=l[r+17|0],v[b+4>>2]=r+19,fr=l[r+18|0],v[b+4>>2]=r+20,Lf(h,n,(u(2,L|(K|R<<8|C<<16)<<8),k()),(u(2,_|(P|S<<8|z<<16)<<8),k()),(u(2,B|(x|J<<8|M<<16)<<8),k()),(u(2,Z|(N|D<<8|q<<16)<<8),k()),(u(2,l[r+19|0]|(X<<8|Y<<16|fr)<<8),k())),(0|n)<(0|g)&&Lr(b,n,h),(0|s)!=(0|(n=n+1|0)););if(v[y+32>>2]=h,Un(y+48|0,y+32|0),O=(A=p[v[h+32>>2]+(m(g,I)<<2)>>2])<O?O:A,(0|T)==(0|(f=f+1|0)))break}if((0|(R=jn(b,1)))>0)for(T=0,K=v[2493],L=v[2485];;){if(h=jn(b,1),I=v[v[e+144>>2]+(h<<2)>>2],(0|(z=jn(b,1)))>0)for(g=0;;){r=v[b+4>>2],v[b+4>>2]=r+1,r=i[0|r],f=jn(b,1);e:{f:switch(0|r){case 0:case 1:if(s=ut(40),1!=(0|r)?(tf(s,f),r=!v[I+44>>2]):(Ut(s,f),r=d[I+48>>2]<2),v[s+36>>2]=h,C=f-1|0,n=0,(0|f)>0)for(A=r?rr:w(1);r=v[b+4>>2],v[b+4>>2]=r+1,S=l[0|r],v[b+4>>2]=r+2,P=l[r+1|0],v[b+4>>2]=r+3,_=l[r+2|0],v[b+4>>2]=r+4,M=l[r+3|0],v[b+4>>2]=r+5,J=l[r+4|0],v[b+4>>2]=r+6,x=l[r+5|0],v[b+4>>2]=r+7,B=l[r+6|0],v[b+4>>2]=r+8,qi(s,n,(u(2,M|(_|P<<8|S<<16)<<8),k()),w(A*(u(2,l[r+7|0]|(B|x<<8|J<<16)<<8),k()))),(0|n)<(0|C)&&Lr(b,n,s),(0|f)!=(0|(n=n+1|0)););v[y+32>>2]=s,Un(y+48|0,y+32|0),O=(A=p[v[s+32>>2]+(m(C,K)<<2)>>2])<O?O:A;break e;case 2:break f;default:break e}if(s=Go(ut(40),f),v[s+36>>2]=h,C=f-1|0,n=0,(0|f)>0)for(;r=v[b+4>>2],v[b+4>>2]=r+1,S=l[0|r],v[b+4>>2]=r+2,P=l[r+1|0],v[b+4>>2]=r+3,_=l[r+2|0],v[b+4>>2]=r+4,M=l[r+3|0],v[b+4>>2]=r+5,J=l[r+4|0],v[b+4>>2]=r+6,x=l[r+5|0],v[b+4>>2]=r+7,B=l[r+6|0],v[b+4>>2]=r+8,q=l[r+7|0],v[b+4>>2]=r+9,D=l[r+8|0],v[b+4>>2]=r+10,N=l[r+9|0],v[b+4>>2]=r+11,Z=l[r+10|0],v[b+4>>2]=r+12,Wi(s,n,(u(2,M|(_|P<<8|S<<16)<<8),k()),(u(2,q|(B|x<<8|J<<16)<<8),k()),(u(2,l[r+11|0]|(Z|N<<8|D<<16)<<8),k())),(0|n)<(0|C)&&Lr(b,n,s),(0|f)!=(0|(n=n+1|0)););v[y+32>>2]=s,Un(y+48|0,y+32|0),O=(A=p[v[s+32>>2]+(m(C,L)<<2)>>2])<O?O:A}if((0|z)==(0|(g=g+1|0)))break}if((0|R)==(0|(T=T+1|0)))break}e:if(!((0|(S=jn(b,1)))<=0))for(P=1,f=0;;){if(r=jn(b,1),J=v[v[e+60>>2]+(r<<2)>>2],T=0,(0|(x=jn(b,1)))>0)for(;;){if(g=0,_=jn(b,1),(0|(B=jn(b,1)))>0)for(;;){if(r=0,(n=jn(b,1))&&(r=v[(v[e+200>>2]+(n<<2)|0)-4>>2]),I=Pt(J,_,n=ht(y+32|0,r,0)),gi(n),!I){if(Rn(y+48|0),oe($,8349,r),r=0,P)break n;break e}if(r=v[I+40>>2],R=(M=v[I+24>>2])?(r>>>0)/3<<1:r,K=jn(b,1),C=Sr(ut(60),K),v[C+56>>2]=I,v[C+20>>2]=_,K)for(q=I+36|0,D=R<<2,z=K-1|0,h=0;;){r=v[b+4>>2],v[b+4>>2]=r+1,n=l[0|r],v[b+4>>2]=r+2,s=l[r+1|0],v[b+4>>2]=r+3,L=l[r+2|0],v[b+4>>2]=r+4,r=l[r+3|0],v[y+44>>2]=0,v[y+36>>2]=0,v[y+40>>2]=0,v[y+32>>2]=8776,u(2,r|(L|s<<8|n<<16)<<8),A=k();f:if(r=jn(b,1)){v[y+16>>2]=0,wn(y+32|0,R,y+16|0),L=r+(n=jn(b,1))|0;i:if(rr==w(1)){if(!(n>>>0>=L>>>0))for(r=v[b+4>>2],N=v[y+44>>2];v[b+4>>2]=r+1,Z=l[0|r],v[b+4>>2]=r+2,Y=l[r+1|0],v[b+4>>2]=r+3,X=l[r+2|0],s=r+4|0,v[b+4>>2]=s,v[N+(n<<2)>>2]=l[r+3|0]|(X|Y<<8|Z<<16)<<8,r=s,(0|L)!=(0|(n=n+1|0)););}else{if(n>>>0>=L>>>0)break i;for(r=v[b+4>>2],N=v[y+44>>2];v[b+4>>2]=r+1,Z=l[0|r],v[b+4>>2]=r+2,Y=l[r+1|0],v[b+4>>2]=r+3,X=l[r+2|0],s=r+4|0,v[b+4>>2]=s,p[N+(n<<2)>>2]=rr*(u(2,l[r+3|0]|(X|Y<<8|Z<<16)<<8),k()),r=s,(0|L)!=(0|(n=n+1|0)););}if(!M&&(n=v[y+36>>2]))for(s=v[I+48>>2],r=0,L=v[y+44>>2];p[(Z=(N=r<<2)+L|0)>>2]=p[s+N>>2]+p[Z>>2],(0|n)!=(0|(r=r+1|0)););}else{if(M){if(v[y+16>>2]=0,wn(y+32|0,R,y+16|0),!R)break f;zi(v[y+44>>2],0,D);break f}v[y+36>>2]=0,Df(y+32|0,q)}if(Zi(C,h,A,y+32|0),h>>>0<z>>>0&&Lr(b,h,C),To(y+32|0),(0|K)==(0|(h=h+1|0)))break}else z=-1;if(v[y+32>>2]=C,Un(y+48|0,y+32|0),O=(A=p[v[C+36>>2]+(z<<2)>>2])<O?O:A,(0|B)==(0|(g=g+1|0)))break}if((0|x)==(0|(T=T+1|0)))break}if(P=(0|S)>(0|(f=f+1|0)),(0|f)==(0|S))break}if(T=jn(b,1)){for(I=Hr(ut(36),T),R=(f=v[e+36>>2])<<2,h=0;;){if(r=v[b+4>>2],v[b+4>>2]=r+1,K=l[0|r],v[b+4>>2]=r+2,L=l[r+1|0],v[b+4>>2]=r+3,z=l[r+2|0],v[b+4>>2]=r+4,S=l[r+3|0],C=jn(b,1),v[y+44>>2]=0,v[y+36>>2]=0,v[y+40>>2]=0,v[y+32>>2]=8744,v[y+16>>2]=0,gn(y+32|0,f,y+16|0),(P=(0|f)<=0)||zi(v[y+44>>2],255,R),r=0,v[y+28>>2]=0,v[y+20>>2]=0,v[y+24>>2]=0,v[y+16>>2]=8744,v[y+12>>2]=0,gn(y+16|0,f-C|0,y+12|0),n=0,C)for(g=0,_=v[y+44>>2],M=v[y+28>>2];;){if((0|(s=jn(b,1)))!=(0|r)){for(;v[M+(n<<2)>>2]=r,n=n+1|0,(0|s)!=(0|(r=r+1|0)););r=s}if(ir=_+(jn(b,1)+r<<2)|0,tr=r,v[ir>>2]=tr,r=r+1|0,(0|C)==(0|(g=g+1|0)))break}if(r>>>0<f>>>0)for(s=v[y+28>>2];v[s+(n<<2)>>2]=r,n=n+1|0,(0|f)!=(0|(r=r+1|0)););if(!P)for(g=v[y+28>>2],C=v[y+44>>2],r=f;-1==v[(P=C+((s=r-1|0)<<2)|0)>>2]&&(n=n-1|0,v[P>>2]=v[g+(n<<2)>>2]),P=r>>>0>1,r=s,P;);if(r=y+32|0,Ce(I,h,(u(2,S|(z|L<<8|K<<16)<<8),k()),r),oo(y+16|0),oo(r),(0|T)==(0|(h=h+1|0)))break}v[y+32>>2]=I,Un(y+48|0,y+32|0),O=(A=p[(v[I+16>>2]+(T<<2)|0)-4>>2])<O?O:A}if((0|(f=jn(b,1)))>0){for(s=de(ut(36),f),h=0;r=v[b+4>>2],v[b+4>>2]=r+1,n=l[0|r],v[b+4>>2]=r+2,T=l[r+1|0],v[b+4>>2]=r+3,I=l[r+2|0],v[b+4>>2]=r+4,r=l[r+3|0],g=jn(b,1),g=v[v[e+80>>2]+(g<<2)>>2],ir=n=ii(ut(40),(u(2,r|(I|T<<8|n<<16)<<8),k()),g),tr=jn(b,0),v[ir+12>>2]=tr,r=v[b+4>>2],v[b+4>>2]=r+1,T=l[0|r],v[b+4>>2]=r+2,I=l[r+1|0],v[b+4>>2]=r+3,C=l[r+2|0],v[b+4>>2]=r+4,v[n+16>>2]=l[r+3|0]|(C|I<<8|T<<16)<<8,v[b+4>>2]=r+5,te(I=n+20|0,T=ht(T=y+32|0,r=(C=l[r+4|0])?$f(b):v[g+32>>2],0)),gi(T),C&&(T=v[5280],Ta[v[v[T>>2]+20>>2]](T,r,8590,1028)),v[g+40>>2]&&(r=v[b+4>>2],v[b+4>>2]=r+1,g=l[0|r],v[b+4>>2]=r+2,T=l[r+1|0],v[b+4>>2]=r+3,I=l[r+2|0],v[b+4>>2]=r+4,v[n+32>>2]=l[r+3|0]|(I|T<<8|g<<16)<<8,v[b+4>>2]=r+5,g=l[r+4|0],v[b+4>>2]=r+6,T=l[r+5|0],v[b+4>>2]=r+7,I=l[r+6|0],v[b+4>>2]=r+8,v[n+36>>2]=l[r+7|0]|(I|T<<8|g<<16)<<8),Ni(s,h,n),(0|f)!=(0|(h=h+1|0)););v[y+32>>2]=s,Un(y+48|0,y+32|0),O=(A=p[(v[s+16>>2]+(f<<2)|0)-4>>2])<O?O:A}r=qr(r=ut(48),n=Ue(y+32|0,er),y+48|0,O),gi(n)}if(so(y+48|0),V=y- -64|0,!r){Ta[v[v[b>>2]+4>>2]](b),e&&Ta[v[v[e>>2]+4>>2]](e),gi(er),e=0;break r}if(v[v[e+96>>2]+(F<<2)>>2]=r,gi(er),(0|nr)==(0|(F=F+1|0)))break}Ta[v[v[b>>2]+4>>2]](b)}return V=t+16|0,v[$+28>>2]&&(v[a>>2]=v[$+32>>2],H(20831,17040,0|a)),hr(e,r=o+48|0,n=o+32|0),hf($),V=a+48|0,be(n),be(r),V=o- -64|0,0|e},function(r,n,e){var f,i;r|=0,V=f=V-16|0,i=n|=0,n=$e(f+4|0,4+(e|=0)|0,v[e>>2],0),Ta[0|r](i,n),gi(n),V=f+16|0},function(r,n){r|=0,n|=0;var e,f=0;V=e=V-16|0,v[e+12>>2]=r;r:{if(r=v[5440])for(;;){if(ei(r+4|0,n))break r;if(!(r=v[r+20>>2]))break}n:{e:{if(r=v[5440])for(;;){if(ei(f=r+4|0,n))break e;if(!(r=v[r+20>>2]))break}r=ut(28),v[r>>2]=17132,f=Tt(r+4|0),v[r+20>>2]=0,v[r+24>>2]=0,te(f,n),v[r+16>>2]=v[e+12>>2],(n=v[5440])&&(v[n+24>>2]=r,v[r+20>>2]=n),v[5440]=r,v[5441]=v[5441]+1;break n}te(f,n),v[r+16>>2]=v[e+12>>2]}}V=e+16|0},function(r,n){var e;r|=0,V=e=V-16|0,n=$e(e+4|0,4+(n|=0)|0,v[n>>2],0),Ta[0|r](n),gi(n),V=e+16|0},function(r){r|=0;var n=0,e=0;r:if(n=v[5440]){for(;;){if(!ei(n+4|0,r)){if(n=v[n+20>>2])continue;break r}break}n:if(n=v[5440]){for(;;){if(!ei(n+4|0,r)){if(n=v[n+20>>2])continue;break n}break}(n=v[n+16>>2])&&Ta[v[v[n>>2]+4>>2]](n)}n:if(n=v[5440]){for(;;){if(!ei(n+4|0,r)){if(n=v[n+20>>2])continue;break n}break}r=v[n+24>>2],e=v[n+20>>2],v[(r?r+20:21760)>>2]=e,e&&(v[e+24>>2]=r),Ta[v[v[n>>2]+4>>2]](n),v[5441]=v[5441]-1}}},function(r,n){n|=0,Ta[0|(r|=0)](n)},lu,$o,function(){return v[5442]},$o,function(){return v[5197]},function(){return v[5443]},$o,function(){return v[5444]},Y,Nr,Uu,function(r){pt(Uu(r|=0))},Lu,function(r){pt(Lu(r|=0))},ju,function(r){pt(ju(r|=0))},fo,function(r){pt(fo(r|=0))},lu,Bf,function(r){Bf(r|=0),ar(r)},ta,pa,function(r,n){r|=0;var e,f=0,i=0,t=0,u=0,o=0,a=0,c=0,b=0,k=0;if(V=e=V-16|0,Fu(0|Ta[v[v[(n|=0)>>2]+8>>2]](n),21296)){for(r=v[n+24>>2],Zn(i=Wt(32),4,20776,6,v[r+4>>2]+16|0),u=v[n+112>>2],a=v[v[i+4>>2]>>2],r=0;o=a+m(r,24)|0,t=f<<2,p[o+12>>2]=p[t+u>>2],p[o+16>>2]=p[u+(4|t)>>2],f=f+2|0,4!=(0|(r=r+1|0)););(f=v[n+28>>2])&&(!(r=v[n+24>>2])|(0|r)==(0|i)||Ta[0|f](r)),v[n+28>>2]=1699,v[n+24>>2]=i}else if(Fu(0|Ta[v[v[n>>2]+8>>2]](n),21200)){if(r=v[n+68>>2],Zn(i=Wt(32),v[n+52>>2]>>>1|0,v[n+148>>2],v[n+140>>2],v[r+4>>2]+16|0),r=v[n+52>>2])for(a=v[v[i+4>>2]>>2],o=r-1>>>1|0,u=v[n+116>>2],r=0;t=a+m(r,24)|0,c=f<<2,p[t+12>>2]=p[u+c>>2],p[t+16>>2]=p[u+(4|c)>>2],f=f+2|0,t=(0|r)==(0|o),r=r+1|0,!t;);(f=v[n+72>>2])&&(!(r=v[n+68>>2])|(0|r)==(0|i)||Ta[0|f](r)),v[n+72>>2]=1699,v[n+68>>2]=i}else b=e,k=v[na(n)+8>>2],v[b>>2]=k,H(20796,17040,0|e);V=e+16|0},function(r,n,e,f){n|=0,e|=0,f|=0,r=v[8+(r|=0)>>2],v[5443]=e,v[5197]=n,v[5444]=f,(r=v[r+36>>2])&&(v[5442]=r,q())},function(r,n,e,f){n|=0,e|=0,f|=0,(r=v[8+(r|=0)>>2])&&(v[e+8>>2]&&(r=v[r+40>>2],v[5197]=n,v[5442]=r,v[5443]=e,v[5444]=f,N()),3==(0|n)&&(r=v[e+8>>2])&&((n=v[e+12>>2])&&Ta[0|n](r),v[e+8>>2]=0,v[e+12>>2]=0))},function(){gi(21744)},zu,function(r){pt(zu(r|=0))},function(r){return v[(r|=0)>>2]=17068,gi(r+8|0),0|r},function(r){v[(r|=0)>>2]=17068,gi(r+8|0),pt(r)},function(){xu(21756)},Af,function(r){pt(Af(r|=0))},function(r){return v[(r|=0)>>2]=17100,gi(r+16|0),gi(r+4|0),0|r},function(r){v[(r|=0)>>2]=17100,gi(r+16|0),gi(r+4|0),pt(r)},xu,function(r){pt(xu(r|=0))},function(r){return v[(r|=0)>>2]=17132,gi(r+4|0),0|r},function(r){v[(r|=0)>>2]=17132,gi(r+4|0),pt(r)},ta,wa,function(r,n){return(n|=0)?0|X(n):0},function(r,n){return r|=0,(n|=0)?(!(r=X(n))|!(3&l[r-4|0])||zi(r,0,n),0|r):0},function(r,n,e,f,i){r|=0,n|=0,f|=0,i|=0;var t=0,u=0,o=0,a=0,c=0,b=0,k=0,s=0,l=0,h=0;if(!(e|=0))return ar(n),0;if(n)if(e>>>0>=4294967232)v[5446]=48,i=0;else{t=e>>>0<11?16:e+11&-8,r=0,i=-8&(a=v[4+(f=n-8|0)>>2]);r:if(3&a){u=f+i|0;n:if(i>>>0>=t>>>0){if((r=i-t|0)>>>0<16)break n;v[f+4>>2]=1&a|t|2,v[4+(i=f+t|0)>>2]=3|r,v[u+4>>2]=1|v[u+4>>2],cr(i,r)}else if(v[5453]!=(0|u))if(v[5452]!=(0|u)){if(2&(o=v[u+4>>2]))break r;if((c=i+(-8&o)|0)>>>0<t>>>0)break r;k=c-t|0;e:if(o>>>0<=255){if((0|(r=v[u+12>>2]))==(0|(i=v[u+8>>2]))){l=21788,h=v[5447]&ot(o>>>3|0),v[l>>2]=h;break e}v[i+12>>2]=r,v[r+8>>2]=i}else{b=v[u+24>>2];f:if((0|u)==(0|(i=v[u+12>>2]))){i:{if(!(o=v[(r=u+20|0)>>2])){if(!(o=v[u+16>>2]))break i;r=u+16|0}for(;s=r,(o=v[(r=(i=o)+20|0)>>2])||(r=i+16|0,o=v[i+16>>2]););v[s>>2]=0;break f}i=0}else r=v[u+8>>2],v[r+12>>2]=i,v[i+8>>2]=r;if(b){r=v[u+28>>2];f:{if(v[(o=22092+(r<<2)|0)>>2]==(0|u)){if(v[o>>2]=i,i)break f;l=21792,h=v[5448]&ot(r),v[l>>2]=h;break e}if(v[(v[b+16>>2]==(0|u)?16:20)+b>>2]=i,!i)break e}v[i+24>>2]=b,(r=v[u+16>>2])&&(v[i+16>>2]=r,v[r+24>>2]=i),(r=v[u+20>>2])&&(v[i+20>>2]=r,v[r+24>>2]=i)}}k>>>0<=15?(v[f+4>>2]=1&a|c|2,v[4+(r=f+c|0)>>2]=1|v[r+4>>2]):(v[f+4>>2]=1&a|t|2,v[4+(r=f+t|0)>>2]=3|k,v[4+(i=f+c|0)>>2]=1|v[i+4>>2],cr(r,k))}else{if((i=i+v[5449]|0)>>>0<t>>>0)break r;(r=i-t|0)>>>0>=16?(v[f+4>>2]=1&a|t|2,v[4+(o=f+t|0)>>2]=1|r,v[(i=f+i|0)>>2]=r,v[i+4>>2]=-2&v[i+4>>2]):(v[f+4>>2]=i|1&a|2,v[4+(r=f+i|0)>>2]=1|v[r+4>>2],r=0),v[5452]=o,v[5449]=r}else{if((i=i+v[5450]|0)>>>0<=t>>>0)break r;v[f+4>>2]=1&a|t|2,i=i-t|0,v[4+(r=f+t|0)>>2]=1|i,v[5450]=i,v[5453]=r}r=f}else{if(t>>>0<256)break r;if(i>>>0>=t+4>>>0&&(r=f,i-t>>>0<=v[5567]<<1>>>0))break r;r=0}i=r+8|0,r||(i=0,(f=X(e))&&(Ri(f,n,(r=(3&(r=v[n-4>>2])?-4:-8)+(-8&r)|0)>>>0<e>>>0?r:e),ar(n),i=f))}else i=X(e);return 0|i},function(r,n){ar(n|=0)},function(){return 0},In],Aa.set=function(r,n){this[r]=n},Aa.get=function(r){return this[r]},Aa);function $a(){return f.byteLength/65536|0}return{z:function(){jt(20952,3662,20976),Nt(20964,2042),Nt(20976,3667),jt(20988,5464,21368),jt(21e3,5364,21440),jt(21012,1855,21452),jt(21024,2017,21452),jt(21036,5498,21072),jt(21048,1749,21440),Nt(21060,6413),jt(21072,5620,21368),jt(21084,5575,21072),jt(21096,5512,21368),jt(21108,5450,21368),jt(21124,1728,21440),jt(21136,6392,21060),jt(21148,5429,21072),Nt(21164,4305),jt(21176,4282,21164),jt(21188,4299,21164),jt(21200,1999,21452),jt(21212,1981,21452),jt(21224,1745,21440),jt(21236,6409,21060),jt(21248,5375,21072),jt(21260,5544,21072),jt(21272,5590,21260),jt(21284,1897,20964),jt(21296,1944,20964),jt(21308,5634,21072),jt(21320,5667,21416),jt(21332,5530,21416),Nt(21368,5672),jt(21380,1704,21440),jt(21392,6368,21060),jt(21404,5401,21072),jt(21416,5649,21072),jt(21428,5495,21072),Nt(21440,5833),jt(21452,1877,20964),Nt(21468,2295),jt(21480,2271,21468),jt(21492,2290,21468),v[5376]=1689,v[5377]=0,Y(),Pu(21504),v[5378]=1690,v[5379]=0,Nr(),Pu(21512),v[5431]=0,v[5432]=0,v[5435]=0,v[5433]=0,v[5434]=0,Tt(21744),v[5440]=0,v[5441]=0,v[5439]=17116,v[5572]=1728,v[5573]=0,In(),v[5573]=v[5571],v[5571]=22288},A:Ta,B:X,C:ar,D:function(r){r|=0;var n,e=0,f=0,t=0,u=0;for(V=n=V-32|0,v[n+16>>2]=v[4388],e=v[4387],v[n+8>>2]=v[4386],v[n+12>>2]=e,e=v[4385],v[n>>2]=v[4384],v[n+4>>2]=e,f=7,t=2,u=1;(u&=!(f=r>>>((e=f)<<2)&15)&!!(0|e))||(i[n+t|0]=l[f+17504|0],t=t+1|0),f=e-1|0,e;);return i[n+t|0]=0,r=(e=X(r=ee(n)+1|0))?Ri(e,n,r):0,V=n+32|0,0|r},E:function(){var r=0;if(r=v[5571])for(;Ta[v[r>>2]](),r=v[r+4>>2];);}}}(r)}(r)}function s(r){return{then:function(n){n({instance:new k(r)})}}}Object.assign(e,u),u=null,e.wasmBinary&&(c=e.wasmBinary),e.noExitRuntime;var v=Error,l={};c=[],"object"!=typeof l&&H("no native wasm support detected");var h,d,p,y,m,w,g,F,A,T=!1;function $(){var r=h.buffer;e.HEAP8=d=new Int8Array(r),e.HEAP16=y=new Int16Array(r),e.HEAP32=w=new Int32Array(r),e.HEAPU8=p=new Uint8Array(r),e.HEAPU16=m=new Uint16Array(r),e.HEAPU32=g=new Uint32Array(r),e.HEAPF32=F=new Float32Array(r),e.HEAPF64=A=new Float64Array(r)}var I=e.INITIAL_MEMORY||33554432;65536<=I||H("INITIAL_MEMORY should be larger than STACK_SIZE, was "+I+"! (STACK_SIZE=65536)"),h=e.wasmMemory?e.wasmMemory:new function(){this.buffer=new ArrayBuffer(I/65536*65536)},$(),I=h.buffer.byteLength;var C,P=[],E=[],O=[];function R(){var r=e.preRun.shift();P.unshift(r)}var S,W=0,G=null;function U(){W++,e.monitorRunDependencies&&e.monitorRunDependencies(W)}function j(){if(W--,e.monitorRunDependencies&&e.monitorRunDependencies(W),0==W&&G){var r=G;G=null,r()}}function H(r){throw e.onAbort&&e.onAbort(r),b(r="Aborted("+r+")"),T=!0,r=new v(r+". Build with -sASSERTIONS for more info."),i(r),r}function L(r){return r.startsWith("data:application/octet-stream;base64,")}function M(r){try{if(r==S&&c)return new Uint8Array(c);throw"both async and sync fetching of the wasm failed"}catch(r){H(r)}}function _(r){return c||"function"!=typeof fetch?Promise.resolve().then((()=>M(r))):fetch(r,{credentials:"same-origin"}).then((n=>{if(!n.ok)throw"failed to load wasm binary file at '"+r+"'";return n.arrayBuffer()})).catch((()=>M(r)))}function z(r,n,e){return _(r).then((()=>s(n))).then((r=>r)).then(e,(r=>{b("failed to asynchronously prepare wasm: "+r),H(r)}))}function x(r,n){var e=S;return c||"function"!=typeof l.instantiateStreaming||L(e)||"function"!=typeof fetch?z(e,r,n):fetch(e,{credentials:"same-origin"}).then((f=>l.instantiateStreaming(f,r).then(n,(function(f){return b("wasm streaming compile failed: "+f),b("falling back to ArrayBuffer instantiation"),z(e,r,n)}))))}L(S="spine.wasm")||(S=a(S));var J="spine.js.mem";function K(){L(J)||(J=a(J)),U();var r=r=>{r.byteLength&&(r=new Uint8Array(r)),p.set(r,1024),e.memoryInitializerRequest&&delete e.memoryInitializerRequest.response,j()},n=()=>{t(r,(()=>{i(Error("could not load memory initializer "+J))}))};if(e.memoryInitializerRequest){var f=()=>{var f=e.memoryInitializerRequest,i=f.response;200!==f.status&&0!==f.status?(console.warn("a problem seems to have happened with Module.memoryInitializerRequest, status: "+f.status+", retrying "+J),n()):r(i)};e.memoryInitializerRequest.response?setTimeout(f,0):e.memoryInitializerRequest.addEventListener("load",f)}else n()}var B={20796:r=>{console.log(r?Vr(r):"")},20831:r=>{console.warn("[Spine]",r?Vr(r):"")}};function N(r){for(;0<r.length;)r.shift()(e)}function q(r){switch(r){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError(`Unknown type size: ${r}`)}}var D=void 0;function V(r){for(var n="";p[r];)n+=D[p[r++]];return n}var Z={},Y={},X={};function Q(r){if(void 0===r)return"_unknown";var n=(r=r.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return 48<=n&&57>=n?`_${r}`:r}function rr(r,n){return r=Q(r),{[r]:function(){return n.apply(this,arguments)}}[r]}function nr(r){var n=Error,e=rr(r,(function(n){this.name=r,this.message=n,void 0!==(n=Error(n).stack)&&(this.stack=this.toString()+"\n"+n.replace(/^Error(:[^\n]*)?\n/,""))}));return e.prototype=Object.create(n.prototype),e.prototype.constructor=e,e.prototype.toString=function(){return void 0===this.message?this.name:`${this.name}: ${this.message}`},e}var er=void 0;function fr(r){throw new er(r)}var ir=void 0;function tr(r){throw new ir(r)}function ur(r,n,e){function f(n){(n=e(n)).length!==r.length&&tr("Mismatched type converter count");for(var f=0;f<r.length;++f)or(r[f],n[f])}r.forEach((function(r){X[r]=n}));var i=Array(n.length),t=[],u=0;n.forEach(((r,n)=>{Y.hasOwnProperty(r)?i[n]=Y[r]:(t.push(r),Z.hasOwnProperty(r)||(Z[r]=[]),Z[r].push((()=>{i[n]=Y[r],++u===t.length&&f(i)})))})),0===t.length&&f(i)}function or(r,n){if(!("argPackAdvance"in n))throw new TypeError("registerType registeredInstance requires argPackAdvance");var e=n.name;if(r||fr(`type "${e}" must have a positive integer typeid pointer`),Y.hasOwnProperty(r)){if({}.qa)return;fr(`Cannot register type '${e}' twice`)}Y[r]=n,delete X[r],Z.hasOwnProperty(r)&&(n=Z[r],delete Z[r],n.forEach((r=>r())))}function ar(r){fr(r.F.I.G.name+" instance already deleted")}var cr=!1;function br(){}function kr(r){--r.count.value,0===r.count.value&&(r.L?r.M.S(r.L):r.I.G.S(r.H))}function sr(r,n,e){return n===e?r:void 0===e.J||null===(r=sr(r,n,e.J))?null:e.ha(r)}var vr={},lr=[];function hr(){for(;lr.length;){var r=lr.pop();r.F.U=!1,r.delete()}}var dr=void 0,pr={};function yr(r,n){for(void 0===n&&fr("ptr should not be undefined");r.J;)n=r.W(n),r=r.J;return pr[n]}function mr(r,n){return n.I&&n.H||tr("makeClassHandle requires ptr and ptrType"),!!n.M!=!!n.L&&tr("Both smartPtrType and smartPtr must be specified"),n.count={value:1},wr(Object.create(r,{F:{value:n}}))}function wr(r){return"undefined"==typeof FinalizationRegistry?(wr=r=>r,r):(cr=new FinalizationRegistry((r=>{kr(r.F)})),br=r=>{cr.unregister(r)},(wr=r=>{var n=r.F;return n.L&&cr.register(r,{F:n},r),r})(r))}function gr(){}function Fr(r,n,e){if(void 0===r[n].K){var f=r[n];r[n]=function(){return r[n].K.hasOwnProperty(arguments.length)||fr(`Function '${e}' called with an invalid number of arguments (${arguments.length}) - expects one of (${r[n].K})!`),r[n].K[arguments.length].apply(this,arguments)},r[n].K=[],r[n].K[f.T]=f}}function Ar(r,n){e.hasOwnProperty(r)?(fr(`Cannot register public name '${r}' twice`),Fr(e,r,r),e.hasOwnProperty(void 0)&&fr("Cannot register multiple overloads of a function with the same number of arguments (undefined)!"),e[r].K[void 0]=n):e[r]=n}function Tr(r,n,e,f,i,t,u,o){this.name=r,this.constructor=n,this.P=e,this.S=f,this.J=i,this.ia=t,this.W=u,this.ha=o,this.la=[]}function $r(r,n,e){for(;n!==e;)n.W||fr(`Expected null or instance of ${e.name}, got an instance of ${n.name}`),r=n.W(r),n=n.J;return r}function Ir(r,n){return null===n?(this.$&&fr(`null is not a valid ${this.name}`),0):(n.F||fr(`Cannot pass "${Br(n)}" as a ${this.name}`),n.F.H||fr(`Cannot pass deleted object as a pointer of type ${this.name}`),$r(n.F.H,n.F.I.G,this.G))}function Cr(r,n){if(null===n){if(this.$&&fr(`null is not a valid ${this.name}`),this.Z){var e=this.ma();return null!==r&&r.push(this.S,e),e}return 0}if(n.F||fr(`Cannot pass "${Br(n)}" as a ${this.name}`),n.F.H||fr(`Cannot pass deleted object as a pointer of type ${this.name}`),!this.Y&&n.F.I.Y&&fr(`Cannot convert argument of type ${n.F.M?n.F.M.name:n.F.I.name} to parameter type ${this.name}`),e=$r(n.F.H,n.F.I.G,this.G),this.Z)switch(void 0===n.F.L&&fr("Passing raw pointer to smart pointer is illegal"),this.pa){case 0:n.F.M===this?e=n.F.L:fr(`Cannot convert argument of type ${n.F.M?n.F.M.name:n.F.I.name} to parameter type ${this.name}`);break;case 1:e=n.F.L;break;case 2:if(n.F.M===this)e=n.F.L;else{var f=n.clone();e=this.na(e,Kr((function(){f.delete()}))),null!==r&&r.push(this.S,e)}break;default:fr("Unsupporting sharing policy")}return e}function Pr(r,n){return null===n?(this.$&&fr(`null is not a valid ${this.name}`),0):(n.F||fr(`Cannot pass "${Br(n)}" as a ${this.name}`),n.F.H||fr(`Cannot pass deleted object as a pointer of type ${this.name}`),n.F.I.Y&&fr(`Cannot convert argument of type ${n.F.I.name} to parameter type ${this.name}`),$r(n.F.H,n.F.I.G,this.G))}function Er(r){return this.fromWireType(w[r>>2])}function Or(r,n,e,f){this.name=r,this.G=n,this.$=e,this.Y=f,this.Z=!1,this.S=this.na=this.ma=this.da=this.pa=this.ka=void 0,void 0!==n.J?this.toWireType=Cr:(this.toWireType=f?Ir:Pr,this.O=null)}function Rr(r,n){e.hasOwnProperty(r)||tr("Replacing nonexistant public symbol"),e[r]=n,e[r].T=void 0}function Sr(r,n){var f=[];return function(){if(f.length=0,Object.assign(f,arguments),r.includes("j")){var i=e["dynCall_"+r];i=f.length?i.apply(null,[n].concat(f)):i.call(null,n)}else i=C.get(n).apply(null,f);return i}}function Wr(r,n){var e=(r=V(r)).includes("j")?Sr(r,n):C.get(n);return"function"!=typeof e&&fr(`unknown function pointer with signature ${r}: ${n}`),e}var Gr=void 0;function Ur(r){var n=V(r=an(r));return on(r),n}function jr(r,n){var e=[],f={};throw n.forEach((function r(n){f[n]||Y[n]||(X[n]?X[n].forEach(r):(e.push(n),f[n]=!0))})),new Gr(`${r}: `+e.map(Ur).join([", "]))}function Hr(r){for(;r.length;){var n=r.pop();r.pop()(n)}}function Lr(r,n,e,f,i){var t=n.length;2>t&&fr("argTypes array size mismatch! Must at least get return value and 'this' types!");var u=null!==n[1]&&null!==e,o=!1;for(e=1;e<n.length;++e)if(null!==n[e]&&void 0===n[e].O){o=!0;break}var a="void"!==n[0].name,c=t-2,b=Array(c),k=[],s=[];return function(){if(arguments.length!==c&&fr(`function ${r} called with ${arguments.length} arguments, expected ${c} args!`),s.length=0,k.length=u?2:1,k[0]=i,u){var e=n[1].toWireType(s,this);k[1]=e}for(var t=0;t<c;++t)b[t]=n[t+2].toWireType(s,arguments[t]),k.push(b[t]);if(t=f.apply(null,k),o)Hr(s);else for(var v=u?1:2;v<n.length;v++){var l=1===v?e:b[v-2];null!==n[v].O&&n[v].O(l)}return a?n[0].fromWireType(t):void 0}}function Mr(r,n){for(var e=[],f=0;f<r;f++)e.push(g[n+4*f>>2]);return e}function _r(r,n,e){return r instanceof Object||fr(`${e} with invalid "this": ${r}`),r instanceof n.G.constructor||fr(`${e} incompatible with "this" of type ${r.constructor.name}`),r.F.H||fr(`cannot call emscripten binding method ${e} on deleted object`),$r(r.F.H,r.F.I.G,n.G)}var zr=new function(){this.N=[void 0],this.aa=[],this.get=function(r){return this.N[r]},this.has=function(r){return void 0!==this.N[r]},this.ea=function(r){var n=this.aa.pop()||this.N.length;return this.N[n]=r,n},this.fa=function(r){this.N[r]=void 0,this.aa.push(r)}};function xr(r){r>=zr.ba&&0==--zr.get(r).oa&&zr.fa(r)}var Jr=r=>(r||fr("Cannot use deleted val. handle = "+r),zr.get(r).value),Kr=r=>{switch(r){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:return zr.ea({oa:1,value:r})}};function Br(r){if(null===r)return"null";var n=typeof r;return"object"===n||"array"===n||"function"===n?r.toString():""+r}function Nr(r,n){switch(n){case 2:return function(r){return this.fromWireType(F[r>>2])};case 3:return function(r){return this.fromWireType(A[r>>3])};default:throw new TypeError("Unknown float type: "+r)}}function qr(r,n,e){switch(n){case 0:return e?function(r){return d[r]}:function(r){return p[r]};case 1:return e?function(r){return y[r>>1]}:function(r){return m[r>>1]};case 2:return e?function(r){return w[r>>2]}:function(r){return g[r>>2]};default:throw new TypeError("Unknown integer type: "+r)}}var Dr="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function Vr(r,n){var e=p,f=r+n;for(n=r;e[n]&&!(n>=f);)++n;if(16<n-r&&e.buffer&&Dr)return Dr.decode(e.subarray(r,n));for(f="";r<n;){var i=e[r++];if(128&i){var t=63&e[r++];if(192==(224&i))f+=String.fromCharCode((31&i)<<6|t);else{var u=63&e[r++];65536>(i=224==(240&i)?(15&i)<<12|t<<6|u:(7&i)<<18|t<<12|u<<6|63&e[r++])?f+=String.fromCharCode(i):(i-=65536,f+=String.fromCharCode(55296|i>>10,56320|1023&i))}}else f+=String.fromCharCode(i)}return f}function Zr(r,n){var e=Y[r];return void 0===e&&fr(n+" has unknown type "+Ur(r)),e}var Yr={},Xr=[];function Qr(){var r=e.SpineWasmUtil,n=r.getCurrentListenerID(),f=r.getCurrentTrackEntry(),i=r.getCurrentEvent();r=r.getCurrentEventType(),globalThis.TrackEntryListeners.emitListener(n,f,i,r)}function rn(){var r=e.SpineWasmUtil,n=r.getCurrentListenerID(),f=r.getCurrentEventType(),i=r.getCurrentTrackEntry();r=r.getCurrentEvent(),globalThis.TrackEntryListeners.emitTrackEntryListener(n,i,r,f)}e._spineListenerCallBackFromJS=Qr,e._spineTrackListenerCallback=rn;for(var nn=Array(256),en=0;256>en;++en)nn[en]=String.fromCharCode(en);D=nn,er=e.BindingError=nr("BindingError"),ir=e.InternalError=nr("InternalError"),gr.prototype.isAliasOf=function(r){if(!(this instanceof gr&&r instanceof gr))return!1;var n=this.F.I.G,e=this.F.H,f=r.F.I.G;for(r=r.F.H;n.J;)e=n.W(e),n=n.J;for(;f.J;)r=f.W(r),f=f.J;return n===f&&e===r},gr.prototype.clone=function(){if(this.F.H||ar(this),this.F.V)return this.F.count.value+=1,this;var r=wr,n=Object,e=n.create,f=Object.getPrototypeOf(this),i=this.F;return(r=r(e.call(n,f,{F:{value:{count:i.count,U:i.U,V:i.V,H:i.H,I:i.I,L:i.L,M:i.M}}}))).F.count.value+=1,r.F.U=!1,r},gr.prototype.delete=function(){this.F.H||ar(this),this.F.U&&!this.F.V&&fr("Object already scheduled for deletion"),br(this),kr(this.F),this.F.V||(this.F.L=void 0,this.F.H=void 0)},gr.prototype.isDeleted=function(){return!this.F.H},gr.prototype.deleteLater=function(){return this.F.H||ar(this),this.F.U&&!this.F.V&&fr("Object already scheduled for deletion"),lr.push(this),1===lr.length&&dr&&dr(hr),this.F.U=!0,this},e.getInheritedInstanceCount=function(){return Object.keys(pr).length},e.getLiveInheritedInstances=function(){var r,n=[];for(r in pr)pr.hasOwnProperty(r)&&n.push(pr[r]);return n},e.flushPendingDeletes=hr,e.setDelayFunction=function(r){dr=r,lr.length&&dr&&dr(hr)},Or.prototype.ja=function(r){return this.da&&(r=this.da(r)),r},Or.prototype.ca=function(r){this.S&&this.S(r)},Or.prototype.argPackAdvance=8,Or.prototype.readValueFromPointer=Er,Or.prototype.deleteObject=function(r){null!==r&&r.delete()},Or.prototype.fromWireType=function(r){function n(){return this.Z?mr(this.G.P,{I:this.ka,H:e,M:this,L:r}):mr(this.G.P,{I:this,H:r})}var e=this.ja(r);if(!e)return this.ca(r),null;var f=yr(this.G,e);if(void 0!==f)return 0===f.F.count.value?(f.F.H=e,f.F.L=r,f.clone()):(f=f.clone(),this.ca(r),f);if(f=this.G.ia(e),!(f=vr[f]))return n.call(this);f=this.Y?f.ga:f.pointerType;var i=sr(e,this.G,f.G);return null===i?n.call(this):this.Z?mr(f.G.P,{I:f,H:i,M:this,L:r}):mr(f.G.P,{I:f,H:i})},Gr=e.UnboundTypeError=nr("UnboundTypeError"),zr.N.push({value:void 0},{value:null},{value:!0},{value:!1}),zr.ba=zr.N.length,e.count_emval_handles=function(){for(var r=0,n=zr.ba;n<zr.N.length;++n)void 0!==zr.N[n]&&++r;return r};var fn,tn={r:function(){},u:function(r,n,e,f,i){var t=q(e);or(r,{name:n=V(n),fromWireType:function(r){return!!r},toWireType:function(r,n){return n?f:i},argPackAdvance:8,readValueFromPointer:function(r){if(1===e)var f=d;else if(2===e)f=y;else{if(4!==e)throw new TypeError("Unknown boolean type size: "+n);f=w}return this.fromWireType(f[r>>t])},O:null})},d:function(r,n,e,f,i,t,u,o,a,c,b,k,s){b=V(b),t=Wr(i,t),o&&(o=Wr(u,o)),c&&(c=Wr(a,c)),s=Wr(k,s);var v=Q(b);Ar(v,(function(){jr(`Cannot construct ${b} due to unbound types`,[f])})),ur([r,n,e],f?[f]:[],(function(n){if(n=n[0],f)var e=n.G,i=e.P;else i=gr.prototype;n=rr(v,(function(){if(Object.getPrototypeOf(this)!==u)throw new er("Use 'new' to construct "+b);if(void 0===a.R)throw new er(b+" has no accessible constructor");var r=a.R[arguments.length];if(void 0===r)throw new er(`Tried to invoke ctor of ${b} with invalid number of parameters (${arguments.length}) - expected (${Object.keys(a.R).toString()}) parameters instead!`);return r.apply(this,arguments)}));var u=Object.create(i,{constructor:{value:n}});n.prototype=u;var a=new Tr(b,n,u,s,e,t,o,c);a.J&&(void 0===a.J.X&&(a.J.X=[]),a.J.X.push(a)),e=new Or(b,a,!0,!1),i=new Or(b+"*",a,!1,!1);var k=new Or(b+" const*",a,!1,!0);return vr[r]={pointerType:i,ga:k},Rr(v,n),[e,i,k]}))},h:function(r,n,e,f,i,t,u){var o=Mr(e,f);n=V(n),t=Wr(i,t),ur([],[r],(function(r){function f(){jr(`Cannot call ${i} due to unbound types`,o)}var i=`${(r=r[0]).name}.${n}`;n.startsWith("@@")&&(n=Symbol[n.substring(2)]);var a=r.G.constructor;return void 0===a[n]?(f.T=e-1,a[n]=f):(Fr(a,n,i),a[n].K[e-1]=f),ur([],o,(function(f){if(f=Lr(i,[f[0],null].concat(f.slice(1)),null,t,u),void 0===a[n].K?(f.T=e-1,a[n]=f):a[n].K[e-1]=f,r.G.X)for(const e of r.G.X)e.constructor.hasOwnProperty(n)||(e.constructor[n]=f);return[]})),[]}))},i:function(r,n,e,f,i,t,u,o){n=V(n),t=Wr(i,t),ur([],[r],(function(r){var i=`${(r=r[0]).name}.${n}`,a={get:function(){jr(`Cannot access ${i} due to unbound types`,[e])},enumerable:!0,configurable:!0};return a.set=o?()=>{jr(`Cannot access ${i} due to unbound types`,[e])}:()=>{fr(`${i} is a read-only property`)},Object.defineProperty(r.G.constructor,n,a),ur([],[e],(function(e){e=e[0];var i={get:function(){return e.fromWireType(t(f))},enumerable:!0};return o&&(o=Wr(u,o),i.set=r=>{var n=[];o(f,e.toWireType(n,r)),Hr(n)}),Object.defineProperty(r.G.constructor,n,i),[]})),[]}))},e:function(r,n,e,f,i,t){0<n||H();var u=Mr(n,e);i=Wr(f,i),ur([],[r],(function(r){var e=`constructor ${(r=r[0]).name}`;if(void 0===r.G.R&&(r.G.R=[]),void 0!==r.G.R[n-1])throw new er(`Cannot register multiple constructors with identical number of parameters (${n-1}) for class '${r.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`);return r.G.R[n-1]=()=>{jr(`Cannot construct ${r.name} due to unbound types`,u)},ur([],u,(function(f){return f.splice(1,0,null),r.G.R[n-1]=Lr(e,f,null,i,t),[]})),[]}))},b:function(r,n,e,f,i,t,u,o){var a=Mr(e,f);n=V(n),t=Wr(i,t),ur([],[r],(function(r){function f(){jr(`Cannot call ${i} due to unbound types`,a)}var i=`${(r=r[0]).name}.${n}`;n.startsWith("@@")&&(n=Symbol[n.substring(2)]),o&&r.G.la.push(n);var c=r.G.P,b=c[n];return void 0===b||void 0===b.K&&b.className!==r.name&&b.T===e-2?(f.T=e-2,f.className=r.name,c[n]=f):(Fr(c,n,i),c[n].K[e-2]=f),ur([],a,(function(f){return f=Lr(i,f,r,t,u),void 0===c[n].K?(f.T=e-2,c[n]=f):c[n].K[e-2]=f,[]})),[]}))},c:function(r,n,e,f,i,t,u,o,a,c){n=V(n),i=Wr(f,i),ur([],[r],(function(r){var f=`${(r=r[0]).name}.${n}`,b={get:function(){jr(`Cannot access ${f} due to unbound types`,[e,u])},enumerable:!0,configurable:!0};return b.set=a?()=>{jr(`Cannot access ${f} due to unbound types`,[e,u])}:()=>{fr(f+" is a read-only property")},Object.defineProperty(r.G.P,n,b),ur([],a?[e,u]:[e],(function(e){var u=e[0],b={get:function(){var n=_r(this,r,f+" getter");return u.fromWireType(i(t,n))},enumerable:!0};if(a){a=Wr(o,a);var k=e[1];b.set=function(n){var e=_r(this,r,f+" setter"),i=[];a(c,e,k.toWireType(i,n)),Hr(i)}}return Object.defineProperty(r.G.P,n,b),[]})),[]}))},t:function(r,n){or(r,{name:n=V(n),fromWireType:function(r){var n=Jr(r);return xr(r),n},toWireType:function(r,n){return Kr(n)},argPackAdvance:8,readValueFromPointer:Er,O:null})},p:function(r,n,e){e=q(e),or(r,{name:n=V(n),fromWireType:function(r){return r},toWireType:function(r,n){return n},argPackAdvance:8,readValueFromPointer:Nr(n,e),O:null})},g:function(r,n,e,f,i){n=V(n),-1===i&&(i=4294967295),i=q(e);var t=r=>r;if(0===f){var u=32-8*e;t=r=>r<<u>>>u}e=n.includes("unsigned")?function(r,n){return n>>>0}:function(r,n){return n},or(r,{name:n,fromWireType:t,toWireType:e,argPackAdvance:8,readValueFromPointer:qr(n,i,0!==f),O:null})},y:function(r,n){var e="std::string"===(n=V(n));or(r,{name:n,fromWireType:function(r){var n=g[r>>2],f=r+4;if(e)for(var i=f,t=0;t<=n;++t){var u=f+t;if(t==n||0==p[u]){if(i=i?Vr(i,u-i):"",void 0===o)var o=i;else o+=String.fromCharCode(0),o+=i;i=u+1}}else{for(o=Array(n),t=0;t<n;++t)o[t]=String.fromCharCode(p[f+t]);o=o.join("")}return on(r),o},toWireType:function(r,n){n instanceof ArrayBuffer&&(n=new Uint8Array(n));var f,i,t="string"==typeof n;if(t||n instanceof Uint8Array||n instanceof Uint8ClampedArray||n instanceof Int8Array||fr("Cannot pass non-string to std::string"),e&&t)for(f=i=0;f<n.length;++f){var u=n.charCodeAt(f);127>=u?i++:2047>=u?i+=2:55296<=u&&57343>=u?(i+=4,++f):i+=3}else i=n.length;if(u=(i=un(4+(f=i)+1))+4,g[i>>2]=f,e&&t){if(t=u,u=f+1,f=p,0<u){u=t+u-1;for(var o=0;o<n.length;++o){var a=n.charCodeAt(o);if(55296<=a&&57343>=a&&(a=65536+((1023&a)<<10)|1023&n.charCodeAt(++o)),127>=a){if(t>=u)break;f[t++]=a}else{if(2047>=a){if(t+1>=u)break;f[t++]=192|a>>6}else{if(65535>=a){if(t+2>=u)break;f[t++]=224|a>>12}else{if(t+3>=u)break;f[t++]=240|a>>18,f[t++]=128|a>>12&63}f[t++]=128|a>>6&63}f[t++]=128|63&a}}f[t]=0}}else if(t)for(t=0;t<f;++t)255<(o=n.charCodeAt(t))&&(on(u),fr("String has UTF-16 code units that do not fit in 8 bits")),p[u+t]=o;else for(t=0;t<f;++t)p[u+t]=n[t];return null!==r&&r.push(on,i),i},argPackAdvance:8,readValueFromPointer:Er,O:function(r){on(r)}})},v:function(r,n){or(r,{ra:!0,name:n=V(n),argPackAdvance:0,fromWireType:function(){},toWireType:function(){}})},k:function(r,n,e){r=Jr(r),n=Zr(n,"emval::as");var f=[],i=Kr(f);return g[e>>2]=i,n.toWireType(f,r)},f:xr,l:function(r,n){return r=Jr(r),n=Jr(n),Kr(r[n])},m:function(r){var n=Yr[r];return Kr(void 0===n?V(r):n)},j:function(r){Hr(Jr(r)),xr(r)},q:function(r,n){return r=(r=Zr(r,"_emval_take_value")).readValueFromPointer(n),Kr(r)},o:function(){H("")},n:function(r,n,e){var f;for(Xr.length=0,e>>=2;f=p[n++];)e+=105!=f&e,Xr.push(105==f?w[e]:A[e++>>1]),++e;return B[r].apply(null,Xr)},s:function(r){var n=p.length;if(2147483648<(r>>>=0))return!1;for(var e=1;4>=e;e*=2){var f=n*(1+.2/e);f=Math.min(f,r+100663296);var i=Math;f=Math.max(r,f);r:{i=i.min.call(i,2147483648,f+(65536-f%65536)%65536)-h.buffer.byteLength+65535>>>16;try{h.grow(i),$();var t=1;break r}catch(r){}t=void 0}if(t)return!0}return!1},a:h,x:Qr,w:rn};function un(){return(un=e.asm.B).apply(null,arguments)}function on(){return(on=e.asm.C).apply(null,arguments)}function an(){return(an=e.asm.D).apply(null,arguments)}function cn(){function r(){if(!fn&&(fn=!0,e.calledRun=!0,!T)){if(N(E),f(e),e.onRuntimeInitialized&&e.onRuntimeInitialized(),e.postRun)for("function"==typeof e.postRun&&(e.postRun=[e.postRun]);e.postRun.length;){var r=e.postRun.shift();O.unshift(r)}N(O)}}if(!(0<W)){if(e.preRun)for("function"==typeof e.preRun&&(e.preRun=[e.preRun]);e.preRun.length;)R();N(P),0<W||(e.setStatus?(e.setStatus("Running..."),setTimeout((function(){setTimeout((function(){e.setStatus("")}),1),r()}),1)):r())}}if(function(){function r(r){return r=r.exports,e.asm=r,K(),C=e.asm.A,E.unshift(e.asm.z),j(),r}var n={a:tn};if(U(),e.instantiateWasm)try{return e.instantiateWasm(n,r)}catch(r){b("Module.instantiateWasm callback failed with error: "+r),i(r)}x(n,(function(n){r(n.instance)})).catch(i)}(),e.__embind_initialize_bindings=function(){return(e.__embind_initialize_bindings=e.asm.E).apply(null,arguments)},G=function r(){fn||cn(),fn||(G=r)},e.preInit)for("function"==typeof e.preInit&&(e.preInit=[e.preInit]);0<e.preInit.length;)e.preInit.pop()();return cn(),r.ready}))}}}));
