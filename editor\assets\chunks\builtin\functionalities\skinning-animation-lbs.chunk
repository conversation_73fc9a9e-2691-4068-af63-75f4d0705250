// Copyright (c) 2017-2020 Xiamen Yaji Software Co., Ltd.

#include <builtin/uniforms/cc-skinning>

#if CC_USE_BAKED_ANIMATION
  #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE
    mat4 getJointMatrix (float i) {
      highp float x, y, invSize;
      CCGetJointTextureCoords(3.0, i, x, y, invSize);
      vec4 v1 = texture(cc_jointTexture, vec2((x + 0.5) * invSize, y));
      vec4 v2 = texture(cc_jointTexture, vec2((x + 1.5) * invSize, y));
      vec4 v3 = texture(cc_jointTexture, vec2((x + 2.5) * invSize, y));
      return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));
    }
  #else
    mat4 getJointMatrix (float i) {
      highp float x, y, invSize;
      CCGetJointTextureCoords(12.0, i, x, y, invSize);
      vec4 v1 = vec4(
        decode32(texture(cc_jointTexture, vec2((x + 0.5) * invSize, y))),
        decode32(texture(cc_jointTexture, vec2((x + 1.5) * invSize, y))),
        decode32(texture(cc_jointTexture, vec2((x + 2.5) * invSize, y))),
        decode32(texture(cc_jointTexture, vec2((x + 3.5) * invSize, y)))
      );
      vec4 v2 = vec4(
        decode32(texture(cc_jointTexture, vec2((x + 4.5) * invSize, y))),
        decode32(texture(cc_jointTexture, vec2((x + 5.5) * invSize, y))),
        decode32(texture(cc_jointTexture, vec2((x + 6.5) * invSize, y))),
        decode32(texture(cc_jointTexture, vec2((x + 7.5) * invSize, y)))
      );
      vec4 v3 = vec4(
        decode32(texture(cc_jointTexture, vec2((x + 8.5) * invSize, y))),
        decode32(texture(cc_jointTexture, vec2((x + 9.5) * invSize, y))),
        decode32(texture(cc_jointTexture, vec2((x + 10.5) * invSize, y))),
        decode32(texture(cc_jointTexture, vec2((x + 11.5) * invSize, y)))
      );
      return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));
    }
  #endif
#else
  #if CC_USE_REAL_TIME_JOINT_TEXTURE
    #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE
      mat4 getJointMatrix (float i) {
        float x = i;
        vec4 v1 = texture(cc_realtimeJoint, vec2( x / 256.0, 0.5 / 3.0));
        vec4 v2 = texture(cc_realtimeJoint, vec2( x / 256.0, 1.5 / 3.0));
        vec4 v3 = texture(cc_realtimeJoint, vec2( x / 256.0, 2.5 / 3.0));
        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));
      }
    #else
      mat4 getJointMatrix (float i) {
       float x = 4.0 * i;
        vec4 v1 = vec4(
          decode32(texture(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 0.5 / 3.0))),
          decode32(texture(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 0.5 / 3.0))),
          decode32(texture(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 0.5 / 3.0))),
          decode32(texture(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 0.5 / 3.0)))
        );
        vec4 v2 = vec4(
          decode32(texture(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 1.5 / 3.0))),
          decode32(texture(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 1.5 / 3.0))),
          decode32(texture(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 1.5 / 3.0))),
          decode32(texture(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 1.5 / 3.0)))
        );
        vec4 v3 = vec4(
          decode32(texture(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 2.5 / 3.0))),
          decode32(texture(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 2.5 / 3.0))),
          decode32(texture(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 2.5 / 3.0))),
          decode32(texture(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 2.5 / 3.0)))
        );
        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));
      }
    #endif
  #else
    mat4 getJointMatrix (float i) {
      int idx = int(i);
      vec4 v1 = cc_joints[idx * 3];
      vec4 v2 = cc_joints[idx * 3 + 1];
      vec4 v3 = cc_joints[idx * 3 + 2];
      return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));
    }
  #endif
#endif

mat4 skinMatrix () {
  vec4 joints = vec4(a_joints); //simple conversion of a_joints from unsigned integer to float to match function parameter type
  return getJointMatrix(joints.x) * a_weights.x
       + getJointMatrix(joints.y) * a_weights.y
       + getJointMatrix(joints.z) * a_weights.z
       + getJointMatrix(joints.w) * a_weights.w;
}

void CCSkin (inout vec4 position) {
  mat4 m = skinMatrix();
  position = m * position;
}

void CCSkin (inout vec4 position, inout vec3 normal, inout vec4 tangent) {
  mat4 m = skinMatrix();
  position = m * position;
  normal = (m * vec4(normal, 0.0)).xyz;
  tangent.xyz = (m * vec4(tangent.xyz, 0.0)).xyz;
}
