System.register(["./index-Y4La_nfG.js","./device-manager-B8lyUDPi.js","./buffer-barrier-q_79u36H.js","./sprite-frame-n8bfYych.js","./sprite-V3bxgKTL.js","./label-DLrq6Qwj.js","./gc-object-D18ulfCO.js","./global-exports-CLZKKIY2.js","./pipeline-state-manager-DQyhxoC_.js","./ui-renderer-CboX9P_t.js","./component-CsuvAQKv.js","./director-8iUu7HD2.js","./scene-ArUG4OfI.js","./deprecated-Bf8XgTPJ.js","./node-event-DTNosVQv.js","./mask.js","./rich-text.js","./graphics-BYn_emZo.js","./factory-BOc5khhM.js","./prefab-BQYc0LyR.js","./touch-B157r-vS.js","./debug-view-BP17WHcy.js","./deprecated-D5UVm7fE.js","./deprecated-6ty78xzL.js","./sprite-renderer-rngMmqx7.js","./create-mesh-o_2FMF_K.js","./mesh-Ba1cTOGw.js","./wasm-minigame-DBi57dFz.js","./zlib.min-CyXMsivM.js","./renderer-CZheciPr.js","./camera-component-X7pwLmnP.js","./deprecated-C_Nm0tQW.js","./model-renderer-D7qfPDfZ.js"],(function(t){"use strict";var e,r,a,i,n,s,o,h,l,c,u,f,d,p,_,m,g,v,S,y,x,D,C,w,T,b,M,B,L,F,I,E,A,R,O,H,P,z,k,U,V,N,W,X,j,Y,G,q,Q,K,Z,J,$,tt,et,rt,at,it,nt,st,ot,ht,lt,ct,ut,ft,dt,pt,_t,mt,gt,vt,St,yt,xt,Dt,Ct,wt,Tt,bt,Mt,Bt,Lt,Ft,It,Et,At,Rt,Ot,Ht,Pt,zt,kt,Ut,Vt,Nt,Wt,Xt,jt;return{setters:[function(t){e=t.e,r=t.g,a=t.c,i=t.i,n=t.J,s=t.H,o=t.d,h=t.a,l=t.s,c=t.r,u=t.o,f=t.k,d=t.V,p=t.C,_=t.$,m=t.M,g=t.L,v=t.h},function(t){S=t.d},function(t){y=t.c,x=t.B,D=t.b,C=t.M,w=t.P,T=t.ay},function(e){b=e.S,M=e.d,t({Atlas:e.A,DynamicAtlasManager:e.D,SpriteFrame:e.S,SpriteFrameEvent:e.a,dynamicAtlasManager:e.d})},function(e){B=e.S,t({Sprite:e.S,SpriteAtlas:e.b,SpriteComponent:e.S})},function(e){L=e.L,F=e.C,I=e.s,E=e.O,A=e.B,R=e.a,O=e.f,H=e.H,P=e.V,z=e.g,k=e.b,U=e.i,V=e.c,N=e.d,W=e.e,X=e.h,j=e.j,Y=e.F,G=e.k,q=e.l,t({BASELINE_RATIO:e.B,BitmapFont:e.l,CacheMode:e.j,Font:e.n,HorizontalTextAlignment:e.H,HtmlTextParser:e.o,LRUCache:e.p,Label:e.L,LabelAtlas:e.m,LabelComponent:e.L,MIDDLE_RATIO:e.M,Overflow:e.O,TTFFont:e.T,VerticalTextAlignment:e.V,fragmentText:e.f,getBaselineOffset:e.e,getEnglishWordPartAtFirst:e.t,getEnglishWordPartAtLast:e.u,getSymbolAt:e.b,getSymbolCodeAt:e.c,getSymbolLength:e.g,isEnglishWordPartAtFirst:e.q,isEnglishWordPartAtLast:e.r,isUnicodeCJK:e.d,isUnicodeSpace:e.i,safeMeasureText:e.a})},function(t){Q=t._,K=t.w,Z=t.a,J=t.b,$=t.j,tt=t.Q,et=t.h,rt=t.aw,at=t.at,it=t.o,nt=t.k,st=t.a6,ot=t.P},function(t){ht=t.c},function(t){lt=t.R,ct=t.L,ut=t.M,ft=t.c},function(e){dt=e.S,pt=e.R,_t=e.b,mt=e.U,gt=e.l,vt=e.d,St=e.a,yt=e.i,xt=e.m,Dt=e.n,Ct=e.M,wt=e.h,t({BaseRenderData:e.B,InstanceMaterialType:e.I,MeshBuffer:e.n,MeshRenderData:e.M,RenderComponent:e.U,RenderData:e.h,Renderable2D:e.U,StencilManager:e.l,UIRenderable:e.U,UIRenderer:e.U,UITransform:e.c,UITransformComponent:e.c,UIVertexFormat:e.o})},function(t){Tt=t.C},function(t){bt=t.u,Mt=t.d},function(t){Bt=t.P,Lt=t.ap,Ft=t.aq,It=t.d,Et=t.b},function(t){At=t.v},function(t){Rt=t.N},function(e){Ot=e.Mask,Ht=e.MaskType,t("MaskComponent",e.Mask)},function(e){Pt=e.RichText,t("RichTextComponent",e.RichText)},function(e){zt=e.G,t("GraphicsComponent",e.G)},function(t){kt=t.W},function(t){Ut=t.E,Vt=t.i},function(t){Nt=t.I,Wt=t.d},function(t){Xt=t.g},function(t){jt=t.M},null,function(e){t({Canvas:e.C,CanvasComponent:e.C,RenderRoot2D:e.R,SpriteRenderer:e.S,UIComponent:e.U})},null,null,null,null,null,null,null,null],execute:function(){var Yt,Gt=new e;function qt(t,r,a,i){var n=a.chunk,s=a.data,o=n.vb,h=a.vertexCount,l=t.worldMatrix,c=l.m00,u=l.m01,f=l.m02,d=l.m03,p=l.m04,_=l.m05,m=l.m06,g=l.m07,v=l.m12,S=l.m13,y=l.m14,x=l.m15;Gt.set(i.r/255,i.g/255,i.b/255,i.a/255);for(var D=0,C=0;C<h;++C){var w=s[C],T=w.x,b=w.y,M=d*T+g*b+x;M=M?1/M:1,o[D+0]=(c*T+p*b+v)*M,o[D+1]=(u*T+_*b+S)*M,o[D+2]=(f*T+m*b+y)*M,e.toArray(o,Gt,D+5),D+=a.floatStride}n.bufferId;for(var B=n.vertexOffset,L=n.meshBuffer,F=n.meshBuffer.iData,I=L.indexOffset,E=0,A=h/4;E<A;E++){var R=B+4*E;F[I++]=R,F[I++]=R+1,F[I++]=R+2,F[I++]=R+1,F[I++]=R+3,F[I++]=R+2}L.indexOffset+=a.indexCount,L.setDirty()}function Qt(t,e){for(var a,i,n,s=t.vertexFormat,o=t.chunk.vb,h=0,l=0;l<s.length;++l){if(a=s[l],(i=y[a.format]).hasAlpha)if(n=t.floatStride,i.size/i.count==1)for(var c=~~r(Math.round(255*e),0,255),u=h;u<o.length;u+=n)o[u]=(4294967040&o[u]|c)>>>0;else if(i.size/i.count==4)for(var f=h+3;f<o.length;f+=n)o[f]=e;h+=i.size>>2}}var Kt,Zt=a("cc.UIMeshRenderer")(Yt=i(110)(Yt=function(t){function e(){var e;return(e=t.call(this)||this)._modelComponent=null,e._dirtyVersion=-1,e._internalId=-1,e.stencilStage=dt.DISABLED,e._renderData=null,e._renderEntity=new pt(_t.DYNAMIC),e}Q(e,t);var r=e.prototype;return r.__preload=function(){this.node._uiProps.uiComp=this},r.onEnable=function(){bt.addRenderer(this),this._markForUpdateRenderData()},r.onDisable=function(){bt.removeRenderer(this),this.renderEntity.enabled=this._canRender()},r.onLoad=function(){this.node._getUITransformComp()||this.node.addComponent("cc.UITransform"),this._modelComponent=this.getComponent("cc.ModelRenderer"),this._modelComponent?this.renderEntity.setNode(this.node):K(16378,this.node?this.node.name:"")},r.onDestroy=function(){this.renderEntity.setNode(null),this.node._uiProps.uiComp===this&&(this.node._uiProps.uiComp=null),this._modelComponent=this.getComponent("cc.ModelRenderer"),this._modelComponent&&(this._modelComponent._sceneGetter=null)},r._render=function(t){if(this._modelComponent){var e=this._modelComponent._collectModels();this._modelComponent._detachFromScene();for(var r=0;r<e.length;r++)e[r].enabled&&t.commitModel(this,e[r],this._modelComponent.material);return!0}return!1},r.fillBuffers=function(t){this.enabled&&this._render(t)},r.updateRenderer=function(){},r._uploadRenderData=function(){},r.postUpdateAssembler=function(){},r.update=function(){this._fitUIRenderQueue()},r._fitUIRenderQueue=function(){if(this._modelComponent)for(var t=this._modelComponent.sharedMaterials.length,e=0;e<t;e++){var r=this._modelComponent.getMaterialInstance(e);if(null!=r)for(var a=r.passes,i=a.length,n=0;n<i;n++)a[n].setPriority(lt.MAX-11),r.recompileShaders({CC_FORCE_FORWARD_SHADING:!0},n)}},r.markForUpdateRenderData=function(t){void 0===t&&(t=!0),this._markForUpdateRenderData(t)},r._markForUpdateRenderData=function(){bt.markDirtyRenderer(this)},r.setNodeDirty=function(){},r.setTextureDirty=function(){},r._canRender=function(){return this.enabled&&null!==this._modelComponent},Z(e,[{key:"modelComponent",get:function(){return this._modelComponent}},{key:"renderEntity",get:function(){return this._renderEntity}},{key:"renderData",get:function(){return this._renderData}}]),e}(Tt))||Yt)||Yt;t({UIMeshRenderer:Zt,UIModelComponent:Zt}),ht.UIMeshRenderer=Zt;var Jt=a("cc.LabelOutline")(Kt=i(110)(Kt=n(L)(Kt=function(t){function e(){return t.apply(this,arguments)||this}Q(e,t);var r=e.prototype;return r.onEnable=function(){this.node.getComponent(L).enableOutline=!0},r.onDisable=function(){this.node.getComponent(L).enableOutline=!1},Z(e,[{key:"color",get:function(){return this.node.getComponent(L).outlineColor},set:function(t){this.node.getComponent(L).outlineColor=t}},{key:"width",get:function(){return this.node.getComponent(L).outlineWidth},set:function(t){this.node.getComponent(L).outlineWidth=t}}]),e}(Tt))||Kt)||Kt)||Kt;t({LabelOutline:Jt,LabelOutlineComponent:Jt}),ht.LabelOutline=Jt;var $t,te,ee,re=ct.Enum.NONE|ct.Enum.UI_3D,ae=function(){function t(){this.model=null,this.texture=null,this.sampler=null,this.useLocalData=null,this.isStatic=!1,this.textureHash=0,this.samplerHash=0,this._passes=[],this._shaders=[],this.visFlags=re,this.inputAssembler=null,this.descriptorSet=null}var e=t.prototype;return e.destroy=function(){this._passes=[]},e.clear=function(){this.inputAssembler=null,this.descriptorSet=null,this.texture=null,this.sampler=null,this.textureHash=0,this.samplerHash=0,this.model=null,this.isStatic=!1,this.useLocalData=null,this.visFlags=re},e.fillPasses=function(t,e,r,a){if(t){var i=t.passes;if(!i)return;this._shaders.length=i.length;for(var n=0;n<i.length;n++){this._passes[n]||(this._passes[n]=new Bt(ht.director.root));var s=i[n],o=this._passes[n];s.update(),e||(e=s.depthStencilState,r=0),o._initPassFromTarget(s,e,r),this._shaders[n]=o.getShaderVariant(a)}}},Z(t,[{key:"passes",get:function(){return this._passes}},{key:"shaders",get:function(){return this._shaders}}]),t}(),ie=a("cc.UIStaticBatch")($t=i(110)((te=function(t){function e(){var e;return(e=t.call(this)||this)._init=!1,e._bufferAccessor=null,e._dirty=!0,e._uiDrawBatchList=[],e}Q(e,t);var r=e.prototype;return r.postUpdateAssembler=function(){},r.markAsDirty=function(){},r._requireDrawBatch=function(){var t=new ae;return t.isStatic=!0,this._uiDrawBatchList.push(t),t},r._clearData=function(){if(this._bufferAccessor){this._bufferAccessor.reset();for(var t=this._getBatcher(),e=0;e<this._uiDrawBatchList.length;e++)this._uiDrawBatchList[e].destroy(t)}this._uiDrawBatchList.length=0,this._init=!1},r._getBatcher=function(){return Mt.root&&Mt.root.batcher2D?Mt.root.batcher2D:(K(9301),null)},Z(e,[{key:"color",get:function(){return this._color},set:function(t){this._color!==t&&this._color.set(t)}},{key:"drawBatchList",get:function(){return this._uiDrawBatchList}}]),e}(mt),J(te.prototype,"color",[s],Object.getOwnPropertyDescriptor(te.prototype,"color"),te.prototype),$t=te))||$t)||$t;t({UIStaticBatch:ie,UIStaticBatchComponent:ie});var ne,se,oe,he=t("LabelShadow",a("cc.LabelShadow")(ee=i(110)(ee=n(L)(ee=function(t){function e(){return t.apply(this,arguments)||this}Q(e,t);var r=e.prototype;return r.onEnable=function(){this.node.getComponent(L).enableShadow=!0},r.onDisable=function(){this.node.getComponent(L).enableShadow=!1},Z(e,[{key:"color",get:function(){return this.node.getComponent(L).shadowColor},set:function(t){this.node.getComponent(L).shadowColor=t}},{key:"offset",get:function(){return this.node.getComponent(L).shadowOffset},set:function(t){this.node.getComponent(L).shadowOffset=t}},{key:"blur",get:function(){return this.node.getComponent(L).shadowBlur},set:function(t){this.node.getComponent(L).shadowBlur=t}}]),e}(Tt))||ee)||ee)||ee),le=a("cc.UIOpacity")(ne=i(110)(ne=o((se=function(t){function e(){var e;return(e=t.call(this)||this)._parentOpacity=1,e._parentOpacityResetFlag=!0,e._opacity=oe&&oe(),e}Q(e,t);var a=e.prototype;return a._setParentOpacity=function(t){this._parentOpacity=t},a._getParentOpacity=function(t){if(null==t||!t.isValid)return 1;var r=t._uiProps.uiComp,a=t.getComponent(e);return r&&r.color?1:a?a._parentOpacity*(a._opacity/255):this._getParentOpacity(t.getParent())},a._parentChanged=function(){},a._setEntityLocalOpacityRecursively=function(){},a.onEnable=function(){this.node.on(Rt.PARENT_CHANGED,this._parentChanged,this),this.node._uiProps.localOpacity=this._parentOpacity*this._opacity/255,this._parentOpacityResetFlag?(this._parentChanged(),this._parentOpacityResetFlag=!1):this._setEntityLocalOpacityRecursively(this.node._uiProps.localOpacity)},a.onDisable=function(){this.node.off(Rt.PARENT_CHANGED,this._parentChanged,this),this.node._uiProps.localOpacity=1,this._setEntityLocalOpacityRecursively(this.node._uiProps.localOpacity)},Z(e,[{key:"opacity",get:function(){return this._opacity},set:function(t){this._opacity!==t&&(t=r(t,0,255),this._opacity=t,this.node._uiProps.localOpacity=t/255)}}]),e}(Tt),oe=h(se.prototype,"_opacity",[l],(function(){return 255})),ne=se))||ne)||ne)||ne;t({UIOpacity:le,UIOpacityComponent:le}),ht.MaskComponent=Ot,$(Ot,"cc.MaskComponent"),ht.LabelComponent=L,$(L,"cc.LabelComponent"),ht.LabelOutlineComponent=Jt,$(Jt,"cc.LabelOutlineComponent"),ht.RichTextComponent=Pt,$(Pt,"cc.RichTextComponent"),ht.SpriteComponent=B,$(B,"cc.SpriteComponent"),ht.UIModelComponent=Zt,$(Zt,"cc.UIModelComponent"),ht.GraphicsComponent=zt,$(zt,"cc.GraphicsComponent"),$(ie,"cc.UIStaticBatchComponent"),$(le,"cc.UIOpacityComponent"),c(Ot.prototype,"Mask",[{name:"graphics",newName:"subComp",target:Ot.prototype,targetName:"Mask"}]),c(Ht,"MaskType",[{name:"RECT",newName:"GRAPHICS_RECT",target:Ht,targetName:"MaskType"},{name:"ELLIPSE",newName:"GRAPHICS_ELLIPSE",target:Ht,targetName:"MaskType"},{name:"IMAGE_STENCIL",newName:"SPRITE_STENCIL",target:Ht,targetName:"MaskType"}]),u(Jt.prototype,"LabelOutline",[{name:"width",suggest:"Please use Label.outlineWidth instead."},{name:"color",suggest:"Please use Label.outlineColor instead."}]),u(he.prototype,"LabelShadow",[{name:"color",suggest:"Please use Label.shadowColor instead."},{name:"offset",suggest:"Please use Label.shadowOffset instead."},{name:"blur",suggest:"Please use Label.shadowBlur instead."}]);var ce=["left","center","right"],ue=2048,fe=W(),de=(1/255).toFixed(3),pe=function(){this.char="",this.valid=!0,this.x=0,this.y=0,this.line=0,this.hash=""},_e=function(){function t(){this._context=null,this._canvas=null,this._canvasData=null,this._lettersInfo=[],this._tmpRect=new f,this._maxFontSize=100,this._fontScale=1;var t=this._canvasData=F.getInstance().get();this._canvas=t.canvas,this._context=t.context}var e=t.prototype;return e.destroy=function(){F.getInstance().put(this._canvasData),this._canvasData=null,this._canvas=null,this._context=null,this._lettersInfo.length=0},e.processingString=function(t,e,r,a,i,n){if(t)e.fntConfig?this._fontScale=1:this._fontScale=this._getStyleFontScale(e.originFontSize,e.fontScale),I.fontScale=this._fontScale,this._setupBMFontOverflowMetrics(r,a),this._updateFontScale(e),this._computeHorizontalKerningForText(e,r,i),this._alignText(e,r,a,i);else{var s=0;for(this._fontScale=this._getStyleFontScale(e.fontSize,e.fontScale),this._updatePaddingRect(e,a),this._calculateLabelFont(e,r,a,i);(a.canvasSize.width>ue||a.canvasSize.height>ue)&&s<=3;){if(++s>3)this._fontScale=1;else{var o=Math.max(a.canvasSize.width,a.canvasSize.height),h=ue/o;this._fontScale*=h,this._fontScale=Math.max(1,this._fontScale)}this._updatePaddingRect(e,a),this._calculateLabelFont(e,r,a,i)}}n&&(n=a.parsedString)},e.generateRenderInfo=function(t,e,r,a,i,n,s){t?(this._computeAlignmentOffset(e,r,a),this.generateVertexData(t,e,r,a,i,n,s)):(this._updateLabelDimensions(e,r,a),this._updateTexture(e,r,a,i),this.generateVertexData(t,e,r,a,i,n,s))},e.setCanvasUsed=function(t,e){this._canvas=t,this._context=e},e._getStyleFontScale=function(t,e){var r=e,a=this._maxFontSize;return r*t>a&&t<a&&(r=a/t),r<1&&(r=1),r},e._calculateLabelFont=function(t,e,r,a){if(this._context){t.actualFontSize=t.fontSize*this._fontScale;var i=a.split("\n"),n=r.parsedString=i,s=this._getFontDesc(t.actualFontSize,t.fontFamily,t.isBold,t.isItalic);this._context.font=t.fontDesc=s;var o=r.canvasSize,h=r.nodeContentSize,l=r.canvasPadding,c=r.contentSizeExtend,u=this._fontScale;switch(e.overFlow){case E.NONE:for(var f=0,d=0;d<i.length;++d){var p=R(this._context,i[d],s);f=f>p?f:p}var _=f,m=(n.length+A)*this._getLineHeight(e.lineHeight,t.actualFontSize,t.fontSize);o.width=_+l.width*u,o.height=m+l.height*u,h.width=(_+c.width*u)/u,h.height=(m+c.height*u)/u;break;case E.SHRINK:this._calculateShrinkFont(i,t,e,r),this._calculateWrapText(i,t,e,r),o.width=h.width*u,o.height=h.height*u;break;case E.CLAMP:this._calculateWrapText(i,t,e,r),o.width=h.width*u,o.height=h.height*u;break;case E.RESIZE_HEIGHT:this._calculateWrapText(i,t,e,r);var g=(r.parsedString.length+A)*this._getLineHeight(e.lineHeight,t.actualFontSize,t.fontSize);o.width=h.width*u,o.height=g+l.height*u,h.height=(g+c.height*u)/u}}},e._getFontDesc=function(t,e,r,a){var i=t.toString()+"px ";return i+=e,r&&(i="bold "+i),a&&(i="italic "+i),i},e._getLineHeight=function(t,e,r){return 0===t?e:t*e/r},e._calculateShrinkFont=function(t,e,r,a){if(this._context){var i=this._getFontDesc(e.actualFontSize,e.fontFamily,e.isBold,e.isItalic);this._context.font=i;var n=this._calculateParagraphLength(t,this._context,i),s=0,o=0,h=0,l=e.actualFontSize,c=a.canvasSize,u=a.nodeContentSize,f=a.canvasPadding,d=this._fontScale;if(r.wrapping){var p=u.width*d,_=u.height*d;if(p<0||_<0)return;o=_+1;for(var m=0,g=0|e.actualFontSize+1,v=0;m<g;){if((v=m+g+1>>1)<=0){tt(4003);break}l=v,i=this._getFontDesc(l,e.fontFamily,e.isBold,e.isItalic),this._context.font=i;var S=this._getLineHeight(r.lineHeight,l,e.fontSize);for(o=0,s=0;s<t.length;++s){var y=R(this._context,t[s],i);o+=O(t[s],y,p,this._measureText(this._context,i)).length*S}o>_?g=v-1:m=v}0===m?tt(4003):(l=m,i=this._getFontDesc(l,e.fontFamily,e.isBold,e.isItalic),this._context.font=i)}else{for(o=t.length*this._getLineHeight(r.lineHeight,l,e.fontSize),s=0;s<t.length;++s)h<n[s]&&(h=n[s]);var x=(c.width-f.width)*d/h,D=c.height*d/o;l=e.actualFontSize*Math.min(1,x,D)|0,i=this._getFontDesc(l,e.fontFamily,e.isBold,e.isItalic),this._context.font=i}e.actualFontSize=l,e.fontDesc=i}},e._calculateWrapText=function(t,e,r,a){if(r.wrapping&&this._context){var i=[],n=a.nodeContentSize.width*this._fontScale,s=this._getFontDesc(e.actualFontSize,e.fontFamily,e.isBold,e.isItalic);this._context.font=s;for(var o=0;o<t.length;++o){var h=R(this._context,t[o],s),l=O(t[o],h,n,this._measureText(this._context,s));i=i.concat(l)}a.parsedString=i,e.fontDesc=s}},e._measureText=function(t,e){return function(r){return R(t,r,e)}},e._calculateParagraphLength=function(t,e,r){return t.map((function(t){return R(e,t,r)}))},e._updatePaddingRect=function(t,e){var r=0,a=0,i=0,n=0,s=0,o=e.contentSizeExtend,h=e.canvasPadding;if(o.width=o.height=0,t.isOutlined&&(r=a=i=n=s=t.outlineWidth,o.width=o.height=2*s),t.hasShadow){var l=t.shadowBlur+s,c=t.shadowOffsetX,u=t.shadowOffsetY;i=Math.max(i,-c+l),n=Math.max(n,c+l),r=Math.max(r,u+l),a=Math.max(a,-u+l)}if(t.isItalic){var f=t.fontSize*Math.tan(.20943951);n+=f,o.width+=f}h.x=i,h.y=r,h.width=i+n,h.height=r+a},e._updateLabelDimensions=function(t,e,r){var a=r.canvasSize;a.width=Math.min(a.width,ue),a.height=Math.min(a.height,ue);var i=this._canvas,n=this._context;i.width=a.width,i.height=a.height,n.font=t.fontDesc,n.textAlign=ce[e.horizontalAlign],n.textBaseline="alphabetic"},e._calculateFillTextStartPosition=function(t,e,r){var a=0,i=r.canvasSize,n=r.canvasPadding;e.horizontalAlign===H.RIGHT?a=i.width-n.width:e.horizontalAlign===H.CENTER&&(a=(i.width-n.width)/2);var s=this._getLineHeight(e.lineHeight,t.actualFontSize,t.fontSize)*(r.parsedString.length-1),o=t.actualFontSize*(1-A/2);if(e.verticalAlign!==P.TOP){var h=s+n.height+t.actualFontSize-i.height;e.verticalAlign===P.BOTTOM?o-=h+=A/2*t.actualFontSize:o-=h/2}o+=fe*t.actualFontSize,r.startPosition.set(a+n.x,o+n.y)},e._updateTexture=function(t,e,r,a){var i=this._context,n=this._canvas;if(i&&n){i.clearRect(0,0,n.width,n.height),i.font=t.fontDesc,this._calculateFillTextStartPosition(t,e,r);var s=this._getLineHeight(e.lineHeight,t.actualFontSize,t.fontSize);i.lineJoin="round",t.isOutlined?(i.fillStyle="rgba("+t.outlineColor.r+", "+t.outlineColor.g+", "+t.outlineColor.b+", "+de+")",i.fillRect(0,0,n.width,n.height)):(i.fillStyle="rgba("+t.color.r+", "+t.color.g+", "+t.color.b+", "+de+")",i.fillRect(0,0,n.width,n.height)),i.fillStyle="rgb("+t.color.r+", "+t.color.g+", "+t.color.b+")";var o=r.startPosition,h=new d(o.x,o.y),l=h.x,c=0;this._drawTextEffect(h,s,t,e,r);for(var u=r.parsedString,f=0;f<u.length;++f)c=h.y+f*s,t.hasShadow&&(this._setupShadow(t),i.fillText(u[f],l,c)),t.isOutlined&&(this._setupOutline(t),i.strokeText(u[f],l,c)),t.hasShadow&&!t.isOutlined||i.fillText(u[f],l,c);t.hasShadow&&(i.shadowColor="transparent"),this._uploadTexture(a)}},e._uploadTexture=function(t){var e,r=this._canvas;t.texture&&r&&(e=t.texture instanceof b?t.texture.texture:t.texture,0!==r.width&&0!==r.height&&(e.getGFXTexture(),e.getGFXSampler(),e.reset({width:r.width,height:r.height,mipmapLevel:1}),e.uploadData(r),e.setWrapMode(kt.CLAMP_TO_EDGE,kt.CLAMP_TO_EDGE),t.texture instanceof b&&(t.texture.rect=new f(0,0,r.width,r.height),t.texture._calculateUV()),ht.director.root&&ht.director.root.batcher2D&&ht.director.root.batcher2D._releaseDescriptorSetCache(e.getHash())))},e._drawTextEffect=function(t,e,r,a,i){if(r.hasShadow||r.isOutlined||r.isUnderline){for(var n=this._context,s=i.parsedString,o=s.length>1&&r.hasShadow,h=this._measureText(this._context,r.fontDesc),l=0,c=0,u=0;u<s.length;++u)if(l=t.x,c=t.y+u*e,o&&(r.hasShadow&&(this._setupShadow(r),n.fillText(s[u],l,c)),r.isOutlined&&(this._setupOutline(r),n.strokeText(s[u],l,c)),r.hasShadow&&!r.isOutlined||n.fillText(s[u],l,c)),r.isUnderline){var f=h(s[u]),p=new d;a.horizontalAlign===H.RIGHT?p.x=t.x-f:a.horizontalAlign===H.CENTER?p.x=t.x-f/2:p.x=t.x,p.y=c+r.actualFontSize/8,n.fillRect(p.x,p.y,f,r.underlineHeight*this._fontScale)}o&&(n.shadowColor="transparent")}},e._setupOutline=function(t){var e=this._context;e.shadowBlur=0,e.shadowOffsetX=0,e.shadowOffsetY=0,e.strokeStyle="rgba("+t.outlineColor.r+", "+t.outlineColor.g+", "+t.outlineColor.b+", "+t.outlineColor.a/255+")",e.lineWidth=2*t.outlineWidth*this._fontScale},e._setupShadow=function(t){var e=this._context,r=this._fontScale;e.shadowColor="rgba("+t.shadowColor.r+", "+t.shadowColor.g+", "+t.shadowColor.b+", "+t.shadowColor.a/255+")",e.shadowBlur=t.shadowBlur*r,e.shadowOffsetX=t.shadowOffsetX*r,e.shadowOffsetY=-t.shadowOffsetY*r},e.generateVertexData=function(t,e,r,a,i,n,s){t?this._updateQuads(e,r,a,i,n,s):(this.updateQuatCount(i),s(e,a,i))},e.updateQuatCount=function(t){var e=t.vertexBuffer,r=t.quadCount;if(e.length!==r){for(var a=e.length;a<r;a++)e.push({x:0,y:0,z:0,u:0,v:0,color:p.WHITE.clone()});e.length=r}},e._setupBMFontOverflowMetrics=function(t,e){var r=e.nodeContentSize,a=r.width,i=r.height;t.overFlow===E.RESIZE_HEIGHT&&(i=0),t.overFlow===E.NONE&&(a=0,i=0),t.textWidthTemp=a,t.textHeightTemp=i,t.textDimensions.width=a,t.textDimensions.height=i,t.maxLineWidth=a},e._updateFontScale=function(t){t.bmfontScale=t.actualFontSize/(t.originFontSize*this._fontScale)},e._computeHorizontalKerningForText=function(t,e,r){var a=r,i=a.length;if(t.fntConfig){var n=t.fntConfig.kerningDict,s=e.horizontalKerning;if(n&&0!==n.length)for(var o=-1,h=0;h<i;++h){var l=a.charCodeAt(h),c=n[o<<16|65535&l]||0;s[h]=h<i-1?c:0,o=l}}},e._alignText=function(t,e,r,a){this._multilineTextWrap(t,e,r,a,this._getFirstWordLen),e.overFlow===E.SHRINK&&(t.fontSize>0&&this._isVerticalClamp(t,e,r,a,this)&&this._shrinkLabelToContentSize(t,e,r,a,this._isVerticalClamp),t.fontSize>0&&this._isHorizontalNeedShrink(e,r)&&this._shrinkLabelToContentSize(t,e,r,a,this._isHorizontalClamp)),this._parsedString(r,a)},e._parsedString=function(t,e){for(var r=[],a="",i=0,n=0,s=z(e);i<s;++i){var o=this._lettersInfo[i];o.valid&&(n===o.line?a+=o.char:(r=r.concat(a),n=o.line,a=""))}r=r.concat(a),t.parsedString=r},e._multilineTextWrap=function(t,e,r,a,i){e.linesWidth.length=0;for(var n=a,s=n.length,o=0,h=0,l=0,c=0,u=0,f=0,p=0,_=null,m=0;m<s;){var g=k(n,m);if("\n"!==g){for(var v=i(t,e,n,m,s),S=f,y=p,x=u,D=h,C=!1,w=new d,T=0;T<v;++T){var b=m+T;if("\r"!==(g=k(n,b)))if(_=I.fontAtlas.getLetterDefinitionForChar(g,I)){var M=D+_.offsetX*t.bmfontScale-I.margin;if(e.wrapping&&e.maxLineWidth>0&&h>0&&M+_.w*t.bmfontScale>e.maxLineWidth&&!U(g)){e.linesWidth.push(u),u=0,o++,h=0,l-=e.lineHeight*this._getFontScale(t,e)+0,C=!0;break}w.x=M,w.y=l-_.offsetY*t.bmfontScale,this._recordLetterInfo(w,g,b,o),b+1<e.horizontalKerning.length&&b<s-1&&(D+=e.horizontalKerning[b+1]*t.bmfontScale),D+=_.xAdvance*t.bmfontScale+e.spacingX,x=w.x+_.w*t.bmfontScale,S<w.y&&(S=w.y),y>w.y-_.h*t.bmfontScale&&(y=w.y-_.h*t.bmfontScale)}else this._recordPlaceholderInfo(b,g),null!=t.fntConfig?tt(16354,t.fntConfig.atlasName,g):tt(16355,t.fontFamily,g);else this._recordPlaceholderInfo(b,g)}C||(h=D,f<S&&(f=S),p>y&&(p=y),c<(u=x)&&(c=u),m+=v)}else e.linesWidth.push(u),u=0,o++,h=0,l-=e.lineHeight*this._getFontScale(t,e)+0,this._recordPlaceholderInfo(m,g),m++}e.linesWidth.push(u),e.numberOfLines=o+1,e.textDesiredHeight=e.numberOfLines*e.lineHeight*this._getFontScale(t,e),e.numberOfLines>1&&(e.textDesiredHeight+=0*(e.numberOfLines-1));var B=r.nodeContentSize;return B.width=e.textWidthTemp,B.height=e.textHeightTemp,e.textWidthTemp<=0&&(B.width=parseFloat(c.toFixed(2))+2*I.margin),e.textHeightTemp<=0&&(B.height=parseFloat(e.textDesiredHeight.toFixed(2))+2*I.margin),e.tailoredTopY=B.height,e.tailoredBottomY=0,f>0&&(e.tailoredTopY=B.height+f),p<-e.textDesiredHeight&&(e.tailoredBottomY=e.textDesiredHeight+p),!0},e._recordPlaceholderInfo=function(t,e){var r=this._lettersInfo;if(t>=r.length){var a=new pe;r.push(a)}r[t].char=e,r[t].hash=""+V(e,0)+I.hash,r[t].valid=!1},e._recordLetterInfo=function(t,e,r,a){var i=this._lettersInfo;if(r>=i.length){var n=new pe;i.push(n)}var s=""+V(e,0)+I.hash;i[r].line=a,i[r].char=e,i[r].hash=s,i[r].valid=I.fontAtlas.getLetter(s).valid,i[r].x=t.x,i[r].y=t.y},e._getFirstWordLen=function(t,e,r,a,i){var n=k(r,a);if(N(n)||"\n"===n||U(n))return 1;var s=I.fontAtlas,o=1,h=s.getLetterDefinitionForChar(n,I);if(!h)return o;for(var l=h.xAdvance*t.bmfontScale+e.spacingX,c=a+1;c<i&&(n=k(r,c),h=s.getLetterDefinitionForChar(n,I));++c){if(l+h.offsetX*t.bmfontScale+h.w*t.bmfontScale>e.maxLineWidth&&!U(n)&&e.maxLineWidth>0)return o;if(l+=h.xAdvance*t.bmfontScale+e.spacingX,"\n"===n||U(n)||N(n))break;o++}return o},e._computeAlignmentOffset=function(t,e,r){var a=r.nodeContentSize;switch(e.linesOffsetX.length=0,e.letterOffsetY=0,e.horizontalAlign){case H.LEFT:for(var i=0;i<e.numberOfLines;++i)e.linesOffsetX.push(0);break;case H.CENTER:for(var n=0,s=e.linesWidth.length;n<s;n++)e.linesOffsetX.push((a.width-e.linesWidth[n])/2);break;case H.RIGHT:for(var o=0,h=e.linesWidth.length;o<h;o++)e.linesOffsetX.push(a.width-e.linesWidth[o])}if(e.letterOffsetY=a.height,e.verticalAlign!==P.TOP){var l=a.height-e.textDesiredHeight+e.lineHeight*this._getFontScale(t,e)-t.originFontSize*this._fontScale*t.bmfontScale;e.verticalAlign===P.BOTTOM?e.letterOffsetY-=l:e.letterOffsetY-=l/2}},e._getFontScale=function(t,e){return e.overFlow===E.SHRINK?t.bmfontScale:1},e._isVerticalClamp=function(t,e,r){return e.textDesiredHeight>r.nodeContentSize.height},e._isHorizontalClamp=function(t,e,r,a,i){for(var n=!1,s=0,o=z(a);s<o;++s){var h=i._lettersInfo[s];if(h.valid){var l=I.fontAtlas.getLetterDefinitionForChar(h.char,I);if(!l)continue;var c=h.x+l.w*t.bmfontScale,u=h.line;if(e.textWidthTemp>0){var f=r.nodeContentSize;if(e.wrapping){if(e.linesWidth[u]>f.width&&(c>f.width||c<0)){n=!0;break}}else if(c>f.width){n=!0;break}}}}return n},e._isHorizontalNeedShrink=function(t,e){for(var r=0,a=t.linesWidth.length;r<a;++r)if(t.linesWidth[r]>e.nodeContentSize.width)return!0;return!1},e._shrinkLabelToContentSize=function(t,e,r,a,i){for(var n=0,s=0|t.actualFontSize,o=0;n<s;){var h=o=n+s+1>>1;if(h<=0)break;t.bmfontScale=h/(t.originFontSize*this._fontScale),this._multilineTextWrap(t,e,r,a,this._getFirstWordLen),this._computeAlignmentOffset(t,e,r),i(t,e,r,a,this)?s=o-1:n=o}n>=0&&this._scaleFontSizeDown(t,e,r,a,n)},e._scaleFontSizeDown=function(t,e,r,a,i){var n=!0;i||(i=.1,n=!1),t.actualFontSize=i,n&&(this._updateFontScale(t),this._multilineTextWrap(t,e,r,a,this._getFirstWordLen))},e._updateQuads=function(t,e,r,a,i,n){for(var s=I.fontAtlas,o=t.spriteFrame?t.spriteFrame.texture:s.getTexture(),h=r.nodeContentSize,l=a.uiTransAnchorX*h.width,c=a.uiTransAnchorY*h.height,u=0,f=z(i);u<f;++u){var d=this._lettersInfo[u];if(d.valid){var p=s.getLetter(d.hash);if(p){var _=this._tmpRect;_.height=p.h,_.width=p.w,_.x=p.u,_.y=p.v;var m=d.y+e.letterOffsetY;if(e.textHeightTemp>0){if(m>e.tailoredTopY){var g=m-e.tailoredTopY;_.y+=g,_.height-=g,m-=g}m-_.height*t.bmfontScale<e.tailoredBottomY&&e.overFlow===E.CLAMP&&(_.height=m<e.tailoredBottomY?0:(m-e.tailoredBottomY)/t.bmfontScale)}var v=d.line,S=d.x+p.w/2*t.bmfontScale+e.linesOffsetX[v];if(e.textWidthTemp>0&&this._isHorizontalClamped(e,r,S,v)&&e.overFlow===E.CLAMP&&(_.width=0),_.height>0&&_.width>0){var y=this._determineRect(t),x=d.x+e.linesOffsetX[d.line],D=a.quadCount;a.quadCount+=4,this.updateQuatCount(a),n(t,r,a,D,o,_,y,x-l,m-c)}}else K(16353)}}return!0},e._isHorizontalClamped=function(t,e,r,a){var i=e.nodeContentSize,n=t.linesWidth[a],s=r>i.width||r<0;return t.wrapping?n>i.width&&s:s},e._determineRect=function(t){var e=t.spriteFrame;if(!e)return!1;var r=e.isRotated(),a=e.getOriginalSize(),i=e.getRect(),n=e.getOffset(),s=n.x+(a.width-i.width)/2,o=n.y-(a.height-i.height)/2,h=this._tmpRect;if(r){var l=h.x;h.x=i.x+i.height-h.y-h.height-o,h.y=l+i.y-s,h.y<0&&(h.height+=o)}else h.x+=i.x-s,h.y+=i.y+o;return r},t}();_e.instance=void 0,_e.instance=new _e;var me=new X(64,64),ge=new Y(null),ve=null,Se=null,ye=null,xe=null,De=null,Ce=function(){function t(){}var e=t.prototype;return e.updateProcessingData=function(t,e,r,a,i,n){t.fontSize=i.fontSize,t.actualFontSize=i.fontSize,t.originFontSize=ye?ye.fontSize:i.fontSize,e.horizontalAlign=i.horizontalAlign,e.verticalAlign=i.verticalAlign,e.spacingX=i.spacingX;var s=i.overflow;e.overFlow=s,e.lineHeight=i.lineHeight,r.nodeContentSize.width=n.width,r.nodeContentSize.height=n.height,s===E.NONE?(e.wrapping=!1,r.nodeContentSize.width+=2*I.margin,r.nodeContentSize.height+=2*I.margin):s===E.RESIZE_HEIGHT?(e.wrapping=!0,r.nodeContentSize.height+=2*I.margin):e.wrapping=i.enableWrapText,a.uiTransAnchorX=n.anchorX,a.uiTransAnchorY=n.anchorY,I.lineHeight=i.lineHeight,I.fontSize=i.fontSize,t.spriteFrame=xe,t.fntConfig=ye,t.fontFamily=I.fontFamily,t.color.set(i.color)},e.updateRenderData=function(t){if(t.renderData&&ve!==t){if(t.renderData.vertDirty){Se=(ve=t).node._getUITransformComp();var e=t.renderData,r=_e.instance,a=t.textStyle,i=t.textLayout,n=t.textLayoutData,s=t.textRenderData;a.fontScale=At.getScaleX(),this._updateFontFamily(t),this.updateProcessingData(a,i,n,s,t,Se),this._updateLabelInfo(t),a.fontDesc=I.fontDesc,r.processingString(!0,a,i,n,t.string),s.quadCount=0,r.generateRenderInfo(!0,a,i,n,s,t.string,this.generateVertexData),e.dataLength!==s.quadCount&&(this.resetRenderData(t),e.dataLength=s.quadCount,e.resize(e.dataLength,e.dataLength/2*3));for(var o=e.data,h=0,l=s.quadCount;h<l;h++)o[h]=s.vertexBuffer[h];var c=e.indexCount;this.createQuadIndices(c),e.chunk.setIndexBuffer(De),ve.actualFontSize=a.actualFontSize,Se.setContentSize(n.nodeContentSize),this.updateUVs(t),e.vertDirty=!1,ve=null,this._resetProperties()}t.spriteFrame&&t.renderData.updateRenderData(t,t.spriteFrame)}},e.updateUVs=function(t){for(var e=t.renderData,r=e.chunk.vb,a=e.vertexCount,i=e.floatStride,n=e.data,s=3,o=0;o<a;o++){var h=n[o];r[s]=h.u,r[s+1]=h.v,s+=i}},e.updateColor=function(){},e.resetRenderData=function(t){var e=t.renderData;e.dataLength=0,e.resize(0,0)},e.generateVertexData=function(t,e,r,a,i,n,s,o,h){var l=a,c=t.bmfontScale,u=r.vertexBuffer,f=i.width,d=i.height,p=n.width,_=n.height,m=0,g=0,v=0,S=0;s?(m=n.x/f,S=(n.x+_)/f,g=(n.y+p)/d,v=n.y/d,u[l].u=m,u[l].v=v,u[l+1].u=m,u[l+1].v=g,u[l+2].u=S,u[l+2].v=v,u[l+3].u=S,u[l+3].v=g):(m=n.x/f,S=(n.x+p)/f,g=(n.y+_)/d,v=n.y/d,u[l].u=m,u[l].v=g,u[l+1].u=S,u[l+1].v=g,u[l+2].u=m,u[l+2].v=v,u[l+3].u=S,u[l+3].v=v),u[l].x=o,u[l].y=h-_*c,u[l+1].x=o+p*c,u[l+1].y=h-_*c,u[l+2].x=o,u[l+2].y=h,u[l+3].x=o+p*c,u[l+3].y=h},e._updateFontFamily=function(t){var e=t.font;xe=e.spriteFrame,ye=e.fntConfig,I.fontAtlas=e.fontDefDictionary,I.fontAtlas||(t.cacheMode===j.CHAR?I.fontAtlas=me:I.fontAtlas=ge),M.packToDynamicAtlas(t,xe)},e._updateLabelInfo=function(){I.hash="",I.margin=0},e._resetProperties=function(){ye=null,xe=null,I.hash="",I.margin=0},e.createQuadIndices=function(t){if(t%6==0){var e=t/6;De=new Uint16Array(t);for(var r=0,a=0;a<e;a++)De[r++]=0+4*a,De[r++]=1+4*a,De[r++]=2+4*a,De[r++]=1+4*a,De[r++]=3+4*a,De[r++]=2+4*a}else et(16308)},t}(),we=new p(255,255,255,255),Te=function(t){function e(){return t.apply(this,arguments)||this}Q(e,t);var r=e.prototype;return r.createData=function(t){var e=t.requestRenderData();return e.resize(0,0),e},r.fillBuffers=function(t){var e=t.node;we.set(t.color),we.a=255*e._uiProps.opacity,qt(e,0,t.renderData,we)},e}(Ce),be=new Te,Me=null,Be=function(t){function e(){return t.apply(this,arguments)||this}Q(e,t);var r=e.prototype;return r.getAssemblerData=function(){return Me||(Me=new X(1024,1024)),Me.getTexture()},r._updateFontFamily=function(t){I.fontAtlas=Me,I.fontFamily=this._getFontFamily(t),t.enableOutline&&t.outlineWidth>0?(I.isOutlined=!0,I.margin=t.outlineWidth,I.out=t.outlineColor.clone(),I.out.a=t.outlineColor.a*t.color.a/255):(I.isOutlined=!1,I.margin=0)},r._getFontFamily=function(t){var e="Arial";return t.useSystemFont?e=t.fontFamily||"Arial":t.font&&(e=t.font._nativeAsset||"Arial"),e},r._updateLabelInfo=function(t){I.fontDesc=this._getFontDesc(),I.color.set(t.color),I.hash=G(I)},r._getFontDesc=function(){return I.fontSize.toString()+"px "+I.fontFamily},e}(Ce),Le=new p(255,255,255,255),Fe=function(t){function e(){return t.apply(this,arguments)||this}Q(e,t);var r=e.prototype;return r.createData=function(t){var e=t.requestRenderData();return e.resize(0,0),e},r.fillBuffers=function(t){if(t.renderData){var e=t.node;Le.a=255*e._uiProps.opacity,qt(e,0,t.renderData,Le)}},r.updateColor=function(){},e}(Be),Ie=new Fe,Ee=L.Overflow,Ae=function(){function t(){}var e=t.prototype;return e.updateProcessingData=function(t,e,r,a,i,n){t.isSystemFontUsed=i.useSystemFont,t.fontSize=i.fontSize,r.nodeContentSize.width=r.canvasSize.width=n.width,r.nodeContentSize.height=r.canvasSize.height=n.height,e.lineHeight=i.lineHeight,e.overFlow=i.overflow,i.overflow===Ee.NONE?e.wrapping=!1:i.overflow===Ee.RESIZE_HEIGHT?e.wrapping=!0:e.wrapping=i.enableWrapText,t.isBold=i.isBold,t.isItalic=i.isItalic,t.isUnderline=i.isUnderline,t.underlineHeight=i.underlineHeight,i.enableOutline&&i.outlineWidth>0?(t.isOutlined=!0,t.outlineColor.set(i.outlineColor),t.outlineWidth=i.outlineWidth):t.isOutlined=!1,i.enableShadow&&(i.shadowBlur>0||!_(i.shadowOffset.x,0)||!_(i.shadowOffset.y,0))?(t.hasShadow=!0,t.shadowColor.set(i.shadowColor),t.shadowBlur=i.shadowBlur,t.shadowOffsetX=i.shadowOffset.x,t.shadowOffsetY=i.shadowOffset.y):t.hasShadow=!1,t.color.set(i.color),a.texture=i.spriteFrame,a.uiTransAnchorX=n.anchorX,a.uiTransAnchorY=n.anchorY,e.horizontalAlign=i.horizontalAlign,e.verticalAlign=i.verticalAlign},e.getAssemblerData=function(){var t=L._canvasPool.get();return t.canvas.width=t.canvas.height=1,t},e.resetAssemblerData=function(t){t&&L._canvasPool.put(t)},e.updateRenderData=function(t){if(t.renderData){if(t.renderData.vertDirty){var e=t.node._getUITransformComp(),r=_e.instance,a=t.textStyle,i=t.textLayout,n=t.textLayoutData,s=t.textRenderData;a.fontScale=At.getScaleX(),this.updateProcessingData(a,i,n,s,t,e),r.setCanvasUsed(t.assemblerData.canvas,t.assemblerData.context),a.fontFamily=this._updateFontFamily(t),this._resetDynamicAtlas(t),r.processingString(!1,a,i,n,t.string),r.generateRenderInfo(!1,a,i,n,s,t.string,this.generateVertexData);var o=t.renderData;o.textureDirty=!0,this._calDynamicAtlas(t,n),t.actualFontSize=a.actualFontSize,e.setContentSize(n.nodeContentSize);var h=o.data;h[0]=s.vertexBuffer[0],h[1]=s.vertexBuffer[1],h[2]=s.vertexBuffer[2],h[3]=s.vertexBuffer[3],this.updateUVs(t),t.renderData.vertDirty=!1,t.contentWidth=n.nodeContentSize.width}t.spriteFrame&&t.renderData.updateRenderData(t,t.spriteFrame)}},e.generateVertexData=function(t,e,r){var a=r.vertexBuffer,i=e.nodeContentSize.width,n=e.nodeContentSize.height,s=r.uiTransAnchorX*i,o=r.uiTransAnchorY*n;a[0].x=-s,a[0].y=-o,a[1].x=i-s,a[1].y=-o,a[2].x=-s,a[2].y=n-o,a[3].x=i-s,a[3].y=n-o},e.updateVertexData=function(){},e.updateUVs=function(){},e._updateFontFamily=function(t){return t.useSystemFont?t.fontFamily||"Arial":t.font&&t.font._nativeAsset||"Arial"},e._calDynamicAtlas=function(t,e){if(!(t.cacheMode!==j.BITMAP||e.canvasSize.width<=0||e.canvasSize.height<=0)){var r=t.ttfSpriteFrame;M.packToDynamicAtlas(t,r)}},e._resetDynamicAtlas=function(t){if(t.cacheMode===j.BITMAP){var e=t.ttfSpriteFrame;M.deleteAtlasSpriteFrame(e),e._resetDynamicAtlasFrame()}},t}(),Re=p.WHITE.clone(),Oe=Uint16Array.from([0,1,2,1,3,2]),He=function(t){function e(){return t.apply(this,arguments)||this}Q(e,t);var r=e.prototype;return r.createData=function(t){var e=t.requestRenderData();e.dataLength=4,e.resize(4,6),t.textRenderData.quadCount=4;for(var r=e.chunk.vb,a=e.floatStride,i=[{u:0,v:1},{u:1,v:1},{u:0,v:0},{u:1,v:0}],n=3,s=0,o=e.dataLength;s<o;++s)r[n]=i[s].u,r[n+1]=i[s].v,n+=a;for(var h=5,l=0;l<e.dataLength;l++)p.toArray(r,Re,h),h+=a;return e.chunk.setIndexBuffer(Oe),e},r.fillBuffers=function(t){for(var e=t.renderData,r=e.chunk,a=e.data,i=t.node,n=r.vb,s=i.worldMatrix,o=e.floatStride,h=0,l=a.length,c=0;c<l;c++){var u=a[c],f=u.x,d=u.y,p=s.m03*f+s.m07*d+s.m15;p=p?1/p:1,n[0+(h=c*o)]=(s.m00*f+s.m04*d+s.m12)*p,n[h+1]=(s.m01*f+s.m05*d+s.m13)*p,n[h+2]=(s.m02*f+s.m06*d+s.m14)*p}var _=r.vertexOffset,m=r.meshBuffer,g=r.meshBuffer.iData,v=m.indexOffset;g[v++]=_,g[v++]=_+1,g[v++]=_+2,g[v++]=_+2,g[v++]=_+1,g[v++]=_+3,m.indexOffset+=6},r.updateVertexData=function(t){var e=t.renderData;if(e){var r=t.node._getUITransformComp(),a=r.width,i=r.height,n=r.anchorX*a,s=r.anchorY*i,o=e.data;o[0].x=-n,o[0].y=-s,o[1].x=a-n,o[1].y=-s,o[2].x=-n,o[2].y=i-s,o[3].x=a-n,o[3].y=i-s}},r.updateUVs=function(t){var e=t.renderData;if(e&&t.ttfSpriteFrame)for(var r=e.chunk.vb,a=t.ttfSpriteFrame.uv,i=e.floatStride,n=3,s=0;s<e.dataLength;++s){var o=2*s;r[n]=a[o],r[n+1]=a[o+1],n+=i}},r.updateColor=function(){},e}(Ae),Pe=new He,ze=t("labelAssembler",{getAssembler:function(t){var e=Pe;return t.font instanceof q?e=be:t.cacheMode===j.CHAR&&(e=Ie),e}});L.Assembler=ze;var ke=B.FillType,Ue=new m,Ve=Uint16Array.from([0,1,2,1,3,2]),Ne=function(){function t(){}var e=t.prototype;return e.updateRenderData=function(t){var e=t.spriteFrame;M.packToDynamicAtlas(t,e);var r=t.renderData;if(r&&e){if(!r.vertDirty)return;var a=t.fillStart,i=t.fillRange;i<0&&(a+=i,i=-i),i=(i=(i=a+i)>1?1:i)<0?0:i;var n=(a=(a=a>1?1:a)<0?0:a)+(i=(i-=a)<0?0:i);n=n>1?1:n,this.updateUVs(t,a,n),this.updateVertexData(t,a,n),r.updateRenderData(t,e)}},e.updateUVs=function(t,e,r){var a=t.spriteFrame,i=t.renderData,n=i.chunk.vb,s=a.width,o=a.height,h=a.rect,l=0,c=0,u=0,f=0,d=0,p=0,_=0,m=0,g=0,v=0;a.isRotated()?(l=h.x/s,c=(h.y+h.width)/o,u=d=l,_=g=(h.x+h.height)/s,p=v=c,f=m=h.y/o):(l=h.x/s,c=(h.y+h.height)/o,u=_=l,d=g=(h.x+h.width)/s,f=p=c,m=v=h.y/o);var S=i.floatStride,y=3;switch(t.fillType){case ke.HORIZONTAL:n[y]=u+(d-u)*e,n[y+1]=f+(p-f)*e,n[y+=S]=u+(d-u)*r,n[y+1]=f+(p-f)*r,n[y+=S]=_+(g-_)*e,n[y+1]=m+(v-m)*e,n[y+=S]=_+(g-_)*r,n[y+1]=m+(v-m)*r;break;case ke.VERTICAL:n[y]=u+(_-u)*e,n[y+1]=f+(m-f)*e,n[y+=S]=d+(g-d)*e,n[y+1]=p+(v-p)*e,n[y+=S]=u+(_-u)*r,n[y+1]=f+(m-f)*r,n[y+=S]=d+(g-d)*r,n[y+1]=p+(v-p)*r;break;default:et(2626)}},e.updateVertexData=function(t,e,r){var a=t.renderData.data,i=t.node._getUITransformComp(),n=i.width,s=i.height,o=i.anchorX*n,h=i.anchorY*s,l=-o,c=-h,u=n-o,f=s-h,d=0;switch(t.fillType){case ke.HORIZONTAL:d=l+(u-l)*r,l+=(u-l)*e,u=d;break;case ke.VERTICAL:d=c+(f-c)*r,c+=(f-c)*e,f=d;break;default:et(2626)}a[0].x=l,a[0].y=c,a[1].x=u,a[1].y=c,a[2].x=l,a[2].y=f,a[3].x=u,a[3].y=f},e.createData=function(t){var e=t.requestRenderData();return e.dataLength=4,e.resize(4,6),e.chunk.setIndexBuffer(Ve),e.data.forEach((function(t){t.z=0})),e},e.updateWorldVertexData=function(t,e){t.node.getWorldMatrix(Ue);for(var r=t.renderData.floatStride,a=t.renderData.data,i=e.vb,n=0,s=0;s<4;s++){var o=a[s],h=o.x,l=o.y,c=Ue.m03*h+Ue.m07*l+Ue.m15;c=c?1/c:1,i[n=s*r]=(Ue.m00*h+Ue.m04*l+Ue.m12)*c,i[n+1]=(Ue.m01*h+Ue.m05*l+Ue.m13)*c,i[n+2]=(Ue.m02*h+Ue.m06*l+Ue.m14)*c}},e.fillBuffers=function(t){var e=t.renderData,r=e.chunk;(t._flagChangedVersion!==t.node.flagChangedVersion||e.vertDirty)&&(this.updateWorldVertexData(t,r),e.vertDirty=!1,t._flagChangedVersion=t.node.flagChangedVersion),r.bufferId;var a=r.vertexOffset,i=r.meshBuffer,n=r.meshBuffer.iData,s=i.indexOffset;n[s++]=a,n[s++]=a+1,n[s++]=a+2,n[s++]=a+2,n[s++]=a+1,n[s++]=a+3,i.indexOffset+=6},e.updateColor=function(t){for(var e=t.renderData,r=e.chunk.vb,a=e.floatStride,i=5,n=t.color,s=n.r/255,o=n.g/255,h=n.b/255,l=t.node._uiProps.opacity,c=0;c<4;c++)r[i]=s,r[i+1]=o,r[i+2]=h,r[i+3]=l,i+=a},t}(),We=new Ne,Xe=2*Math.PI,je=1e-6,Ye=new m,Ge=[new d,new d,new d,new d],qe=new Array(4),Qe=new Array(8),Ke=[new d,new d,new d,new d],Ze=[new d,new d,new d,new d],Je=new d,$e=[new d,new d,new d,new d],tr=null;function er(t,e,r,a,i,n,s){var o=Math.sin(n);o=Math.abs(o)>je?o:0;var h=Math.cos(n),l=0,c=0;if(0!==(h=Math.abs(h)>je?h:0)){if(l=o/h,(t-i.x)*h>0){var u=i.y+l*(t-i.x);s[0].x=t,s[0].y=u}if((e-i.x)*h>0){var f=i.y+l*(e-i.x);s[2].x=e,s[2].y=f}}if(0!==o){if(c=h/o,(a-i.y)*o>0){var d=i.x+c*(a-i.y);s[3].x=d,s[3].y=a}if((r-i.y)*o>0){var p=i.x+c*(r-i.y);s[1].x=p,s[1].y=r}}}function rr(t){var e=t.node._getUITransformComp(),r=e.width,a=e.height,i=e.anchorX*r,n=e.anchorY*a,s=-i,o=-n,h=r-i,l=a-n,c=qe;c[0]=s,c[1]=o,c[2]=h,c[3]=l;var u=t.fillCenter,f=Je.x=Math.min(Math.max(0,u.x),1)*(h-s)+s,p=Je.y=Math.min(Math.max(0,u.y),1)*(l-o)+o;Ge[0].x=Ge[3].x=s,Ge[1].x=Ge[2].x=h,Ge[0].y=Ge[1].y=o,Ge[2].y=Ge[3].y=l,$e.forEach((function(t){d.set(t,0,0)})),f!==c[0]&&d.set($e[0],3,0),f!==c[2]&&d.set($e[2],1,2),p!==c[1]&&d.set($e[1],0,1),p!==c[3]&&d.set($e[3],2,3)}function ar(t,e){var r=e.x-t.x,a=e.y-t.y;if(0===r&&0===a)return 0;if(0===r)return a>0?.5*Math.PI:1.5*Math.PI;var i=Math.atan(a/r);return r<0&&(i+=Math.PI),i}function ir(t,e,r,a,i){var n=qe,s=n[0],o=n[1],h=n[2],l=n[3];t[e].x=r.x,t[e].y=r.y,t[e+1].x=a.x,t[e+1].y=a.y,t[e+2].x=i.x,t[e+2].y=i.y,nr((r.x-s)/(h-s),(r.y-o)/(l-o),t,e),nr((a.x-s)/(h-s),(a.y-o)/(l-o),t,e+1),nr((i.x-s)/(h-s),(i.y-o)/(l-o),t,e+2)}function nr(t,e,r,a){var i=Qe,n=i[0]+(i[2]-i[0])*t,s=i[4]+(i[6]-i[4])*t,o=i[1]+(i[3]-i[1])*t,h=i[5]+(i[7]-i[5])*t,l=r[a];l.u=n+(s-n)*e,l.v=o+(h-o)*e}for(var sr=function(){function t(){}var e=t.prototype;return e.createData=function(t){return t.requestRenderData()},e.updateRenderData=function(t){var e=t.spriteFrame;M.packToDynamicAtlas(t,e),this.updateUVs(t);var r,a,i,n,s,o,h,l,c,u=t.renderData;if(u&&e){if(!u.vertDirty)return;var f=u.data,d=t.fillStart,p=t.fillRange;for(p<0&&(d+=p,p=-p);d>=1;)d-=1;for(;d<0;)d+=1;var _=(d*=Xe)+(p*=Xe);rr(t),a=(r=e).width,i=r.height,n=r.getRect(),s=0,o=0,h=0,l=0,c=Qe,r.isRotated()?(s=n.x/a,o=(n.x+n.height)/a,h=n.y/i,l=(n.y+n.width)/i,c[0]=c[2]=s,c[4]=c[6]=o,c[3]=c[7]=l,c[1]=c[5]=h):(s=n.x/a,o=(n.x+n.width)/a,h=n.y/i,l=(n.y+n.height)/i,c[0]=c[4]=s,c[2]=c[6]=o,c[1]=c[3]=l,c[5]=c[7]=h),er(qe[0],qe[2],qe[1],qe[3],Je,d,Ke),er(qe[0],qe[2],qe[1],qe[3],Je,d+p,Ze);for(var m=0,g=0;g<4;++g){var v=$e[g];if(v)if(p>=Xe)u.dataLength=m+3,ir(f,m,Je,Ge[v.x],Ge[v.y]),m+=3;else{var S=ar(Je,Ge[v.x]),y=ar(Je,Ge[v.y]);y<S&&(y+=Xe),S-=Xe,y-=Xe;for(var x=0;x<3;++x)S>=_||(S>=d?(u.dataLength=m+3,ir(f,m,Je,Ge[v.x],y>=_?Ze[g]:Ge[v.y]),m+=3):y>d&&(y<=_?(u.dataLength=m+3,ir(f,m,Je,Ke[g],Ge[v.y]),m+=3):(u.dataLength=m+3,ir(f,m,Je,Ke[g],Ze[g]),m+=3))),S+=Xe,y+=Xe}}0===m&&(u.dataLength=0),u.resize(m,m),u.updateRenderData(t,e)}},e.createQuadIndices=function(t){tr=null,tr=new Uint16Array(t);for(var e=0,r=0;r<t;r++)tr[e++]=r},e.fillBuffers=function(t){var e=t.node,r=t.renderData,a=r.chunk;(t._flagChangedVersion!==e.flagChangedVersion||r.vertDirty)&&(this.updateWorldVertexAndUVData(t,a),r.vertDirty=!1,t._flagChangedVersion=e.flagChangedVersion),this.updateColorLate(t),a.bufferId;for(var i=a.vertexOffset,n=a.meshBuffer,s=a.meshBuffer.iData,o=n.indexOffset,h=0;h<r.indexCount;h++)s[o+h]=i+h;n.indexOffset+=r.indexCount,n.setDirty()},e.updateWorldUVData=function(t){for(var e=t.renderData,r=e.floatStride,a=e.data,i=e.chunk.vb,n=0;n<a.length;n++){var s=n*r;i[s+3]=a[n].u,i[s+4]=a[n].v}},e.updateWorldVertexAndUVData=function(t,e){t.node.getWorldMatrix(Ye);for(var r=t.renderData,a=r.floatStride,i=t.renderData.data,n=e.vb,s=r.vertexCount,o=0,h=0;h<s;h++){var l=i[h],c=l.x,u=l.y,f=Ye.m03*c+Ye.m07*u+Ye.m15;f=f?1/f:1,n[o+0]=(Ye.m00*c+Ye.m04*u+Ye.m12)*f,n[o+1]=(Ye.m01*c+Ye.m05*u+Ye.m13)*f,n[o+2]=(Ye.m02*c+Ye.m06*u+Ye.m14)*f,n[o+3]=l.u,n[o+4]=l.v,o+=a}},e.updateUVs=function(t){t.renderData.vertDirty=!0,t._markForUpdateRenderData()},e.updateColorLate=function(t){for(var e=t.renderData,r=e.chunk.vb,a=e.floatStride,i=e.vertexCount,n=5,s=t.color,o=s.r/255,h=s.g/255,l=s.b/255,c=t.node._uiProps.opacity,u=0;u<i;u++)r[n]=o,r[n+1]=h,r[n+2]=l,r[n+3]=c,n+=a},e.updateColor=function(){},t}(),or=new sr,hr=Uint16Array.from([0,1,2,1,3,2]),lr=function(){function t(){}var e=t.prototype;return e.createData=function(t){var e=t.requestRenderData();return e.dataLength=4,e.resize(4,6),e.chunk.setIndexBuffer(hr),e},e.updateRenderData=function(t){var e=t.spriteFrame;M.packToDynamicAtlas(t,e),this.updateUVs(t);var r=t.renderData;r&&e&&(r.vertDirty&&this.updateVertexData(t),r.updateRenderData(t,e))},e.updateWorldVerts=function(t,e){var r=t.renderData;if(r)for(var a=e.vb,i=r.data,n=t.node.worldMatrix,s=n.m00,o=n.m01,h=n.m02,l=n.m03,c=n.m04,u=n.m05,f=n.m06,d=n.m07,p=n.m12,_=n.m13,m=n.m14,g=n.m15,v=r.floatStride,S=0,y=i.length,x=0;x<y;++x){var D=i[x],C=D.x,w=D.y,T=l*C+d*w+g;T=T?1/T:1,a[0+(S=x*v)]=(s*C+c*w+p)*T,a[S+1]=(o*C+u*w+_)*T,a[S+2]=(h*C+f*w+m)*T}},e.fillBuffers=function(t){if(null!==t){var e=t.renderData;if(e){var r=e.chunk;(t._flagChangedVersion!==t.node.flagChangedVersion||e.vertDirty)&&(this.updateWorldVerts(t,r),e.vertDirty=!1,t._flagChangedVersion=t.node.flagChangedVersion);var a=r.vertexOffset,i=r.meshBuffer,n=r.meshBuffer.iData,s=i.indexOffset,o=a;n[s++]=o,n[s++]=o+1,n[s++]=o+2,n[s++]=o+1,n[s++]=o+3,n[s++]=o+2,i.indexOffset+=6}}},e.updateVertexData=function(t){var e=t.renderData;if(e){var r=t.node._getUITransformComp(),a=e.data,i=r.width,n=r.height,s=r.anchorX*i,o=r.anchorY*n,h=0,l=0,c=0,u=0;if(t.trim)h=-s,l=-o,c=i-s,u=n-o;else{var f=t.spriteFrame,d=f.originalSize,p=i/d.width,_=n/d.height,m=f.trimmedBorder;h=m.x*p-s,l=m.z*_-o,c=i+m.y*p-s,u=n+m.w*_-o}a[0].x=h,a[0].y=l,a[1].x=c,a[1].y=l,a[2].x=h,a[2].y=u,a[3].x=c,a[3].y=u,e.vertDirty=!0}},e.updateUVs=function(t){var e=t.renderData;if(t.spriteFrame&&e)for(var r=e.chunk.vb,a=t.spriteFrame.uv,i=e.floatStride,n=3,s=0;s<e.dataLength;++s){var o=2*s;r[n]=a[o],r[n+1]=a[o+1],n+=i}},e.updateColor=function(t){var e=t.renderData;if(e)for(var r=e.chunk.vb,a=5,i=t.color,n=i.r/255,s=i.g/255,o=i.b/255,h=i.a/255,l=0;l<4;l++,a+=e.floatStride)r[a]=n,r[a+1]=s,r[a+2]=o,r[a+3]=h},t}(),cr=new lr,ur=[],fr=0;fr<4;fr++)ur.push({x:0,y:0,z:0,u:0,v:0,color:new p});var dr,pr,_r,mr,gr,vr,Sr,yr=function(){function t(){this.QUAD_INDICES=void 0}var e=t.prototype;return e.createData=function(t){var e=t.requestRenderData();e.dataLength=16,e.resize(16,54);var r=this.QUAD_INDICES=new Uint16Array(54);return this.createQuadIndices(4,4),e.chunk.setIndexBuffer(r),e},e.createQuadIndices=function(t,e){for(var r=0,a=this.QUAD_INDICES,i=0;i<t-1;i++)for(var n=0;n<e-1;n++){var s=i*e+n;a[r++]=s,a[r++]=s+1,a[r++]=s+e,a[r++]=s+1,a[r++]=s+1+e,a[r++]=s+e}},e.updateRenderData=function(t){var e=t.spriteFrame;M.packToDynamicAtlas(t,e),this.updateUVs(t);var r=t.renderData;r&&e&&(r.vertDirty&&this.updateVertexData(t),r.updateRenderData(t,e))},e.updateVertexData=function(t){var e=t.renderData;if(e){var r=e.data,a=t.node._getUITransformComp(),i=a.width,n=a.height,s=a.anchorX*i,o=a.anchorY*n,h=t.spriteFrame,l=h.insetLeft,c=h.insetRight,u=h.insetTop,f=h.insetBottom,d=i-l-c,p=n-u-f,_=i/(l+c),m=n/(u+f);_=Number.isNaN(_)||_>1?1:_,m=Number.isNaN(m)||m>1?1:m,d=d<0?0:d,p=p<0?0:p,ur[0].x=-s,ur[0].y=-o,ur[1].x=l*_-s,ur[1].y=f*m-o,ur[2].x=ur[1].x+d,ur[2].y=ur[1].y+p,ur[3].x=i-s,ur[3].y=n-o;for(var g=0;g<4;g++)for(var v=0;v<4;v++){var S=4*g+v;S<e.dataLength&&g<ur.length&&v<ur.length&&(r[S].x=ur[v].x,r[S].y=ur[g].y)}}},e.fillBuffers=function(t){var e=t.renderData;if(e){var r=e.chunk;(t._flagChangedVersion!==t.node.flagChangedVersion||e.vertDirty)&&(this.updateWorldVertexData(t,r),e.vertDirty=!1,t._flagChangedVersion=t.node.flagChangedVersion),r.bufferId;for(var a=r.vertexOffset,i=r.meshBuffer,n=r.meshBuffer.iData,s=i.indexOffset,o=0;o<3;++o)for(var h=0;h<3;++h){var l=a+4*o+h;n[s++]=l,n[s++]=l+1,n[s++]=l+4,n[s++]=l+1,n[s++]=l+5,n[s++]=l+4}i.indexOffset=s}},e.updateWorldVertexData=function(t,e){var r=t.renderData;if(r)for(var a=r.floatStride,i=r.data,n=e.vb,s=t.node.worldMatrix,o=s.m00,h=s.m01,l=s.m02,c=s.m03,u=s.m04,f=s.m05,d=s.m06,p=s.m07,_=s.m12,m=s.m13,g=s.m14,v=s.m15,S=0,y=0;y<4;++y)for(var x=i[4*y],D=0;D<4;++D){var C=i[D].x,w=x.y,T=c*C+p*w+v;T=T?1/T:1,n[0+(S=(4*y+D)*a)]=(o*C+u*w+_)*T,n[S+1]=(h*C+f*w+m)*T,n[S+2]=(l*C+d*w+g)*T}},e.updateUVs=function(t){var e=t.renderData;if(t.spriteFrame&&e)for(var r=e.chunk.vb,a=e.floatStride,i=t.spriteFrame.uvSliced,n=3,s=0;s<16;s++)r[n]=i[s].u,r[n+1]=i[s].v,n+=a},e.updateColor=function(t){var e=t.renderData;if(e)for(var r=e.chunk.vb,a=e.floatStride,i=5,n=t.color,s=n.r/255,o=n.g/255,h=n.b/255,l=t.node._uiProps.opacity,c=0;c<16;c++)r[i]=s,r[i+1]=o,r[i+2]=h,r[i+3]=l,i+=a},t}(),xr=new yr,Dr=new m,Cr=0,wr=[],Tr=null;function br(t){return t&&(t.insetTop>0||t.insetBottom>0||t.insetLeft>0||t.insetRight>0)?2:0}var Mr=function(){function t(){}var e=t.prototype;return e.createData=function(t){return t.requestRenderData()},e.updateRenderData=function(t){var e=t.renderData;if(e){var r=t.spriteFrame;if(r&&e&&e.vertDirty){var a=t.node._getUITransformComp(),i=Math.abs(a.width),n=Math.abs(a.height),s=r.getRect(),o=r.insetLeft,h=r.insetRight,l=s.width-o-h,c=r.insetTop,u=r.insetBottom,f=s.height-c-u,d=i-o-h,p=n-c-u;d=d>0?d:0,p=p>0?p:0;var _=0===l?d:d/l,m=0===f?p:p/f,g=br(r),v=Math.ceil(m+g),S=Math.ceil(_+g);e.dataLength=4*v*S,this.updateVerts(t,d,p,v,S),e.vertexCount!==v*S*4&&(t.renderEntity.colorDirty=!0),e.resize(v*S*4,v*S*6),e.updateRenderData(t,r)}}},e.createQuadIndices=function(t){if(t%6==0){var e=t/6;Tr=new Uint16Array(t);for(var r=0,a=0;a<e;a++)Tr[r++]=0+4*a,Tr[r++]=1+4*a,Tr[r++]=2+4*a,Tr[r++]=1+4*a,Tr[r++]=3+4*a,Tr[r++]=2+4*a}else et(16308)},e.updateUVs=function(t){var e=t.renderData;e&&(e.vertDirty=!0,t._markForUpdateRenderData())},e.fillBuffers=function(t){var e=t.node,r=t.renderData;if(r){var a=r.chunk;if(null!==a){(t._flagChangedVersion!==e.flagChangedVersion||r.vertDirty)&&(this.updateWorldVertexAndUVData(t,a),r.vertDirty=!1,t._flagChangedVersion=e.flagChangedVersion),this.updateColorLate(t),a.bufferId;for(var i=a.vertexOffset,n=a.meshBuffer,s=a.meshBuffer.iData,o=n.indexOffset,h=0;h<r.indexCount;h+=6)s[o++]=i,s[o++]=i+1,s[o++]=i+2,s[o++]=i+1,s[o++]=i+3,s[o++]=i+2,i+=4,n.indexOffset+=6;n.setDirty()}}},e.updateWorldUVData=function(t){var e=t.renderData;if(e)for(var r=e.floatStride,a=e.data,i=e.chunk.vb,n=0;n<a.length;n++){var s=n*r;i[s+3]=a[n].u,i[s+4]=a[n].v}},e.updateWorldVertexAndUVData=function(t,e){var r=t.renderData;if(r){t.node.getWorldMatrix(Dr);for(var a=r.floatStride,i=r.data,n=e.vb,s=i.length,o=0;o<s;o++){var h=i[o].x,l=i[o].y,c=i[o].z,u=Dr.m03*h+Dr.m07*l+Dr.m11*c+Dr.m15;u=u?1/u:1;var f=o*a;n[f]=(Dr.m00*h+Dr.m04*l+Dr.m08*c+Dr.m12)*u,n[f+1]=(Dr.m01*h+Dr.m05*l+Dr.m09*c+Dr.m13)*u,n[f+2]=(Dr.m02*h+Dr.m06*l+Dr.m10*c+Dr.m14)*u}this.updateWorldUVData(t)}},e.updateVerts=function(t,e,r,a,i){var n=t.node._getUITransformComp(),s=t.renderData;if(s){var o,h,l=s.data,c=t.spriteFrame,u=c.rect,f=Math.abs(n.width),d=Math.abs(n.height),_=n.anchorX*f,m=n.anchorY*d,g=c.insetLeft,v=c.insetRight,S=u.width-g-v,y=c.insetTop,x=c.insetBottom,D=u.height-y-x,C=n.width/(g+v)>1?1:n.width/(g+v),w=n.height/(y+x)>1?1:n.height/(y+x);o=S>0?Math.floor(1e3*e)/1e3%S==0?S:e%S:e,h=D>0?Math.floor(1e3*r)/1e3%D==0?D:r%D:r,wr.length=0,Cr=Math.max(a+1,i+1);for(var T=0;T<Cr;T++)wr.push({x:0,y:0,z:0,u:0,v:0,color:new p});var b=br(c);if(0===b)for(var M=0;M<Cr;M++)wr[M].x=M>=i?f-_:M*S-_,wr[M].y=M>=a?d-m:M*D-m;else for(var B=0;B<Cr;B++)0===B?wr[B].x=-_:1===B?wr[B].x=g*C-_:B>1&&B<i-1?wr[B].x=S>0?g*C-_+S*(B-1):g+e-_:B===i-1?wr[B].x=g*C-_+o+S*(B-2):B>=i&&(wr[B].x=Math.min(g+e+v,f)-_),0===B?wr[B].y=-m:1===B?wr[B].y=x*w-m:B>1&&B<a-1?wr[B].y=D>0?x*w-m+D*(B-1):x+r-m:B===a-1?wr[B].y=x*w-m+h+D*(B-2):B>=a&&(wr[B].y=Math.min(x+r+y,d)-m);for(var L=0,F=0,I=0,E=0,A=0;A<a;++A){I=wr[A].y,E=wr[A+1].y;for(var R=0;R<i;++R){L=wr[R].x,F=wr[R+1].x;var O=4*(A*i+R);l[O].x=L,l[O].y=I,l[O+1].x=F,l[O+1].y=I,l[O+2].x=L,l[O+2].y=E,l[O+3].x=F,l[O+3].y=E}}var H=c.rotated;c.uv;var P=c.uvSliced;dr=P[0],pr=P[1],_r=P[2],mr=P[3],gr=P[4],vr=P[8],Sr=P[12];for(var z=0,k=0,U=0===S?e:e/S,V=0===D?r:r/D,N=[],W=[],X=0;X<a;++X){k=r>D?r>=(b>0?X:X+1)*D?1:V%1:V;for(var j=0;j<i;++j){z=e>S?e>=(b>0?j:j+1)*S?1:U%1:U,H?(0===b?(N[0]=gr.u,N[1]=gr.u,N[2]=gr.u+(vr.u-gr.u)*k,W[0]=pr.v,W[1]=pr.v+(_r.v-pr.v)*z,W[2]=pr.v):(0===X?(N[0]=dr.u,N[1]=dr.u,N[2]=gr.u):X<a-1?(N[0]=gr.u,N[1]=gr.u,N[2]=gr.u+(vr.u-gr.u)*k):X===a-1&&(N[0]=vr.u,N[1]=vr.u,N[2]=Sr.u),0===j?(W[0]=dr.v,W[1]=pr.v,W[2]=dr.v):j<i-1?(W[0]=pr.v,W[1]=pr.v+(_r.v-pr.v)*z,W[2]=pr.v):j===i-1&&(W[0]=_r.v,W[1]=mr.v,W[2]=_r.v)),N[3]=N[2],W[3]=W[1]):(0===b?(N[0]=pr.u,N[1]=pr.u+(_r.u-pr.u)*z,N[2]=pr.u,W[0]=gr.v,W[1]=gr.v,W[2]=gr.v+(vr.v-gr.v)*k):(0===j?(N[0]=dr.u,N[1]=pr.u,N[2]=dr.u):j<i-1?(N[0]=pr.u,N[1]=pr.u+(_r.u-pr.u)*z,N[2]=pr.u):j===i-1&&(N[0]=_r.u,N[1]=mr.u,N[2]=_r.u),0===X?(W[0]=dr.v,W[1]=dr.v,W[2]=gr.v):X<a-1?(W[0]=gr.v,W[1]=gr.v,W[2]=gr.v+(vr.v-gr.v)*k):X===a-1&&(W[0]=vr.v,W[1]=vr.v,W[2]=Sr.v)),N[3]=N[1],W[3]=W[2]);var Y=4*(X*i+j);l[Y].u=N[0],l[Y].v=W[0],l[Y+1].u=N[1],l[Y+1].v=W[1],l[Y+2].u=N[2],l[Y+2].v=W[2],l[Y+3].u=N[3],l[Y+3].v=W[3]}}}},e.updateColorLate=function(t){var e=t.renderData;if(e)for(var r=e.chunk.vb,a=e.floatStride,i=e.vertexCount,n=5,s=t.color,o=s.r/255,h=s.g/255,l=s.b/255,c=t.node._uiProps.opacity,u=0;u<i;u++)r[n]=o,r[n+1]=h,r[n+2]=l,r[n+3]=c,n+=a},e.updateColor=function(){},t}(),Br=new Mr,Lr=B.Type,Fr=B.FillType,Ir=t("spriteAssembler",{getAssembler:function(t){var e=cr,r=t;switch(r.type){case Lr.SLICED:e=xr;break;case Lr.TILED:e=Br;break;case Lr.FILLED:e=r.fillType===Fr.RADIAL?or:We}return e}});B.Assembler=Ir;var Er=[Nt.MOUSE_DOWN,Nt.MOUSE_MOVE,Nt.MOUSE_UP,Nt.MOUSE_WHEEL,Nt.MOUSE_LEAVE,Nt.MOUSE_ENTER],Ar=[Nt.TOUCH_START,Nt.TOUCH_MOVE,Nt.TOUCH_END,Nt.TOUCH_CANCEL],Rr=function(){function t(){this.priority=Ut.UI,this._isListDirty=!1,this._inDispatchCount=0,this._pointerEventProcessorList=[],this._processorListToAdd=[],this._processorListToRemove=[],Vt._registerEventDispatcher(this);var t=Lt.callbacksInvoker;t.on(Ft.ADD_POINTER_EVENT_PROCESSOR,this.addPointerEventProcessor,this),t.on(Ft.REMOVE_POINTER_EVENT_PROCESSOR,this.removePointerEventProcessor,this),t.on(Ft.MARK_LIST_DIRTY,this._markListDirty,this)}var e=t.prototype;return e.onThrowException=function(){this._inDispatchCount=0},e.dispatchEvent=function(t){var e=t.type;return Ar.includes(e)?this.dispatchEventTouch(t):!Er.includes(e)||this.dispatchEventMouse(t)},e.addPointerEventProcessor=function(t){0===this._inDispatchCount?this._pointerEventProcessorList.includes(t)||(this._pointerEventProcessorList.push(t),this._isListDirty=!0):this._processorListToAdd.includes(t)||this._processorListToAdd.push(t),rt(this._processorListToRemove,t)},e.removePointerEventProcessor=function(t){0===this._inDispatchCount?(rt(this._pointerEventProcessorList,t),this._isListDirty=!0):this._processorListToRemove.includes(t)||this._processorListToRemove.push(t),rt(this._processorListToAdd,t)},e.dispatchEventMouse=function(t){this._inDispatchCount++,this._sortPointerEventProcessorList();for(var e=this._pointerEventProcessorList,r=e.length,a=!0,i=0;i<r;++i){var n=e[i];if(n.isEnabled&&n.shouldHandleEventMouse&&n._handleEventMouse(t)){if(a=!1,!t.preventSwallow)break;t.preventSwallow=!1}}return--this._inDispatchCount<=0&&this._updatePointerEventProcessorList(),a},e.dispatchEventTouch=function(t){this._inDispatchCount++,this._sortPointerEventProcessorList();for(var e=this._pointerEventProcessorList,r=e.length,a=t.touch,i=!0,n=0;n<r;++n){var s=e[n];if(s.isEnabled&&s.shouldHandleEventTouch)if(t.type===Nt.TOUCH_START){if(s._handleEventTouch(t)){if(s.isEnabled)s.claimedTouchIdList.push(a.getID());else{var o=new Wt([t.touch],!0,Nt.TOUCH_CANCEL);o.touch=t.touch,s.dispatchEvent(o),s.claimedTouchIdList.length=0}if(i=!1,!t.preventSwallow)break;t.preventSwallow=!1}}else if(s.claimedTouchIdList.length>0){var h=s.claimedTouchIdList.indexOf(a.getID());if(-1!==h){if(s._handleEventTouch(t),t.type!==Nt.TOUCH_END&&t.type!==Nt.TOUCH_CANCEL||(at(s.claimedTouchIdList,h),t.preventSwallow||this._removeClaimedTouch(n+1,a.getID())),i=!1,!t.preventSwallow)break;t.preventSwallow=!1}}}return--this._inDispatchCount<=0&&this._updatePointerEventProcessorList(),i},e._removeClaimedTouch=function(t,e){for(var r=this._pointerEventProcessorList,a=r.length,i=t;i<a;++i){var n=r[i],s=n.claimedTouchIdList.indexOf(e);-1!==s&&at(n.claimedTouchIdList,s)}},e._updatePointerEventProcessorList=function(){for(var t=this._processorListToAdd,e=t.length,r=0;r<e;++r)this.addPointerEventProcessor(t[r]);t.length=0;for(var a=this._processorListToRemove,i=a.length,n=0;n<i;++n)this.removePointerEventProcessor(a[n]);a.length=0},e._sortPointerEventProcessorList=function(){if(this._isListDirty){for(var t=this._pointerEventProcessorList,e=t.length,r=0;r<e;++r){var a=t[r],i=a.node;if(i._uiProps){var n=i._getUITransformComp();a.cachedCameraPriority=n.cameraPriority}}t.sort(this._sortByPriority),this._isListDirty=!1}},e._sortByPriority=function(t,e){var r=t.node,a=e.node;if(!(e&&a&&a.activeInHierarchy&&a._getUITransformComp()))return-1;if(!(t&&r&&r.activeInHierarchy&&r._getUITransformComp()))return 1;if(t.cachedCameraPriority!==e.cachedCameraPriority)return e.cachedCameraPriority-t.cachedCameraPriority;for(var i=r,n=a,s=!1;(null==(o=i.parent)?void 0:o.uuid)!==(null==(h=n.parent)?void 0:h.uuid);){var o,h,l,c,u,f;i=null===(null==(l=i)||null==(c=l.parent)?void 0:c.parent)?(s=!0)&&a:i&&i.parent,n=null===(null==(u=n)||null==(f=u.parent)?void 0:f.parent)?(s=!0)&&r:n&&n.parent}if(i.uuid===n.uuid){if(i.uuid===a.uuid)return-1;if(i.uuid===r.uuid)return 1}var d=i?i.siblingIndex:0,p=n?n.siblingIndex:0;return s?d-p:p-d},e._markListDirty=function(){this._isListDirty=!0},t}();new Rr;var Or=new T(null),Hr=new m,Pr=t("UI",function(){function t(t){var e=this;this._screens=[],this._staticVBBuffer=null,this._bufferAccessors=new Map,this._currBID=-1,this._indexStart=0,this._emptyMaterial=new Et,this._currRenderData=null,this._currMaterial=this._emptyMaterial,this._currTexture=null,this._currSampler=null,this._currStaticRoot=null,this._currComponent=null,this._currTransform=null,this._currTextureHash=0,this._currSamplerHash=0,this._currLayer=0,this._currDepthStencilStateStage=null,this._currIsStatic=!1,this._currHash=0,this._currIsMiddleware=!1,this._middlewareEnableBatch=!1,this._middlewareBuffer=null,this._middlewareIndexStart=0,this._middlewareIndexCount=0,this._pOpacity=1,this._opacityDirty=0,this._descriptorSetCache=new kr,this._meshDataArray=[],this._maskClearModel=null,this._maskClearMtl=null,this._maskModelMesh=null,this._root=t,this.device=t.device,this._batches=new st(64),this._drawBatchPool=new ot((function(){return new ae}),128,(function(t){return t.destroy(e)}))}var e=t.prototype;return e.initialize=function(){return!0},e.destroy=function(){for(var t=0;t<this._batches.length;t++)this._batches.array[t]&&this._batches.array[t].destroy(this);this._batches.destroy();for(var e,r=it(this._bufferAccessors.values());!(e=r()).done;)e.value.destroy();this._bufferAccessors.clear(),this._drawBatchPool&&this._drawBatchPool.destroy(),this._descriptorSetCache.destroy(),gt.sharedManager.destroy(),this._maskClearModel&&this._maskModelMesh&&(ht.director.root.destroyModel(this._maskClearModel),this._maskModelMesh.destroy()),this._maskClearMtl&&this._maskClearMtl.destroy()},e.syncRootNodesToNative=function(){},e.addScreen=function(t){this._screens.push(t),this._screens.sort(this._screenSort)},e.removeScreen=function(t){var e=this._screens.indexOf(t);-1!==e&&this._screens.splice(e,1)},e.sortScreens=function(){this._screens.sort(this._screenSort)},e.getFirstRenderCamera=function(t){if(t.scene&&t.scene.renderScene)for(var e=t.scene.renderScene.cameras,r=0;r<e.length;r++){var a=e[r];if(a.visibility&t.layer)return a}return null},e.update=function(){for(var t=this._screens,e=0,r=0;r<t.length;++r){var a=t[r],i=a._getRenderScene();if(a.enabledInHierarchy&&i){this._opacityDirty=0,this._pOpacity=1,this.walk(a.node),this.autoMergeBatches(this._currComponent),this.resetRenderStates();var n=0;if(this._batches.length>e)for(;e<this._batches.length;++e){var s=this._batches.array[e];if(s.model)for(var o=s.model.subModels,h=0;h<o.length;h++)o[h].priority=n++;else s.descriptorSet=this._descriptorSetCache.getDescriptorSet(s);i.addBatch(s)}}}},e.uploadBuffers=function(){if(this._batches.length>0){for(var t=this._meshDataArray.length,e=0;e<t;e++)this._meshDataArray[e].uploadBuffers();for(var r,a=it(this._bufferAccessors.values());!(r=a()).done;){var i=r.value;i.uploadBuffers(),i.reset()}this._descriptorSetCache.update()}},e.reset=function(){for(var t=0;t<this._batches.length;++t){var e=this._batches.array[t];e.isStatic||(e.clear(),this._drawBatchPool.free(e))}for(var r,a=it(this._bufferAccessors.values());!(r=a()).done;)r.value.reset();for(var i=this._meshDataArray.length,n=0;n<i;n++)this._meshDataArray[n].freeIAPool();this._meshDataArray.length=0,this._staticVBBuffer=null,this._currBID=-1,this._indexStart=0,this._currHash=0,this._currLayer=0,this._currRenderData=null,this._currMaterial=this._emptyMaterial,this._currTexture=null,this._currSampler=null,this._currComponent=null,this._currTransform=null,this._batches.clear(),gt.sharedManager.reset()},e.switchBufferAccessor=function(t){void 0===t&&(t=vt);var e=t===vt?36:St(t);if(!this._staticVBBuffer||this._staticVBBuffer.vertexFormatBytes!==e){var r=this._bufferAccessors.get(e);r||(r=new yt(this.device,t),this._bufferAccessors.set(e,r)),this._staticVBBuffer=r,this._currBID=-1}return this._staticVBBuffer},e.registerBufferAccessor=function(t,e){this._bufferAccessors.set(t,e)},e.updateBuffer=function(t,e){var r=this.switchBufferAccessor(t);this._currBID!==e&&(this._currBID=e,this._indexStart=r.getMeshBuffer(e).indexOffset)},e.commitComp=function(t,e,r,a,i){var n,s=0,o=-1;if(e&&e.chunk){if(!e.isValid())return;s=e.dataHash,n=e.material,o=e.chunk.bufferId}t.stencilStage===dt.ENTER_LEVEL||t.stencilStage===dt.ENTER_LEVEL_INVERTED?this._insertMaskBatch(t):t.stencilStage=gt.sharedManager.stage;var h=t.stencilStage;this._currHash===s&&0!==s&&this._currMaterial===n&&this._currDepthStencilStateStage===h||(this.autoMergeBatches(this._currComponent),e&&!e._isMeshBuffer&&this.updateBuffer(e.vertexFormat,o),this._currRenderData=e,this._currHash=e?e.dataHash:0,this._currComponent=t,this._currTransform=i,this._currMaterial=t.getRenderMaterial(0),this._currDepthStencilStateStage=h,this._currLayer=t.node.layer,r?(this._currTexture=r.getGFXTexture(),this._currSampler=r.getGFXSampler(),this._currTextureHash=r.getHash(),this._currSamplerHash=this._currSampler.hash):(this._currTexture=null,this._currSampler=null,this._currTextureHash=0,this._currSamplerHash=0)),a.fillBuffers&&a.fillBuffers(t,this)},e.commitIA=function(t,e,r,a,i){this._currMaterial!==this._emptyMaterial&&(this.autoMergeBatches(this._currComponent),this.resetRenderStates());var n=null,s=0;t&&(t.stencilStage=gt.sharedManager.stage,n=null!==t.customMaterial?gt.sharedManager.getStencilStage(t.stencilStage,a):gt.sharedManager.getStencilStage(t.stencilStage),s=gt.sharedManager.getStencilHash(t.stencilStage));var o=this._currStaticRoot?this._currStaticRoot._requireDrawBatch():this._drawBatchPool.alloc();o.visFlags=t.node.layer,o.inputAssembler=e,o.useLocalData=i||null,r&&(o.texture=r.getGFXTexture(),o.sampler=r.getGFXSampler(),o.textureHash=r.getHash(),o.samplerHash=o.sampler.hash),o.fillPasses(a||null,n,s,null),this._batches.push(o)},e.commitMiddleware=function(t,e,r,a,i,n,s){var o=i.getGFXTexture();s&&this._middlewareEnableBatch&&this._middlewareBuffer===e&&this._currTexture===o&&this._currMaterial.hash===n.hash&&this._middlewareIndexStart+this._middlewareIndexCount===r&&this._currLayer===t.node.layer?this._middlewareIndexCount+=a:(this.autoMergeBatches(this._currComponent),this.resetRenderStates(),this._currComponent=t,this._currTexture=o,this._currSampler=i.getGFXSampler(),this._currTextureHash=i.getHash(),this._currLayer=t.node.layer,this._currSamplerHash=this._currSampler.hash,this._currHash=0,this._currTransform=s?null:t.node,this._middlewareEnableBatch=s,this._middlewareBuffer=e,this._currMaterial=n,this._middlewareIndexStart=r,this._middlewareIndexCount=a),this._currIsMiddleware=!0},e.commitModel=function(t,e,r){this._currMaterial!==this._emptyMaterial&&(this.autoMergeBatches(this._currComponent),this.resetRenderStates());var a=null,i=0;r&&(t.stencilStage===dt.ENTER_LEVEL||t.stencilStage===dt.ENTER_LEVEL_INVERTED?this._insertMaskBatch(t):t.stencilStage=gt.sharedManager.stage,a=gt.sharedManager.getStencilStage(t.stencilStage,r),i=gt.sharedManager.getStencilHash(t.stencilStage));var n=ht.director.getTotalFrames();e&&(e.updateTransform(n),e.updateUBOs(n));for(var s=0;s<e.subModels.length;s++){var o=this._drawBatchPool.alloc(),h=e.subModels[s];o.visFlags=t.node.layer,o.model=e,o.texture=null,o.sampler=null,o.useLocalData=null,a||(a=null),o.fillPasses(r,a,i,h.patches),o.inputAssembler=h.inputAssembler,o.model.visFlags=o.visFlags,o.descriptorSet=h.descriptorSet,this._batches.push(o)}},e.setupStaticBatch=function(t,e){this.finishMergeBatches(),this._staticVBBuffer=e,this.currStaticRoot=t},e.endStaticBatch=function(){this.finishMergeBatches(),this.currStaticRoot=null,this._staticVBBuffer=null,this.switchBufferAccessor()},e.commitStaticBatch=function(t){this._batches.concat(t.drawBatchList),this.finishMergeBatches()},e.autoMergeBatches=function(t){if(this._currIsMiddleware)this.mergeBatchesForMiddleware(t);else{var e=this._currMaterial;if(e){var r,a=this._currRenderData,i=this._staticVBBuffer;if(a&&a._isMeshBuffer)r=a.requestIA(this.device),-1===this._meshDataArray.indexOf(a)&&this._meshDataArray.push(a);else if(i){var n=this._currBID,s=i.getMeshBuffer(n);if(!s)return;var o=s.indexOffset-this._indexStart;if(o<=0)return;nt(this._indexStart<s.indexOffset),s.setDirty(),(r=s.requireFreeIA(this.device)).firstIndex=this._indexStart,r.indexCount=o,this._indexStart=s.indexOffset}if(this._currBID=-1,r&&this._currTexture){var h=null,l=0;t&&(h=null!==t.customMaterial?gt.sharedManager.getStencilStage(t.stencilStage,e):gt.sharedManager.getStencilStage(t.stencilStage),l=gt.sharedManager.getStencilHash(t.stencilStage));var c=this._currStaticRoot?this._currStaticRoot._requireDrawBatch():this._drawBatchPool.alloc();c.visFlags=this._currLayer,c.texture=this._currTexture,c.sampler=this._currSampler,c.inputAssembler=r,c.useLocalData=this._currTransform,c.textureHash=this._currTextureHash,c.samplerHash=this._currSamplerHash,c.fillPasses(e,h,l,null),this._batches.push(c)}}}},e.mergeBatchesForMiddleware=function(t){var e,r;t.stencilStage=gt.sharedManager.stage,r=null!==t.customMaterial?gt.sharedManager.getStencilStage(t.stencilStage,this._currMaterial):gt.sharedManager.getStencilStage(t.stencilStage),e=gt.sharedManager.getStencilHash(t.stencilStage);var a=this._currStaticRoot?this._currStaticRoot._requireDrawBatch():this._drawBatchPool.alloc();a.visFlags=t.node.layer;var i=this._middlewareBuffer.requireFreeIA(this.device);i.firstIndex=this._middlewareIndexStart,i.indexCount=this._middlewareIndexCount,a.inputAssembler=i,a.useLocalData=this._currTransform,a.texture=this._currTexture,a.sampler=this._currSampler,a.textureHash=this._currTextureHash,a.samplerHash=this._currSamplerHash,a.fillPasses(this._currMaterial||null,r,e,null),this._batches.push(a),this._currIsMiddleware=!1,this._middlewareBuffer=null},e.forceMergeBatches=function(t,e,r){this._currMaterial=t,e?(this._currTexture=e.getGFXTexture(),this._currSampler=e.getGFXSampler(),this._currTextureHash=e.getHash(),this._currSamplerHash=this._currSampler.hash):(this._currTexture=this._currSampler=null,this._currTextureHash=this._currSamplerHash=0),this._currLayer=r.node.layer,this.autoMergeBatches(r)},e.resetRenderStates=function(){this._currMaterial=this._emptyMaterial,this._currRenderData=null,this._currTexture=null,this._currComponent=null,this._currTransform=null,this._currTextureHash=0,this._currSamplerHash=0,this._currLayer=0},e.finishMergeBatches=function(){this.autoMergeBatches(),this.resetRenderStates()},e.flushMaterial=function(t){this._currMaterial=t},e.walk=function(t,e){if(void 0===e&&(e=0),t.activeInHierarchy){var r=t.children,a=t._uiProps,i=a.uiComp,n=this._pOpacity,s=n,o=i&&i.color?i.color.a/255:1;if(this._pOpacity=s*=o*a.localOpacity,a.setOpacity(s),!_(s,0,g)){if(a.colorDirty&&this._opacityDirty++,i&&i.enabledInHierarchy&&i.fillBuffers(this),this._opacityDirty&&i&&!i.useVertexOpacity&&i.renderData&&i.renderData.vertexCount>0){Qt(i.renderData,s);var h=i.renderData.getMeshBuffer();h&&h.setDirty()}if(r.length>0&&!t._static)for(var l=0;l<r.length;++l){var c=r[l];this.walk(c,e)}a.colorDirty&&(this._opacityDirty--,a.colorDirty=!1)}this._pOpacity=n,i&&i.enabledInHierarchy&&(i.postUpdateAssembler(this),(i.stencilStage===dt.ENTER_LEVEL||i.stencilStage===dt.ENTER_LEVEL_INVERTED)&&gt.sharedManager.getMaskStackSize()>0&&(this.autoMergeBatches(this._currComponent),this.resetRenderStates(),gt.sharedManager.exitMask())),e+=1}},e._screenSort=function(t,e){return t.node.siblingIndex-e.node.siblingIndex},e._releaseDescriptorSetCache=function(t){this._descriptorSetCache.releaseDescriptorSetCache(t)},e._createClearModel=function(){if(!this._maskClearModel){this._maskClearMtl=It.get("default-clear-stencil"),this._maskClearModel=ht.director.root.createModel(jt);var t=St(xt),e=S.gfxDevice,r=e.createBuffer(new x(D.VERTEX|D.TRANSFER_DST,C.DEVICE,4*t,t)),a=new Float32Array([-1,-1,0,1,-1,0,-1,1,0,1,1,0]);r.update(a);var i=e.createBuffer(new x(D.INDEX|D.TRANSFER_DST,C.DEVICE,6*Uint16Array.BYTES_PER_ELEMENT,Uint16Array.BYTES_PER_ELEMENT)),n=new Uint16Array([0,1,2,2,1,3]);i.update(n),this._maskModelMesh=new Xt([r],xt,w.TRIANGLE_LIST,i),this._maskModelMesh.subMeshIdx=0,this._maskClearModel.initSubModel(0,this._maskModelMesh,this._maskClearMtl)}},e._insertMaskBatch=function(t){this.autoMergeBatches(this._currComponent),this.resetRenderStates(),this._createClearModel(),this._maskClearModel.node=this._maskClearModel.transform=t.node;var e=gt.sharedManager;e.pushMask(1);var r=e.clear(t),a=null,i=0,n=this._maskClearMtl;n&&(a=e.getStencilStage(r,n),i=e.getStencilHash(r));var s=this._maskClearModel,o=ht.director.getTotalFrames();s&&(s.updateTransform(o),s.updateUBOs(o));for(var h=0;h<s.subModels.length;h++){var l=this._drawBatchPool.alloc(),c=s.subModels[h];l.visFlags=t.node.layer,l.model=s,l.texture=null,l.sampler=null,l.useLocalData=null,a||(a=null),l.fillPasses(n,a,i,c.patches),l.inputAssembler=c.inputAssembler,l.model.visFlags=l.visFlags,l.descriptorSet=c.descriptorSet,this._batches.push(l)}e.enableMask()},e.syncMeshBuffersToNative=function(){},Z(t,[{key:"nativeObj",get:function(){return this._nativeObj}},{key:"currBufferAccessor",get:function(){return this._staticVBBuffer||(this._staticVBBuffer=this.switchBufferAccessor()),this._staticVBBuffer}},{key:"batches",get:function(){return this._batches}},{key:"currStaticRoot",set:function(t){this._currStaticRoot=t}},{key:"currIsStatic",set:function(t){this._currIsStatic=t}}]),t}()),zr=function(){var t=e.prototype;function e(){this._descriptorSet=null,this._transform=null,this._textureHash=0,this._samplerHash=0,this._localBuffer=null,this._transformUpdate=!0;var t=S.gfxDevice;this._localData=new Float32Array(ft.COUNT),this._localBuffer=t.createBuffer(new x(D.UNIFORM|D.TRANSFER_DST,C.HOST|C.DEVICE,ft.SIZE,ft.SIZE))}return t.getDescriptorSet=function(){return this._descriptorSet},t.initialize=function(t){var e=S.gfxDevice;this._transform=t.useLocalData,this._textureHash=t.textureHash,this._samplerHash=t.samplerHash,Or.layout=t.passes[0].localSetLayout,this._descriptorSet=e.createDescriptorSet(Or),this._descriptorSet.bindBuffer(ft.BINDING,this._localBuffer);var r=ut.SAMPLER_SPRITE;this._descriptorSet.bindTexture(r,t.texture),this._descriptorSet.bindSampler(r,t.sampler),this._descriptorSet.update(),this._transformUpdate=!0},t.updateTransform=function(t){t!==this._transform&&(this._transform=t,this._transformUpdate=!0,this.uploadLocalData())},t.equals=function(t,e,r){return this._transform===t&&this._textureHash===e&&this._samplerHash===r},t.reset=function(){this._transform=null,this._textureHash=0,this._samplerHash=0},t.destroy=function(){this._localBuffer&&(this._localBuffer.destroy(),this._localBuffer=null),this._descriptorSet&&(this._descriptorSet.destroy(),this._descriptorSet=null),this._localData=null},t.isValid=function(){return this._transform&&this._transform.isValid},t.uploadLocalData=function(){var t=this._transform;if((t.hasChangedFlags||t.isTransformDirty())&&(t.updateWorldTransform(),this._transformUpdate=!0),this._transformUpdate){var e=t.worldMatrix;m.toArray(this._localData,e,ft.MAT_WORLD_OFFSET),m.invert(Hr,e),m.transpose(Hr,Hr);var r=m.determinant(Hr),a=1/Math.sqrt(r);m.multiplyScalar(Hr,Hr,a),m.toArray(this._localData,Hr,ft.MAT_WORLD_IT_OFFSET),this._localBuffer.update(this._localData),this._transformUpdate=!1}},e}(),kr=function(){function t(){this._descriptorSetCache=new Map,this._dsCacheHashByTexture=new Map,this._localDescriptorSetCache=[],this._localCachePool=new ot((function(){return new zr}),16,(function(t){return t.destroy()}))}var e=t.prototype;return e.getDescriptorSet=function(t){if(ht.director.root,t.useLocalData){for(var e=this._localDescriptorSetCache,r=0,a=e.length;r<a;r++){var i=e[r];if(i.equals(t.useLocalData,t.textureHash,t.samplerHash))return i.getDescriptorSet()}var n=this._localCachePool.alloc();return n.initialize(t),this._localDescriptorSetCache.push(n),n.getDescriptorSet()}var s=t.textureHash^t.samplerHash;if(this._descriptorSetCache.has(s))return this._descriptorSetCache.get(s);Or.layout=t.passes[0].localSetLayout;var o=S.gfxDevice.createDescriptorSet(Or),h=ut.SAMPLER_SPRITE;return o.bindTexture(h,t.texture),o.bindSampler(h,t.sampler),o.update(),this._descriptorSetCache.set(s,o),this._dsCacheHashByTexture.set(t.textureHash,s),o},e.update=function(){var t=this._localDescriptorSetCache,e=t.length;if(0!==e){for(var r=[],a=0;a<e;a++){var i=t[a];if(i.isValid())i.uploadLocalData();else{i.reset();var n=t.indexOf(i);r.push(n)}}for(var s=r.length-1;s>=0;s--){var o=r[s],h=t[o];t.splice(o,1),this._localCachePool.free(h)}}},e.reset=function(){for(var t=this._localDescriptorSetCache,e=t.length,r=0;r<e;r++){var a=t[r];this._localCachePool.free(a)}this._localDescriptorSetCache.length=0},e.releaseDescriptorSetCache=function(t){var e=this._dsCacheHashByTexture.get(t);e&&this._descriptorSetCache.has(e)&&(this._descriptorSetCache.get(e).destroy(),this._descriptorSetCache.delete(e),this._dsCacheHashByTexture.delete(t))},e.destroy=function(){for(var t,e=it(this._descriptorSetCache.values());!(t=e()).done;)t.value.destroy();this._descriptorSetCache.clear(),this._dsCacheHashByTexture.clear(),this._localDescriptorSetCache.length=0,this._localCachePool.destroy()},t}();ht.internal.Batcher2D=Pr,t("UIDrawBatch",function(t){function e(){return t.apply(this,arguments)||this}return Q(e,t),e}(ae)),u(Dt.prototype,"MeshBuffer",["byteStart","vertexStart","indicesStart","request"].map((function(t){return{name:t,suggest:"please use meshBuffer.accessor."+t+" instead"}}))),c(Dt.prototype,"MeshBuffer",[{name:"indicesOffset",newName:"indexOffset"}]),v(Dt.prototype,"MeshBuffer",[{name:"vertexBuffers"},{name:"indexBuffer"}]),c(Pr.prototype,"Batcher2D",[{name:"currBufferBatch",newName:"currBufferAccessor"},{name:"acquireBufferBatch",newName:"switchBufferAccessor"}]),v(Ct.prototype,"MeshRenderData",[{name:"formatByte"},{name:"byteStart"},{name:"byteCount"}]),c(Ct.prototype,"MeshRenderData",[{name:"indicesStart",newName:"indexStart"}]),t("QuadRenderData",function(t){function e(e){var r;return r=t.call(this,e)||this,K(9006),r}return Q(e,t),e}(Ct)),ht.UI={MeshBuffer:Dt,spriteAssembler:Ir,labelAssembler:ze,RenderData:wt,MeshRenderData:Ct}}}}));
