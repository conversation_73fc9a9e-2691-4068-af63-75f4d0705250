System.register(["./gc-object-CKHc4SnS.js","./index-C5lmLqDW.js","./global-exports-CR3GRnjt.js","./scene-7MDSMR3j.js","./component-BaGvu7EF.js","./factory-D9_8ZCqM.js","./deprecated-Ca3AjUwj.js","./model-renderer-BcRDUYby.js","./rendering-sub-mesh-CowWLfXC.js","./debug-view-CKetkq9d.js","./director-DIlqD2Nd.js","./device-manager-BvjvoelW.js","./buffer-barrier-CuX_5NUR.js","./deprecated-D4QUWou_.js","./terrain-asset-CHGiv9LN.js","./pipeline-state-manager-Cdpe3is6.js","./pipeline-scene-data-B9tBPl1_.js","./prefab-DH0xadMc.js","./node-event-DTNosVQv.js","./touch-DB0AR-Sc.js","./renderer-9hfAnqUF.js"],(function(t){"use strict";var e,i,r,n,s,a,h,o,l,u,_,f,d,c,g,p,v,b,y,M,m,x,w,I,k,S,L,C,z,A,E,B,R,T,P,N,D,U,H,O,V,F,j,W,X,G,Y,K,q,J,Z,Q,$,tt;return{setters:[function(t){e=t.a,i=t._,r=t.F,n=t.b,s=t.d,a=t.o},function(t){h=t.g,o=t.c,l=t.a,u=t.e,_=t.u,f=t.b,d=t.k,c=t.V,g=t.t,p=t.d,v=t.B,b=t.s},function(t){y=t.l},function(t){M=t.b,m=t.c,x=t.d,w=t.k,I=t.N,k=t.E},function(t){S=t.C},function(t){L=t.W,C=t.P,z=t.T},function(t){A=t.M},function(t){E=t.M},function(t){B=t.R},function(t){R=t.P},function(t){T=t.d},function(t){P=t.d},function(t){N=t.B,D=t.b,U=t.M,H=t.A,O=t.a,V=t.F,F=t.P},null,function(e){j=e.T,W=e.a,X=e.b,G=e.c,Y=e.d,K=e.e,q=e.f,J=e.g,Z=e.h,Q=e.i,$=e.j,tt=e.k,t({TERRAIN_BLOCK_TILE_COMPLEXITY:e.b,TERRAIN_BLOCK_VERTEX_COMPLEXITY:e.a,TERRAIN_BLOCK_VERTEX_SIZE:e.T,TERRAIN_DATA_VERSION:e.r,TERRAIN_DATA_VERSION2:e.s,TERRAIN_DATA_VERSION3:e.t,TERRAIN_DATA_VERSION4:e.u,TERRAIN_DATA_VERSION5:e.h,TERRAIN_DATA_VERSION6:e.v,TERRAIN_DATA_VERSION7:e.w,TERRAIN_DATA_VERSION8:e.x,TERRAIN_DATA_VERSION_DEFAULT:e.y,TERRAIN_EAST_INDEX:e.q,TERRAIN_HEIGHT_BASE:e.e,TERRAIN_HEIGHT_FACTORY:e.f,TERRAIN_HEIGHT_FACTORY_V7:e.m,TERRAIN_HEIGHT_FMAX:e.j,TERRAIN_HEIGHT_FMIN:e.k,TERRAIN_MAX_BLEND_LAYERS:e.g,TERRAIN_MAX_LAYER_COUNT:e.i,TERRAIN_MAX_LEVELS:e.l,TERRAIN_NORTH_INDEX:e.n,TERRAIN_SOUTH_INDEX:e.o,TERRAIN_WEST_INDEX:e.p,TerrainAsset:e.c,TerrainLayerBinaryInfo:e.z,TerrainLayerInfo:e.d})},null,null,null,null,null,null],execute:function(){t("HeightField",function(){function t(t,e){this.data=new Uint16Array,this.w=0,this.h=0,this.w=t,this.h=e,this.data=new Uint16Array(t*e);for(var i=0;i<t*e;++i)this.data[i]=0}var e=t.prototype;return e.set=function(t,e,i){this.data[e*this.w+t]=i},e.get=function(t,e){return this.data[e*this.w+t]},e.getClamp=function(t,e){return t=h(t,0,this.w-1),e=h(e,0,this.h-1),this.get(t,e)},e.getAt=function(t,e){var i=t,r=e,n=Math.floor(i),s=Math.floor(r),a=n+1,o=s+1,l=i-n,u=r-s;n=h(n,0,this.w-1),s=h(s,0,this.h-1),a=h(a,0,this.w-1),o=h(o,0,this.h-1);var _=this.get(n,s),f=this.get(a,s),d=this.get(n,o),c=this.get(a,o),g=.5*(f+d);return l+u<=1?c=g+(g-_):_=g+(g-c),(_*(1-l)+f*l)*(1-u)+(d*(1-l)+c*l)*u},t}());var et,it,rt,nt,st,at,ht,ot,lt,ut,_t,ft,dt,ct,gt,pt,vt,bt,yt,Mt,mt,xt,wt,It,kt,St,Lt,Ct,zt,At,Et,Bt,Rt,Tt,Pt,Nt,Dt,Ut,Ht=33,Ot=1e14,Vt=function(){function t(){this.level=0,this.north=0,this.south=0,this.west=0,this.east=0}return t.prototype.equals=function(t){return this.level===t.level&&this.north===t.north&&this.south===t.south&&this.west===t.west&&this.east===t.east},t}(),Ft=function(){this.size=0,this.indices=null},jt=function(){this.key=new Vt,this.start=0,this.size=0,this.buffer=null,this.primCount=0},Wt=function(){function t(){this._bodyIndexPool=void 0,this._connecterIndexPool=void 0,this._indexMap=[],this._indexBuffer=new Uint16Array,this._bodyIndexPool=new Array(4);for(var e=0;e<4;++e)this._bodyIndexPool[e]=new Ft;this._connecterIndexPool=new Array(64);for(var i=0;i<4;++i)for(var r=0;r<4;++r)for(var n=0;n<4;++n)this._connecterIndexPool[t.mapIndex(i,r,n)]=new Ft;for(var s=0;s<4;++s)this._genBodyIndex(s);for(var a=0;a<4;++a)for(var h=0;h<4;++h)this._genConnecterIndexNorth(a,h),this._genConnecterIndexSouth(a,h),this._genConnecterIndexWest(a,h),this._genConnecterIndexEast(a,h);for(var o=0;o<4;++o)for(var l=0;l<4;++l)if(!(l<o))for(var u=0;u<4;++u)if(!(u<o))for(var _=0;_<4;++_)if(!(_<o))for(var f=0;f<4;++f)if(!(f<o)){var d=new Vt;d.level=o,d.north=l,d.south=u,d.west=_,d.east=f,this._genIndexData(d)}}t.mapIndex=function(t,e,i){return 16*t+4*e+i};var e=t.prototype;return e.getIndexData=function(t){for(var e=0;e<this._indexMap.length;++e)if(this._indexMap[e].key.equals(t))return this._indexMap[e];return null},e._genBodyIndex=function(t){var e=1<<t,i=32>>t,r=0;if(t<3&&(i-=2,r=e*Ht+e),0!==i&&0!==i){var n=i*i*6;this._bodyIndexPool[t].indices=new Uint16Array(n);for(var s=0,a=new Uint16Array(n),h=r,o=h+Ht*e,l=0;l<i;++l){for(var u=0;u<i;++u)a[s++]=o+u*e,a[s++]=o+(u+1)*e,a[s++]=h+u*e,a[s++]=o+(u+1)*e,a[s++]=h+(u+1)*e,a[s++]=h+u*e;h+=Ht*e,o+=Ht*e}this._bodyIndexPool[t].size=s,this._bodyIndexPool[t].indices=a}},e._genConnecterIndexNorth=function(e,i){var r=t.mapIndex(e,i,0);if(i<e||3===e)return this._connecterIndexPool[r].size=0,void(this._connecterIndexPool[r].indices=null);var n=1<<e,s=1<<i,a=32>>e,h=0,o=new Uint16Array(2*a+2);o[h++]=0,o[h++]=0;for(var l=1;l<a;++l){var u=l*n,_=n*Ht+u,f=(n-n)*Ht+u/s*s;o[h++]=_,o[h++]=f}o[h++]=32,o[h++]=32,this._connecterIndexPool[r].size=h,this._connecterIndexPool[r].indices=o},e._genConnecterIndexSouth=function(e,i){var r=t.mapIndex(e,i,1);if(i<e||3===e)return this._connecterIndexPool[r].size=0,void(this._connecterIndexPool[r].indices=null);var n=1<<e,s=1<<i,a=32>>e,h=0,o=new Uint16Array(2*a+2);o[h++]=1056,o[h++]=1056;for(var l=1;l<a;++l){var u=l*n,_=32-n,f=(_+n)*Ht+u/s*s,d=_*Ht+u;o[h++]=f,o[h++]=d}o[h++]=1088,o[h++]=1088,this._connecterIndexPool[r].size=h,this._connecterIndexPool[r].indices=o},e._genConnecterIndexWest=function(e,i){var r=t.mapIndex(e,i,2);if(i<e||3===e)return this._connecterIndexPool[r].size=0,void(this._connecterIndexPool[r].indices=null);var n=1<<e,s=1<<i,a=32>>e,h=0,o=new Uint16Array(2*a+2);o[h++]=0,o[h++]=0;for(var l=1;l<a;++l){var u=l*n/s*s*Ht+0,_=l*n*Ht+n;o[h++]=u,o[h++]=_}o[h++]=1056,o[h++]=1056,this._connecterIndexPool[r].size=h,this._connecterIndexPool[r].indices=o},e._genConnecterIndexEast=function(e,i){var r=t.mapIndex(e,i,3);if(i<e||3===e)return this._connecterIndexPool[r].size=0,void(this._connecterIndexPool[r].indices=null);var n=1<<e,s=1<<i,a=32>>e,h=0,o=new Uint16Array(2*a+2);o[h++]=32,o[h++]=32;for(var l=1;l<a;++l){var u=l*n*Ht+(32-n),_=l*n/s*s*Ht+32;o[h++]=u,o[h++]=_}o[h++]=1088,o[h++]=1088,this._connecterIndexPool[r].size=h,this._connecterIndexPool[r].indices=o},e._getConnenterIndex=function(e,i,r){return this._connecterIndexPool[t.mapIndex(e,i,r)]},e._genIndexData=function(t){var e=this.getIndexData(t);if(null!=e)return e;var i=this._bodyIndexPool[t.level],r=this._getConnenterIndex(t.level,t.north,0),n=this._getConnenterIndex(t.level,t.south,1),s=this._getConnenterIndex(t.level,t.west,2),a=this._getConnenterIndex(t.level,t.east,3);if((e=new jt).size=0,e.primCount=0,null!=i.indices&&(e.size+=i.size),r.indices&&(e.size+=3*(r.size-2)),n.indices&&(e.size+=3*(n.size-2)),s.indices&&(e.size+=3*(s.size-2)),a.indices&&(e.size+=3*(a.size-2)),0===e.size)return null;var h=0;if(e.buffer=new Uint16Array(e.size),e.key.level=t.level,e.key.east=t.east,e.key.west=t.west,e.key.north=t.north,e.key.south=t.south,i.indices)for(var o=0;o<i.size;++o)e.buffer[h++]=i.indices[o];if(r.indices)for(var l=0;l<r.size-2;l+=2){var u=r.indices[l+0],_=r.indices[l+1],f=r.indices[l+2],d=r.indices[l+3];e.buffer[h++]=u,e.buffer[h++]=f,e.buffer[h++]=_,e.buffer[h++]=f,e.buffer[h++]=d,e.buffer[h++]=_}if(n.indices)for(var c=0;c<n.size-2;c+=2){var g=n.indices[c+0],p=n.indices[c+1],v=n.indices[c+2],b=n.indices[c+3];e.buffer[h++]=g,e.buffer[h++]=v,e.buffer[h++]=p,e.buffer[h++]=v,e.buffer[h++]=b,e.buffer[h++]=p}if(s.indices)for(var y=0;y<s.size-2;y+=2){var M=s.indices[y+0],m=s.indices[y+1],x=s.indices[y+2],w=s.indices[y+3];e.buffer[h++]=M,e.buffer[h++]=x,e.buffer[h++]=m,e.buffer[h++]=x,e.buffer[h++]=w,e.buffer[h++]=m}if(a.indices)for(var I=0;I<a.size-2;I+=2){var k=a.indices[I+0],S=a.indices[I+1],L=a.indices[I+2],C=a.indices[I+3];e.buffer[h++]=k,e.buffer[h++]=L,e.buffer[h++]=S,e.buffer[h++]=L,e.buffer[h++]=C,e.buffer[h++]=S}e.primCount=h/3,e.start=this._indexBuffer.length,this._indexMap.push(e);var z=new Uint16Array(e.start+e.size);return z.set(this._indexBuffer,0),z.set(e.buffer,e.start),this._indexBuffer=z,e},t}(),Xt=t("TerrainInfo",o("cc.TerrainInfo")((it=function(){function t(){this.tileSize=rt&&rt(),this.blockCount=nt&&nt(),this.weightMapSize=st&&st(),this.lightMapSize=at&&at()}return e(t,[{key:"size",get:function(){var t=new v(0,0);return t.width=this.blockCount[0]*X*this.tileSize,t.height=this.blockCount[1]*X*this.tileSize,t}},{key:"tileCount",get:function(){var t=[0,0];return t[0]=this.blockCount[0]*X,t[1]=this.blockCount[1]*X,t}},{key:"vertexCount",get:function(){var t=this.tileCount;return t[0]+=1,t[1]+=1,t}}]),t}(),rt=l(it.prototype,"tileSize",[b],(function(){return 1})),nt=l(it.prototype,"blockCount",[b],(function(){return[1,1]})),st=l(it.prototype,"weightMapSize",[b],(function(){return 128})),at=l(it.prototype,"lightMapSize",[b],(function(){return 128})),et=it))||et),Gt=t("TerrainLayer",o("cc.TerrainLayer")((ot=function(){this.detailMap=lt&&lt(),this.normalMap=ut&&ut(),this.tileSize=_t&&_t(),this.metallic=ft&&ft(),this.roughness=dt&&dt()},lt=l(ot.prototype,"detailMap",[b],(function(){return null})),ut=l(ot.prototype,"normalMap",[b],(function(){return null})),_t=l(ot.prototype,"tileSize",[b],(function(){return 1})),ft=l(ot.prototype,"metallic",[b],(function(){return 0})),dt=l(ot.prototype,"roughness",[b],(function(){return 1})),ht=ot))||ht),Yt=function(t){function e(){for(var e,i=arguments.length,r=new Array(i),n=0;n<i;n++)r[n]=arguments[n];return(e=t.call.apply(t,[this].concat(r))||this)._model=null,e._meshData=null,e._brushPass=null,e._brushMaterial=null,e._currentMaterial=null,e._currentMaterialLayers=0,e._lightmap=null,e}i(e,t);var r=e.prototype;return r.destroy=function(){return null!=this._model&&(y.director.root.destroyModel(this._model),this._model=null),t.prototype.destroy.call(this)},r._destroyModel=function(){null!=this._model&&(y.director.root.destroyModel(this._model),this._model=null),null!=this._meshData&&(this._meshData.destroy(),this._meshData=null)},r._invalidMaterial=function(){null!=this._currentMaterial&&(this._clearMaterials(),this._brushPass=null,this._currentMaterial=null,null!=this._model&&(this._model.enabled=!1))},r._updateMaterial=function(t,e){if(null==this._meshData||null==this._model)return!1;var i=t.getMaxLayer();if(null==this._currentMaterial||i!==this._currentMaterialLayers){if(this._currentMaterial=new M,this._currentMaterial.initialize({effectAsset:t.getTerrain().getEffectAsset(),defines:t._getMaterialDefines(i)}),null!==this._brushMaterial){var r=new M;r.copy(this._brushMaterial),this._brushPass=null,null!==r.passes&&r.passes.length>0&&(this._brushPass=r.passes[0],this._currentMaterial.passes.push(this._brushPass),r.passes.pop())}return e&&this._model.initSubModel(0,this._meshData,this._currentMaterial),this.setSharedMaterial(this._currentMaterial,0),this._currentMaterialLayers=i,this._model.enabled=!0,this._model.receiveShadow=t.getTerrain().receiveShadow,!0}return!1},r._updateLightingmap=function(t,e){null!=this._model&&(this._lightmap=t,this._updateReceiveDirLight(),this._model.updateLightingmap(t,e))},r._onMaterialModified=function(t,e){null!=this._model&&this._onRebuildPSO(t,e||this._getBuiltinMaterial())},r._onRebuildPSO=function(t,e){this._model&&this._model.setSubModelMaterial(t,e)},r._clearMaterials=function(){null!=this._model&&this._onMaterialModified(0,null)},r._onUpdateReceiveDirLight=function(t,e){void 0===e&&(e=!1),this._model&&(e?this._model.receiveDirLight=!1:this.node&&(t&this.node.layer)===this.node.layer||t&this._model.visFlags?this._model.receiveDirLight=!0:this._model.receiveDirLight=!1)},r._updateReceiveDirLight=function(){var t=this.node.scene;if(t&&t.renderScene){var e=t.renderScene.mainLight;if(e){var i=e.visibility;e.node&&(e.node.mobility===m.Static&&this._lightmap?this._onUpdateReceiveDirLight(i,!0):this._onUpdateReceiveDirLight(i))}}},r._getBuiltinMaterial=function(){return x.get("missing-material")},e}(E),Kt=t("TerrainBlockLightmapInfo",o("cc.TerrainBlockLightmapInfo")((gt=function(){this.texture=pt&&pt(),this.UOff=vt&&vt(),this.VOff=bt&&bt(),this.UScale=yt&&yt(),this.VScale=Mt&&Mt()},pt=l(gt.prototype,"texture",[b],(function(){return null})),vt=l(gt.prototype,"UOff",[b],(function(){return 0})),bt=l(gt.prototype,"VOff",[b],(function(){return 0})),yt=l(gt.prototype,"UScale",[b],(function(){return 0})),Mt=l(gt.prototype,"VScale",[b],(function(){return 0})),ct=gt))||ct),qt=t("TerrainBlock",function(){function t(t,e,i){this._terrain=void 0,this._node=void 0,this._renderable=void 0,this._index=[1,1],this._weightMap=null,this._lightmapInfo=null,this._lodLevel=0,this._lodKey=new Vt,this._errorMetrics=[0,0,0,0],this._LevelDistances=[Ot,Ot,Ot,Ot],this._bbMin=_(),this._bbMax=_(),this._terrain=t,this._index[0]=e,this._index[1]=i,this._lightmapInfo=t._getLightmapInfo(e,i),this._node=new I("TerrainBlock"),this._node.setParent(this._terrain.node),this._node.hideFlags|=s.DontSave|s.HideInHierarchy,this._node.layer=this._terrain.node.layer,this._renderable=this._node.addComponent(Yt)}var i=t.prototype;return i.build=function(){var t=T.root.device,e=new Float32Array(j*W*W);this._buildVertexData(e);var i=t.createBuffer(new N(D.VERTEX|D.TRANSFER_DST,U.DEVICE,j*Float32Array.BYTES_PER_ELEMENT*W*W,j*Float32Array.BYTES_PER_ELEMENT));i.update(e),this._buildBoundingBox();var r=[new H(O.ATTR_POSITION,V.RGB32F),new H(O.ATTR_NORMAL,V.RGB32F),new H(O.ATTR_TEX_COORD,V.RG32F)];this._renderable._meshData=new B([i],r,F.TRIANGLE_LIST,this._terrain._getSharedIndexBuffer(),null,!1),this._renderable._model=y.director.root.createModel(A),this._renderable._model.createBoundingShape(this._bbMin,this._bbMax),this._renderable._model.node=this._renderable._model.transform=this._node,null!=this._renderable.node.scene&&(this.visible=!0),this._updateWeightMap(),this._updateMaterial(!0),this._terrain.lodEnable&&(this._updateLodBuffer(e),this._updateIndexBuffer())},i.rebuild=function(){this._updateHeight(),this._updateWeightMap(),this._renderable._invalidMaterial(),this._updateMaterial(!1)},i.destroy=function(){this.visible=!1,this._renderable._destroyModel(),null!=this._node&&this._node.isValid&&this._node.destroy(),null!=this._weightMap&&this._weightMap.destroy()},i.update=function(){this._updateMaterial(!1),this.lightmap!==this._renderable._lightmap&&this._renderable._updateLightingmap(this.lightmap,this.lightmapUVParam);var t=this._terrain.useNormalMap,e=this._terrain.usePBR,i=function(t){return null!==t?t.detailMap:null},r=function(t){var e=null!==t?t.normalMap:null;return null===e&&(e=x.get("normal-texture")),e},n=this._renderable._currentMaterial;if(null!==n){var s=this.getMaxLayer(),a=new u(1,1,1,1),h=new u(1,1,1,1),o=new u(0,0,0,0);if(0===s)if(-1!==this.layers[0]){var l=this._terrain.getLayer(this.layers[0]);null!==l&&(a.x=1/l.tileSize,h.x=l.roughness,o.x=l.metallic),n.setProperty("detailMap0",i(l)),t&&n.setProperty("normalMap0",r(l))}else n.setProperty("detailMap0",x.get("default-texture")),t&&n.setProperty("normalMap0",x.get("normal-texture"));else if(1===s){var _=this._terrain.getLayer(this.layers[0]),f=this._terrain.getLayer(this.layers[1]);null!==_&&(a.x=1/_.tileSize,h.x=_.roughness,o.x=_.metallic),null!==f&&(a.y=1/f.tileSize,h.y=f.roughness,o.y=f.metallic),n.setProperty("weightMap",this._weightMap),n.setProperty("detailMap0",i(_)),n.setProperty("detailMap1",i(f)),t&&(n.setProperty("normalMap0",r(_)),n.setProperty("normalMap1",r(f)))}else if(2===s){var d=this._terrain.getLayer(this.layers[0]),c=this._terrain.getLayer(this.layers[1]),g=this._terrain.getLayer(this.layers[2]);null!==d&&(a.x=1/d.tileSize,h.x=d.roughness,o.x=d.metallic),null!==c&&(a.y=1/c.tileSize,h.y=c.roughness,o.y=c.metallic),null!==g&&(a.z=1/g.tileSize,h.z=g.roughness,o.z=g.metallic),n.setProperty("weightMap",this._weightMap),n.setProperty("detailMap0",i(d)),n.setProperty("detailMap1",i(c)),n.setProperty("detailMap2",i(g)),t&&(n.setProperty("normalMap0",r(d)),n.setProperty("normalMap1",r(c)),n.setProperty("normalMap2",r(g)))}else if(3===s){var p=this._terrain.getLayer(this.layers[0]),v=this._terrain.getLayer(this.layers[1]),b=this._terrain.getLayer(this.layers[2]),y=this._terrain.getLayer(this.layers[3]);null!==p&&(a.x=1/p.tileSize,h.x=p.roughness,o.x=p.metallic),null!==v&&(a.y=1/v.tileSize,h.y=v.roughness,o.y=v.metallic),null!==b&&(a.z=1/b.tileSize,h.z=b.roughness,o.z=b.metallic),null!==y&&(a.w=1/y.tileSize,h.w=y.roughness,o.w=y.metallic),n.setProperty("weightMap",this._weightMap),n.setProperty("detailMap0",i(p)),n.setProperty("detailMap1",i(v)),n.setProperty("detailMap2",i(b)),n.setProperty("detailMap3",i(y)),t&&(n.setProperty("normalMap0",r(p)),n.setProperty("normalMap1",r(v)),n.setProperty("normalMap2",r(b)),n.setProperty("normalMap3",r(y)))}n.setProperty("UVScale",a),e&&(n.setProperty("roughness",h),n.setProperty("metallic",o))}},i._buildLodInfo=function(){var t=new Float32Array(j*W*W);this._buildVertexData(t),this._updateLodBuffer(t),this._updateIndexBuffer()},i._updateLevel=function(t){var e=this._terrain,i=e.node,r=_(),n=_();f.add(r,this._bbMin,i.worldPosition),f.add(n,this._bbMax,i.worldPosition);var s=f.distance(r,t),a=f.distance(n,t),h=Math.min(s,a);for(h-=e.LodBias,this._lodLevel=0;this._lodLevel<3&&!(h<=this._LevelDistances[this._lodLevel+1]);)++this._lodLevel},i.setBrushMaterial=function(t){this._renderable._brushMaterial!==t&&(this._renderable._invalidMaterial(),this._renderable._brushMaterial=t)},i._getBrushMaterial=function(){return this._renderable?this._renderable._brushMaterial:null},i._getBrushPass=function(){return this._renderable?this._renderable._brushPass:null},i.getTerrain=function(){return this._terrain},i.getIndex=function(){return this._index},i.getRect=function(){var t=new d;return t.x=this._index[0]*X,t.y=this._index[1]*X,t.width=X,t.height=X,t},i.setLayer=function(t,e){this.layers[t]!==e&&(this._terrain.setBlockLayer(this._index[0],this._index[1],t,e),this._renderable._invalidMaterial(),this._updateMaterial(!1))},i.getLayer=function(t){return this.layers[t]},i.getMaxLayer=function(){return this.layers[3]>=0?3:this.layers[2]>=0?2:this.layers[1]>=0?1:0},i._getMaterialDefines=function(t){var e=1;return this._terrain.node&&this._terrain.node.scene&&this._terrain.node.scene.globals.bakedWithStationaryMainLight&&(e=2),{LAYERS:t+1,CC_USE_LIGHTMAP:null!==this.lightmap?e:0,USE_NORMALMAP:this._terrain.useNormalMap?1:0,USE_PBR:this._terrain.usePBR?1:0}},i._invalidMaterial=function(){this._renderable._invalidMaterial()},i._updateMaterial=function(t){this._renderable._updateMaterial(this,t)&&(null!==this.lightmap&&this.lightmap.setWrapMode(L.CLAMP_TO_BORDER,L.CLAMP_TO_BORDER),this._renderable._updateLightingmap(this.lightmap,this.lightmapUVParam))},i._updateHeight=function(){if(null!=this._renderable._meshData){var t=new Float32Array(j*W*W);this._buildVertexData(t),this._renderable._meshData.vertexBuffers[0].update(t),this._buildBoundingBox(),this._renderable._model.createBoundingShape(this._bbMin,this._bbMax),this._renderable._model.updateWorldBound(),this._updateLodBuffer(t),this._updateIndexBuffer()}},i._updateWeightMap=function(){if(0!==this.getMaxLayer()){null==this._weightMap&&(this._weightMap=new w,this._weightMap.create(this._terrain.weightMapSize,this._terrain.weightMapSize,C.RGBA8888),this._weightMap.setFilters(z.LINEAR,z.LINEAR),this._weightMap.setWrapMode(L.CLAMP_TO_EDGE,L.CLAMP_TO_EDGE));for(var t=new Uint8Array(this._terrain.weightMapSize*this._terrain.weightMapSize*4),e=0,i=0;i<this._terrain.weightMapSize;++i)for(var r=0;r<this._terrain.weightMapSize;++r){var n=this._index[0]*this._terrain.weightMapSize+r,s=this._index[1]*this._terrain.weightMapSize+i,a=this._terrain.getWeight(n,s);t[4*e+0]=Math.floor(255*a.x),t[4*e+1]=Math.floor(255*a.y),t[4*e+2]=Math.floor(255*a.z),t[4*e+3]=Math.floor(255*a.w),e+=1}this._weightMap.uploadData(t)}else null!=this._weightMap&&(this._weightMap.destroy(),this._weightMap=null)},i._updateLightmap=function(t){this._lightmapInfo=t,this._invalidMaterial()},i._updateLod=function(){var t=new Vt;if(t.level=this._lodLevel,t.north=this._lodLevel,t.south=this._lodLevel,t.west=this._lodLevel,t.east=this._lodLevel,this._index[0]>0){var e=this.getTerrain().getBlock(this._index[0]-1,this._index[1]);t.west=e._lodLevel,t.west<this._lodLevel&&(t.west=this._lodLevel)}if(this._index[0]<this._terrain.info.blockCount[0]-1){var i=this.getTerrain().getBlock(this._index[0]+1,this._index[1]);t.east=i._lodLevel,t.east<this._lodLevel&&(t.east=this._lodLevel)}if(this._index[1]>0){var r=this.getTerrain().getBlock(this._index[0],this._index[1]-1);t.north=r._lodLevel,t.north<this._lodLevel&&(t.north=this._lodLevel)}if(this._index[1]<this._terrain.info.blockCount[1]-1){var n=this.getTerrain().getBlock(this._index[0],this._index[1]+1);t.south=n._lodLevel,t.south<this._lodLevel&&(t.south=this._lodLevel)}this._lodKey.equals(t)||(this._lodKey=t,this._updateIndexBuffer())},i._resetLod=function(){var t=new Vt;t.level=0,t.north=0,t.south=0,t.west=0,t.east=0,this._lodKey.equals(t)||(this._lodKey=t,this._updateIndexBuffer())},i._updateIndexBuffer=function(){if(null!==this._renderable._meshData&&null!==this._renderable._model&&0!==this._renderable._model.subModels.length){var t=this._terrain._getIndexData(this._lodKey);if(null!==t){var e=this._renderable._model.subModels[0];e.inputAssembler.firstIndex=t.start,e.inputAssembler.indexCount=t.size}}},i._getHeight=function(t,e,i){return i[(W*e+t)*j+1]},i._updateLodBuffer=function(t){this._lodLevel=0,this._lodKey=new Vt,this._calcErrorMetrics(t),this._calcLevelDistances(t)},i._calcErrorMetrics=function(t){this._errorMetrics[0]=0;for(var e=1;e<4;++e)this._errorMetrics[e]=this._calcErrorMetric(e,t);for(var i=2;i<4;++i)this._errorMetrics[i]=Math.max(this._errorMetrics[i],this._errorMetrics[i-1])},i._calcErrorMetric=function(t,e){for(var i=0,r=1<<t,n=W,s=W,a=n-1>>t,h=s-1>>t,o=0;o<s;o+=r)for(var l=0;l<a;++l){var u=l*r,_=u+r,f=(_+u)/2,d=this._getHeight(u,o,e),c=this._getHeight(_,o,e),g=this._getHeight(f,o,e),p=(d+c)/2,v=Math.abs(g-p);i=Math.max(i,v)}for(var b=0;b<n;b+=r)for(var y=0;y<h;++y){var M=y*r,m=M+r,x=(M+m)/2,w=this._getHeight(b,M,e),I=this._getHeight(b,m,e),k=this._getHeight(b,x,e),S=(w+I)/2,L=Math.abs(k-S);i=Math.max(i,L)}for(var C=0;C<h;++C)for(var z=C*r,A=z+r,E=(z+A)/2,B=0;B<a;++B){var R=B*r,T=R+r,P=(R+T)/2,N=this._getHeight(R,z,e),D=this._getHeight(T,A,e),U=this._getHeight(P,E,e),H=(N+D)/2,O=Math.abs(U-H);i=Math.max(i,O)}return i},i._calcLevelDistances=function(){for(var t=1;t<4;++t){var e=96*this._errorMetrics[t];this._LevelDistances[t]=e}},i._buildVertexData=function(t){for(var e=0,i=0;i<W;++i)for(var r=0;r<W;++r){var n=this._index[0]*X+r,s=this._index[1]*X+i,a=this._terrain.getPosition(n,s),h=this._terrain.getNormal(n,s),o=new c(r/X,i/X);t[e++]=a.x,t[e++]=a.y,t[e++]=a.z,t[e++]=h.x,t[e++]=h.y,t[e++]=h.z,t[e++]=o.x,t[e++]=o.y}},i._buildBoundingBox=function(){this._bbMin.set(Number.MAX_VALUE,Number.MAX_VALUE,Number.MAX_VALUE),this._bbMax.set(Number.MIN_VALUE,Number.MIN_VALUE,Number.MIN_VALUE);for(var t=0;t<W;++t)for(var e=0;e<W;++e){var i=this._index[0]*X+e,r=this._index[1]*X+t,n=this._terrain.getPosition(i,r);f.min(this._bbMin,this._bbMin,n),f.max(this._bbMax,this._bbMax,n)}},e(t,[{key:"valid",get:function(){if(null===this._terrain)return!1;for(var t=this._terrain.getBlocks(),e=0;e<t.length;++e)if(t[e]===this)return!0;return!1}},{key:"material",get:function(){return this._renderable?this._renderable._currentMaterial:null}},{key:"layers",get:function(){return this._terrain.getBlockLayers(this._index[0],this._index[1])}},{key:"weightmap",get:function(){return this._weightMap}},{key:"lightmap",get:function(){return this._lightmapInfo?this._lightmapInfo.texture:null}},{key:"lightmapUVParam",get:function(){return null!=this._lightmapInfo?new u(this._lightmapInfo.UOff,this._lightmapInfo.VOff,this._lightmapInfo.UScale,this._lightmapInfo.VScale):new u(0,0,0,0)}},{key:"visible",get:function(){return null!==this._renderable._model&&null!==this._renderable._model.scene},set:function(t){null!==this._renderable._model&&(t?null!=this._terrain.node&&null!=this._terrain.node.scene&&null!=this._terrain.node.scene.renderScene&&null==this._renderable._model.scene&&this._terrain.node.scene.renderScene.addModel(this._renderable._model):null!==this._renderable._model.scene&&this._renderable._model.scene.removeModel(this._renderable._model))}}]),t}());t("Terrain",(mt=o("cc.Terrain"),xt=g(G),wt=g(k),It=g(Kt),kt=g(a),St=g(G),Lt=g(k),Ct=g(Xt),mt(zt=p((At=function(t){function n(){var e;(e=t.call(this)||this).__asset=Et&&Et(),e._effectAsset=Bt&&Bt(),e._lightmapInfos=Rt&&Rt(),e._receiveShadow=Tt&&Tt(),e._useNormalmap=Pt&&Pt(),e._usePBR=Nt&&Nt(),e._lodEnable=Dt&&Dt(),e._lodBias=Ut&&Ut(),e._buitinAsset=null,e._tileSize=1,e._blockCount=[1,1],e._weightMapSize=128,e._lightMapSize=128,e._heights=new Uint16Array,e._weights=new Uint8Array,e._normals=new Float32Array,e._layerList=[],e._layerBuffer=[],e._blocks=[],e._lod=null,e._sharedIndexBuffer=null,e._sharedLodIndexBuffer=null;for(var i=0;i<Q;++i)e._layerList.push(null);return e}i(n,t);var s=n.prototype;return s.build=function(t){return this._tileSize=t.tileSize,this._blockCount[0]=t.blockCount[0],this._blockCount[1]=t.blockCount[1],this._weightMapSize=t.weightMapSize,this._lightMapSize=t.lightMapSize,this._buildImp()},s.rebuild=function(t){for(var e=0;e<this._blocks.length;++e)this._blocks[e].destroy();this._blocks=[],this._resetLightmap(!1),this._rebuildLayerBuffer(t);var i=this._rebuildHeights(t);this._rebuildWeights(t),this._tileSize=t.tileSize,this._blockCount[0]=t.blockCount[0],this._blockCount[1]=t.blockCount[1],this._weightMapSize=t.weightMapSize,this._lightMapSize=t.lightMapSize,i&&(this._normals=new Float32Array(3*this.heights.length),this._buildNormals());for(var r=0;r<this._blockCount[1];++r)for(var n=0;n<this._blockCount[0];++n)this._blocks.push(new qt(this,n,r));for(var s=0;s<this._blocks.length;++s)this._blocks[s].build()},s.importHeightField=function(t,e){for(var i=0,r=0;r<this.vertexCount[1];++r)for(var n=0;n<this.vertexCount[0];++n){var s=n/this.tileCount[0],a=r/this.tileCount[1],h=t.getAt(s*t.w,a*t.h)*e;this._heights[i++]=h}this._buildNormals();for(var o=0;o<this._blocks.length;++o)this._blocks[o]._updateHeight()},s.exportHeightField=function(t,e){for(var i=0,r=0;r<t.h;++r)for(var n=0;n<t.w;++n){var s=n/(t.w-1),a=r/(t.h-1),h=s*this.size.width,o=a*this.size.height,l=this.getHeightAt(h,o);null!=l&&(t.data[i++]=l*e)}},s.exportAsset=function(){var t=new G;t.tileSize=this.tileSize,t.blockCount=this.blockCount,t.lightMapSize=this.lightMapSize,t.weightMapSize=this.weightMapSize,t.heights=this.heights,t.weights=this.weights,t.layerBuffer=new Array(4*this._blocks.length);for(var e=0;e<this._blocks.length;++e)t.layerBuffer[4*e+0]=this._blocks[e].layers[0],t.layerBuffer[4*e+1]=this._blocks[e].layers[1],t.layerBuffer[4*e+2]=this._blocks[e].layers[2],t.layerBuffer[4*e+3]=this._blocks[e].layers[3];return this.exportLayerListToAsset(t),t},s.exportLayerListToAsset=function(t){t.layerInfos.length=0;for(var e=0;e<this._layerList.length;++e){var i=this._layerList[e];if(i&&i.detailMap&&r(i.detailMap)){var n=new Y;n.slot=e,n.tileSize=i.tileSize,n.detailMap=i.detailMap,n.normalMap=i.normalMap,n.metallic=i.metallic,n.roughness=i.roughness,t.layerInfos.push(n)}}},s.getEffectAsset=function(){return null===this._effectAsset?y.EffectAsset.get("1d08ef62-a503-4ce2-8b9a-46c90873f7d3"):this._effectAsset},s.onEnable=function(){0===this._blocks.length&&this._buildImp();for(var t=0;t<this._blocks.length;++t)this._blocks[t].visible=!0;y.director.root.pipelineEvent.on(R.RENDER_CAMERA_BEGIN,this.onUpdateFromCamera,this)},s.onDisable=function(){y.director.root.pipelineEvent.off(R.RENDER_CAMERA_BEGIN,this.onUpdateFromCamera,this);for(var t=0;t<this._blocks.length;++t)this._blocks[t].visible=!1},s.onDestroy=function(){for(var t=0;t<this._blocks.length;++t)this._blocks[t].destroy();this._blocks=[];for(var e=0;e<this._layerList.length;++e)this._layerList[e]=null;null!=this._sharedIndexBuffer&&this._sharedIndexBuffer.destroy(),null!=this._sharedLodIndexBuffer&&this._sharedLodIndexBuffer.destroy()},s.onRestore=function(){this.onEnable(),this._buildImp(!0)},s.update=function(){for(var t=0;t<this._blocks.length;++t)this._blocks[t].update()},s.onUpdateFromCamera=function(t){if(this.lodEnable&&null!=this._sharedLodIndexBuffer&&t.scene===this._getRenderScene()){for(var e=0;e<this._blocks.length;++e)this._blocks[e]._updateLevel(t.position);for(var i=0;i<this._blocks.length;++i)this._blocks[i]._updateLod()}},s.addLayer=function(t){for(var e=0;e<this._layerList.length;++e){var i;if(null===this._layerList[e]||this._layerList[e]&&null===(null==(i=this._layerList[e])?void 0:i.detailMap))return this._layerList[e]=t,this._asset&&this.exportLayerListToAsset(this._asset),e}return-1},s.setLayer=function(t,e){this._layerList[t]=e,this._asset&&this.exportLayerListToAsset(this._asset)},s.removeLayer=function(t){this._layerList[t]=null,this._asset&&this.exportLayerListToAsset(this._asset)},s.getLayer=function(t){return-1===t?null:this._layerList[t]},s.getPosition=function(t,e){var i=t*this._tileSize,r=e*this._tileSize,n=this.getHeight(t,e);return _(i,n,r)},s.getHeightField=function(){return this._heights},s.setHeight=function(t,e,i){i=h(i,tt,$),this._heights[e*this.vertexCount[0]+t]=K+i/q},s.getHeight=function(t,e){return(this._heights[e*this.vertexCount[0]+t]-K)*q},s.getHeightClamp=function(t,e){return t=h(t,0,this.vertexCount[0]-1),e=h(e,0,this.vertexCount[1]-1),this.getHeight(t,e)},s.getHeightAt=function(t,e){var i=t/this.tileSize,r=e/this.tileSize,n=Math.floor(i),s=Math.floor(r),a=n+1,o=s+1,l=i-n,u=r-s;if(n<0||n>this.vertexCount[0]-1||s<0||s>this.vertexCount[1]-1)return null;n=h(n,0,this.vertexCount[0]-1),s=h(s,0,this.vertexCount[1]-1),a=h(a,0,this.vertexCount[0]-1),o=h(o,0,this.vertexCount[1]-1);var _=this.getHeight(n,s),f=this.getHeight(a,s),d=this.getHeight(n,o),c=this.getHeight(a,o),g=.5*(f+d);return l+u<=1?c=g+(g-_):_=g+(g-c),(_*(1-l)+f*l)*(1-u)+(d*(1-l)+c*l)*u},s._setNormal=function(t,e,i){var r=e*this.vertexCount[0]+t;this._normals[3*r+0]=i.x,this._normals[3*r+1]=i.y,this._normals[3*r+2]=i.z},s.getNormal=function(t,e){var i=e*this.vertexCount[0]+t,r=_();return r.x=this._normals[3*i+0],r.y=this._normals[3*i+1],r.z=this._normals[3*i+2],r},s.getNormalAt=function(t,e){var i=t/this.tileSize,r=e/this.tileSize,n=Math.floor(i),s=Math.floor(r),a=n+1,o=s+1,l=i-n,u=r-s;if(n<0||n>this.vertexCount[0]-1||s<0||s>this.vertexCount[1]-1)return null;n=h(n,0,this.vertexCount[0]-1),s=h(s,0,this.vertexCount[1]-1),a=h(a,0,this.vertexCount[0]-1),o=h(o,0,this.vertexCount[1]-1);var d=this.getNormal(n,s),c=this.getNormal(a,s),g=this.getNormal(n,o),p=this.getNormal(a,o),v=_();f.add(v,c,g).multiplyScalar(.5),l+u<=1?(p.set(v),p.subtract(d),p.add(v)):(d.set(v),d.subtract(p),d.add(v));var b=_(),y=_(),M=_();return f.lerp(b,d,c,l),f.lerp(y,g,p,l),f.lerp(M,b,y,u),M},s.setWeight=function(t,e,i){var r=e*this._weightMapSize*this._blockCount[0]+t;this._weights[4*r+0]=255*i.x,this._weights[4*r+1]=255*i.y,this._weights[4*r+2]=255*i.z,this._weights[4*r+3]=255*i.w},s.getWeight=function(t,e){var i=e*this._weightMapSize*this._blockCount[0]+t,r=new u;return r.x=this._weights[4*i+0]/255,r.y=this._weights[4*i+1]/255,r.z=this._weights[4*i+2]/255,r.w=this._weights[4*i+3]/255,r},s.getWeightAt=function(t,e){var i=this.weightMapSize*this.blockCount[0],r=this.weightMapSize*this.blockCount[1];if(0===i||0===r)return null;var n=t/i,s=e/r,a=Math.floor(n),o=Math.floor(s),l=a+1,_=o+1,f=n-a,d=s-o;if(a<0||a>i-1||o<0||o>r-1)return null;a=h(a,0,i-1),o=h(o,0,r-1),l=h(l,0,i-1),_=h(_,0,r-1);var c=this.getWeight(a,o),g=this.getWeight(l,o),p=this.getWeight(a,_),v=this.getWeight(l,_),b=new u;u.add(b,g,p).multiplyScalar(.5),f+d<=1?(v=new u,u.subtract(v,b,c).add(b)):(c=new u,u.subtract(c,b,v).add(b));var y=new u,M=new u,m=new u;return u.lerp(y,c,g,f),u.lerp(M,p,v,f),u.lerp(m,y,M,d),m},s.getMaxWeightLayerAt=function(t,e){var i=this.weightMapSize*this.blockCount[0],r=this.weightMapSize*this.blockCount[1];if(0===i||0===r)return null;var n=t/i,s=e/r,a=Math.floor(n),h=Math.floor(s);if(a<0||a>i-1||h<0||h>r-1)return null;var o=this.getWeight(a,h),l=Math.floor(t/this.weightMapSize),u=Math.floor(e/this.weightMapSize),_=this.getBlock(l,u),f=0;return o.y>o[f]&&-1!==_.getLayer(1)&&(f=1),o.y>o[f]&&-1!==_.getLayer(2)&&(f=2),o.z>o[f]&&-1!==_.getLayer(3)&&(f=3),f=_.getLayer(f),this.getLayer(f)},s.getBlockLayers=function(t,e){var i=(e*this._blockCount[0]+t)*J;return[this._layerBuffer[i],this._layerBuffer[i+1],this._layerBuffer[i+2],this._layerBuffer[i+3]]},s.getBlockLayer=function(t,e,i){var r=(e*this._blockCount[0]+t)*J;return this._layerBuffer[r+i]},s.setBlockLayer=function(t,e,i,r){var n=(e*this._blockCount[0]+t)*J;this._layerBuffer[n+i]=r},s.getBlock=function(t,e){return this._blocks[e*this._blockCount[0]+t]},s.getBlocks=function(){return this._blocks},s.rayCheck=function(t,e,i,r){void 0===r&&(r=!0);var n=t;r&&f.subtract(n,t,this.node.worldPosition);var s=_();s.set(e),s.multiplyScalar(i);var a=null;if(e.equals(_(0,1,0))){var h=this.getHeightAt(n.x,n.z);null!=h&&n.y<=h&&(a=_(n.x,h,n.z))}else if(e.equals(_(0,-1,0))){var o=this.getHeightAt(n.x,n.z);null!=o&&n.y>=o&&(a=_(n.x,o,n.z))}else{for(var l=0;l++<2e3;){var u=this.getHeightAt(n.x,n.z);if(null!=u&&n.y<=u)break;n.add(e)}for(;l++<2e3;){var d=this.getHeightAt(n.x,n.z);if(null!=d&&n.y<=d){a=_(n.x,d,n.z);break}n.add(s)}}return a},s._createSharedIndexBuffer=function(){var t=P.gfxDevice;if(null!==this._lod){var e=t.createBuffer(new N(D.INDEX|D.TRANSFER_DST,U.DEVICE,Uint16Array.BYTES_PER_ELEMENT*this._lod._indexBuffer.length,Uint16Array.BYTES_PER_ELEMENT));return e.update(this._lod._indexBuffer),e}for(var i=new Uint16Array(X*X*6),r=0,n=0;n<X;++n)for(var s=0;s<X;++s){var a=n*W+s,h=n*W+s+1,o=(n+1)*W+s,l=(n+1)*W+s+1;i[r++]=a,i[r++]=o,i[r++]=h,i[r++]=h,i[r++]=o,i[r++]=l}var u=t.createBuffer(new N(D.INDEX|D.TRANSFER_DST,U.DEVICE,Uint16Array.BYTES_PER_ELEMENT*i.length,Uint16Array.BYTES_PER_ELEMENT));return u.update(i),u},s._getSharedIndexBuffer=function(){return null!==this._sharedLodIndexBuffer?this._sharedLodIndexBuffer:null!==this._sharedIndexBuffer?this._sharedIndexBuffer:(this.lodEnable&&null===this._lod&&(this._lod=new Wt),null!==this._lod?(this._sharedLodIndexBuffer=this._createSharedIndexBuffer(),this._sharedLodIndexBuffer):(this._sharedIndexBuffer=this._createSharedIndexBuffer(),this._sharedIndexBuffer))},s._getIndexData=function(t){return null!==this._sharedLodIndexBuffer&&null!==this._lod?this._lod.getIndexData(t):null},s._resetLightmap=function(t){if(this._lightmapInfos.length=0,t)for(var e=0;e<this._blockCount[0]*this._blockCount[1];++e)this._lightmapInfos.push(new Kt)},s._updateLightmap=function(t,e,i,r,n,s){if(e){if(0===this._lightmapInfos.length)for(var a=0;a<this._blockCount[0]*this._blockCount[1];++a)this._lightmapInfos.push(new Kt)}else if(0===this._lightmapInfos.length)return;this._lightmapInfos[t].texture=e,this._lightmapInfos[t].UOff=i,this._lightmapInfos[t].VOff=r,this._lightmapInfos[t].UScale=n,this._lightmapInfos[t].VScale=s,this._blocks[t]._updateLightmap(this._lightmapInfos[t])},s._getLightmapInfo=function(t,e){var i=e*this._blockCount[0]+t;return i<this._lightmapInfos.length?this._lightmapInfos[i]:null},s._calcNormal=function(t,e){var i,r,n=1,s=this.getPosition(t,e);t<this.vertexCount[0]-1?i=this.getPosition(t+1,e):(n*=-1,i=this.getPosition(t-1,e)),e<this.vertexCount[1]-1?r=this.getPosition(t,e+1):(n*=-1,r=this.getPosition(t,e-1)),i.subtract(s),r.subtract(s);var a=_();return a.set(r),a.cross(i),a.multiplyScalar(n),a.normalize(),a},s._buildNormals=function(){for(var t=0,e=0;e<this.vertexCount[1];++e)for(var i=0;i<this.vertexCount[0];++i){var r=this._calcNormal(i,e);this._normals[3*t+0]=r.x,this._normals[3*t+1]=r.y,this._normals[3*t+2]=r.z,t+=1}},s._buildImp=function(t){var e=this;if(void 0===t&&(t=!1),!this.valid){var i=this.__asset;if(this._buitinAsset!==i&&(this._buitinAsset=i),!t&&null!==i){this._tileSize=i.tileSize,this._blockCount=i.blockCount,this._weightMapSize=i.weightMapSize,this._lightMapSize=i.lightMapSize,this._heights=i.heights,this._normals=i.normals,this._weights=i.weights,this._layerBuffer=i.layerBuffer;for(var r=0;r<this._layerList.length;++r)this._layerList[r]=null;if(i.version<Z)for(var n=function(){var t=new Gt,r=i.layerBinaryInfos[s];t.tileSize=r.tileSize,y.assetManager.loadAny(r.detailMapId,(function(e,i){t.detailMap=i})),""!==r.normalMapId&&y.assetManager.loadAny(r.normalMapId,(function(e,i){t.normalMap=i})),t.roughness=r.roughness,t.metallic=r.metallic,e._layerList[r.slot]=t},s=0;s<i.layerBinaryInfos.length;++s)n();else for(var a=0;a<i.layerInfos.length;++a){var h=new Gt,o=i.layerInfos[a];h.tileSize=o.tileSize,h.detailMap=o.detailMap,h.normalMap=o.normalMap,h.roughness=o.roughness,h.metallic=o.metallic,this._layerList[o.slot]=h}}if(0!==this._blockCount[0]&&0!==this._blockCount[1]){var l=this.vertexCount[0]*this.vertexCount[1];if(null===this._heights||this._heights.length!==l){this._heights=new Uint16Array(l),this._normals=new Float32Array(3*l);for(var u=0;u<l;++u)this._heights[u]=K,this._normals[3*u+0]=0,this._normals[3*u+1]=1,this._normals[3*u+2]=0;i&&(i.heights=this._heights,i.normals=this._normals)}null!==this._normals&&this._normals.length===3*l||(this._normals=new Float32Array(3*l),this._buildNormals());var _=this.blockCount[0]*this.blockCount[1]*J;if(null===this._layerBuffer||this._layerBuffer.length!==_){this._layerBuffer=new Array(_);for(var f=0;f<_;++f)this._layerBuffer[f]=-1;i&&(i.layerBuffer=this._layerBuffer)}var d=this._weightMapSize*this._blockCount[0],c=this._weightMapSize*this._blockCount[1];if(this._weights.length!==d*c*4){this._weights=new Uint8Array(d*c*4);for(var g=0;g<d*c;++g)this._weights[4*g+0]=255,this._weights[4*g+1]=0,this._weights[4*g+2]=0,this._weights[4*g+3]=0;i&&(i.weights=this._weights)}for(var p=0;p<this._blockCount[1];++p)for(var v=0;v<this._blockCount[0];++v)this._blocks.push(new qt(this,v,p));for(var b=0;b<this._blocks.length;++b)this._blocks[b].build()}}},s._rebuildHeights=function(t){if(this.vertexCount[0]===t.vertexCount[0]&&this.vertexCount[1]===t.vertexCount[1])return!1;for(var e=new Uint16Array(t.vertexCount[0]*t.vertexCount[1]),i=0;i<e.length;++i)e[i]=K;for(var r=Math.min(this.vertexCount[0],t.vertexCount[0]),n=Math.min(this.vertexCount[1],t.vertexCount[1]),s=0;s<n;++s)for(var a=0;a<r;++a){var h=s*t.vertexCount[0]+a,o=s*this.vertexCount[0]+a;e[h]=this._heights[o]}return this._heights=e,!0},s._rebuildLayerBuffer=function(t){if(this.blockCount[0]===t.blockCount[0]&&this.blockCount[1]===t.blockCount[1])return!1;var e=[];e.length=t.blockCount[0]*t.blockCount[1]*J;for(var i=0;i<e.length;++i)e[i]=-1;for(var r=Math.min(this.blockCount[0],t.blockCount[0]),n=Math.min(this.blockCount[1],t.blockCount[1]),s=0;s<n;++s)for(var a=0;a<r;++a)for(var h=s*t.blockCount[0]+a,o=s*this.blockCount[0]+a,l=0;l<J;++l)e[h*J+l]=this._layerBuffer[o*J+l];return this._layerBuffer=e,!0},s._rebuildWeights=function(t){var e=this,i=this._weightMapSize,r=this._weightMapSize*this._blockCount[0],n=this._weightMapSize*this._blockCount[1],s=t.weightMapSize*t.blockCount[0],a=t.weightMapSize*t.blockCount[1];if(s===r&&a===n)return!1;for(var h=new Uint8Array(s*a*4),o=0;o<s*a;++o)h[4*o+0]=255,h[4*o+1]=0,h[4*o+2]=0,h[4*o+3]=0;for(var l=Math.min(t.blockCount[0],this._blockCount[0]),_=Math.min(t.blockCount[1],this._blockCount[1]),f=function(t,e,i){var n=e*r+t,s=new u;return s.x=i[4*n+0]/255,s.y=i[4*n+1]/255,s.z=i[4*n+2]/255,s.w=i[4*n+3]/255,s},d=function(t,r,n,s){var a=Math.floor(t),h=Math.floor(r),o=Math.min(a+1,i-1),l=Math.min(h+1,i-1),_=t-a,d=r-h,c=f(a+n,h+s,e._weights),g=f(o+n,h+s,e._weights),p=f(a+n,l+s,e._weights),v=f(o+n,l+s,e._weights),b=new u;u.add(b,g,p).multiplyScalar(.5),_+d<=1?(v.set(b),v.subtract(c),v.add(b)):(c.set(b),c.subtract(v),c.add(b));var y=new u,M=new u,m=new u;return u.lerp(y,c,g,_),u.lerp(M,p,v,_),u.lerp(m,y,M,d),m},c=0;c<_;++c)for(var g=0;g<l;++g)for(var p=g*i,v=c*i,b=0;b<t.weightMapSize;++b)for(var y=0;y<t.weightMapSize;++y){var M=void 0;M=t.weightMapSize===i?f(y+p,b+v,this._weights):d(y/(t.weightMapSize-1)*(i-1),b/(t.weightMapSize-1)*(i-1),p,v,this._weights);var m=g*t.weightMapSize+y,x=(c*t.weightMapSize+b)*s+m;h[4*x+0]=255*M.x,h[4*x+1]=255*M.y,h[4*x+2]=255*M.z,h[4*x+3]=255*M.w}return this._weights=h,!0},e(n,[{key:"_asset",get:function(){return this.__asset},set:function(t){if(this.__asset=t,this._buitinAsset!==this.__asset){this._buitinAsset=this.__asset;for(var e=0;e<this._blocks.length;++e)this._blocks[e].destroy();if(this._blocks=[],null===this.__asset){this._effectAsset=null,this._lightmapInfos=[],this._receiveShadow=!1,this._useNormalmap=!1,this._usePBR=!1,this._tileSize=1,this._blockCount=[1,1],this._weightMapSize=128,this._lightMapSize=128,this._heights=new Uint16Array,this._weights=new Uint8Array,this._normals=new Float32Array,this._layerBuffer=[],this._blocks=[],this._layerList=[];for(var i=0;i<Q;++i)this._layerList.push(null)}P.gfxDevice&&this._buildImp()}}},{key:"effectAsset",get:function(){return this._effectAsset},set:function(t){if(this._effectAsset!==t){this._effectAsset=t;for(var e=0;e<this._blocks.length;++e)this._blocks[e]._invalidMaterial()}}},{key:"receiveShadow",get:function(){return this._receiveShadow},set:function(t){this._receiveShadow=t;for(var e=0;e<this._blocks.length;e++)this._blocks[e]._invalidMaterial()}},{key:"useNormalMap",get:function(){return this._useNormalmap},set:function(t){this._useNormalmap=t;for(var e=0;e<this._blocks.length;e++)this._blocks[e]._invalidMaterial()}},{key:"usePBR",get:function(){return this._usePBR},set:function(t){this._usePBR=t;for(var e=0;e<this._blocks.length;e++)this._blocks[e]._invalidMaterial()}},{key:"lodEnable",get:function(){return this._lodEnable},set:function(t){if(this._lodEnable=t,this._lodEnable&&null===this._lod){this._lod=new Wt,null===this._sharedLodIndexBuffer&&(this._sharedLodIndexBuffer=this._createSharedIndexBuffer());for(var e=0;e<this._blocks.length;++e)this._blocks[e].destroy();this._blocks=[];for(var i=0;i<this._blockCount[1];++i)for(var r=0;r<this._blockCount[0];++r)this._blocks.push(new qt(this,r,i));for(var n=0;n<this._blocks.length;++n)this._blocks[n].build()}if(!this._lodEnable)for(var s=0;s<this._blocks.length;s++)this._blocks[s]._resetLod()}},{key:"LodBias",get:function(){return this._lodBias},set:function(t){this._lodBias=t}},{key:"size",get:function(){var t=new v(0,0);return t.width=this.blockCount[0]*X*this.tileSize,t.height=this.blockCount[1]*X*this.tileSize,t}},{key:"tileSize",get:function(){return this._tileSize}},{key:"tileCount",get:function(){return[this.blockCount[0]*X,this.blockCount[1]*X]}},{key:"vertexCount",get:function(){var t=this.tileCount;return t[0]+=1,t[1]+=1,t}},{key:"blockCount",get:function(){return this._blockCount}},{key:"lightMapSize",get:function(){return this._lightMapSize}},{key:"weightMapSize",get:function(){return this._weightMapSize}},{key:"heights",get:function(){return this._heights}},{key:"weights",get:function(){return this._weights}},{key:"valid",get:function(){return this._blocks.length>0}},{key:"info",get:function(){var t=new Xt;return t.tileSize=this.tileSize,t.blockCount[0]=this.blockCount[0],t.blockCount[1]=this.blockCount[1],t.weightMapSize=this.weightMapSize,t.lightMapSize=this.lightMapSize,t}}]),n}(S),Et=l(At.prototype,"__asset",[xt,b],(function(){return null})),Bt=l(At.prototype,"_effectAsset",[wt,b],(function(){return null})),Rt=l(At.prototype,"_lightmapInfos",[It,b],(function(){return[]})),Tt=l(At.prototype,"_receiveShadow",[b],(function(){return!1})),Pt=l(At.prototype,"_useNormalmap",[b],(function(){return!1})),Nt=l(At.prototype,"_usePBR",[b],(function(){return!1})),Dt=l(At.prototype,"_lodEnable",[b],(function(){return!1})),Ut=l(At.prototype,"_lodBias",[kt,b],(function(){return 0})),n(At.prototype,"_asset",[St],Object.getOwnPropertyDescriptor(At.prototype,"_asset"),At.prototype),n(At.prototype,"effectAsset",[Lt],Object.getOwnPropertyDescriptor(At.prototype,"effectAsset"),At.prototype),n(At.prototype,"info",[Ct],Object.getOwnPropertyDescriptor(At.prototype,"info"),At.prototype),zt=At))||zt)||zt))}}}));
