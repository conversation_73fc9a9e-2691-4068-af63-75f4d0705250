// Copyright (c) 2017-2020 Xiamen Yaji Software Co., Ltd.

// common module

// constant value
#define QUATER_PI         0.78539816340
#define HALF_PI           1.57079632679
#define PI                3.14159265359
#define PI2               6.28318530718
#define PI4               12.5663706144

#define INV_QUATER_PI     1.27323954474
#define INV_HALF_PI       0.63661977237
#define INV_PI            0.31830988618
#define INV_PI2           0.15915494309
#define INV_PI4           0.07957747155

#define EPSILON           1e-6
#define EPSILON_LOWP      1e-4
#define LOG2              1.442695
#define EXP_VALUE         2.71828183
#define FP_MAX            65504.0
#define FP_SCALE          0.0009765625
#define FP_SCALE_INV      1024.0
#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)

// common functions
#pragma define equalf(data1, data2) (abs(float(data1) - float(data2)) < EPSILON)
#pragma define equalf_lowp(data1, data2) (abs(float(data1) - float(data2)) < EPSILON_LOWP)
#pragma define equalf_epsilon(data1, data2, epsilonValue) (abs(float(data1) - float(data2)) < epsilonValue)
#pragma define clip(value) if(value < 0.0) discard;
#pragma define lerp(value1, value2, value2Multiplier) mix(value1, value2, value2Multiplier)

float saturate(float value) { return clamp(value, 0.0, 1.0); }
vec2 saturate(vec2 value) { return clamp(value, vec2(0.0), vec2(1.0)); }
vec3 saturate(vec3 value) { return clamp(value, vec3(0.0), vec3(1.0)); }
vec4 saturate(vec4 value) { return clamp(value, vec4(0.0), vec4(1.0)); }


// runtime constants
#pragma define-meta CC_DEVICE_SUPPORT_FLOAT_TEXTURE default(1)
#pragma define-meta CC_DEVICE_MAX_FRAGMENT_UNIFORM_VECTORS default(1024)
#pragma define-meta CC_DEVICE_MAX_VERTEX_UNIFORM_VECTORS default(1024)
#pragma define-meta CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS default(128)
#pragma define-meta CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS default(128)
#pragma define-meta CC_DEVICE_CAN_BENEFIT_FROM_INPUT_ATTACHMENT default(0)
#pragma define-meta CC_PLATFORM_ANDROID_AND_WEBGL default(0)
#pragma define-meta CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES default(0)

// When sampling RTs, Y-flip is needed if the screen space sign Y is positive (pointing upwards)
#pragma define CC_HANDLE_RT_SAMPLE_FLIP(uv) uv = cc_cameraPos.w > 1.0 ? vec2(uv.x, 1.0 - uv.y) : uv
#pragma define CC_HANDLE_GET_CLIP_FLIP(uv) uv = cc_cameraPos.w == 0.0 ? vec2(uv.x, -uv.y) : uv

// CC_USE_LIGHTMAP Values
#define LIGHT_MAP_TYPE_DISABLED 0
#define LIGHT_MAP_TYPE_ALL_IN_ONE 1
#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2

// CC_USE_REFLECTION_PROBE Values
#define REFLECTION_PROBE_TYPE_NONE 0
#define REFLECTION_PROBE_TYPE_CUBE 1
#define REFLECTION_PROBE_TYPE_PLANAR 2
#define REFLECTION_PROBE_TYPE_BLEND 3
#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4
#pragma define IS_REFLECTION_PROBE_USE_RGBE(mipcount) mipcount > 1000.0
#pragma define RESTORE_REFLECTION_PROBE_MIP_COUNT(mipcount) if (mipcount > 1000.0) mipcount -= 1000.0

// Light Type
#define LIGHT_TYPE_DIRECTIONAL 0.0
#define LIGHT_TYPE_SPHERE 1.0
#define LIGHT_TYPE_SPOT 2.0
#define LIGHT_TYPE_POINT 3.0
#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0

#define IS_DIRECTIONAL_LIGHT(light_type) equalf_lowp(light_type, LIGHT_TYPE_DIRECTIONAL)
#define IS_SPHERE_LIGHT(light_type) equalf_lowp(light_type, LIGHT_TYPE_SPHERE)
#define IS_SPOT_LIGHT(light_type) equalf_lowp(light_type, LIGHT_TYPE_SPOT)
#define IS_POINT_LIGHT(light_type) equalf_lowp(light_type, LIGHT_TYPE_POINT)
#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) equalf_lowp(light_type, LIGHT_TYPE_RANGED_DIRECTIONAL)

// CC_TONE_MAPPING_TYPE Values
#define TONE_MAPPING_ACES 0
#define TONE_MAPPING_LINEAR 1

// Material
#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0

// CC_USE_DEBUG_VIEW Values
#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE
  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1
#endif
#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC
  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2
#endif
