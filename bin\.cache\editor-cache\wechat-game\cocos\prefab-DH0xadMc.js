System.register(["./index-C5lmLqDW.js","./scene-7MDSMR3j.js","./component-BaGvu7EF.js","./gc-object-CKHc4SnS.js","./global-exports-CR3GRnjt.js","./pipeline-state-manager-Cdpe3is6.js","./touch-DB0AR-Sc.js","./node-event-DTNosVQv.js"],(function(e){"use strict";var t,n,a,o,i,r,s,c,u,h,_,E,l,v,p,m,d,f,g,T,y,N,O,C,I,S,M,U,A,L,D,w,K,b,P,R,F,H,k,Y,G,z,x,W,V,j,B,X,Q,Z,J,q,$,ee;return{setters:[function(e){t=e.$,n=e.V,a=e.J,o=e.m,i=e.r,r=e.o,s=e.B,c=e.h,u=e.c,h=e.a,_=e.s},function(e){E=e.N,l=e.a5,v=e.aa,p=e.ab,m=e.ac,d=e.ad},function(e){f=e.E,g=e.A},function(e){T=e.ac,y=e.n,N=e.U,O=e.h,C=e.W,I=e.ar,S=e.R,M=e.A,U=e._,A=e.d,L=e.w,D=e.a,w=e.as,K=e.I,b=e.a3,P=e.j,R=e.F,F=e.H,H=e.at,k=e.E,Y=e.v,G=e.au},function(e){z=e.c,x=e.l},function(e){W=e.L},function(e){V=e.a,j=e.I,B=e.K,X=e.b,Q=e.c,Z=e.T,J=e.d,q=e.S,$=e.E},function(e){ee=e.N}],execute:function(){e("f",Ne);var te=e("A",(function(e,t,n,a){void 0===e&&(e=0),void 0===t&&(t=0),void 0===n&&(n=0),void 0===a&&(a=0),this.x=e,this.y=t,this.z=n,this.timestamp=a}));T.Attr.setClassAttr(f,"target","type","Object"),T.Attr.setClassAttr(f,"target","ctor",E),t({SystemEventType:{newName:"Input.EventType",since:"3.3.0",removed:!1}}),t({SystemEvent:{newName:"Input",since:"3.4.0",removed:!1},systemEvent:{newName:"input",since:"3.4.0",removed:!1}});var ne=function(){function e(){this._isStarted=!1,this._accelMode="normal",this._eventTarget=new y,this._didAccelerateFunc=this._didAccelerate.bind(this)}var t=e.prototype;return t._registerEvent=function(){N.onAccelerometerChange(this._didAccelerateFunc)},t._unregisterEvent=function(){N.offAccelerometerChange(this._didAccelerateFunc)},t._didAccelerate=function(e){var t=performance.now(),n=new te(e.x,e.y,e.z,t),a=new V(n);this._eventTarget.emit(j.DEVICEMOTION,a)},t.start=function(){var e=this;this._registerEvent(),N.startAccelerometer({interval:this._accelMode,success:function(){e._isStarted=!0}})},t.stop=function(){var e=this;N.stopAccelerometer({success:function(){e._isStarted=!1},fail:function(){O(16305)}}),this._unregisterEvent()},t.setInterval=function(e){this._accelMode=e>=200?"normal":e>=60?"ui":"game",this._isStarted&&(this.stop(),this.start())},t.on=function(e,t,n){this._eventTarget.on(e,t,n)},e}(),ae={Backspace:B.BACKSPACE,Tab:B.TAB,Enter:B.ENTER,ShiftLeft:B.SHIFT_LEFT,ControlLeft:B.CTRL_LEFT,AltLeft:B.ALT_LEFT,ShiftRight:B.SHIFT_RIGHT,ControlRight:B.CTRL_RIGHT,AltRight:B.ALT_RIGHT,Pause:B.PAUSE,CapsLock:B.CAPS_LOCK,Escape:B.ESCAPE,Space:B.SPACE,PageUp:B.PAGE_UP,PageDown:B.PAGE_DOWN,End:B.END,Home:B.HOME,ArrowLeft:B.ARROW_LEFT,ArrowUp:B.ARROW_UP,ArrowRight:B.ARROW_RIGHT,ArrowDown:B.ARROW_DOWN,Insert:B.INSERT,Delete:B.DELETE,Digit0:B.DIGIT_0,Digit1:B.DIGIT_1,Digit2:B.DIGIT_2,Digit3:B.DIGIT_3,Digit4:B.DIGIT_4,Digit5:B.DIGIT_5,Digit6:B.DIGIT_6,Digit7:B.DIGIT_7,Digit8:B.DIGIT_8,Digit9:B.DIGIT_9,KeyA:B.KEY_A,KeyB:B.KEY_B,KeyC:B.KEY_C,KeyD:B.KEY_D,KeyE:B.KEY_E,KeyF:B.KEY_F,KeyG:B.KEY_G,KeyH:B.KEY_H,KeyI:B.KEY_I,KeyJ:B.KEY_J,KeyK:B.KEY_K,KeyL:B.KEY_L,KeyM:B.KEY_M,KeyN:B.KEY_N,KeyO:B.KEY_O,KeyP:B.KEY_P,KeyQ:B.KEY_Q,KeyR:B.KEY_R,KeyS:B.KEY_S,KeyT:B.KEY_T,KeyU:B.KEY_U,KeyV:B.KEY_V,KeyW:B.KEY_W,KeyX:B.KEY_X,KeyY:B.KEY_Y,KeyZ:B.KEY_Z,Numpad0:B.NUM_0,Numpad1:B.NUM_1,Numpad2:B.NUM_2,Numpad3:B.NUM_3,Numpad4:B.NUM_4,Numpad5:B.NUM_5,Numpad6:B.NUM_6,Numpad7:B.NUM_7,Numpad8:B.NUM_8,Numpad9:B.NUM_9,NumpadMultiply:B.NUM_MULTIPLY,NumpadAdd:B.NUM_PLUS,NumpadSubtract:B.NUM_SUBTRACT,NumpadDecimal:B.NUM_DECIMAL,NumpadDivide:B.NUM_DIVIDE,NumpadEnter:B.NUM_ENTER,F1:B.F1,F2:B.F2,F3:B.F3,F4:B.F4,F5:B.F5,F6:B.F6,F7:B.F7,F8:B.F8,F9:B.F9,F10:B.F10,F11:B.F11,F12:B.F12,NumLock:B.NUM_LOCK,ScrollLock:B.SCROLL_LOCK,Semicolon:B.SEMICOLON,Equal:B.EQUAL,Comma:B.COMMA,Minus:B.DASH,Period:B.PERIOD,Slash:B.SLASH,Backquote:B.BACK_QUOTE,BracketLeft:B.BRACKET_LEFT,Backslash:B.BACKSLASH,BracketRight:B.BRACKET_RIGHT,Quote:B.QUOTE};function oe(e){return ae[e]||B.NONE}var ie,re,se=function(){function e(){this._eventTarget=new y,this._keyStateMap={},C.hasFeature(I.EVENT_KEYBOARD)&&this._registerEvent()}var t=e.prototype;return t._registerEvent=function(){var e,t,n=this;null==(e=N.wx)||null==e.onKeyDown||e.onKeyDown((function(e){var t=oe(e.code);if(n._keyStateMap[t]){var a=n._getInputEvent(e,j.KEY_PRESSING);n._eventTarget.emit(j.KEY_PRESSING,a)}else{var o=n._getInputEvent(e,j.KEY_DOWN);n._eventTarget.emit(j.KEY_DOWN,o)}n._keyStateMap[t]=!0})),null==(t=N.wx)||null==t.onKeyUp||t.onKeyUp((function(e){var t=oe(e.code),a=n._getInputEvent(e,j.KEY_UP);n._keyStateMap[t]=!1,n._eventTarget.emit(j.KEY_UP,a)}))},t._getInputEvent=function(e,t){var n=oe(e.code);return new X(n,t)},t.on=function(e,t,n){this._eventTarget.on(e,t,n)},e}(),ce=function(){function e(){this._eventTarget=new y,this._isPressed=!1,this._preMousePos=new n,C.hasFeature(I.EVENT_MOUSE)&&this._registerEvent()}var t=e.prototype;return t._getLocation=function(e){var t=a.windowSize,o=a.devicePixelRatio,i=e.x*o,r=t.height-e.y*o;return new n(i,r)},t._registerEvent=function(){var e,t,n,a;null==(e=N.wx)||null==e.onMouseDown||e.onMouseDown(this._createCallback(j.MOUSE_DOWN)),null==(t=N.wx)||null==t.onMouseMove||t.onMouseMove(this._createCallback(j.MOUSE_MOVE)),null==(n=N.wx)||null==n.onMouseUp||n.onMouseUp(this._createCallback(j.MOUSE_UP)),null==(a=N.wx)||null==a.onWheel||a.onWheel(this._handleMouseWheel.bind(this))},t._createCallback=function(e){var t=this;return function(n){var a=t._getLocation(n),o=n.button;switch(e){case j.MOUSE_DOWN:t._isPressed=!0;break;case j.MOUSE_UP:t._isPressed=!1;break;case j.MOUSE_MOVE:t._isPressed||(o=Q.BUTTON_MISSING)}var i=new Q(e,!1,t._preMousePos);i.setLocation(a.x,a.y),i.setButton(o),i.movementX=a.x-t._preMousePos.x,i.movementY=t._preMousePos.y-a.y,t._preMousePos.set(a.x,a.y),t._eventTarget.emit(e,i)}},t._handleMouseWheel=function(e){var t=j.MOUSE_WHEEL,n=this._getLocation(e),a=e.button,o=new Q(t,!1,this._preMousePos);o.setLocation(n.x,n.y),o.setButton(a),o.movementX=n.x-this._preMousePos.x,o.movementY=this._preMousePos.y-n.y,o.setScrollData(e.deltaX,-e.deltaY),this._preMousePos.set(n.x,n.y),this._eventTarget.emit(j.MOUSE_WHEEL,o)},t.on=function(e,t,n){this._eventTarget.on(e,t,n)},t.dispatchEventsInCache=function(){},e}(),ue=new n,he=new(function(){function e(){this._touchMap=new Map,this._maxTouches=8}var t=e.prototype;return t._createTouch=function(e,t,n){if(this._touchMap.has(e))S(2301);else{if(!this._checkTouchMapSizeMoreThanMax(e)){var a=new Z(t,n,e);return this._touchMap.set(e,a),this._updateTouch(a,t,n),a}S(2300)}},t.releaseTouch=function(e){this._touchMap.has(e)&&this._touchMap.delete(e)},t.getTouch=function(e){return this._touchMap.get(e)},t.getOrCreateTouch=function(e,t,n){var a=this.getTouch(e);return a?this._updateTouch(a,t,n):a=this._createTouch(e,t,n),a},t.getAllTouches=function(){var e=[];return this._touchMap.forEach((function(t){t&&e.push(t)})),e},t.getTouchCount=function(){return this._touchMap.size},t._updateTouch=function(e,t,n){e.getLocation(ue),e.setPrevPoint(ue),e.setPoint(t,n)},t._checkTouchMapSizeMoreThanMax=function(e){var t=this;if(this._touchMap.has(e))return!1;var n=M.ENABLE_MULTI_TOUCH?this._maxTouches:1;if(this._touchMap.size<n)return!1;var a=performance.now();return this._touchMap.forEach((function(e){a-e.lastModified>M.TOUCH_TIMEOUT&&(S(2302,e.getID()),t.releaseTouch(e.getID()))})),n>=this._touchMap.size},e}()),_e=function(){function e(){this._eventTarget=new y,C.hasFeature(I.INPUT_TOUCH)&&this._registerEvent()}var t=e.prototype;return t._registerEvent=function(){N.onTouchStart(this._createCallback(j.TOUCH_START)),N.onTouchMove(this._createCallback(j.TOUCH_MOVE)),N.onTouchEnd(this._createCallback(j.TOUCH_END)),N.onTouchCancel(this._createCallback(j.TOUCH_CANCEL))},t._createCallback=function(e){var t=this;return function(n){for(var o=[],i=a.windowSize,r=a.devicePixelRatio,s=n.changedTouches.length,c=0;c<s;++c){var u=n.changedTouches[c],h=u.identifier;if(null!==h){var _=t._getLocation(u,i,r),E=he.getOrCreateTouch(h,_.x,_.y);E&&(e!==j.TOUCH_END&&e!==j.TOUCH_CANCEL||he.releaseTouch(h),o.push(E))}}if(o.length>0){var l=new J(o,!1,e,he.getAllTouches());t._eventTarget.emit(e,l)}}},t._getLocation=function(e,t,a){var o=e.clientX*a,i=t.height-e.clientY*a;return new n(o,i)},t.on=function(e,t,n){this._eventTarget.on(e,t,n)},t.dispatchEventsInCache=function(){},e}();e("E",re),function(e){e[e.GLOBAL=0]="GLOBAL",e[e.UI=1]="UI"}(re||e("E",re={}));var Ee=function(){function e(e){this.priority=re.GLOBAL,this._inputEventTarget=e}var t=e.prototype;return t.onThrowException=function(){},t.dispatchEvent=function(e){return this._inputEventTarget.emit(e.type,e),!0},e}(),le=((ie={})[j.MOUSE_DOWN]=j.TOUCH_START,ie[j.MOUSE_MOVE]=j.TOUCH_MOVE,ie[j.MOUSE_UP]=j.TOUCH_END,ie),ve=e("I",function(){function e(){this._eventTarget=new y,this._touchInput=new _e,this._mouseInput=new ce,this._keyboardInput=new se,this._accelerometerInput=new ne,this._eventKeyboardList=[],this._eventAccelerationList=[],this._eventGamepadList=[],this._eventHandleList=[],this._eventHMDList=[],this._eventHandheldList=[],this._needSimulateTouchMoveEvent=!1,this._eventDispatcherList=[],this._registerEvent(),this._inputEventDispatcher=new Ee(this._eventTarget),this._registerEventDispatcher(this._inputEventDispatcher)}var t=e.prototype;return t._dispatchMouseDownEvent=function(e){var t,n;null==(t=(n=this._mouseInput).dispatchMouseDownEvent)||t.call(n,e)},t._dispatchMouseMoveEvent=function(e){var t,n;null==(t=(n=this._mouseInput).dispatchMouseMoveEvent)||t.call(n,e)},t._dispatchMouseUpEvent=function(e){var t,n;null==(t=(n=this._mouseInput).dispatchMouseUpEvent)||t.call(n,e)},t._dispatchMouseScrollEvent=function(e){var t,n;null==(t=(n=this._mouseInput).dispatchScrollEvent)||t.call(n,e)},t._dispatchKeyboardDownEvent=function(e){var t,n;null==(t=(n=this._keyboardInput).dispatchKeyboardDownEvent)||t.call(n,e)},t._dispatchKeyboardUpEvent=function(e){var t,n;null==(t=(n=this._keyboardInput).dispatchKeyboardUpEvent)||t.call(n,e)},t.on=function(e,t,n){return this._eventTarget.on(e,t,n),t},t.once=function(e,t,n){return this._eventTarget.once(e,t,n),t},t.off=function(e,t,n){this._eventTarget.off(e,t,n)},t.getTouch=function(e){return he.getTouch(e)},t.getAllTouches=function(){return he.getAllTouches()},t.getTouchCount=function(){return he.getTouchCount()},t.setAccelerometerEnabled=function(e){e?this._accelerometerInput.start():this._accelerometerInput.stop()},t.setAccelerometerInterval=function(e){this._accelerometerInput.setInterval(e)},t._simulateEventTouch=function(e){var t=le[e.type],n=he.getOrCreateTouch(0,e.getLocationX(),e.getLocationY());if(n){var a=[n],o=new J(a,!1,t,t===j.TOUCH_END?[]:a);o.windowId=e.windowId,t===j.TOUCH_END&&he.releaseTouch(0),this._dispatchEventTouch(o)}},t._registerEventDispatcher=function(e){this._eventDispatcherList.push(e),this._eventDispatcherList.sort((function(e,t){return t.priority-e.priority}))},t._emitEvent=function(e){for(var t=this._eventDispatcherList.length,n=0;n<t;++n){var a=this._eventDispatcherList[n];try{if(!a.dispatchEvent(e))break}catch(e){throw this._clearEvents(),a.onThrowException(),e}}},t._registerEvent=function(){var e=this,t=e._touchInput,n=e._mouseInput,a=e._keyboardInput;if(e._handleInput,o.hasFeature(o.Feature.INPUT_TOUCH)&&(t.on(j.TOUCH_START,(function(t){e._dispatchEventTouch(t)})),t.on(j.TOUCH_MOVE,(function(t){e._dispatchEventTouch(t)})),t.on(j.TOUCH_END,(function(t){e._dispatchEventTouch(t)})),t.on(j.TOUCH_CANCEL,(function(t){e._dispatchEventTouch(t)}))),o.hasFeature(o.Feature.EVENT_MOUSE)&&(n.on(j.MOUSE_DOWN,(function(t){e._needSimulateTouchMoveEvent=!0,e._simulateEventTouch(t),e._dispatchEventMouse(t)})),n.on(j.MOUSE_MOVE,(function(t){e._needSimulateTouchMoveEvent&&e._simulateEventTouch(t),e._dispatchEventMouse(t)})),n.on(j.MOUSE_UP,(function(t){e._needSimulateTouchMoveEvent=!1,e._simulateEventTouch(t),e._dispatchEventMouse(t)})),n.on(j.MOUSE_WHEEL,(function(t){e._dispatchEventMouse(t)})),n.on(j.MOUSE_LEAVE,(function(t){e._dispatchEventMouse(t)})),n.on(j.MOUSE_ENTER,(function(t){e._dispatchEventMouse(t)}))),o.hasFeature(o.Feature.EVENT_KEYBOARD)){var i=e._eventKeyboardList;a.on(j.KEY_DOWN,(function(t){e._dispatchOrPushEvent(t,i)})),a.on(j.KEY_PRESSING,(function(t){e._dispatchOrPushEvent(t,i)})),a.on(j.KEY_UP,(function(t){e._dispatchOrPushEvent(t,i)}))}if(o.hasFeature(o.Feature.EVENT_ACCELEROMETER)){var r=e._eventAccelerationList;e._accelerometerInput.on(j.DEVICEMOTION,(function(t){e._dispatchOrPushEvent(t,r)}))}},t._clearEvents=function(){this._eventKeyboardList.length=0,this._eventAccelerationList.length=0,this._eventGamepadList.length=0,this._eventHandleList.length=0,this._eventHMDList.length=0},t._dispatchOrPushEvent=function(e){this._emitEvent(e)},t._dispatchEventMouse=function(e){this._emitEvent(e)},t._dispatchEventTouch=function(e){for(var t=e.getTouches(),n=t.length,a=0;a<n;++a)e.touch=t[a],e.propagationStopped=e.propagationImmediateStopped=!1,this._emitEvent(e)},t._frameDispatchEvents=function(){},e}());ve.EventType=j;var pe=e("i",new ve),me=e("S",function(e){function t(){var t;return t=e.call(this)||this,pe.on(j.MOUSE_DOWN,(function(e){t.emit(q.MOUSE_DOWN,e)})),pe.on(j.MOUSE_MOVE,(function(e){t.emit(q.MOUSE_MOVE,e)})),pe.on(j.MOUSE_UP,(function(e){t.emit(q.MOUSE_UP,e)})),pe.on(j.MOUSE_WHEEL,(function(e){t.emit(q.MOUSE_WHEEL,e)})),pe.on(j.TOUCH_START,(function(e){t.emit(q.TOUCH_START,e.touch,e)})),pe.on(j.TOUCH_MOVE,(function(e){t.emit(q.TOUCH_MOVE,e.touch,e)})),pe.on(j.TOUCH_END,(function(e){t.emit(q.TOUCH_END,e.touch,e)})),pe.on(j.TOUCH_CANCEL,(function(e){t.emit(q.TOUCH_CANCEL,e.touch,e)})),pe.on(j.KEY_DOWN,(function(e){t.emit(q.KEY_DOWN,e)})),pe.on(j.KEY_PRESSING,(function(e){t.emit(q.KEY_DOWN,e)})),pe.on(j.KEY_UP,(function(e){t.emit(q.KEY_UP,e)})),pe.on(j.DEVICEMOTION,(function(e){t.emit(q.DEVICEMOTION,e)})),t}U(t,e);var n=t.prototype;return n.setAccelerometerEnabled=function(e){pe.setAccelerometerEnabled(e)},n.setAccelerometerInterval=function(e){pe.setAccelerometerInterval(e)},n.on=function(t,n,a,o){return e.prototype.on.call(this,t,n,a,o),n},n.off=function(t,n,a){e.prototype.off.call(this,t,n,a)},t}(y));me.EventType=q,z.SystemEvent=me;var de,fe=e("s",new me);z.systemEvent=fe,i(q,"Node.EventType",[{name:"POSITION_PART",newName:"TRANSFORM_CHANGED"},{name:"ROTATION_PART",newName:"TRANSFORM_CHANGED"},{name:"SCALE_PART",newName:"TRANSFORM_CHANGED"}]),i($,"Event",[{name:"ACCELERATION",newName:"DEVICEMOTION",target:me.EventType,targetName:"SystemEvent.EventType"}]),r($,"Event",[{name:"TOUCH",suggest:"please use SystemEvent.EventType.TOUCH_START, SystemEvent.EventType.TOUCH_MOVE, SystemEvent.EventType.TOUCH_END and SystemEvent.EventType.TOUCH_CANCEL instead"},{name:"MOUSE",suggest:"please use SystemEvent.EventType.MOUSE_DOWN, SystemEvent.EventType.MOUSE_MOVE, SystemEvent.EventType.MOUSE_UP, SystemEvent.EventType.MOUSE_WHEEL, Node.EventType.MOUSE_ENTER and Node.EventType.MOUSE_LEAVE instead"},{name:"KEYBOARD",suggest:"please use SystemEvent.EventType.KEY_DOWN and SystemEvent.EventType.KEY_UP instead"}]),i(Q,"EventMouse",["DOWN","UP","MOVE"].map((function(e){return{name:e,newName:"MOUSE_"+e,target:me.EventType,targetName:"SystemEvent.EventType"}}))),i(Q,"EventMouse",[{name:"SCROLL",newName:"MOUSE_WHEEL",target:me.EventType,targetName:"SystemEvent.EventType"}]),r(Q.prototype,"EventMouse.prototype",[{name:"eventType",suggest:"please use EventMouse.prototype.type instead"}]),i(J,"EventTouch",[{name:"BEGAN",newName:"TOUCH_START",target:me.EventType,targetName:"SystemEvent.EventType"}]),i(J,"EventTouch",[{name:"MOVED",newName:"TOUCH_MOVE",target:me.EventType,targetName:"SystemEvent.EventType"}]),i(J,"EventTouch",[{name:"ENDED",newName:"TOUCH_END",target:me.EventType,targetName:"SystemEvent.EventType"}]),i(J,"EventTouch",[{name:"CANCELLED",newName:"TOUCH_CANCEL",target:me.EventType,targetName:"SystemEvent.EventType"}]),r(J.prototype,"EventTouch.prototype",[{name:"getEventCode",suggest:"please use EventTouch.prototype.type instead"}]),i(J.prototype,"EventTouch.prototype",[{name:"getUILocationInView",newName:"getLocationInView",target:J,targetName:"EventTouch"}]),r(M.KEY,"macro.KEY",["back","menu","0","1","2","3","4","5","6","7","8","9","0","*","+","-","/",";","=",",",".","[","]","dpadLeft","dpadRight","dpadUp","dpadDown","dpadCenter"].map((function(e){return{name:e}}))),r(M.KEY,"macro.KEY",[{name:"shift",suggest:"please use KeyCode.SHIFT_LEFT instead"}]),r(M.KEY,"macro.KEY",[{name:"ctrl",suggest:"please use KeyCode.CTRL_LEFT instead"}]),r(M.KEY,"macro.KEY",[{name:"alt",suggest:"please use KeyCode.ALT_LEFT instead"}]),r(M,"macro",[{name:"KEY",suggest:"please use KeyCode instead"}]),i(E.prototype,"Node",[{name:"childrenCount",newName:"children.length",customGetter:function(){return this.children.length}}]),i(E.prototype,"Node",[{name:"width",targetName:"node.getComponent(UITransform)",customGetter:function(){return this._getUITransformComp().width},customSetter:function(e){this._getUITransformComp().width=e}},{name:"height",targetName:"node.getComponent(UITransform)",customGetter:function(){return this._getUITransformComp().height},customSetter:function(e){this._getUITransformComp().height=e}},{name:"anchorX",targetName:"node.getComponent(UITransform)",customGetter:function(){return this._getUITransformComp().anchorX},customSetter:function(e){this._getUITransformComp().anchorX=e}},{name:"anchorY",targetName:"node.getComponent(UITransform)",customGetter:function(){return this._getUITransformComp().anchorY},customSetter:function(e){this._getUITransformComp().anchorY=e}},{name:"getAnchorPoint",targetName:"node.getComponent(UITransform)",customFunction:function(e){return e||(e=new n),e.set(this._getUITransformComp().anchorPoint),e}},{name:"setAnchorPoint",targetName:"node.getComponent(UITransform)",customFunction:function(e,t){this._getUITransformComp().setAnchorPoint(e,t)}},{name:"getContentSize",targetName:"node.getComponent(UITransform)",customFunction:function(e){return e||(e=new s),e.set(this._getUITransformComp().contentSize),e}},{name:"setContentSize",targetName:"node.getComponent(UITransform)",customFunction:function(e,t){"number"==typeof e?this._getUITransformComp().setContentSize(e,t):this._getUITransformComp().setContentSize(e)}}]),c(l.prototype,"SceneGlobals.prototype",[{name:"aspect"},{name:"selfShadow"},{name:"linear"},{name:"packing"},{name:"autoAdapt"},{name:"fixedArea"},{name:"pcf"},{name:"bias"},{name:"normalBias"},{name:"near"},{name:"far"},{name:"shadowDistance"},{name:"invisibleOcclusionRange"},{name:"orthoSize"},{name:"saturation"}]),i(l.prototype,"SceneGlobals.prototype",[{name:"distance",newName:"planeHeight"},{name:"normal",newName:"planeDirection"},{name:"size",newName:"shadowMapSize"}]),c(E.prototype,"Node.prototype",[{name:"addLayer"},{name:"removeLayer"}]),i(v.prototype,"NodeUIProperties",[{name:"opacityDirty",newName:"colorDirty"}]),c(W,"Layers",[{name:"All"},{name:"RaycastMask"},{name:"check"}]),i(W,"Layers",[{name:"Default",newName:"DEFAULT",target:W.Enum,targetName:"Layers.Enum"},{name:"Always",newName:"ALWAYS",target:W.Enum,targetName:"Layers.Enum"},{name:"IgnoreRaycast",newName:"IGNORE_RAYCAST",target:W.Enum,targetName:"Layers.Enum"},{name:"Gizmos",newName:"GIZMOS",target:W.Enum,targetName:"Layers.Enum"},{name:"Editor",newName:"EDITOR",target:W.Enum,targetName:"Layers.Enum"},{name:"UI",newName:"UI_3D",target:W.Enum,targetName:"Layers.Enum"},{name:"UI2D",newName:"UI_2D",target:W.Enum,targetName:"Layers.Enum"},{name:"SceneGizmo",newName:"SCENE_GIZMO",target:W.Enum,targetName:"Layers.Enum"},{name:"makeInclusiveMask",newName:"makeMaskInclude",target:W,targetName:"Layers"},{name:"makeExclusiveMask",newName:"makeMaskExclude",target:W,targetName:"Layers"}]),c(W.Enum,"Layers.Enum",[{name:"ALWAYS"}]),c(W.BitMask,"Layers.BitMask",[{name:"ALWAYS"}]);var ge=A.HideInHierarchy,Te=A.DontSave,ye=e("a",u("cc.PrivateNode")(de=function(e){function t(t){var n;return n=e.call(this,t)||this,L(12003,n.name),n.hideFlags|=Te|ge,n}return U(t,e),t}(E))||de);function Ne(e,t){if(!t){var n=x.director.getScene();if(!n)return null;t=n}return t.getChildByPath(e)}i(q,"SystemEventType",["MOUSE_ENTER","MOUSE_LEAVE","TRANSFORM_CHANGED","SCENE_CHANGED_FOR_PERSISTS","SIZE_CHANGED","ANCHOR_CHANGED","COLOR_CHANGED","CHILD_ADDED","CHILD_REMOVED","PARENT_CHANGED","NODE_DESTROYED","LAYER_CHANGED","SIBLING_ORDER_CHANGED"].map((function(e){return{name:e,target:E.EventType,targetName:"Node.EventType"}}))),i(E.EventType,"Node.EventType",[{name:"DEVICEMOTION",target:me.EventType,targetName:"SystemEvent.EventType"},{name:"KEY_DOWN",target:me.EventType,targetName:"SystemEvent.EventType"},{name:"KEY_UP",target:me.EventType,targetName:"SystemEvent.EventType"}]),x.PrivateNode=ye,t({BaseNode:{newName:"Node",since:"3.7.0",removed:!1}}),x.find=Ne;var Oe=K,Ce=A.IsStartCalled,Ie=A.IsOnEnableCalled;function Se(e,t){for(var n=t.constructor._executionOrder,a=t._id,o=0,i=e.length-1,r=i>>>1;o<=i;r=o+i>>>1){var s=e[r],c=s.constructor._executionOrder;if(c>n)i=r-1;else if(c<n)o=r+1;else{var u=s._id;if(u>a)i=r-1;else{if(!(u<a))return r;o=r+1}}}return~o}function Me(e,t){for(var n=e.array,a=e.i+1;a<n.length;){var o=n[a];o.node._activeInHierarchy?++a:(e.removeAt(a),t&&(o._objFlags&=~t))}}A.IsEditorOnEnableCalled;var Ue=function(){function e(e){var t=w;this._zero=new t([]),this._neg=new t([]),this._pos=new t([]),this._invoke=e}return D(e,[{key:"zero",get:function(){return this._zero}},{key:"neg",get:function(){return this._neg}},{key:"pos",get:function(){return this._pos}}]),e}();function Ae(e,t){return e.constructor._executionOrder-t.constructor._executionOrder}Ue.stableRemoveInactive=Me;var Le=function(e){function t(){return e.apply(this,arguments)||this}U(t,e);var n=t.prototype;return n.add=function(e){var t=e.constructor._executionOrder;(0===t?this._zero:t<0?this._neg:this._pos).array.push(e)},n.remove=function(e){var t=e.constructor._executionOrder;(0===t?this._zero:t<0?this._neg:this._pos).fastRemove(e)},n.cancelInactive=function(e){Me(this._zero,e),Me(this._neg,e),Me(this._pos,e)},n.invoke=function(){var e=this._neg;e.array.length>0&&(e.array.sort(Ae),this._invoke(e),e.array.length=0),this._invoke(this._zero),this._zero.array.length=0;var t=this._pos;t.array.length>0&&(t.array.sort(Ae),this._invoke(t),t.array.length=0)},t}(Ue),De=function(e){function t(t){return e.call(this,t)||this}U(t,e);var n=t.prototype;return n.add=function(e){var t=e.constructor._executionOrder;if(0===t)this._zero.array.push(e);else{var n=t<0?this._neg.array:this._pos.array,a=Se(n,e);a<0&&n.splice(~a,0,e)}},n.remove=function(e){var t=e.constructor._executionOrder;if(0===t)this._zero.fastRemove(e);else{var n=t<0?this._neg:this._pos,a=Se(n.array,e);a>=0&&n.removeAt(a)}},n.invoke=function(e){this._neg.array.length>0&&this._invoke(this._neg,e),this._invoke(this._zero,e),this._pos.array.length>0&&this._invoke(this._pos,e)},t}(Ue);function we(e,t,n){return function(a,o){try{t(a,o)}catch(t){x._throw(t);var i=a.array;for(n&&(i[a.i]._objFlags|=n),++a.i;a.i<i.length;++a.i)try{e(i[a.i],o)}catch(e){x._throw(e),n&&(i[a.i]._objFlags|=n)}}}}var Ke=we((function(e){e.start(),e._objFlags|=Ce}),(function(e){var t=e.array;for(e.i=0;e.i<t.length;++e.i){var n=t[e.i];n.start(),n._objFlags|=Ce}}),Ce),be=we((function(e,t){e.update(t)}),(function(e,t){var n=e.array;for(e.i=0;e.i<n.length;++e.i)n[e.i].update(t)})),Pe=we((function(e,t){e.lateUpdate(t)}),(function(e,t){var n=e.array;for(e.i=0;e.i<n.length;++e.i)n[e.i].lateUpdate(t)})),Re=function(e){var t=x.director._compScheduler,n=e.array;for(e.i=0;e.i<n.length;++e.i){var a=n[e.i];a._enabled&&(a.onEnable(),!a.node._activeInHierarchy||t._onEnabled(a))}},Fe=(e("C",function(){function e(){this._deferredComps=[],this.unscheduleAll()}var t=e.prototype;return t.unscheduleAll=function(){this.startInvoker=new Le(Ke),this.updateInvoker=new De(be),this.lateUpdateInvoker=new De(Pe),this._updating=!1},t._onEnabled=function(e){x.director.getScheduler().resumeTarget(e),e._objFlags|=Ie,this._updating?this._deferredComps.push(e):this._scheduleImmediate(e)},t._onDisabled=function(e){x.director.getScheduler().pauseTarget(e),e._objFlags&=~Ie;var t=this._deferredComps.indexOf(e);t>=0?Oe(this._deferredComps,t):(!e.internalStart||e._objFlags&Ce||this.startInvoker.remove(e),e.internalUpdate&&this.updateInvoker.remove(e),e.internalLateUpdate&&this.lateUpdateInvoker.remove(e))},t.enableComp=function(e,t){if(!(e._objFlags&Ie)){if(e.internalOnEnable){if(t)return void t.add(e);if(e.internalOnEnable(),!e.node.activeInHierarchy)return}this._onEnabled(e)}},t.disableComp=function(e){e._objFlags&Ie&&(e.internalOnDisable&&e.internalOnDisable(),this._onDisabled(e))},t.startPhase=function(){this._updating=!0,this.startInvoker.invoke(),this._startForNewComps()},t.updatePhase=function(e){this.updateInvoker.invoke(e)},t.lateUpdatePhase=function(e){this.lateUpdateInvoker.invoke(e),this._updating=!1,this._startForNewComps()},t._startForNewComps=function(){this._deferredComps.length>0&&(this._deferredSchedule(),this.startInvoker.invoke())},t._scheduleImmediate=function(e){"function"!=typeof e.internalStart||e._objFlags&Ce||this.startInvoker.add(e),"function"==typeof e.internalUpdate&&this.updateInvoker.add(e),"function"==typeof e.internalLateUpdate&&this.lateUpdateInvoker.add(e)},t._deferredSchedule=function(){for(var e=this._deferredComps,t=0,n=e.length;t<n;t++)this._scheduleImmediate(e[t]);e.length=0},e}()),A.IsPreloadStarted),He=A.IsOnLoadStarted,ke=A.IsOnLoadCalled,Ye=A.IsOnEnableCalled,Ge=A.Deactivating,ze=function(e){function t(t){return e.call(this,t)||this}U(t,e);var n=t.prototype;return n.add=function(e){this._zero.array.push(e)},n.remove=function(e){this._zero.fastRemove(e)},n.cancelInactive=function(e){Ue.stableRemoveInactive(this._zero,e)},n.invoke=function(){this._invoke(this._zero),this._zero.array.length=0},t}(Ue),xe=we((function(e){null==e.internalPreload||e.internalPreload()}),(function(e){var t=e.array;for(e.i=0;e.i<t.length;++e.i){var n,a;null==(n=(a=t[e.i]).internalPreload)||n.call(a)}})),We=we((function(e){null==e.internalOnLoad||e.internalOnLoad(),e._objFlags|=ke}),(function(e){var t=e.array;for(e.i=0;e.i<t.length;++e.i){var n=t[e.i];null==n.internalOnLoad||n.internalOnLoad(),n._objFlags|=ke}}),ke),Ve=new b(4);function je(e,t,n){O(3817,e.name,n),F("Corrupted component value:",t),t?e._removeComponent(t):H(e.getWritableComponents(),n)}Ve.get=function(){var e=this._get()||{preload:new ze(xe),onLoad:new Le(We),onEnable:new Le(Re)};e.preload.zero.i=-1;var t=e.onLoad;return t.zero.i=-1,t.neg.i=-1,t.pos.i=-1,(t=e.onEnable).zero.i=-1,t.neg.i=-1,t.pos.i=-1,e},e("N",function(){function e(){this.reset()}var t=e.prototype;return t.reset=function(){this._activatingStack=[]},t.activateNode=function(e,t){if(t){var n=Ve.get();n&&(this._activatingStack.push(n),this._activateNodeRecursively(e,n.preload,n.onLoad,n.onEnable),n.preload.invoke(),n.onLoad.invoke(),n.onEnable.invoke(),this._activatingStack.pop(),Ve.put(n))}else{this._deactivateNodeRecursively(e);for(var a,o=this._activatingStack,i=P(o);!(a=i()).done;){var r=a.value;r.preload.cancelInactive(Fe),r.onLoad.cancelInactive(He),r.onEnable.cancelInactive(Ye)}}e.emit(ee.ACTIVE_IN_HIERARCHY_CHANGED,e)},t.activateComp=function(e,t,n,a){if(R(e,!0)&&(e._objFlags&Fe||(e._objFlags|=Fe,e.internalPreload&&(t?t.add(e):e.internalPreload())),e._objFlags&He||(e._objFlags|=He,e.internalOnLoad?n?n.add(e):(e.internalOnLoad(),e._objFlags|=ke):e._objFlags|=ke),e._enabled)){if(!e.node.activeInHierarchy)return;x.director._compScheduler.enableComp(e,a)}},t.destroyComp=function(e){x.director._compScheduler.disableComp(e),e.internalOnDestroy&&e._objFlags&ke&&e.internalOnDestroy()},t._activateNodeRecursively=function(e,t,n,a){if(e._objFlags&Ge)O(3816,e.name);else{e._setActiveInHierarchy(!0);for(var o=e.components.length,i=0;i<o;++i){var r=e.components[i];r instanceof x.Component?this.activateComp(r,t,n,a):(je(e,r,i),--i,--o)}for(var s=0,c=e.children.length;s<c;++s){var u=e.children[s];u.active&&this._activateNodeRecursively(u,t,n,a)}e._onPostActivated(!0)}},t._deactivateNodeRecursively=function(e){e._objFlags|=Ge,e._setActiveInHierarchy(!1);for(var t=e.components.length,n=0;n<t;++n){var a=e.components[n];if(a._enabled&&(x.director._compScheduler.disableComp(a),e.activeInHierarchy))return void(e._objFlags&=~Ge)}for(var o=0,i=e.children.length;o<i;++o){var r=e.children[o];if(r.activeInHierarchy&&(this._deactivateNodeRecursively(r),e.activeInHierarchy))return void(e._objFlags&=~Ge)}e._onPostActivated(!1),e._objFlags&=~Ge},e}());var Be,Xe,Qe,Ze,Je,qe,$e=k({AUTO:0,SINGLE_INSTANCE:1,MULTI_INSTANCE:2}),et=e("P",u("cc.Prefab")((qe=function(e){function t(){var t;return(t=e.call(this)||this).data=Qe&&Qe(),t.optimizationPolicy=Ze&&Ze(),t.persistent=Je&&Je(),t._createFunction=null,t._instantiatedTimes=0,t}U(t,e);var n=t.prototype;return n.createNode=function(e){var t=x.instantiate(this);t.name=this.name,e(null,t)},n.compileCreateFunction=function(){},n._doInstantiate=function(e){return this.data._prefab||L(3700),this._createFunction||this.compileCreateFunction(),this._createFunction(e)},n._instantiate=function(){var e;return e=this.data._instantiate(),++this._instantiatedTimes,e},n.initDefault=function(t){e.prototype.initDefault.call(this,t),this.data=new E,this.data.name="(Missing Node)";var n=new x._PrefabInfo;n.asset=this,n.root=this.data,this.data._prefab=n},n.validate=function(){return!!this.data},n.onLoaded=function(){var e=this.data;p(e),m(e)},t}(g),qe.OptimizationPolicy=$e,qe.OptimizationPolicyThreshold=3,Qe=h((Xe=qe).prototype,"data",[_],(function(){return null})),Ze=h(Xe.prototype,"optimizationPolicy",[_],(function(){return $e.AUTO})),Je=h(Xe.prototype,"persistent",[_],(function(){return!1})),Be=Xe))||Be);Y(et,"_utils",d),x.Prefab=et,G(x,"cc._Prefab","Prefab")}}}));
