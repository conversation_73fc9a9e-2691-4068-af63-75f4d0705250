System.register(["./index-Y4La_nfG.js","./director-8iUu7HD2.js","./gc-object-D18ulfCO.js","./global-exports-CLZKKIY2.js","./scene-ArUG4OfI.js","./prefab-BQYc0LyR.js","./device-manager-B8lyUDPi.js","./buffer-barrier-q_79u36H.js","./pipeline-state-manager-DQyhxoC_.js","./debug-view-BP17WHcy.js","./node-event-DTNosVQv.js","./component-CsuvAQKv.js","./factory-BOc5khhM.js","./deprecated-D5UVm7fE.js"],(function(e,t){"use strict";var i,n,s,r,a,o,h,u,l,c,d,g,_,p,f,m,E,T,S,v,R,w,y,I,b,P,N,A,x,D,M,F,O,C,L,z,B,H,k,V,X,W,U,j,G,Y,q,Q,J,Z,K,$,ee,te,ie,ne,se,re,ae,oe,he,ue,le,ce,de,ge,_e,pe;return{setters:[function(e){i=e.y,n=e.B,s=e.N,r=e.I,a=e.z,o=e.V,h=e.k,u=e.w,l=e.S,c=e.M,d=e.m,g=e.O,_=e.P,p=e.l,f=e.o,m=e.h,E=e.r},function(e){T=e.d,S=e.X,v=e.D,R=e.a},function(e){w=e.a,y=e.U,I=e.z,b=e.W,P=e._,N=e.s,A=e.S,x=e.h,D=e.X,M=e.Y,F=e.Z,O=e.$,C=e.a0,L=e.a1,z=e.G,B=e.Q,H=e.a2,k=e.m},function(e){V=e.c,X=e.a,W=e.l,U=e.E,j=e.e},function(e){G=e.b,Y=e.i,q=e.d},function(e){Q=e.i},function(e){J=e.d,Z=e.L},function(e){K=e.ab,$=e.ac,ee=e.B,te=e.b,ie=e.M,ne=e.A,se=e.F,re=e.I,ae=e.ad,oe=e.ae,he=e.T,ue=e.d,le=e.e,ce=e.f},function(e){de=e.P,ge=e.S,_e=e.k,pe=e.L},null,null,null,null,null],execute:function(){var fe,me=function(){function e(){var e=this;this._rafHandle=0,this._onTick=null,this._targetFrameRate=60,this._isPlaying=!1,this._updateCallback=function(){e._isPlaying&&(e._rafHandle=requestAnimationFrame(e._updateCallback)),e._onTick&&e._onTick()}}var t=e.prototype;return t.start=function(){this._isPlaying||(this._rafHandle=requestAnimationFrame(this._updateCallback),this._isPlaying=!0)},t.stop=function(){this._isPlaying&&(cancelAnimationFrame(this._rafHandle),this._rafHandle=0,this._isPlaying=!1)},w(e,[{key:"targetFrameRate",get:function(){return this._targetFrameRate},set:function(e){this._targetFrameRate!==e&&(this._targetFrameRate=e,y.setPreferredFramesPerSecond(this._targetFrameRate),this._isPlaying&&(this.stop(),this.start()))}},{key:"onTick",get:function(){return this._onTick},set:function(e){this._onTick=e}}]),e}(),Ee=i(),Te=((fe={})[I.ORIENTATION_AUTO]=b.AUTO,fe[I.ORIENTATION_LANDSCAPE]=b.LANDSCAPE,fe[I.ORIENTATION_PORTRAIT]=b.PORTRAIT,fe),Se=e("V",function(e){function t(){var t;(t=e.call(this)||this)._designResolutionSize=i(0,0),t._scaleX=1,t._scaleY=1,t._viewportRect=u(),t._visibleRect=u(),t._autoFullScreen=!1,t._retinaEnabled=!1,t._resizeCallback=null;var n=ve,s=Re;return t._rpExactFit=new xe(n.EQUAL_TO_FRAME,s.EXACT_FIT),t._rpShowAll=new xe(n.EQUAL_TO_FRAME,s.SHOW_ALL),t._rpNoBorder=new xe(n.EQUAL_TO_FRAME,s.NO_BORDER),t._rpFixedHeight=new xe(n.EQUAL_TO_FRAME,s.FIXED_HEIGHT),t._rpFixedWidth=new xe(n.EQUAL_TO_FRAME,s.FIXED_WIDTH),t._resolutionPolicy=t._rpShowAll,t}P(t,e);var h=t.prototype;return h.init=function(){var e=n.windowSize,t=e.width,i=e.height;this._designResolutionSize.width=t,this._designResolutionSize.height=i,this._viewportRect.width=t,this._viewportRect.height=i,this._visibleRect.width=t,this._visibleRect.height=i,Ee.width=this._visibleRect.width,Ee.height=this._visibleRect.height,s.init(this._visibleRect),this.resizeWithBrowserSize(!0);var r=N.querySettings(A.SCREEN,"designResolution");r&&this.setDesignResolutionSize(Number(r.width),Number(r.height),r.policy||xe.FIXED_HEIGHT),n.on("window-resize",this._updateAdaptResult,this),n.on("fullscreen-change",this._updateAdaptResult,this)},h.resizeWithBrowserSize=function(e){r.handleResizeEvent=e},h.setResizeCallback=function(e){"function"!=typeof e&&null!=e||(this._resizeCallback=e)},h.setOrientation=function(e){r.orientation=Te[e]},h.adjustViewportMeta=function(){},h.enableRetina=function(e){this._retinaEnabled=!!e},h.isRetinaEnabled=function(){return this._retinaEnabled},h.enableAutoFullScreen=function(e){e!==this._autoFullScreen&&(this._autoFullScreen=e,e&&n.requestFullScreen().catch((function(){})))},h.isAutoFullScreenEnabled=function(){return this._autoFullScreen},h.setCanvasSize=function(e,t){r.resolutionScale=1;var i=r.devicePixelRatio,s=new a(e*i,t*i);n.windowSize=s},h.getCanvasSize=function(){return n.windowSize},h.getFrameSize=function(){var e=r.devicePixelRatio,t=n.windowSize;return t.width/=e,t.height/=e,t},h.setFrameSize=function(e,t){var i=r.devicePixelRatio;n.windowSize=new a(e*i,t*i)},h.getVisibleSize=function(){return new a(this._visibleRect.width,this._visibleRect.height)},h.getVisibleSizeInPixel=function(){return new a(this._visibleRect.width*this._scaleX,this._visibleRect.height*this._scaleY)},h.getVisibleOrigin=function(){return new o(this._visibleRect.x,this._visibleRect.y)},h.getVisibleOriginInPixel=function(){return new o(this._visibleRect.x*this._scaleX,this._visibleRect.y*this._scaleY)},h.getResolutionPolicy=function(){return this._resolutionPolicy},h._updateResolutionPolicy=function(e){if(e instanceof xe)this._resolutionPolicy=e;else{var t=xe;e===t.EXACT_FIT&&(this._resolutionPolicy=this._rpExactFit),e===t.SHOW_ALL&&(this._resolutionPolicy=this._rpShowAll),e===t.NO_BORDER&&(this._resolutionPolicy=this._rpNoBorder),e===t.FIXED_HEIGHT&&(this._resolutionPolicy=this._rpFixedHeight),e===t.FIXED_WIDTH&&(this._resolutionPolicy=this._rpFixedWidth)}},h.setResolutionPolicy=function(e){this._updateResolutionPolicy(e);var t=De.getDesignResolutionSize();De.setDesignResolutionSize(t.width,t.height,e)},h.setDesignResolutionSize=function(e,t,i){if(e>0&&t>0){this._updateResolutionPolicy(i);var n=this._resolutionPolicy;n&&n.preApply(this),this._designResolutionSize.width=e,this._designResolutionSize.height=t;var r=n.apply(this,this._designResolutionSize);if(r.scale&&2===r.scale.length&&(this._scaleX=r.scale[0],this._scaleY=r.scale[1]),r.viewport){var a=this._viewportRect,o=this._visibleRect,h=r.viewport;a.x=h.x,a.y=h.y,a.width=h.width,a.height=h.height,o.x=0,o.y=0,o.width=h.width/this._scaleX,o.height=h.height/this._scaleY}n.postApply(this),Ee.width=this._visibleRect.width,Ee.height=this._visibleRect.height,s.init(this._visibleRect),this.emit("design-resolution-changed")}else x(2200)},h.getDesignResolutionSize=function(){return new a(this._designResolutionSize.width,this._designResolutionSize.height)},h.setRealPixelResolution=function(e,t,i){this.setDesignResolutionSize(e,t,i)},h.getViewportRect=function(){return this._viewportRect},h.getScaleX=function(){return this._scaleX},h.getScaleY=function(){return this._scaleY},h.getDevicePixelRatio=function(){return r.devicePixelRatio},h.convertToLocationInView=function(e,t,i,s){void 0===s&&(s=new o);var a=r.devicePixelRatio*(e-i.left),h=r.devicePixelRatio*(i.top+i.height-t);return r.isFrameRotated?(s.x=n.windowSize.width-h,s.y=a):(s.x=a,s.y=h),s},h._convertToUISpace=function(e){var t=this._viewportRect;e.x=(e.x-t.x)/this._scaleX,e.y=(e.y-t.y)/this._scaleY},h._updateAdaptResult=function(e,t,i){V.director.root.resize(e,t,void 0===i||0===i?1:i);var n=this._designResolutionSize,s=n.width,r=n.height;e>0&&t>0?this.setDesignResolutionSize(s,r,this._resolutionPolicy):D(!1,"_updateAdaptResult Invalid size."),this.emit("canvas-resize"),this._resizeCallback&&this._resizeCallback()},t}(M(l)));Se.instance=void 0;var ve=function(){function e(){this.name="ContainerStrategy"}var t=e.prototype;return t.preApply=function(){},t.apply=function(){},t.postApply=function(){},t._setupCanvas=function(){var e=V.game.canvas;if(e){var t=n.windowSize;e.width!==t.width&&(e.width=t.width),e.height!==t.height&&(e.height=t.height)}},e}();ve.EQUAL_TO_FRAME=void 0,ve.PROPORTION_TO_FRAME=void 0;var Re=function(){function e(){this.name="ContentStrategy",this._result={scale:[1,1],viewport:null},this._strategy=xe.UNKNOWN}var t=e.prototype;return t.preApply=function(){},t.apply=function(){return{scale:[1,1]}},t.postApply=function(){},t._buildResult=function(e,t,i,n,s,r){Math.abs(e-i)<2&&(i=e),Math.abs(t-n)<2&&(n=t);var a=new h(Math.round((e-i)/2),Math.round((t-n)/2),i,n),o=this._result;return o.scale=[s,r],o.viewport=a,o},w(e,[{key:"strategy",get:function(){return this._strategy}}]),e}(),we=function(e){function t(){var t;return(t=e.call(this)||this).name="EqualToFrame",t}return P(t,e),t.prototype.apply=function(){r.isProportionalToFrame=!1,this._setupCanvas()},t}(ve),ye=function(e){function t(){var t;return(t=e.call(this)||this).name="ProportionalToFrame",t}return P(t,e),t.prototype.apply=function(){r.isProportionalToFrame=!0,this._setupCanvas()},t}(ve);ve.EQUAL_TO_FRAME=new we,ve.PROPORTION_TO_FRAME=new ye;var Ie=function(e){function t(){var t;return(t=e.call(this)||this).name="ExactFit",t._strategy=xe.EXACT_FIT,t}return P(t,e),t.prototype.apply=function(e,t){var i=n.windowSize,s=i.width,r=i.height,a=s/t.width,o=r/t.height;return this._buildResult(s,r,s,r,a,o)},t}(Re),be=function(e){function t(){var t;return(t=e.call(this)||this).name="ShowAll",t._strategy=xe.SHOW_ALL,t}return P(t,e),t.prototype.apply=function(e,t){var i,s,r=n.windowSize,a=r.width,o=r.height,h=t.width,u=t.height,l=a/h,c=o/u,d=0;return l<c?(i=a,s=u*(d=l)):(i=h*(d=c),s=o),this._buildResult(a,o,i,s,d,d)},t}(Re),Pe=function(e){function t(){var t;return(t=e.call(this)||this).name="NoBorder",t._strategy=xe.NO_BORDER,t}return P(t,e),t.prototype.apply=function(e,t){var i,s,r,a=n.windowSize,o=a.width,h=a.height,u=t.width,l=t.height,c=o/u,d=h/l;return c<d?(s=u*(i=d),r=h):(s=o,r=l*(i=c)),this._buildResult(o,h,s,r,i,i)},t}(Re),Ne=function(e){function t(){var t;return(t=e.call(this)||this).name="FixedHeight",t._strategy=xe.FIXED_HEIGHT,t}return P(t,e),t.prototype.apply=function(e,t){var i=n.windowSize,s=i.width,r=i.height,a=r/t.height,o=s,h=r;return this._buildResult(s,r,o,h,a,a)},t}(Re),Ae=function(e){function t(){var t;return(t=e.call(this)||this).name="FixedWidth",t._strategy=xe.FIXED_WIDTH,t}return P(t,e),t.prototype.apply=function(e,t){var i=n.windowSize,s=i.width,r=i.height,a=s/t.width,o=s,h=r;return this._buildResult(s,r,o,h,a,a)},t}(Re),xe=e("R",function(){function e(e,t){this.name="ResolutionPolicy",this._containerStrategy=e,this._contentStrategy=t}var t=e.prototype;return t.preApply=function(e){this._contentStrategy.preApply(e)},t.apply=function(e,t){return this._containerStrategy.apply(e,t),this._contentStrategy.apply(e,t)},t.postApply=function(e){this._contentStrategy.postApply(e)},t.setContainerStrategy=function(e){this._containerStrategy=e},t.setContentStrategy=function(e){this._contentStrategy=e},t.getContentStrategy=function(){return this._contentStrategy},w(e,[{key:"canvasSize",get:function(){return n.windowSize}}]),e}());xe.EXACT_FIT=0,xe.NO_BORDER=1,xe.SHOW_ALL=2,xe.FIXED_HEIGHT=3,xe.FIXED_WIDTH=4,xe.UNKNOWN=5,xe.ContainerStrategy=ve,xe.ContentStrategy=Re,V.ResolutionPolicy=xe,Re.EXACT_FIT=new Ie,Re.SHOW_ALL=new be,Re.NO_BORDER=new Pe,Re.FIXED_HEIGHT=new Ne,Re.FIXED_WIDTH=new Ae;var De=e("v",Se.instance=V.view=new Se);T.registerSystem("view",De,0),V.winSize=Ee;var Me=new o;function Fe(e,t,i,n){e.setProperty(t,i,n)}var Oe=function(){var e=t.prototype;function t(){this.settings=void 0,this._curTime=0,this.device=void 0,this.swapchain=void 0,this.shader=void 0,this.sampler=void 0,this.cmdBuff=void 0,this.quadAssmebler=void 0,this.vertexBuffers=void 0,this.indicesBuffers=void 0,this.renderArea=void 0,this.clearColors=void 0,this.projection=void 0,this.isMobile=!1,this.bgMat=void 0,this.bgImage=void 0,this.bgTexture=void 0,this.logoMat=void 0,this.logoImage=void 0,this.logoTexture=void 0,this.watermarkMat=void 0,this.watermarkTexture=void 0,this.bgWidth=1920,this.bgHeight=1080,this.logoWidthTemp=140,this.logoHeightTemp=200,this.logoWidth=0,this.logoHeight=0,this.logoXTrans=.5,this.logoYTrans=1/6****/6,this.textSize=24,this.textHeight=24,this.textXTrans=.5,this.textYExtraTrans=32,this.textExpandSize=4,this.scaleSize=1}return e.init=function(){var e,t,i,n,s,r,a,o=this,h=xe.SHOW_ALL,u=N.querySettings(A.SCREEN,"designResolution");if(null!==u&&(h=u.policy),this.settings={policy:null!==(e=h)&&void 0!==e?e:xe.SHOW_ALL,displayRatio:null!==(t=N.querySettings(A.SPLASH_SCREEN,"displayRatio"))&&void 0!==t?t:.4,totalTime:null!==(i=N.querySettings(A.SPLASH_SCREEN,"totalTime"))&&void 0!==i?i:3e3,watermarkLocation:null!==(n=N.querySettings(A.SPLASH_SCREEN,"watermarkLocation"))&&void 0!==n?n:"default",autoFit:null===(s=N.querySettings(A.SPLASH_SCREEN,"autoFit"))||void 0===s||s,logo:null!==(r=N.querySettings(A.SPLASH_SCREEN,"logo"))&&void 0!==r?r:void 0,background:null!==(a=N.querySettings(A.SPLASH_SCREEN,"background"))&&void 0!==a?a:void 0},this._curTime=0,!(this.settings.totalTime<=0||void 0===this.settings.logo||void 0===this.settings.background)){this.device=V.director.root.device,this.swapchain=V.director.root.mainWindow.swapchain,this.preInit(),this.initLayout(),"default"===this.settings.logo.type&&this.initWaterMark();var l=Promise.resolve(),c=Promise.resolve();return"custom"===this.settings.background.type&&(l=new Promise((function(e,t){o.bgImage=new X.Image,o.bgImage.onload=function(){o.initBG(),e()},o.bgImage.onerror=function(){t()},o.bgImage.src=o.settings.background.base64}))),"none"!==this.settings.logo.type&&(c=new Promise((function(e,t){o.logoImage=new X.Image,o.logoImage.onload=function(){o.initLogo(),e()},o.logoImage.onerror=function(){t()},o.logoImage.src=o.settings.logo.base64}))),Promise.all([l,c])}return this.settings.totalTime=0,Promise.resolve([])},e.preInit=function(){var e,t=null==(e=this.settings.background)?void 0:e.color;this.clearColors=t?[new K(t.x,t.y,t.z,t.w)]:[new K(0,0,0,1)];var i=this.device,n=this.swapchain,s=i.capabilities;this.renderArea=new $(0,0,n.width,n.height),this.cmdBuff=i.commandBuffer;var r=new Float32Array([.5,.5,1,0,-.5,.5,0,0,.5,-.5,1,1,-.5,-.5,0,1]),a=4*Float32Array.BYTES_PER_ELEMENT,o=4*a;this.vertexBuffers=i.createBuffer(new ee(te.VERTEX|te.TRANSFER_DST,ie.DEVICE,o,a)),this.vertexBuffers.update(r);var h=new Uint16Array([0,1,2,1,3,2]),u=Uint16Array.BYTES_PER_ELEMENT,l=6*u;this.indicesBuffers=i.createBuffer(new ee(te.INDEX|te.TRANSFER_DST,ie.DEVICE,l,u)),this.indicesBuffers.update(h);var g=[new ne("a_position",se.RG32F),new ne("a_texCoord",se.RG32F)],_=new re(g,[this.vertexBuffers],this.indicesBuffers);this.quadAssmebler=i.createInputAssembler(_),this.projection=new c,c.ortho(this.projection,-1,1,-1,1,-1,1,s.clipSpaceMinZ,s.clipSpaceSignY,n.surfaceTransform),this.isMobile=d.isMobile},e.initLayout=function(){this.isMobile?(this.bgWidth=812,this.bgHeight=375,this.logoWidthTemp=70,this.logoHeightTemp=100,this.textSize=12,this.textHeight=this.textSize+this.textExpandSize,this.textXTrans=.5,this.textYExtraTrans=16):(this.bgWidth=1920,this.bgHeight=1080,this.logoWidthTemp=140,this.logoHeightTemp=200,this.textSize=24,this.textHeight=this.textSize+this.textExpandSize,this.textXTrans=.5,this.textYExtraTrans=32),this.logoXTrans=.5,this.logoYTrans=1/6****/6,this.initScale()},e.initScale=function(){var e=this.swapchain.width,t=this.swapchain.height,i=this.isMobile?375:1080,n=this.isMobile?812:1920;if(e>t){var s=n;n=i,i=s}this.scaleSize=e/t>16/9?t/n:e/i},e.update=function(e){var t=this.settings,i=this.device,n=this.swapchain,s=i.capabilities;c.ortho(this.projection,-1,1,-1,1,-1,1,s.clipSpaceMinZ,s.clipSpaceSignY,n.surfaceTransform);var r=n.width,a=n.height;this.initScale(),this._curTime+=1e3*e;var o=g(this._curTime/t.totalTime),h=_(o),u=1,l=1,d=this.bgImage;if("custom"===t.background.type){t.policy===xe.FIXED_WIDTH?(u=r,l=r/d.width*d.height):t.policy===xe.FIXED_HEIGHT?(u=a/d.height*d.width,l=a):t.policy===xe.SHOW_ALL?d.width/this.bgHeight>r/a?(u=r,l=r/d.width*d.height):(u=a/d.height*d.width,l=a):t.policy===xe.NO_BORDER?d.width/d.height>r/a?(u=a/d.height*d.width,l=a):(u=r,l=r/d.width*d.height):(u=r,l=a);var p=this.bgMat;Fe(p,"resolution",Me.set(r,a),0),Fe(p,"scale",Me.set(u,l),0),Fe(p,"translate",Me.set(.5*r,.5*a),0),Fe(p,"percent",1),Fe(p,"u_projection",this.projection),p.passes[0].update()}var f=a*this.logoYTrans;if("none"!==this.settings.logo.type){l=.185*a*t.displayRatio,u=this.logoWidth*(.185*a/this.logoHeight)*t.displayRatio;var m=this.logoMat;Fe(m,"resolution",Me.set(r,a),0),Fe(m,"scale",Me.set(u,l),0),Fe(m,"translate",Me.set(r*this.logoXTrans,f),0),Fe(m,"percent",h),Fe(m,"u_projection",this.projection),m.passes[0].update()}if("default"===this.settings.logo.type&&this.watermarkMat){var E=this.watermarkTexture.width,T=this.watermarkTexture.height;u=E,l=T;var S=f-(.5*this.logoHeight*t.displayRatio+this.textYExtraTrans)*this.scaleSize-.5*T,v=this.watermarkMat;Fe(v,"resolution",Me.set(r,a),0),Fe(v,"scale",Me.set(u,l),0),Fe(v,"translate",Me.set(r*this.textXTrans,S),0),Fe(v,"percent",h),Fe(v,"u_projection",this.projection),v.passes[0].update()}this.frame()},e.initBG=function(){var e=this.device;this.bgMat=new G,this.bgMat.initialize({effectName:"util/splash-screen"});var t=new ae;t.addressU=oe.CLAMP,t.addressV=oe.CLAMP,t.addressW=oe.CLAMP,this.sampler=e.getSampler(t),this.bgTexture=e.createTexture(new he(ue.TEX2D,le.SAMPLED|le.TRANSFER_DST,se.RGBA8,this.bgImage.width,this.bgImage.height));var i=this.bgMat.passes[0],n=i.getBinding("mainTexture");i.bindTexture(n,this.bgTexture),this.shader=i.getShaderVariant();var s=i.descriptorSet;s.bindSampler(n,this.sampler),s.update();var r=new ce,a=r.texExtent;a.width=this.bgImage.width,a.height=this.bgImage.height,a.depth=1,e.copyTexImagesToTexture([this.bgImage],this.bgTexture,[r])},e.initLogo=function(){var e=this.device;this.logoMat=new G,this.logoMat.initialize({effectName:"util/splash-screen"});var t=new ae;t.addressU=oe.CLAMP,t.addressV=oe.CLAMP,t.addressW=oe.CLAMP,this.sampler=e.getSampler(t),this.logoTexture=e.createTexture(new he(ue.TEX2D,le.SAMPLED|le.TRANSFER_DST,se.RGBA8,this.logoImage.width,this.logoImage.height));var i=this.logoMat.passes[0],n=i.getBinding("mainTexture");i.bindTexture(n,this.logoTexture),this.shader=i.getShaderVariant();var s=i.descriptorSet;s.bindSampler(n,this.sampler),s.update();var r=new ce,a=r.texExtent;a.width=this.logoImage.width,a.height=this.logoImage.height,a.depth=1,e.copyTexImagesToTexture([this.logoImage],this.logoTexture,[r]);var o=this.logoImage.width/this.logoImage.height;o<1?(this.logoWidth=this.logoWidthTemp,this.logoHeight=this.logoWidthTemp/o):(this.logoWidth=this.logoHeightTemp*o,this.logoHeight=this.logoHeightTemp)},e.initWaterMark=function(){var e=X.document.createElement("canvas");e.height=this.textHeight*this.scaleSize,e.style.width=""+e.width,e.style.height=""+e.height;var t="Created with Cocos",i=e.getContext("2d");i.font=this.textSize*this.scaleSize+"px Arial",i.textBaseline="top",i.textAlign="center",i.fillStyle="#707070";var n=i.measureText(t).width+10;e.width=n,i.font=this.textSize*this.scaleSize+"px Arial",i.textBaseline="top",i.textAlign="center",i.fillStyle="#707070",i.fillText(t,e.width/2,0);var s=new ce,r=s.texExtent;r.width=e.width,r.height=e.height,r.depth=1,this.watermarkTexture=this.device.createTexture(new he(ue.TEX2D,le.SAMPLED|le.TRANSFER_DST,se.RGBA8,e.width,e.height)),this.device.copyTexImagesToTexture([e],this.watermarkTexture,[s]),this.watermarkMat=new G,this.watermarkMat.initialize({effectName:"util/splash-screen"});var a=this.watermarkMat.passes[0],o=a.getBinding("mainTexture");a.bindTexture(o,this.watermarkTexture),a.descriptorSet.update()},e.frame=function(){var e=this.device,t=this.swapchain,i=this.projection,n=this.bgMat,s=this.logoMat,r=this.watermarkMat,a=this.settings,o=this.quadAssmebler,h=e.capabilities;if(!d.isXR||xr.entry.isRenderAllowable())for(var u=d.isXR?2:1,l=0;l<u;l++){if(d.isXR){xr.entry.renderLoopStart(l);var g=xr.entry.getEyeFov(l),_=1,f=1;l===S.LEFT?_=Math.abs(Math.tan(g[0]))/Math.abs(Math.tan(g[1])):l===S.RIGHT&&(f=Math.abs(Math.tan(g[1]))/Math.abs(Math.tan(g[0]))),c.ortho(i,-_,f,-1,1,-1,1,h.clipSpaceMinZ,h.clipSpaceSignY,t.surfaceTransform),i.m00=p[t.surfaceTransform][0],i.m05=p[t.surfaceTransform][3]*h.clipSpaceSignY,"custom"===a.background.type&&(Fe(n,"u_projection",i),n.passes[0].update()),"none"!==a.logo.type&&(Fe(s,"u_projection",i),s.passes[0].update()),"default"===a.logo.type&&r&&(Fe(r,"u_projection",i),r.passes[0].update())}e.enableAutoBarrier(!0),e.acquire([t]);var m=this.cmdBuff,E=V.director.root.mainWindow.framebuffer,T=this.renderArea;if(T.width=t.width,T.height=t.height,m.begin(),m.beginRenderPass(E.renderPass,E,T,this.clearColors,1,0),V.director.root.pipeline,"custom"===a.background.type){var v=n.passes[0],R=de.getOrCreatePipelineState(e,v,this.shader,E.renderPass,o);m.bindPipelineState(R),m.bindDescriptorSet(ge.MATERIAL,v.descriptorSet),m.bindInputAssembler(o),m.draw(o)}if("none"!==a.logo.type){var w=s.passes[0],y=de.getOrCreatePipelineState(e,w,this.shader,E.renderPass,o);m.bindPipelineState(y),m.bindDescriptorSet(ge.MATERIAL,w.descriptorSet),m.bindInputAssembler(o),m.draw(o)}if("default"===a.logo.type&&r){var I=this.watermarkMat.passes[0],b=de.getOrCreatePipelineState(e,I,this.shader,E.renderPass,o);m.bindPipelineState(b),m.bindDescriptorSet(ge.MATERIAL,I.descriptorSet),m.bindInputAssembler(o),m.draw(o)}m.endRenderPass(),m.end(),e.flushCommands([m]),e.queue.submit([m]),e.present(),e.enableAutoBarrier(!W.rendering),d.isXR&&xr.entry.renderLoopEnd(l)}},e.destroy=function(){this.device=null,this.swapchain=null,this.clearColors=null,this.bgImage&&(this.bgImage.destroy&&this.bgImage.destroy(),this.bgImage=null),this.bgMat&&(this.bgMat.destroy(),this.bgMat=null),this.bgTexture&&(this.bgTexture.destroy(),this.bgTexture=null),this.logoImage&&(this.logoImage.destroy&&this.logoImage.destroy(),this.logoImage=null),this.logoMat&&(this.logoMat.destroy(),this.logoMat=null),this.logoTexture&&(this.logoTexture.destroy(),this.logoTexture=null),this.renderArea=null,this.cmdBuff=null,this.shader=null,this.quadAssmebler&&(this.quadAssmebler.destroy(),this.quadAssmebler=null),this.vertexBuffers&&(this.vertexBuffers.destroy(),this.vertexBuffers=null),this.indicesBuffers&&(this.indicesBuffers.destroy(),this.indicesBuffers=null),this.sampler=null,this.watermarkMat&&(this.watermarkMat.destroy(),this.watermarkMat=null),this.watermarkTexture&&(this.watermarkTexture.destroy(),this.watermarkTexture=null),this.settings=null},t.createInstance=function(){return t._ins=new t,t._ins},t.releaseInstance=function(){t._ins&&(t._ins.destroy(),t._ins=null)},w(t,[{key:"isFinished",get:function(){return this._curTime>=this.settings.totalTime}},{key:"curTime",get:function(){return this._curTime},set:function(e){this._curTime=e}}],[{key:"instance",get:function(){return t._ins}}]),t}();Oe._ins=null,V.internal.SplashScreen=Oe;var Ce=new(function(){function e(){this._data=null}return e.prototype.init=function(e){var t=this;return void 0===e&&(e=""),W.rendering&&W.rendering.enableEffectImport&&e?new Promise((function(i,n){if(e.startsWith("http")){var s=new XMLHttpRequest;s.open("GET",e),s.responseType="arraybuffer",s.onload=function(){t._data=s.response,i()},s.onerror=function(){n(new Error("request effect settings failed!"))},s.send(null)}else globalThis.fsUtils.readArrayBuffer(e,(function(e,s){e?n(e):(t._data=s,i())}))})):Promise.resolve()},w(e,[{key:"data",get:function(){return this._data}}]),e}());W.effectSettings=Ce;var Le=N.querySettings.bind(N),ze=e("G",function(e){function i(){var t;return(t=e.call(this)||this).frame=null,t.container=null,t.canvas=null,t.renderType=-1,t.eventTargetOn=e.prototype.on,t.eventTargetOnce=e.prototype.once,t.config={},t.onStart=null,t.frameTime=1e3/60,t._isCloning=!1,t._inited=!1,t._engineInited=!1,t._rendererInitialized=!1,t._paused=!0,t._pausedByEngine=!1,t._frameRate=60,t._pacer=null,t._initTime=0,t._startTime=0,t._deltaTime=0,t._useFixedDeltaTime=!1,t._shouldLoadLaunchScene=!0,t.onPreBaseInitDelegate=new H,t.onPostBaseInitDelegate=new H,t.onPreInfrastructureInitDelegate=new H,t.onPostInfrastructureInitDelegate=new H,t.onPreSubsystemInitDelegate=new H,t.onPostSubsystemInitDelegate=new H,t.onPreProjectInitDelegate=new H,t.onPostProjectInitDelegate=new H,t}P(i,e);var s=i.prototype;return s.setFrameRate=function(e){this.frameRate=e},s.getFrameRate=function(){return this.frameRate},s.step=function(){T.tick(this._calculateDT(!0))},s.pauseByEngine=function(){this._paused||(this._pausedByEngine=!0,this.pause())},s.resumeByEngine=function(){this._pausedByEngine&&(this.resume(),this._pausedByEngine=!1)},s.pause=function(){var e;this._paused||(this._paused=!0,null==(e=this._pacer)||e.stop(),this.emit(i.EVENT_PAUSE))},s.resume=function(){var e;this._paused&&(Q._clearEvents(),this._paused=!1,null==(e=this._pacer)||e.start(),this.emit(i.EVENT_RESUME))},s.isPaused=function(){return this._paused},s.restart=function(){var e=this;return new Promise((function(e){T.once(v.END_FRAME,(function(){return e()}))})).then((function(){T.reset(),V.Object._deferredDestroy(),e.pause(),e.resume(),e._shouldLoadLaunchScene=!0})).then((function(){return Oe.createInstance().init()})).then((function(){e._safeEmit(i.EVENT_RESTART)}))},s.end=function(){F.close()},s.on=function(e,t,i,n){return this.canRegisterEvent(e)&&t.call(i),this.eventTargetOn(e,t,i,n)},s.once=function(e,t,i){return this.canRegisterEvent(e)?t.call(i):this.eventTargetOnce(e,t,i)},s.canRegisterEvent=function(e){return this._engineInited&&e===i.EVENT_ENGINE_INITED||this._inited&&e===i.EVENT_GAME_INITED||this._rendererInitialized&&e===i.EVENT_RENDERER_INITED},s.init=function(e){var s=this;return this._compatibleWithOldParams(e),Promise.resolve().then((function(){return s.emit(i.EVENT_PRE_BASE_INIT),s.onPreBaseInitDelegate.dispatch()})).then((function(){var t=e.debugMode||O.NONE;C(t)})).then((function(){return d.init()})).then((function(){s._initEvents()})).then((function(){return N.init(e.settingsPath,e.overrideSettings)})).then((function(){return s.emit(i.EVENT_POST_BASE_INIT),s.onPostBaseInitDelegate.dispatch()})).then((function(){return s.emit(i.EVENT_PRE_INFRASTRUCTURE_INIT),s.onPreInfrastructureInitDelegate.dispatch()})).then((function(){I.init(),s._initXR();var e,t={frame:e=document.createElement("div"),canvas:window.canvas,container:e};return s.canvas=t.canvas,s.frame=t.frame,s.container=t.container,n.init(),L.init(),J.init(s.canvas,_e)})).then((function(){if(Le(A.RENDERING,"customPipeline")){if(!V.rendering)return void x(12109);I.CUSTOM_PIPELINE_NAME||(I.CUSTOM_PIPELINE_NAME="Builtin")}else V.rendering=void 0;Y.init(),q.init(),pe.init(),s.initPacer()})).then((function(){return s.emit(i.EVENT_POST_INFRASTRUCTURE_INIT),s.onPostInfrastructureInitDelegate.dispatch()})).then((function(){return s.emit(i.EVENT_PRE_SUBSYSTEM_INIT),s.onPreSubsystemInitDelegate.dispatch()})).then((function(){return Ce.init(Le(A.RENDERING,"effectSettingsPath"))})).then((function(){if(V.rendering&&V.rendering.enableEffectImport)if(Le(A.RENDERING,"renderMode")!==Z.HEADLESS){var e=Ce.data;null!==e?V.rendering.init(J.gfxDevice,e):x(1102)}else V.rendering.init(J.gfxDevice,null)})).then((function(){var e=Le(A.SCRIPTING,"scriptPackages");return e?Promise.all(e.map((function(e){return t.import(e)}))):Promise.resolve([])})).then((function(){return T.init(),q.loadBuiltinAssets()})).then((function(){return s.emit(i.EVENT_POST_SUBSYSTEM_INIT),s.onPostSubsystemInitDelegate.dispatch()})).then((function(){z("Cocos Creator v"+j),s.emit(i.EVENT_ENGINE_INITED),s._engineInited=!0})).then((function(){return s.emit(i.EVENT_PRE_PROJECT_INIT),s.onPreProjectInitDelegate.dispatch()})).then((function(){var e=Le(A.PLUGINS,"jsList"),t=Promise.resolve();return e&&e.forEach((function(e){t=t.then((function(){return t="src/"+e,globalThis.__taobaoRequire(t);var t}))})),t})).then((function(){return s._loadProjectBundles()})).then((function(){return s._loadCCEScripts()})).then((function(){return s._setupRenderPipeline()})).then((function(){return s._loadPreloadAssets()})).then((function(){return q.compileBuiltinMaterial(),Oe.createInstance().init()})).then((function(){return s.emit(i.EVENT_POST_PROJECT_INIT),s.onPostProjectInitDelegate.dispatch()})).then((function(){s._inited=!0,s._safeEmit(i.EVENT_GAME_INITED)}))},s._initXR=function(){var e;if(void 0===globalThis.__globalXR&&(globalThis.__globalXR={}),globalThis.__globalXR.webxrCompatible=null!==(e=Le(A.XR,"webxrCompatible"))&&void 0!==e&&e,d.isXR){var t,i;xr.entry=xr.XrEntry.getInstance();var n=null!==(t=Le(A.RENDERING,"msaa"))&&void 0!==t?t:1,s=null!==(i=Le(A.RENDERING,"renderingScale"))&&void 0!==i?i:1;xr.entry.setMultisamplesRTT(n),xr.entry.setRenderingScale(s)}},s._compatibleWithOldParams=function(e){var t=e.overrideSettings=e.overrideSettings||{};"showFPS"in e&&(t.profiling=t.profiling||{},t.profiling.showFPS=e.showFPS),"frameRate"in e&&(t.screen=t.screen||{},t.screen.frameRate=e.frameRate),"renderMode"in e&&(t.rendering=t.rendering||{},t.rendering.renderMode=e.renderMode),"renderPipeline"in e&&(t.rendering=t.rendering||{},t.rendering.renderPipeline=e.renderPipeline),"assetOptions"in e&&(t.assets=t.assets||{},Object.assign(t.assets,e.assetOptions)),"customJointTextureLayouts"in e&&(t.animation=t.animation||{},t.animation.customJointTextureLayouts=e.customJointTextureLayouts),"physics"in e&&(t.physics=t.physics||{},Object.assign(t.physics,e.physics)),"orientation"in e&&(t.screen=t.screen||{},t.screen.orientation=e.orientation),"exactFitScreen"in e&&(t.screen=t.screen||{},t.screen.exactFitScreen=e.exactFitScreen)},s._loadPreloadAssets=function(){var e=Le(A.ASSETS,"preloadAssets");return e?Promise.all(e.map((function(e){return new Promise((function(t,i){Y.loadAny(e,(function(e){e?i(e):t()}))}))}))):Promise.resolve([])},s._loadCCEScripts=function(){return new Promise((function(e){e()}))},s._loadProjectBundles=function(){var e=Le(A.ASSETS,"preloadBundles");return e?Promise.all(e.map((function(e){var t=e.bundle,i=e.version;return new Promise((function(e,n){var s={};i&&(s.version=i),Y.loadBundle(t,s,(function(t){t?n(t):e()}))}))}))):Promise.resolve([])},s.run=function(e){e&&(this.onStart=e),this._inited&&!U&&this.resume()},s._calculateDT=function(e){if(this._useFixedDeltaTime=e,e)return this._startTime=performance.now(),this.frameTime/1e3;var t=performance.now();return this._deltaTime=t>this._startTime?(t-this._startTime)/1e3:0,this._deltaTime>i.DEBUG_DT_THRESHOLD&&(this._deltaTime=this.frameTime/1e3),this._startTime=t,this._deltaTime},s._updateCallback=function(){var e=this;if(this._inited)if(Oe.instance&&!Oe.instance.isFinished)Oe.instance.update(this._calculateDT(!1));else if(this._shouldLoadLaunchScene){Oe.releaseInstance(),this._shouldLoadLaunchScene=!1;var t,i=Le(A.LAUNCH,"launchScene");i?T.loadScene(i,(function(){B(1103,i),e._initTime=performance.now(),T.startAnimation(),null==e.onStart||e.onStart()})):(this._initTime=performance.now(),T.startAnimation(),null==(t=this.onStart)||t.call(this))}else T.tick(this._calculateDT(!1))},s.initPacer=function(){var e,t=null!==(e=Le(A.SCREEN,"frameRate"))&&void 0!==e?e:60;D("number"==typeof t),this._pacer=new me,this._pacer.onTick=this._updateCallback.bind(this),this.frameRate=t},s._initEvents=function(){F.on("show",this._onShow,this),F.on("hide",this._onHide,this),F.on("close",this._onClose,this)},s._onHide=function(){this.emit(i.EVENT_HIDE),this.pauseByEngine()},s._onShow=function(){this.emit(i.EVENT_SHOW),this.resumeByEngine()},s._onClose=function(){this.emit(i.EVENT_CLOSE),F.exit()},s.addPersistRootNode=function(e){T.addPersistRootNode(e)},s.removePersistRootNode=function(e){T.removePersistRootNode(e)},s.isPersistRootNode=function(e){return T.isPersistRootNode(e)},s._setupRenderPipeline=function(){var e=Le(A.RENDERING,"customPipeline");return this._setRenderPipeline(!!e)},s._setRenderPipeline=function(e){T.root.setRenderPipeline(e)?(this._rendererInitialized=!0,this._safeEmit(i.EVENT_RENDERER_INITED)):x(1222)},s._safeEmit=function(e){this.emit(e)},w(i,[{key:"inited",get:function(){return this._inited}},{key:"frameRate",get:function(){return this._frameRate},set:function(e){"number"!=typeof e&&(e=parseInt(e,10),Number.isNaN(e)&&(e=60)),this._frameRate=e,this.frameTime=1e3/e,this._pacer&&(this._pacer.targetFrameRate=this._frameRate)}},{key:"deltaTime",get:function(){return this._useFixedDeltaTime?this.frameTime/1e3:this._deltaTime}},{key:"totalTime",get:function(){return performance.now()-this._initTime}},{key:"frameStartTime",get:function(){return this._startTime}}]),i}(k));ze.EVENT_HIDE="game_on_hide",ze.EVENT_SHOW="game_on_show",ze.EVENT_LOW_MEMORY="game_on_low_memory",ze.EVENT_GAME_INITED="game_inited",ze.EVENT_ENGINE_INITED="engine_inited",ze.EVENT_RENDERER_INITED="renderer_inited",ze.EVENT_PRE_BASE_INIT="pre_base_init",ze.EVENT_POST_BASE_INIT="post_base_init",ze.EVENT_PRE_INFRASTRUCTURE_INIT="pre_infrastructure_init",ze.EVENT_POST_INFRASTRUCTURE_INIT="post_infrastructure_init",ze.EVENT_PRE_SUBSYSTEM_INIT="pre_subsystem_init",ze.EVENT_POST_SUBSYSTEM_INIT="post_subsystem_init",ze.EVENT_PRE_PROJECT_INIT="pre_project_init",ze.EVENT_POST_PROJECT_INIT="post_project_init",ze.EVENT_RESTART="game_on_restart",ze.EVENT_PAUSE="game_on_pause",ze.EVENT_RESUME="game_on_resume",ze.EVENT_CLOSE="game_on_close",ze.RENDER_TYPE_CANVAS=0,ze.RENDER_TYPE_WEBGL=1,ze.RENDER_TYPE_OPENGL=2,ze.RENDER_TYPE_HEADLESS=3,ze.DEBUG_DT_THRESHOLD=1,V.Game=ze;var Be=e("g",V.game=new ze);f(R.prototype,"director",[{name:"calculateDeltaTime"},{name:"getDeltaTime",suggest:"Use game.deltaTime instead"},{name:"getTotalTime",suggest:"Use game.totalTime instead"},{name:"getCurrentTime",suggest:"Use game.frameStartTime instead"}]),m(R.prototype,"director",[{name:"setAnimationInterval",suggest:"please use game.frameRate instead"},{name:"getAnimationInterval",suggest:"please use game.frameRate instead"},{name:"getRunningScene",suggest:"please use getScene instead"},{name:"setDepthTest",suggest:"please use camera API instead"},{name:"setClearColor",suggest:"please use camera API instead"},{name:"getWinSize",suggest:"please use view.getVisibleSize instead"},{name:"getWinSizeInPixels"},{name:"purgeCachedData",suggest:"please use assetManager.releaseAll instead"},{name:"convertToGL"},{name:"convertToUI"}]),E(T,"director",[{name:"_getSceneUuid",targetName:"assetManager.main",newName:"getSceneInfo",customFunction:function(e){var t;return Y.main?null==(t=Y.main.getSceneInfo(e))?void 0:t.uuid:""}}]),f(Be,"game",[{name:"collisionMatrix"},{name:"groupList"}]),E(Be,"game",[{name:"_sceneInfos",targetName:"assetManager.main",newName:"getSceneInfo",customGetter:function(){var e=[];return Y.main&&Y.main.config.scenes.forEach((function(t){e.push(t)})),e}}])}}}));
