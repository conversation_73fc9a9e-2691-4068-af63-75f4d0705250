System.register(["./global-exports-CLZKKIY2.js"],(function(t){"use strict";var i;return{setters:[function(t){i=t.l}],execute:function(){var n=t("AffineTransform",function(){function t(t,i,n,a,c,x){void 0===t&&(t=1),void 0===i&&(i=0),void 0===n&&(n=0),void 0===a&&(a=1),void 0===c&&(c=0),void 0===x&&(x=0),this.a=t,this.b=i,this.c=n,this.d=a,this.tx=c,this.ty=x}return t.identity=function(){return new t},t.clone=function(i){return new t(i.a,i.b,i.c,i.d,i.tx,i.ty)},t.concat=function(t,i,n){var a=i.a,c=i.b,x=i.c,d=i.d,y=i.tx,r=i.ty;t.a=a*n.a+c*n.c,t.b=a*n.b+c*n.d,t.c=x*n.a+d*n.c,t.d=x*n.b+d*n.d,t.tx=y*n.a+r*n.c+n.tx,t.ty=y*n.b+r*n.d+n.ty},t.invert=function(t,i){var n=1/(i.a*i.d-i.b*i.c);t.a=n*i.d,t.b=-n*i.b,t.c=-n*i.c,t.d=n*i.a,t.tx=n*(i.c*i.ty-i.d*i.tx),t.ty=n*(i.b*i.tx-i.a*i.ty)},t.fromMat4=function(t,i){t.a=i.m00,t.b=i.m01,t.c=i.m04,t.d=i.m05,t.tx=i.m12,t.ty=i.m13},t.transformVec2=function(t,i,n,a){var c,x;a?(c=i,x=n):(a=n,c=i.x,x=i.y),t.x=a.a*c+a.c*x+a.tx,t.y=a.b*c+a.d*x+a.ty},t.transformSize=function(t,i,n){t.width=n.a*i.width+n.c*i.height,t.height=n.b*i.width+n.d*i.height},t.transformRect=function(t,i,n){var a=i.x+i.width,c=i.y+i.height,x=n.a*i.x+n.c*i.y+n.tx,d=n.b*i.x+n.d*i.y+n.ty,y=n.a*a+n.c*i.y+n.tx,r=n.b*a+n.d*i.y+n.ty,o=n.a*i.x+n.c*c+n.tx,e=n.b*i.x+n.d*c+n.ty,h=n.a*a+n.c*c+n.tx,f=n.b*a+n.d*c+n.ty,b=Math.min(x,y,o,h),s=Math.max(x,y,o,h),u=Math.min(d,r,e,f),m=Math.max(d,r,e,f);t.x=b,t.y=u,t.width=s-b,t.height=m-u},t.transformObb=function(t,i,n,a,c,x,d){void 0===d&&(d=!0);var y=x.a*c.x+x.c*c.y+x.tx,r=x.b*c.x+x.d*c.y+x.ty,o=x.a*c.width,e=x.b*c.width,h=x.c*c.height,f=x.d*c.height;d?(i.x=y,i.y=r,n.x=o+y,n.y=e+r,t.x=h+y,t.y=f+r,a.x=o+h+y,a.y=e+f+r):(t.x=y,t.y=r,a.x=o+y,a.y=e+r,i.x=h+y,i.y=f+r,n.x=o+h+y,n.y=e+f+r)},t}());i.AffineTransform=n}}}));
