System.register(["./_commonjsHelpers-gZMueHPa.js","./index-DoSzW704.js","./gc-object-CKHc4SnS.js","./index-C5lmLqDW.js","./collision-matrix-B7MK4XMK.js","./util-D0MMXf4o.js","./physics-framework.js","./scene-7MDSMR3j.js","./deprecated-T2yM9nsJ.js","./director-DIlqD2Nd.js","./global-exports-CR3GRnjt.js","./component-BaGvu7EF.js","./factory-D9_8ZCqM.js","./device-manager-BvjvoelW.js","./buffer-barrier-CuX_5NUR.js","./deprecated-Ca3AjUwj.js","./pipeline-state-manager-Cdpe3is6.js","./debug-view-CKetkq9d.js","./prefab-DH0xadMc.js","./touch-DB0AR-Sc.js","./node-event-DTNosVQv.js","./mesh-C8knhDLk.js","./rendering-sub-mesh-CowWLfXC.js","./wasm-minigame-DoCiKH-Y.js","./zlib.min-CyXMsivM.js","./skeleton-d3ONjcrt.js","./terrain-asset-CHGiv9LN.js","./capsule-sT4rQfGi.js","./base.js","./deprecated-D4QUWou_.js","./render-types-DTmbYHic.js","./pipeline-scene-data-B9tBPl1_.js","./deprecated-C8l6Kwy8.js","./camera-component-Df61RNZm.js","./model-renderer-BcRDUYby.js","./renderer-9hfAnqUF.js","./instantiate-Bj9mBEzA.js","./move-BmfpEZyZ.js"],(function(){"use strict";var t,e,i,o,n,s,r,a,l,h,p,c,u,d,y,v,f,m,w,g,b,x,B,S,A,C,E;return{setters:[function(e){t=e.g},function(t){e=t.P,i=t.j,o=t.s},function(t){n=t.a,s=t.I,r=t.w,a=t.L,l=t._},function(t){h=t.b,p=t.Q,c=t.A,u=t.C,d=t.g,y=t.al},function(t){v=t.a,f=t.P,m=t.c,w=t.E},function(t){g=t.g,b=t.s,x=t.a,B=t.V},null,function(t){S=t.T},function(t){A=t.g,C=t.G},function(t){E=t.d},null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){function M(t){throw new Error('Could not dynamically require "'+t+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var _={exports:{}};_.exports=function t(e,i,o){function n(r,a){if(!i[r]){if(!e[r]){if(!a&&M)return M(r);if(s)return s(r,!0);throw new Error("Cannot find module '"+r+"'")}var l=i[r]={exports:{}};e[r][0].call(l.exports,(function(t){return n(e[r][1][t]||t)}),l,l.exports,t,e,i,o)}return i[r].exports}for(var s=M,r=0;r<o.length;r++)n(o[r]);return n}({1:[function(t,e){e.exports={name:"@cocos/cannon",version:"1.2.8",description:"A lightweight 3D physics engine written in JavaScript.",homepage:"https://github.com/cocos-creator/cannon.js",author:"Stefan Hedman <<EMAIL>> (http://steffe.se), JayceLai",keywords:["cannon.js","cocos","creator","physics","engine","3d"],scripts:{build:"grunt && npm run preprocess && grunt addLicense && grunt addDate",preprocess:"node node_modules/uglify-js/bin/uglifyjs build/cannon.js -o build/cannon.min.js -c -m",postpublish:"cnpm sync @cocos/cannon"},main:"./build/cannon.js",engines:{node:"*"},repository:{type:"git",url:"https://github.com/cocos-creator/cannon.js.git"},bugs:{url:"https://github.com/cocos-creator/cannon.js/issues"},licenses:[{type:"MIT"}],devDependencies:{jshint:"latest","uglify-js":"latest",nodeunit:"^0.9.0",grunt:"~0.4.0","grunt-contrib-jshint":"~0.1.1","grunt-contrib-nodeunit":"^0.4.1","grunt-contrib-concat":"~0.1.3","grunt-contrib-uglify":"^0.5.1","grunt-browserify":"^2.1.4","grunt-contrib-yuidoc":"^0.5.2",browserify:"*"},dependencies:{}}},{}],2:[function(t,e){e.exports={version:t("../package.json").version,AABB:t("./collision/AABB"),ArrayCollisionMatrix:t("./collision/ArrayCollisionMatrix"),Body:t("./objects/Body"),Box:t("./shapes/Box"),Broadphase:t("./collision/Broadphase"),Constraint:t("./constraints/Constraint"),ContactEquation:t("./equations/ContactEquation"),Narrowphase:t("./world/Narrowphase"),ConeTwistConstraint:t("./constraints/ConeTwistConstraint"),ContactMaterial:t("./material/ContactMaterial"),ConvexPolyhedron:t("./shapes/ConvexPolyhedron"),Cylinder:t("./shapes/Cylinder"),DistanceConstraint:t("./constraints/DistanceConstraint"),Equation:t("./equations/Equation"),EventTarget:t("./utils/EventTarget"),FrictionEquation:t("./equations/FrictionEquation"),GSSolver:t("./solver/GSSolver"),GridBroadphase:t("./collision/GridBroadphase"),Heightfield:t("./shapes/Heightfield"),HingeConstraint:t("./constraints/HingeConstraint"),LockConstraint:t("./constraints/LockConstraint"),Mat3:t("./math/Mat3"),Material:t("./material/Material"),NaiveBroadphase:t("./collision/NaiveBroadphase"),ObjectCollisionMatrix:t("./collision/ObjectCollisionMatrix"),Pool:t("./utils/Pool"),Particle:t("./shapes/Particle"),Plane:t("./shapes/Plane"),PointToPointConstraint:t("./constraints/PointToPointConstraint"),Quaternion:t("./math/Quaternion"),Ray:t("./collision/Ray"),RaycastVehicle:t("./objects/RaycastVehicle"),RaycastResult:t("./collision/RaycastResult"),RigidVehicle:t("./objects/RigidVehicle"),RotationalEquation:t("./equations/RotationalEquation"),RotationalMotorEquation:t("./equations/RotationalMotorEquation"),SAPBroadphase:t("./collision/SAPBroadphase"),SPHSystem:t("./objects/SPHSystem"),Shape:t("./shapes/Shape"),Solver:t("./solver/Solver"),Sphere:t("./shapes/Sphere"),SplitSolver:t("./solver/SplitSolver"),Spring:t("./objects/Spring"),Transform:t("./math/Transform"),Trimesh:t("./shapes/Trimesh"),Vec3:t("./math/Vec3"),Vec3Pool:t("./utils/Vec3Pool"),World:t("./world/World"),Octree:t("./utils/Octree")}},{"../package.json":1,"./collision/AABB":3,"./collision/ArrayCollisionMatrix":4,"./collision/Broadphase":5,"./collision/GridBroadphase":6,"./collision/NaiveBroadphase":7,"./collision/ObjectCollisionMatrix":8,"./collision/Ray":10,"./collision/RaycastResult":11,"./collision/SAPBroadphase":12,"./constraints/ConeTwistConstraint":13,"./constraints/Constraint":14,"./constraints/DistanceConstraint":15,"./constraints/HingeConstraint":16,"./constraints/LockConstraint":17,"./constraints/PointToPointConstraint":18,"./equations/ContactEquation":20,"./equations/Equation":21,"./equations/FrictionEquation":22,"./equations/RotationalEquation":23,"./equations/RotationalMotorEquation":24,"./material/ContactMaterial":25,"./material/Material":26,"./math/Mat3":28,"./math/Quaternion":29,"./math/Transform":30,"./math/Vec3":31,"./objects/Body":32,"./objects/RaycastVehicle":33,"./objects/RigidVehicle":34,"./objects/SPHSystem":35,"./objects/Spring":36,"./shapes/Box":38,"./shapes/ConvexPolyhedron":39,"./shapes/Cylinder":40,"./shapes/Heightfield":41,"./shapes/Particle":42,"./shapes/Plane":43,"./shapes/Shape":44,"./shapes/Sphere":45,"./shapes/Trimesh":46,"./solver/GSSolver":47,"./solver/Solver":48,"./solver/SplitSolver":49,"./utils/EventTarget":50,"./utils/Octree":51,"./utils/Pool":52,"./utils/Vec3Pool":55,"./world/Narrowphase":56,"./world/World":57}],3:[function(t,e){var i=t("../math/Vec3");function o(t){t=t||{},this.lowerBound=new i,t.lowerBound&&this.lowerBound.copy(t.lowerBound),this.upperBound=new i,t.upperBound&&this.upperBound.copy(t.upperBound)}t("../utils/Utils"),e.exports=o;var n=new i;o.prototype.setFromPoints=function(t,e,i,o){var s=this.lowerBound,r=this.upperBound,a=i;s.copy(t[0]),a&&a.vmult(s,s),r.copy(s);for(var l=1;l<t.length;l++){var h=t[l];a&&(a.vmult(h,n),h=n),h.x>r.x&&(r.x=h.x),h.x<s.x&&(s.x=h.x),h.y>r.y&&(r.y=h.y),h.y<s.y&&(s.y=h.y),h.z>r.z&&(r.z=h.z),h.z<s.z&&(s.z=h.z)}return e&&(e.vadd(s,s),e.vadd(r,r)),o&&(s.x-=o,s.y-=o,s.z-=o,r.x+=o,r.y+=o,r.z+=o),this},o.prototype.copy=function(t){return this.lowerBound.copy(t.lowerBound),this.upperBound.copy(t.upperBound),this},o.prototype.clone=function(){return(new o).copy(this)},o.prototype.extend=function(t){this.lowerBound.x=Math.min(this.lowerBound.x,t.lowerBound.x),this.upperBound.x=Math.max(this.upperBound.x,t.upperBound.x),this.lowerBound.y=Math.min(this.lowerBound.y,t.lowerBound.y),this.upperBound.y=Math.max(this.upperBound.y,t.upperBound.y),this.lowerBound.z=Math.min(this.lowerBound.z,t.lowerBound.z),this.upperBound.z=Math.max(this.upperBound.z,t.upperBound.z)},o.prototype.overlaps=function(t){var e=this.lowerBound,i=this.upperBound,o=t.lowerBound,n=t.upperBound,s=o.x<=i.x&&i.x<=n.x||e.x<=n.x&&n.x<=i.x,r=o.y<=i.y&&i.y<=n.y||e.y<=n.y&&n.y<=i.y,a=o.z<=i.z&&i.z<=n.z||e.z<=n.z&&n.z<=i.z;return s&&r&&a},o.prototype.volume=function(){var t=this.lowerBound,e=this.upperBound;return(e.x-t.x)*(e.y-t.y)*(e.z-t.z)},o.prototype.contains=function(t){var e=this.lowerBound,i=this.upperBound,o=t.lowerBound,n=t.upperBound;return e.x<=o.x&&i.x>=n.x&&e.y<=o.y&&i.y>=n.y&&e.z<=o.z&&i.z>=n.z},o.prototype.getCorners=function(t,e,i,o,n,s,r,a){var l=this.lowerBound,h=this.upperBound;t.copy(l),e.set(h.x,l.y,l.z),i.set(h.x,h.y,l.z),o.set(l.x,h.y,h.z),n.set(h.x,l.y,h.z),s.set(l.x,h.y,l.z),r.set(l.x,l.y,h.z),a.copy(h)};var s=[new i,new i,new i,new i,new i,new i,new i,new i];o.prototype.toLocalFrame=function(t,e){var i=s,o=i[0],n=i[1],r=i[2],a=i[3],l=i[4],h=i[5],p=i[6],c=i[7];this.getCorners(o,n,r,a,l,h,p,c);for(var u=0;8!==u;u++){var d=i[u];t.pointToLocal(d,d)}return e.setFromPoints(i)},o.prototype.toWorldFrame=function(t,e){var i=s,o=i[0],n=i[1],r=i[2],a=i[3],l=i[4],h=i[5],p=i[6],c=i[7];this.getCorners(o,n,r,a,l,h,p,c);for(var u=0;8!==u;u++){var d=i[u];t.pointToWorld(d,d)}return e.setFromPoints(i)},o.prototype.overlapsRay=function(t){var e=1/t._direction.x,i=1/t._direction.y,o=1/t._direction.z,n=(this.lowerBound.x-t.from.x)*e,s=(this.upperBound.x-t.from.x)*e,r=(this.lowerBound.y-t.from.y)*i,a=(this.upperBound.y-t.from.y)*i,l=(this.lowerBound.z-t.from.z)*o,h=(this.upperBound.z-t.from.z)*o,p=Math.max(Math.max(Math.min(n,s),Math.min(r,a)),Math.min(l,h)),c=Math.min(Math.min(Math.max(n,s),Math.max(r,a)),Math.max(l,h));return!(c<0||p>c)}},{"../math/Vec3":31,"../utils/Utils":54}],4:[function(t,e){function i(){this.matrix=[]}e.exports=i,i.prototype.get=function(t,e){if(t=t.index,(e=e.index)>t){var i=e;e=t,t=i}return this.matrix[(t*(t+1)>>1)+e-1]},i.prototype.set=function(t,e,i){if(t=t.index,(e=e.index)>t){var o=e;e=t,t=o}this.matrix[(t*(t+1)>>1)+e-1]=i?1:0},i.prototype.reset=function(){for(var t=0,e=this.matrix.length;t!==e;t++)this.matrix[t]=0},i.prototype.setNumObjects=function(t){this.matrix.length=t*(t-1)>>1}},{}],5:[function(t,e){var i=t("../objects/Body"),o=t("../math/Vec3"),n=t("../math/Quaternion");function s(){this.world=null,this.useBoundingBoxes=!1,this.dirty=!0}t("../shapes/Shape"),t("../shapes/Plane"),e.exports=s,s.prototype.collisionPairs=function(){throw new Error("collisionPairs not implemented for this BroadPhase class!")},s.prototype.needBroadphaseCollision=function(t,e){if(!(t.collisionFilterGroup&e.collisionFilterMask&&e.collisionFilterGroup&t.collisionFilterMask))return!1;var o=!!(t.type&i.STATIC),n=!!(e.type&i.STATIC);return!(o&&n||!t.hasTrigger&&!e.hasTrigger&&t.sleepState===i.SLEEPING&&e.sleepState===i.SLEEPING)},s.prototype.intersectionTest=function(t,e,i,o){this.useBoundingBoxes?this.doBoundingBoxBroadphase(t,e,i,o):this.doBoundingSphereBroadphase(t,e,i,o)};var r=new o;new o,new n,new o,s.prototype.doBoundingSphereBroadphase=function(t,e,i,o){var n=r;e.position.vsub(t.position,n);var s=Math.pow(t.boundingRadius+e.boundingRadius,2);n.norm2()<s&&(i.push(t),o.push(e))},s.prototype.doBoundingBoxBroadphase=function(t,e,i,o){t.aabbNeedsUpdate&&t.computeAABB(),e.aabbNeedsUpdate&&e.computeAABB(),t.aabb.overlaps(e.aabb)&&(i.push(t),o.push(e))};var a={keys:[]},l=[],h=[];s.prototype.makePairsUnique=function(t,e){for(var i=a,o=l,n=h,s=t.length,r=0;r!==s;r++)o[r]=t[r],n[r]=e[r];for(t.length=0,e.length=0,r=0;r!==s;r++){var p=o[r].id,c=n[r].id;i[u=p<c?p+","+c:c+","+p]=r,i.keys.push(u)}for(r=0;r!==i.keys.length;r++){var u=i.keys.pop(),d=i[u];t.push(o[d]),e.push(n[d]),delete i[u]}},s.prototype.setWorld=function(){};var p=new o;s.boundingSphereCheck=function(t,e){var i=p;return t.position.vsub(e.position,i),Math.pow(t.shape.boundingSphereRadius+e.shape.boundingSphereRadius,2)>i.norm2()},s.prototype.aabbQuery=function(){return console.warn(".aabbQuery is not implemented in this Broadphase subclass."),[]}},{"../math/Quaternion":29,"../math/Vec3":31,"../objects/Body":32,"../shapes/Plane":43,"../shapes/Shape":44}],6:[function(t,e){e.exports=s;var i=t("./Broadphase"),o=t("../math/Vec3"),n=t("../shapes/Shape");function s(t,e,n,s,r){i.apply(this),this.nx=n||10,this.ny=s||10,this.nz=r||10,this.aabbMin=t||new o(100,100,100),this.aabbMax=e||new o(-100,-100,-100);var a=this.nx*this.ny*this.nz;if(a<=0)throw"GridBroadphase: Each dimension's n must be >0";this.bins=[],this.binLengths=[],this.bins.length=a,this.binLengths.length=a;for(var l=0;l<a;l++)this.bins[l]=[],this.binLengths[l]=0}s.prototype=new i,s.prototype.constructor=s;var r=new o;new o,s.prototype.collisionPairs=function(t,e,i){var o=t.numObjects(),s=t.bodies,a=this.aabbMax,l=this.aabbMin,h=this.nx,p=this.ny,c=this.nz,u=p*c,d=c,y=a.x,v=a.y,f=a.z,m=l.x,w=l.y,g=l.z,b=h/(y-m),x=p/(v-w),B=c/(f-g),S=(y-m)/h,A=(v-w)/p,C=(f-g)/c,E=.5*Math.sqrt(S*S+A*A+C*C),M=n.types,_=M.SPHERE,z=M.PLANE;M.BOX,M.COMPOUND,M.CONVEXPOLYHEDRON;for(var F=this.bins,R=this.binLengths,T=this.bins.length,P=0;P!==T;P++)R[P]=0;var q=Math.ceil;function I(t,e,i,o,n,s,r){var a=(t-m)*b|0,l=(e-w)*x|0,y=(i-g)*B|0,v=q((o-m)*b),f=q((n-w)*x),S=q((s-g)*B);a<0?a=0:a>=h&&(a=h-1),l<0?l=0:l>=p&&(l=p-1),y<0?y=0:y>=c&&(y=c-1),v<0?v=0:v>=h&&(v=h-1),f<0?f=0:f>=p&&(f=p-1),S<0?S=0:S>=c&&(S=c-1),l*=d,y*=1,v*=u,f*=d,S*=1;for(var A=a*=u;A<=v;A+=u)for(var C=l;C<=f;C+=d)for(var E=y;E<=S;E+=1){var M=A+C+E;F[M][R[M]++]=r}}for(l=Math.min,a=Math.max,P=0;P!==o;P++){var V=(et=s[P]).shape;switch(V.type){case _:var L=et.position.x,N=et.position.y,W=et.position.z,j=V.radius;I(L-j,N-j,W-j,L+j,N+j,W+j,et);break;case z:V.worldNormalNeedsUpdate&&V.computeWorldNormal(et.quaternion);var k=V.worldNormal,O=m+.5*S-et.position.x,D=w+.5*A-et.position.y,U=g+.5*C-et.position.z,G=r;G.set(O,D,U);for(var H=0,Q=0;H!==h;H++,Q+=u,G.y=D,G.x+=S)for(var X=0,Y=0;X!==p;X++,Y+=d,G.z=U,G.y+=A)for(var K=0,Z=0;K!==c;K++,Z+=1,G.z+=C)if(G.dot(k)<E){var J=Q+Y+Z;F[J][R[J]++]=et}break;default:et.aabbNeedsUpdate&&et.computeAABB(),I(et.aabb.lowerBound.x,et.aabb.lowerBound.y,et.aabb.lowerBound.z,et.aabb.upperBound.x,et.aabb.upperBound.y,et.aabb.upperBound.z,et)}}for(P=0;P!==T;P++){var $=R[P];if($>1){var tt=F[P];for(H=0;H!==$;H++){var et=tt[H];for(X=0;X!==H;X++){var it=tt[X];this.needBroadphaseCollision(et,it)&&this.intersectionTest(et,it,e,i)}}}}this.makePairsUnique(e,i)}},{"../math/Vec3":31,"../shapes/Shape":44,"./Broadphase":5}],7:[function(t,e){e.exports=n;var i=t("./Broadphase"),o=t("./AABB");function n(){i.apply(this)}n.prototype=new i,n.prototype.constructor=n,n.prototype.collisionPairs=function(t,e,i){var o,n,s,r,a=t.bodies,l=a.length;for(o=0;o!==l;o++)for(n=0;n!==o;n++)s=a[o],r=a[n],this.needBroadphaseCollision(s,r)&&this.intersectionTest(s,r,e,i)},new o,n.prototype.aabbQuery=function(t,e,i){i=i||[];for(var o=0;o<t.bodies.length;o++){var n=t.bodies[o];n.aabbNeedsUpdate&&n.computeAABB(),n.aabb.overlaps(e)&&i.push(n)}return i}},{"./AABB":3,"./Broadphase":5}],8:[function(t,e){function i(){this.matrix={}}e.exports=i,i.prototype.get=function(t,e){if(t=t.id,(e=e.id)>t){var i=e;e=t,t=i}return t+"-"+e in this.matrix},i.prototype.set=function(t,e,i){if(t=t.id,(e=e.id)>t){var o=e;e=t,t=o}i?this.matrix[t+"-"+e]=!0:delete this.matrix[t+"-"+e]},i.prototype.reset=function(){this.matrix={}},i.prototype.setNumObjects=function(){}},{}],9:[function(t,e){function i(){this.current=[],this.previous=[]}function o(t,e){t.push((4294901760&e)>>16,65535&e)}e.exports=i,i.prototype.getKey=function(t,e){if(e<t){var i=e;e=t,t=i}return t<<16|e},i.prototype.set=function(t,e){for(var i=this.getKey(t,e),o=this.current,n=0;i>o[n];)n++;if(i!==o[n]){for(e=o.length-1;e>=n;e--)o[e+1]=o[e];o[n]=i}},i.prototype.tick=function(){var t=this.current;this.current=this.previous,this.previous=t,this.current.length=0},i.prototype.getDiff=function(t,e){for(var i=this.current,n=this.previous,s=i.length,r=n.length,a=0,l=0;l<s;l++){for(var h=i[l];h>n[a];)a++;h===n[a]||o(t,h)}for(a=0,l=0;l<r;l++){for(var p=n[l];p>i[a];)a++;i[a]===p||o(e,p)}}},{}],10:[function(t,e){e.exports=l;var i=t("../math/Vec3"),o=t("../math/Quaternion"),n=t("../math/Transform");t("../shapes/ConvexPolyhedron"),t("../shapes/Box");var s=t("../collision/RaycastResult"),r=t("../shapes/Shape"),a=t("../collision/AABB");function l(t,e){this.from=t?t.clone():new i,this.to=e?e.clone():new i,this._direction=new i,this.precision=1e-4,this.checkCollisionResponse=!0,this.skipBackfaces=!1,this.collisionFilterMask=-1,this.collisionFilterGroup=-1,this.mode=l.ANY,this.result=new s,this.hasHit=!1,this.callback=function(){}}l.prototype.constructor=l,l.CLOSEST=1,l.ANY=2,l.ALL=4;var h=new a,p=[];l.prototype.intersectWorld=function(t,e){return this.mode=e.mode||l.ANY,this.result=e.result||new s,this.skipBackfaces=!!e.skipBackfaces,this.checkCollisionResponse=!!e.checkCollisionResponse,this.collisionFilterMask=void 0!==e.collisionFilterMask?e.collisionFilterMask:-1,this.collisionFilterGroup=void 0!==e.collisionFilterGroup?e.collisionFilterGroup:-1,e.from&&this.from.copy(e.from),e.to&&this.to.copy(e.to),this.callback=e.callback||function(){},this.hasHit=!1,this.result.reset(),this._updateDirection(),this.getAABB(h),p.length=0,t.broadphase.aabbQuery(t,h,p),this.intersectBodies(p),this.hasHit};var c=new i,u=new i;function d(t,e,i,o){o.vsub(e,V),i.vsub(e,c),t.vsub(e,u);var n,s,r=V.dot(V),a=V.dot(c),l=V.dot(u),h=c.dot(c),p=c.dot(u);return(n=h*l-a*p)>=0&&(s=r*p-a*l)>=0&&n+s<r*h-a*a}l.pointInTriangle=d;var y=new i,v=new o;l.prototype.intersectBody=function(t,e){if(e&&(this.result=e,this._updateDirection()),(!this.checkCollisionResponse||t.collisionResponse)&&l.perBodyFilter(this,t))for(var i=y,o=v,n=0,s=t.shapes.length;n<s;n++){var r=t.shapes[n];if(l.perShapeFilter(this,r)&&(t.quaternion.mult(t.shapeOrientations[n],o),t.quaternion.vmult(t.shapeOffsets[n],i),i.vadd(t.position,i),this.intersectShape(r,o,i,t),this.result._shouldStop))break}},l.prototype.intersectBodies=function(t,e){e&&(this.result=e,this._updateDirection());for(var i=0,o=t.length;!this.result._shouldStop&&i<o;i++)this.intersectBody(t[i])},l.prototype._updateDirection=function(){this.to.vsub(this.from,this._direction),this._direction.normalize()},l.prototype.intersectShape=function(t,e,i,o){if(!(N(this.from,this._direction,i)>t.boundingSphereRadius)){var n=this[t.type];n&&n.call(this,t,e,i,o,t)}},new i,new i;var f=new i,m=new i,w=new i,g=new i;new i,new s,l.prototype.intersectBox=function(t,e,i,o,n){return this.intersectConvex(t.convexPolyhedronRepresentation,e,i,o,n)},l.prototype[r.types.BOX]=l.prototype.intersectBox,l.prototype.intersectPlane=function(t,e,o,n,s){var r=this.from,a=this.to,l=this._direction,h=new i(0,0,1);e.vmult(h,h);var p=new i;r.vsub(o,p);var c=p.dot(h);if(a.vsub(o,p),!(c*p.dot(h)>0||r.distanceTo(a)<c)){var u=h.dot(l);if(!(Math.abs(u)<this.precision)){var d=new i,y=new i,v=new i;r.vsub(o,d);var f=-h.dot(d)/u;l.scale(f,y),r.vadd(y,v),this.reportIntersection(h,v,s,n,-1)}}},l.prototype[r.types.PLANE]=l.prototype.intersectPlane,l.prototype.getAABB=function(t){var e=this.to,i=this.from;t.lowerBound.x=Math.min(e.x,i.x),t.lowerBound.y=Math.min(e.y,i.y),t.lowerBound.z=Math.min(e.z,i.z),t.upperBound.x=Math.max(e.x,i.x),t.upperBound.y=Math.max(e.y,i.y),t.upperBound.z=Math.max(e.z,i.z)};var b={faceList:[0]},x=new i,B=new l,S=[];l.prototype.intersectHeightfield=function(t,e,i,o,s){t.data,t.elementSize;var r=B;r.from.copy(this.from),r.to.copy(this.to),n.pointToLocalFrame(i,e,r.from,r.from),n.pointToLocalFrame(i,e,r.to,r.to),r._updateDirection();var l,h,p,c,u=S;l=h=0,p=c=t.data.length-1;var d=new a;r.getAABB(d),t.getIndexOfPosition(d.lowerBound.x,d.lowerBound.y,u,!0),l=Math.max(l,u[0]),h=Math.max(h,u[1]),t.getIndexOfPosition(d.upperBound.x,d.upperBound.y,u,!0),p=Math.min(p,u[0]+1),c=Math.min(c,u[1]+1);for(var y=l;y<p;y++)for(var v=h;v<c;v++){if(this.result._shouldStop)return;if(t.getAabbAtIndex(y,v,d),d.overlapsRay(r)){if(t.getConvexTrianglePillar(y,v,!1),n.pointToWorldFrame(i,e,t.pillarOffset,x),this.intersectConvex(t.pillarConvex,e,x,o,s,b),this.result._shouldStop)return;t.getConvexTrianglePillar(y,v,!0),n.pointToWorldFrame(i,e,t.pillarOffset,x),this.intersectConvex(t.pillarConvex,e,x,o,s,b)}}},l.prototype[r.types.HEIGHTFIELD]=l.prototype.intersectHeightfield;var A=new i,C=new i;l.prototype.intersectSphere=function(t,e,i,o,n){var s=this.from,r=this.to,a=t.radius,l=Math.pow(r.x-s.x,2)+Math.pow(r.y-s.y,2)+Math.pow(r.z-s.z,2),h=2*((r.x-s.x)*(s.x-i.x)+(r.y-s.y)*(s.y-i.y)+(r.z-s.z)*(s.z-i.z)),p=Math.pow(s.x-i.x,2)+Math.pow(s.y-i.y,2)+Math.pow(s.z-i.z,2)-Math.pow(a,2),c=Math.pow(h,2)-4*l*p,u=A,d=C;if(!(c<0))if(0===c)s.lerp(r,c,u),u.vsub(i,d),d.normalize(),this.reportIntersection(d,u,n,o,-1);else{var y=(-h-Math.sqrt(c))/(2*l),v=(-h+Math.sqrt(c))/(2*l);if(y>=0&&y<=1&&(s.lerp(r,y,u),u.vsub(i,d),d.normalize(),this.reportIntersection(d,u,n,o,-1)),this.result._shouldStop)return;v>=0&&v<=1&&(s.lerp(r,v,u),u.vsub(i,d),d.normalize(),this.reportIntersection(d,u,n,o,-1))}},l.prototype[r.types.SPHERE]=l.prototype.intersectSphere;var E=new i;new i,new i;var M=new i;l.prototype.intersectConvex=function(t,e,i,o,n,s){for(var r=E,a=M,l=s&&s.faceList||null,h=t.faces,p=t.vertices,c=t.faceNormals,u=this._direction,y=this.from,v=this.to,b=y.distanceTo(v),x=l?l.length:h.length,B=this.result,S=0;!B._shouldStop&&S<x;S++){var A=l?l[S]:S,C=h[A],_=c[A],z=e,F=i;a.copy(p[C[0]]),z.vmult(a,a),a.vadd(F,a),a.vsub(y,a),z.vmult(_,r);var R=u.dot(r);if(!(Math.abs(R)<this.precision)){var T=r.dot(a)/R;if(!(T<0)){u.mult(T,f),f.vadd(y,f),m.copy(p[C[0]]),z.vmult(m,m),F.vadd(m,m);for(var P=1;!B._shouldStop&&P<C.length-1;P++){w.copy(p[C[P]]),g.copy(p[C[P+1]]),z.vmult(w,w),z.vmult(g,g),F.vadd(w,w),F.vadd(g,g);var q=f.distanceTo(y);!d(f,m,w,g)&&!d(f,w,m,g)||q>b||this.reportIntersection(r,f,n,o,A)}}}}},l.prototype[r.types.CONVEXPOLYHEDRON]=l.prototype.intersectConvex;var _=new i,z=new i,F=new i,R=new i,T=new i,P=new i;new a;var q=[],I=new n;l.prototype.intersectTrimesh=function(t,e,i,o,s,r){var a=_,l=q,h=I,p=M,c=z,u=F,y=R,v=P,b=T;r&&r.faceList;var x=t.indices;t.vertices,t.faceNormals;var B=this.from,S=this.to,A=this._direction;h.position.copy(i),h.quaternion.copy(e),n.vectorToLocalFrame(i,e,A,c),n.pointToLocalFrame(i,e,B,u),n.pointToLocalFrame(i,e,S,y),y.x*=t.scale.x,y.y*=t.scale.y,y.z*=t.scale.z,u.x*=t.scale.x,u.y*=t.scale.y,u.z*=t.scale.z,y.vsub(u,c),c.normalize();var C=u.distanceSquared(y);t.tree.rayQuery(this,h,l);for(var E=0,V=l.length;!this.result._shouldStop&&E!==V;E++){var L=l[E];t.getNormal(L,a),t.getVertex(x[3*L],m),m.vsub(u,p);var N=c.dot(a),W=a.dot(p)/N;if(!(W<0)){c.scale(W,f),f.vadd(u,f),t.getVertex(x[3*L+1],w),t.getVertex(x[3*L+2],g);var j=f.distanceSquared(u);!d(f,w,m,g)&&!d(f,m,w,g)||j>C||(n.vectorToWorldFrame(e,a,b),n.pointToWorldFrame(i,e,f,v),this.reportIntersection(b,v,s,o,L))}}l.length=0},l.prototype[r.types.TRIMESH]=l.prototype.intersectTrimesh,l.prototype.reportIntersection=function(t,e,i,o,n){var s=this.from,r=this.to,a=s.distanceTo(e),h=this.result;if(!(this.skipBackfaces&&t.dot(this._direction)>0))switch(h.hitFaceIndex=void 0!==n?n:-1,this.mode){case l.ALL:this.hasHit=!0,h.set(s,r,t,e,i,o,a),h.hasHit=!0,this.callback(h);break;case l.CLOSEST:(a<h.distance||!h.hasHit)&&(this.hasHit=!0,h.hasHit=!0,h.set(s,r,t,e,i,o,a));break;case l.ANY:this.hasHit=!0,h.hasHit=!0,h.set(s,r,t,e,i,o,a),h._shouldStop=!0}};var V=new i,L=new i;function N(t,e,i){i.vsub(t,V);var o=V.dot(e);return e.mult(o,L),L.vadd(t,L),i.distanceTo(L)}l.perBodyFilter=function(t,e){return!!(t.collisionFilterGroup&e.collisionFilterMask&&e.collisionFilterGroup&t.collisionFilterMask)},l.perShapeFilter=function(t,e){return!(t.checkCollisionResponse&&!e.collisionResponse)}},{"../collision/AABB":3,"../collision/RaycastResult":11,"../math/Quaternion":29,"../math/Transform":30,"../math/Vec3":31,"../shapes/Box":38,"../shapes/ConvexPolyhedron":39,"../shapes/Shape":44}],11:[function(t,e){var i=t("../math/Vec3");function o(){this.rayFromWorld=new i,this.rayToWorld=new i,this.hitNormalWorld=new i,this.hitPointWorld=new i,this.hasHit=!1,this.shape=null,this.body=null,this.hitFaceIndex=-1,this.distance=-1,this._shouldStop=!1}e.exports=o,o.prototype.reset=function(){this.rayFromWorld.setZero(),this.rayToWorld.setZero(),this.hitNormalWorld.setZero(),this.hitPointWorld.setZero(),this.hasHit=!1,this.shape=null,this.body=null,this.hitFaceIndex=-1,this.distance=-1,this._shouldStop=!1},o.prototype.abort=function(){this._shouldStop=!0},o.prototype.set=function(t,e,i,o,n,s,r){this.rayFromWorld.copy(t),this.rayToWorld.copy(e),this.hitNormalWorld.copy(i),this.hitPointWorld.copy(o),this.shape=n,this.body=s,this.distance=r}},{"../math/Vec3":31}],12:[function(t,e){t("../shapes/Shape");var i=t("../collision/Broadphase");function o(t){i.apply(this),this.axisList=[],this.world=null,this.axisIndex=0;var e=this.axisList;this._addBodyHandler=function(t){e.push(t.body)},this._removeBodyHandler=function(t){var i=e.indexOf(t.body);-1!==i&&e.splice(i,1)},t&&this.setWorld(t)}e.exports=o,o.prototype=new i,o.prototype.setWorld=function(t){this.axisList.length=0;for(var e=0;e<t.bodies.length;e++)this.axisList.push(t.bodies[e]);t.removeEventListener("addBody",this._addBodyHandler),t.removeEventListener("removeBody",this._removeBodyHandler),t.addEventListener("addBody",this._addBodyHandler),t.addEventListener("removeBody",this._removeBodyHandler),this.world=t,this.dirty=!0},o.insertionSortX=function(t){for(var e=1,i=t.length;e<i;e++){for(var o=t[e],n=e-1;n>=0&&!(t[n].aabb.lowerBound.x<=o.aabb.lowerBound.x);n--)t[n+1]=t[n];t[n+1]=o}return t},o.insertionSortY=function(t){for(var e=1,i=t.length;e<i;e++){for(var o=t[e],n=e-1;n>=0&&!(t[n].aabb.lowerBound.y<=o.aabb.lowerBound.y);n--)t[n+1]=t[n];t[n+1]=o}return t},o.insertionSortZ=function(t){for(var e=1,i=t.length;e<i;e++){for(var o=t[e],n=e-1;n>=0&&!(t[n].aabb.lowerBound.z<=o.aabb.lowerBound.z);n--)t[n+1]=t[n];t[n+1]=o}return t},o.prototype.collisionPairs=function(t,e,i){var n,s,r=this.axisList,a=r.length,l=this.axisIndex;for(this.dirty&&(this.sortList(),this.dirty=!1),n=0;n!==a;n++){var h=r[n];for(s=n+1;s<a;s++){var p=r[s];if(this.needBroadphaseCollision(h,p)){if(!o.checkBounds(h,p,l))break;this.intersectionTest(h,p,e,i)}}}},o.prototype.sortList=function(){for(var t=this.axisList,e=this.axisIndex,i=t.length,n=0;n!==i;n++){var s=t[n];s.aabbNeedsUpdate&&s.computeAABB()}0===e?o.insertionSortX(t):1===e?o.insertionSortY(t):2===e&&o.insertionSortZ(t)},o.checkBounds=function(t,e,i){var o,n;0===i?(o=t.position.x,n=e.position.x):1===i?(o=t.position.y,n=e.position.y):2===i&&(o=t.position.z,n=e.position.z);var s=t.boundingRadius;return n-e.boundingRadius<o+s},o.prototype.autoDetectAxis=function(){for(var t=0,e=0,i=0,o=0,n=0,s=0,r=this.axisList,a=r.length,l=1/a,h=0;h!==a;h++){var p=r[h],c=p.position.x;t+=c,e+=c*c;var u=p.position.y;i+=u,o+=u*u;var d=p.position.z;n+=d,s+=d*d}var y=e-t*t*l,v=o-i*i*l,f=s-n*n*l;this.axisIndex=y>v?y>f?0:2:v>f?1:2},o.prototype.aabbQuery=function(t,e,i){i=i||[],this.dirty&&(this.sortList(),this.dirty=!1);var o=this.axisIndex,n="x";1===o&&(n="y"),2===o&&(n="z");var s=this.axisList;e.lowerBound[n],e.upperBound[n];for(var r=0;r<s.length;r++){var a=s[r];a.aabbNeedsUpdate&&a.computeAABB(),a.aabb.overlaps(e)&&i.push(a)}return i}},{"../collision/Broadphase":5,"../shapes/Shape":44}],13:[function(t,e){e.exports=r,t("./Constraint");var i=t("./PointToPointConstraint"),o=t("../equations/ConeEquation"),n=t("../equations/RotationalEquation");t("../equations/ContactEquation");var s=t("../math/Vec3");function r(t,e,r){var a=void 0!==(r=r||{}).maxForce?r.maxForce:1e6,l=r.pivotA?r.pivotA.clone():new s,h=r.pivotB?r.pivotB.clone():new s;this.axisA=r.axisA?r.axisA.clone():new s,this.axisB=r.axisB?r.axisB.clone():new s,i.call(this,t,l,e,h,a),this.collideConnected=!!r.collideConnected,this.angle=void 0!==r.angle?r.angle:0;var p=this.coneEquation=new o(t,e,r),c=this.twistEquation=new n(t,e,r);this.twistAngle=void 0!==r.twistAngle?r.twistAngle:0,p.maxForce=0,p.minForce=-a,c.maxForce=0,c.minForce=-a,this.equations.push(p,c)}r.prototype=new i,r.constructor=r,new s,new s,r.prototype.update=function(){var t=this.bodyA,e=this.bodyB,o=this.coneEquation,n=this.twistEquation;i.prototype.update.call(this),t.vectorToWorldFrame(this.axisA,o.axisA),e.vectorToWorldFrame(this.axisB,o.axisB),this.axisA.tangents(n.axisA,n.axisA),t.vectorToWorldFrame(n.axisA,n.axisA),this.axisB.tangents(n.axisB,n.axisB),e.vectorToWorldFrame(n.axisB,n.axisB),o.angle=this.angle,n.maxAngle=this.twistAngle}},{"../equations/ConeEquation":19,"../equations/ContactEquation":20,"../equations/RotationalEquation":23,"../math/Vec3":31,"./Constraint":14,"./PointToPointConstraint":18}],14:[function(t,e){e.exports=o;var i=t("../utils/Utils");function o(t,e,n){n=i.defaults(n,{collideConnected:!0,wakeUpBodies:!0}),this.equations=[],this.bodyA=t,this.bodyB=e,this.id=o.idCounter++,this.collideConnected=n.collideConnected,n.wakeUpBodies&&(t&&t.wakeUp(),e&&e.wakeUp())}o.prototype.update=function(){throw new Error("method update() not implmemented in this Constraint subclass!")},o.prototype.enable=function(){for(var t=this.equations,e=0;e<t.length;e++)t[e].enabled=!0},o.prototype.disable=function(){for(var t=this.equations,e=0;e<t.length;e++)t[e].enabled=!1},o.idCounter=0},{"../utils/Utils":54}],15:[function(t,e){e.exports=n;var i=t("./Constraint"),o=t("../equations/ContactEquation");function n(t,e,n,s){i.call(this,t,e),void 0===n&&(n=t.position.distanceTo(e.position)),void 0===s&&(s=1e6),this.distance=n;var r=this.distanceEquation=new o(t,e);this.equations.push(r),r.minForce=-s,r.maxForce=s}n.prototype=new i,n.prototype.update=function(){var t=this.bodyA,e=this.bodyB,i=this.distanceEquation,o=.5*this.distance,n=i.ni;e.position.vsub(t.position,n),n.normalize(),n.mult(o,i.ri),n.mult(-o,i.rj)}},{"../equations/ContactEquation":20,"./Constraint":14}],16:[function(t,e){e.exports=r,t("./Constraint");var i=t("./PointToPointConstraint"),o=t("../equations/RotationalEquation"),n=t("../equations/RotationalMotorEquation");t("../equations/ContactEquation");var s=t("../math/Vec3");function r(t,e,r){var a=void 0!==(r=r||{}).maxForce?r.maxForce:1e6,l=r.pivotA?r.pivotA.clone():new s,h=r.pivotB?r.pivotB.clone():new s;i.call(this,t,l,e,h,a),(this.axisA=r.axisA?r.axisA.clone():new s(1,0,0)).normalize(),(this.axisB=r.axisB?r.axisB.clone():new s(1,0,0)).normalize();var p=this.rotationalEquation1=new o(t,e,r),c=this.rotationalEquation2=new o(t,e,r),u=this.motorEquation=new n(t,e,a);u.enabled=!1,this.equations.push(p,c,u)}r.prototype=new i,r.constructor=r,r.prototype.enableMotor=function(){this.motorEquation.enabled=!0},r.prototype.disableMotor=function(){this.motorEquation.enabled=!1},r.prototype.setMotorSpeed=function(t){this.motorEquation.targetVelocity=t},r.prototype.setMotorMaxForce=function(t){this.motorEquation.maxForce=t,this.motorEquation.minForce=-t};var a=new s,l=new s;r.prototype.update=function(){var t=this.bodyA,e=this.bodyB,o=this.motorEquation,n=this.rotationalEquation1,s=this.rotationalEquation2,r=a,h=l,p=this.axisA,c=this.axisB;i.prototype.update.call(this),t.quaternion.vmult(p,r),e.quaternion.vmult(c,h),r.tangents(n.axisA,s.axisA),n.axisB.copy(h),s.axisB.copy(h),this.motorEquation.enabled&&(t.quaternion.vmult(this.axisA,o.axisA),e.quaternion.vmult(this.axisB,o.axisB))}},{"../equations/ContactEquation":20,"../equations/RotationalEquation":23,"../equations/RotationalMotorEquation":24,"../math/Vec3":31,"./Constraint":14,"./PointToPointConstraint":18}],17:[function(t,e){e.exports=s,t("./Constraint");var i=t("./PointToPointConstraint"),o=t("../equations/RotationalEquation");t("../equations/RotationalMotorEquation"),t("../equations/ContactEquation");var n=t("../math/Vec3");function s(t,e,s){var r=void 0!==(s=s||{}).maxForce?s.maxForce:1e6,a=new n,l=new n,h=new n;t.position.vadd(e.position,h),h.scale(.5,h),e.pointToLocalFrame(h,l),t.pointToLocalFrame(h,a),i.call(this,t,a,e,l,r),this.xA=t.vectorToLocalFrame(n.UNIT_X),this.xB=e.vectorToLocalFrame(n.UNIT_X),this.yA=t.vectorToLocalFrame(n.UNIT_Y),this.yB=e.vectorToLocalFrame(n.UNIT_Y),this.zA=t.vectorToLocalFrame(n.UNIT_Z),this.zB=e.vectorToLocalFrame(n.UNIT_Z);var p=this.rotationalEquation1=new o(t,e,s),c=this.rotationalEquation2=new o(t,e,s),u=this.rotationalEquation3=new o(t,e,s);this.equations.push(p,c,u)}s.prototype=new i,s.constructor=s,new n,new n,s.prototype.update=function(){var t=this.bodyA,e=this.bodyB;this.motorEquation;var o=this.rotationalEquation1,n=this.rotationalEquation2,s=this.rotationalEquation3;i.prototype.update.call(this),t.vectorToWorldFrame(this.xA,o.axisA),e.vectorToWorldFrame(this.yB,o.axisB),t.vectorToWorldFrame(this.yA,n.axisA),e.vectorToWorldFrame(this.zB,n.axisB),t.vectorToWorldFrame(this.zA,s.axisA),e.vectorToWorldFrame(this.xB,s.axisB)}},{"../equations/ContactEquation":20,"../equations/RotationalEquation":23,"../equations/RotationalMotorEquation":24,"../math/Vec3":31,"./Constraint":14,"./PointToPointConstraint":18}],18:[function(t,e){e.exports=s;var i=t("./Constraint"),o=t("../equations/ContactEquation"),n=t("../math/Vec3");function s(t,e,s,r,a){i.call(this,t,s),a=void 0!==a?a:1e6,this.pivotA=e?e.clone():new n,this.pivotB=r?r.clone():new n;var l=this.equationX=new o(t,s),h=this.equationY=new o(t,s),p=this.equationZ=new o(t,s);this.equations.push(l,h,p),l.minForce=h.minForce=p.minForce=-a,l.maxForce=h.maxForce=p.maxForce=a,l.ni.set(1,0,0),h.ni.set(0,1,0),p.ni.set(0,0,1)}s.prototype=new i,s.prototype.update=function(){var t=this.bodyA,e=this.bodyB,i=this.equationX,o=this.equationY,n=this.equationZ;t.quaternion.vmult(this.pivotA,i.ri),e.quaternion.vmult(this.pivotB,i.rj),o.ri.copy(i.ri),o.rj.copy(i.rj),n.ri.copy(i.ri),n.rj.copy(i.rj)}},{"../equations/ContactEquation":20,"../math/Vec3":31,"./Constraint":14}],19:[function(t,e){e.exports=n;var i=t("../math/Vec3");t("../math/Mat3");var o=t("./Equation");function n(t,e,n){var s=void 0!==(n=n||{}).maxForce?n.maxForce:1e6;o.call(this,t,e,-s,s),this.axisA=n.axisA?n.axisA.clone():new i(1,0,0),this.axisB=n.axisB?n.axisB.clone():new i(0,1,0),this.angle=void 0!==n.angle?n.angle:0}n.prototype=new o,n.prototype.constructor=n;var s=new i,r=new i;n.prototype.computeB=function(t){var e=this.a,i=this.b,o=this.axisA,n=this.axisB,a=s,l=r,h=this.jacobianElementA,p=this.jacobianElementB;return o.cross(n,a),n.cross(o,l),h.rotational.copy(l),p.rotational.copy(a),-(Math.cos(this.angle)-o.dot(n))*e-this.computeGW()*i-t*this.computeGiMf()}},{"../math/Mat3":28,"../math/Vec3":31,"./Equation":21}],20:[function(t,e){e.exports=n;var i=t("./Equation"),o=t("../math/Vec3");function n(t,e,n){n=void 0!==n?n:1e6,i.call(this,t,e,0,n),this.si=null,this.sj=null,this.restitution=0,this.ri=new o,this.rj=new o,this.ni=new o}t("../math/Mat3"),n.prototype=new i,n.prototype.constructor=n;var s=new o,r=new o,a=new o;n.prototype.computeB=function(t){var e=this.a,i=this.b,o=this.bi,n=this.bj,l=this.ri,h=this.rj,p=s,c=r,u=o.velocity,d=o.angularVelocity;o.force,o.torque;var y=n.velocity,v=n.angularVelocity;n.force,n.torque;var f=a,m=this.jacobianElementA,w=this.jacobianElementB,g=this.ni;l.cross(g,p),h.cross(g,c),g.negate(m.spatial),p.negate(m.rotational),w.spatial.copy(g),w.rotational.copy(c),f.copy(n.position),f.vadd(h,f),f.vsub(o.position,f),f.vsub(l,f);var b=g.dot(f),x=this.restitution+1;return-b*e-(x*y.dot(g)-x*u.dot(g)+v.dot(c)-d.dot(p))*i-t*this.computeGiMf()};var l=new o,h=new o,p=new o,c=new o,u=new o;n.prototype.getImpactVelocityAlongNormal=function(){var t=l,e=h,i=p,o=c,n=u;return this.bi.position.vadd(this.ri,i),this.bj.position.vadd(this.rj,o),this.bi.getVelocityAtWorldPoint(i,t),this.bj.getVelocityAtWorldPoint(o,e),t.vsub(e,n),this.ni.dot(n)}},{"../math/Mat3":28,"../math/Vec3":31,"./Equation":21}],21:[function(t,e){e.exports=n;var i=t("../math/JacobianElement"),o=t("../math/Vec3");function n(t,e,o,s){this.id=n.id++,this.minForce=void 0===o?-1e6:o,this.maxForce=void 0===s?1e6:s,this.bi=t,this.bj=e,this.a=0,this.b=0,this.eps=0,this.jacobianElementA=new i,this.jacobianElementB=new i,this.enabled=!0,this.multiplier=0,this.setSpookParams(1e7,4,1/60)}n.prototype.constructor=n,n.id=0,n.prototype.setSpookParams=function(t,e,i){var o=e,n=t,s=i;this.a=4/(s*(1+4*o)),this.b=4*o/(1+4*o),this.eps=4/(s*s*n*(1+4*o))},n.prototype.computeB=function(t,e,i){var o=this.computeGW();return-this.computeGq()*t-o*e-this.computeGiMf()*i},n.prototype.computeGq=function(){var t=this.jacobianElementA,e=this.jacobianElementB,i=this.bi,o=this.bj,n=i.position,s=o.position;return t.spatial.dot(n)+e.spatial.dot(s)},new o,n.prototype.computeGW=function(){var t=this.jacobianElementA,e=this.jacobianElementB,i=this.bi,o=this.bj,n=i.velocity,s=o.velocity,r=i.angularVelocity,a=o.angularVelocity;return t.multiplyVectors(n,r)+e.multiplyVectors(s,a)},n.prototype.computeGWlambda=function(){var t=this.jacobianElementA,e=this.jacobianElementB,i=this.bi,o=this.bj,n=i.vlambda,s=o.vlambda,r=i.wlambda,a=o.wlambda;return t.multiplyVectors(n,r)+e.multiplyVectors(s,a)};var s=new o,r=new o,a=new o,l=new o;n.prototype.computeGiMf=function(){var t=this.jacobianElementA,e=this.jacobianElementB,i=this.bi,o=this.bj,n=i.force,h=i.torque,p=o.force,c=o.torque,u=i.invMassSolve,d=o.invMassSolve;return n.scale(u,s),p.scale(d,r),i.invInertiaWorldSolve.vmult(h,a),o.invInertiaWorldSolve.vmult(c,l),t.multiplyVectors(s,a)+e.multiplyVectors(r,l)};var h=new o;n.prototype.computeGiMGt=function(){var t=this.jacobianElementA,e=this.jacobianElementB,i=this.bi,o=this.bj,n=i.invMassSolve,s=o.invMassSolve,r=i.invInertiaWorldSolve,a=o.invInertiaWorldSolve,l=n+s;return r.vmult(t.rotational,h),l+=h.dot(t.rotational),a.vmult(e.rotational,h),l+h.dot(e.rotational)};var p=new o;new o,new o,new o,new o,new o,n.prototype.addToWlambda=function(t){var e=this.jacobianElementA,i=this.jacobianElementB,o=this.bi,n=this.bj,s=p;o.vlambda.addScaledVector(o.invMassSolve*t,e.spatial,o.vlambda),n.vlambda.addScaledVector(n.invMassSolve*t,i.spatial,n.vlambda),o.invInertiaWorldSolve.vmult(e.rotational,s),o.wlambda.addScaledVector(t,s,o.wlambda),n.invInertiaWorldSolve.vmult(i.rotational,s),n.wlambda.addScaledVector(t,s,n.wlambda)},n.prototype.computeC=function(){return this.computeGiMGt()+this.eps}},{"../math/JacobianElement":27,"../math/Vec3":31}],22:[function(t,e){e.exports=n;var i=t("./Equation"),o=t("../math/Vec3");function n(t,e,n){i.call(this,t,e,-n,n),this.ri=new o,this.rj=new o,this.t=new o}t("../math/Mat3"),n.prototype=new i,n.prototype.constructor=n;var s=new o,r=new o;n.prototype.computeB=function(t){this.a;var e=this.b;this.bi,this.bj;var i=this.ri,o=this.rj,n=s,a=r,l=this.t;i.cross(l,n),o.cross(l,a);var h=this.jacobianElementA,p=this.jacobianElementB;return l.negate(h.spatial),n.negate(h.rotational),p.spatial.copy(l),p.rotational.copy(a),-this.computeGW()*e-t*this.computeGiMf()}},{"../math/Mat3":28,"../math/Vec3":31,"./Equation":21}],23:[function(t,e){e.exports=n;var i=t("../math/Vec3");t("../math/Mat3");var o=t("./Equation");function n(t,e,n){var s=void 0!==(n=n||{}).maxForce?n.maxForce:1e6;o.call(this,t,e,-s,s),this.axisA=n.axisA?n.axisA.clone():new i(1,0,0),this.axisB=n.axisB?n.axisB.clone():new i(0,1,0),this.maxAngle=Math.PI/2}n.prototype=new o,n.prototype.constructor=n;var s=new i,r=new i;n.prototype.computeB=function(t){var e=this.a,i=this.b,o=this.axisA,n=this.axisB,a=s,l=r,h=this.jacobianElementA,p=this.jacobianElementB;return o.cross(n,a),n.cross(o,l),h.rotational.copy(l),p.rotational.copy(a),-(Math.cos(this.maxAngle)-o.dot(n))*e-this.computeGW()*i-t*this.computeGiMf()}},{"../math/Mat3":28,"../math/Vec3":31,"./Equation":21}],24:[function(t,e){e.exports=n;var i=t("../math/Vec3");t("../math/Mat3");var o=t("./Equation");function n(t,e,n){n=void 0!==n?n:1e6,o.call(this,t,e,-n,n),this.axisA=new i,this.axisB=new i,this.targetVelocity=0}n.prototype=new o,n.prototype.constructor=n,n.prototype.computeB=function(t){this.a;var e=this.b;this.bi,this.bj;var i=this.axisA,o=this.axisB,n=this.jacobianElementA,s=this.jacobianElementB;return n.rotational.copy(i),o.negate(s.rotational),-(this.computeGW()-this.targetVelocity)*e-t*this.computeGiMf()}},{"../math/Mat3":28,"../math/Vec3":31,"./Equation":21}],25:[function(t,e){var i=t("../utils/Utils");function o(t,e,n){n=i.defaults(n,{friction:.3,restitution:.3,contactEquationStiffness:1e7,contactEquationRelaxation:3,frictionEquationStiffness:1e7,frictionEquationRelaxation:3}),this.id=o.idCounter++,this.materials=[t,e],this.friction=n.friction,this.restitution=n.restitution,this.contactEquationStiffness=n.contactEquationStiffness,this.contactEquationRelaxation=n.contactEquationRelaxation,this.frictionEquationStiffness=n.frictionEquationStiffness,this.frictionEquationRelaxation=n.frictionEquationRelaxation}e.exports=o,o.idCounter=0},{"../utils/Utils":54}],26:[function(t,e){function i(t){var e="";"string"==typeof(t=t||{})?(e=t,t={}):"object"==typeof t&&(e=""),this.name=e,this.id=i.idCounter++,this.friction=void 0!==t.friction?t.friction:-1,this.restitution=void 0!==t.restitution?t.restitution:-1,this.correctInelastic=0}e.exports=i,i.idCounter=0},{}],27:[function(t,e){e.exports=o;var i=t("./Vec3");function o(){this.spatial=new i,this.rotational=new i}o.prototype.multiplyElement=function(t){return t.spatial.dot(this.spatial)+t.rotational.dot(this.rotational)},o.prototype.multiplyVectors=function(t,e){return t.dot(this.spatial)+e.dot(this.rotational)}},{"./Vec3":31}],28:[function(t,e){e.exports=o;var i=t("./Vec3");function o(t){this.elements=t||[0,0,0,0,0,0,0,0,0]}o.prototype.identity=function(){var t=this.elements;t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=0,t[7]=0,t[8]=1},o.prototype.setZero=function(){var t=this.elements;t[0]=0,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=0,t[6]=0,t[7]=0,t[8]=0},o.prototype.setTrace=function(t){var e=this.elements;e[0]=t.x,e[4]=t.y,e[8]=t.z},o.prototype.getTrace=function(t){t=t||new i;var e=this.elements;t.x=e[0],t.y=e[4],t.z=e[8]},o.prototype.vmult=function(t,e){e=e||new i;var o=this.elements,n=t.x,s=t.y,r=t.z;return e.x=o[0]*n+o[1]*s+o[2]*r,e.y=o[3]*n+o[4]*s+o[5]*r,e.z=o[6]*n+o[7]*s+o[8]*r,e},o.prototype.smult=function(t){for(var e=0;e<this.elements.length;e++)this.elements[e]*=t},o.prototype.mmult=function(t,e){for(var i=e||new o,n=0;n<3;n++)for(var s=0;s<3;s++){for(var r=0,a=0;a<3;a++)r+=t.elements[n+3*a]*this.elements[a+3*s];i.elements[n+3*s]=r}return i},o.prototype.scale=function(t,e){e=e||new o;for(var i=this.elements,n=e.elements,s=0;3!==s;s++)n[3*s+0]=t.x*i[3*s+0],n[3*s+1]=t.y*i[3*s+1],n[3*s+2]=t.z*i[3*s+2];return e},o.prototype.solve=function(t,e){e=e||new i;for(var o,n=[],s=0;s<12;s++)n.push(0);for(s=0;s<3;s++)for(o=0;o<3;o++)n[s+4*o]=this.elements[s+3*o];n[3]=t.x,n[7]=t.y,n[11]=t.z;var r,a,l=3,h=l;do{if(0===n[(s=h-l)+4*s])for(o=s+1;o<h;o++)if(0!==n[s+4*o]){r=4;do{n[(a=4-r)+4*s]+=n[a+4*o]}while(--r);break}if(0!==n[s+4*s])for(o=s+1;o<h;o++){var p=n[s+4*o]/n[s+4*s];r=4;do{n[(a=4-r)+4*o]=a<=s?0:n[a+4*o]-n[a+4*s]*p}while(--r)}}while(--l);if(e.z=n[11]/n[10],e.y=(n[7]-n[6]*e.z)/n[5],e.x=(n[3]-n[2]*e.z-n[1]*e.y)/n[0],isNaN(e.x)||isNaN(e.y)||isNaN(e.z)||e.x===1/0||e.y===1/0||e.z===1/0)throw"Could not solve equation! Got x=["+e.toString()+"], b=["+t.toString()+"], A=["+this.toString()+"]";return e},o.prototype.e=function(t,e,i){if(void 0===i)return this.elements[e+3*t];this.elements[e+3*t]=i},o.prototype.copy=function(t){for(var e=0;e<t.elements.length;e++)this.elements[e]=t.elements[e];return this},o.prototype.toString=function(){for(var t="",e=0;e<9;e++)t+=this.elements[e]+",";return t},o.prototype.reverse=function(t){t=t||new o;for(var e,i=[],n=0;n<18;n++)i.push(0);for(n=0;n<3;n++)for(e=0;e<3;e++)i[n+6*e]=this.elements[n+3*e];i[3]=1,i[9]=0,i[15]=0,i[4]=0,i[10]=1,i[16]=0,i[5]=0,i[11]=0,i[17]=1;var s,r,a=3,l=a;do{if(0===i[(n=l-a)+6*n])for(e=n+1;e<l;e++)if(0!==i[n+6*e]){s=6;do{i[(r=6-s)+6*n]+=i[r+6*e]}while(--s);break}if(0!==i[n+6*n])for(e=n+1;e<l;e++){var h=i[n+6*e]/i[n+6*n];s=6;do{i[(r=6-s)+6*e]=r<=n?0:i[r+6*e]-i[r+6*n]*h}while(--s)}}while(--a);n=2;do{e=n-1;do{h=i[n+6*e]/i[n+6*n],s=6;do{i[(r=6-s)+6*e]=i[r+6*e]-i[r+6*n]*h}while(--s)}while(e--)}while(--n);n=2;do{h=1/i[n+6*n],s=6;do{i[(r=6-s)+6*n]=i[r+6*n]*h}while(--s)}while(n--);n=2;do{e=2;do{if(r=i[3+e+6*n],isNaN(r)||r===1/0)throw"Could not reverse! A=["+this.toString()+"]";t.e(n,e,r)}while(e--)}while(n--);return t},o.prototype.setRotationFromQuaternion=function(t){var e=t.x,i=t.y,o=t.z,n=t.w,s=e+e,r=i+i,a=o+o,l=e*s,h=e*r,p=e*a,c=i*r,u=i*a,d=o*a,y=n*s,v=n*r,f=n*a,m=this.elements;return m[0]=1-(c+d),m[1]=h-f,m[2]=p+v,m[3]=h+f,m[4]=1-(l+d),m[5]=u-y,m[6]=p-v,m[7]=u+y,m[8]=1-(l+c),this},o.prototype.transpose=function(t){for(var e=(t=t||new o).elements,i=this.elements,n=0;3!==n;n++)for(var s=0;3!==s;s++)e[3*n+s]=i[3*s+n];return t}},{"./Vec3":31}],29:[function(t,e){e.exports=o;var i=t("./Vec3");function o(t,e,i,o){this.x=void 0!==t?t:0,this.y=void 0!==e?e:0,this.z=void 0!==i?i:0,this.w=void 0!==o?o:1}o.prototype.set=function(t,e,i,o){return this.x=t,this.y=e,this.z=i,this.w=o,this},o.prototype.toString=function(){return this.x+","+this.y+","+this.z+","+this.w},o.prototype.toArray=function(){return[this.x,this.y,this.z,this.w]},o.prototype.setFromAxisAngle=function(t,e){var i=Math.sin(.5*e);return this.x=t.x*i,this.y=t.y*i,this.z=t.z*i,this.w=Math.cos(.5*e),this},o.prototype.toAxisAngle=function(t){t=t||new i,this.normalize();var e=2*Math.acos(this.w),o=Math.sqrt(1-this.w*this.w);return o<.001?(t.x=this.x,t.y=this.y,t.z=this.z):(t.x=this.x/o,t.y=this.y/o,t.z=this.z/o),[t,e]};var n=new i,s=new i;o.prototype.setFromVectors=function(t,e){if(t.isAntiparallelTo(e)){var i=n,o=s;t.tangents(i,o),this.setFromAxisAngle(i,Math.PI)}else{var r=t.cross(e);this.x=r.x,this.y=r.y,this.z=r.z,this.w=Math.sqrt(Math.pow(t.norm(),2)*Math.pow(e.norm(),2))+t.dot(e),this.normalize()}return this},new i,new i,new i,o.prototype.mult=function(t,e){e=e||new o;var i=this.x,n=this.y,s=this.z,r=this.w,a=t.x,l=t.y,h=t.z,p=t.w;return e.x=i*p+r*a+n*h-s*l,e.y=n*p+r*l+s*a-i*h,e.z=s*p+r*h+i*l-n*a,e.w=r*p-i*a-n*l-s*h,e},o.prototype.inverse=function(t){var e=this.x,i=this.y,n=this.z,s=this.w;t=t||new o,this.conjugate(t);var r=1/(e*e+i*i+n*n+s*s);return t.x*=r,t.y*=r,t.z*=r,t.w*=r,t},o.prototype.conjugate=function(t){return(t=t||new o).x=-this.x,t.y=-this.y,t.z=-this.z,t.w=this.w,t},o.prototype.normalize=function(){var t=Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w);return 0===t?(this.x=0,this.y=0,this.z=0,this.w=0):(t=1/t,this.x*=t,this.y*=t,this.z*=t,this.w*=t),this},o.prototype.normalizeFast=function(){var t=(3-(this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w))/2;return 0===t?(this.x=0,this.y=0,this.z=0,this.w=0):(this.x*=t,this.y*=t,this.z*=t,this.w*=t),this},o.prototype.vmult=function(t,e){e=e||new i;var o=t.x,n=t.y,s=t.z,r=this.x,a=this.y,l=this.z,h=this.w,p=h*o+a*s-l*n,c=h*n+l*o-r*s,u=h*s+r*n-a*o,d=-r*o-a*n-l*s;return e.x=p*h+d*-r+c*-l-u*-a,e.y=c*h+d*-a+u*-r-p*-l,e.z=u*h+d*-l+p*-a-c*-r,e},o.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this.z=t.z,this.w=t.w,this},o.prototype.toEuler=function(t,e){var i,o,n;e=e||"YZX";var s=this.x,r=this.y,a=this.z,l=this.w;if("YZX"!==e)throw new Error("Euler order "+e+" not supported yet.");var h=s*r+a*l;if(h>.499&&(i=2*Math.atan2(s,l),o=Math.PI/2,n=0),h<-.499&&(i=-2*Math.atan2(s,l),o=-Math.PI/2,n=0),isNaN(i)){var p=s*s,c=r*r,u=a*a;i=Math.atan2(2*r*l-2*s*a,1-2*c-2*u),o=Math.asin(2*h),n=Math.atan2(2*s*l-2*r*a,1-2*p-2*u)}t.y=i,t.z=o,t.x=n},o.prototype.setFromEuler=function(t,e,i,o){o=o||"XYZ";var n=Math.cos(t/2),s=Math.cos(e/2),r=Math.cos(i/2),a=Math.sin(t/2),l=Math.sin(e/2),h=Math.sin(i/2);return"XYZ"===o?(this.x=a*s*r+n*l*h,this.y=n*l*r-a*s*h,this.z=n*s*h+a*l*r,this.w=n*s*r-a*l*h):"YXZ"===o?(this.x=a*s*r+n*l*h,this.y=n*l*r-a*s*h,this.z=n*s*h-a*l*r,this.w=n*s*r+a*l*h):"ZXY"===o?(this.x=a*s*r-n*l*h,this.y=n*l*r+a*s*h,this.z=n*s*h+a*l*r,this.w=n*s*r-a*l*h):"ZYX"===o?(this.x=a*s*r-n*l*h,this.y=n*l*r+a*s*h,this.z=n*s*h-a*l*r,this.w=n*s*r+a*l*h):"YZX"===o?(this.x=a*s*r+n*l*h,this.y=n*l*r+a*s*h,this.z=n*s*h-a*l*r,this.w=n*s*r-a*l*h):"XZY"===o&&(this.x=a*s*r-n*l*h,this.y=n*l*r-a*s*h,this.z=n*s*h+a*l*r,this.w=n*s*r+a*l*h),this},o.prototype.clone=function(){return new o(this.x,this.y,this.z,this.w)},o.prototype.slerp=function(t,e,i){i=i||new o;var n,s,r,a,l,h=this.x,p=this.y,c=this.z,u=this.w,d=t.x,y=t.y,v=t.z,f=t.w;return(s=h*d+p*y+c*v+u*f)<0&&(s=-s,d=-d,y=-y,v=-v,f=-f),1-s>1e-6?(n=Math.acos(s),r=Math.sin(n),a=Math.sin((1-e)*n)/r,l=Math.sin(e*n)/r):(a=1-e,l=e),i.x=a*h+l*d,i.y=a*p+l*y,i.z=a*c+l*v,i.w=a*u+l*f,i},o.prototype.integrate=function(t,e,i,n){n=n||new o;var s=t.x*i.x,r=t.y*i.y,a=t.z*i.z,l=this.x,h=this.y,p=this.z,c=this.w,u=.5*e;return n.x+=u*(s*c+r*p-a*h),n.y+=u*(r*c+a*l-s*p),n.z+=u*(a*c+s*h-r*l),n.w+=u*(-s*l-r*h-a*p),n},o.prototype.euqals=function(t){return this.x===t.x&&this.y===t.y&&this.z===t.z&&this.w===t.w}},{"./Vec3":31}],30:[function(t,e){var i=t("./Vec3"),o=t("./Quaternion");function n(t){t=t||{},this.position=new i,t.position&&this.position.copy(t.position),this.quaternion=new o,t.quaternion&&this.quaternion.copy(t.quaternion)}e.exports=n;var s=new o;n.pointToLocalFrame=function(t,e,o,n){return n=n||new i,o.vsub(t,n),e.conjugate(s),s.vmult(n,n),n},n.prototype.pointToLocal=function(t,e){return n.pointToLocalFrame(this.position,this.quaternion,t,e)},n.pointToWorldFrame=function(t,e,o,n){return n=n||new i,e.vmult(o,n),n.vadd(t,n),n},n.prototype.pointToWorld=function(t,e){return n.pointToWorldFrame(this.position,this.quaternion,t,e)},n.prototype.vectorToWorldFrame=function(t,e){return e=e||new i,this.quaternion.vmult(t,e),e},n.vectorToWorldFrame=function(t,e,i){return t.vmult(e,i),i},n.vectorToLocalFrame=function(t,e,o,n){return n=n||new i,e.w*=-1,e.vmult(o,n),e.w*=-1,n}},{"./Quaternion":29,"./Vec3":31}],31:[function(t,e){e.exports=o;var i=t("./Mat3");function o(t,e,i){this.x=t||0,this.y=e||0,this.z=i||0}o.ZERO=new o(0,0,0),o.UNIT_X=new o(1,0,0),o.UNIT_Y=new o(0,1,0),o.UNIT_Z=new o(0,0,1),o.prototype.cross=function(t,e){var i=t.x,n=t.y,s=t.z,r=this.x,a=this.y,l=this.z;return(e=e||new o).x=a*s-l*n,e.y=l*i-r*s,e.z=r*n-a*i,e},o.prototype.set=function(t,e,i){return this.x=t,this.y=e,this.z=i,this},o.prototype.setZero=function(){this.x=this.y=this.z=0},o.prototype.vadd=function(t,e){if(!e)return new o(this.x+t.x,this.y+t.y,this.z+t.z);e.x=t.x+this.x,e.y=t.y+this.y,e.z=t.z+this.z},o.prototype.vsub=function(t,e){if(!e)return new o(this.x-t.x,this.y-t.y,this.z-t.z);e.x=this.x-t.x,e.y=this.y-t.y,e.z=this.z-t.z},o.prototype.crossmat=function(){return new i([0,-this.z,this.y,this.z,0,-this.x,-this.y,this.x,0])},o.prototype.normalize=function(){var t=this.x,e=this.y,i=this.z,o=Math.sqrt(t*t+e*e+i*i);if(o>0){var n=1/o;this.x*=n,this.y*=n,this.z*=n}else this.x=0,this.y=0,this.z=0;return o},o.prototype.unit=function(t){t=t||new o;var e=this.x,i=this.y,n=this.z,s=Math.sqrt(e*e+i*i+n*n);return s>0?(s=1/s,t.x=e*s,t.y=i*s,t.z=n*s):(t.x=1,t.y=0,t.z=0),t},o.prototype.norm=function(){var t=this.x,e=this.y,i=this.z;return Math.sqrt(t*t+e*e+i*i)},o.prototype.length=o.prototype.norm,o.prototype.norm2=function(){return this.dot(this)},o.prototype.lengthSquared=o.prototype.norm2,o.prototype.distanceTo=function(t){var e=this.x,i=this.y,o=this.z,n=t.x,s=t.y,r=t.z;return Math.sqrt((n-e)*(n-e)+(s-i)*(s-i)+(r-o)*(r-o))},o.prototype.distanceSquared=function(t){var e=this.x,i=this.y,o=this.z,n=t.x,s=t.y,r=t.z;return(n-e)*(n-e)+(s-i)*(s-i)+(r-o)*(r-o)},o.prototype.mult=function(t,e){e=e||new o;var i=this.x,n=this.y,s=this.z;return e.x=t*i,e.y=t*n,e.z=t*s,e},o.prototype.vmul=function(t,e){return(e=e||new o).x=t.x*this.x,e.y=t.y*this.y,e.z=t.z*this.z,e},o.prototype.scale=o.prototype.mult,o.prototype.addScaledVector=function(t,e,i){return(i=i||new o).x=this.x+t*e.x,i.y=this.y+t*e.y,i.z=this.z+t*e.z,i},o.prototype.dot=function(t){return this.x*t.x+this.y*t.y+this.z*t.z},o.prototype.isZero=function(){return 0===this.x&&0===this.y&&0===this.z},o.prototype.negate=function(t){return(t=t||new o).x=-this.x,t.y=-this.y,t.z=-this.z,t};var n=new o,s=new o;o.prototype.tangents=function(t,e){var i=this.norm();if(i>0){var o=n,r=1/i;o.set(this.x*r,this.y*r,this.z*r);var a=s;Math.abs(o.x)<.9?(a.set(1,0,0),o.cross(a,t)):(a.set(0,1,0),o.cross(a,t)),o.cross(t,e)}else t.set(1,0,0),e.set(0,1,0)},o.prototype.toString=function(){return this.x+","+this.y+","+this.z},o.prototype.toArray=function(){return[this.x,this.y,this.z]},o.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this.z=t.z,this},o.prototype.lerp=function(t,e,i){var o=this.x,n=this.y,s=this.z;i.x=o+(t.x-o)*e,i.y=n+(t.y-n)*e,i.z=s+(t.z-s)*e},o.prototype.almostEquals=function(t,e){return void 0===e&&(e=1e-6),!(Math.abs(this.x-t.x)>e||Math.abs(this.y-t.y)>e||Math.abs(this.z-t.z)>e)},o.prototype.almostZero=function(t){return void 0===t&&(t=1e-6),!(Math.abs(this.x)>t||Math.abs(this.y)>t||Math.abs(this.z)>t)};var r=new o;o.prototype.isAntiparallelTo=function(t,e){return this.negate(r),r.almostEquals(t,e)},o.prototype.clone=function(){return new o(this.x,this.y,this.z)}},{"./Mat3":28}],32:[function(t,e){e.exports=c;var i=t("../utils/EventTarget"),o=t("../shapes/Shape"),n=t("../math/Vec3"),s=t("../math/Mat3"),r=t("../math/Quaternion");t("../material/Material");var a=t("../collision/AABB"),l=t("../shapes/Box"),h=t("../collision/RaycastResult"),p=t("../world/World");function c(t){t=t||{},i.apply(this),this.id=c.idCounter++,this.world=null,this.preStep=null,this.ccdSpeedThreshold=void 0!==t.ccdSpeedThreshold?t.ccdSpeedThreshold:-1,this.ccdIterations=void 0!==t.ccdIterations?t.ccdIterations:5,this.postStep=null,this.vlambda=new n,this.collisionFilterGroup="number"==typeof t.collisionFilterGroup?t.collisionFilterGroup:1,this.collisionFilterMask="number"==typeof t.collisionFilterMask?t.collisionFilterMask:-1,this.collisionResponse=!0,this.position=new n,this.previousPosition=new n,this.interpolatedPosition=new n,this.initPosition=new n,t.position&&(this.position.copy(t.position),this.previousPosition.copy(t.position),this.interpolatedPosition.copy(t.position),this.initPosition.copy(t.position)),this.velocity=new n,t.velocity&&this.velocity.copy(t.velocity),this.initVelocity=new n,this.force=new n;var e="number"==typeof t.mass?t.mass:0;this.mass=e,this.invMass=e>0?1/e:0,this.material=t.material||null,this.linearDamping="number"==typeof t.linearDamping?t.linearDamping:.01,this.type=e<=0?c.STATIC:c.DYNAMIC,typeof t.type==typeof c.STATIC&&(this.type=t.type),this.allowSleep=void 0===t.allowSleep||t.allowSleep,this.sleepState=0,this.sleepSpeedLimit=void 0!==t.sleepSpeedLimit?t.sleepSpeedLimit:.1,this.sleepTimeLimit=void 0!==t.sleepTimeLimit?t.sleepTimeLimit:1,this.timeLastSleepy=0,this._wakeUpAfterNarrowphase=!1,this.torque=new n,this.quaternion=new r,this.initQuaternion=new r,this.previousQuaternion=new r,this.interpolatedQuaternion=new r,t.quaternion&&(this.quaternion.copy(t.quaternion),this.initQuaternion.copy(t.quaternion),this.previousQuaternion.copy(t.quaternion),this.interpolatedQuaternion.copy(t.quaternion)),this.angularVelocity=new n,t.angularVelocity&&this.angularVelocity.copy(t.angularVelocity),this.initAngularVelocity=new n,this.shapes=[],this.shapeOffsets=[],this.shapeOrientations=[],this.inertia=new n,this.invInertia=new n,this.invInertiaWorld=new s,this.invMassSolve=0,this.invInertiaSolve=new n,this.invInertiaWorldSolve=new s,this.fixedRotation=void 0!==t.fixedRotation&&t.fixedRotation,this.useGravity=!0,this.angularDamping=void 0!==t.angularDamping?t.angularDamping:.01,this.linearFactor=new n(1,1,1),t.linearFactor&&this.linearFactor.copy(t.linearFactor),this.angularFactor=new n(1,1,1),t.angularFactor&&this.angularFactor.copy(t.angularFactor),this.aabb=new a,this.aabbNeedsUpdate=!0,this.boundingRadius=0,this.wlambda=new n,t.shape&&this.addShape(t.shape),this.hasTrigger=!0,this.updateMassProperties()}c.prototype=new i,c.prototype.constructor=c,c.COLLIDE_EVENT_NAME="collide",c.DYNAMIC=1,c.STATIC=2,c.KINEMATIC=4,c.AWAKE=0,c.SLEEPY=1,c.SLEEPING=2,c.idCounter=0,c.wakeupEvent={type:"wakeup"},c.prototype.wakeUp=function(){var t=this.sleepState;this.sleepState=0,this._wakeUpAfterNarrowphase=!1,t===c.SLEEPING&&this.dispatchEvent(c.wakeupEvent)},c.prototype.sleep=function(){this.sleepState=c.SLEEPING,this.velocity.set(0,0,0),this.angularVelocity.set(0,0,0),this._wakeUpAfterNarrowphase=!1},c.sleepyEvent={type:"sleepy"},c.sleepEvent={type:"sleep"},c.prototype.sleepTick=function(t){if(this.allowSleep){var e=this.sleepState,i=this.velocity.norm2()+this.angularVelocity.norm2(),o=Math.pow(this.sleepSpeedLimit,2);e===c.AWAKE&&i<o?(this.sleepState=c.SLEEPY,this.timeLastSleepy=t,this.dispatchEvent(c.sleepyEvent)):e===c.SLEEPY&&i>o?this.wakeUp():e===c.SLEEPY&&t-this.timeLastSleepy>this.sleepTimeLimit&&(this.sleep(),this.dispatchEvent(c.sleepEvent))}},c.prototype.updateSolveMassProperties=function(){this.sleepState===c.SLEEPING||this.type===c.KINEMATIC?(this.invMassSolve=0,this.invInertiaSolve.setZero(),this.invInertiaWorldSolve.setZero()):(this.invMassSolve=this.invMass,this.invInertiaSolve.copy(this.invInertia),this.invInertiaWorldSolve.copy(this.invInertiaWorld))},c.prototype.pointToLocalFrame=function(t,e){return e=e||new n,t.vsub(this.position,e),this.quaternion.conjugate().vmult(e,e),e},c.prototype.vectorToLocalFrame=function(t,e){return e=e||new n,this.quaternion.conjugate().vmult(t,e),e},c.prototype.pointToWorldFrame=function(t,e){return e=e||new n,this.quaternion.vmult(t,e),e.vadd(this.position,e),e},c.prototype.vectorToWorldFrame=function(t,e){return e=e||new n,this.quaternion.vmult(t,e),e};var u=new n,d=new r;c.prototype.addShape=function(t,e,i){var o=new n,s=new r;return e&&o.copy(e),i&&s.copy(i),this.shapes.push(t),this.shapeOffsets.push(o),this.shapeOrientations.push(s),this.aabbNeedsUpdate=!0,this.updateMassProperties(),this.updateBoundingRadius(),this.updateHasTrigger(),p.idToShapeMap[t.id]=t,t.body=this,this},c.prototype.removeShape=function(t){var e=this.shapes.indexOf(t);-1!==e&&(this.shapes.splice(e,1),this.shapeOffsets.splice(e,1),this.shapeOrientations.splice(e,1),this.aabbNeedsUpdate=!0,this.updateMassProperties(),this.updateBoundingRadius(),this.updateHasTrigger())},c.prototype.updateBoundingRadius=function(){for(var t=this.shapes,e=this.shapeOffsets,i=t.length,o=0,n=0;n!==i;n++){var s=t[n];s.updateBoundingSphereRadius();var r=e[n].norm(),a=s.boundingSphereRadius;r+a>o&&(o=r+a)}this.boundingRadius=o};var y=new a;c.prototype.computeAABB=function(){for(var t=this.shapes,e=this.shapeOffsets,i=this.shapeOrientations,o=t.length,n=u,s=d,r=this.quaternion,a=this.aabb,l=y,h=0;h!==o;h++){var p=t[h];r.vmult(e[h],n),n.vadd(this.position,n),i[h].mult(r,s),p.calculateWorldAABB(n,s,l.lowerBound,l.upperBound),0===h?a.copy(l):a.extend(l)}this.aabbNeedsUpdate=!1};var v=new s,f=new s;new s,c.prototype.updateInertiaWorld=function(t){var e=this.invInertia;if(e.x!==e.y||e.y!==e.z||t){var i=v,o=f;i.setRotationFromQuaternion(this.quaternion),i.transpose(o),i.scale(e,i),i.mmult(o,this.invInertiaWorld)}},new n;var m=new n;c.prototype.applyForce=function(t,e){if(this.type===c.DYNAMIC){var i=m;e.cross(t,i),this.force.vadd(t,this.force),this.torque.vadd(i,this.torque)}};var w=new n,g=new n;c.prototype.applyLocalForce=function(t,e){if(this.type===c.DYNAMIC){var i=w,o=g;this.vectorToWorldFrame(t,i),this.vectorToWorldFrame(e,o),this.applyForce(i,o)}},new n;var b=new n,x=new n;c.prototype.applyImpulse=function(t,e){if(this.type===c.DYNAMIC){var i=e,o=b;o.copy(t),o.mult(this.invMass,o),this.velocity.vadd(o,this.velocity);var n=x;i.cross(t,n),this.invInertiaWorld.vmult(n,n),this.angularVelocity.vadd(n,this.angularVelocity)}};var B=new n,S=new n;c.prototype.applyLocalImpulse=function(t,e){if(this.type===c.DYNAMIC){var i=B,o=S;this.vectorToWorldFrame(t,i),this.vectorToWorldFrame(e,o),this.applyImpulse(i,o)}};var A=new n;c.prototype.updateMassProperties=function(){var t=A;this.invMass=this.mass>0?1/this.mass:0;var e=this.inertia,i=this.fixedRotation;this.computeAABB(),t.set((this.aabb.upperBound.x-this.aabb.lowerBound.x)/2,(this.aabb.upperBound.y-this.aabb.lowerBound.y)/2,(this.aabb.upperBound.z-this.aabb.lowerBound.z)/2),l.calculateInertia(t,this.mass,e),this.invInertia.set(e.x>0&&!i?1/e.x:0,e.y>0&&!i?1/e.y:0,e.z>0&&!i?1/e.z:0),this.updateInertiaWorld(!0)},c.prototype.getVelocityAtWorldPoint=function(t,e){var i=new n;return t.vsub(this.position,i),this.angularVelocity.cross(i,e),this.velocity.vadd(e,e),e},new n,new n;var C=new n;new r;var E=new r;c.prototype.integrate=function(t,e,i){if(this.previousPosition.copy(this.position),this.previousQuaternion.copy(this.quaternion),(this.type===c.DYNAMIC||p.integrateKinematic&&this.type===c.KINEMATIC)&&this.sleepState!==c.SLEEPING){var o=this.velocity,n=this.angularVelocity,s=this.position,r=this.force,a=this.torque,l=this.quaternion,h=this.invMass,u=this.invInertiaWorld,d=this.linearFactor,y=h*t;o.x+=r.x*y*d.x,o.y+=r.y*y*d.y,o.z+=r.z*y*d.z;var v=u.elements,f=this.angularFactor,m=a.x*f.x,w=a.y*f.y,g=a.z*f.z;n.x+=t*(v[0]*m+v[1]*w+v[2]*g),n.y+=t*(v[3]*m+v[4]*w+v[5]*g),n.z+=t*(v[6]*m+v[7]*w+v[8]*g),l.integrate(this.angularVelocity,t,this.angularFactor,l),e&&(i?l.normalizeFast():l.normalize()),this.type===c.DYNAMIC&&this.ccdSpeedThreshold>0&&this.velocity.length()>=Math.pow(this.ccdSpeedThreshold,2)&&this.integrateToTimeOfImpact(t)||(o.mult(t,C),s.vadd(C,s)),this.aabbNeedsUpdate=!0,this.updateInertiaWorld()}},c.prototype.updateKinematic=function(t){if(this.type===c.KINEMATIC&&0!==t){this.velocity.setZero(),this.angularVelocity.setZero();var e=1/t;this.previousPosition.almostEquals(this.position)||(this.position.vsub(this.previousPosition,this.velocity),this.velocity.mult(e,this.velocity),this.aabbNeedsUpdate=!0),this.previousQuaternion.euqals(this.quaternion)||(this.previousQuaternion.conjugate(E),E.mult(this.quaternion,E),E.toEuler(this.angularVelocity),this.angularVelocity.mult(e,this.angularVelocity),this.aabbNeedsUpdate=!0)}},c.prototype.applyGravity=function(t){if(this.type===c.DYNAMIC){if(1==this.linearDamping)this.force.setZero();else if(this.useGravity){var e=t.x,i=t.y,o=t.z,n=this.force,s=this.mass;n.x+=s*e,n.y+=s*i,n.z+=s*o}1==this.angularDamping&&this.torque.setZero()}};var M=new n,_=new n,z=new n,F=new n,R=new n,T=new n,P=new h,q=new h,I=new h,V=new n,L=new n,N=new n,W=new s;c.prototype.integrateToTimeOfImpact=function(t){M.copy(this.velocity),M.normalize(),this.velocity.mult(t,z),z.vadd(this.position,_),_.vsub(this.position,R);var e,i=R.length(),n=this.collisionFilterGroup;this.collisionFilterGroup=0;for(var s=1,r=0;r<this.shapes.length;r++){var a=this.shapes[r],l={collisionFilterMask:a.collisionFilterMask,collisionFilterGroup:a.collisionFilterGroup,skipBackfaces:!0};if(V.copy(this.position),V.vadd(R,L),c.DrawLine(V,L),this.world.raycastClosest(V,L,l,P),e=P.body,a.type===o.types.SPHERE&&(s=a.radius,p.ccdSphereAdvance)){if(N.copy(this.velocity),N.y=0,N.normalize(),V.copy(N),W.identity(),W.elements[0]=V.x,W.elements[1]=-V.z,W.elements[3]=V.z,W.elements[4]=V.x,F.set(0,a.radius,0),W.vmult(F,F),F.z=F.y,F.y=0,F.vadd(this.position,V),z.vadd(V,L),c.DrawLine(V,L),this.world.raycastClosest(V,L,l,q),F.negate(F),F.vadd(this.position,V),z.vadd(V,L),c.DrawLine(V,L),this.world.raycastClosest(V,L,l,I),q.hasHit){q.hitPointWorld.vsub(this.position,V);var h=M.dot(V);(!P.hasHit||P.distance>h)&&(P.hasHit=!0,P.distance=h,e=q.body,M.mult(h,V),V.vadd(this.position,P.hitPointWorld))}I.hasHit&&(I.hitPointWorld.vsub(this.position,V),h=M.dot(V),(!P.hasHit||P.distance>h)&&(P.hasHit=!0,P.distance=h,e=I.body,M.mult(h,V),V.vadd(this.position,P.hitPointWorld)))}if(e)break}if(this.collisionFilterGroup=n,!e)return!1;P.hasHit&&c.DrawSphere(P.hitPointWorld,.05),q.hasHit&&c.DrawSphere(q.hitPointWorld,.05),I.hasHit&&c.DrawSphere(I.hitPointWorld,.05);var u=1;(_=P.hitPointWorld).vsub(this.position,R),u=P.distance/i,T.copy(this.position);for(var d=0,y=0,v=u,f=1;f>=y&&d<this.ccdIterations;){d++,v=(f+y)/2,R.mult(v,C),T.vadd(C,this.position),this.computeAABB(),c.DrawSphere(this.position,s);var m=this.aabb.overlaps(e.aabb);if(m){var w=[];this.world.narrowphase.getContacts([this],[e],this.world,w,[],[],[]),m=w.length>0}m?f=v:y=v}return u=f,this.position.copy(T),R.mult(u,C),this.position.vadd(C,this.position),!0},c.DrawSphere=c.DrawLine=function(){},c.prototype.isSleeping=function(){return this.sleepState===c.SLEEPING},c.prototype.isSleepy=function(){return this.sleepState===c.SLEEPY},c.prototype.isAwake=function(){return this.sleepState===c.AWAKE},c.prototype.updateHasTrigger=function(){for(var t=this.shapes.length;t--&&(this.hasTrigger=!this.shapes[t].collisionResponse,!this.hasTrigger););}},{"../collision/AABB":3,"../collision/RaycastResult":11,"../material/Material":26,"../math/Mat3":28,"../math/Quaternion":29,"../math/Vec3":31,"../shapes/Box":38,"../shapes/Shape":44,"../utils/EventTarget":50,"../world/World":57}],33:[function(t,e){t("./Body");var i=t("../math/Vec3"),o=t("../math/Quaternion");t("../collision/RaycastResult");var n=t("../collision/Ray"),s=t("../objects/WheelInfo");function r(t){this.chassisBody=t.chassisBody,this.wheelInfos=[],this.sliding=!1,this.world=null,this.indexRightAxis=void 0!==t.indexRightAxis?t.indexRightAxis:1,this.indexForwardAxis=void 0!==t.indexForwardAxis?t.indexForwardAxis:0,this.indexUpAxis=void 0!==t.indexUpAxis?t.indexUpAxis:2}e.exports=r,new i,new i,new i;var a=new i,l=new i,h=new i;new n,r.prototype.addWheel=function(t){var e=new s(t=t||{}),i=this.wheelInfos.length;return this.wheelInfos.push(e),i},r.prototype.setSteeringValue=function(t,e){this.wheelInfos[e].steering=t},new i,r.prototype.applyEngineForce=function(t,e){this.wheelInfos[e].engineForce=t},r.prototype.setBrake=function(t,e){this.wheelInfos[e].brake=t},r.prototype.addToWorld=function(t){this.constraints,t.addBody(this.chassisBody);var e=this;this.preStepCallback=function(){e.updateVehicle(t.dt)},t.addEventListener("preStep",this.preStepCallback),this.world=t},r.prototype.getVehicleAxisWorld=function(t,e){e.set(0===t?1:0,1===t?1:0,2===t?1:0),this.chassisBody.vectorToWorldFrame(e,e)},r.prototype.updateVehicle=function(t){for(var e=this.wheelInfos,o=e.length,n=this.chassisBody,s=0;s<o;s++)this.updateWheelTransform(s);this.currentVehicleSpeedKmHour=3.6*n.velocity.norm();var r=new i;for(this.getVehicleAxisWorld(this.indexForwardAxis,r),r.dot(n.velocity)<0&&(this.currentVehicleSpeedKmHour*=-1),s=0;s<o;s++)this.castRay(e[s]);this.updateSuspension(t);var a=new i,l=new i;for(s=0;s<o;s++){var h=(d=e[s]).suspensionForce;h>d.maxSuspensionForce&&(h=d.maxSuspensionForce),d.raycastResult.hitNormalWorld.scale(h*t,a),d.raycastResult.hitPointWorld.vsub(n.position,l),n.applyImpulse(a,l)}this.updateFriction(t);var p=new i,c=new i,u=new i;for(s=0;s<o;s++){var d=e[s];n.getVelocityAtWorldPoint(d.chassisConnectionPointWorld,u);var y=1;if(1===this.indexUpAxis&&(y=-1),d.isInContact){this.getVehicleAxisWorld(this.indexForwardAxis,c);var v=c.dot(d.raycastResult.hitNormalWorld);d.raycastResult.hitNormalWorld.scale(v,p),c.vsub(p,c);var f=c.dot(u);d.deltaRotation=y*f*t/d.radius}!d.sliding&&d.isInContact||0===d.engineForce||!d.useCustomSlidingRotationalSpeed||(d.deltaRotation=(d.engineForce>0?1:-1)*d.customSlidingRotationalSpeed*t),Math.abs(d.brake)>Math.abs(d.engineForce)&&(d.deltaRotation=0),d.rotation+=d.deltaRotation,d.deltaRotation*=.99}},r.prototype.updateSuspension=function(){for(var t=this.chassisBody.mass,e=this.wheelInfos,i=e.length,o=0;o<i;o++){var n=e[o];if(n.isInContact){var s,r=n.suspensionRestLength-n.suspensionLength;s=n.suspensionStiffness*r*n.clippedInvContactDotSuspension;var a=n.suspensionRelativeVelocity;s-=(a<0?n.dampingCompression:n.dampingRelaxation)*a,n.suspensionForce=s*t,n.suspensionForce<0&&(n.suspensionForce=0)}else n.suspensionForce=0}},r.prototype.removeFromWorld=function(t){this.constraints,t.remove(this.chassisBody),t.removeEventListener("preStep",this.preStepCallback),this.world=null};var p=new i,c=new i;r.prototype.castRay=function(t){var e=p,o=c;this.updateWheelTransformWorld(t);var n=this.chassisBody,s=-1,r=t.suspensionRestLength+t.radius;t.directionWorld.scale(r,e);var a=t.chassisConnectionPointWorld;a.vadd(e,o);var l=t.raycastResult;l.reset();var h=n.collisionResponse;n.collisionResponse=!1,this.world.rayTest(a,o,l),n.collisionResponse=h;var u=l.body;if(t.raycastResult.groundObject=0,u){s=l.distance,t.raycastResult.hitNormalWorld=l.hitNormalWorld,t.isInContact=!0;var d=l.distance;t.suspensionLength=d-t.radius;var y=t.suspensionRestLength-t.maxSuspensionTravel,v=t.suspensionRestLength+t.maxSuspensionTravel;t.suspensionLength<y&&(t.suspensionLength=y),t.suspensionLength>v&&(t.suspensionLength=v,t.raycastResult.reset());var f=t.raycastResult.hitNormalWorld.dot(t.directionWorld),m=new i;n.getVelocityAtWorldPoint(t.raycastResult.hitPointWorld,m);var w=t.raycastResult.hitNormalWorld.dot(m);if(f>=-.1)t.suspensionRelativeVelocity=0,t.clippedInvContactDotSuspension=10;else{var g=-1/f;t.suspensionRelativeVelocity=w*g,t.clippedInvContactDotSuspension=g}}else t.suspensionLength=t.suspensionRestLength+0*t.maxSuspensionTravel,t.suspensionRelativeVelocity=0,t.directionWorld.scale(-1,t.raycastResult.hitNormalWorld),t.clippedInvContactDotSuspension=1;return s},r.prototype.updateWheelTransformWorld=function(t){t.isInContact=!1;var e=this.chassisBody;e.pointToWorldFrame(t.chassisConnectionPointLocal,t.chassisConnectionPointWorld),e.vectorToWorldFrame(t.directionLocal,t.directionWorld),e.vectorToWorldFrame(t.axleLocal,t.axleWorld)},r.prototype.updateWheelTransform=function(t){var e=a,i=l,n=h,s=this.wheelInfos[t];this.updateWheelTransformWorld(s),s.directionLocal.scale(-1,e),i.copy(s.axleLocal),e.cross(i,n),n.normalize(),i.normalize();var r=s.steering,p=new o;p.setFromAxisAngle(e,r);var c=new o;c.setFromAxisAngle(i,s.rotation);var u=s.worldTransform.quaternion;this.chassisBody.quaternion.mult(p,u),u.mult(c,u),u.normalize();var d=s.worldTransform.position;d.copy(s.directionWorld),d.scale(s.suspensionLength,d),d.vadd(s.chassisConnectionPointWorld,d)};var u=[new i(1,0,0),new i(0,1,0),new i(0,0,1)];r.prototype.getWheelTransformWorld=function(t){return this.wheelInfos[t].worldTransform};var d=new i,y=[],v=[];r.prototype.updateFriction=function(t){for(var e=d,o=this.wheelInfos,n=o.length,s=this.chassisBody,r=v,a=y,l=0;l<n;l++){var h=(E=o[l]).raycastResult.body;E.sideImpulse=0,E.forwardImpulse=0,r[l]||(r[l]=new i),a[l]||(a[l]=new i)}for(l=0;l<n;l++)if(h=(E=o[l]).raycastResult.body){var p=a[l];this.getWheelTransformWorld(l).vectorToWorldFrame(u[this.indexRightAxis],p);var c=E.raycastResult.hitNormalWorld,f=p.dot(c);c.scale(f,e),p.vsub(e,p),p.normalize(),c.cross(p,r[l]),r[l].normalize(),E.sideImpulse=_(s,E.raycastResult.hitPointWorld,h,E.raycastResult.hitPointWorld,p),E.sideImpulse*=1}for(this.sliding=!1,l=0;l<n;l++){h=(E=o[l]).raycastResult.body;var m=0;if(E.slipInfo=1,h){var w=E.brake?E.brake:0;m=g(s,h,E.raycastResult.hitPointWorld,r[l],w);var b=w/(m+=E.engineForce*t);E.slipInfo*=b}if(E.forwardImpulse=0,E.skidInfo=1,h){E.skidInfo=1;var x=E.suspensionForce*t*E.frictionSlip,B=x*x;E.forwardImpulse=m;var S=.5*E.forwardImpulse,A=1*E.sideImpulse,C=S*S+A*A;E.sliding=!1,C>B&&(this.sliding=!0,E.sliding=!0,b=x/Math.sqrt(C),E.skidInfo*=b)}}if(this.sliding)for(l=0;l<n;l++)0!==(E=o[l]).sideImpulse&&E.skidInfo<1&&(E.forwardImpulse*=E.skidInfo,E.sideImpulse*=E.skidInfo);for(l=0;l<n;l++){var E=o[l],M=new i;if(E.raycastResult.hitPointWorld.vsub(s.position,M),0!==E.forwardImpulse){var z=new i;r[l].scale(E.forwardImpulse,z),s.applyImpulse(z,M)}if(0!==E.sideImpulse){h=E.raycastResult.body;var F=new i;E.raycastResult.hitPointWorld.vsub(h.position,F);var R=new i;a[l].scale(E.sideImpulse,R),s.vectorToLocalFrame(M,M),M["xyz"[this.indexUpAxis]]*=E.rollInfluence,s.vectorToWorldFrame(M,M),s.applyImpulse(R,M),R.scale(-1,R),h.applyImpulse(R,F)}}};var f=new i,m=new i,w=new i;function g(t,e,i,o,n){var s=0,r=i,a=f,l=m,h=w;return t.getVelocityAtWorldPoint(r,a),e.getVelocityAtWorldPoint(r,l),a.vsub(l,h),n<(s=-o.dot(h)*(1/(A(t,i,o)+A(e,i,o))))&&(s=n),s<-n&&(s=-n),s}var b=new i,x=new i,B=new i,S=new i;function A(t,e,i){var o=b,n=x,s=B,r=S;return e.vsub(t.position,o),o.cross(i,n),t.invInertiaWorld.vmult(n,r),r.cross(o,s),t.invMass+i.dot(s)}var C=new i,E=new i,M=new i;function _(t,e,i,o,n){if(n.norm2()>1.1)return 0;var s=C,r=E,a=M;return t.getVelocityAtWorldPoint(e,s),i.getVelocityAtWorldPoint(o,r),s.vsub(r,a),-.2*n.dot(a)*(1/(t.invMass+i.invMass))}},{"../collision/Ray":10,"../collision/RaycastResult":11,"../math/Quaternion":29,"../math/Vec3":31,"../objects/WheelInfo":37,"./Body":32}],34:[function(t,e){var i=t("./Body"),o=t("../shapes/Sphere"),n=t("../shapes/Box"),s=t("../math/Vec3"),r=t("../constraints/HingeConstraint");function a(t){if(this.wheelBodies=[],this.coordinateSystem=void 0===t.coordinateSystem?new s(1,2,3):t.coordinateSystem.clone(),this.chassisBody=t.chassisBody,!this.chassisBody){var e=new n(new s(5,2,.5));this.chassisBody=new i(1,e)}this.constraints=[],this.wheelAxes=[],this.wheelForces=[]}e.exports=a,a.prototype.addWheel=function(t){var e=(t=t||{}).body;e||(e=new i(1,new o(1.2))),this.wheelBodies.push(e),this.wheelForces.push(0),new s;var n=void 0!==t.position?t.position.clone():new s,a=new s;this.chassisBody.pointToWorldFrame(n,a),e.position.set(a.x,a.y,a.z);var l=void 0!==t.axis?t.axis.clone():new s(0,1,0);this.wheelAxes.push(l);var h=new r(this.chassisBody,e,{pivotA:n,axisA:l,pivotB:s.ZERO,axisB:l,collideConnected:!1});return this.constraints.push(h),this.wheelBodies.length-1},a.prototype.setSteeringValue=function(t,e){var i=this.wheelAxes[e],o=Math.cos(t),n=Math.sin(t),s=i.x,r=i.y;this.constraints[e].axisA.set(o*s-n*r,n*s+o*r,0)},a.prototype.setMotorSpeed=function(t,e){var i=this.constraints[e];i.enableMotor(),i.motorTargetVelocity=t},a.prototype.disableMotor=function(t){this.constraints[t].disableMotor()};var l=new s;a.prototype.setWheelForce=function(t,e){this.wheelForces[e]=t},a.prototype.applyWheelForce=function(t,e){var i=this.wheelAxes[e],o=this.wheelBodies[e],n=o.torque;i.scale(t,l),o.vectorToWorldFrame(l,l),n.vadd(l,n)},a.prototype.addToWorld=function(t){for(var e=this.constraints,i=this.wheelBodies.concat([this.chassisBody]),o=0;o<i.length;o++)t.addBody(i[o]);for(o=0;o<e.length;o++)t.addConstraint(e[o]);t.addEventListener("preStep",this._update.bind(this))},a.prototype._update=function(){for(var t=this.wheelForces,e=0;e<t.length;e++)this.applyWheelForce(t[e],e)},a.prototype.removeFromWorld=function(t){for(var e=this.constraints,i=this.wheelBodies.concat([this.chassisBody]),o=0;o<i.length;o++)t.remove(i[o]);for(o=0;o<e.length;o++)t.removeConstraint(e[o])};var h=new s;a.prototype.getWheelSpeed=function(t){var e=this.wheelAxes[t],i=this.wheelBodies[t].angularVelocity;return this.chassisBody.vectorToWorldFrame(e,h),i.dot(h)}},{"../constraints/HingeConstraint":16,"../math/Vec3":31,"../shapes/Box":38,"../shapes/Sphere":45,"./Body":32}],35:[function(t,e){e.exports=o,t("../shapes/Shape");var i=t("../math/Vec3");function o(){this.particles=[],this.density=1,this.smoothingRadius=1,this.speedOfSound=1,this.viscosity=.01,this.eps=1e-6,this.pressures=[],this.densities=[],this.neighbors=[]}t("../math/Quaternion"),t("../shapes/Particle"),t("../objects/Body"),t("../material/Material"),o.prototype.add=function(t){this.particles.push(t),this.neighbors.length<this.particles.length&&this.neighbors.push([])},o.prototype.remove=function(t){var e=this.particles.indexOf(t);-1!==e&&(this.particles.splice(e,1),this.neighbors.length>this.particles.length&&this.neighbors.pop())};var n=new i;o.prototype.getNeighbors=function(t,e){for(var i=this.particles.length,o=t.id,s=this.smoothingRadius*this.smoothingRadius,r=n,a=0;a!==i;a++){var l=this.particles[a];l.position.vsub(t.position,r),o!==l.id&&r.norm2()<s&&e.push(l)}};var s=new i,r=new i,a=new i,l=new i,h=new i,p=new i;o.prototype.update=function(){for(var t=this.particles.length,e=s,i=this.speedOfSound,o=this.eps,n=0;n!==t;n++){var c=this.particles[n];(A=this.neighbors[n]).length=0,this.getNeighbors(c,A),A.push(this.particles[n]);for(var u=A.length,d=0,y=0;y!==u;y++){c.position.vsub(A[y].position,e);var v=e.norm(),f=this.w(v);d+=A[y].mass*f}this.densities[n]=d,this.pressures[n]=i*i*(this.densities[n]-this.density)}var m=r,w=a,g=l,b=h,x=p;for(n=0;n!==t;n++){var B,S,A,C=this.particles[n];for(m.set(0,0,0),w.set(0,0,0),u=(A=this.neighbors[n]).length,y=0;y!==u;y++){var E=A[y];C.position.vsub(E.position,b);var M=b.norm();B=-E.mass*(this.pressures[n]/(this.densities[n]*this.densities[n]+o)+this.pressures[y]/(this.densities[y]*this.densities[y]+o)),this.gradw(b,g),g.mult(B,g),m.vadd(g,m),E.velocity.vsub(C.velocity,x),x.mult(1/(1e-4+this.densities[n]*this.densities[y])*this.viscosity*E.mass,x),S=this.nablaw(M),x.mult(S,x),w.vadd(x,w)}w.mult(C.mass,w),m.mult(C.mass,m),C.force.vadd(w,C.force),C.force.vadd(m,C.force)}},o.prototype.w=function(t){var e=this.smoothingRadius;return 315/(64*Math.PI*Math.pow(e,9))*Math.pow(e*e-t*t,3)},o.prototype.gradw=function(t,e){var i=t.norm(),o=this.smoothingRadius;t.mult(945/(32*Math.PI*Math.pow(o,9))*Math.pow(o*o-i*i,2),e)},o.prototype.nablaw=function(t){var e=this.smoothingRadius;return 945/(32*Math.PI*Math.pow(e,9))*(e*e-t*t)*(7*t*t-3*e*e)}},{"../material/Material":26,"../math/Quaternion":29,"../math/Vec3":31,"../objects/Body":32,"../shapes/Particle":42,"../shapes/Shape":44}],36:[function(t,e){var i=t("../math/Vec3");function o(t,e,o){o=o||{},this.restLength="number"==typeof o.restLength?o.restLength:1,this.stiffness=o.stiffness||100,this.damping=o.damping||1,this.bodyA=t,this.bodyB=e,this.localAnchorA=new i,this.localAnchorB=new i,o.localAnchorA&&this.localAnchorA.copy(o.localAnchorA),o.localAnchorB&&this.localAnchorB.copy(o.localAnchorB),o.worldAnchorA&&this.setWorldAnchorA(o.worldAnchorA),o.worldAnchorB&&this.setWorldAnchorB(o.worldAnchorB)}e.exports=o,o.prototype.setWorldAnchorA=function(t){this.bodyA.pointToLocalFrame(t,this.localAnchorA)},o.prototype.setWorldAnchorB=function(t){this.bodyB.pointToLocalFrame(t,this.localAnchorB)},o.prototype.getWorldAnchorA=function(t){this.bodyA.pointToWorldFrame(this.localAnchorA,t)},o.prototype.getWorldAnchorB=function(t){this.bodyB.pointToWorldFrame(this.localAnchorB,t)};var n=new i,s=new i,r=new i,a=new i,l=new i,h=new i,p=new i,c=new i,u=new i,d=new i,y=new i;o.prototype.applyForce=function(){var t=this.stiffness,e=this.damping,i=this.restLength,o=this.bodyA,v=this.bodyB,f=n,m=s,w=r,g=a,b=y,x=l,B=h,S=p,A=c,C=u,E=d;this.getWorldAnchorA(x),this.getWorldAnchorB(B),x.vsub(o.position,S),B.vsub(v.position,A),B.vsub(x,f);var M=f.norm();m.copy(f),m.normalize(),v.velocity.vsub(o.velocity,w),v.angularVelocity.cross(A,b),w.vadd(b,w),o.angularVelocity.cross(S,b),w.vsub(b,w),m.mult(-t*(M-i)-e*w.dot(m),g),o.force.vsub(g,o.force),v.force.vadd(g,v.force),S.cross(g,C),A.cross(g,E),o.torque.vsub(C,o.torque),v.torque.vadd(E,v.torque)}},{"../math/Vec3":31}],37:[function(t,e){var i=t("../math/Vec3"),o=t("../math/Transform"),n=t("../collision/RaycastResult"),s=t("../utils/Utils");function r(t){t=s.defaults(t,{chassisConnectionPointLocal:new i,chassisConnectionPointWorld:new i,directionLocal:new i,directionWorld:new i,axleLocal:new i,axleWorld:new i,suspensionRestLength:1,suspensionMaxLength:2,radius:1,suspensionStiffness:100,dampingCompression:10,dampingRelaxation:10,frictionSlip:1e4,steering:0,rotation:0,deltaRotation:0,rollInfluence:.01,maxSuspensionForce:Number.MAX_VALUE,isFrontWheel:!0,clippedInvContactDotSuspension:1,suspensionRelativeVelocity:0,suspensionForce:0,skidInfo:0,suspensionLength:0,maxSuspensionTravel:1,useCustomSlidingRotationalSpeed:!1,customSlidingRotationalSpeed:-.1}),this.maxSuspensionTravel=t.maxSuspensionTravel,this.customSlidingRotationalSpeed=t.customSlidingRotationalSpeed,this.useCustomSlidingRotationalSpeed=t.useCustomSlidingRotationalSpeed,this.sliding=!1,this.chassisConnectionPointLocal=t.chassisConnectionPointLocal.clone(),this.chassisConnectionPointWorld=t.chassisConnectionPointWorld.clone(),this.directionLocal=t.directionLocal.clone(),this.directionWorld=t.directionWorld.clone(),this.axleLocal=t.axleLocal.clone(),this.axleWorld=t.axleWorld.clone(),this.suspensionRestLength=t.suspensionRestLength,this.suspensionMaxLength=t.suspensionMaxLength,this.radius=t.radius,this.suspensionStiffness=t.suspensionStiffness,this.dampingCompression=t.dampingCompression,this.dampingRelaxation=t.dampingRelaxation,this.frictionSlip=t.frictionSlip,this.steering=0,this.rotation=0,this.deltaRotation=0,this.rollInfluence=t.rollInfluence,this.maxSuspensionForce=t.maxSuspensionForce,this.engineForce=0,this.brake=0,this.isFrontWheel=t.isFrontWheel,this.clippedInvContactDotSuspension=1,this.suspensionRelativeVelocity=0,this.suspensionForce=0,this.skidInfo=0,this.suspensionLength=0,this.sideImpulse=0,this.forwardImpulse=0,this.raycastResult=new n,this.worldTransform=new o,this.isInContact=!1}e.exports=r;var a=new i,l=new i;a=new i,r.prototype.updateWheel=function(t){var e=this.raycastResult;if(this.isInContact){var i=e.hitNormalWorld.dot(e.directionWorld);e.hitPointWorld.vsub(t.position,l),t.getVelocityAtWorldPoint(l,a);var o=e.hitNormalWorld.dot(a);if(i>=-.1)this.suspensionRelativeVelocity=0,this.clippedInvContactDotSuspension=10;else{var n=-1/i;this.suspensionRelativeVelocity=o*n,this.clippedInvContactDotSuspension=n}}else e.suspensionLength=this.suspensionRestLength,this.suspensionRelativeVelocity=0,e.directionWorld.scale(-1,e.hitNormalWorld),this.clippedInvContactDotSuspension=1}},{"../collision/RaycastResult":11,"../math/Transform":30,"../math/Vec3":31,"../utils/Utils":54}],38:[function(t,e){e.exports=s;var i=t("./Shape"),o=t("../math/Vec3"),n=t("./ConvexPolyhedron");function s(t){i.call(this,{type:i.types.BOX}),this.halfExtents=t,this.convexPolyhedronRepresentation=null,this.updateConvexPolyhedronRepresentation(),this.updateBoundingSphereRadius()}s.prototype=new i,s.prototype.constructor=s,s.prototype.updateConvexPolyhedronRepresentation=function(){var t=this.halfExtents.x,e=this.halfExtents.y,i=this.halfExtents.z,s=o,r=[new s(-t,-e,-i),new s(t,-e,-i),new s(t,e,-i),new s(-t,e,-i),new s(-t,-e,i),new s(t,-e,i),new s(t,e,i),new s(-t,e,i)];new s(0,0,1),new s(0,1,0),new s(1,0,0);var a=new n(r,[[3,2,1,0],[4,5,6,7],[5,4,0,1],[2,3,7,6],[0,4,7,3],[1,2,6,5]]);this.convexPolyhedronRepresentation=a,a.material=this.material},s.prototype.calculateLocalInertia=function(t,e){return e=e||new o,s.calculateInertia(this.halfExtents,t,e),e},s.calculateInertia=function(t,e,i){var o=t;o.isZero()?(i.x=2/12*e,i.y=2/12*e,i.z=2/12*e):(i.x=1/12*e*(4*o.y*o.y+4*o.z*o.z),i.y=1/12*e*(4*o.x*o.x+4*o.z*o.z),i.z=1/12*e*(4*o.y*o.y+4*o.x*o.x))},s.prototype.getSideNormals=function(t,e){var i=t,o=this.halfExtents;if(i[0].set(o.x,0,0),i[1].set(0,o.y,0),i[2].set(0,0,o.z),i[3].set(-o.x,0,0),i[4].set(0,-o.y,0),i[5].set(0,0,-o.z),void 0!==e)for(var n=0;n!==i.length;n++)e.vmult(i[n],i[n]);return i},s.prototype.volume=function(){return 8*this.halfExtents.x*this.halfExtents.y*this.halfExtents.z},s.prototype.updateBoundingSphereRadius=function(){this.boundingSphereRadius=this.halfExtents.norm()};var r=new o;new o,s.prototype.forEachWorldCorner=function(t,e,i){for(var o=this.halfExtents,n=[[o.x,o.y,o.z],[-o.x,o.y,o.z],[-o.x,-o.y,o.z],[-o.x,-o.y,-o.z],[o.x,-o.y,-o.z],[o.x,o.y,-o.z],[-o.x,o.y,-o.z],[o.x,-o.y,o.z]],s=0;s<n.length;s++)r.set(n[s][0],n[s][1],n[s][2]),e.vmult(r,r),t.vadd(r,r),i(r.x,r.y,r.z)};var a=[new o,new o,new o,new o,new o,new o,new o,new o];s.prototype.calculateWorldAABB=function(t,e,i,o){var n=this.halfExtents;a[0].set(n.x,n.y,n.z),a[1].set(-n.x,n.y,n.z),a[2].set(-n.x,-n.y,n.z),a[3].set(-n.x,-n.y,-n.z),a[4].set(n.x,-n.y,-n.z),a[5].set(n.x,n.y,-n.z),a[6].set(-n.x,n.y,-n.z),a[7].set(n.x,-n.y,n.z);var s=a[0];e.vmult(s,s),t.vadd(s,s),o.copy(s),i.copy(s);for(var r=1;r<8;r++){s=a[r],e.vmult(s,s),t.vadd(s,s);var l=s.x,h=s.y,p=s.z;l>o.x&&(o.x=l),h>o.y&&(o.y=h),p>o.z&&(o.z=p),l<i.x&&(i.x=l),h<i.y&&(i.y=h),p<i.z&&(i.z=p)}}},{"../math/Vec3":31,"./ConvexPolyhedron":39,"./Shape":44}],39:[function(t,e){e.exports=s;var i=t("./Shape"),o=t("../math/Vec3");t("../math/Quaternion");var n=t("../math/Transform");function s(t,e,o){i.call(this,{type:i.types.CONVEXPOLYHEDRON}),this.vertices=t||[],this.worldVertices=[],this.worldVerticesNeedsUpdate=!0,this.faces=e||[],this.faceNormals=[],this.computeNormals(),this.worldFaceNormalsNeedsUpdate=!0,this.worldFaceNormals=[],this.uniqueEdges=[],this.uniqueAxes=o?o.slice():null,this.computeEdges(),this.updateBoundingSphereRadius()}s.prototype=new i,s.prototype.constructor=s;var r=new o;s.prototype.computeEdges=function(){var t=this.faces,e=this.vertices;e.length;var i=this.uniqueEdges;i.length=0;for(var o=r,n=0;n!==t.length;n++)for(var s=t[n],a=s.length,l=0;l!==a;l++){var h=(l+1)%a;e[s[l]].vsub(e[s[h]],o),o.normalize();for(var p=!1,c=0;c!==i.length;c++)if(i[c].almostEquals(o)||i[c].almostEquals(o)){p=!0;break}p||i.push(o.clone())}},s.prototype.computeNormals=function(){this.faceNormals.length=this.faces.length;for(var t=0;t<this.faces.length;t++){for(var e=0;e<this.faces[t].length;e++)if(!this.vertices[this.faces[t][e]])throw new Error("Vertex "+this.faces[t][e]+" not found!");var i=this.faceNormals[t]||new o;this.getFaceNormal(t,i),i.negate(i),this.faceNormals[t]=i}};var a=new o,l=new o;s.computeNormal=function(t,e,i,o){e.vsub(t,l),i.vsub(e,a),a.cross(l,o),o.isZero()||o.normalize()},s.prototype.getFaceNormal=function(t,e){var i=this.faces[t],o=this.vertices[i[0]],n=this.vertices[i[1]],r=this.vertices[i[2]];return s.computeNormal(o,n,r,e)};var h=new o;s.prototype.clipAgainstHull=function(t,e,i,n,s,r,a,l,p){for(var c=h,u=-1,d=-Number.MAX_VALUE,y=0;y<i.faces.length;y++){c.copy(i.faceNormals[y]),s.vmult(c,c);var v=c.dot(r);v>d&&(d=v,u=y)}for(var f=[],m=i.faces[u],w=m.length,g=0;g<w;g++){var b=i.vertices[m[g]],x=new o;x.copy(b),s.vmult(x,x),n.vadd(x,x),f.push(x)}u>=0&&this.clipFaceAgainstHull(r,t,e,f,a,l,p)};var p=new o,c=new o,u=new o,d=new o,y=new o,v=new o;s.prototype.findSeparatingAxis=function(t,e,i,o,n,s,r,a){var l=p,h=c,f=u,m=d,w=y,g=v,b=Number.MAX_VALUE,x=this;if(x.uniqueAxes)for(S=0;S!==x.uniqueAxes.length;S++){if(i.vmult(x.uniqueAxes[S],l),!1===(E=x.testSepAxis(l,t,e,i,o,n)))return!1;E<b&&(b=E,s.copy(l))}else for(var B=r?r.length:x.faces.length,S=0;S<B;S++){var A=r?r[S]:S;if(l.copy(x.faceNormals[A]),i.vmult(l,l),!1===(E=x.testSepAxis(l,t,e,i,o,n)))return!1;E<b&&(b=E,s.copy(l))}if(t.uniqueAxes)for(S=0;S!==t.uniqueAxes.length;S++){if(n.vmult(t.uniqueAxes[S],h),!1===(E=x.testSepAxis(h,t,e,i,o,n)))return!1;E<b&&(b=E,s.copy(h))}else{var C=a?a.length:t.faces.length;for(S=0;S<C;S++){var E;if(A=a?a[S]:S,h.copy(t.faceNormals[A]),n.vmult(h,h),!1===(E=x.testSepAxis(h,t,e,i,o,n)))return!1;E<b&&(b=E,s.copy(h))}}for(var M=0;M!==x.uniqueEdges.length;M++){i.vmult(x.uniqueEdges[M],m);for(var _=0;_!==t.uniqueEdges.length;_++)if(n.vmult(t.uniqueEdges[_],w),m.cross(w,g),!g.almostZero()){g.normalize();var z=x.testSepAxis(g,t,e,i,o,n);if(!1===z)return!1;z<b&&(b=z,s.copy(g))}}return o.vsub(e,f),f.dot(s)>0&&s.negate(s),!0};var f=[],m=[];s.prototype.testSepAxis=function(t,e,i,o,n,r){s.project(this,t,i,o,f),s.project(e,t,n,r,m);var a=f[0]-m[1],l=m[0]-f[1];return a<l?a:l};var w=new o,g=new o;s.prototype.calculateLocalInertia=function(t,e){this.computeLocalAABB(w,g);var i=g.x-w.x,o=g.y-w.y,n=g.z-w.z;e.x=1/12*t*(4*o*o+4*n*n),e.y=1/12*t*(4*i*i+4*n*n),e.z=1/12*t*(4*o*o+4*i*i)},s.prototype.getPlaneConstantOfFace=function(t){var e=this.faces[t],i=this.faceNormals[t],o=this.vertices[e[0]];return-i.dot(o)};var b=new o,x=new o,B=new o,S=new o,A=new o,C=new o,E=new o,M=new o;s.prototype.clipFaceAgainstHull=function(t,e,i,o,n,s,r){for(var a=b,l=x,h=B,p=S,c=A,u=C,d=E,y=M,v=this,f=o,m=[],w=-1,g=Number.MAX_VALUE,_=0;_<v.faces.length;_++){a.copy(v.faceNormals[_]),i.vmult(a,a);var z=a.dot(t);z<g&&(g=z,w=_)}if(!(w<0)){var F=v.faces[w];F.connectedFaces=[];for(var R=0;R<v.faces.length;R++)for(var T=0;T<v.faces[R].length;T++)-1!==F.indexOf(v.faces[R][T])&&R!==w&&-1===F.connectedFaces.indexOf(R)&&F.connectedFaces.push(R);f.length;for(var P=F.length,q=0;q<P;q++){var I=v.vertices[F[q]],V=v.vertices[F[(q+1)%P]];I.vsub(V,l),h.copy(l),i.vmult(h,h),e.vadd(h,h),p.copy(this.faceNormals[w]),i.vmult(p,p),e.vadd(p,p),h.cross(p,c),c.negate(c),u.copy(I),i.vmult(u,u),e.vadd(u,u),u.dot(c);var L=F.connectedFaces[q];d.copy(this.faceNormals[L]);var N=this.getPlaneConstantOfFace(L);y.copy(d),i.vmult(y,y);var W=N-y.dot(e);for(this.clipFaceAgainstPlane(f,m,y,W);f.length;)f.shift();for(;m.length;)f.push(m.shift())}for(d.copy(this.faceNormals[w]),N=this.getPlaneConstantOfFace(w),y.copy(d),i.vmult(y,y),W=N-y.dot(e),R=0;R<f.length;R++){var j=y.dot(f[R])+W;if(j<=n&&(j=n),j<=s){var k=f[R];if(j<=0){var O={point:k,normal:y,depth:j};r.push(O)}}}}},s.prototype.clipFaceAgainstPlane=function(t,e,i,n){var s,r,a=t.length;if(a<2)return e;var l=t[t.length-1],h=t[0];s=i.dot(l)+n;for(var p=0;p<a;p++){if(h=t[p],r=i.dot(h)+n,s<0)if(r<0)(c=new o).copy(h),e.push(c);else{var c=new o;l.lerp(h,s/(s-r),c),e.push(c)}else r<0&&(c=new o,l.lerp(h,s/(s-r),c),e.push(c),e.push(h));l=h,s=r}return e},s.prototype.computeWorldVertices=function(t,e){for(var i=this.vertices.length;this.worldVertices.length<i;)this.worldVertices.push(new o);for(var n=this.vertices,s=this.worldVertices,r=0;r!==i;r++)e.vmult(n[r],s[r]),t.vadd(s[r],s[r]);this.worldVerticesNeedsUpdate=!1},new o,s.prototype.computeLocalAABB=function(t,e){var i=this.vertices.length,o=this.vertices;t.set(Number.MAX_VALUE,Number.MAX_VALUE,Number.MAX_VALUE),e.set(-Number.MAX_VALUE,-Number.MAX_VALUE,-Number.MAX_VALUE);for(var n=0;n<i;n++){var s=o[n];s.x<t.x?t.x=s.x:s.x>e.x&&(e.x=s.x),s.y<t.y?t.y=s.y:s.y>e.y&&(e.y=s.y),s.z<t.z?t.z=s.z:s.z>e.z&&(e.z=s.z)}},s.prototype.computeWorldFaceNormals=function(t){for(var e=this.faceNormals.length;this.worldFaceNormals.length<e;)this.worldFaceNormals.push(new o);for(var i=this.faceNormals,n=this.worldFaceNormals,s=0;s!==e;s++)t.vmult(i[s],n[s]);this.worldFaceNormalsNeedsUpdate=!1},s.prototype.updateBoundingSphereRadius=function(){for(var t=0,e=this.vertices,i=0,o=e.length;i!==o;i++){var n=e[i].norm2();n>t&&(t=n)}this.boundingSphereRadius=Math.sqrt(t)};var _=new o;s.prototype.calculateWorldAABB=function(t,e,i,o){for(var n,s,r,a,l,h,p=this.vertices.length,c=this.vertices,u=0;u<p;u++){_.copy(c[u]),e.vmult(_,_),t.vadd(_,_);var d=_;(d.x<n||void 0===n)&&(n=d.x),(d.x>a||void 0===a)&&(a=d.x),(d.y<s||void 0===s)&&(s=d.y),(d.y>l||void 0===l)&&(l=d.y),(d.z<r||void 0===r)&&(r=d.z),(d.z>h||void 0===h)&&(h=d.z)}i.set(n,s,r),o.set(a,l,h)},s.prototype.volume=function(){return 4*Math.PI*this.boundingSphereRadius/3},s.prototype.getAveragePointLocal=function(t){t=t||new o;for(var e=this.vertices.length,i=this.vertices,n=0;n<e;n++)t.vadd(i[n],t);return t.mult(1/e,t),t},s.prototype.transformAllPoints=function(t,e){var i=this.vertices.length,o=this.vertices;if(e){for(var n=0;n<i;n++){var s=o[n];e.vmult(s,s)}for(n=0;n<this.faceNormals.length;n++)s=this.faceNormals[n],e.vmult(s,s)}if(t)for(n=0;n<i;n++)(s=o[n]).vadd(t,s)};var z=new o,F=new o,R=new o;s.prototype.pointIsInside=function(t){var e=this.vertices.length,i=this.vertices,o=this.faces,n=this.faceNormals,s=this.faces.length,r=z;this.getAveragePointLocal(r);for(var a=0;a<s;a++){this.faces[a].length,e=n[a];var l=i[o[a][0]],h=F;t.vsub(l,h);var p=e.dot(h),c=R;r.vsub(l,c);var u=e.dot(c);if(p<0&&u>0||p>0&&u<0)return!1}return-1},new o;var T=new o,P=new o;s.project=function(t,e,i,o,s){var r=t.vertices.length,a=T,l=0,h=0,p=P,c=t.vertices;p.setZero(),n.vectorToLocalFrame(i,o,e,a),n.pointToLocalFrame(i,o,p,p);var u=p.dot(a);h=l=c[0].dot(a);for(var d=1;d<r;d++){var y=c[d].dot(a);y>l&&(l=y),y<h&&(h=y)}if((h-=u)>(l-=u)){var v=h;h=l,l=v}s[0]=l,s[1]=h}},{"../math/Quaternion":29,"../math/Transform":30,"../math/Vec3":31,"./Shape":44}],40:[function(t,e){e.exports=n,t("./Shape");var i=t("../math/Vec3");t("../math/Quaternion");var o=t("./ConvexPolyhedron");function n(t,e,n,s,r){if(r){for(var a=s,l=Math.cos,h=Math.sin,p=n/2,c=[],u=[],d=[0],y=[1],v=[],f=2*Math.PI/a,m=0;m<a;m++)c.push(new i(t*l(f*m),p,t*h(f*m))),c.push(new i(t*l(f*m),-p,t*h(f*m))),m<a-1?(u.push([2*m+2,2*m+3,2*m+1,2*m]),d.push(2*m+2),y.push(2*m+3)):u.push([0,1,2*m+1,2*m]),(a%2==1||m<a/2)&&v.push(new i(l(f*(m+.5)),0,h(f*(m+.5))));u.push(y);var w=[];for(m=0;m<d.length;m++)w.push(d[d.length-m-1]);return u.push(w),v.push(new i(0,1,0)),void o.call(this,c,u,v)}a=s;var g=[],b=(v=[],[]),x=[],B=[];for(l=Math.cos,h=Math.sin,g.push(new i(e*l(0),e*h(0),.5*-n)),x.push(0),g.push(new i(t*l(0),t*h(0),.5*n)),B.push(1),m=0;m<a;m++){f=2*Math.PI/a*(m+1);var S=2*Math.PI/a*(m+.5);m<a-1?(g.push(new i(e*l(f),e*h(f),.5*-n)),x.push(2*m+2),g.push(new i(t*l(f),t*h(f),.5*n)),B.push(2*m+3),b.push([2*m+2,2*m+3,2*m+1,2*m])):b.push([0,1,2*m+1,2*m]),(a%2==1||m<a/2)&&v.push(new i(l(S),h(S),0))}for(b.push(B),v.push(new i(0,0,1)),w=[],m=0;m<x.length;m++)w.push(x[x.length-m-1]);b.push(w),o.call(this,g,b,v)}n.prototype=new o},{"../math/Quaternion":29,"../math/Vec3":31,"./ConvexPolyhedron":39,"./Shape":44}],41:[function(t,e){var i=t("./Shape"),o=t("./ConvexPolyhedron"),n=t("../math/Vec3"),s=t("../utils/Utils");function r(t,e){e=s.defaults(e,{maxValue:null,minValue:null,elementSize:1}),this.data=t,this.maxValue=e.maxValue,this.minValue=e.minValue,this.elementSize=e.elementSize,null===e.minValue&&this.updateMinValue(),null===e.maxValue&&this.updateMaxValue(),this.cacheEnabled=!0,i.call(this,{type:i.types.HEIGHTFIELD}),this.pillarConvex=new o,this.pillarOffset=new n,this.updateBoundingSphereRadius(),this._cachedPillars={}}e.exports=r,r.prototype=new i,r.prototype.update=function(){this._cachedPillars={}},r.prototype.updateMinValue=function(){for(var t=this.data,e=t[0][0],i=0;i!==t.length;i++)for(var o=0;o!==t[i].length;o++){var n=t[i][o];n<e&&(e=n)}this.minValue=e},r.prototype.updateMaxValue=function(){for(var t=this.data,e=t[0][0],i=0;i!==t.length;i++)for(var o=0;o!==t[i].length;o++){var n=t[i][o];n>e&&(e=n)}this.maxValue=e},r.prototype.setHeightValueAtIndex=function(t,e,i){this.data[t][e]=i,this.clearCachedConvexTrianglePillar(t,e,!1),t>0&&(this.clearCachedConvexTrianglePillar(t-1,e,!0),this.clearCachedConvexTrianglePillar(t-1,e,!1)),e>0&&(this.clearCachedConvexTrianglePillar(t,e-1,!0),this.clearCachedConvexTrianglePillar(t,e-1,!1)),e>0&&t>0&&this.clearCachedConvexTrianglePillar(t-1,e-1,!0)},r.prototype.getRectMinMax=function(t,e,i,o,n){n=n||[];for(var s=this.data,r=this.minValue,a=t;a<=i;a++)for(var l=e;l<=o;l++){var h=s[a][l];h>r&&(r=h)}n[0]=this.minValue,n[1]=r},r.prototype.getIndexOfPosition=function(t,e,i,o){var n=this.elementSize,s=this.data,r=Math.floor(t/n),a=Math.floor(e/n);return i[0]=r,i[1]=a,o&&(r<0&&(r=0),a<0&&(a=0),r>=s.length-1&&(r=s.length-1),a>=s[0].length-1&&(a=s[0].length-1)),!(r<0||a<0||r>=s.length-1||a>=s[0].length-1)};var a=[],l=new n,h=new n,p=new n,c=new n;r.prototype.getTriangleAt=function(t,e,i,o,n,s){var r=a;this.getIndexOfPosition(t,e,r,i);var l=r[0],h=r[1],p=this.data;i&&(l=Math.min(p.length-2,Math.max(0,l)),h=Math.min(p[0].length-2,Math.max(0,h)));var c=this.elementSize,u=Math.pow(t/c-l,2)+Math.pow(e/c-h,2)>Math.pow(t/c-(l+1),2)+Math.pow(e/c-(h+1),2);return this.getTriangle(l,h,u,o,n,s),u};var u=new n,d=new n,y=new n,v=new n,f=new n;function m(t,e,i,o,n,s,r,a,l){l.x=((s-a)*(t-r)+(r-n)*(e-a))/((s-a)*(i-r)+(r-n)*(o-a)),l.y=((a-o)*(t-r)+(i-r)*(e-a))/((s-a)*(i-r)+(r-n)*(o-a)),l.z=1-l.x-l.y}r.prototype.getNormalAt=function(t,e,i,o){var n=u,s=d,r=y,a=v,l=f;this.getTriangleAt(t,e,i,n,s,r),s.vsub(n,a),r.vsub(n,l),a.cross(l,o),o.normalize()},r.prototype.getAabbAtIndex=function(t,e,i){var o=this.data,n=this.elementSize;i.lowerBound.set(t*n,e*n,o[t][e]),i.upperBound.set((t+1)*n,(e+1)*n,o[t+1][e+1])},r.prototype.getHeightAt=function(t,e,i){var o=this.data,n=h,s=p,r=c,u=a;this.getIndexOfPosition(t,e,u,i);var d=u[0],y=u[1];i&&(d=Math.min(o.length-2,Math.max(0,d)),y=Math.min(o[0].length-2,Math.max(0,y)));var v=this.getTriangleAt(t,e,i,n,s,r);m(t,e,n.x,n.y,s.x,s.y,r.x,r.y,l);var f=l;return v?o[d+1][y+1]*f.x+o[d][y+1]*f.y+o[d+1][y]*f.z:o[d][y]*f.x+o[d+1][y]*f.y+o[d][y+1]*f.z},r.prototype.getCacheConvexTrianglePillarKey=function(t,e,i){return t+"_"+e+"_"+(i?1:0)},r.prototype.getCachedConvexTrianglePillar=function(t,e,i){return this._cachedPillars[this.getCacheConvexTrianglePillarKey(t,e,i)]},r.prototype.setCachedConvexTrianglePillar=function(t,e,i,o,n){this._cachedPillars[this.getCacheConvexTrianglePillarKey(t,e,i)]={convex:o,offset:n}},r.prototype.clearCachedConvexTrianglePillar=function(t,e,i){delete this._cachedPillars[this.getCacheConvexTrianglePillarKey(t,e,i)]},r.prototype.getTriangle=function(t,e,i,o,n,s){var r=this.data,a=this.elementSize;i?(o.set((t+1)*a,(e+1)*a,r[t+1][e+1]),n.set(t*a,(e+1)*a,r[t][e+1]),s.set((t+1)*a,e*a,r[t+1][e])):(o.set(t*a,e*a,r[t][e]),n.set((t+1)*a,e*a,r[t+1][e]),s.set(t*a,(e+1)*a,r[t][e+1]))},r.prototype.getConvexTrianglePillar=function(t,e,i){var s=this.pillarConvex,r=this.pillarOffset;if(this.cacheEnabled){if(a=this.getCachedConvexTrianglePillar(t,e,i))return this.pillarConvex=a.convex,void(this.pillarOffset=a.offset);s=new o,r=new n,this.pillarConvex=s,this.pillarOffset=r}var a=this.data,l=this.elementSize,h=s.faces;s.vertices.length=6;for(var p=0;p<6;p++)s.vertices[p]||(s.vertices[p]=new n);for(h.length=5,p=0;p<5;p++)h[p]||(h[p]=[]);var c=s.vertices,u=(Math.min(a[t][e],a[t+1][e],a[t][e+1],a[t+1][e+1])-this.minValue)/2+this.minValue;i?(r.set((t+.75)*l,(e+.75)*l,u),c[0].set(.25*l,.25*l,a[t+1][e+1]-u),c[1].set(-.75*l,.25*l,a[t][e+1]-u),c[2].set(.25*l,-.75*l,a[t+1][e]-u),c[3].set(.25*l,.25*l,-u-1),c[4].set(-.75*l,.25*l,-u-1),c[5].set(.25*l,-.75*l,-u-1),h[0][0]=0,h[0][1]=1,h[0][2]=2,h[1][0]=5,h[1][1]=4,h[1][2]=3,h[2][0]=2,h[2][1]=5,h[2][2]=3,h[2][3]=0,h[3][0]=3,h[3][1]=4,h[3][2]=1,h[3][3]=0,h[4][0]=1,h[4][1]=4,h[4][2]=5,h[4][3]=2):(r.set((t+.25)*l,(e+.25)*l,u),c[0].set(-.25*l,-.25*l,a[t][e]-u),c[1].set(.75*l,-.25*l,a[t+1][e]-u),c[2].set(-.25*l,.75*l,a[t][e+1]-u),c[3].set(-.25*l,-.25*l,-u-1),c[4].set(.75*l,-.25*l,-u-1),c[5].set(-.25*l,.75*l,-u-1),h[0][0]=0,h[0][1]=1,h[0][2]=2,h[1][0]=5,h[1][1]=4,h[1][2]=3,h[2][0]=0,h[2][1]=2,h[2][2]=5,h[2][3]=3,h[3][0]=1,h[3][1]=0,h[3][2]=3,h[3][3]=4,h[4][0]=4,h[4][1]=5,h[4][2]=2,h[4][3]=1),s.computeNormals(),s.computeEdges(),s.updateBoundingSphereRadius(),this.setCachedConvexTrianglePillar(t,e,i,s,r)},r.prototype.calculateLocalInertia=function(t,e){return(e=e||new n).set(0,0,0),e},r.prototype.volume=function(){return Number.MAX_VALUE},r.prototype.calculateWorldAABB=function(t,e,i,o){i.set(-Number.MAX_VALUE,-Number.MAX_VALUE,-Number.MAX_VALUE),o.set(Number.MAX_VALUE,Number.MAX_VALUE,Number.MAX_VALUE)},r.prototype.updateBoundingSphereRadius=function(){var t=this.data,e=this.elementSize;this.boundingSphereRadius=new n(t.length*e,t[0].length*e,Math.max(Math.abs(this.maxValue),Math.abs(this.minValue))).norm()},r.prototype.setHeightsFromImage=function(t,e){var i=document.createElement("canvas");i.width=t.width,i.height=t.height;var o=i.getContext("2d");o.drawImage(t,0,0);var n=o.getImageData(0,0,t.width,t.height),s=this.data;s.length=0,this.elementSize=Math.abs(e.x)/n.width;for(var r=0;r<n.height;r++){for(var a=[],l=0;l<n.width;l++){var h=(n.data[4*(r*n.height+l)]+n.data[4*(r*n.height+l)+1]+n.data[4*(r*n.height+l)+2])/4/255*e.z;e.x<0?a.push(h):a.unshift(h)}e.y<0?s.unshift(a):s.push(a)}this.updateMaxValue(),this.updateMinValue(),this.update()}},{"../math/Vec3":31,"../utils/Utils":54,"./ConvexPolyhedron":39,"./Shape":44}],42:[function(t,e){e.exports=n;var i=t("./Shape"),o=t("../math/Vec3");function n(){i.call(this,{type:i.types.PARTICLE})}n.prototype=new i,n.prototype.constructor=n,n.prototype.calculateLocalInertia=function(t,e){return(e=e||new o).set(0,0,0),e},n.prototype.volume=function(){return 0},n.prototype.updateBoundingSphereRadius=function(){this.boundingSphereRadius=0},n.prototype.calculateWorldAABB=function(t,e,i,o){i.copy(t),o.copy(t)}},{"../math/Vec3":31,"./Shape":44}],43:[function(t,e){e.exports=n;var i=t("./Shape"),o=t("../math/Vec3");function n(){i.call(this,{type:i.types.PLANE}),this.worldNormal=new o,this.worldNormalNeedsUpdate=!0,this.boundingSphereRadius=Number.MAX_VALUE}n.prototype=new i,n.prototype.constructor=n,n.prototype.computeWorldNormal=function(t){var e=this.worldNormal;e.set(0,0,1),t.vmult(e,e),this.worldNormalNeedsUpdate=!1},n.prototype.calculateLocalInertia=function(t,e){return e||new o},n.prototype.volume=function(){return Number.MAX_VALUE};var s=new o;n.prototype.calculateWorldAABB=function(t,e,i,o){s.set(0,0,1),e.vmult(s,s);var n=Number.MAX_VALUE;i.set(-n,-n,-n),o.set(n,n,n),1===s.x&&(o.x=t.x),1===s.y&&(o.y=t.y),1===s.z&&(o.z=t.z),-1===s.x&&(i.x=t.x),-1===s.y&&(i.y=t.y),-1===s.z&&(i.z=t.z)},n.prototype.updateBoundingSphereRadius=function(){this.boundingSphereRadius=Number.MAX_VALUE}},{"../math/Vec3":31,"./Shape":44}],44:[function(t,e){e.exports=o;var i=t("../utils/EventTarget"),o=t("./Shape");function o(t){t=t||{},i.apply(this),this.id=o.idCounter++,this.type=t.type||0,this.boundingSphereRadius=0,this.collisionResponse=!t.collisionResponse||t.collisionResponse,this.collisionFilterGroup=void 0!==t.collisionFilterGroup?t.collisionFilterGroup:1,this.collisionFilterMask=void 0!==t.collisionFilterMask?t.collisionFilterMask:-1,this.material=t.material?t.material:null,this.body=null}t("../math/Vec3"),t("../math/Quaternion"),t("../material/Material"),o.prototype=new i,o.prototype.constructor=o,o.prototype.updateBoundingSphereRadius=function(){throw"computeBoundingSphereRadius() not implemented for shape type "+this.type},o.prototype.volume=function(){throw"volume() not implemented for shape type "+this.type},o.prototype.calculateLocalInertia=function(){throw"calculateLocalInertia() not implemented for shape type "+this.type},o.idCounter=0,o.types={SPHERE:1,PLANE:2,BOX:4,COMPOUND:8,CONVEXPOLYHEDRON:16,HEIGHTFIELD:32,PARTICLE:64,CYLINDER:128,TRIMESH:256}},{"../material/Material":26,"../math/Quaternion":29,"../math/Vec3":31,"../utils/EventTarget":50,"./Shape":44}],45:[function(t,e){e.exports=n;var i=t("./Shape"),o=t("../math/Vec3");function n(t){if(i.call(this,{type:i.types.SPHERE}),this.radius=void 0!==t?t:1,this.radius<0)throw new Error("The sphere radius cannot be negative.");this.updateBoundingSphereRadius()}n.prototype=new i,n.prototype.constructor=n,n.prototype.calculateLocalInertia=function(t,e){e=e||new o;var i=2*t*this.radius*this.radius/5;return e.x=i,e.y=i,e.z=i,e},n.prototype.volume=function(){return 4*Math.PI*this.radius/3},n.prototype.updateBoundingSphereRadius=function(){this.boundingSphereRadius=this.radius},n.prototype.calculateWorldAABB=function(t,e,i,o){for(var n=this.radius,s=["x","y","z"],r=0;r<s.length;r++){var a=s[r];i[a]=t[a]-n,o[a]=t[a]+n}}},{"../math/Vec3":31,"./Shape":44}],46:[function(t,e){e.exports=a;var i=t("./Shape"),o=t("../math/Vec3");t("../math/Quaternion");var n=t("../math/Transform"),s=t("../collision/AABB"),r=t("../utils/Octree");function a(t,e){i.call(this,{type:i.types.TRIMESH}),this.vertices=new Float32Array(t),this.indices=new Int16Array(e),this.normals=new Float32Array(e.length),this.aabb=new s,this.edges=null,this.scale=new o(1,1,1),this.tree=new r,this.updateEdges(),this.updateNormals(),this.updateAABB(),this.updateBoundingSphereRadius(),this.updateTree()}a.prototype=new i,a.prototype.constructor=a;var l=new o;a.prototype.updateTree=function(){var t=this.tree;t.reset(),t.aabb.copy(this.aabb);var e=this.scale;t.aabb.lowerBound.x*=1/e.x,t.aabb.lowerBound.y*=1/e.y,t.aabb.lowerBound.z*=1/e.z,t.aabb.upperBound.x*=1/e.x,t.aabb.upperBound.y*=1/e.y,t.aabb.upperBound.z*=1/e.z;for(var i=new s,n=new o,r=new o,a=new o,l=[n,r,a],h=0;h<this.indices.length/3;h++){var p=3*h;this._getUnscaledVertex(this.indices[p],n),this._getUnscaledVertex(this.indices[p+1],r),this._getUnscaledVertex(this.indices[p+2],a),i.setFromPoints(l),t.insert(i,h)}t.removeEmptyNodes()};var h=new s;a.prototype.getTrianglesInAABB=function(t,e){h.copy(t);var i=this.scale,o=i.x,n=i.y,s=i.z,r=h.lowerBound,a=h.upperBound;return r.x/=o,r.y/=n,r.z/=s,a.x/=o,a.y/=n,a.z/=s,this.tree.aabbQuery(h,e)},a.prototype.setScale=function(t){var e=this.scale.x===this.scale.y===this.scale.z,i=t.x===t.y===t.z;e&&i||this.updateNormals(),this.scale.copy(t),this.updateAABB(),this.updateBoundingSphereRadius()},a.prototype.updateNormals=function(){for(var t=l,e=this.normals,i=0;i<this.indices.length/3;i++){var o=3*i,n=this.indices[o],s=this.indices[o+1],r=this.indices[o+2];this.getVertex(n,y),this.getVertex(s,v),this.getVertex(r,f),a.computeNormal(v,y,f,t),e[o]=t.x,e[o+1]=t.y,e[o+2]=t.z}},a.prototype.updateEdges=function(){for(var t={},e=function(){t[n<s?n+"_"+s:s+"_"+n]=!0},i=0;i<this.indices.length/3;i++){var o=3*i,n=this.indices[o],s=this.indices[o+1];this.indices[o+2],e(),e(),e()}var r=Object.keys(t);for(this.edges=new Int16Array(2*r.length),i=0;i<r.length;i++){var a=r[i].split("_");this.edges[2*i]=parseInt(a[0],10),this.edges[2*i+1]=parseInt(a[1],10)}},a.prototype.getEdgeVertex=function(t,e,i){var o=this.edges[2*t+(e?1:0)];this.getVertex(o,i)};var p=new o,c=new o;a.prototype.getEdgeVector=function(t,e){var i=p,o=c;this.getEdgeVertex(t,0,i),this.getEdgeVertex(t,1,o),o.vsub(i,e)};var u=new o,d=new o;a.computeNormal=function(t,e,i,o){e.vsub(t,d),i.vsub(e,u),u.cross(d,o),o.isZero()||o.normalize()};var y=new o,v=new o,f=new o;a.prototype.getVertex=function(t,e){var i=this.scale;return this._getUnscaledVertex(t,e),e.x*=i.x,e.y*=i.y,e.z*=i.z,e},a.prototype._getUnscaledVertex=function(t,e){var i=3*t,o=this.vertices;return e.set(o[i],o[i+1],o[i+2])},a.prototype.getWorldVertex=function(t,e,i,o){return this.getVertex(t,o),n.pointToWorldFrame(e,i,o,o),o},a.prototype.getTriangleVertices=function(t,e,i,o){var n=3*t;this.getVertex(this.indices[n],e),this.getVertex(this.indices[n+1],i),this.getVertex(this.indices[n+2],o)},a.prototype.getNormal=function(t,e){var i=3*t;return e.set(this.normals[i],this.normals[i+1],this.normals[i+2])};var m=new s;a.prototype.calculateLocalInertia=function(t,e){this.computeLocalAABB(m);var i=m.upperBound.x-m.lowerBound.x,o=m.upperBound.y-m.lowerBound.y,n=m.upperBound.z-m.lowerBound.z;return e.set(1/12*t*(4*o*o+4*n*n),1/12*t*(4*i*i+4*n*n),1/12*t*(4*o*o+4*i*i))};var w=new o;a.prototype.computeLocalAABB=function(t){var e=t.lowerBound,i=t.upperBound,o=this.vertices.length/3,n=w;if(0!==o){this.getVertex(0,n),e.copy(n),i.copy(n);for(var s=0;s!==o;s++)this.getVertex(s,n),n.x<e.x?e.x=n.x:n.x>i.x&&(i.x=n.x),n.y<e.y?e.y=n.y:n.y>i.y&&(i.y=n.y),n.z<e.z?e.z=n.z:n.z>i.z&&(i.z=n.z)}},a.prototype.updateAABB=function(){this.computeLocalAABB(this.aabb)},a.prototype.updateBoundingSphereRadius=function(){for(var t=0,e=this.vertices,i=new o,n=0,s=e.length/3;n!==s;n++){this.getVertex(n,i);var r=i.norm2();r>t&&(t=r)}this.boundingSphereRadius=Math.sqrt(t)},new o;var g=new n,b=new s;a.prototype.calculateWorldAABB=function(t,e,i,o){var n=g,s=b;n.position=t,n.quaternion=e,this.aabb.toWorldFrame(n,s),i.copy(s.lowerBound),o.copy(s.upperBound)},a.prototype.volume=function(){return 4*Math.PI*this.boundingSphereRadius/3},a.createTorus=function(t,e,i,o,n){t=t||1,e=e||.5,i=i||8,o=o||6,n=n||2*Math.PI;for(var s=[],r=[],l=0;l<=i;l++)for(var h=0;h<=o;h++){var p=h/o*n,c=l/i*Math.PI*2,u=(t+e*Math.cos(c))*Math.cos(p),d=(t+e*Math.cos(c))*Math.sin(p),y=e*Math.sin(c);s.push(u,d,y)}for(l=1;l<=i;l++)for(h=1;h<=o;h++){var v=(o+1)*l+h-1,f=(o+1)*(l-1)+h-1,m=(o+1)*(l-1)+h,w=(o+1)*l+h;r.push(v,f,w),r.push(f,m,w)}return new a(s,r)}},{"../collision/AABB":3,"../math/Quaternion":29,"../math/Transform":30,"../math/Vec3":31,"../utils/Octree":51,"./Shape":44}],47:[function(t,e){e.exports=o,t("../math/Vec3"),t("../math/Quaternion");var i=t("./Solver");function o(){i.call(this),this.iterations=10,this.tolerance=1e-7}o.prototype=new i;var n=[],s=[],r=[];o.prototype.solve=function(t,e){var i,o,a,l,h,p=0,c=this.iterations,u=this.tolerance*this.tolerance,d=this.equations,y=d.length,v=e.bodies,f=v.length,m=t;if(0!==y)for(var w=0;w!==f;w++)v[w].updateSolveMassProperties();var g=s,b=r,x=n;for(g.length=y,b.length=y,x.length=y,w=0;w!==y;w++){var B=d[w];x[w]=0,b[w]=B.computeB(m),g[w]=1/B.computeC()}if(0!==y){for(w=0;w!==f;w++){var S=(E=v[w]).vlambda,A=E.wlambda;S.set(0,0,0),A.set(0,0,0)}for(p=0;p!==c;p++){l=0;for(var C=0;C!==y;C++)B=d[C],i=b[C],o=g[C],(h=x[C])+(a=o*(i-B.computeGWlambda()-B.eps*h))<B.minForce?a=B.minForce-h:h+a>B.maxForce&&(a=B.maxForce-h),x[C]+=a,l+=a>0?a:-a,B.addToWlambda(a);if(l*l<u)break}for(w=0;w!==f;w++){var E,M=(E=v[w]).velocity,_=E.angularVelocity;E.vlambda.vmul(E.linearFactor,E.vlambda),M.vadd(E.vlambda,M),E.wlambda.vmul(E.angularFactor,E.wlambda),_.vadd(E.wlambda,_)}for(var z=d.length,F=1/m;z--;)d[z].multiplier=x[z]*F}return p}},{"../math/Quaternion":29,"../math/Vec3":31,"./Solver":48}],48:[function(t,e){function i(){this.equations=[]}e.exports=i,i.prototype.solve=function(){return 0},i.prototype.addEquation=function(t){t.enabled&&this.equations.push(t)},i.prototype.removeEquation=function(t){var e=this.equations,i=e.indexOf(t);-1!==i&&e.splice(i,1)},i.prototype.removeAllEquations=function(){this.equations.length=0}},{}],49:[function(t,e){e.exports=n,t("../math/Vec3"),t("../math/Quaternion");var i=t("./Solver"),o=t("../objects/Body");function n(t){for(i.call(this),this.iterations=10,this.tolerance=1e-7,this.subsolver=t,this.nodes=[],this.nodePool=[];this.nodePool.length<128;)this.nodePool.push(this.createNode())}n.prototype=new i;var s=[],r=[],a={bodies:[]},l=o.STATIC;function h(t){for(var e=t.length,i=0;i!==e;i++){var o=t[i];if(!(o.visited||o.body.type&l))return o}return!1}var p=[];function c(t,e,i,o){for(p.push(t),t.visited=!0,e(t,i,o);p.length;)for(var n,s=p.pop();n=h(s.children);)n.visited=!0,e(n,i,o),p.push(n)}function u(t,e,i){e.push(t.body);for(var o=t.eqs.length,n=0;n!==o;n++){var s=t.eqs[n];-1===i.indexOf(s)&&i.push(s)}}function d(t,e){return e.id-t.id}n.prototype.createNode=function(){return{body:null,children:[],eqs:[],visited:!1}},n.prototype.solve=function(t,e){for(var i=s,o=this.nodePool,n=e.bodies,l=this.equations,p=l.length,y=n.length,v=this.subsolver;o.length<y;)o.push(this.createNode());i.length=y;for(var f=0;f<y;f++)i[f]=o[f];for(f=0;f!==y;f++){var m=i[f];m.body=n[f],m.children.length=0,m.eqs.length=0,m.visited=!1}for(var w=0;w!==p;w++){var g=l[w],b=(f=n.indexOf(g.bi),n.indexOf(g.bj)),x=i[f],B=i[b];x.children.push(B),x.eqs.push(g),B.children.push(x),B.eqs.push(g)}var S,A=0,C=r;v.tolerance=this.tolerance,v.iterations=this.iterations;for(var E=a;S=h(i);){C.length=0,E.bodies.length=0,c(S,u,E.bodies,C);var M=C.length;for(C=C.sort(d),f=0;f!==M;f++)v.addEquation(C[f]);v.solve(t,E),v.removeAllEquations(),A++}return A}},{"../math/Quaternion":29,"../math/Vec3":31,"../objects/Body":32,"./Solver":48}],50:[function(t,e){var i=function(){};e.exports=i,i.prototype={constructor:i,addEventListener:function(t,e){void 0===this._listeners&&(this._listeners={});var i=this._listeners;return void 0===i[t]&&(i[t]=[]),-1===i[t].indexOf(e)&&i[t].push(e),this},hasEventListener:function(t,e){if(void 0===this._listeners)return!1;var i=this._listeners;return void 0!==i[t]&&-1!==i[t].indexOf(e)},hasAnyEventListener:function(t){return void 0!==this._listeners&&void 0!==this._listeners[t]},removeEventListener:function(t,e){if(void 0===this._listeners)return this;var i=this._listeners;if(void 0===i[t])return this;var o=i[t].indexOf(e);return-1!==o&&i[t].splice(o,1),this},dispatchEvent:function(t){if(void 0===this._listeners)return this;var e=this._listeners[t.type];if(void 0!==e){t.target=this;for(var i=0,o=e.length;i<o;i++)e[i].call(this,t)}return this}}},{}],51:[function(t,e){var i=t("../collision/AABB"),o=t("../math/Vec3");function n(t){t=t||{},this.root=t.root||null,this.aabb=t.aabb?t.aabb.clone():new i,this.data=[],this.children=[]}function s(t,e){(e=e||{}).root=null,e.aabb=t,n.call(this,e),this.maxDepth=void 0!==e.maxDepth?e.maxDepth:8}e.exports=s,s.prototype=new n,n.prototype.reset=function(){this.children.length=this.data.length=0},n.prototype.insert=function(t,e,i){var o=this.data;if(i=i||0,!this.aabb.contains(t))return!1;var n=this.children;if(i<(this.maxDepth||this.root.maxDepth)){var s=!1;n.length||(this.subdivide(),s=!0);for(var r=0;8!==r;r++)if(n[r].insert(t,e,i+1))return!0;s&&(n.length=0)}return o.push(e),!0};var r=new o;n.prototype.subdivide=function(){var t=this.aabb,e=t.lowerBound,s=t.upperBound,a=this.children;a.push(new n({aabb:new i({lowerBound:new o(0,0,0)})}),new n({aabb:new i({lowerBound:new o(1,0,0)})}),new n({aabb:new i({lowerBound:new o(1,1,0)})}),new n({aabb:new i({lowerBound:new o(1,1,1)})}),new n({aabb:new i({lowerBound:new o(0,1,1)})}),new n({aabb:new i({lowerBound:new o(0,0,1)})}),new n({aabb:new i({lowerBound:new o(1,0,1)})}),new n({aabb:new i({lowerBound:new o(0,1,0)})})),s.vsub(e,r),r.scale(.5,r);for(var l=this.root||this,h=0;8!==h;h++){var p=a[h];p.root=l;var c=p.aabb.lowerBound;c.x*=r.x,c.y*=r.y,c.z*=r.z,c.vadd(e,c),c.vadd(r,p.aabb.upperBound)}},n.prototype.aabbQuery=function(t,e){this.data,this.children;for(var i=[this];i.length;){var o=i.pop();o.aabb.overlaps(t)&&Array.prototype.push.apply(e,o.data),Array.prototype.push.apply(i,o.children)}return e};var a=new i;n.prototype.rayQuery=function(t,e,i){return t.getAABB(a),a.toLocalFrame(e,a),this.aabbQuery(a,i),i},n.prototype.removeEmptyNodes=function(){for(var t=this.children.length-1;t>=0;t--)this.children[t].removeEmptyNodes(),this.children[t].children.length||this.children[t].data.length||this.children.splice(t,1)}},{"../collision/AABB":3,"../math/Vec3":31}],52:[function(t,e){function i(){this.objects=[],this.type=Object}e.exports=i,i.prototype.release=function(){for(var t=arguments.length,e=0;e!==t;e++)this.objects.push(arguments[e]);return this},i.prototype.get=function(){return 0===this.objects.length?this.constructObject():this.objects.pop()},i.prototype.constructObject=function(){throw new Error("constructObject() not implemented in this Pool subclass yet!")},i.prototype.resize=function(t){for(var e=this.objects;e.length>t;)e.pop();for(;e.length<t;)e.push(this.constructObject());return this}},{}],53:[function(t,e){function i(){this.data={keys:[]}}e.exports=i,i.prototype.get=function(t,e){if(t>e){var i=e;e=t,t=i}return this.data[t+"-"+e]},i.prototype.set=function(t,e,i){if(t>e){var o=e;e=t,t=o}var n=t+"-"+e;return this.get(t,e)||this.data.keys.push(n),this.data[n]=i,this.data[n]},i.prototype.del=function(t,e){if(t>e){var i=e;e=t,t=i}var o=t+"-"+e,n=this.data.keys.indexOf(o);return n>=0&&(this.data.keys.splice(n,1),delete this.data[o],!0)},i.prototype.reset=function(){this.data={keys:[]}},i.prototype.getLength=function(){return this.data.keys.length},i.prototype.getKeyByIndex=function(t){return this.data.keys[t]},i.prototype.getDataByKey=function(t){return this.data[t]}},{}],54:[function(t,e){function i(){}e.exports=i,i.defaults=function(t,e){for(var i in t=t||{},e)i in t||(t[i]=e[i]);return t}},{}],55:[function(t,e){e.exports=n;var i=t("../math/Vec3"),o=t("./Pool");function n(){o.call(this),this.type=i}n.prototype=new o,n.prototype.constructObject=function(){return new i}},{"../math/Vec3":31,"./Pool":52}],56:[function(t,e){e.exports=c;var i=t("../collision/AABB");t("../objects/Body");var o=t("../shapes/Shape"),n=t("../collision/Ray"),s=t("../math/Vec3"),r=t("../math/Transform");t("../shapes/ConvexPolyhedron");var a=t("../math/Quaternion");t("../solver/Solver");var l=t("../utils/Vec3Pool"),h=t("../equations/ContactEquation"),p=t("../equations/FrictionEquation");function c(t){this.contactPointPool=[],this.frictionEquationPool=[],this.result=[],this.frictionResult=[],this.v3pool=new l,this.world=t,this.currentContactMaterial=null,this.enableFrictionReduction=!1}c.prototype.createContactEquation=function(t,e,i,o,n,s){var r;this.contactPointPool.length?((r=this.contactPointPool.pop()).bi=t,r.bj=e):r=new h(t,e),r.enabled=t.collisionResponse&&e.collisionResponse&&i.collisionResponse&&o.collisionResponse;var a=this.currentContactMaterial,l=a.contactEquationRelaxation;r.restitution=a.restitution;var p=i.material||t.material,c=o.material||e.material;if(p&&c){p.restitution>=0&&c.restitution>=0&&(r.restitution=p.restitution*c.restitution);var u=p.correctInelastic||c.correctInelastic;u&&(l*=u)}return r.setSpookParams(a.contactEquationStiffness,l,this.world.dt),r.si=n||i,r.sj=s||o,r},c.prototype.createFrictionEquationsFromContact=function(t,e){var i=t.bi,o=t.bj,n=t.si,s=t.sj,r=this.world,a=this.currentContactMaterial,l=a.friction,h=n.material||i.material,c=s.material||o.material;if(h&&c&&h.friction>=0&&c.friction>=0&&(l=h.friction*c.friction),l>0){var u=l*r.gravity.length(),d=i.invMass+o.invMass;d>0&&(d=1/d);var y=this.frictionEquationPool,v=y.length?y.pop():new p(i,o,u*d),f=y.length?y.pop():new p(i,o,u*d);return v.bi=f.bi=i,v.bj=f.bj=o,v.minForce=f.minForce=-u*d,v.maxForce=f.maxForce=u*d,v.ri.copy(t.ri),v.rj.copy(t.rj),f.ri.copy(t.ri),f.rj.copy(t.rj),t.ni.tangents(v.t,f.t),v.setSpookParams(a.frictionEquationStiffness,a.frictionEquationRelaxation,r.dt),f.setSpookParams(a.frictionEquationStiffness,a.frictionEquationRelaxation,r.dt),v.enabled=f.enabled=t.enabled,e.push(v,f),!0}return!1};var u=new s,d=new s,y=new s;c.prototype.createFrictionFromAverage=function(t){var e=this.result[this.result.length-1];if(this.createFrictionEquationsFromContact(e,this.frictionResult)&&1!==t){var i=this.frictionResult[this.frictionResult.length-2],o=this.frictionResult[this.frictionResult.length-1];u.setZero(),d.setZero(),y.setZero();var n=e.bi;e.bj;for(var s=0;s!==t;s++)(e=this.result[this.result.length-1-s]).bodyA!==n?(u.vadd(e.ni,u),d.vadd(e.ri,d),y.vadd(e.rj,y)):(u.vsub(e.ni,u),d.vadd(e.rj,d),y.vadd(e.ri,y));var r=1/t;d.scale(r,i.ri),y.scale(r,i.rj),o.ri.copy(i.ri),o.rj.copy(i.rj),u.normalize(),u.tangents(i.t,o.t)}};var v=new s,f=new s,m=new a,w=new a;c.prototype.getContacts=function(t,e,i,o,n,s,r){this.contactPointPool=n,this.frictionEquationPool=r,this.result=o,this.frictionResult=s;for(var a=m,l=w,h=v,p=f,c=0,u=t.length;c!==u;c++){var d=t[c],y=e[c],g=null;d.material&&y.material&&(g=i.getContactMaterial(d.material,y.material)||null);for(var b=0==d.collisionResponse||0==y.collisionResponse,x=0;x<d.shapes.length;x++){d.quaternion.mult(d.shapeOrientations[x],a),d.quaternion.vmult(d.shapeOffsets[x],h),h.vadd(d.position,h);for(var B=d.shapes[x],S=0;S<y.shapes.length;S++){y.quaternion.mult(y.shapeOrientations[S],l),y.quaternion.vmult(y.shapeOffsets[S],p),p.vadd(y.position,p);var A=y.shapes[S];if(B.collisionFilterMask&A.collisionFilterGroup&&A.collisionFilterMask&B.collisionFilterGroup&&!(h.distanceTo(p)>B.boundingSphereRadius+A.boundingSphereRadius)){b|=0==B.collisionResponse||0==A.collisionResponse;var C=null;B.material&&A.material&&(C=i.getContactMaterial(B.material,A.material)||null),this.currentContactMaterial=C||g||i.defaultContactMaterial;var E=this[B.type|A.type];if(E&&(B.type<A.type?E.call(this,B,A,h,p,a,l,d,y,B,A,b):E.call(this,A,B,p,h,l,a,y,d,B,A,b))&&b){i.shapeOverlapKeeper.set(B.id,A.id),i.bodyOverlapKeeper.set(d.id,y.id);var M={si:B,sj:A};i.triggerDic.set(B.id,A.id,M),i.oldTriggerDic.set(B.id,A.id,M)}}}}}},c.prototype[o.types.BOX|o.types.BOX]=c.prototype.boxBox=function(t,e,i,o,n,s,r,a,l,h,p){return t.convexPolyhedronRepresentation.material=t.material,e.convexPolyhedronRepresentation.material=e.material,t.convexPolyhedronRepresentation.collisionResponse=t.collisionResponse,e.convexPolyhedronRepresentation.collisionResponse=e.collisionResponse,this.convexConvex(t.convexPolyhedronRepresentation,e.convexPolyhedronRepresentation,i,o,n,s,r,a,t,e,p)},c.prototype[o.types.BOX|o.types.CONVEXPOLYHEDRON]=c.prototype.boxConvex=function(t,e,i,o,n,s,r,a,l,h,p){return t.convexPolyhedronRepresentation.material=t.material,t.convexPolyhedronRepresentation.collisionResponse=t.collisionResponse,this.convexConvex(t.convexPolyhedronRepresentation,e,i,o,n,s,r,a,t,e,p)},c.prototype[o.types.BOX|o.types.PARTICLE]=c.prototype.boxParticle=function(t,e,i,o,n,s,r,a,l,h,p){return t.convexPolyhedronRepresentation.material=t.material,t.convexPolyhedronRepresentation.collisionResponse=t.collisionResponse,this.convexParticle(t.convexPolyhedronRepresentation,e,i,o,n,s,r,a,t,e,p)},c.prototype[o.types.SPHERE]=c.prototype.sphereSphere=function(t,e,i,o,n,s,r,a,l,h,p){if(p)return i.distanceSquared(o)<Math.pow(t.radius+e.radius,2);var c=this.createContactEquation(r,a,t,e,l,h);o.vsub(i,c.ni),c.ni.normalize(),c.ri.copy(c.ni),c.rj.copy(c.ni),c.ri.mult(t.radius,c.ri),c.rj.mult(-e.radius,c.rj),c.ri.vadd(i,c.ri),c.ri.vsub(r.position,c.ri),c.rj.vadd(o,c.rj),c.rj.vsub(a.position,c.rj),this.result.push(c),this.createFrictionEquationsFromContact(c,this.frictionResult)};var g=new s,b=new s,x=new s;c.prototype[o.types.PLANE|o.types.TRIMESH]=c.prototype.planeTrimesh=function(t,e,i,o,n,a,l,h,p,c,u){var d=new s,y=g;y.set(0,0,1),n.vmult(y,y);for(var v=0;v<e.vertices.length/3;v++){e.getVertex(v,d);var f=new s;f.copy(d),r.pointToWorldFrame(o,a,f,d);var m=b;if(d.vsub(i,m),y.dot(m)<=0){if(u)return!0;var w=this.createContactEquation(l,h,t,e,p,c);w.ni.copy(y);var B=x;y.scale(m.dot(y),B),d.vsub(B,B),w.ri.copy(B),w.ri.vsub(l.position,w.ri),w.rj.copy(d),w.rj.vsub(h.position,w.rj),this.result.push(w),this.createFrictionEquationsFromContact(w,this.frictionResult)}}};var B=new s,S=new s;new s;var A=new s,C=new s,E=new s,M=new s,_=new s,z=new s,F=new s,R=new s,T=new s,P=new s,q=new s,I=new i,V=[];c.prototype[o.types.SPHERE|o.types.TRIMESH]=c.prototype.sphereTrimesh=function(t,e,i,o,s,a,l,h,p,c,u){var d=E,y=M,v=_,f=z,m=F,w=R,g=I,b=C,x=S,L=V;r.pointToLocalFrame(o,a,i,m);var N=t.radius;g.lowerBound.set(m.x-N,m.y-N,m.z-N),g.upperBound.set(m.x+N,m.y+N,m.z+N),e.getTrianglesInAABB(g,L);for(var W=A,j=t.radius*t.radius,k=0;k<L.length;k++)for(var O=0;O<3;O++)if(e.getVertex(e.indices[3*L[k]+O],W),W.vsub(m,x),x.norm2()<=j){if(b.copy(W),r.pointToWorldFrame(o,a,b,W),W.vsub(i,x),u)return!0;(G=this.createContactEquation(l,h,t,e,p,c)).ni.copy(x),G.ni.normalize(),G.ri.copy(G.ni),G.ri.scale(t.radius,G.ri),G.ri.vadd(i,G.ri),G.ri.vsub(l.position,G.ri),G.rj.copy(W),G.rj.vsub(h.position,G.rj),this.result.push(G),this.createFrictionEquationsFromContact(G,this.frictionResult)}for(k=0;k<L.length;k++)for(O=0;O<3;O++){e.getVertex(e.indices[3*L[k]+O],d),e.getVertex(e.indices[3*L[k]+(O+1)%3],y),y.vsub(d,v),m.vsub(y,w);var D=w.dot(v);m.vsub(d,w);var U=w.dot(v);if(U>0&&D<0&&(m.vsub(d,w),f.copy(v),f.normalize(),U=w.dot(f),f.scale(U,w),w.vadd(d,w),(Z=w.distanceTo(m))<t.radius)){if(u)return!0;var G=this.createContactEquation(l,h,t,e,p,c);w.vsub(m,G.ni),G.ni.normalize(),G.ni.scale(t.radius,G.ri),r.pointToWorldFrame(o,a,w,w),w.vsub(h.position,G.rj),r.vectorToWorldFrame(a,G.ni,G.ni),r.vectorToWorldFrame(a,G.ri,G.ri),this.result.push(G),this.createFrictionEquationsFromContact(G,this.frictionResult)}}for(var H=T,Q=P,X=q,Y=B,K=(k=0,L.length);k!==K;k++){e.getTriangleVertices(L[k],H,Q,X),e.getNormal(L[k],Y),m.vsub(H,w);var Z=w.dot(Y);if(Y.scale(Z,w),m.vsub(w,w),Z=w.distanceTo(m),n.pointInTriangle(w,H,Q,X)&&Z<t.radius){if(u)return!0;G=this.createContactEquation(l,h,t,e,p,c),w.vsub(m,G.ni),G.ni.normalize(),G.ni.scale(t.radius,G.ri),r.pointToWorldFrame(o,a,w,w),w.vsub(h.position,G.rj),r.vectorToWorldFrame(a,G.ni,G.ni),r.vectorToWorldFrame(a,G.ri,G.ri),this.result.push(G),this.createFrictionEquationsFromContact(G,this.frictionResult)}}L.length=0};var L=new s,N=new s,W=new s,j=new s,k=new s;c.prototype[o.types.SPHERE|o.types.PLANE]=c.prototype.spherePlane=function(t,e,i,o,n,s,r,a,l,h,p){if(W.set(0,0,1),s.vmult(W,W),W.negate(W),W.normalize(),W.mult(t.radius,j),i.vsub(o,L),W.mult(W.dot(L),N),L.vsub(N,k),-L.dot(W)<=t.radius){if(p)return!0;var c=this.createContactEquation(r,a,t,e,l,h);c.ni.copy(W),c.ri.copy(j),c.rj.copy(k);var u=c.ri,d=c.rj;u.vadd(i,u),u.vsub(r.position,u),d.vadd(o,d),d.vsub(a.position,d),this.result.push(c),this.createFrictionEquationsFromContact(c,this.frictionResult)}return!1};var O=new s,D=new s,U=new s;function G(t,e,i){for(var o=null,n=t.length,s=0;s!==n;s++){var r=t[s],a=O;t[(s+1)%n].vsub(r,a);var l=D;a.cross(e,l);var h=U;i.vsub(r,h);var p=l.dot(h);if(!(null===o||p>0&&!0===o||p<=0&&!1===o))return!1;null===o&&(o=p>0)}return!0}var H=new s,Q=new s,X=new s,Y=new s,K=[new s,new s,new s,new s,new s,new s],Z=new s,J=new s,$=new s,tt=new s;c.prototype[o.types.SPHERE|o.types.BOX]=c.prototype.sphereBox=function(t,e,i,o,n,s,r,a,l,h,p){var c=this.v3pool,u=K;i.vsub(o,H),e.getSideNormals(u,s);for(var d=t.radius,y=!1,v=J,f=$,m=tt,w=null,g=0,b=0,x=0,B=null,S=0,A=u.length;S!==A&&!1===y;S++){var C=Q;C.copy(u[S]);var E=C.norm();C.normalize();var M=H.dot(C);if(M<E+d&&M>0){var _=X,z=Y;_.copy(u[(S+1)%3]),z.copy(u[(S+2)%3]);var F=_.norm(),R=z.norm();_.normalize(),z.normalize();var T=H.dot(_),P=H.dot(z);if(T<F&&T>-F&&P<R&&P>-R){var q=Math.abs(M-E-d);if((null===B||q<B)&&(B=q,b=T,x=P,w=E,v.copy(C),f.copy(_),m.copy(z),g++,p))return!0}}}if(g){y=!0;var I=this.createContactEquation(r,a,t,e,l,h);v.mult(-d,I.ri),I.ni.copy(v),I.ni.negate(I.ni),v.mult(w,v),f.mult(b,f),v.vadd(f,v),m.mult(x,m),v.vadd(m,I.rj),I.ri.vadd(i,I.ri),I.ri.vsub(r.position,I.ri),I.rj.vadd(o,I.rj),I.rj.vsub(a.position,I.rj),this.result.push(I),this.createFrictionEquationsFromContact(I,this.frictionResult)}for(var V=c.get(),L=Z,N=0;2!==N&&!y;N++)for(var W=0;2!==W&&!y;W++)for(var j=0;2!==j&&!y;j++)if(V.set(0,0,0),N?V.vadd(u[0],V):V.vsub(u[0],V),W?V.vadd(u[1],V):V.vsub(u[1],V),j?V.vadd(u[2],V):V.vsub(u[2],V),o.vadd(V,L),L.vsub(i,L),L.norm2()<d*d){if(p)return!0;y=!0,(I=this.createContactEquation(r,a,t,e,l,h)).ri.copy(L),I.ri.normalize(),I.ni.copy(I.ri),I.ri.mult(d,I.ri),I.rj.copy(V),I.ri.vadd(i,I.ri),I.ri.vsub(r.position,I.ri),I.rj.vadd(o,I.rj),I.rj.vsub(a.position,I.rj),this.result.push(I),this.createFrictionEquationsFromContact(I,this.frictionResult)}c.release(V),V=null;var k=c.get(),O=c.get(),D=(I=c.get(),c.get()),U=(q=c.get(),u.length);for(N=0;N!==U&&!y;N++)for(W=0;W!==U&&!y;W++)if(N%3!=W%3){u[W].cross(u[N],k),k.normalize(),u[N].vadd(u[W],O),I.copy(i),I.vsub(O,I),I.vsub(o,I);var G=I.dot(k);for(k.mult(G,D),j=0;j===N%3||j===W%3;)j++;q.copy(i),q.vsub(D,q),q.vsub(O,q),q.vsub(o,q);var et=Math.abs(G),it=q.norm();if(et<u[j].norm()&&it<d){if(p)return!0;y=!0;var ot=this.createContactEquation(r,a,t,e,l,h);O.vadd(D,ot.rj),ot.rj.copy(ot.rj),q.negate(ot.ni),ot.ni.normalize(),ot.ri.copy(ot.rj),ot.ri.vadd(o,ot.ri),ot.ri.vsub(i,ot.ri),ot.ri.normalize(),ot.ri.mult(d,ot.ri),ot.ri.vadd(i,ot.ri),ot.ri.vsub(r.position,ot.ri),ot.rj.vadd(o,ot.rj),ot.rj.vsub(a.position,ot.rj),this.result.push(ot),this.createFrictionEquationsFromContact(ot,this.frictionResult)}}c.release(k,O,I,D,q)};var et=new s,it=new s,ot=new s,nt=new s,st=new s,rt=new s,at=new s,lt=new s,ht=new s,pt=new s;c.prototype[o.types.SPHERE|o.types.CONVEXPOLYHEDRON]=c.prototype.sphereConvex=function(t,e,i,o,n,s,r,a,l,h,p){var c=this.v3pool;i.vsub(o,et);for(var u=e.faceNormals,d=e.faces,y=e.vertices,v=t.radius,f=0;f!==y.length;f++){var m=y[f],w=st;s.vmult(m,w),o.vadd(w,w);var g=nt;if(w.vsub(i,g),g.norm2()<v*v)return!!p||(b=!0,(q=this.createContactEquation(r,a,t,e,l,h)).ri.copy(g),q.ri.normalize(),q.ni.copy(q.ri),q.ri.mult(v,q.ri),w.vsub(o,q.rj),q.ri.vadd(i,q.ri),q.ri.vsub(r.position,q.ri),q.rj.vadd(o,q.rj),q.rj.vsub(a.position,q.rj),this.result.push(q),void this.createFrictionEquationsFromContact(q,this.frictionResult))}for(var b=!1,x=(f=0,d.length);f!==x&&!1===b;f++){var B=u[f],S=d[f],A=rt;s.vmult(B,A);var C=at;s.vmult(y[S[0]],C),C.vadd(o,C);var E=lt;A.mult(-v,E),i.vadd(E,E);var M=ht;E.vsub(C,M);var _=M.dot(A),z=pt;if(i.vsub(C,z),_<0&&z.dot(A)>0){for(var F=[],R=0,T=S.length;R!==T;R++){var P=c.get();s.vmult(y[S[R]],P),o.vadd(P,P),F.push(P)}if(G(F,A,i)){if(p)return!0;b=!0;var q=this.createContactEquation(r,a,t,e,l,h);A.mult(-v,q.ri),A.negate(q.ni);var I=c.get();A.mult(-_,I);var V=c.get();A.mult(-v,V),i.vsub(o,q.rj),q.rj.vadd(V,q.rj),q.rj.vadd(I,q.rj),q.rj.vadd(o,q.rj),q.rj.vsub(a.position,q.rj),q.ri.vadd(i,q.ri),q.ri.vsub(r.position,q.ri),c.release(I),c.release(V),this.result.push(q),this.createFrictionEquationsFromContact(q,this.frictionResult),R=0;for(var L=F.length;R!==L;R++)c.release(F[R]);return}for(R=0;R!==S.length;R++){var N=c.get(),W=c.get();s.vmult(y[S[(R+1)%S.length]],N),s.vmult(y[S[(R+2)%S.length]],W),o.vadd(N,N),o.vadd(W,W);var j=it;W.vsub(N,j);var k=ot;j.unit(k);var O=c.get(),D=c.get();i.vsub(N,D);var U=D.dot(k);k.mult(U,O),O.vadd(N,O);var H=c.get();if(O.vsub(i,H),U>0&&U*U<j.norm2()&&H.norm2()<v*v){if(p)return!0;for(q=this.createContactEquation(r,a,t,e,l,h),O.vsub(o,q.rj),O.vsub(i,q.ni),q.ni.normalize(),q.ni.mult(v,q.ri),q.rj.vadd(o,q.rj),q.rj.vsub(a.position,q.rj),q.ri.vadd(i,q.ri),q.ri.vsub(r.position,q.ri),this.result.push(q),this.createFrictionEquationsFromContact(q,this.frictionResult),R=0,L=F.length;R!==L;R++)c.release(F[R]);return c.release(N),c.release(W),c.release(O),c.release(H),void c.release(D)}c.release(N),c.release(W),c.release(O),c.release(H),c.release(D)}for(R=0,L=F.length;R!==L;R++)c.release(F[R])}}},new s,new s,c.prototype[o.types.PLANE|o.types.BOX]=c.prototype.planeBox=function(t,e,i,o,n,s,r,a,l,h,p){return e.convexPolyhedronRepresentation.material=e.material,e.convexPolyhedronRepresentation.collisionResponse=e.collisionResponse,e.convexPolyhedronRepresentation.id=e.id,this.planeConvex(t,e.convexPolyhedronRepresentation,i,o,n,s,r,a,t,e,p)};var ct=new s,ut=new s,dt=new s,yt=new s;c.prototype[o.types.PLANE|o.types.CONVEXPOLYHEDRON]=c.prototype.planeConvex=function(t,e,i,o,n,s,r,a,l,h,p){var c=ct,u=ut;u.set(0,0,1),n.vmult(u,u);for(var d=0,y=dt,v=0;v!==e.vertices.length;v++)if(c.copy(e.vertices[v]),s.vmult(c,c),o.vadd(c,c),c.vsub(i,y),u.dot(y)<=0){if(p)return!0;var f=this.createContactEquation(r,a,t,e,l,h),m=yt;u.mult(u.dot(y),m),c.vsub(m,m),m.vsub(i,f.ri),f.ni.copy(u),c.vsub(o,f.rj),f.ri.vadd(i,f.ri),f.ri.vsub(r.position,f.ri),f.rj.vadd(o,f.rj),f.rj.vsub(a.position,f.rj),this.result.push(f),d++,this.enableFrictionReduction||this.createFrictionEquationsFromContact(f,this.frictionResult)}this.enableFrictionReduction&&d&&this.createFrictionFromAverage(d)};var vt=new s,ft=new s;c.prototype[o.types.CONVEXPOLYHEDRON]=c.prototype.convexConvex=function(t,e,i,o,n,s,r,a,l,h,p,c,u){var d=vt;if(!(i.distanceTo(o)>t.boundingSphereRadius+e.boundingSphereRadius)&&t.findSeparatingAxis(e,i,n,o,s,d,c,u)){var y=[],v=ft;t.clipAgainstHull(i,n,e,o,s,d,-100,100,y);for(var f=0,m=0;m!==y.length;m++){if(p)return!0;var w=this.createContactEquation(r,a,t,e,l,h),g=w.ri,b=w.rj;d.negate(w.ni),y[m].normal.negate(v),v.mult(y[m].depth,v),y[m].point.vadd(v,g),b.copy(y[m].point),g.vsub(i,g),b.vsub(o,b),g.vadd(i,g),g.vsub(r.position,g),b.vadd(o,b),b.vsub(a.position,b),this.result.push(w),f++,this.enableFrictionReduction||this.createFrictionEquationsFromContact(w,this.frictionResult)}this.enableFrictionReduction&&f&&this.createFrictionFromAverage(f)}};var mt=new s,wt=new s,gt=new s;c.prototype[o.types.PLANE|o.types.PARTICLE]=c.prototype.planeParticle=function(t,e,i,o,n,s,r,a,l,h,p){var c=mt;c.set(0,0,1),r.quaternion.vmult(c,c);var u=wt;if(o.vsub(r.position,u),c.dot(u)<=0){if(p)return!0;var d=this.createContactEquation(a,r,e,t,l,h);d.ni.copy(c),d.ni.negate(d.ni),d.ri.set(0,0,0);var y=gt;c.mult(c.dot(o),y),o.vsub(y,y),d.rj.copy(y),this.result.push(d),this.createFrictionEquationsFromContact(d,this.frictionResult)}};var bt=new s;c.prototype[o.types.PARTICLE|o.types.SPHERE]=c.prototype.sphereParticle=function(t,e,i,o,n,s,r,a,l,h,p){var c=bt;if(c.set(0,0,1),o.vsub(i,c),c.norm2()<=t.radius*t.radius){if(p)return!0;var u=this.createContactEquation(a,r,e,t,l,h);c.normalize(),u.rj.copy(c),u.rj.mult(t.radius,u.rj),u.ni.copy(c),u.ni.negate(u.ni),u.ri.set(0,0,0),this.result.push(u),this.createFrictionEquationsFromContact(u,this.frictionResult)}};var xt=new a,Bt=new s;new s;var St=new s,At=new s,Ct=new s;c.prototype[o.types.PARTICLE|o.types.CONVEXPOLYHEDRON]=c.prototype.convexParticle=function(t,e,i,o,n,s,r,a,l,h,p){var c=-1,u=St,d=Ct,y=null,v=Bt;if(v.copy(o),v.vsub(i,v),n.conjugate(xt),xt.vmult(v,v),t.pointIsInside(v)){t.worldVerticesNeedsUpdate&&t.computeWorldVertices(i,n),t.worldFaceNormalsNeedsUpdate&&t.computeWorldFaceNormals(n);for(var f=0,m=t.faces.length;f!==m;f++){var w=[t.worldVertices[t.faces[f][0]]],g=t.worldFaceNormals[f];o.vsub(w[0],At);var b=-g.dot(At);if(null===y||Math.abs(b)<Math.abs(y)){if(p)return!0;y=b,c=f,u.copy(g)}}if(-1!==c){var x=this.createContactEquation(a,r,e,t,l,h);u.mult(y,d),d.vadd(o,d),d.vsub(i,d),x.rj.copy(d),u.negate(x.ni),x.ri.set(0,0,0);var B=x.ri,S=x.rj;B.vadd(o,B),B.vsub(a.position,B),S.vadd(i,S),S.vsub(r.position,S),this.result.push(x),this.createFrictionEquationsFromContact(x,this.frictionResult)}else console.warn("Point found inside convex, but did not find penetrating face!")}},c.prototype[o.types.BOX|o.types.HEIGHTFIELD]=c.prototype.boxHeightfield=function(t,e,i,o,n,s,r,a,l,h,p){return t.convexPolyhedronRepresentation.material=t.material,t.convexPolyhedronRepresentation.collisionResponse=t.collisionResponse,this.convexHeightfield(t.convexPolyhedronRepresentation,e,i,o,n,s,r,a,t,e,p)};var Et=new s,Mt=new s,_t=[0];c.prototype[o.types.CONVEXPOLYHEDRON|o.types.HEIGHTFIELD]=c.prototype.convexHeightfield=function(t,e,i,o,n,s,a,l,h,p,c){var u=e.data,d=e.elementSize,y=t.boundingSphereRadius,v=Mt,f=_t,m=Et;r.pointToLocalFrame(o,s,i,m);var w=Math.floor((m.x-y)/d)-1,g=Math.ceil((m.x+y)/d)+1,b=Math.floor((m.y-y)/d)-1,x=Math.ceil((m.y+y)/d)+1;if(!(g<0||x<0||w>u.length||b>u[0].length)){w<0&&(w=0),g<0&&(g=0),b<0&&(b=0),x<0&&(x=0),w>=u.length&&(w=u.length-1),g>=u.length&&(g=u.length-1),x>=u[0].length&&(x=u[0].length-1),b>=u[0].length&&(b=u[0].length-1);var B=[];e.getRectMinMax(w,b,g,x,B);var S=B[0],A=B[1];if(!(m.z-y>A||m.z+y<S))for(var C=w;C<g;C++)for(var E=b;E<x;E++){var M=!1;if(e.getConvexTrianglePillar(C,E,!1),r.pointToWorldFrame(o,s,e.pillarOffset,v),i.distanceTo(v)<e.pillarConvex.boundingSphereRadius+t.boundingSphereRadius&&(M=this.convexConvex(t,e.pillarConvex,i,v,n,s,a,l,h,p,c,f,null)),c&&M)return!0;if(e.getConvexTrianglePillar(C,E,!0),r.pointToWorldFrame(o,s,e.pillarOffset,v),i.distanceTo(v)<e.pillarConvex.boundingSphereRadius+t.boundingSphereRadius&&(M=this.convexConvex(t,e.pillarConvex,i,v,n,s,a,l,h,p,c,f,null)),c&&M)return!0}}};var zt=new s,Ft=new s;c.prototype[o.types.SPHERE|o.types.HEIGHTFIELD]=c.prototype.sphereHeightfield=function(t,e,i,o,n,s,a,l,h,p,c){var u=e.data,d=t.radius,y=e.elementSize,v=Ft,f=zt;r.pointToLocalFrame(o,s,i,f);var m=Math.floor((f.x-d)/y)-1,w=Math.ceil((f.x+d)/y)+1,g=Math.floor((f.y-d)/y)-1,b=Math.ceil((f.y+d)/y)+1;if(!(w<0||b<0||m>u.length||g>u[0].length)){m<0&&(m=0),w<0&&(w=0),g<0&&(g=0),b<0&&(b=0),m>=u.length&&(m=u.length-1),w>=u.length&&(w=u.length-1),b>=u[0].length&&(b=u[0].length-1),g>=u[0].length&&(g=u[0].length-1);var x=[];e.getRectMinMax(m,g,w,b,x);var B=x[0],S=x[1];if(!(f.z-d>S||f.z+d<B))for(var A=this.result,C=m;C<w;C++)for(var E=g;E<b;E++){var M=A.length,_=!1;if(e.getConvexTrianglePillar(C,E,!1),r.pointToWorldFrame(o,s,e.pillarOffset,v),i.distanceTo(v)<e.pillarConvex.boundingSphereRadius+t.boundingSphereRadius&&(_=this.sphereConvex(t,e.pillarConvex,i,v,n,s,a,l,t,e,c)),c&&_)return!0;if(e.getConvexTrianglePillar(C,E,!0),r.pointToWorldFrame(o,s,e.pillarOffset,v),i.distanceTo(v)<e.pillarConvex.boundingSphereRadius+t.boundingSphereRadius&&(_=this.sphereConvex(t,e.pillarConvex,i,v,n,s,a,l,t,e,c)),c&&_)return!0;if(A.length-M>2)return}}}},{"../collision/AABB":3,"../collision/Ray":10,"../equations/ContactEquation":20,"../equations/FrictionEquation":22,"../math/Quaternion":29,"../math/Transform":30,"../math/Vec3":31,"../objects/Body":32,"../shapes/ConvexPolyhedron":39,"../shapes/Shape":44,"../solver/Solver":48,"../utils/Vec3Pool":55}],57:[function(t,e){e.exports=w,t("../shapes/Shape");var i=t("../math/Vec3"),o=t("../math/Quaternion"),n=t("../solver/GSSolver");t("../equations/ContactEquation"),t("../equations/FrictionEquation");var s=t("./Narrowphase"),r=t("../utils/EventTarget"),a=t("../collision/ArrayCollisionMatrix"),l=t("../collision/ObjectCollisionMatrix"),h=t("../collision/OverlapKeeper"),p=t("../material/Material"),c=t("../material/ContactMaterial"),u=t("../objects/Body"),d=t("../utils/TupleDictionary"),y=t("../collision/RaycastResult"),v=t("../collision/AABB"),f=t("../collision/Ray"),m=t("../collision/NaiveBroadphase");function w(t){t=t||{},r.apply(this),this.dt=-1,this.allowSleep=!!t.allowSleep,this.contacts=[],this.frictionEquations=[],this.quatNormalizeSkip=void 0!==t.quatNormalizeSkip?t.quatNormalizeSkip:0,this.quatNormalizeFast=void 0!==t.quatNormalizeFast&&t.quatNormalizeFast,this.time=0,this.stepnumber=0,this.default_dt=1/60,this.nextId=0,this.gravity=new i,t.gravity&&this.gravity.copy(t.gravity),this.broadphase=void 0!==t.broadphase?t.broadphase:new m,this.bodies=[],this.solver=void 0!==t.solver?t.solver:new n,this.constraints=[],this.narrowphase=new s(this),this.collisionMatrix=new a,this.collisionMatrixPrevious=new a,this.bodyOverlapKeeper=new h,this.shapeOverlapKeeper=new h,this.materials=[],this.contactmaterials=[],this.contactMaterialTable=new d,this.defaultMaterial=new p("default"),this.defaultContactMaterial=new c(this.defaultMaterial,this.defaultMaterial,{friction:.3,restitution:0}),this.doProfiling=!1,this.profile={solve:0,makeContactConstraints:0,broadphase:0,integrate:0,narrowphase:0},this.accumulator=0,this.subsystems=[],this.addBodyEvent={type:"addBody",body:null},this.removeBodyEvent={type:"removeBody",body:null},this.idToBodyMap={},this.broadphase.setWorld(this),this.substeps=0,this.cm=new l,this.tm=new l,this.triggerDic=new d,this.oldTriggerDic=new d,this.contactsDic=new d,this.oldContactsDic=new d}w.idToBodyMap={},w.idToShapeMap={},w.integrateKinematic=!1,w.ccdSphereAdvance=!1,w.prototype=new r,new v;var g=new f;if(w.prototype.getContactMaterial=function(t,e){return this.contactMaterialTable.get(t.id,e.id)},w.prototype.numObjects=function(){return this.bodies.length},w.prototype.collisionMatrixTick=function(){var t=this.collisionMatrixPrevious;this.collisionMatrixPrevious=this.collisionMatrix,this.collisionMatrix=t,this.collisionMatrix.reset(),this.bodyOverlapKeeper.tick(),this.shapeOverlapKeeper.tick()},w.prototype.add=w.prototype.addBody=function(t){-1===this.bodies.indexOf(t)&&(t.index=this.bodies.length,this.bodies.push(t),t.world=this,t.initPosition.copy(t.position),t.initVelocity.copy(t.velocity),t.timeLastSleepy=this.time,t instanceof u&&(t.initAngularVelocity.copy(t.angularVelocity),t.initQuaternion.copy(t.quaternion)),this.collisionMatrix.setNumObjects(this.bodies.length),this.addBodyEvent.body=t,this.cm.setNumObjects(this.bodies.length),w.idToBodyMap[t.id]=t,this.dispatchEvent(this.addBodyEvent))},w.prototype.addConstraint=function(t){this.constraints.push(t)},w.prototype.removeConstraint=function(t){var e=this.constraints.indexOf(t);-1!==e&&this.constraints.splice(e,1)},w.prototype.rayTest=function(t,e,i){i instanceof y?this.raycastClosest(t,e,{skipBackfaces:!0},i):this.raycastAll(t,e,{skipBackfaces:!0},i)},w.prototype.raycastAll=function(t,e,i,o){return i.mode=f.ALL,i.from=t,i.to=e,i.callback=o,g.intersectWorld(this,i)},w.prototype.raycastAny=function(t,e,i,o){return i.mode=f.ANY,i.from=t,i.to=e,i.result=o,g.intersectWorld(this,i)},w.prototype.raycastClosest=function(t,e,i,o){return i.mode=f.CLOSEST,i.from=t,i.to=e,i.result=o,g.intersectWorld(this,i)},w.prototype.remove=function(t){t.world=null;var e=this.bodies.length-1,i=this.bodies,o=i.indexOf(t);if(-1!==o){i.splice(o,1);for(var n=0;n!==i.length;n++)i[n].index=n;this.collisionMatrix.setNumObjects(e),this.removeBodyEvent.body=t,delete w.idToBodyMap[t.id],this.cm.setNumObjects(e),this.dispatchEvent(this.removeBodyEvent)}},w.prototype.removeBody=w.prototype.remove,w.prototype.getBodyById=function(t){return w.idToBodyMap[t]},w.prototype.getShapeById=function(t){return w.idToShapeMap[t]},w.prototype.addMaterial=function(t){this.materials.push(t)},w.prototype.addContactMaterial=function(t){this.contactmaterials.push(t),this.contactMaterialTable.set(t.materials[0].id,t.materials[1].id,t)},"undefined"==typeof performance&&(performance={}),!performance.now){var b=Date.now();performance.timing&&performance.timing.navigationStart&&(b=performance.timing.navigationStart),performance.now=function(){return Date.now()-b}}w.prototype.saveKinematicAndApplyGravity=function(t){for(var e=this.bodies,i=this.bodies.length,o=0;o!==i;o++){var n=e[o];n.type===u.DYNAMIC?n.applyGravity(this.gravity):n.type===u.KINEMATIC&&n.updateKinematic(t)}},w.prototype.step=function(t,e,i){if(i=i||10,0===(e=e||0))this.saveKinematicAndApplyGravity(t),this.internalStep(t),this.time+=t,this.substeps=1;else{for(this.saveKinematicAndApplyGravity(e),this.accumulator+=e,this.substeps=0;this.accumulator>=t&&this.substeps<i;)this.internalStep(t),this.accumulator-=t,this.substeps++;for(var o=this.accumulator%t/t,n=0;n!==this.bodies.length;n++){var s=this.bodies[n];s.previousPosition.lerp(s.position,o,s.interpolatedPosition),s.previousQuaternion.slerp(s.quaternion,o,s.interpolatedQuaternion),s.previousQuaternion.normalize()}this.time+=e}this.clearForces()};var x,B,S,A,C,E,M={type:"postStep"},_={type:"preStep"},z={type:"collide",body:null,contact:null},F=[],R=[],T=[],P=[];new i,new i,new i,new i,new i,new i,new i,new i,new i,new o,new o,new o,new i,w.prototype.internalStep=function(t){this.dt=t;var e,i=this.contacts,o=T,n=P,s=this.numObjects(),r=this.bodies,a=this.solver,l=this.doProfiling,h=this.profile,p=u.DYNAMIC,c=this.constraints,d=R,y=0;l&&(e=performance.now()),y=0;for(var v=this.subsystems.length;y!==v;y++)this.subsystems[y].update();l&&(e=performance.now()),o.length=0,n.length=0,this.broadphase.collisionPairs(this,o,n),l&&(h.broadphase=performance.now()-e);var f=c.length;for(y=0;y!==f;y++)if(!(q=c[y]).collideConnected)for(var m=o.length-1;m>=0;m-=1)(q.bodyA===o[m]&&q.bodyB===n[m]||q.bodyB===o[m]&&q.bodyA===n[m])&&(o.splice(m,1),n.splice(m,1));this.collisionMatrixTick(),l&&(e=performance.now());var w=F,g=i.length;for(y=0;y!==g;y++)w.push(i[y]);i.length=0;var b=this.frictionEquations.length;for(y=0;y!==b;y++)d.push(this.frictionEquations[y]);for(this.frictionEquations.length=0,this.narrowphase.getContacts(o,n,this,i,w,this.frictionEquations,d),l&&(h.narrowphase=performance.now()-e),l&&(e=performance.now()),y=0;y<this.frictionEquations.length;y++)a.addEquation(this.frictionEquations[y]);for(var x=i.length,B=0;B!==x;B++){var S=(q=i[B]).bi,A=q.bj,C=q.si,E=q.sj;C.material&&E.material?C.material.restitution>=0&&E.material.restitution>=0&&(q.restitution=C.material.restitution*E.material.restitution):S.material&&A.material&&S.material.restitution>=0&&A.material.restitution>=0&&(q.restitution=S.material.restitution*A.material.restitution),a.addEquation(q),S.allowSleep&&S.type===u.DYNAMIC&&S.sleepState===u.SLEEPING&&A.sleepState===u.AWAKE&&A.type!==u.STATIC&&A.velocity.norm2()+A.angularVelocity.norm2()>=2*Math.pow(A.sleepSpeedLimit,2)&&(S._wakeUpAfterNarrowphase=!0),A.allowSleep&&A.type===u.DYNAMIC&&A.sleepState===u.SLEEPING&&S.sleepState===u.AWAKE&&S.type!==u.STATIC&&S.velocity.norm2()+S.angularVelocity.norm2()>=2*Math.pow(S.sleepSpeedLimit,2)&&(A._wakeUpAfterNarrowphase=!0),this.collisionMatrix.set(S,A,!0),this.collisionMatrixPrevious.get(S,A)||(z.body=A,z.contact=q,S.dispatchEvent(z),z.body=S,A.dispatchEvent(z)),this.bodyOverlapKeeper.set(S.id,A.id),this.shapeOverlapKeeper.set(C.id,E.id)}for(this.emitContactEvents(),l&&(h.makeContactConstraints=performance.now()-e,e=performance.now()),y=0;y!==s;y++)(S=r[y])._wakeUpAfterNarrowphase&&(S.wakeUp(),S._wakeUpAfterNarrowphase=!1);for(f=c.length,y=0;y!==f;y++){var q;(q=c[y]).update(),m=0;for(var I=q.equations.length;m!==I;m++){var V=q.equations[m];a.addEquation(V)}}for(a.solve(t,this),l&&(h.solve=performance.now()-e),a.removeAllEquations(),this.dispatchEvent(_),y=0;y!==s;y++)(S=r[y]).preStep&&S.preStep.call(S);l&&(e=performance.now());var L=this.stepnumber%(this.quatNormalizeSkip+1)==0,N=this.quatNormalizeFast;for(y=0;y!==s;y++)r[y].integrate(t,L,N);var W=Math.pow;for(y=0;y!==s;y++)if((S=r[y]).type&p){var j=W(1-S.linearDamping,t),k=S.velocity;k.mult(j,k);var O=S.angularVelocity;if(O){var D=W(1-S.angularDamping,t);O.mult(D,O)}}for(this.broadphase.dirty=!0,l&&(h.integrate=performance.now()-e),this.time+=t,this.stepnumber+=1,this.dispatchEvent(M),y=0;y!==s;y++){var U=(S=r[y]).postStep;U&&U.call(S)}if(this.allowSleep)for(y=0;y!==s;y++)r[y].sleepTick(this.time)},w.prototype.emitContactEvents=(x=[],B=[],S={type:"beginContact",bodyA:null,bodyB:null},A={type:"endContact",bodyA:null,bodyB:null},C={type:"beginShapeContact",bodyA:null,bodyB:null,shapeA:null,shapeB:null},E={type:"endShapeContact",bodyA:null,bodyB:null,shapeA:null,shapeB:null},function(){var t=this.hasAnyEventListener("beginContact"),e=this.hasAnyEventListener("endContact");if((t||e)&&this.bodyOverlapKeeper.getDiff(x,B),t){for(var i=0,o=x.length;i<o;i+=2)S.bodyA=this.getBodyById(x[i]),S.bodyB=this.getBodyById(x[i+1]),this.dispatchEvent(S);S.bodyA=S.bodyB=null}if(e){for(i=0,o=B.length;i<o;i+=2)A.bodyA=this.getBodyById(B[i]),A.bodyB=this.getBodyById(B[i+1]),this.dispatchEvent(A);A.bodyA=A.bodyB=null}x.length=B.length=0;var n=this.hasAnyEventListener("beginShapeContact"),s=this.hasAnyEventListener("endShapeContact");if((n||s)&&this.shapeOverlapKeeper.getDiff(x,B),n){for(i=0,o=x.length;i<o;i+=2){var r=this.getShapeById(x[i]),a=this.getShapeById(x[i+1]);C.shapeA=r,C.shapeB=a,C.bodyA=r.body,C.bodyB=a.body,this.dispatchEvent(C)}C.bodyA=C.bodyB=C.shapeA=C.shapeB=null}if(s){for(i=0,o=B.length;i<o;i+=2)r=this.getShapeById(B[i]),a=this.getShapeById(B[i+1]),E.shapeA=r,E.shapeB=a,E.bodyA=r.body,E.bodyB=a.body,this.dispatchEvent(E);E.bodyA=E.bodyB=E.shapeA=E.shapeB=null}}),w.prototype.clearForces=function(){for(var t=this.bodies,e=t.length,i=0;i!==e;i++){var o=t[i];o.force,o.torque,o.force.set(0,0,0),o.torque.set(0,0,0)}};var q={type:"cc-trigger",event:"",selfBody:null,otherBody:null,selfShape:null,otherShape:null},I={type:"cc-collide",event:"",body:null,selfShape:null,otherShape:null,contacts:null},V=[];w.prototype.emitTriggeredEvents=function(){if(0!=this.substeps){for(var t,e,i=this.triggerDic.getLength();i--;)if(t=this.triggerDic.getKeyByIndex(i),null!=(e=this.triggerDic.getDataByKey(t))){var o=e.si,n=e.sj;this.tm.get(o,n)?q.event="onTriggerStay":(this.tm.set(o,n,!0),q.event="onTriggerEnter"),q.selfShape=o,q.otherShape=n,q.selfBody=o.body,q.otherBody=n.body,o.dispatchEvent(q),q.selfShape=n,q.otherShape=o,q.selfBody=n.body,q.otherBody=o.body,n.dispatchEvent(q)}for(i=this.oldTriggerDic.getLength();i>0;)i--,t=this.oldTriggerDic.getKeyByIndex(i),null==this.triggerDic.getDataByKey(t)&&null!=(e=this.oldTriggerDic.getDataByKey(t))&&(o=e.si,n=e.sj,this.tm.set(o,n,!1),this.oldTriggerDic.del(o.id,n.id)&&i--,q.event="onTriggerExit",q.selfShape=o,q.otherShape=n,q.selfBody=o.body,q.otherBody=n.body,o.dispatchEvent(q),q.selfShape=n,q.otherShape=o,q.selfBody=n.body,q.otherBody=o.body,n.dispatchEvent(q));this.triggerDic.reset()}},w.prototype.emitCollisionEvents=function(){if(0!=this.substeps){for(var t,e,i=this.contacts,o=this.contacts.length;o--;){var n=(p=i[o]).si,s=p.sj,r=this.contactsDic.get(n.id,s.id);null==r&&(r=this.contactsDic.set(n.id,s.id,[])),r.push(p)}for(o=this.contactsDic.getLength();o--;)if(t=this.contactsDic.getKeyByIndex(o),null!=(e=this.contactsDic.getDataByKey(t))){n=e[0].si,s=e[0].sj;var a=n.body,l=s.body;this.cm.get(a,l)?I.event="onCollisionStay":(this.cm.set(a,l,!0),I.event="onCollisionEnter"),I.bi=a,I.contact=e[0],I.contacts=e,I.body=l,I.selfShape=n,I.otherShape=s,a.dispatchEvent(I),I.body=a,I.selfShape=s,I.otherShape=n,l.dispatchEvent(I)}var h=V;for(o=h.length;o--;){var p;n=(p=h[o]).si,s=p.sj,null==this.oldContactsDic.get(n.id,s.id)&&this.oldContactsDic.set(n.id,s.id,p)}for(o=this.oldContactsDic.getLength();o--;)t=this.oldContactsDic.getKeyByIndex(o),null==this.contactsDic.getDataByKey(t)&&(n=(e=this.oldContactsDic.getDataByKey(t)).si,s=e.sj,a=n.body,l=s.body,this.cm.get(a,l)&&(a.isSleeping()&&l.isSleeping()||(this.cm.set(a,l,!1),I.bi=a,I.contact=e,I.event="onCollisionExit",I.body=l,I.selfShape=n,I.otherShape=s,I.contacts.length=0,I.contacts.push(e),a.dispatchEvent(I),I.body=a,I.selfShape=s,I.otherShape=n,l.dispatchEvent(I))));this.contactsDic.reset(),this.oldContactsDic.reset(),F=V,V=this.contacts.slice(),this.contacts.length=0}}},{"../collision/AABB":3,"../collision/ArrayCollisionMatrix":4,"../collision/NaiveBroadphase":7,"../collision/ObjectCollisionMatrix":8,"../collision/OverlapKeeper":9,"../collision/Ray":10,"../collision/RaycastResult":11,"../equations/ContactEquation":20,"../equations/FrictionEquation":22,"../material/ContactMaterial":25,"../material/Material":26,"../math/Quaternion":29,"../math/Vec3":31,"../objects/Body":32,"../shapes/Shape":44,"../solver/GSSolver":47,"../utils/EventTarget":50,"../utils/TupleDictionary":53,"./Narrowphase":56}]},{},[2])(2);var z=t(_.exports),F=new z.Vec3,R=new z.Vec3,T=function(){function t(){this._rigidBody=void 0,this._sharedBody=void 0,this._isEnabled=!1}var i=t.prototype;return i.setAllowSleep=function(t){this.impl.type===z.Body.DYNAMIC&&(this.impl.allowSleep=t,this._wakeUpIfSleep())},i.setMass=function(t){this.impl.type===z.Body.DYNAMIC&&(this.impl.mass=t,this.impl.updateMassProperties(),this._wakeUpIfSleep())},i.setType=function(t){switch(t){case v.DYNAMIC:this.impl.type=z.Body.DYNAMIC,this.impl.allowSleep=this._rigidBody.allowSleep,this.setMass(this._rigidBody.mass);break;case v.KINEMATIC:this.impl.type=z.Body.KINEMATIC,this.impl.mass=0,this.impl.allowSleep=!1,this.impl.sleepState=z.Body.AWAKE,this.impl.updateMassProperties();break;case v.STATIC:default:this.impl.type=z.Body.STATIC,this.impl.mass=0,this.impl.allowSleep=!0,this.impl.updateMassProperties(),this.clearState()}},i.setLinearDamping=function(t){this.impl.linearDamping=t},i.setAngularDamping=function(t){this.impl.angularDamping=t},i.useGravity=function(t){this.impl.useGravity=t,this._wakeUpIfSleep()},i.useCCD=function(t){this.impl.ccdSpeedThreshold=t?.01:-1},i.isUsingCCD=function(){return-1!==this.impl.ccdSpeedThreshold},i.setLinearFactor=function(t){h.copy(this.impl.linearFactor,t),this._wakeUpIfSleep()},i.setAngularFactor=function(t){h.copy(this.impl.angularFactor,t);var e=h.equals(this.impl.angularFactor,h.ZERO);e!==this.impl.fixedRotation&&(this.impl.fixedRotation=e,this.impl.updateMassProperties()),this._wakeUpIfSleep()},i.initialize=function(t){this._rigidBody=t,this._sharedBody=e.instance.physicsWorld.getSharedBody(this._rigidBody.node,this),this._sharedBody.reference=!0,this._sharedBody.wrappedBody=this},i.onLoad=function(){},i.onEnable=function(){this._isEnabled=!0,this.setType(this._rigidBody.type),this.setMass(this._rigidBody.mass),this.setAllowSleep(this._rigidBody.allowSleep),this.setLinearDamping(this._rigidBody.linearDamping),this.setAngularDamping(this._rigidBody.angularDamping),this.useGravity(this._rigidBody.useGravity),this.setLinearFactor(this._rigidBody.linearFactor),this.setAngularFactor(this._rigidBody.angularFactor),this._sharedBody.enabled=!0},i.onDisable=function(){this._isEnabled=!1,this._sharedBody.enabled=!1},i.onDestroy=function(){this._sharedBody.reference=!1,this._rigidBody=null,this._sharedBody=null},i.clearVelocity=function(){this.impl.velocity.setZero(),this.impl.angularVelocity.setZero()},i.clearForces=function(){this.impl.force.setZero(),this.impl.torque.setZero()},i.clearState=function(){this.clearVelocity(),this.clearForces()},i.wakeUp=function(){return this.impl.wakeUp()},i.sleep=function(){return this.impl.sleep()},i.setSleepThreshold=function(t){this.impl.sleepSpeedLimit=t,this._wakeUpIfSleep()},i.getSleepThreshold=function(){return this.impl.sleepSpeedLimit},i.getLinearVelocity=function(t){return h.copy(t,this.impl.velocity),t},i.setLinearVelocity=function(t){this._wakeUpIfSleep(),h.copy(this.impl.velocity,t)},i.getAngularVelocity=function(t){return h.copy(t,this.impl.angularVelocity),t},i.setAngularVelocity=function(t){this._wakeUpIfSleep(),h.copy(this.impl.angularVelocity,t)},i.applyForce=function(t,e){this._sharedBody.syncSceneToPhysics(),this._wakeUpIfSleep(),null==e&&(e=h.ZERO),this.impl.applyForce(h.copy(F,t),h.copy(R,e))},i.applyImpulse=function(t,e){this._sharedBody.syncSceneToPhysics(),this._wakeUpIfSleep(),null==e&&(e=h.ZERO),this.impl.applyImpulse(h.copy(F,t),h.copy(R,e))},i.applyLocalForce=function(t,e){this._sharedBody.syncSceneToPhysics(),this._wakeUpIfSleep(),null==e&&(e=h.ZERO),this.impl.applyLocalForce(h.copy(F,t),h.copy(R,e))},i.applyLocalImpulse=function(t,e){this._sharedBody.syncSceneToPhysics(),this._wakeUpIfSleep(),null==e&&(e=h.ZERO),this.impl.applyLocalImpulse(h.copy(F,t),h.copy(R,e))},i.applyTorque=function(t){this._sharedBody.syncSceneToPhysics(),this._wakeUpIfSleep(),h.add(this.impl.torque,this.impl.torque,t)},i.applyLocalTorque=function(t){this._sharedBody.syncSceneToPhysics(),this._wakeUpIfSleep(),h.copy(F,t),this.impl.vectorToWorldFrame(F,F),h.add(this.impl.torque,this.impl.torque,F)},i.getGroup=function(){return this.impl.collisionFilterGroup},i.setGroup=function(t){this.impl.collisionFilterGroup=t,this._wakeUpIfSleep()},i.addGroup=function(t){this.impl.collisionFilterGroup|=t,this._wakeUpIfSleep()},i.removeGroup=function(t){this.impl.collisionFilterGroup&=~t,this._wakeUpIfSleep()},i.getMask=function(){return this.impl.collisionFilterMask},i.setMask=function(t){this.impl.collisionFilterMask=t,this._wakeUpIfSleep()},i.addMask=function(t){this.impl.collisionFilterMask|=t,this._wakeUpIfSleep()},i.removeMask=function(t){this.impl.collisionFilterMask&=~t,this._wakeUpIfSleep()},i._wakeUpIfSleep=function(){this.impl.isAwake()||this.impl.wakeUp()},n(t,[{key:"isAwake",get:function(){return this.impl.isAwake()}},{key:"isSleepy",get:function(){return this.impl.isSleepy()}},{key:"isSleeping",get:function(){return this.impl.isSleeping()}},{key:"impl",get:function(){return this._sharedBody.body}},{key:"rigidBody",get:function(){return this._rigidBody}},{key:"sharedBody",get:function(){return this._sharedBody}},{key:"isEnabled",get:function(){return this._isEnabled}}]),t}();function P(t,e){t.checkCollisionResponse=!e.queryTrigger,t.collisionFilterGroup=-1,t.collisionFilterMask=e.mask}function q(t,e){t._assign(e.hitPointWorld,e.distance,g(e.shape).collider,e.hitNormalWorld)}function I(t){t.aabbNeedsUpdate=!0,t.updateMassProperties(),t.updateBoundingRadius()}var V={type:"onTriggerEnter",selfCollider:null,otherCollider:null,impl:null},L=new z.Quaternion,N=new z.Vec3,W=new z.Vec3,j=function(){function t(){this._collider=void 0,this._shape=void 0,this._offset=new z.Vec3,this._orient=new z.Quaternion,this._index=-1,this._sharedBody=void 0,this.onTriggerListener=this._onTrigger.bind(this),this._isBinding=!1}var i=t.prototype;return i.updateEventListener=function(){},i.setMaterial=function(i){var o=null==i?e.instance.defaultMaterial:i;null==t.idToMaterial[o.id]&&(t.idToMaterial[o.id]=new z.Material(o.id)),this._shape.material=t.idToMaterial[o.id];var n=this._shape.material;n.friction=o.friction,n.restitution=o.restitution;var s=z.CC_CONFIG.correctInelastic;n.correctInelastic=0===n.restitution?s:0},i.setAsTrigger=function(t){this._shape.collisionResponse=!t,this._index>=0&&this._body.updateHasTrigger()},i.setCenter=function(t){this._setCenter(t),this._index>=0&&I(this._body)},i.setAttachedBody=function(t){if(t){if(this._sharedBody){if(this._sharedBody.wrappedBody===t.body)return;this._sharedBody.reference=!1}this._sharedBody=e.instance.physicsWorld.getSharedBody(t.node),this._sharedBody.reference=!0}else this._sharedBody&&(this._sharedBody.reference=!1),this._sharedBody=e.instance.physicsWorld.getSharedBody(this._collider.node),this._sharedBody.reference=!0},i.getAABB=function(t){p.copy(L,this._collider.node.worldRotation),this._shape.calculateWorldAABB(z.Vec3.ZERO,L,N,W),h.subtract(t.halfExtents,W,N),h.multiplyScalar(t.halfExtents,t.halfExtents,.5),h.add(t.center,this._collider.node.worldPosition,this._collider.center)},i.getBoundingSphere=function(t){t.radius=this._shape.boundingSphereRadius,h.add(t.center,this._collider.node.worldPosition,this._collider.center)},i.initialize=function(t){this._collider=t,this._isBinding=!0,this._sharedBody=e.instance.physicsWorld.getSharedBody(this._collider.node),this._sharedBody.reference=!0,this.onComponentSet(),b(this._shape,this),this._shape.addEventListener("cc-trigger",this.onTriggerListener)},i.onComponentSet=function(){},i.onLoad=function(){this.setMaterial(this._collider.sharedMaterial),this.setCenter(this._collider.center),this.setAsTrigger(this._collider.isTrigger)},i.onEnable=function(){this._sharedBody.addShape(this),this._sharedBody.enabled=!0},i.onDisable=function(){this._sharedBody.removeShape(this),this._sharedBody.enabled=!1},i.onDestroy=function(){this._sharedBody.reference=!1,this._shape.removeEventListener("cc-trigger",this.onTriggerListener),delete z.World.idToShapeMap[this._shape.id],this._sharedBody=null,b(this._shape,null),this._offset=null,this._orient=null,this._shape=null,this._collider=null,this.onTriggerListener=null},i.getGroup=function(){return this._body.collisionFilterGroup},i.setGroup=function(t){this._body.collisionFilterGroup=t,this._body.isAwake()||this._body.wakeUp()},i.addGroup=function(t){this._body.collisionFilterGroup|=t,this._body.isAwake()||this._body.wakeUp()},i.removeGroup=function(t){this._body.collisionFilterGroup&=~t,this._body.isAwake()||this._body.wakeUp()},i.getMask=function(){return this._body.collisionFilterMask},i.setMask=function(t){this._body.collisionFilterMask=t,this._body.isAwake()||this._body.wakeUp()},i.addMask=function(t){this._body.collisionFilterMask|=t,this._body.isAwake()||this._body.wakeUp()},i.removeMask=function(t){this._body.collisionFilterMask&=~t,this._body.isAwake()||this._body.wakeUp()},i.setScale=function(){this._setCenter(this._collider.center)},i.setIndex=function(t){this._index=t},i.setOffsetAndOrient=function(t,e){h.copy(t,this._offset),p.copy(e,this._orient),this._offset=t,this._orient=e},i._setCenter=function(t){var e=this._offset;h.subtract(e,this._sharedBody.node.worldPosition,this._collider.node.worldPosition),h.add(e,e,t),h.multiply(e,e,this._collider.node.worldScale)},i._onTrigger=function(t){V.type=t.event;var e=g(t.selfShape),i=g(t.otherShape);e&&e.collider.needTriggerEvent&&(V.selfCollider=e.collider,V.otherCollider=i?i.collider:null,V.impl=t,this._collider.emit(V.type,V))},n(t,[{key:"impl",get:function(){return this._shape}},{key:"collider",get:function(){return this._collider}},{key:"attachedRigidBody",get:function(){return this._sharedBody.wrappedBody?this._sharedBody.wrappedBody.rigidBody:null}},{key:"sharedBody",get:function(){return this._sharedBody}},{key:"_body",get:function(){return this._sharedBody.body}}]),t}();j.idToMaterial={};var k=new p,O=function(){function t(t){this.impl=null,this.event=void 0,this.event=t}var e=t.prototype;return e.getLocalPointOnA=function(t){this.impl&&h.copy(t,this.impl.rj)},e.getLocalPointOnB=function(t){this.impl&&h.copy(t,this.impl.ri)},e.getWorldPointOnA=function(t){this.impl&&h.add(t,this.impl.rj,this.impl.bj.position)},e.getWorldPointOnB=function(t){this.impl&&h.add(t,this.impl.ri,this.impl.bi.position)},e.getLocalNormalOnA=function(t){this.impl&&(this.getWorldNormalOnA(t),p.conjugate(k,this.impl.bi.quaternion),h.transformQuat(t,t,k))},e.getLocalNormalOnB=function(t){this.impl&&(p.conjugate(k,this.impl.bj.quaternion),h.transformQuat(t,this.impl.ni,k))},e.getWorldNormalOnA=function(t){this.impl&&(this.getWorldNormalOnB(t),this.isBodyA||h.negate(t,t))},e.getWorldNormalOnB=function(t){this.impl&&h.copy(t,this.impl.ni)},n(t,[{key:"isBodyA",get:function(){if(this.impl){var t=this.event.selfCollider.shape.impl,e=this.impl.bj;return t.body.id===e.id}return!1}}]),t}(),D=new h,U=new p,G=[],H={type:"onCollisionEnter",selfCollider:null,otherCollider:null,contacts:[],impl:null},Q=function(){function t(t,i){this.node=void 0,this.wrappedWorld=void 0,this.body=void 0,this.wrappedShapes=[],this.wrappedJoints0=[],this.wrappedJoints1=[],this.wrappedBody=null,this.index=-1,this.ref=0,this.onCollidedListener=this.onCollided.bind(this),this.wrappedWorld=i,this.node=t,this.body=new z.Body,b(this.body,this),this.body.collisionFilterGroup=e.PhysicsGroup.DEFAULT,this.body.sleepSpeedLimit=e.instance.sleepThreshold,this.body.material=this.wrappedWorld.impl.defaultMaterial,this.body.addEventListener("cc-collide",this.onCollidedListener)}t.getSharedBody=function(i,o,n){var s,r=i.uuid;if(t.sharedBodesMap.has(r))s=t.sharedBodesMap.get(r);else{s=new t(i,o);var a=f.DEFAULT,l=e.instance.collisionMatrix[a];s.body.collisionFilterGroup=a,s.body.collisionFilterMask=l,s.body.position=new z.Vec3(i.worldPosition.x,i.worldPosition.y,i.worldPosition.z),s.body.quaternion=new z.Quaternion(i.worldRotation.x,i.worldRotation.y,i.worldRotation.z,i.worldRotation.w),t.sharedBodesMap.set(i.uuid,s)}if(n){s.wrappedBody=n;var h=n.rigidBody.group,p=e.instance.collisionMatrix[h];s.body.collisionFilterGroup=h,s.body.collisionFilterMask=p,s.body.position=new z.Vec3(i.worldPosition.x,i.worldPosition.y,i.worldPosition.z),s.body.quaternion=new z.Quaternion(i.worldRotation.x,i.worldRotation.y,i.worldRotation.z,i.worldRotation.w)}return s};var i=t.prototype;return i.addShape=function(t){if(this.wrappedShapes.indexOf(t)<0){var e=this.body.shapes.length;this.body.addShape(t.impl),this.wrappedShapes.push(t),t.setIndex(e);var i=this.body.shapeOffsets[e],o=this.body.shapeOrientations[e];t.setOffsetAndOrient(i,o),this.body.isSleeping()&&this.body.wakeUp()}},i.removeShape=function(t){var e=this.wrappedShapes.indexOf(t);e>=0&&(s(this.wrappedShapes,e),this.body.removeShape(t.impl),t.setIndex(-1),this.body.isSleeping()&&this.body.wakeUp())},i.addJoint=function(t,e){e?this.wrappedJoints1.indexOf(t)<0&&this.wrappedJoints1.push(t):this.wrappedJoints0.indexOf(t)<0&&this.wrappedJoints0.push(t)},i.removeJoint=function(t,e){if(e){var i=this.wrappedJoints1.indexOf(t);i>=0&&s(this.wrappedJoints1,i)}else{var o=this.wrappedJoints0.indexOf(t);o>=0&&s(this.wrappedJoints0,o)}},i.syncSceneToPhysics=function(){var t=this.node,e=this.body;t.hasChangedFlags&&(e.isSleeping()&&e.wakeUp(),h.copy(e.position,t.worldPosition),p.copy(e.quaternion,t.worldRotation),e.aabbNeedsUpdate=!0,t.hasChangedFlags&S.SCALE&&this.syncScale())},i.syncPhysicsToScene=function(){var t=this.node,e=this.body;e.type===v.DYNAMIC&&(e.isSleeping()||(h.copy(D,e.position),p.copy(U,e.quaternion),t.worldPosition=D,t.worldRotation=U))},i.syncInitial=function(){var t=this.node,e=this.body;h.copy(e.position,t.worldPosition),p.copy(e.quaternion,t.worldRotation),h.copy(e.previousPosition,t.worldPosition),p.copy(e.previousQuaternion,t.worldRotation),e.aabbNeedsUpdate=!0,this.syncScale(),e.isSleeping()&&e.wakeUp()},i.syncScale=function(){for(var t=0;t<this.wrappedShapes.length;t++)this.wrappedShapes[t].setScale(this.node.worldScale);for(var e=0;e<this.wrappedJoints0.length;e++)this.wrappedJoints0[e].updateScale0();for(var i=0;i<this.wrappedJoints1.length;i++)this.wrappedJoints1[i].updateScale1();I(this.body)},i.destroy=function(){b(this.body,null),this.body.removeEventListener("cc-collide",this.onCollidedListener),t.sharedBodesMap.delete(this.node.uuid),delete z.World.idToBodyMap[this.body.id],this.node=null,this.wrappedWorld=null,this.body=null,this.wrappedShapes=null,this.wrappedJoints0=null,this.wrappedJoints1=null,this.onCollidedListener=null},i.onCollided=function(t){H.type=t.event;var e=g(t.selfShape),i=g(t.otherShape);if(e&&e.collider.needCollisionEvent){G.push.apply(G,H.contacts),H.contacts.length=0,H.impl=t,H.selfCollider=e.collider,H.otherCollider=i?i.collider:null;var o=0;if("onCollisionExit"!==H.type)for(o=0;o<t.contacts.length;o++){var n=t.contacts[o];if(G.length>0){var s=G.pop();s.impl=n,H.contacts.push(s)}else{var r=new O(H);r.impl=n,H.contacts.push(r)}}for(o=0;o<this.wrappedShapes.length;o++)this.wrappedShapes[o].collider.emit(H.type,H)}},n(t,[{key:"enabled",set:function(t){t?this.index<0&&(this.index=this.wrappedWorld.bodies.length,this.wrappedWorld.addSharedBody(this),this.syncInitial()):this.index>=0&&(0===this.wrappedShapes.length&&null==this.wrappedBody||0===this.wrappedShapes.length&&null!=this.wrappedBody&&!this.wrappedBody.isEnabled)&&(this.body.sleep(),this.index=-1,this.wrappedWorld.removeSharedBody(this))}},{key:"reference",set:function(t){t?this.ref++:this.ref--,0===this.ref&&this.destroy()}}]),t}();Q.sharedBodesMap=new Map;var X=new c,Y=function(){var t=e.prototype;function e(){this.bodies=[],this.constraints=[],this._world=void 0,this._debugLineCount=0,this._MAX_DEBUG_LINE_COUNT=16384,this._debugDrawFlags=m.NONE,this._debugConstraintSize=.3,this._aabbColor=new u(0,255,255,255),this._wireframeColor=new u(255,0,255,255),this._world=new z.World,this._world.broadphase=new z.NaiveBroadphase,this._world.solver.iterations=10,this._world.solver.tolerance=1e-4,this._world.defaultContactMaterial.contactEquationStiffness=1e6,this._world.defaultContactMaterial.frictionEquationStiffness=1e6,this._world.defaultContactMaterial.contactEquationRelaxation=3,this._world.defaultContactMaterial.frictionEquationRelaxation=3}return t.setDefaultMaterial=function(t){this._world.defaultMaterial.friction=t.friction,this._world.defaultMaterial.restitution=t.restitution,null!=j.idToMaterial[t.id]&&(j.idToMaterial[t.id]=this._world.defaultMaterial)},t.setAllowSleep=function(t){this._world.allowSleep=t},t.setGravity=function(t){h.copy(this._world.gravity,t)},t.sweepBox=function(){return r(9641),!1},t.sweepBoxClosest=function(){return r(9641),!1},t.sweepSphere=function(){return r(9641),!1},t.sweepSphereClosest=function(){return r(9641),!1},t.sweepCapsule=function(){return r(9641),!1},t.sweepCapsuleClosest=function(){return r(9641),!1},t.destroy=function(){(this.constraints.length||this.bodies.length)&&a("You should destroy all physics component first."),this._world.broadphase=null,this._world=null},t.emitEvents=function(){this._world.emitTriggeredEvents(),this._world.emitCollisionEvents()},t.syncSceneToPhysics=function(){for(var t=0;t<this.bodies.length;t++)this.bodies[t].syncSceneToPhysics()},t.syncAfterEvents=function(){this.syncSceneToPhysics()},t.step=function(t,e,i){if(0!==this.bodies.length){this._world.step(t,e,i);for(var o=0;o<this.bodies.length;o++)this.bodies[o].syncPhysicsToScene();this._debugDraw()}},t.raycastClosest=function(t,i,o){J(t,i.maxDistance),P($,i);var n=this._world.raycastClosest(K,Z,$,e.rayResult);return n&&q(o,e.rayResult),n},t.raycast=function(t,e,i,o){return J(t,e.maxDistance),P($,e),this._world.raycastAll(K,Z,$,(function(t){var e=i.add();q(e,t),o.push(e)}))},t.getSharedBody=function(t,e){return Q.getSharedBody(t,this,e)},t.addSharedBody=function(t){this.bodies.indexOf(t)<0&&(this.bodies.push(t),this._world.addBody(t.body))},t.removeSharedBody=function(t){var e=this.bodies.indexOf(t);e>=0&&(s(this.bodies,e),this._world.remove(t.body))},t.addConstraint=function(t){this.constraints.indexOf(t)<0&&(this.constraints.push(t),this._world.addConstraint(t.impl))},t.removeConstraint=function(t){var e=this.constraints.indexOf(t);e>=0&&(s(this.constraints,e),this._world.removeConstraint(t.impl))},t._getDebugRenderer=function(){var t,e=null==(t=E.root.mainWindow)?void 0:t.cameras;return e?0===e.length?null:e[0]?(e[0].initGeometryRenderer(),e[0].geometryRenderer):null:null},t._debugDraw=function(){var t=this._getDebugRenderer();if(t&&(this._debugLineCount=0,this._debugDrawFlags&m.AABB))for(var e=0;e<this.bodies.length;e++)for(var i=this.bodies[e],o=0;o<i.wrappedShapes.length;o++){var n=i.wrappedShapes[o];this._debugLineCount+12<this._MAX_DEBUG_LINE_COUNT&&(this._debugLineCount+=12,n.getAABB(X),t.addBoundingBox(X,this._aabbColor))}},n(e,[{key:"impl",get:function(){return this._world}},{key:"debugDrawFlags",get:function(){return this._debugDrawFlags},set:function(t){this._debugDrawFlags=t}},{key:"debugDrawConstraintSize",get:function(){return this._debugConstraintSize},set:function(t){this._debugConstraintSize=t}}]),e}();Y.rayResult=new z.RaycastResult;var K=new z.Vec3,Z=new z.Vec3;function J(t,e){h.copy(K,t.o),t.computeHit(Z,e)}var $={checkCollisionResponse:!1,collisionFilterGroup:-1,collisionFilterMask:-1,skipBackfaces:!0},tt=function(t){function i(){var e;return(e=t.call(this)||this).halfExtent=void 0,e.halfExtent=new z.Vec3(.5,.5,.5),e._shape=new z.Box(e.halfExtent.clone()),e}l(i,t);var o=i.prototype;return o.updateSize=function(){h.multiplyScalar(this.halfExtent,this.collider.size,.5);var t=x(B.set(this.collider.node.worldScale)),i=this.halfExtent.x*t.x,o=this.halfExtent.y*t.y,n=this.halfExtent.z*t.z,s=e.instance.minVolumeSize;this.impl.halfExtents.x=d(i,s,Number.MAX_VALUE),this.impl.halfExtents.y=d(o,s,Number.MAX_VALUE),this.impl.halfExtents.z=d(n,s,Number.MAX_VALUE),this.impl.updateConvexPolyhedronRepresentation(),-1!==this._index&&I(this._body)},o.onLoad=function(){t.prototype.onLoad.call(this),this.updateSize()},o.setScale=function(e){t.prototype.setScale.call(this,e),this.updateSize()},n(i,[{key:"collider",get:function(){return this._collider}},{key:"impl",get:function(){return this._shape}}]),i}(j),et=function(t){l(o,t);var i=o.prototype;function o(e){var i;return void 0===e&&(e=.5),(i=t.call(this)||this)._shape=new z.Sphere(e),i}return i.updateRadius=function(){var t=Math.abs(y(this.collider.node.worldScale));this.impl.radius=d(this.collider.radius*Math.abs(t),e.instance.minVolumeSize,Number.MAX_VALUE),this.impl.updateBoundingSphereRadius(),-1!==this._index&&I(this._body)},i.onLoad=function(){t.prototype.onLoad.call(this),this.updateRadius()},i.setScale=function(e){t.prototype.setScale.call(this,e),this.updateRadius()},n(o,[{key:"collider",get:function(){return this._collider}},{key:"impl",get:function(){return this._shape}}]),o}(j),it=new z.Vec3,ot=function(t){function e(){return t.apply(this,arguments)||this}l(e,t);var i=e.prototype;return i.setMesh=function(t){if(this._isBinding){var e=t;if(null!=this._shape)if(e&&e.renderingSubMeshes.length>0){var i=e.renderingSubMeshes[0].geometricInfo.positions,o=e.renderingSubMeshes[0].geometricInfo.indices;o instanceof Uint8Array?this.updateProperties(i,new Uint16Array(o)):o instanceof Uint16Array?this.updateProperties(i,o):o instanceof Uint32Array?this.updateProperties(i,new Uint16Array(o)):this.updateProperties(i,new Uint16Array)}else this.updateProperties(new Float32Array,new Uint16Array);else if(e&&e.renderingSubMeshes.length>0){var n=e.renderingSubMeshes[0].geometricInfo.positions,s=e.renderingSubMeshes[0].geometricInfo.indices;this._shape=new z.Trimesh(n,s)}else this._shape=new z.Trimesh(new Float32Array,new Uint16Array)}},i.onComponentSet=function(){this.setMesh(this.collider.mesh)},i.onLoad=function(){t.prototype.onLoad.call(this),this.setMesh(this.collider.mesh)},i.setScale=function(e){t.prototype.setScale.call(this,e),h.copy(it,e),this.impl.setScale(it)},i.updateProperties=function(t,e){this.impl.vertices=new Float32Array(t),this.impl.indices=new Int16Array(e),this.impl.normals=new Float32Array(e.length),this.impl.aabb=new z.AABB,this.impl.edges=[],this.impl.tree=new z.Octree(new z.AABB),this.impl.updateEdges(),this.impl.updateNormals(),this.impl.updateAABB(),this.impl.updateBoundingSphereRadius(),this.impl.updateTree(),this.impl.setScale(this.impl.scale),this._index>=0&&I(this._body)},n(e,[{key:"collider",get:function(){return this._collider}},{key:"impl",get:function(){return this._shape}}]),e}(j),nt=function(t){l(i,t);var e=i.prototype;function i(e,i,o){var n;return void 0===e&&(e=.5),void 0===i&&(i=2),void 0===o&&(o=w.Y_AXIS),(n=t.call(this)||this)._shape=new z.Cylinder(e,e,i,z.CC_CONFIG.numSegmentsCylinder,o===w.Y_AXIS),n}return e.setRadius=function(){this.updateProperties(this.collider.radius,this.collider.height,z.CC_CONFIG.numSegmentsCylinder,this.collider.direction,this.collider.node.worldScale),-1!==this._index&&I(this._body)},e.setHeight=function(){this.updateProperties(this.collider.radius,this.collider.height,z.CC_CONFIG.numSegmentsCylinder,this.collider.direction,this.collider.node.worldScale),-1!==this._index&&I(this._body)},e.setDirection=function(){this.updateProperties(this.collider.radius,this.collider.height,z.CC_CONFIG.numSegmentsCylinder,this.collider.direction,this.collider.node.worldScale),-1!==this._index&&I(this._body)},e.onLoad=function(){t.prototype.onLoad.call(this),this.setRadius(this.collider.radius)},e.setScale=function(e){t.prototype.setScale.call(this,e),this.setRadius(this.collider.radius)},e.updateProperties=function(t,e,i,o,n){var s=e,r=t,a=Math.cos,l=Math.sin,h=Math.abs,p=Math.max;1===o?(s=h(n.y)*e,r=p(h(n.x),h(n.z))*t):2===o?(s=h(n.z)*e,r=p(h(n.x),h(n.y))*t):(s=h(n.x)*e,r=p(h(n.y),h(n.z))*t);var c=i,u=s/2,d=[],y=[],v=[],f=2*Math.PI/c;if(1===o){for(var m=[1],w=[0],g=0;g<c;g++){var b=r*a(f*g),x=r*l(f*g);d.push(new z.Vec3(b,u,x)),d.push(new z.Vec3(b,-u,x)),g<c-1?(y.push([2*g+2,2*g+3,2*g+1,2*g]),w.push(2*g+2),m.push(2*g+3)):y.push([0,1,2*g+1,2*g]),(c%2==1||g<c/2)&&v.push(new z.Vec3(a(f*(g+.5)),0,l(f*(g+.5))))}y.push(m);for(var B=[],S=0;S<w.length;S++)B.push(w[w.length-S-1]);y.push(B),v.push(new z.Vec3(0,1,0))}else if(2===o){for(var A=[0],C=[1],E=0;E<c;E++){var M=r*a(f*E),_=r*l(f*E);d.push(new z.Vec3(M,_,u)),d.push(new z.Vec3(M,_,-u)),E<c-1?(y.push([2*E,2*E+1,2*E+3,2*E+2]),A.push(2*E+2),C.push(2*E+3)):y.push([2*E,2*E+1,0,1]),(c%2==1||E<c/2)&&v.push(new z.Vec3(a(f*(E+.5)),l(f*(E+.5)),0))}y.push(A);for(var F=[],R=0;R<C.length;R++)F.push(C[C.length-R-1]);y.push(F),v.push(new z.Vec3(0,0,1))}else{for(var T=[0],P=[1],q=0;q<c;q++){var I=r*a(f*q),V=r*l(f*q);d.push(new z.Vec3(u,I,V)),d.push(new z.Vec3(-u,I,V)),q<c-1?(y.push([2*q,2*q+1,2*q+3,2*q+2]),T.push(2*q+2),P.push(2*q+3)):y.push([2*q,2*q+1,0,1]),(c%2==1||q<c/2)&&v.push(new z.Vec3(0,a(f*(q+.5)),l(f*(q+.5))))}y.push(T);for(var L=[],N=0;N<P.length;N++)L.push(P[P.length-N-1]);y.push(L),v.push(new z.Vec3(1,0,0))}this.impl.vertices=d,this.impl.faces=y,this.impl.uniqueAxes=v,this.impl.worldVerticesNeedsUpdate=!0,this.impl.worldFaceNormalsNeedsUpdate=!0,this.impl.computeNormals(),this.impl.computeEdges(),this.impl.updateBoundingSphereRadius()},n(i,[{key:"collider",get:function(){return this._collider}},{key:"impl",get:function(){return this._shape}}]),i}(j),st=new h,rt=new h,at=function(t){l(i,t);var e=i.prototype;function i(e,i,o){var n;return void 0===e&&(e=.5),void 0===i&&(i=1),void 0===o&&(o=w.Y_AXIS),(n=t.call(this)||this)._shape=new z.Cylinder(0,e,i,z.CC_CONFIG.numSegmentsCone,o===w.Y_AXIS),n}return e.setRadius=function(){this.updateProperties(this.collider.radius,this.collider.height,z.CC_CONFIG.numSegmentsCone,this.collider.direction,this.collider.node.worldScale),-1!==this._index&&I(this._body)},e.setHeight=function(){this.updateProperties(this.collider.radius,this.collider.height,z.CC_CONFIG.numSegmentsCone,this.collider.direction,this.collider.node.worldScale),-1!==this._index&&I(this._body)},e.setDirection=function(){this.updateProperties(this.collider.radius,this.collider.height,z.CC_CONFIG.numSegmentsCone,this.collider.direction,this.collider.node.worldScale),-1!==this._index&&I(this._body)},e.onLoad=function(){t.prototype.onLoad.call(this),this.setRadius(this.collider.radius)},e.setScale=function(e){t.prototype.setScale.call(this,e),this.setRadius(this.collider.radius)},e.updateProperties=function(t,e,i,o,n){var s=e,r=t,a=Math.cos,l=Math.sin,p=Math.abs,c=Math.max;1===o?(s=p(n.y)*e,r=c(p(n.x),p(n.z))*t):2===o?(s=p(n.z)*e,r=c(p(n.x),p(n.y))*t):(s=p(n.x)*e,r=c(p(n.y),p(n.z))*t);var u=i,d=s/2,y=[],v=[],f=[],m=2*Math.PI/u;if(1===o){var w=[];v.push(w),y.push(new z.Vec3(0,d,0));for(var g=0;g<u;g++){var b=r*a(m*g),x=r*l(m*g);y.push(new z.Vec3(b,-d,x))}for(var B=0;B<u;B++){0!==B&&w.push(B);var S;S=B<u-1?[0,B+2,B+1]:[0,1,B+1],v.push(S),h.subtract(st,y[0],y[S[1]]),h.subtract(rt,y[S[2]],y[S[1]]),h.cross(st,rt,st),st.normalize(),f.push(new z.Vec3(st.x,st.y,st.z))}f.push(new z.Vec3(0,-1,0))}else if(2===o){var A=[];v.push(A),y.push(new z.Vec3(0,0,d));for(var C=0;C<u;C++){var E=r*a(m*C),M=r*l(m*C);y.push(new z.Vec3(E,M,-d))}for(var _=0;_<u;_++){0!==_&&A.push(u-_);var F;F=_<u-1?[0,_+1,_+2]:[0,_+1,1],v.push(F),h.subtract(st,y[0],y[F[1]]),h.subtract(rt,y[F[2]],y[F[1]]),h.cross(st,st,rt),st.normalize(),f.push(new z.Vec3(st.x,st.y,st.z))}f.push(new z.Vec3(0,0,-1))}else{var R=[];v.push(R),y.push(new z.Vec3(d,0,0));for(var T=0;T<u;T++){var P=r*a(m*T),q=r*l(m*T);y.push(new z.Vec3(-d,P,q))}for(var I=0;I<u;I++){0!==I&&R.push(u-I);var V;V=I<u-1?[0,I+1,I+2]:[0,I+1,1],v.push(V),h.subtract(st,y[0],y[V[1]]),h.subtract(rt,y[V[2]],y[V[1]]),h.cross(st,st,rt),st.normalize(),f.push(new z.Vec3(st.x,st.y,st.z))}f.push(new z.Vec3(-1,0,0))}this.impl.vertices=y,this.impl.faces=v,this.impl.uniqueAxes=f,this.impl.worldVerticesNeedsUpdate=!0,this.impl.worldFaceNormalsNeedsUpdate=!0,this.impl.computeNormals(),this.impl.computeEdges(),this.impl.updateBoundingSphereRadius()},n(i,[{key:"collider",get:function(){return this._collider}},{key:"impl",get:function(){return this._shape}}]),i}(j),lt=new z.AABB,ht=new z.AABB,pt=new z.Transform;z.Heightfield.prototype.calculateWorldAABB=function(t,e,i,o){var n=pt,s=ht;h.copy(n.position,t),p.copy(n.quaternion,e);var r=this.elementSize,a=this.data;lt.lowerBound.set(0,0,this.minValue),lt.upperBound.set((a.length-1)*r,(a[0].length-1)*r,this.maxValue),lt.toWorldFrame(n,s),i.copy(s.lowerBound),o.copy(s.upperBound)};var ct,ut=function(t){l(i,t);var e=i.prototype;function i(){var e;return(e=t.call(this)||this).data=void 0,e.options=void 0,e._terrainID=void 0,e.data=[[]],e.options={elementSize:0},e._terrainID="",e}return e.setTerrain=function(t){if(t){if(this._terrainID!==t._uuid){var e=t,i=e.getVertexCountI(),o=e.getVertexCountJ();this._terrainID=e._uuid,this.data.length=i-1;for(var n=0;n<i;n++){null==this.data[n]&&(this.data[n]=[]),this.data[n].length=o-1;for(var s=0;s<o;s++)this.data[n][s]=e.getHeight(n,o-1-s)}this.options.elementSize=e.tileSize,this.updateProperties(this.data,this.options.elementSize)}}else""!==this._terrainID&&(this._terrainID="",this.data.length=1,this.data[0]=this.data[0]||[],this.data[0].length=0,this.options.elementSize=0,this.updateProperties(this.data,this.options.elementSize))},e.onComponentSet=function(){var t=this.collider.terrain;if(t){for(var e=t.getVertexCountI(),i=t.getVertexCountJ(),o=0;o<e;o++){null==this.data[o]&&(this.data[o]=[]);for(var n=0;n<i;n++)this.data[o][n]=t.getHeight(o,i-1-n)}this.options.elementSize=t.tileSize,this._terrainID=t._uuid}this._shape=new z.Heightfield(this.data,this.options)},e.onLoad=function(){t.prototype.onLoad.call(this),this.setTerrain(this.collider.terrain)},e.updateProperties=function(t,e){var i=this.impl;i.data=t,i.elementSize=e,i.updateMinValue(),i.updateMaxValue(),i.updateBoundingSphereRadius(),i.update(),this._index>=0&&I(this._body)},e._setCenter=function(t){var e=this.collider.terrain;if(e){p.fromEuler(this._orient,-90,0,0);var i=this._offset;h.set(i,0,0,(e.getVertexCountJ()-1)*e.tileSize),h.add(i,i,t)}},n(i,[{key:"collider",get:function(){return this._collider}},{key:"impl",get:function(){return this._shape}}]),i}(j),dt=function(t){function e(){for(var e,i=arguments.length,o=new Array(i),n=0;n<i;n++)o[n]=arguments[n];return(e=t.call.apply(t,[this].concat(o))||this).vertices=[],e}l(e,t);var o=e.prototype;return o.setShapeType=function(){this._isBinding},o.setVertices=function(t){var e=this.vertices.length;if(4===e){for(var i=this._collider.node.worldScale,o=0;o<e;o++)h.multiply(this.vertices[o],i,t[o]);var n=this.impl;n.computeNormals(),n.computeEdges(),n.updateBoundingSphereRadius()}-1!==this._index&&I(this._body)},o.onComponentSet=function(){if(this.collider.shapeType===i.ESimplexType.TETRAHEDRON){for(var t=0;t<4;t++)this.vertices[t]=new z.Vec3(0,0,0);this._shape=yt(this.vertices)}else i.ESimplexType.VERTEX,this._shape=new z.Particle},o.onLoad=function(){t.prototype.onLoad.call(this),this.collider.updateVertices()},o.setScale=function(e){t.prototype.setScale.call(this,e),this.collider.updateVertices()},n(e,[{key:"collider",get:function(){return this._collider}},{key:"impl",get:function(){return this._shape}}]),e}(j),yt=(ct=[[0,3,2],[0,1,3],[0,2,1],[1,2,3]],function(t){return new z.ConvexPolyhedron(t,ct)}),vt=function(t){function e(){var e;return(e=t.call(this)||this)._shape=new z.Plane,e}l(e,t);var i=e.prototype;return i.setNormal=function(t){p.rotationTo(this._orient,h.UNIT_Z,t),-1!==this._index&&I(this._body)},i.setConstant=function(t){h.scaleAndAdd(this._offset,this._collider.center,this.collider.normal,t)},i.onLoad=function(){t.prototype.onLoad.call(this),this.setConstant(this.collider.constant),this.setNormal(this.collider.normal)},i._setCenter=function(e){t.prototype._setCenter.call(this,e),this.setConstant(this.collider.constant)},n(e,[{key:"collider",get:function(){return this._collider}},{key:"impl",get:function(){return this._shape}}]),e}(j);z.World.staticBody=new z.Body,z.World.idToConstraintMap={};var ft=function(){function t(){this._impl=void 0,this._com=void 0,this._rigidBody=void 0,this._connectedBody=void 0}var e=t.prototype;return e.setConnectedBody=function(t){if(this._connectedBody!==t){var e=this._connectedBody;e&&e.body.sharedBody.removeJoint(this,1);var i=this._rigidBody.body.sharedBody;i.removeJoint(this,0),this._impl&&(i.wrappedWorld.removeConstraint(this),delete z.World.idToConstraintMap[this._impl.id],this._impl=null),this._connectedBody=t;var o=this._connectedBody;this.onComponentSet(),this.setEnableCollision(this._com.enableCollision),z.World.idToConstraintMap[this._impl.id]=this._impl,i.wrappedWorld.addConstraint(this),i.addJoint(this,0),o&&o.body.sharedBody.addJoint(this,1)}},e.setEnableCollision=function(t){this._impl.collideConnected=t},e.initialize=function(t){this._com=t,this._rigidBody=t.attachedBody,this._connectedBody=t.connectedBody,this.onComponentSet(),this.setEnableCollision(t.enableCollision),z.World.idToConstraintMap[this._impl.id]=this._impl},e.onComponentSet=function(){},e.updateScale0=function(){},e.updateScale1=function(){},e.onEnable=function(){var t=this._rigidBody.body.sharedBody;t.wrappedWorld.addConstraint(this),t.addJoint(this,0);var e=this._connectedBody;e&&e.body.sharedBody.addJoint(this,1)},e.onDisable=function(){var t=this._rigidBody.body.sharedBody;t.wrappedWorld.removeConstraint(this),t.removeJoint(this,0);var e=this._connectedBody;e&&e.body.sharedBody.removeJoint(this,1)},e.onDestroy=function(){delete z.World.idToConstraintMap[this._impl.id],this._com=null,this._rigidBody=null,this._connectedBody=null,this._impl=null},n(t,[{key:"impl",get:function(){return this._impl}},{key:"constraint",get:function(){return this._com}}]),t}(),mt=new h,wt=function(t){function e(){return t.apply(this,arguments)||this}l(e,t);var i=e.prototype;return i.setPivotA=function(){var t=this.constraint;h.multiply(this.impl.pivotA,t.node.worldScale,t.pivotA),t.connectedBody||this.setPivotB(t.pivotB)},i.setPivotB=function(){var t=this.constraint,e=t.connectedBody;if(e)h.multiply(this.impl.pivotB,e.node.worldScale,t.pivotB);else{var i=t.node;h.multiply(mt,i.worldScale,t.pivotA),h.transformQuat(mt,mt,i.worldRotation),h.add(mt,mt,i.worldPosition),h.copy(this.impl.pivotB,mt)}},i.onComponentSet=function(){var t=this._rigidBody.body.impl,e=this.constraint.connectedBody,i=z.World.staticBody;e&&(i=e.body.impl),this._impl=new z.PointToPointConstraint(t,null,i),this.setPivotA(this.constraint.pivotA),this.setPivotB(this.constraint.pivotB)},i.updateScale0=function(){this.setPivotA(this.constraint.pivotA)},i.updateScale1=function(){this.setPivotB(this.constraint.pivotB)},n(e,[{key:"impl",get:function(){return this._impl}},{key:"constraint",get:function(){return this._com}}]),e}(ft),gt=new h,bt=new p,xt=function(t){function e(){return t.apply(this,arguments)||this}l(e,t);var i=e.prototype;return i.setPivotA=function(){var t=this.constraint;h.multiply(this.impl.pivotA,this.constraint.node.worldScale,t.pivotA),t.connectedBody||this.setPivotB(t.pivotB)},i.setPivotB=function(){var t=this.constraint,e=t.connectedBody;if(e)h.multiply(this.impl.pivotB,e.node.worldScale,t.pivotB);else{var i=this.constraint.node;h.multiply(gt,i.worldScale,t.pivotA),h.transformQuat(gt,gt,i.worldRotation),h.add(gt,gt,i.worldPosition),h.copy(this.impl.pivotB,gt)}},i.setAxis=function(t){var e=this.impl.equations;h.copy(this.impl.axisA,t),h.copy(e[3].axisA,t),h.copy(e[4].axisA,t),h.copy(e[5].axisA,t),this.constraint.connectedBody?(h.transformQuat(this.impl.axisB,t,this.constraint.node.worldRotation),p.invert(bt,this.constraint.connectedBody.node.worldRotation),h.transformQuat(this.impl.axisB,this.impl.axisB,bt),h.copy(e[3].axisB,this.impl.axisB),h.copy(e[4].axisB,this.impl.axisB),h.copy(e[5].axisB,this.impl.axisB)):(h.transformQuat(this.impl.axisB,t,this.constraint.node.worldRotation),h.copy(e[3].axisB,this.impl.axisB),h.copy(e[4].axisB,this.impl.axisB),h.copy(e[5].axisB,this.impl.axisB))},i.setLimitEnabled=function(){r(9613)},i.setLowerLimit=function(){r(9613)},i.setUpperLimit=function(){r(9613)},i.setMotorEnabled=function(){r(9613)},i.setMotorVelocity=function(){r(9613)},i.setMotorForceLimit=function(){r(9613)},i.onComponentSet=function(){var t=this._rigidBody.body.impl,e=this.constraint.connectedBody,i=z.World.staticBody;e&&(i=e.body.impl),this._impl=new z.HingeConstraint(t,i),this.setPivotA(this.constraint.pivotA),this.setPivotB(this.constraint.pivotB),this.setAxis(this.constraint.axis)},i.updateScale0=function(){this.setPivotA(this.constraint.pivotA)},i.updateScale1=function(){this.setPivotB(this.constraint.pivotB)},n(e,[{key:"impl",get:function(){return this._impl}},{key:"constraint",get:function(){return this._com}}]),e}(ft),Bt=function(t){function e(){for(var e,i=arguments.length,o=new Array(i),n=0;n<i;n++)o[n]=arguments[n];return(e=t.call.apply(t,[this].concat(o))||this)._breakForce=1e9,e}l(e,t);var i=e.prototype;return i.setBreakForce=function(t){this._breakForce=t,this.updateFrame()},i.setBreakTorque=function(){},i.onComponentSet=function(){this._breakForce=this.constraint.breakForce,this.updateFrame()},i.updateFrame=function(){var t=this._rigidBody.body.impl,e=this.constraint.connectedBody,i=z.World.staticBody;e&&(i=e.body.impl),this._impl=new z.LockConstraint(t,i,{maxForce:this._breakForce})},i.updateScale0=function(){this.updateFrame()},i.updateScale1=function(){this.updateFrame()},n(e,[{key:"impl",get:function(){return this._impl}},{key:"constraint",get:function(){return this._com}}]),e}(ft);A.once(C.EVENT_PRE_SUBSYSTEM_INIT,(function(){o.register("cannon.js",{PhysicsWorld:Y,RigidBody:T,BoxShape:tt,SphereShape:et,TrimeshShape:ot,CylinderShape:nt,ConeShape:at,TerrainShape:ut,SimplexShape:dt,PlaneShape:vt,PointToPointConstraint:wt,HingeConstraint:xt,FixedConstraint:Bt})})),globalThis&&(globalThis.CANNON=z),z.CC_CONFIG={numSegmentsCone:12,numSegmentsCylinder:12,ignoreSelfBody:!0,correctInelastic:3},z.ArrayCollisionMatrix.prototype.reset=function(){for(var t in this.matrix)delete this.matrix[t]},z.Ray.perBodyFilter=function(t,e){return!!(t.collisionFilterMask&e.collisionFilterGroup)}}}}));
