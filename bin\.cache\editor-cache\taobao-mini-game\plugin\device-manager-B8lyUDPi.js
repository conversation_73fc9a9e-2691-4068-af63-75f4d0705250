System.register(["./buffer-barrier-q_79u36H.js","./global-exports-CLZKKIY2.js","./index-Y4La_nfG.js","./gc-object-D18ulfCO.js"],(function(e){"use strict";var t,i,a,r,s,n,F,o,u,c,m,d,l,G,g,E,T,p,_,S,v,A,B,X,h,f,R,N,D,y,P,L,O,U,C,b,W,x,I,w;return{setters:[function(e){t=e.z,i=e.E,a=e.G,r=e.H,s=e.J,n=e.K,F=e.L,o=e.N,u=e.O,c=e.Q,m=e.W,d=e.X,l=e.Y,G=e.Z,g=e._,E=e.$,T=e.a0,p=e.a1,_=e.a2,S=e.a3,v=e.a4,A=e.a5,B=e.a6,X=e.t,h=e.a7,f=e.w,R=e.x,N=e.a8,D=e.a9},function(e){y=e.c},function(e){P=e.r,L=e.h,O=e.m,U=e.B},function(e){C=e.s,b=e.M,W=e.N,x=e.h,I=e.g,w=e.a}],execute:function(){var M={Device:i,Swapchain:a,Buffer:r,Texture:s,Sampler:n,Shader:F,InputAssembler:o,RenderPass:u,Framebuffer:c,DescriptorSet:m,DescriptorSetLayout:d,PipelineLayout:l,PipelineState:G,CommandBuffer:g,Queue:E,GeneralBarrier:T,TextureBarrier:p,BufferBarrier:_,RasterizerState:S,BlendState:v,BlendTarget:A,DepthStencilState:B,PipelineStateInfo:X};Object.assign(M,t),y.gfx=M;var z,k,H={GFXDevice:!0,GFXBuffer:!0,GFXTexture:!0,GFXSampler:!0,GFXShader:!0,GFXInputAssembler:!0,GFXRenderPass:!0,GFXFramebuffer:!0,GFXPipelineState:!0,GFXCommandBuffer:!0,GFXQueue:!0,GFXObjectType:!0,GFXObject:!1,GFXAttributeName:!0,GFXType:!0,GFXFormat:!0,GFXBufferUsageBit:!0,GFXMemoryUsageBit:!0,GFXBufferFlagBit:!0,GFXBufferAccessBit:"MemoryAccessBit",GFXPrimitiveMode:!0,GFXPolygonMode:!0,GFXShadeModel:!0,GFXCullMode:!0,GFXComparisonFunc:!0,GFXStencilOp:!0,GFXBlendOp:!0,GFXBlendFactor:!0,GFXColorMask:!0,GFXFilter:!0,GFXAddress:!0,GFXTextureType:!0,GFXTextureUsageBit:!0,GFXSampleCount:!0,GFXTextureFlagBit:!0,GFXShaderStageFlagBit:!0,GFXDescriptorType:!0,GFXCommandBufferType:!0,GFXLoadOp:!0,GFXStoreOp:!0,GFXPipelineBindPoint:!0,GFXDynamicStateFlagBit:!0,GFXStencilFace:!0,GFXQueueType:!0,GFXRect:!0,GFXViewport:!0,GFXColor:!0,GFXClearFlag:!0,GFXOffset:!0,GFXExtent:!0,GFXTextureSubres:"TextureSubresLayers",GFXTextureCopy:!0,GFXBufferTextureCopy:!0,GFXFormatType:!0,GFXFormatInfo:!0,GFXMemoryStatus:!0,GFXFormatInfos:!0,GFXFormatSize:!0,GFXFormatSurfaceSize:!0,GFXGetTypeSize:!0,getTypedArrayConstructor:!1};for(var V in H){var j=H[V];!0===j?j=V.slice(3):!1===j&&(j=V),P(y,"cc",[{name:V,newName:j,target:y.gfx,targetName:"cc.gfx"}])}L(y,"cc",[{name:"GFX_MAX_VERTEX_ATTRIBUTES"},{name:"GFX_MAX_TEXTURE_UNITS"},{name:"GFX_MAX_ATTACHMENTS"},{name:"GFX_MAX_BUFFER_BINDINGS"},{name:"GFXTextureLayout"}]),L(h,"Feature",[{name:"COLOR_FLOAT",suggest:"Please use device.getFormatFeatures() instead, like: \nlet isSupported = device.getFormatFeatures(Format.R32F) & FormatFeatureBit.RENDER_TARGET;"},{name:"COLOR_HALF_FLOAT",suggest:"Please use device.getFormatFeatures() instead, like: \nlet isSupported = device.getFormatFeatures(Format.R16F) & FormatFeatureBit.RENDER_TARGET;"},{name:"TEXTURE_FLOAT",suggest:"Please use device.getFormatFeatures() instead, like: \nlet isSupported = (device.getFormatFeatures(Format.R32F) & (FormatFeatureBit.RENDER_TARGET | FormatFeatureBit.SAMPLED_TEXTURE)) === (FormatFeatureBit.RENDER_TARGET | FormatFeatureBit.SAMPLED_TEXTURE);"},{name:"TEXTURE_HALF_FLOAT",suggest:"Please use device.getFormatFeatures() instead, like: \nlet isSupported = (device.getFormatFeatures(Format.R16F) & (FormatFeatureBit.RENDER_TARGET | FormatFeatureBit.SAMPLED_TEXTURE)) === (FormatFeatureBit.RENDER_TARGET | FormatFeatureBit.SAMPLED_TEXTURE);"},{name:"TEXTURE_FLOAT_LINEAR",suggest:"Please use device.getFormatFeatures() instead, like: \nlet isSupported = device.getFormatFeatures(Format.R32F) & FormatFeatureBit.LINEAR_FILTER;"},{name:"TEXTURE_HALF_FLOAT_LINEAR",suggest:"Please use device.getFormatFeatures() instead, like: \nlet isSupported = device.getFormatFeatures(Format.R16F) & FormatFeatureBit.LINEAR_FILTER;"},{name:"FORMAT_R11G11B10F",suggest:"Please use device.getFormatFeatures() instead, like: \nlet isSupported = device.getFormatFeatures(Format.R11G11B10F) !== FormatFeatureBit.NONE;"},{name:"FORMAT_SRGB",suggest:"Please use device.getFormatFeatures() instead, like: \nlet isSupported = device.getFormatFeatures(Format.SRGB8) !== FormatFeatureBit.NONE;"},{name:"FORMAT_ETC1",suggest:"Please use device.getFormatFeatures() instead, like: \nlet isSupported = device.getFormatFeatures(Format.ETC_RGB8) !== FormatFeatureBit.NONE;"},{name:"FORMAT_ETC2",suggest:"Please use device.getFormatFeatures() instead, like: \nlet isSupported = device.getFormatFeatures(Format.ETC2_RGB8) !== FormatFeatureBit.NONE;"},{name:"FORMAT_DXT",suggest:"Please use device.getFormatFeatures() instead, like: \nlet isSupported = device.getFormatFeatures(Format.BC1) !== FormatFeatureBit.NONE;"},{name:"FORMAT_PVRTC",suggest:"Please use device.getFormatFeatures() instead, like: \nlet isSupported = device.getFormatFeatures(Format.PVRTC_RGB2) !== FormatFeatureBit.NONE;"},{name:"FORMAT_ASTC",suggest:"Please use device.getFormatFeatures() instead, like: \nlet isSupported = device.getFormatFeatures(Format.ASTC_RGBA_4x4) !== FormatFeatureBit.NONE;"},{name:"FORMAT_RGB8",suggest:"Please use device.getFormatFeatures() instead, like: \nlet isSupported = device.getFormatFeatures(Format.RGB8) !== FormatFeatureBit.NONE;"}]),L(f.prototype,"ColorAttachment",[{name:"beginAccesses",suggest:"Please assign to ColorAttachment.barrier instead"},{name:"endAccesses",suggest:"Please assign to ColorAttachment.barrier instead"}]),L(R.prototype,"DepthStencilAttachment",[{name:"beginAccesses",suggest:"Please assign to DepthStencilAttachment.barrier instead"},{name:"endAccesses",suggest:"Please assign to DepthStencilAttachment.barrier instead"}]),P(i.prototype,"Device",[{name:"getGlobalBarrier",newName:"getGeneralBarrier"}]),e("L",z),function(e){e[e.AUTO=0]="AUTO",e[e.CANVAS=1]="CANVAS",e[e.WEBGL=2]="WEBGL",e[e.HEADLESS=3]="HEADLESS",e[e.WEBGPU=4]="WEBGPU"}(z||e("L",z={})),e("R",k),function(e){e[e.UNKNOWN=-1]="UNKNOWN",e[e.CANVAS=0]="CANVAS",e[e.WEBGL=1]="WEBGL",e[e.WEBGPU=2]="WEBGPU",e[e.OPENGL=3]="OPENGL",e[e.HEADLESS=4]="HEADLESS"}(k||e("R",k={}));var K=e("D",function(){function e(){this.initialized=!1,this._gfxDevice=void 0,this._canvas=null,this._swapchain=void 0,this._renderType=k.UNKNOWN,this._deviceInitialized=!1}var t=e.prototype;return t._tryInitializeWebGPUDevice=function(e,t){var i=this;return this._deviceInitialized?Promise.resolve(!0):e?(this._gfxDevice=new e,new Promise((function(e,a){i._gfxDevice.initialize(t).then((function(t){i._deviceInitialized=t,e(t)})).catch((function(e){a(e)}))}))):Promise.resolve(!1)},t._tryInitializeDeviceSync=function(e,t){return!!this._deviceInitialized||(e&&(this._gfxDevice=new e,this._deviceInitialized=this._gfxDevice.initialize(t)),this._deviceInitialized)},t.init=function(e,t){var a=this;if(this.initialized)return!0;var r=C.querySettings(b.Category.RENDERING,"renderMode");this._canvas=e,this._canvas&&(this._canvas.oncontextmenu=function(){return!1}),this._renderType=this._determineRenderType(r),this._deviceInitialized=!1;var s=new D(t);if(this._renderType===k.WEBGL||this._renderType===k.WEBGPU){var n=!!globalThis.WebGL2RenderingContext;if(globalThis.navigator.userAgent.toLowerCase(),O.browserType===W.UC&&(n=!1),i.canvas=e,this._renderType===k.WEBGPU&&y.WebGPUDevice)return new Promise((function(e,t){a._tryInitializeWebGPUDevice(y.WebGPUDevice,s).then((function(t){a._initSwapchain(),e(t)})).catch((function(e){t(e)}))}));n&&y.WebGL2Device&&this._tryInitializeDeviceSync(y.WebGL2Device,s),y.WebGLDevice&&this._tryInitializeDeviceSync(y.WebGLDevice,s),y.EmptyDevice&&this._tryInitializeDeviceSync(y.EmptyDevice,s),this._initSwapchain()}else this._renderType===k.HEADLESS&&y.EmptyDevice&&(this._tryInitializeDeviceSync(y.EmptyDevice,s),this._initSwapchain());return!!this._gfxDevice||(x(16337),this._renderType=k.UNKNOWN,!1)},t._initSwapchain=function(){var e=new N(1,this._canvas),t=U.windowSize;e.width=t.width,e.height=t.height,this._swapchain=this._gfxDevice.createSwapchain(e)},t._supportWebGPU=function(){return"gpu"in globalThis.navigator},t._determineRenderType=function(e){("number"!=typeof e||e>z.WEBGPU||e<z.AUTO)&&(e=z.AUTO);var t=k.CANVAS,i=!1;if(e===z.CANVAS?(t=k.CANVAS,i=!0):e===z.AUTO||e===z.WEBGPU?(t=this._supportWebGPU()?k.WEBGPU:k.WEBGL,i=!0):e===z.WEBGL?(t=k.WEBGL,i=!0):e===z.HEADLESS&&(t=k.HEADLESS,i=!0),!i)throw new Error(I(3820,e));return t},w(e,[{key:"gfxDevice",get:function(){return this._gfxDevice}},{key:"swapchain",get:function(){return this._swapchain}}]),e}());e("d",new K)}}}));
