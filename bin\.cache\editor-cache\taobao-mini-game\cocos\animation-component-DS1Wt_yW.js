System.register(["./gc-object-D18ulfCO.js","./index-Y4La_nfG.js","./global-exports-CLZKKIY2.js","./component-CsuvAQKv.js","./deprecated-CuuzltLj.js","./scene-ArUG4OfI.js","./prefab-BQYc0LyR.js","./pipeline-state-manager-DQyhxoC_.js","./node-event-DTNosVQv.js","./buffer-barrier-q_79u36H.js"],(function(t){"use strict";var e,n,i,r,a,s,o,u,h,c,l,f,p,_,v,d,y,m,g,T,E,w,A,P,S,C,k,b,x,I,O,N,L,M,R,F,D,U,B,z,V,H,W,Q,j,q,K,G,Y,J,X,Z,$;return{setters:[function(t){e=t.g,n=t.a,i=t.r,r=t._,a=t.x,s=t.k,o=t.w,u=t.an,h=t.h,c=t.o,l=t.V,f=t.aw,p=t.aF,_=t.p,v=t.aG,d=t.Y,y=t.b,m=t.j},function(t){g=t.b5,T=t.c,E=t.a,w=t.s,A=t.b6,P=t.V,S=t.b,C=t.e,k=t.Q,b=t.aE,x=t.aI,I=t.C,O=t.z,N=t.aK,L=t.a$,M=t.aP,R=t.ab,F=t.a3,D=t.aZ,U=t.aF,B=t.aH,z=t.aJ,V=t.M,H=t.$,W=t.g,Q=t.O,j=t.t,q=t.i},function(t){K=t.c},function(t){G=t.A,Y=t.C},function(t){J=t.S,X=t.B},function(t){Z=t.N},null,null,null,function(t){$=t.m}],execute:function(){t({f:zt,g:ot,i:Oe,s:function(t,e){return t instanceof e},u:vt,x:Me,y:Re});var tt,et=t("C","cc.animation."),nt=t("c",Symbol("CreateEval")),it=function(){function t(){this._isPlaying=!1,this._isPaused=!1,this._stepOnce=!1}var i=t.prototype;return i.play=function(){this._isPlaying?this._isPaused?(this._isPaused=!1,this.onResume()):this.onError(e(3912)):(this._isPlaying=!0,this.onPlay())},i.stop=function(){this._isPlaying&&(this._isPlaying=!1,this.onStop(),this._isPaused=!1)},i.pause=function(){this._isPlaying&&!this._isPaused&&(this._isPaused=!0,this.onPause())},i.resume=function(){this._isPlaying&&this._isPaused&&(this._isPaused=!1,this.onResume())},i.step=function(){this.pause(),this._stepOnce=!0,this._isPlaying||this.play()},i.update=function(){},i.onPlay=function(){},i.onPause=function(){},i.onResume=function(){},i.onStop=function(){},i.onError=function(){},n(t,[{key:"isPlaying",get:function(){return this._isPlaying}},{key:"isPaused",get:function(){return this._isPaused}},{key:"isMotionless",get:function(){return!this.isPlaying||this.isPaused}}]),t}();!function(t){t[t.Default=g.Default]="Default",t[t.Normal=g.Normal]="Normal",t[t.Reverse=g.Reverse]="Reverse",t[t.Loop=g.Loop]="Loop",t[t.LoopReverse=g.Loop|g.Reverse]="LoopReverse",t[t.PingPong=g.PingPong]="PingPong",t[t.PingPongReverse=g.PingPong|g.Reverse]="PingPongReverse"}(tt||(tt={})),i(tt);var rt,at=t("W",function(){function t(t){this.ratio=0,this.time=0,this.direction=1,this.stopped=!0,this.iterations=0,this.frameIndex=void 0,t&&this.set(t)}return t.prototype.set=function(t){this.ratio=t.ratio,this.time=t.time,this.direction=t.direction,this.stopped=t.stopped,this.iterations=t.iterations,this.frameIndex=t.frameIndex},t}()),st=function(){function t(t){this.weight=0,this._pose=void 0,this._blendStateWriters=[],this._pose=t}var e=t.prototype;return e.destroy=function(){for(var t=0;t<this._blendStateWriters.length;++t)this._pose.destroyWriter(this._blendStateWriters[t]);this._blendStateWriters.length=0},e.createPoseWriter=function(t,e,n){var i=this._pose.createWriter(t,e,this,n);return this._blendStateWriters.push(i),i},t}();function ot(){return K.director.getAnimationManager()}!function(t){t.PLAY="play",t.STOP="stop",t.PAUSE="pause",t.RESUME="resume",t.LASTFRAME="lastframe",t.FINISHED="finished"}(rt||(rt={})),i(rt);var ut,ht,ct,lt,ft,pt,_t=t("A",function(t){function e(e,n){var i;return void 0===n&&(n=""),(i=t.call(this)||this).duration=1,i.time=0,i.frameRate=0,i._targetNode=null,i._curveLoaded=!1,i._clip=void 0,i._speed=1,i._useSimpleProcess=!1,i._target=null,i._wrapMode=tt.Normal,i._repeatCount=1,i._delay=0,i._delayTime=0,i._currentFramePlayed=!1,i._name=void 0,i._lastIterations=NaN,i._lastWrapInfo=null,i._wrappedInfo=new at,i._allowLastFrame=!1,i._blendStateWriterHost={weight:0},i._playbackDuration=0,i._invDuration=1,i._poseOutput=null,i._weight=1,i._clipEval=void 0,i._clipEventEval=void 0,i._clipEmbeddedPlayerEval=void 0,i._doNotCreateEval=!1,i._clip=e,i._name=n||e&&e.name,i._playbackRange={min:0,max:e.duration},i._playbackDuration=e.duration,e.duration||a("Clip "+e.name+" has zero duration."),i}r(e,t);var i=e.prototype;return i.initialize=function(t,e,n){if(!this._curveLoaded){this._curveLoaded=!0,this._poseOutput&&(this._poseOutput.destroy(),this._poseOutput=null),this._clipEval&&(this._clipEval=void 0),this._clipEventEval&&(this._clipEventEval=void 0),this._clipEmbeddedPlayerEval&&(this._clipEmbeddedPlayerEval.destroy(),this._clipEmbeddedPlayerEval=void 0),this._targetNode=t;var i=this._clip;if(this.duration=i.duration,this._invDuration=1/this.duration,this._speed=i.speed,this.wrapMode=i.wrapMode,this.frameRate=i.sample,this._playbackRange.min=0,this._playbackRange.max=i.duration,this._playbackDuration=i.duration,(this.wrapMode&g.Loop)===g.Loop?this.repeatCount=1/0:this.repeatCount=1,!this._doNotCreateEval){var r,a,s,o=null!==(r=null!=e?e:null==(a=ot())?void 0:a.blendState)&&void 0!==r?r:null;o&&(this._poseOutput=new st(o)),this._clipEval=i.createEvaluator({target:t,pose:null!==(s=this._poseOutput)&&void 0!==s?s:void 0,mask:n})}i.containsAnyEvent()&&(this._clipEventEval=i.createEventEvaluator(this._targetNode)),i.containsAnyEmbeddedPlayer()&&(this._clipEmbeddedPlayerEval=i.createEmbeddedPlayerEvaluator(this._targetNode),this._clipEmbeddedPlayerEval.notifyHostSpeedChanged(this._speed))}},i.destroy=function(){this.isMotionless||ot().removeAnimation(this),this._poseOutput&&(this._poseOutput.destroy(),this._poseOutput=null),this._clipEval=void 0},i.emit=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];ot().pushDelayEvent(this._emit,this,e)},i.on=function(t,e,n){return this._target&&this._target.isValid?this._target.on(t,e,n):null},i.once=function(t,e,n){return this._target&&this._target.isValid?this._target.once(t,e,n):null},i.off=function(t,e,n){this._target&&this._target.isValid&&this._target.off(t,e,n)},i.allowLastFrameEvent=function(t){this._allowLastFrame=t},i._setEventTarget=function(t){this._target=t},i.setTime=function(t){this._currentFramePlayed=!1,this.time=t||0;var e,n=this.getWrappedInfo(t,this._wrappedInfo);null==(e=this._clipEventEval)||e.ignore(n.ratio,n.direction)},i.update=function(t){this._delayTime>0&&(this._delayTime-=t,this._delayTime>0)||(this._currentFramePlayed?this.time+=t*this._speed:this._currentFramePlayed=!0,this._process())},i.sample=function(){var t=this.getWrappedInfo(this.time,this._wrappedInfo);return this._sampleCurves(t.time),this._sampleEvents(t),this._sampleEmbeddedPlayers(t),t},i.onPlay=function(){var t;this.setTime(this._getPlaybackStart()),this._delayTime=this._delay,this._onReplayOrResume(),this.emit(rt.PLAY,this),null==(t=this._clipEmbeddedPlayerEval)||t.notifyHostPlay(this.current)},i.onStop=function(){var t;this.isPaused||this._onPauseOrStop(),this.emit(rt.STOP,this),null==(t=this._clipEmbeddedPlayerEval)||t.notifyHostStop()},i.onResume=function(){var t;this._onReplayOrResume(),this.emit(rt.RESUME,this),null==(t=this._clipEmbeddedPlayerEval)||t.notifyHostPlay(this.current)},i.onPause=function(){var t;this._onPauseOrStop(),this.emit(rt.PAUSE,this),null==(t=this._clipEmbeddedPlayerEval)||t.notifyHostPause(this.current)},i._sampleCurves=function(t){var e=this._poseOutput,n=this._clipEval;e&&(e.weight=this.weight),n&&n.evaluate(t)},i._process=function(){this._useSimpleProcess?this.simpleProcess():this.process()},i.process=function(){var t,e=this.sample();this._allowLastFrame&&(t=this._lastWrapInfo?this._lastWrapInfo:this._lastWrapInfo=new at(e),this.repeatCount>1&&(0|e.iterations)>(0|t.iterations)&&this.emit(rt.LASTFRAME,this),t.set(e)),e.stopped&&(this.stop(),this.emit(rt.FINISHED,this))},i.simpleProcess=function(){var t=this._playbackRange.min,e=this._playbackDuration,n=0,i=0;if(0!==e&&((n=this.time%e)<0&&(n+=e),i=(t+n)*this._invDuration),this._sampleCurves(t+n),this._clipEventEval||this._clipEmbeddedPlayerEval){var r=this.getWrappedInfo(this.time,this._wrappedInfo);this._sampleEvents(r),this._sampleEmbeddedPlayers(r)}this._allowLastFrame&&(Number.isNaN(this._lastIterations)&&(this._lastIterations=i),(this.time>0&&this._lastIterations>i||this.time<0&&this._lastIterations<i)&&this.emit(rt.LASTFRAME,this),this._lastIterations=i)},i._needReverse=function(t){var e=this.wrapMode,n=!1;return(e&g.PingPong)===g.PingPong&&(t-(0|t)==0&&t>0&&(t-=1),1&t&&(n=!n)),(e&g.Reverse)===g.Reverse&&(n=!n),n},i.getWrappedInfo=function(t,e){e=e||new at;var n=this._playbackRange.min,i=this._playbackDuration,r=this.repeatCount;if(0===i)return e.time=0,e.ratio=0,e.direction=1,e.stopped=!!Number.isFinite(r),e.iterations=0,e;var a=!1,s=(t-=n)>0?t/i:-t/i;if(s>=r){s=r,a=!0;var o=r-(0|r);0===o&&(o=1),t=o*i*(t>0?1:-1)}if(t>i){var u=t%i;t=0===u?i:u}else t<0&&0!=(t%=i)&&(t+=i);var h=!1,c=this._wrapMode&g.ShouldWrap;c&&(h=this._needReverse(s));var l=h?-1:1;return this.speed<0&&(l*=-1),c&&h&&(t=i-t),e.time=n+t,e.ratio=e.time/this.duration,e.direction=l,e.stopped=a,e.iterations=s,e},i._getPlaybackStart=function(){return this._playbackRange.min},i._sampleEvents=function(t){var e;null==(e=this._clipEventEval)||e.sample(t.ratio,t.direction,t.iterations)},i._sampleEmbeddedPlayers=function(t){var e;null==(e=this._clipEmbeddedPlayerEval)||e.evaluate(t.time,Math.trunc(t.iterations))},i._emit=function(t,e){this._target&&this._target.isValid&&this._target.emit(t,t,e)},i._onReplayOrResume=function(){ot().addAnimation(this)},i._onPauseOrStop=function(){ot().removeAnimation(this)},n(e,[{key:"clip",get:function(){return this._clip}},{key:"name",get:function(){return this._name}},{key:"length",get:function(){return this.duration}},{key:"wrapMode",get:function(){return this._wrapMode},set:function(t){var e;this._wrapMode=t,this.time=0,t&g.Loop?this.repeatCount=1/0:this.repeatCount=1,null==(e=this._clipEventEval)||e.setWrapMode(t)}},{key:"repeatCount",get:function(){return this._repeatCount},set:function(t){this._repeatCount=t;var e=this._wrapMode&g.ShouldWrap,n=(this.wrapMode&g.Reverse)===g.Reverse;this._useSimpleProcess=t===1/0&&!e&&!n}},{key:"delay",get:function(){return this._delay},set:function(t){this._delayTime=this._delay=t}},{key:"playbackRange",get:function(){return this._playbackRange},set:function(t){s(t.max>=t.min),this._playbackRange.min=Math.max(t.min,0),this._playbackRange.max=Math.min(t.max,this.duration),this._playbackDuration=this._playbackRange.max-this._playbackRange.min,this.setTime(0)}},{key:"speed",get:function(){return this._speed},set:function(t){var e;this._speed=t,null==(e=this._clipEmbeddedPlayerEval)||e.notifyHostSpeedChanged(t)}},{key:"current",get:function(){return this.getWrappedInfo(this.time).time}},{key:"ratio",get:function(){return 0===this.duration?0:this.current/this.duration}},{key:"weight",get:function(){return this._weight},set:function(t){this._weight=t,this._poseOutput&&(this._poseOutput.weight=t)}},{key:"curveLoaded",get:function(){return this._curveLoaded}}]),e}(it));function vt(t){return"string"==typeof t||"number"==typeof t}K.AnimationState=_t;var dt,yt,mt,gt,Tt,Et,wt,At,Pt,St,Ct,kt,bt,xt,It,Ot,Nt,Lt=t("H",T("cc.animation.HierarchyPath")((ht=function(){function t(t){this.path=ct&&ct(),this.path=t||""}return t.prototype.get=function(t){return t instanceof Z?t.getChildByPath(this.path)||(o(3926,t.name,this.path),null):(o(3925),null)},t}(),ct=E(ht.prototype,"path",[w],(function(){return""})),ut=ht))||ut),Mt=t("k",T("cc.animation.ComponentPath")((ft=function(){function t(t){this.component=pt&&pt(),this.component=t||""}return t.prototype.get=function(t){return t instanceof Z?t.getComponent(this.component)||(o(3928,t.name,this.component),null):(o(3927),null)},t}(),pt=E(ft.prototype,"component",[w],(function(){return""})),lt=ft))||lt),Rt=t("n",Symbol("NormalizedFollow")),Ft=Symbol("ConvertAsTrsPath"),Dt=t("t",Symbol("TrackBinding")),Ut=t("r",T(et+"TrackPath")((yt=function(){function t(){this._paths=mt&&mt()}var e=t.prototype;return e.toProperty=function(t){return this._paths.push(t),this},e.toElement=function(t){return this._paths.push(t),this},e.toHierarchy=function(t){return this._paths.push(new Lt(t)),this},e.toComponent=function(t){var e=new Mt("string"==typeof t?t:u(t));return this._paths.push(e),this},e.toCustomized=function(t){return this._paths.push(t),this},e.append=function(){for(var t,e=arguments.length,n=new Array(e),i=0;i<e;i++)n[i]=arguments[i];var r=(t=this._paths).concat.apply(t,n.map((function(t){return t._paths})));return this._paths=r,this},e.isPropertyAt=function(t){return"string"==typeof this._paths[t]},e.parsePropertyAt=function(t){return this._paths[t]},e.isElementAt=function(t){return"number"==typeof this._paths[t]},e.parseElementAt=function(t){return this._paths[t]},e.isHierarchyAt=function(t){return this._paths[t]instanceof Lt},e.parseHierarchyAt=function(t){return s(this.isHierarchyAt(t)),this._paths[t].path},e.isComponentAt=function(t){return this._paths[t]instanceof Mt},e.parseComponentAt=function(t){return s(this.isComponentAt(t)),this._paths[t].component},e.slice=function(e,n){var i=new t;return i._paths=this._paths.slice(e,n),i},e.trace=function(t,e,n){var i,r;return null!==(i=e)&&void 0!==i||(e=0),null!==(r=n)&&void 0!==r||(n=this._paths.length),this[Rt](t,e,n)},e[Ft]=function(){for(var t,e=this._paths,n=e.length,i=0,r="";i<n;++i){var a=e[i];if(!(a instanceof Lt))break;a.path&&(r?r+="/"+a.path:r=a.path)}if(i===n)return null;if(i!==n-1)return null;switch(e[i]){case"position":case"scale":case"rotation":case"eulerAngles":t=e[i];break;default:return null}return{node:r,property:t}},e[Rt]=function(t,e,n){for(var i=this._paths,r=t,a=e;a<n;++a){var s=i[a];if(vt(s)){if(!(s in r))return o(3929,s),null;r=r[s]}else r=s.get(r);if(null===r)break}return r},n(t,[{key:"length",get:function(){return this._paths.length}}]),t}(),mt=E(yt.prototype,"_paths",[w],(function(){return[]})),dt=yt))||dt),Bt=T(et+"TrackBinding")(gt=A((At=function(){function t(){this.path=Et&&Et(),this.proxy=wt&&wt()}var e=t.prototype;return e.parseTrsPath=function(){return this.proxy?null:this.path[Ft]()},e.createRuntimeBinding=function(t,e,n){var i=this.path,r=this.proxy,a=i.length,s=a-1;if(0===a||!i.isPropertyAt(s)&&!i.isElementAt(s)||r){if(r){var o=i[Rt](t,0,a);if(null===o)return null;var u=r.forTarget(o);if(!u)return null;var c={setValue:function(t){u.set(t)}},l=u.get;return l&&(c.getValue=function(){return l.call(u)}),c}return h(3921),null}var f,p=i.isPropertyAt(s)?i.parsePropertyAt(s):i.parseElementAt(s),_=i[Rt](t,0,a-1);return null===_?null:e&&_ instanceof Z&&zt(p)?e.createPoseWriter(_,p,n):(f=function(t){_[p]=t},{target:_,setValue:f,getValue:function(){return _[p]}})},e.isMaskedOff=function(t){var e=this.parseTrsPath();if(!e)return!1;for(var n=t.joints[Symbol.iterator](),i=n.next();!i.done;i=n.next()){var r=i.value;if(r.path===e.node)return!r.enabled}return!1},t}(),At._animationFunctions=new WeakMap,Et=E((Tt=At).prototype,"path",[w],(function(){return new Ut})),wt=E(Tt.prototype,"proxy",[w],null),gt=Tt))||gt)||gt;function zt(t){return"position"===t||"rotation"===t||"scale"===t||"eulerAngles"===t}var Vt,Ht,Wt,Qt,jt,qt=t("T",T(et+"Track")((St=function(){function t(){this._binding=Ct&&Ct()}var e=t.prototype;return e.channels=function(){return[]},e.range=function(){for(var t,e={min:1/0,max:-1/0},n=c(this.channels());!(t=n()).done;){var i=t.value;e.min=Math.min(e.min,i.curve.rangeMin),e.max=Math.max(e.max,i.curve.rangeMax)}return e},n(t,[{key:"path",get:function(){return this._binding.path},set:function(t){this._binding.path=t}},{key:"proxy",get:function(){return this._binding.proxy},set:function(t){this._binding.proxy=t}},{key:Dt,get:function(){return this._binding}}]),t}(),Ct=E(St.prototype,"_binding",[w],(function(){return new Bt})),Pt=St))||Pt),Kt=t("b",T(et+"Channel")((bt=function(){function t(t){this.name="",this._curve=xt&&xt(),this._curve=t}return n(t,[{key:"curve",get:function(){return this._curve}}]),t}(),xt=E(bt.prototype,"_curve",[w],null),kt=bt))||kt),Gt=T(et+"SingleChannelTrack")((Ot=function(t){function e(){var e;return(e=t.call(this)||this)._channel=Nt&&Nt(),e._channel=new Kt(e.createCurve()),e}r(e,t);var i=e.prototype;return i.channels=function(){return[this._channel]},i.createCurve=function(){throw new Error("Not impl")},i[nt]=function(){var t=this._channel.curve;return new Yt(t)},n(e,[{key:"channel",get:function(){return this._channel}}]),e}(qt),Nt=E(Ot.prototype,"_channel",[w],null),It=Ot))||It,Yt=function(){function t(t){this._curve=t}return t.prototype.evaluate=function(t){return this._curve.evaluate(t)},n(t,[{key:"requiresDefault",get:function(){return!1}}]),t}();function Jt(t,e,n,i){var r,a,s,o,u,h=new e,c=new e,l=new e,f=T(t)((a=function(){function t(t,n,i){this.dataPoint=s&&s(),this.inTangent=o&&o(),this.outTangent=u&&u(),this.dataPoint=t||new e,this.inTangent=n||new e,this.outTangent=i||new e}var r=t.prototype;return r.lerp=function(t,e,r){var a=this.dataPoint,s=t.dataPoint;c=n(c,this.inTangent,r),l=n(l,t.outTangent,r);var o=e*e*e,u=e*e,f=o-2*u+e,p=-2*o+3*u,_=o-u;return h=n(h,a,2*o-3*u+1),h=i(h,h,c,f),h=i(h,h,s,p),h=i(h,h,l,_)},r.getNoLerp=function(){return this.dataPoint},t}(),s=E(a.prototype,"dataPoint",[w],(function(){return new e})),o=E(a.prototype,"inTangent",[w],(function(){return new e})),u=E(a.prototype,"outTangent",[w],(function(){return new e})),r=a))||r;if(e===k){var p=f.prototype.lerp;f.prototype.lerp=function(t,e,n){var i=p.call(this,t,e,n);return k.normalize(i,i),i}}return f}var Xt,Zt,$t,te,ee,ne=t("o",Jt("cc.CubicSplineVec2Value",P,P.multiplyScalar,P.scaleAndAdd)),ie=t("p",Jt("cc.CubicSplineVec3Value",S,S.multiplyScalar,S.scaleAndAdd)),re=t("q",Jt("cc.CubicSplineVec4Value",C,C.multiplyScalar,C.scaleAndAdd)),ae=t("m",Jt("cc.CubicSplineQuatValue",k,k.multiplyScalar,k.scaleAndAdd)),se=t("l",T("cc.CubicSplineNumberValue")((Ht=function(){function t(t,e,n){this.dataPoint=Wt&&Wt(),this.inTangent=Qt&&Qt(),this.outTangent=jt&&jt(),this.dataPoint=t,this.inTangent=e,this.outTangent=n}var e=t.prototype;return e.lerp=function(t,e,n){var i=this.dataPoint,r=t.dataPoint,a=e*e*e,s=e*e;return i*(2*a-3*s+1)+this.outTangent*n*(a-2*s+e)+r*(-2*a+3*s)+t.inTangent*n*(a-s)},e.getNoLerp=function(){return this.dataPoint},t}(),Wt=E(Ht.prototype,"dataPoint",[w],(function(){return 0})),Qt=E(Ht.prototype,"inTangent",[w],(function(){return 0})),jt=E(Ht.prototype,"outTangent",[w],(function(){return 0})),Vt=Ht))||Vt),oe=t("R",T(et+"RealTrack")(Xt=function(t){function e(){return t.apply(this,arguments)||this}return r(e,t),e.prototype.createCurve=function(){return new b},e}(Gt))||Xt);function ue(t){return 0===t.keyFramesCount?void 0:t}var he,ce,le,fe,pe,_e,ve,de,ye=["X","Y","Z","W"],me=t("V",T(et+"VectorTrack")(($t=function(t){function e(){var e;(e=t.call(this)||this)._channels=te&&te(),e._nComponents=ee&&ee(),e._channels=new Array(4);for(var n=0;n<e._channels.length;++n){var i=new Kt(new b);i.name=ye[n],e._channels[n]=i}return e}r(e,t);var i=e.prototype;return i.channels=function(){return this._channels},i[nt]=function(){switch(this._nComponents){default:case 2:return new ge(ue(this._channels[0].curve),ue(this._channels[1].curve));case 3:return new Te(ue(this._channels[0].curve),ue(this._channels[1].curve),ue(this._channels[2].curve));case 4:return new Ee(ue(this._channels[0].curve),ue(this._channels[1].curve),ue(this._channels[2].curve),ue(this._channels[3].curve))}},n(e,[{key:"componentsCount",get:function(){return this._nComponents},set:function(t){this._nComponents=t}}]),e}(qt),te=E($t.prototype,"_channels",[w],null),ee=E($t.prototype,"_nComponents",[w],(function(){return 4})),Zt=$t))||Zt),ge=function(){function t(t,e){this._result=new P,this._x=t,this._y=e}return t.prototype.evaluate=function(t,e){return e&&P.copy(this._result,e),this._x&&(this._result.x=this._x.evaluate(t)),this._y&&(this._result.y=this._y.evaluate(t)),this._result},n(t,[{key:"requiresDefault",get:function(){return!this._x||!this._y}}]),t}(),Te=function(){function t(t,e,n){this._result=new S,this._x=t,this._y=e,this._z=n}return t.prototype.evaluate=function(t,e){var n=this._x,i=this._y,r=this._z,a=this._result;return e&&S.copy(a,e),n&&(a.x=n.evaluate(t)),i&&(a.y=i.evaluate(t)),r&&(a.z=r.evaluate(t)),a},n(t,[{key:"requiresDefault",get:function(){return!this._x||!this._y||!this._z}}]),t}(),Ee=function(){function t(t,e,n,i){this._result=new C,this._x=t,this._y=e,this._z=n,this._w=i}return t.prototype.evaluate=function(t,e){return e&&C.copy(this._result,e),this._x&&(this._result.x=this._x.evaluate(t)),this._y&&(this._result.y=this._y.evaluate(t)),this._z&&(this._result.z=this._z.evaluate(t)),this._w&&(this._result.w=this._w.evaluate(t)),this._result},n(t,[{key:"requiresDefault",get:function(){return!(this._x&&this._y&&this._z&&this._w)}}]),t}(),we=t("Q",T(et+"QuatTrack")(he=function(t){function e(){return t.apply(this,arguments)||this}r(e,t);var n=e.prototype;return n.createCurve=function(){return new x},n[nt]=function(){return new Ae(this.channels()[0].curve)},e}(Gt))||he),Ae=function(){function t(t){this._result=new k,this._curve=t}return t.prototype.evaluate=function(t){return this._curve.evaluate(t,this._result),this._result},n(t,[{key:"requiresDefault",get:function(){return!1}}]),t}(),Pe=["Red","Green","Blue","Alpha"],Se=t("j",T(et+"ColorTrack")((le=function(t){function e(){var e;(e=t.call(this)||this)._channels=fe&&fe(),e._channels=new Array(4);for(var n=0;n<e._channels.length;++n){var i=new Kt(new b);i.name=Pe[n],e._channels[n]=i}return e}r(e,t);var n=e.prototype;return n.channels=function(){return this._channels},n[nt]=function(){return new Ce(ue(this._channels[0].curve),ue(this._channels[1].curve),ue(this._channels[2].curve),ue(this._channels[3].curve))},e}(qt),fe=E(le.prototype,"_channels",[w],null),ce=le))||ce),Ce=function(){function t(t,e,n,i){this._result=new I,this._x=t,this._y=e,this._z=n,this._w=i}return t.prototype.evaluate=function(t,e){return e&&I.copy(this._result,e),this._x&&(this._result.r=this._x.evaluate(t)),this._y&&(this._result.g=this._y.evaluate(t)),this._z&&(this._result.b=this._z.evaluate(t)),this._w&&(this._result.a=this._w.evaluate(t)),this._result},n(t,[{key:"requiresDefault",get:function(){return!(this._x&&this._y&&this._z&&this._w)}}]),t}(),ke=["Width","Height"],be=t("S",T(et+"SizeTrack")((_e=function(t){function e(){var e;(e=t.call(this)||this)._channels=ve&&ve(),e._channels=new Array(2);for(var n=0;n<e._channels.length;++n){var i=new Kt(new b);i.name=ke[n],e._channels[n]=i}return e}r(e,t);var n=e.prototype;return n.channels=function(){return this._channels},n[nt]=function(){return new xe(ue(this._channels[0].curve),ue(this._channels[1].curve))},e}(qt),ve=E(_e.prototype,"_channels",[w],null),pe=_e))||pe),xe=function(){function t(t,e){this._result=new O,this._width=t,this._height=e}return t.prototype.evaluate=function(t,e){return e&&(this._result.x=e.x,this._result.y=e.y),this._width&&(this._result.width=this._width.evaluate(t)),this._height&&(this._result.height=this._height.evaluate(t)),this._result},n(t,[{key:"requiresDefault",get:function(){return!this._width||!this._height}}]),t}(),Ie=t("O",T(et+"ObjectTrack")(de=function(t){function e(){return t.apply(this,arguments)||this}return r(e,t),e.prototype.createCurve=function(){return new N},e}(Gt))||de);function Oe(t,e,n){for(var i=t.components,r=i.length,a=0;a<r;++a){var s=i[a],o=s[e];"function"==typeof o&&o.apply(s,n)}}var Ne=t("v",function(){function t(t){var e,n;this.ratios=void 0,this._findRatio=void 0,this.ratios=t;for(var i=!0,r=1,a=t.length;r<a;r++)if(e=t[r]-t[r-1],1===r)n=e;else if(Math.abs(e-n)>1e-6){i=!1;break}this._findRatio=i?Fe:L}return t.prototype.sample=function(t){return this._findRatio(this.ratios,t)},t}());K.RatioSampler=Ne;var Le=t("w",function(){function t(e,n){this.types=void 0,this.type=null,this._values=[],this._lerp=void 0,this._duration=void 0,this._array=void 0,this._duration=n,this._values=e.values;var i=function(e){return"string"==typeof e?e:Array.isArray(e)?e[0]===e[1]&&e[2]===e[3]?t.Linear:t.Bezier(e):t.Linear};if(void 0!==e.easingMethod)this.type=i(e.easingMethod);else if(Array.isArray(e.easingMethods))this.types=e.easingMethods.map(i);else if(void 0!==e.easingMethods){this.types=new Array(this._values.length).fill(null);for(var r=0,a=Object.keys(e.easingMethods);r<a.length;r++){var s=a[r];this.types[s]=i(e.easingMethods[s])}}else this.type=null;var o=e.values[0];(void 0===e.interpolate||e.interpolate)&&(this._lerp=We(o)),void 0!==e._arrayLength&&(this._array=new Array(e._arrayLength))}t.Bezier=function(t){return t};var e=t.prototype;return e.hasLerp=function(){return!!this._lerp},e.valueAt=function(t){if(void 0===this._array){var e=this._values[t];return e&&e.getNoLerp?e.getNoLerp():e}for(var n=0;n<this._array.length;++n)this._array[n]=this._values[this._array.length*t+n];return this._array},e.valueBetween=function(t,e,n,i,r){if(this._lerp){var a=this.types?this.types[e]:this.type,s=r-n,o=(t-n)/s;if(a&&(o=Re(o,a)),void 0===this._array){var u=this._values[e],h=this._values[i];return this._lerp(u,h,o,s*this._duration)}for(var c=0;c<this._array.length;++c){var l=this._values[this._array.length*e+c],f=this._values[this._array.length*i+c];this._array[c]=this._lerp(l,f,o,s*this._duration)}return this._array}if(void 0===this._array)return this.valueAt(e);for(var p=0;p<this._array.length;++p)this._array[p]=this._values[this._array.length*e+p];return this._array},e.empty=function(){return 0===this._values.length},e.constant=function(){return 1===this._values.length},t}());function Me(t,e,n){var i=e.sample(n);if(i<0)if((i=~i)<=0)i=0;else{if(!(i>=e.ratios.length))return t.valueBetween(n,i-1,e.ratios[i-1],i,e.ratios[i]);i=e.ratios.length-1}return t.valueAt(i)}function Re(t,e){if("string"==typeof e){var n=F[e];n?t=n(t):h(3906,e)}else Array.isArray(e)&&(t=M(e,t));return t}function Fe(t,e){var n=t.length-1;if(0===n)return 0;var i=t[0];if(e<i)return 0;var r=t[n];if(e>r)return n;var a=(e=(e-i)/(r-i))/(1/n),s=0|a,o=1e-6;return a-s<o?s:s+1-a<o?s+1:~(s+1)}Le.Linear=null,K.AnimCurve=Le,t("E",function(){function t(){this.events=[]}return t.prototype.add=function(t,e){this.events.push({func:t||"",params:e||[]})},t}()),K.sampleAnimationCurve=Me;var De,Ue,Be,ze,Ve,He,We=function(){function t(t){var e=new t;return function(n,i,r){return t.lerp(e,n,i,r),e}}function e(t,e,n,i){return t.lerp(e,n,i)}return function(n){if(null!==n){if("number"==typeof n)return R;if("object"==typeof n&&n.constructor){if(n instanceof k)return i=new k,function(t,e,n){return k.slerp(i,t,e,n)};if(n instanceof l)return t(n.constructor);if(n.constructor===Number)return R;if("function"==typeof n.lerp)return e}var i}}}(),Qe=T(et+"UntypedTrackChannel")((Ue=function(t){function e(){var e;return(e=t.call(this,new b)||this).property=Be&&Be(),e}return r(e,t),e}(Kt),Be=E(Ue.prototype,"property",[w],(function(){return""})),De=Ue))||De,je=t("U",T(et+"UntypedTrack")((Ve=function(t){function n(){for(var e,n=arguments.length,i=new Array(n),r=0;r<n;r++)i[r]=arguments[r];return(e=t.call.apply(t,[this].concat(i))||this)._channels=He&&He(),e}r(n,t);var i=n.prototype;return i.channels=function(){return this._channels},i[nt]=function(){throw new Error("UntypedTrack should be handled specially. Please file an issue.")},i.createLegacyEval=function(t){var n=this,i=function(t){var e;return null==(e=n._channels.find((function(e){return e.property===t})))?void 0:e.curve};switch(!0){default:throw new Error(e(3931));case t instanceof P:return new ge(i("x"),i("y"));case t instanceof S:return new Te(i("x"),i("y"),i("z"));case t instanceof C:return new Ee(i("x"),i("y"),i("z"),i("w"));case t instanceof I:return new Ce(i("r"),i("g"),i("b"),i("a"));case t instanceof O:return new xe(i("width"),i("height"))}},i.addChannel=function(t){var e=new Qe;return e.property=t,this._channels.push(e),e},i.upgrade=function(t){var e=this,n=function(t,n){var i=e.channels().find((function(e){return e.property===t}));i&&(n.name=i.name,n.curve.assignSorted(Array.from(i.curve.times()),Array.from(i.curve.values())))},i=t(this.path,this.proxy);switch(i){default:case"size":break;case"vec2":case"vec3":case"vec4":var r=new me;r.path=this.path,r.proxy=this.proxy,r.componentsCount="vec2"===i?2:"vec3"===i?3:4;var a=r.channels(),s=a[0],o=a[1],u=a[2],h=a[3];switch(i){case"vec4":n("w",h);case"vec3":n("z",u);default:n("x",s),n("y",o)}return r;case"color":var c=new Se,l=c.channels(),f=l[0],p=l[1],_=l[2],v=l[3];return n("r",f),n("g",p),n("b",_),n("a",v),n("x",f),n("y",p),n("z",_),n("w",v),c}return null},n}(qt),He=E(Ve.prototype,"_channels",[w],(function(){return[]})),ze=Ve))||ze),qe=function(){function t(t){this._keys=[],this._curves=[],this._commonTargets=[],this._ratioSamplers=[],this._runtimeCurves=void 0,this._data=null,this._duration=void 0,this._duration=t}var e=t.prototype;return e.getPropertyCurves=function(){return this._runtimeCurves||this._createPropertyCurves(),this._runtimeCurves},e.toTracks=function(){for(var t,e=[],n=this.keys,i=this.curves,r=this.commonTargets,a=function(t,e,n){for(var i,r=new Ut,a=c(e);!(i=a()).done;){var s=i.value;"string"==typeof s?r.toProperty(s):"number"==typeof s?r.toElement(s):s instanceof Lt?r.toHierarchy(s.path):s instanceof Mt?r.toComponent(s.component):r.toCustomized(s)}t.path=r,t.proxy=n},u=r.map((function(t){var n=new je;return a(n,t.modifiers,t.valueAdapter),e.push(n),n})),h=function(){var i,r=t.value,h=r.data,c=h.values;if(0===c.length)return 0;var l=h.keys<0?[0]:n[h.keys],f=c[0],p=null===(i=h.interpolate)||void 0===i||i;s("number"!=typeof h._arrayLength||"number"==typeof f);var _,v=new Ge(h,l.length),d=function(t){a(t,r.modifiers,r.valueAdapter)};if("number"==typeof r.commonTarget){if(!c.every((function(t){return"number"==typeof t})))return o(3932),0;if(r.valueAdapter||1!==r.modifiers.length||"string"!=typeof r.modifiers[0])return o(3933),0;var y=r.modifiers[0],m=u[r.commonTarget].addChannel(y).curve;_=m}!function(){if("number"==typeof f){if(!c.every((function(t){return"number"==typeof t})))return void o(3934);var t;if(_)t=_;else{var n=new oe;d(n),e.push(n),t=n.channel.curve}var i=p?U.LINEAR:U.CONSTANT;return t.assignSorted(l,c.map((function(t){return{value:t,interpolationMode:i}}))),void v.convert(t)}if("object"==typeof f)switch(!0){default:break;case Ke(c,P):case Ke(c,S):case Ke(c,C):var r=f instanceof P?2:f instanceof S?3:4,a=new me;d(a),a.componentsCount=r;var u=a.channels(),h=u[0].curve,y=u[1].curve,m=u[2].curve,g=u[3].curve,T=p?U.LINEAR:U.CONSTANT,E=function(t){return{value:t,interpolationMode:T}};switch(r){case 4:g.assignSorted(l,c.map((function(t){return E(t.w)}))),v.convert(g);case 3:m.assignSorted(l,c.map((function(t){return E(t.z)}))),v.convert(m);default:h.assignSorted(l,c.map((function(t){return E(t.x)}))),v.convert(h),y.assignSorted(l,c.map((function(t){return E(t.y)}))),v.convert(y)}return void e.push(a);case Ke(c,k):var w=new we;d(w);var A=p?z.SLERP:z.CONSTANT;return w.channel.curve.assignSorted(l,c.map((function(t){return{value:k.clone(t),interpolationMode:A}}))),v.convertQuatCurve(w.channel.curve),void e.push(w);case Ke(c,I):var b=new Se;d(b);var x=b.channels(),N=x[0].curve,L=x[1].curve,M=x[2].curve,R=x[3].curve,F=p?U.LINEAR:U.CONSTANT,D=function(t){return{value:t,interpolationMode:F}};return N.assignSorted(l,c.map((function(t){return D(t.r)}))),v.convert(N),L.assignSorted(l,c.map((function(t){return D(t.g)}))),v.convert(L),M.assignSorted(l,c.map((function(t){return D(t.b)}))),v.convert(M),R.assignSorted(l,c.map((function(t){return D(t.a)}))),v.convert(R),void e.push(b);case Ke(c,O):var B=new be;d(B);var V=B.channels(),H=V[0].curve,W=V[1].curve,Q=p?U.LINEAR:U.CONSTANT,j=function(t){return{value:t,interpolationMode:Q}};return H.assignSorted(l,c.map((function(t){return j(t.width)}))),v.convert(H),W.assignSorted(l,c.map((function(t){return j(t.height)}))),v.convert(W),void e.push(B);case Ke(c,se):s(v.nil);var q=new oe;d(q);var K=p?U.CUBIC:U.CONSTANT;return q.channel.curve.assignSorted(l,c.map((function(t){return{value:t.dataPoint,leftTangent:t.inTangent,rightTangent:t.outTangent,interpolationMode:K}}))),void e.push(q);case Ke(c,ne):case Ke(c,ie):case Ke(c,re):s(v.nil);var G=f instanceof ne?2:f instanceof ie?3:4,Y=new me;d(Y),Y.componentsCount=G;var J=Y.channels(),X=J[0],Z=J[1],$=J[2],tt=J[3],et=p?U.LINEAR:U.CONSTANT,nt=function(t,e,n){return{value:t,leftTangent:e,rightTangent:n,interpolationMode:et}};switch(G){case 4:tt.curve.assignSorted(l,c.map((function(t){return nt(t.dataPoint.w,t.inTangent.w,t.outTangent.w)})));case 3:$.curve.assignSorted(l,c.map((function(t){return nt(t.dataPoint.z,t.inTangent.z,t.outTangent.z)})));default:X.curve.assignSorted(l,c.map((function(t){return nt(t.dataPoint.y,t.inTangent.y,t.outTangent.y)}))),Z.curve.assignSorted(l,c.map((function(t){return nt(t.dataPoint.x,t.inTangent.x,t.outTangent.x)})))}return void e.push(Y);case c.every((function(t){return t instanceof ae})):o(3935)}var it=new Ie;d(it),it.channel.curve.assignSorted(l,c),e.push(it)}()},l=c(i);!(t=l()).done;)h();return e},e._createPropertyCurves=function(){var t=this;this._ratioSamplers=this._keys.map((function(e){return new Ne(e.map((function(e){return e/t._duration})))})),this._runtimeCurves=this._curves.map((function(e){return{curve:new Le(e.data,t._duration),modifiers:e.modifiers,valueAdapter:e.valueAdapter,sampler:t._ratioSamplers[e.data.keys],commonTarget:e.commonTarget}}))},n(t,[{key:"keys",get:function(){return this._keys},set:function(t){this._keys=t}},{key:"curves",get:function(){return this._curves},set:function(t){this._curves=t,delete this._runtimeCurves}},{key:"commonTargets",get:function(){return this._commonTargets},set:function(t){this._commonTargets=t}},{key:"data",get:function(){return this._data}}]),t}();function Ke(t,e){return t.every((function(t){return t instanceof e}))}var Ge=function(){function t(t,e){this._easingMethods=void 0;var n=t.easingMethods;Array.isArray(n)?0===n.length&&0!==e?this._easingMethods=new Array(e).fill(null):this._easingMethods=n:this._easingMethods=void 0===n?new Array(e).fill(t.easingMethod):Array.from({length:e},(function(t,e){var i;return null!==(i=n[e])&&void 0!==i?i:null}))}var e=t.prototype;return e.convert=function(t){var e,n,i,r,a,o,u,h,c,l,f,p,_,v,d,y,m,g,T,E,w,A,P=this._easingMethods;if(P){var S=t.keyFramesCount;if(!(t.keyFramesCount<2)){Array.isArray(P)&&s(S===P.length);for(var C=S-1,k=0;k<C;++k){var b=P[k];b&&(Array.isArray(b)?(n=b,i=t.getKeyframeTime(k),r=t.getKeyframeValue(k),a=t.getKeyframeTime(k+1),o=t.getKeyframeValue(k+1),u=void 0,h=void 0,c=void 0,l=void 0,f=void 0,p=void 0,_=void 0,v=void 0,d=void 0,y=void 0,m=void 0,g=void 0,T=void 0,E=void 0,w=void 0,A=void 0,u=n[0],h=n[1],c=n[2],l=n[3],f=r.value,p=3*(a-i),_=3*(o.value-f),y=(1-c)*p,m=(1-l)*_,g=1/3,T=(d=h*_)/(v=u*p),E=Math.sqrt(v*v+d*d)*g,w=m/y,A=Math.sqrt(y*y+m*m)*g,r.interpolationMode=U.CUBIC,r.tangentWeightMode=(e=r.tangentWeightMode)===B.NONE?B.RIGHT:e===B.LEFT?B.BOTH:e,r.rightTangent=T,r.rightTangentWeight=E,o.tangentWeightMode=An(o.tangentWeightMode),o.leftTangent=w,o.leftTangentWeight=A):Ye(b,t,k))}}}},e.convertQuatCurve=function(t){var e=this._easingMethods;if(e){var n=t.keyFramesCount;if(!(t.keyFramesCount<2)){Array.isArray(e)&&s(n===e.length);for(var i=n-1,r=0;r<i;++r){var a=e[r];a&&(Array.isArray(a)?t.getKeyframeValue(r).easingMethod=a.slice():Je(a,t,r))}}}},n(t,[{key:"nil",get:function(){return!this._easingMethods||this._easingMethods.every((function(t){return null==t}))}}]),t}();function Ye(t,e,n){s(n!==e.keyFramesCount-1);var i=e.getKeyframeValue(n),r=wn[t];r===D.CONSTANT?i.interpolationMode=U.CONSTANT:(i.interpolationMode=U.LINEAR,i.easingMethod=r)}function Je(t,e,n){s(n!==e.keyFramesCount-1);var i=e.getKeyframeValue(n),r=wn[t];i.easingMethod=r}var Xe,Ze,$e,tn,en,nn,rn,an,sn,on,un,hn,cn,ln,fn,pn,_n,vn,dn,yn,mn,gn,Tn,En,wn={constant:D.CONSTANT,linear:D.LINEAR,quadIn:D.QUAD_IN,quadOut:D.QUAD_OUT,quadInOut:D.QUAD_IN_OUT,quadOutIn:D.QUAD_OUT_IN,cubicIn:D.CUBIC_IN,cubicOut:D.CUBIC_OUT,cubicInOut:D.CUBIC_IN_OUT,cubicOutIn:D.CUBIC_OUT_IN,quartIn:D.QUART_IN,quartOut:D.QUART_OUT,quartInOut:D.QUART_IN_OUT,quartOutIn:D.QUART_OUT_IN,quintIn:D.QUINT_IN,quintOut:D.QUINT_OUT,quintInOut:D.QUINT_IN_OUT,quintOutIn:D.QUINT_OUT_IN,sineIn:D.SINE_IN,sineOut:D.SINE_OUT,sineInOut:D.SINE_IN_OUT,sineOutIn:D.SINE_OUT_IN,expoIn:D.EXPO_IN,expoOut:D.EXPO_OUT,expoInOut:D.EXPO_IN_OUT,expoOutIn:D.EXPO_OUT_IN,circIn:D.CIRC_IN,circOut:D.CIRC_OUT,circInOut:D.CIRC_IN_OUT,circOutIn:D.CIRC_OUT_IN,elasticIn:D.ELASTIC_IN,elasticOut:D.ELASTIC_OUT,elasticInOut:D.ELASTIC_IN_OUT,elasticOutIn:D.ELASTIC_OUT_IN,backIn:D.BACK_IN,backOut:D.BACK_OUT,backInOut:D.BACK_IN_OUT,backOutIn:D.BACK_OUT_IN,bounceIn:D.BOUNCE_IN,bounceOut:D.BOUNCE_OUT,bounceInOut:D.BOUNCE_IN_OUT,bounceOutIn:D.BOUNCE_OUT_IN,smooth:D.SMOOTH,fade:D.FADE};function An(t){return t===B.NONE?B.LEFT:t===B.RIGHT?B.BOTH:t}var Pn=T,Sn=w;function Cn(){throw new Error("split() only valid in Editor.")}Pn(et+"ExoticAnimation")((Xe=function(){function t(){this._nodeAnimations=Ze&&Ze()}var e=t.prototype;return e.createEvaluator=function(t){return new Rn(this._nodeAnimations,t)},e.createEvaluatorForAnimationGraph=function(t){return new Un(this._nodeAnimations,t)},e.addNodeAnimation=function(t){var e=new kn(t);return this._nodeAnimations.push(e),e},e.collectAnimatedJoints=function(){return Array.from(new Set(this._nodeAnimations.map((function(t){return t.path}))))},e.split=function(){return Cn()},e.toHashString=function(){return this._nodeAnimations.map((function(t){return t.toHashString()})).join("\n")},t}(),Ze=E(Xe.prototype,"_nodeAnimations",[Sn],(function(){return[]})),Xe));var kn=Pn(et+"ExoticNodeAnimation")((tn=function(){function t(t){this._path=en&&en(),this._position=nn&&nn(),this._rotation=rn&&rn(),this._scale=an&&an(),this._path=t}var e=t.prototype;return e.createPosition=function(t,e){this._position=new Ln(t,new On(e))},e.createRotation=function(t,e){this._rotation=new Ln(t,new Nn(e))},e.createScale=function(t,e){this._scale=new Ln(t,new On(e))},e.createEvaluator=function(t){return new Fn(this._path,this._position,this._rotation,this._scale,t)},e.createEvaluatorForAnimationGraph=function(t){var e=t.bindTransform(this._path);return e?new Bn(e,this._position,this._rotation,this._scale):null},e.split=function(){return Cn()},e.toHashString=function(){var t,e,n,i,r,a;return this._path+"\n"+(null!==(t=null==(e=this._position)?void 0:e.toHashString())&&void 0!==t?t:"")+(null!==(n=null==(i=this._scale)?void 0:i.toHashString())&&void 0!==n?n:"")+(null!==(r=null==(a=this._rotation)?void 0:a.toHashString())&&void 0!==r?r:"")},n(t,[{key:"path",get:function(){return this._path}}]),t}(),en=E(tn.prototype,"_path",[Sn],(function(){return""})),nn=E(tn.prototype,"_position",[Sn],(function(){return null})),rn=E(tn.prototype,"_rotation",[Sn],(function(){return null})),an=E(tn.prototype,"_scale",[Sn],(function(){return null})),$e=tn))||$e;function bn(t){return t.toPrecision(2)}function xn(t){return t.map((function(t){return Number.parseFloat(bn(t))})).join(" ")}var In=Pn(et+"ExoticVectorLikeTrackValues")((on=function(){function t(t){this._values=un&&un(),this._isQuantized=hn&&hn(),this._values=t}var e=t.prototype;return e.quantize=function(t){s(!this._isQuantized),this._values=Yn(this._values,t),this._isQuantized=!0},e.toHashString=function(){var t=this._isQuantized,e=this._values;return t+" "+(t?e.toHashString():xn(e))},n(t,[{key:"precision",get:function(){return this._isQuantized?this._values.originalPrecision:Wn(this._values)}}]),t}(),un=E(on.prototype,"_values",[Sn],null),hn=E(on.prototype,"_isQuantized",[Sn],(function(){return!1})),sn=on))||sn,On=Pn(et+"ExoticVec3TrackValues")(cn=function(t){function e(){return t.apply(this,arguments)||this}r(e,t),e.imitate=function(t,n){var i=new e(t);return n._isQuantized&&i.quantize(n._values.quantizationType),i};var n=e.prototype;return n.get=function(t,e){var n=this._values;this._isQuantized?Zn(n,t,e):S.fromArray(e,n,3*t)},n.lerp=function(t,e,n,i,r,a){var s=this._values;this._isQuantized?(Zn(s,t,i),Zn(s,e,r)):(S.fromArray(i,s,3*t),S.fromArray(r,s,3*e)),S.lerp(a,i,r,n)},e}(In))||cn,Nn=Pn(et+"ExoticQuatTrackValues")(ln=function(t){function e(){return t.apply(this,arguments)||this}r(e,t),e.imitate=function(t,n){var i=new e(t);return n._isQuantized&&i.quantize(n._values.quantizationType),i};var n=e.prototype;return n.get=function(t,e){var n=this._values;this._isQuantized?$n(n,t,e):k.fromArray(e,n,4*t)},n.lerp=function(t,e,n,i,r,a){var s=this._values;this._isQuantized?($n(s,t,i),$n(s,e,r)):(k.fromArray(i,s,4*t),k.fromArray(r,s,4*e)),k.slerp(a,i,r,n)},e}(In))||ln,Ln=Pn(et+"ExoticTrack")((pn=function(){function t(t,e){this.times=_n&&_n(),this.values=vn&&vn(),this.times=t,this.values=e}return t.prototype.toHashString=function(){var t=this.times,e=this.values;return"times: "+xn(t)+"; values: "+e.toHashString()},t}(),_n=E(pn.prototype,"times",[Sn],null),vn=E(pn.prototype,"values",[Sn],null),fn=pn))||fn;function Mn(t,e){t.length,s(0!==t.length);var n=0,i=0,r=L(t,e);if(r>=0)n=r;else{var a=~r,o=a-1;n=o;var u=t[a],h=t[o];i=(e-h)/(u-h)}return{index:n,ratio:i}}!function(){function t(){this._reset()}var e=t.prototype;e.calculate=function(t,e,n){if(this._reset(),!(e>n)){var i=t.length;if(i){var r=t[0],a=t[i-1],s=0,o=0;if(e<r);else if(e>=a)s=i-1,o=0;else{var u=Mn(t,e);s=u.index,o=u.ratio}var h=0,c=0;if(n<r);else if(n>=a)h=i-1,c=0;else{var l=Mn(t,n);h=l.index,c=l.ratio}var f=!o,p=!c;s!==h||o!==c?(f||(this.preLerpIndex=s,this.preLerpRatio=o),this.directKeyframesBegin=f?s:s+1,this.directKeyframesEnd=h+1,p||(this.postLerpIndex=h,this.postLerpRatio=c)):f?(this.directKeyframesBegin=s,this.directKeyframesEnd=s+1):(this.preLerpIndex=s,this.preLerpRatio=o)}}},e._reset=function(){this.preLerpIndex=-1,this.preLerpRatio=0,this.directKeyframesBegin=0,this.directKeyframesEnd=0,this.postLerpIndex=-1,this.postLerpRatio=0},n(t,[{key:"keyframesCount",get:function(){var t=this.preLerpIndex,e=this.directKeyframesBegin;return 0+(t<0?0:1)+(this.directKeyframesEnd-e)+(this.postLerpIndex<0?0:1)}}])}();var Rn=function(){function t(t,e){this._nodeEvaluations=void 0,this._nodeEvaluations=t.map((function(t){return t.createEvaluator(e)}))}return t.prototype.evaluate=function(t){this._nodeEvaluations.forEach((function(e){e.evaluate(t)}))},t}(),Fn=function(){function t(t,e,n,i,r){this._position=null,this._rotation=null,this._scale=null,e&&(this._position=Xn(e.times,e.values,S,t,"position",r)),n&&(this._rotation=Xn(n.times,n.values,k,t,"rotation",r)),i&&(this._scale=Xn(i.times,i.values,S,t,"scale",r))}return t.prototype.evaluate=function(t){if(this._position){var e=this._position.evaluator.evaluate(t);this._position.runtimeBinding.setValue(e)}if(this._rotation){var n=this._rotation.evaluator.evaluate(t);this._rotation.runtimeBinding.setValue(n)}if(this._scale){var i=this._scale.evaluator.evaluate(t);this._scale.runtimeBinding.setValue(i)}},t}(),Dn=function(){function t(t,e,n){this._times=void 0,this._inputSampleResultCache={just:!1,index:-1,nextIndex:-1,ratio:0},this._values=void 0,this._prevValue=void 0,this._nextValue=void 0,this._resultValue=void 0,this._times=t,this._values=e,this._prevValue=new n,this._nextValue=new n,this._resultValue=new n}return t.prototype.evaluate=function(t){var e=this._times,n=this._values,i=this._resultValue;if(0===e.length)return i;var r=zn(e,t,this._inputSampleResultCache);return r.just?n.get(r.index,i):n.lerp(r.index,r.nextIndex,r.ratio,this._prevValue,this._nextValue,i),i},t}(),Un=function(){function t(t,e){this._nodeEvaluations=void 0,this._nodeEvaluations=t.map((function(t){return t.createEvaluatorForAnimationGraph(e)})).filter((function(t){return!!t}))}var e=t.prototype;return e.destroy=function(){for(var t=this._nodeEvaluations,e=t.length,n=0;n<e;++n)t[n].destroy()},e.evaluate=function(t,e){for(var n=this._nodeEvaluations,i=n.length,r=0;r<i;++r)n[r].evaluate(t,e)},t}(),Bn=function(){function t(t,e,n,i){this._position=null,this._rotation=null,this._scale=null,this._transformHandle=void 0,this._transformHandle=t,e&&(this._position=new Dn(e.times,e.values,S)),n&&(this._rotation=new Dn(n.times,n.values,k)),i&&(this._scale=new Dn(i.times,i.values,S))}var e=t.prototype;return e.destroy=function(){this._transformHandle.destroy()},e.evaluate=function(t,e){var n=this._transformHandle.index,i=this._position,r=this._rotation,a=this._scale,s=e.transforms;if(i){var o=i.evaluate(t);s.setPosition(n,o)}if(r){var u=r.evaluate(t);s.setRotation(n,u)}if(a){var h=a.evaluate(t);s.setScale(n,h)}},t}();function zn(t,e,n){var i=t.length,r=t[0],a=t[i-1];if(e<r)n.just=!0,n.index=0;else if(e>a)n.just=!0,n.index=i-1;else{var s=L(t,e);if(s>=0)n.just=!0,n.index=s;else{var o=~s,u=o-1,h=t[u],c=t[o],l=(e-t[u])/(c-h);n.just=!1,n.index=u,n.nextIndex=o,n.ratio=l}}return n}var Vn,Hn={uint8:Uint8Array,uint16:Uint16Array};function Wn(t){switch(t.BYTES_PER_ELEMENT){default:case 4:return Vn.FLOAT_32;case 8:return Vn.FLOAT_64}}!function(t){t[t.FLOAT_32=0]="FLOAT_32",t[t.FLOAT_64=1]="FLOAT_64"}(Vn||(Vn={}));var Qn,jn,qn,Kn,Gn=Pn(et+"QuantizedFloatArray")((yn=function(){function t(t,e,n,i){void 0===i&&(i=0),this.originalPrecision=mn&&mn(),this.min=gn&&gn(),this.extent=Tn&&Tn(),this.values=En&&En(),this.originalPrecision=t,this.values=e,this.extent=n,this.min=i}return t.prototype.toHashString=function(){var t=this.originalPrecision,e=this.min,n=this.extent,i=this.values;return t+" "+bn(e)+" "+bn(n)+" "+i.join(" ")},n(t,[{key:"quantizationType",get:function(){switch(this.values.BYTES_PER_ELEMENT){default:case 1:return"uint8";case 2:return"uint16"}}}]),t}(),mn=E(yn.prototype,"originalPrecision",[Sn],null),gn=E(yn.prototype,"min",[Sn],null),Tn=E(yn.prototype,"extent",[Sn],null),En=E(yn.prototype,"values",[Sn],null),dn=yn))||dn;function Yn(t,e){var n=Hn[e],i=1<<n.BYTES_PER_ELEMENT,r=Number.POSITIVE_INFINITY,a=Number.NEGATIVE_INFINITY;t.forEach((function(t){r=Math.min(t,r),a=Math.max(t,a)}));var s=a-r,o=n.from(t,(function(t){return(t-r)/s*i}));return new Gn(Wn(t),o,s,r)}function Jn(t,e){return t.values[e]/(1<<t.values.BYTES_PER_ELEMENT)*t.extent+t.min}function Xn(t,e,n,i,r,a){var s=new Bt;s.path=(new Ut).toHierarchy(i).toProperty(r);var o=a(s);return o?{runtimeBinding:o,evaluator:new Dn(t,e,n)}:null}function Zn(t,e,n){S.set(n,Jn(t,3*e+0),Jn(t,3*e+1),Jn(t,3*e+2))}function $n(t,e,n){k.set(n,Jn(t,4*e+0),Jn(t,4*e+1),Jn(t,4*e+2),Jn(t,4*e+3))}var ti,ei,ni,ii,ri,ai,si,oi,ui,hi,ci,li,fi,pi,_i,vi,di,yi,mi,gi=T(et+"AuxiliaryCurveEntry")((jn=function(){this.name=qn&&qn(),this.curve=Kn&&Kn()},qn=E(jn.prototype,"name",[w],(function(){return""})),Kn=E(jn.prototype,"curve",[w],(function(){return new b})),Qn=jn))||Qn,Ti=Symbol("SearchForRootBonePath"),Ei=t("e",Symbol("ExoticAnimation")),wi=Symbol("[[EmbeddedPlayerCount]]"),Ai=Symbol("[[GetEmbeddedPlayers]]"),Pi=Symbol("[[AddEmbeddedPlayer]]"),Si=Symbol("[[RemoveEmbeddedPlayer]]"),Ci=Symbol("[[ClearEmbeddedPlayers]]"),ki=t("d",Symbol("[[Additive Settings]]")),bi=t("h",T("cc.AnimationClip")((_i=function(t){function e(e){var n;return(n=t.call(this,e)||this).sample=ni&&ni(),n.speed=ii&&ii(),n.wrapMode=ri&&ri(),n.enableTrsBlending=ai&&ai(),n._duration=si&&si(),n._hash=oi&&oi(),n.frameRate=0,n._tracks=ui&&ui(),n._exoticAnimation=hi&&hi(),n._legacyData=void 0,n._legacyDataDirty=!1,n._events=ci&&ci(),n._embeddedPlayers=li&&li(),n._additiveSettings=fi&&fi(),n._auxiliaryCurveEntries=pi&&pi(),n._runtimeEvents={ratios:[],eventGroups:[]},n}r(e,t),e.createWithSpriteFrames=function(t,n){var i=new e;i.sample=n||i.sample,i.duration=t.length/i.sample;var r=1/i.sample,a=new Ie;return a.path=(new Ut).toComponent("cc.Sprite").toProperty("spriteFrame"),a.channels()[0].curve.assignSorted(t.map((function(t,e){return[r*e,t]}))),i.addTrack(a),i};var i=e.prototype;return i.onLoaded=function(){this.frameRate=this.sample,this.events=this._events},i.range=function(){for(var t={min:1/0,max:-1/0},e=this._tracks,n=e.length,i=0;i<n;++i){var r=e[i].range();t.min=Math.min(t.min,r.min),t.max=Math.max(t.max,r.max)}return t},i.getTrack=function(t){return this._tracks[t]},i.addTrack=function(t){var e=this._tracks.length;return this._tracks.push(t),e},i.removeTrack=function(t){this._tracks.splice(t,1)},i.clearTracks=function(){this._tracks.length=0},i.containsAnyEvent=function(){return 0!==this._events.length},i.createEventEvaluator=function(t){return new Bi(t,this._runtimeEvents.ratios,this._runtimeEvents.eventGroups,this.wrapMode)},i.containsAnyEmbeddedPlayer=function(){return 0!==this._embeddedPlayers.length},i.createEmbeddedPlayerEvaluator=function(t){return new Oi(this._embeddedPlayers,t)},i.createEvaluator=function(t){var e=this,n=t.target;return this._createEvalWithBinder(n,(function(i){if(!t.mask||!i.isMaskedOff(t.mask)){var r=i.createRuntimeBinding(n,e.enableTrsBlending?t.pose:void 0,!1);return null!=r?r:void 0}}),t.rootMotion)},i.destroy=function(){var e,n=null==(e=K.director.root)?void 0:e.dataPoolManager;return n&&n.releaseAnimationClip(this),J.destroy(this),t.prototype.destroy.call(this)},i[X]=function(t,e,n){for(var i=1/e,r=this._collectAnimatedJoints(),a=r.length,s={},o=0;o<a;++o)s[r[o]]={transforms:Array.from({length:n},(function(){return new V}))};var u=r.reduce((function(t,e){return t[e]=new Mi,t}),{});for(var h in u){var c=u[h],l=h.lastIndexOf("/");if(l>=0){var f=h.substring(0,l),p=u[f];p&&(c.parent=p)}}for(var _=this._createEvalWithBinder(void 0,(function(t){var e=t.parseTrsPath();if(e){var n=u[e.node];if(n)return Ui(n,e.property)}}),void 0),v=0;v<n;++v){var d=t+i*v;_.evaluate(d);for(var y=0;y<a;++y){var m=r[y];V.copy(s[m].transforms[v],u[m].globalTransform)}for(var g=0;g<a;++g){var T=r[g];u[T].invalidate()}}return{samples:e,frames:n,joints:s}},i.upgradeUntypedTracks=function(t){for(var e=[],n=[],i=this._tracks,r=i.length,a=0;a<r;++a){var s=i[a];if(s instanceof je){var o=s.upgrade(t);o&&(e.push(o),n.push(s))}}for(var u=n.length,h=0;h<u;++h)f(i,n[h]);i.push.apply(i,e)},i[Ti]=function(){return this._searchForRootBonePath()},i.getPropertyCurves=function(){return this._getLegacyData().getPropertyCurves()},i.updateEventDatas=function(){this.events=this._events},i.hasEvents=function(){return 0!==this.events.length},i.syncLegacyData=function(){this._legacyData&&(this._fromLegacy(this._legacyData),this._legacyData=void 0)},i[Ai]=function(){return this._embeddedPlayers},i[Pi]=function(t){this._embeddedPlayers.push(t)},i[Si]=function(t){var e=this._embeddedPlayers.indexOf(t);e>=0&&this._embeddedPlayers.splice(e,1)},i[Ci]=function(){this._embeddedPlayers.length=0},i.getAuxiliaryCurveNames_experimental=function(){return this._auxiliaryCurveEntries.map((function(t){return t.name}))},i.hasAuxiliaryCurve_experimental=function(t){return!!this._findAuxiliaryCurveEntry(t)},i.addAuxiliaryCurve_experimental=function(t){var e=this._findAuxiliaryCurveEntry(t);return e||((e=new gi).name=t,this._auxiliaryCurveEntries.push(e)),e.curve},i.getAuxiliaryCurve_experimental=function(t){return this._findAuxiliaryCurveEntry(t).curve},i.renameAuxiliaryCurve_experimental=function(t,e){var n=this._findAuxiliaryCurveEntry(t);n&&(n.name=e)},i.removeAuxiliaryCurve_experimental=function(t){p(this._auxiliaryCurveEntries,(function(e){return e.name===t}))},i._trySyncLegacyData=function(){this._legacyDataDirty&&(this._legacyDataDirty=!1,this.syncLegacyData())},i._createEvalWithBinder=function(t,e,n){this._legacyDataDirty&&(this._legacyDataDirty=!1,this.syncLegacyData());var i,r=[];n&&(i=this._createRootMotionEvaluation(t,n,r));for(var a,s=[],o=this._tracks,u=o.length,c=0;c<u;++c){var l=o[c];if(!r.includes(l)&&!Array.from(l.channels()).every((function(t){return 0===t.curve.keyFramesCount}))){var f=e(l[Dt]);if(f){var p=void 0;if(l instanceof je){if(!f.getValue){h(3930);continue}var _=f.getValue();p=l.createLegacyEval(_)}else p=l[nt]();s.push(new Ii(f,p))}}}return this._exoticAnimation&&(a=this._exoticAnimation.createEvaluator(e)),new Ni(s,a,i)},i._createRootMotionEvaluation=function(t,e,n){if(t instanceof Z){var i=this._searchForRootBonePath();if(i){var r=t.getChildByPath(i);if(r){for(var a=new Li,s=[],u=this._tracks,c=u.length,l=0;l<c;++l){var f=u[l],p=f[Dt].parseTrsPath();if(p&&p.node===i){n.push(f);var _=Ui(a,p.property);if(_){var v=f[nt]();s.push(new Ii(_,v))}}}return new Fi(r,this._duration,a,s)}o(3924)}else o(3923)}else h(3920)},i._searchForRootBonePath=function(){var t=this._tracks.map((function(t){var e=t[Dt].parseTrsPath();if(e){var n=e.node;return{path:n,rank:n.split("/").length}}return{path:"",rank:0}}));t.sort((function(t,e){return t.rank-e.rank}));var e=t.findIndex((function(t){return 0!==t.rank}));if(e<0)return"";for(var n=t.length,i=t[e],r=!0,a=e+1;a<n;++a){var s=t[a];if(s.rank!==i.rank)break;if(s.path!==i.path){r=!1;break}}return r?i.path:""},i._getLegacyData=function(){return this._legacyData||(this._legacyData=this._toLegacy()),this._legacyData},i._toLegacy=function(){var t=new qe(this._duration);return t.keys=[],t.curves=[],t.commonTargets=[],t},i._fromLegacy=function(t){for(var e=t.toTracks(),n=e.length,i=0;i<n;++i)this.addTrack(e[i])},i._collectAnimatedJoints=function(){for(var t=new Set,e=this._tracks,n=e.length,i=0;i<n;++i){var r=e[i][Dt].parseTrsPath();r&&t.add(r.node)}if(this._exoticAnimation)for(var a=this._exoticAnimation.collectAnimatedJoints(),s=a.length,o=0;o<s;++o)t.add(a[o]);return Array.from(t)},i._findAuxiliaryCurveEntry=function(t){return this._auxiliaryCurveEntries.find((function(e){return e.name===t}))},n(e,[{key:"duration",get:function(){return this._duration},set:function(t){this._duration=t}},{key:"tracksCount",get:function(){return this._tracks.length}},{key:"tracks",get:function(){return this._tracks}},{key:"hash",get:function(){var t,e;if(this._hash)return this._hash;var n="Exotic:"+(null!==(t=null==(e=this._exoticAnimation)?void 0:e.toHashString())&&void 0!==t?t:"");return this._hash=$(n,666)}},{key:"events",get:function(){return this._events},set:function(t){var e=this;this._events=t;for(var n=[],i=[],r=this.events.sort((function(t,e){return t.frame-e.frame})),a=r.length,s=function(){var t=r[o],a=t.frame/e._duration,s=n.findIndex((function(t){return t===a}));s<0&&(s=n.length,n.push(a),i.push({events:[]})),i[s].events.push({functionName:t.func,parameters:t.params})},o=0;o<a;++o)s();this._runtimeEvents={ratios:n,eventGroups:i}}},{key:Ei,get:function(){return this._exoticAnimation}},{key:Ei,set:function(t){this._exoticAnimation=t}},{key:"isAdditive_experimental",get:function(){return this._additiveSettings.enabled}},{key:ki,get:function(){return this._additiveSettings}},{key:"keys",get:function(){return this._getLegacyData().keys}},{key:"keys",set:function(t){this._legacyDataDirty=!0,this._getLegacyData().keys=t}},{key:"curves",get:function(){return this._legacyDataDirty=!0,this._getLegacyData().curves}},{key:"curves",set:function(t){this._getLegacyData().curves=t}},{key:"commonTargets",get:function(){return this._getLegacyData().commonTargets}},{key:"commonTargets",set:function(t){this._legacyDataDirty=!0,this._getLegacyData().commonTargets=t}},{key:"data",get:function(){return this._getLegacyData().data}},{key:"eventGroups",get:function(){return this._runtimeEvents.eventGroups}},{key:wi,get:function(){return this._embeddedPlayers.length}},{key:"auxiliaryCurveCount_experimental",get:function(){return this._auxiliaryCurveEntries.length}}]),e}(G),_i.WrapMode=tt,ni=E((ei=_i).prototype,"sample",[w],(function(){return 60})),ii=E(ei.prototype,"speed",[w],(function(){return 1})),ri=E(ei.prototype,"wrapMode",[w],(function(){return tt.Normal})),ai=E(ei.prototype,"enableTrsBlending",[w],(function(){return!1})),si=E(ei.prototype,"_duration",[w],(function(){return 0})),oi=E(ei.prototype,"_hash",[w],(function(){return 0})),ui=E(ei.prototype,"_tracks",[w],(function(){return[]})),hi=E(ei.prototype,"_exoticAnimation",[w],(function(){return null})),ci=E(ei.prototype,"_events",[w],(function(){return[]})),li=E(ei.prototype,"_embeddedPlayers",[w],(function(){return[]})),fi=E(ei.prototype,"_additiveSettings",[w],(function(){return new xi})),pi=E(ei.prototype,"_auxiliaryCurveEntries",[w],(function(){return[]})),ti=ei))||ti),xi=T("cc.AnimationClipAdditiveSettings")((di=function(){this.enabled=yi&&yi(),this.refClip=mi&&mi()},yi=E(di.prototype,"enabled",[w],(function(){return!1})),mi=E(di.prototype,"refClip",[w],(function(){return null})),vi=di))||vi;K.AnimationClip=bi;var Ii=function(){function t(t,e){this._binding=void 0,this._trackEval=void 0,this._shouldEvaluateDefault=!0,this._binding=t,this._trackEval=e,this._shouldEvaluateDefault=!!t.getValue&&e.requiresDefault}return t.prototype.evaluate=function(t){var e=this._binding,n=this._trackEval,i=this._shouldEvaluateDefault?e.getValue():void 0,r=n.evaluate(t,i);e.setValue(r)},t}(),Oi=function(){function t(t,e){this._embeddedPlayers=t,this._embeddedPlayerEvaluationInfos=t.map((function(t){var n=t.playable;if(!n)return null;var i=n.instantiate(e);return i?{instantiatedPlayer:i,entered:!1,hostPauseTime:0,lastIterations:0}:null}))}var e=t.prototype;return e.destroy=function(){for(var t=this._embeddedPlayerEvaluationInfos,e=t.length,n=0;n<e;++n){var i;null==(i=t[n])||i.instantiatedPlayer.destroy()}this._embeddedPlayerEvaluationInfos.length=0},e.evaluate=function(t,e){for(var n=this._embeddedPlayers,i=this._embeddedPlayerEvaluationInfos,r=n.length,a=0;a<r;++a){var s=i[a];if(s){var o=s.entered,u=s.instantiatedPlayer,h=s.lastIterations,c=n[a],l=c.begin,f=c.end;if(t>=l&&t<=f?o?e!==h&&(u.stop(),u.play(),s.entered=!0):(u.play(),s.entered=!0):o&&(u.stop(),s.entered=!1),s.lastIterations=e,s.entered){var p=t-l;s.instantiatedPlayer.setTime(p)}}}},e.notifyHostSpeedChanged=function(t){for(var e=this._embeddedPlayers,n=this._embeddedPlayerEvaluationInfos,i=e.length,r=0;r<i;++r){var a=n[r];if(a){var s=a.instantiatedPlayer;e[r].reconciledSpeed&&s.setSpeed(t)}}},e.notifyHostPlay=function(t){for(var e=this._embeddedPlayers,n=this._embeddedPlayerEvaluationInfos,i=e.length,r=0;r<i;++r){var a=n[r];if(a){var s=e[r],o=s.begin,u=s.end,h=a.instantiatedPlayer;if(a.entered){var c=a.hostPauseTime;if(h.randomAccess||H(c,t,1e-5)){var l=W(t,o,u);h.play(),h.setTime(l-o)}else h.stop()}}}},e.notifyHostPause=function(t){for(var e=this._embeddedPlayers,n=this._embeddedPlayerEvaluationInfos,i=e.length,r=0;r<i;++r){var a=n[r];if(a){var s=a.instantiatedPlayer;a.entered&&(s.pause(),a.hostPauseTime=t)}}},e.notifyHostStop=function(){for(var t=this._embeddedPlayers,e=this._embeddedPlayerEvaluationInfos,n=t.length,i=0;i<n;++i){var r=e[i];if(r){var a=r.instantiatedPlayer;r.entered&&(r.entered=!1,a.stop())}}},t}(),Ni=function(){function t(t,e,n){this._exoticAnimationEvaluator=void 0,this._trackEvalStatues=[],this._rootMotionEvaluation=void 0,this._trackEvalStatues=t,this._exoticAnimationEvaluator=e,this._rootMotionEvaluation=n}var e=t.prototype;return e.evaluate=function(t){for(var e=this._trackEvalStatues,n=this._exoticAnimationEvaluator,i=e.length,r=0;r<i;++r)e[r].evaluate(t);n&&n.evaluate(t)},e.evaluateRootMotion=function(t,e){var n=this._rootMotionEvaluation;n&&n.evaluate(t,e)},t}(),Li=function(){function t(){this.position=new S,this.scale=new S(1,1,1),this.rotation=new k,this.eulerAngles=new S}return t.prototype.getTransform=function(t){V.fromRTS(t,this.rotation,this.position,this.scale)},t}(),Mi=function(t){function e(){for(var e,n=arguments.length,i=new Array(n),r=0;r<n;r++)i[r]=arguments[r];return(e=t.call.apply(t,[this].concat(i))||this).parent=null,e._dirty=!0,e._transform=new V,e}return r(e,t),e.prototype.invalidate=function(){this._dirty=!0},n(e,[{key:"globalTransform",get:function(){var t=this._transform;return this._dirty&&(this._dirty=!1,V.fromRTS(t,this.rotation,this.position,this.scale),this.parent&&V.multiply(t,this.parent.globalTransform,t)),this._transform}}]),e}(Li),Ri=new V,Fi=function(){function t(t,e,n,i){this._initialTransformCache=new V,this._clipEndTransformCache=new V,this._startTransformCache=new V,this._endTransformCache=new V,this._motionTransformCache=new V,this._translationMotionCache=new S,this._rotationMotionCache=new k,this._scaleMotionCache=new S,this._rootBone=t,this._duration=e,this._boneTransform=n,this._trackEvalStatuses=i}var e=t.prototype;return e.evaluate=function(t,e){var n=this._calcMotionTransform(t,e,this._motionTransformCache),i=this._translationMotionCache,r=this._rotationMotionCache,a=this._scaleMotionCache,s=this._rootBone;V.toSRT(n,r,i,a),S.add(i,i,s.position),s.setPosition(i),k.multiply(r,r,s.rotation),s.setRotation(r),S.multiply(a,a,s.scale),s.setScale(a)},e._calcMotionTransform=function(t,e,n){var i=this._duration,r=i-t,a=this._evaluateAt(t,this._startTransformCache);if(e<r){var s=this._evaluateAt(t+e,this._endTransformCache);Di(n,a,s)}else{V.identity(n);var o=function(t,e){Di(Ri,t,e),V.multiply(n,n,Ri)},u=e-r,h=Math.floor(u/i),c=u-h*i,l=this._evaluateAt(0,this._initialTransformCache),f=this._evaluateAt(i,this._clipEndTransformCache),p=this._evaluateAt(c,this._endTransformCache);o(a,f),Di(Ri,l,f);for(var _=0;_<h;++_)V.multiply(n,n,Ri);o(l,p)}return n},e._evaluateAt=function(t,e){for(var n=this._trackEvalStatuses,i=n.length,r=0;r<i;++r)n[r].evaluate(t);return this._boneTransform.getTransform(e),e},t}();function Di(t,e,n){V.invert(t,e),V.multiply(t,n,t)}function Ui(t,e){switch(e){default:return;case"position":return{setValue:function(e){S.copy(t.position,e)}};case"rotation":return{setValue:function(e){k.copy(t.rotation,e)}};case"scale":return{setValue:function(e){S.copy(t.scale,e)}};case"eulerAngles":return{setValue:function(e){S.copy(t.eulerAngles,e)}}}}var Bi=function(){function t(t,e,n,i){this._lastFrameIndex=-1,this._lastIterations=0,this._lastDirection=0,this._ignoreIndex=-1,this._sampled=!1,this._targetNode=t,this._ratios=e,this._eventGroups=n,this._wrapMode=i}var e=t.prototype;return e.setWrapMode=function(t){this._wrapMode=t},e.ignore=function(t,e){this._ignoreIndex=-1,this._sampled=!1;var n=Vi(t,this._ratios);n<0&&(n=~n-1,e<0&&(n+=1),this._ignoreIndex=n)},e.reset=function(){this._lastFrameIndex=-1,this._lastIterations=0,this._lastDirection=0,this._ignoreIndex=-1,this._sampled=!1},e.sample=function(t,e,n){if(0!==this._eventGroups.length){var i=this._eventGroups.length,r=Vi(t,this._ratios);if(r<0&&(r=~r-1,e<0&&(r+=1)),this._ignoreIndex!==r&&(this._ignoreIndex=-1),!this._sampled)return this._sampled=!0,this._doFire(r,!1),this._lastFrameIndex=r,this._lastIterations=n,void(this._lastDirection=e);var a=this._wrapMode,s=zi(n),o=zi(this._lastIterations),u=this._lastFrameIndex,h=this._lastDirection,c=-1!==o&&s!==o;if(u===r&&c&&1===i)this._doFire(0,!1);else if(u!==r||c){e=h;do{if(u!==r){if(-1===e&&0===u&&r>0?((a&g.PingPong)===g.PingPong?e*=-1:u=i,o++):1===e&&u===i-1&&r<i-1&&((a&g.PingPong)===g.PingPong?e*=-1:u=-1,o++),u===r)break;if(o>s)break}u+=e,this._doFire(u,!0)}while(u!==r&&u>-1&&u<i)}this._lastFrameIndex=r,this._lastIterations=n,this._lastDirection=e}},e._doFire=function(t,e){e?ot().pushDelayEvent(this._checkAndFire,this,[t]):this._checkAndFire(t)},e._checkAndFire=function(t){if(this._targetNode&&this._targetNode.isValid){var e=this._eventGroups;if(!(t<0||t>=e.length||this._ignoreIndex===t))for(var n=e[t],i=n.events.length,r=0;r<i;++r){var a=n.events[r];Oe(this._targetNode,a.functionName,a.parameters)}}},t}();function zi(t){return t-(0|t)==0&&(t-=1),0|t}function Vi(t,e){return L(e,t)}var Hi,Wi,Qi,ji,qi,Ki,Gi,Yi,Ji,Xi,Zi,$i=function(t){function e(e){var n;return(n=t.call(this)||this)._managedStates=[],n._fadings=[],n._scheduled=!1,n._scheduler=null!=e?e:ot(),n}r(e,t);var n=e.prototype;return n.update=function(t){if(!this.isMotionless){var e=this._managedStates,n=this._fadings;if(1===e.length&&1===n.length){var i=e[0].state;i&&(i.weight=1)}else this._calculateWeights(t);1===e.length&&1===n.length&&this._unscheduleThis()}},n.crossFade=function(t,e){var n;0===this._managedStates.length&&(e=0),0===e&&this.clear();var i=this._managedStates.find((function(e){return e.state===t}));i?null!=(n=i.state)&&n.isMotionless&&i.state.play():(i={state:t,reference:0},t&&t.play(),this._managedStates.push(i)),++i.reference,this._fadings.unshift({easeDuration:e,easeTime:0,target:i}),this.isMotionless||this._scheduleThis()},n.clear=function(){for(var t=0;t<this._managedStates.length;++t){var e=this._managedStates[t].state;e&&e.stop()}this._managedStates.length=0,this._fadings.length=0},n.onPlay=function(){t.prototype.onPlay.call(this),this._scheduleThis()},n.onPause=function(){t.prototype.onPause.call(this);for(var e=0;e<this._managedStates.length;++e){var n=this._managedStates[e].state;n&&n.pause()}this._unscheduleThis()},n.onResume=function(){t.prototype.onResume.call(this);for(var e=0;e<this._managedStates.length;++e){var n=this._managedStates[e].state;n&&n.resume()}this._scheduleThis()},n.onStop=function(){t.prototype.onStop.call(this),this.clear()},n._calculateWeights=function(t){for(var e=this._managedStates,n=this._fadings,i=0;i<e.length;++i){var r=e[i].state;r&&(r.weight=0)}for(var a=1,s=n.length,o=0;o<n.length;++o){var u=n[o];u.easeTime+=t;var h=0===u.easeDuration?1:Q(u.easeTime/u.easeDuration),c=h*a;if(a*=1-h,u.target.state&&(u.target.state.weight+=c),u.easeTime>=u.easeDuration){s=o+1,u.easeTime=u.easeDuration;break}}if(s!==n.length){for(var l=s;l<n.length;++l){var p=n[l];--p.target.reference,p.target.reference<=0&&(p.target.state&&p.target.state.stop(),f(this._managedStates,p.target))}n.splice(s)}},n._scheduleThis=function(){this._scheduled||(this._scheduler.addCrossFade(this),this._scheduled=!0)},n._unscheduleThis=function(){this._scheduled&&(this._scheduler.removeCrossFade(this),this._scheduled=!1)},e}(it),tr=t("a",(Hi=T("cc.Animation"),Wi=q(99),Qi=j([bi]),ji=j(bi),qi=j([bi]),Hi(Ki=Wi((Zi=function(t){function e(){for(var e,n=arguments.length,i=new Array(n),r=0;r<n;r++)i[r]=arguments[r];return(e=t.call.apply(t,[this].concat(i))||this).playOnLoad=Yi&&Yi(),e._crossFade=new $i,e._nameToState=_(!0),e._clips=Ji&&Ji(),e._defaultClip=Xi&&Xi(),e._hasBeenPlayed=!1,e}r(e,t);var i=e.prototype;return i.onLoad=function(){for(var t in this.clips=this._clips,this._nameToState)this._nameToState[t].initialize(this.node)},i.start=function(){this.playOnLoad&&!this._hasBeenPlayed&&this._defaultClip&&this.crossFade(this._defaultClip.name,0)},i.onEnable=function(){this._crossFade.resume()},i.onDisable=function(){this._crossFade.pause()},i.onDestroy=function(){for(var t in this._crossFade.stop(),this._nameToState)this._nameToState[t].destroy();this._nameToState=_(!0)},i.play=function(t){if(this._hasBeenPlayed=!0,!t){if(!this._defaultClip)return;t=this._defaultClip.name}this.crossFade(t,0)},i.crossFade=function(t,e){void 0===e&&(e=.3),this._hasBeenPlayed=!0;var n=this._nameToState[t];n&&this.doPlayOrCrossFade(n,e)},i.pause=function(){this._crossFade.pause()},i.resume=function(){this._crossFade.resume()},i.stop=function(){this._crossFade.stop()},i.getState=function(t){var e=this._nameToState[t];return e&&!e.curveLoaded&&e.initialize(this.node),e||null},i.createState=function(t,e){return e=e||t.name,this.removeState(e),this._doCreateState(t,e)},i.removeState=function(t){var e=this._nameToState[t];e&&(e.allowLastFrameEvent(!1),e.stop(),delete this._nameToState[t])},i.addClip=function(t,e){return v(this._clips,t)||this._clips.push(t),this.createState(t,e)},i.removeClip=function(t,e){var n;for(var i in this._nameToState){var r=this._nameToState[i];if(r.clip===t){n=r;break}}if(t===this._defaultClip){if(!e)return void o(3902);this._defaultClip=null}if(n&&n.isPlaying){if(!e)return void o(3903);n.stop()}this._clips=this._clips.filter((function(e){return e!==t})),n&&delete this._nameToState[n.name]},i.on=function(e,n,i,r){var a=t.prototype.on.call(this,e,n,i,r);return e===rt.LASTFRAME&&this._syncAllowLastFrameEvent(),a},i.once=function(e,n,i){var r=t.prototype.once.call(this,e,n,i);return e===rt.LASTFRAME&&this._syncAllowLastFrameEvent(),r},i.off=function(e,n,i){t.prototype.off.call(this,e,n,i),e===rt.LASTFRAME&&this._syncDisallowLastFrameEvent()},i._createState=function(t,e){return new _t(t,e)},i._doCreateState=function(t,e){var n=this._createState(t,e);return n._setEventTarget(this),n.allowLastFrameEvent(this.hasEventListener(rt.LASTFRAME)),this.node&&n.initialize(this.node),this._nameToState[n.name]=n,n},i.doPlayOrCrossFade=function(t,e){this._crossFade.play(),this._crossFade.crossFade(t,e)},i._removeStateOfAutomaticClip=function(t){for(var e in this._nameToState){var n=this._nameToState[e];er(t,n.clip)&&(n.stop(),delete this._nameToState[e])}},i._syncAllowLastFrameEvent=function(){if(this.hasEventListener(rt.LASTFRAME))for(var t in this._nameToState)this._nameToState[t].allowLastFrameEvent(!0)},i._syncDisallowLastFrameEvent=function(){if(!this.hasEventListener(rt.LASTFRAME))for(var t in this._nameToState)this._nameToState[t].allowLastFrameEvent(!1)},n(e,[{key:"clips",get:function(){return this._clips},set:function(t){var e=this;this._crossFade&&this._crossFade.clear(),this._clips.forEach((function(t){t&&e._removeStateOfAutomaticClip(t)})),t.forEach((function(t){t&&e.createState(t)}));var n=t.find((function(t){return er(t,e._defaultClip)}));this._defaultClip=n||null,this._clips=t}},{key:"defaultClip",get:function(){return this._defaultClip},set:function(t){this._defaultClip=t,t&&(this._clips.findIndex((function(e){return er(e,t)}))>=0||(this._clips.push(t),this.createState(t)))}}]),e}(d(Y)),Zi.EventType=rt,y((Gi=Zi).prototype,"clips",[Qi],Object.getOwnPropertyDescriptor(Gi.prototype,"clips"),Gi.prototype),y(Gi.prototype,"defaultClip",[ji],Object.getOwnPropertyDescriptor(Gi.prototype,"defaultClip"),Gi.prototype),Yi=E(Gi.prototype,"playOnLoad",[w],(function(){return!1})),Ji=E(Gi.prototype,"_clips",[qi],(function(){return[]})),Xi=E(Gi.prototype,"_defaultClip",[w],(function(){return null})),Ki=Gi))||Ki)||Ki));function er(t,e){return t===e||!!t&&!!e&&t._uuid===e._uuid&&t._uuid}K.Animation=tr,K.AnimationComponent=tr,m(tr,"cc.AnimationComponent")}}}));
