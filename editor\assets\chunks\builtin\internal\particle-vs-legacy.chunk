
precision highp float;
#include <common/math/transform>
#include <builtin/internal/particle-common>
#include <builtin/uniforms/cc-local>

in vec3 a_texCoord1;  // size
in vec3 a_texCoord2;  // rotation
in vec4 a_color;

in vec3 a_texCoord;  // xy:vertex index,z:frame index
#if !CC_INSTANCE_PARTICLE
 in vec3 a_position;  // center position
#endif
#if CC_INSTANCE_PARTICLE
 in vec4 a_texCoord4; // xyz:position,z:frame index
#endif

#if CC_RENDER_MODE == RENDER_MODE_STRETCHED_BILLBOARD
  in vec3 a_color1; // velocity.x, velocity.y, velocity.z, scale
#endif

#if CC_RENDER_MODE == RENDER_MODE_MESH
  in vec3 a_texCoord3;  // mesh vertices
  in vec3 a_normal;     // mesh normal
  in vec4 a_color1;     // mesh color
#endif

vec4 lpvs_main () {
  vec3 compScale = scale.xyz * a_texCoord1;
  #if !CC_INSTANCE_PARTICLE
    vec4 pos = vec4(a_position.xyz, 1);
  #endif
  #if CC_INSTANCE_PARTICLE
    vec4 pos = vec4(a_texCoord4.xyz, 1);
  #endif
  #if CC_RENDER_MODE == RENDER_MODE_STRETCHED_BILLBOARD
    vec4 velocity = vec4(a_color1.xyz, 0);
  #endif

  #if !CC_USE_WORLD_SPACE
    // simulate in world space. apply cc_matWorld matrix on CPU side.
    pos = cc_matWorld * pos;
    #if CC_RENDER_MODE == RENDER_MODE_STRETCHED_BILLBOARD
      velocity = cc_matWorld * velocity;
    #endif
  #endif

  #pragma define INDENTIFY_NEG_QUAT 10.0

  #if ROTATION_OVER_TIME_MODULE_ENABLE
    vec3 rotTmp = a_texCoord2;
    float mulFactor = 1.0;
    if (rotTmp.x > INDENTIFY_NEG_QUAT * 0.5) {
        rotTmp.x -= INDENTIFY_NEG_QUAT;
        mulFactor = -1.0;
    }
    vec4 rot = vec4(rotTmp, 0.0);
    rot.w = mulFactor * sqrt(abs(1.0 - rot.x * rot.x - rot.y * rot.y - rot.z * rot.z));
  #endif
  #if !ROTATION_OVER_TIME_MODULE_ENABLE
    #if CC_RENDER_MODE != RENDER_MODE_MESH
      #if CC_RENDER_MODE == RENDER_MODE_BILLBOARD
        vec3 rotEuler = a_texCoord2;
      #elif CC_RENDER_MODE == RENDER_MODE_STRETCHED_BILLBOARD
        vec3 rotEuler = vec3(0.);
      #endif
      #if CC_RENDER_MODE != RENDER_MODE_BILLBOARD && CC_RENDER_MODE != RENDER_MODE_STRETCHED_BILLBOARD
        vec3 rotEuler = vec3(0., 0., a_texCoord2.z);
      #endif
      vec4 rot = quaternionFromEuler(rotEuler);
    #endif
    #if CC_RENDER_MODE == RENDER_MODE_MESH
      vec4 rot = quaternionFromEuler(a_texCoord2);
    #endif
  #endif

  #if CC_RENDER_MODE != RENDER_MODE_MESH
    vec2 cornerOffset = vec2((a_texCoord.xy - 0.5));

    #if CC_RENDER_MODE == RENDER_MODE_BILLBOARD || CC_RENDER_MODE == RENDER_MODE_VERTICAL_BILLBOARD
      computeVertPos(pos, cornerOffset, rot, compScale, cc_matViewInv);
    #elif CC_RENDER_MODE == RENDER_MODE_STRETCHED_BILLBOARD
      computeVertPos(pos, cornerOffset, rot, compScale, cc_cameraPos.xyz, velocity, frameTile_velLenScale.z, frameTile_velLenScale.w, a_texCoord.x);
    #elif RENDER_MODE_HORIZONTAL_BILLBOARD
      computeVertPos(pos, cornerOffset, rot, compScale);
    #endif

    color = a_color;
  #endif
  #if CC_RENDER_MODE == RENDER_MODE_MESH
    mat3 rotMat = quatToMat3(rot);
    mat3 nodeMat = quatToMat3(nodeRotation);
    rotMat = nodeMat * rotMat;
    rot = mat3ToQuat(rotMat);

    mat4 xformNoScale = matrixFromRT(rot, pos.xyz);
    mat4 xform = matFromRTS(rot, pos.xyz, compScale);
    pos = xform * vec4(a_texCoord3, 1);
    vec4 normal = xformNoScale * vec4(a_normal, 0);
    color = a_color * a_color1;
  #endif

  #if !CC_INSTANCE_PARTICLE
    uv = computeUV(a_texCoord.z, a_texCoord.xy, frameTile_velLenScale.xy) * mainTiling_Offset.xy + mainTiling_Offset.zw;
  #endif
  #if CC_INSTANCE_PARTICLE
    uv = computeUV(a_texCoord4.w, a_texCoord.xy, frameTile_velLenScale.xy) * mainTiling_Offset.xy + mainTiling_Offset.zw;
  #endif

  pos = cc_matViewProj * pos;

  return pos;
}
