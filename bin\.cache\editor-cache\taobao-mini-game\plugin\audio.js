System.register(["./gc-object-D18ulfCO.js","./global-exports-CLZKKIY2.js","./index-Y4La_nfG.js","./component-CsuvAQKv.js","./factory-BOc5khhM.js","./device-manager-B8lyUDPi.js","./buffer-barrier-q_79u36H.js"],(function(t){"use strict";var e,n,o,i,r,a,u,s,l,c,p,h,_,d,f,y,g,v,m,E,P,I,A,T;return{setters:[function(t){e=t.a,n=t.m,o=t.Z,i=t.U,r=t.b,a=t._,u=t.H,s=t.F,l=t.j},function(t){c=t.l,p=t.c},function(t){h=t.g,_=t.O,d=t.c,f=t.a,y=t.H,g=t.s,v=t.t,m=t.r,E=t.o},function(t){P=t.A,I=t.C},function(t){A=t.d,T=t.f},null,null],execute:function(){var N,S,k;function C(t){for(var e=t._operationQueue.length,n=t._operationQueue.slice(),o=[],i=!1,r=e-1;r>=0;r--){var a=n[r];if("stop"===a.op){o.push(a);break}if("seek"===a.op)i||(o.push(a),i=!0);else{if(i){o.push(a);break}0===o.length&&o.push(a)}}t._operationQueue=o.reverse()}!function(t){t.PLAYED="play",t.PAUSED="pause",t.STOPPED="stop",t.SEEKED="seeked",t.ENDED="ended",t.INTERRUPTION_BEGIN="interruptionBegin",t.INTERRUPTION_END="interruptionEnd",t.USER_GESTURE="on_gesture"}(N||(N={})),function(t){t[t.DOM_AUDIO=0]="DOM_AUDIO",t[t.WEB_AUDIO=1]="WEB_AUDIO",t[t.MINIGAME_AUDIO=2]="MINIGAME_AUDIO",t[t.NATIVE_AUDIO=3]="NATIVE_AUDIO",t[t.UNKNOWN_AUDIO=4]="UNKNOWN_AUDIO"}(S||(S={})),function(t){t[t.INIT=0]="INIT",t[t.PLAYING=1]="PLAYING",t[t.PAUSED=2]="PAUSED",t[t.STOPPED=3]="STOPPED",t[t.INTERRUPTED=4]="INTERRUPTED"}(k||(k={})),t("AudioPCMDataView",function(){function t(){this._bufferView=void 0,this._normalizeFactor=1;for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];if(2===e.length)this._bufferView=e[0],this._normalizeFactor=e[1];else{var o=e[0],i=e[1],r=e[2];this._bufferView=new i(o),this._normalizeFactor=r}}return t.prototype.getData=function(t){return this._bufferView[t]*this._normalizeFactor},e(t,[{key:"length",get:function(){return this._bufferView.length}}]),t}());var D,O=0;function L(t,e){var n;e.invoking||(e.invoking=!0,(n=e.func).call.apply(n,[t].concat(e.args)).then((function(){e.invoking=!1,t._operationQueue.shift(),t._eventTarget.emit(e.id.toString()),t._eventTarget.off(e.id.toString()),C(t);var n=t._operationQueue[0];n&&L(t,n)})).catch((function(){})))}function w(t,e,n){var o=n.value;n.value=function(){for(var t=this,n=arguments.length,i=new Array(n),r=0;r<n;r++)i[r]=arguments[r];return new Promise((function(n){var r=O++,a=t;a._operationQueue.push({op:e,id:r,func:o,args:i,invoking:!1}),a._eventTarget.once(r.toString(),n),L(a,a._operationQueue[0])}))}}var R,x,U,b,B=function(){function t(t,e){var n=this;this._onPlayCb=void 0,this._onEndCb=void 0,this._innerAudioContext=t,t.volume=e,t.onPlay((function(){null==n._onPlayCb||n._onPlayCb()}));var i=function(){n._innerAudioContext&&(n._innerAudioContext=null,o.off("hide",n._onInterruptedBegin,n),o.off("show",n._onInterruptedEnd,n),null==n._onEndCb||n._onEndCb(),t.destroy())};t.onEnded(i),t.onStop(i),o.on("hide",this._onInterruptedBegin,this),o.on("show",this._onInterruptedEnd,this)}var n=t.prototype;return n._onInterruptedBegin=function(){this._innerAudioContext.pause()},n._onInterruptedEnd=function(){this._innerAudioContext.play()},n.play=function(){this._innerAudioContext.play()},n.stop=function(){this._innerAudioContext.stop()},e(t,[{key:"onPlay",get:function(){return this._onPlayCb},set:function(t){this._onPlayCb=t}},{key:"onEnd",get:function(){return this._onEndCb},set:function(t){this._onEndCb=t}}]),t}(),M=(D=function(){var t=r.prototype;function r(t){this._state=k.INIT,this._cacheTime=0,this._needSeek=!1,this._seeking=!1,this._readyToHandleOnShow=!1,this._eventTarget=new n,this._operationQueue=[];var e=this;e._innerAudioContext=t,o.on("hide",e._onInterruptedBegin,e),o.on("show",e._onInterruptedEnd,e);var i=e._eventTarget;e._onPlay=function(){e._state=k.PLAYING,i.emit(N.PLAYED),e._needSeek&&e.seek(e._cacheTime).catch((function(){}))},t.onPlay(e._onPlay),e._onPause=function(){e._state=k.PAUSED;try{var t=e._innerAudioContext.currentTime;null!=t&&(e._cacheTime=t)}catch(t){}i.emit(N.PAUSED)},t.onPause(e._onPause),e._onStop=function(){e._state=k.STOPPED,e._resetSeekCache(),i.emit(N.STOPPED)},t.onStop(e._onStop),e._onSeeked=function(){i.emit(N.SEEKED),e._seeking=!1,e._needSeek&&(e._needSeek=!1,e._cacheTime.toFixed(2)!==e._innerAudioContext.currentTime.toFixed(2)&&e.seek(e._cacheTime).catch((function(){})))},t.onSeeked(e._onSeeked),e._onEnded=function(){e._state=k.INIT,e._resetSeekCache(),i.emit(N.ENDED)},t.onEnded(e._onEnded)}return t._resetSeekCache=function(){this._cacheTime=0,this._needSeek=!1,this._seeking=!1},t.destroy=function(){var t=this;o.off("hide",t._onInterruptedBegin,t),o.off("show",t._onInterruptedEnd,t);var e=t._innerAudioContext;e&&(["Play","Pause","Stop","Seeked","Ended"].forEach((function(e){t._offEvent(e)})),e.stop(),e.destroy(),t._innerAudioContext=null,t._state=k.INIT)},t._onInterruptedBegin=function(){var t=this;this._state===k.PLAYING&&this.pause().then((function(){t._state=k.INTERRUPTED,t._readyToHandleOnShow=!0,t._eventTarget.emit(N.INTERRUPTION_BEGIN)})).catch((function(){}))},t._onInterruptedEnd=function(){var t=this;this._readyToHandleOnShow?(this._state===k.INTERRUPTED&&this.play().then((function(){t._eventTarget.emit(N.INTERRUPTION_END)})).catch((function(){})),this._readyToHandleOnShow=!1):this._eventTarget.once(N.INTERRUPTION_END,this._onInterruptedEnd,this)},t._offEvent=function(t){this["_on"+t]&&(this._innerAudioContext["off"+t](this["_on"+t]),this["_on"+t]=null)},r.load=function(t){return new Promise((function(e,n){r.loadNative(t).then((function(t){e(new r(t))})).catch(n)}))},r.loadNative=function(t){return new Promise((function(e,n){var o=i.createInnerAudioContext(),r=setTimeout((function(){a(),e(o)}),8e3);function a(){o.offCanplay(u),o.offError(s)}function u(){a(),clearTimeout(r),e(o)}function s(t){a(),clearTimeout(r),console.error("failed to load innerAudioContext"),n(new Error(t))}o.onCanplay(u),o.onError(s),o.src=t}))},r.loadOneShotAudio=function(t,e){return new Promise((function(n,o){r.loadNative(t).then((function(t){n(new B(t,e))})).catch(o)}))},t.getPCMData=function(){},t.seek=function(t){var e=this;return new Promise((function(n){e._state!==k.PLAYING||e._seeking?(e._cacheTime!==t&&(e._cacheTime=t,e._needSeek=!0),n()):(t=h(t,0,e.duration),e._seeking=!0,e._cacheTime=t,e._eventTarget.once(N.SEEKED,n),e._innerAudioContext.seek(t))}))},t.play=function(){var t=this;return new Promise((function(e){t._eventTarget.once(N.PLAYED,e),t._innerAudioContext.play()}))},t.pause=function(){var t=this;return new Promise((function(e){t.state!==k.PLAYING?e():(t._eventTarget.once(N.PAUSED,e),t._innerAudioContext.pause())}))},t.stop=function(){var t=this;return new Promise((function(e){if(k.INIT===t._state)return t._resetSeekCache(),void e();t._eventTarget.once(N.STOPPED,e),t._innerAudioContext.stop()}))},t.onInterruptionBegin=function(t){this._eventTarget.on(N.INTERRUPTION_BEGIN,t)},t.offInterruptionBegin=function(t){this._eventTarget.off(N.INTERRUPTION_BEGIN,t)},t.onInterruptionEnd=function(t){this._eventTarget.on(N.INTERRUPTION_END,t)},t.offInterruptionEnd=function(t){this._eventTarget.off(N.INTERRUPTION_END,t)},t.onEnded=function(t){this._eventTarget.on(N.ENDED,t)},t.offEnded=function(t){this._eventTarget.off(N.ENDED,t)},e(r,[{key:"src",get:function(){return this._innerAudioContext?this._innerAudioContext.src:""}},{key:"type",get:function(){return S.MINIGAME_AUDIO}},{key:"state",get:function(){return this._state}},{key:"loop",get:function(){return this._innerAudioContext.loop},set:function(t){this._innerAudioContext.loop=t}},{key:"volume",get:function(){return this._innerAudioContext.volume},set:function(t){t=_(t),this._innerAudioContext.volume=t}},{key:"duration",get:function(){return this._innerAudioContext.duration}},{key:"currentTime",get:function(){return this._state!==k.PLAYING||this._needSeek||this._seeking?this._cacheTime:this._innerAudioContext.currentTime}},{key:"sampleRate",get:function(){return 0}}]),r}(),r(D.prototype,"seek",[w],Object.getOwnPropertyDescriptor(D.prototype,"seek"),D.prototype),r(D.prototype,"play",[w],Object.getOwnPropertyDescriptor(D.prototype,"play"),D.prototype),r(D.prototype,"pause",[w],Object.getOwnPropertyDescriptor(D.prototype,"pause"),D.prototype),r(D.prototype,"stop",[w],Object.getOwnPropertyDescriptor(D.prototype,"stop"),D.prototype),D),G=function(){function t(t){this._audio=void 0,this._audio=t}var n=t.prototype;return n.play=function(){this._audio.play()},n.stop=function(){this._audio.stop()},e(t,[{key:"onPlay",get:function(){return this._audio.onPlay},set:function(t){this._audio.onPlay=t}},{key:"onEnd",get:function(){return this._audio.onEnd},set:function(t){this._audio.onEnd=t}}]),t}(),j=function(){function t(t){this._player=void 0,this._player=t}t.load=function(e){return new Promise((function(n,o){M.load(e).then((function(e){n(new t(e))})).catch(o)}))};var n=t.prototype;return n.destroy=function(){this._player.destroy()},t.loadNative=function(t){return M.loadNative(t)},t.loadOneShotAudio=function(t,e){return new Promise((function(n,o){M.loadOneShotAudio(t,e).then((function(t){n(new G(t))})).catch(o)}))},n.getPCMData=function(t){return this._player.getPCMData(t)},n.seek=function(t){return this._player.seek(t)},n.play=function(){return this._player.play()},n.pause=function(){return this._player.pause()},n.stop=function(){return this._player.stop()},n.onInterruptionBegin=function(t){this._player.onInterruptionBegin(t)},n.offInterruptionBegin=function(t){this._player.offInterruptionBegin(t)},n.onInterruptionEnd=function(t){this._player.onInterruptionEnd(t)},n.offInterruptionEnd=function(t){this._player.offInterruptionEnd(t)},n.onEnded=function(t){this._player.onEnded(t)},n.offEnded=function(t){this._player.offEnded(t)},e(t,[{key:"src",get:function(){return this._player.src}},{key:"type",get:function(){return this._player.type}},{key:"state",get:function(){return this._player.state}},{key:"loop",get:function(){return this._player.loop},set:function(t){this._player.loop=t}},{key:"volume",get:function(){return this._player.volume},set:function(t){this._player.volume=t}},{key:"duration",get:function(){return this._player.duration}},{key:"currentTime",get:function(){return this._player.currentTime}},{key:"sampleRate",get:function(){return this._player.sampleRate}}]),t}();j.maxAudioChannel=10,c.AudioPlayer=j;var Y=t("AudioClip",d("cc.AudioClip")((b=function(t){function n(e){var n;return(n=t.call(this,e)||this)._duration=U&&U(),n._loadMode=S.UNKNOWN_AUDIO,n._meta=null,n._player=null,n}a(n,t);var o=n.prototype;return o.destroy=function(){var e,n=t.prototype.destroy.call(this);return null==(e=this._player)||e.destroy(),this._player=null,this._meta&&(this._meta.player=null),n},o.validate=function(){return!!this._meta},o.getDuration=function(){return this._duration?this._duration:this._meta?this._meta.duration:0},o.getCurrentTime=function(){return this._player?this._player.currentTime:0},o.getVolume=function(){return this._player?this._player.volume:0},o.getLoop=function(){return!!this._player&&this._player.loop},o.setCurrentTime=function(t){var e;null==(e=this._player)||e.seek(t).catch((function(){}))},o.setVolume=function(t){this._player&&(this._player.volume=t)},o.setLoop=function(t){this._player&&(this._player.loop=t)},o.play=function(){var t;null==(t=this._player)||t.play().catch((function(){}))},o.pause=function(){var t;null==(t=this._player)||t.pause().catch((function(){}))},o.stop=function(){var t;null==(t=this._player)||t.stop().catch((function(){}))},o.playOneShot=function(t){void 0===t&&(t=1),this._nativeAsset&&j.loadOneShotAudio(this._nativeAsset.url,t).then((function(t){t.play()})).catch((function(){}))},e(n,[{key:"duration",set:function(t){this._duration=t}},{key:"_nativeAsset",get:function(){return this._meta},set:function(t){this._meta=t,t?(this._loadMode=t.type,this._player=t.player):(this._meta=null,this._loadMode=S.UNKNOWN_AUDIO,this._duration=0)}},{key:"_nativeDep",get:function(){return{uuid:this._uuid,audioLoadMode:this.loadMode,ext:this._native,__isNative__:!0}}},{key:"loadMode",get:function(){return this._loadMode}},{key:"state",get:function(){return this._player?this._player.state:k.INIT}}]),n}(P),b.AudioType=S,U=f((x=b).prototype,"_duration",[g],(function(){return 0})),r(x.prototype,"_nativeDep",[y],Object.getOwnPropertyDescriptor(x.prototype,"_nativeDep"),x.prototype),R=x))||R);function V(t,e,n){j.load(t,{audioLoadMode:e.audioLoadMode}).then((function(e){var o={player:e,url:t,duration:e.duration,type:e.type};n(null,o)})).catch((function(t){n(t)}))}function K(t,e,n,o){var i=new Y;i._nativeUrl=t,i._nativeAsset=e,i.duration=e.duration,o(null,i)}p.AudioClip=Y,A.register({".mp3":V,".ogg":V,".wav":V,".m4a":V}),T.register({".mp3":K,".ogg":K,".wav":K,".m4a":K});var Q,F,H,W,z,Z,q,J,X,$,tt,et,nt=new(function(){function t(){this._oneShotAudioInfoList=[],this._audioPlayerInfoList=[]}var e=t.prototype;return e._findIndex=function(t,e){return t.findIndex((function(t){return t.audio===e}))},e._tryAddPlaying=function(t,e){var n=this._findIndex(t,e);if(n>-1)return t[n].playTime=performance.now(),!1;var o={audio:e,playTime:performance.now()};return t.push(o),!0},e.addPlaying=function(t){t instanceof j?this._tryAddPlaying(this._audioPlayerInfoList,t):this._tryAddPlaying(this._oneShotAudioInfoList,t)},e._tryRemovePlaying=function(t,e){var n=this._findIndex(t,e);return-1!==n&&(u(t,n),!0)},e.removePlaying=function(t){t instanceof j?this._tryRemovePlaying(this._audioPlayerInfoList,t):this._tryRemovePlaying(this._oneShotAudioInfoList,t)},e.discardOnePlayingIfNeeded=function(){var t;this._audioPlayerInfoList.length+this._oneShotAudioInfoList.length<j.maxAudioChannel||(this._oneShotAudioInfoList.length>0?this._oneShotAudioInfoList.forEach((function(e){(!t||e.playTime<t.playTime)&&(t=e)})):this._audioPlayerInfoList.forEach((function(e){(!t||e.playTime<t.playTime)&&(t=e)})),t&&(t.audio.stop(),this.removePlaying(t.audio)))},e.pause=function(){this._oneShotAudioInfoList.forEach((function(t){t.audio.stop()})),this._audioPlayerInfoList.forEach((function(t){t.audio.pause().catch((function(){}))}))},e.resume=function(){this._audioPlayerInfoList.forEach((function(t){t.audio.play().catch((function(){}))}))},t}()),ot="audiosource-loaded";!function(t){t.STARTED="started",t.ENDED="ended"}(tt||(tt={})),function(t){t.PLAY="play",t.STOP="stop",t.PAUSE="pause",t.SEEK="seek"}(et||(et={}));var it=(Q=d("cc.AudioSource"),F=v(Y),H=v(Y),Q(($=function(t){function n(){var e;return(e=t.call(this)||this)._clip=Z&&Z(),e._player=null,e._hasRegisterListener=!1,e._loop=q&&q(),e._playOnAwake=J&&J(),e._volume=X&&X(),e._cachedCurrentTime=-1,e._operationsBeforeLoading=[],e._isLoaded=!1,e._lastSetClip=null,e}a(n,t);var o=n.prototype;return o._resetPlayer=function(){this._player&&(nt.removePlaying(this._player),this._unregisterListener(),this._player.destroy(),this._player=null)},o._syncPlayer=function(){var t=this,e=this._clip;if(this._lastSetClip!==e)return e?void(e._nativeAsset?(this._isLoaded=!1,this._lastSetClip=e,this._operationsBeforeLoading.length=0,j.load(e._nativeAsset.url,{audioLoadMode:e.loadMode}).then((function(n){var o;t._lastSetClip===e?(t._isLoaded=!0,t._resetPlayer(),t._player=n,t._syncStates(),null==(o=t.node)||o.emit(ot)):n.destroy()})).catch((function(){}))):console.error("Invalid audio clip")):(this._lastSetClip=null,void this._resetPlayer())},o._registerListener=function(){var t=this;if(!this._hasRegisterListener&&this._player){var e=this._player;e.onEnded((function(){var n;nt.removePlaying(e),null==(n=t.node)||n.emit(tt.ENDED,t)})),e.onInterruptionBegin((function(){nt.removePlaying(e)})),e.onInterruptionEnd((function(){t._player===e&&nt.addPlaying(e)})),this._hasRegisterListener=!0}},o._unregisterListener=function(){this._player&&this._hasRegisterListener&&(this._player.offEnded(),this._player.offInterruptionBegin(),this._player.offInterruptionEnd(),this._hasRegisterListener=!1)},o.onLoad=function(){this._syncPlayer()},o.onEnable=function(){this._playOnAwake&&!this.playing&&this.play()},o.onDisable=function(){var t=this._getRootNode();null!=t&&t._persistNode||this.pause()},o.onDestroy=function(){this.stop(),this.clip=null},o.getPCMData=function(t){var e=this;return new Promise((function(n){if(0!==t&&1!==t)return s("Only support channel index 0 or 1 to get buffer"),void n(void 0);var o;e._player?n(e._player.getPCMData(t)):null==(o=e.node)||o.once(ot,(function(){var o;n(null==(o=e._player)?void 0:o.getPCMData(t))}))}))},o.getSampleRate=function(){var t=this;return new Promise((function(e){var n;t._player?e(t._player.sampleRate):null==(n=t.node)||n.once(ot,(function(){e(t._player.sampleRate)}))}))},o._getRootNode=function(){for(var t,e,n=this.node,o=null==(t=n)||null==(e=t.parent)?void 0:e.parent;o;){var i,r,a;o=null==(r=n=null==(i=n)?void 0:i.parent)||null==(a=r.parent)?void 0:a.parent}return n},o.play=function(){var t=this;if(this._isLoaded||!this.clip){var e;this._registerListener(),nt.discardOnePlayingIfNeeded(),this.state===k.PLAYING&&(null==(e=this._player)||e.stop().catch((function(){})));var n=this._player;n&&(n.play().then((function(){var e;null==(e=t.node)||e.emit(tt.STARTED,t)})).catch((function(){nt.removePlaying(n)})),nt.addPlaying(n))}else this._operationsBeforeLoading.push({op:et.PLAY,params:null})},o.pause=function(){var t;this._isLoaded||!this.clip?null==(t=this._player)||t.pause().catch((function(){})):this._operationsBeforeLoading.push({op:et.PAUSE,params:null})},o.stop=function(){this._isLoaded||!this.clip?this._player&&(this._player.stop().catch((function(){})),nt.removePlaying(this._player)):this._operationsBeforeLoading.push({op:et.STOP,params:null})},o.playOneShot=function(t,e){var n;void 0===e&&(e=1),t._nativeAsset?j.loadOneShotAudio(t._nativeAsset.url,this._volume*e,{audioLoadMode:t.loadMode}).then((function(t){n=t,nt.discardOnePlayingIfNeeded(),t.onEnd=function(){nt.removePlaying(t)},t.play(),nt.addPlaying(t)})).catch((function(){n&&nt.removePlaying(n)})):console.error("Invalid audio clip")},o._syncStates=function(){var t=this;this._player&&(this._player.loop=this._loop,this._player.volume=this._volume,this._operationsBeforeLoading.forEach((function(e){var n;e.op===et.SEEK?(t._cachedCurrentTime=e.params&&e.params[0],t._player&&t._player.seek(t._cachedCurrentTime).catch((function(){}))):null==(n=t[e.op])||n.call(t)})),this._operationsBeforeLoading.length=0)},e(n,[{key:"clip",get:function(){return this._clip},set:function(t){t!==this._clip&&(this._clip=t,this._syncPlayer())}},{key:"loop",get:function(){return this._loop},set:function(t){this._loop=t,this._player&&(this._player.loop=t)}},{key:"playOnAwake",get:function(){return this._playOnAwake},set:function(t){this._playOnAwake=t}},{key:"volume",get:function(){return this._volume},set:function(t){Number.isNaN(t)?s("illegal audio volume!"):(t=h(t,0,1),this._player?(this._player.volume=t,this._volume=this._player.volume):this._volume=t)}},{key:"currentTime",get:function(){return this._player?this._player.currentTime:this._cachedCurrentTime<0?0:this._cachedCurrentTime},set:function(t){var e;Number.isNaN(t)?s("illegal audio time!"):(t=h(t,0,this.duration),this._isLoaded||!this.clip?(this._cachedCurrentTime=t,null==(e=this._player)||e.seek(this._cachedCurrentTime).catch((function(){}))):this._operationsBeforeLoading.push({op:et.SEEK,params:[t]}))}},{key:"duration",get:function(){var t,e;return null!==(t=null==(e=this._clip)?void 0:e.getDuration())&&void 0!==t?t:this._player?this._player.duration:0}},{key:"state",get:function(){return this._player?this._player.state:k.INIT}},{key:"playing",get:function(){return this.state===n.AudioState.PLAYING}}],[{key:"maxAudioChannel",get:function(){return j.maxAudioChannel}}]),n}(I),$.AudioState=k,$.EventType=tt,Z=f((z=$).prototype,"_clip",[F],(function(){return null})),q=f(z.prototype,"_loop",[g],(function(){return!1})),J=f(z.prototype,"_playOnAwake",[g],(function(){return!0})),X=f(z.prototype,"_volume",[g],(function(){return 1})),r(z.prototype,"clip",[H],Object.getOwnPropertyDescriptor(z.prototype,"clip"),z.prototype),W=z))||W);t({AudioSource:it,AudioSourceComponent:it}),m(Y,"AudioClip",[{name:"PlayingState",newName:"AudioState",target:it,targetName:"AudioSource"}]),E(Y.prototype,"AudioClip.prototype",["state","play","pause","stop","playOneShot","setCurrentTime","setVolume","setLoop","getCurrentTime","getVolume","getLoop"].map((function(t){return{name:t,suggest:"please use AudioSource.prototype."+t+" instead"}}))),p.AudioSourceComponent=it,l(it,"cc.AudioSourceComponent")}}}));
