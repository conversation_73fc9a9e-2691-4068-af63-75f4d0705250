System.register(["./gc-object-D18ulfCO.js","./index-Y4La_nfG.js","./global-exports-CLZKKIY2.js"],(function(t){"use strict";var e,n,i,r,o,s,u,a,c,l,h,p,f,d,_,m,v,g,y,C,b,I,E,A,k,x,N;return{setters:[function(t){e=t.Z,n=t.a3,i=t.p,r=t.a,o=t.w,s=t.am,u=t.o,a=t.K,c=t._,l=t.R,h=t.x,p=t.g,f=t.Y,d=t.b,_=t.J,m=t.d,v=t.I,g=t.v,y=t.an,C=t.ao},function(t){b=t.a,I=t.c,E=t.p,A=t.s,k=t.t},function(t){x=t.c,N=t.l}],execute:function(){t({_:T,a:U,b:F,c:L,d:j,e:P,g:B,i:rt,j:R,l:Q,m:D,q:ut,r:st,s:z,t:at});var O=/(\.[^./?\\]*)(\?.*)?$/,S=/((.*)(\/|\\|\\\\))?(.*?\..*$)?/,w=/[^./]+\/\.\.\//;function R(){for(var t="",e=arguments.length,n=new Array(e),i=0;i<e;i++)n[i]=arguments[i];return n.forEach((function(e){t=(t+(""===t?"":"/")+e).replace(/(\/|\\\\)$/,"")})),t}function P(t){var e=O.exec(t);return e?e[1]:""}function D(t){if(t){var e=t.lastIndexOf(".");if(-1!==e)return t.substring(0,e)}return t}function F(t,e){var n=t.indexOf("?");n>0&&(t=t.substring(0,n));var i=/(\/|\\)([^/\\]+)$/g.exec(t.replace(/(\/|\\)$/,""));if(!i)return t;var r=i[2];return e&&t.substring(t.length-e.length).toLowerCase()===e.toLowerCase()?r.substring(0,r.length-e.length):r}function j(t){var e=S.exec(t);return e?e[2]:""}function U(t,e){e=e||"";var n=t.indexOf("?"),i="";return n>0&&(i=t.substring(n),t=t.substring(0,n)),(n=t.lastIndexOf("."))<0?t+e+i:t.substring(0,n)+e+i}function L(t,e,n){if(0===e.indexOf("."))return U(t,e);var i=t.indexOf("?"),r="",o=n?P(t):"";return i>0&&(r=t.substring(i),t=t.substring(0,i)),i=(i=t.lastIndexOf("/"))<=0?0:i+1,t.substring(0,i)+e+o+r}function T(t){var e=t=String(t);do{e=t,t=t.replace(w,"")}while(e.length!==t.length);return t}function z(t){return t.replace(/[/\\]$/,"")}function B(){return e.os===n.WINDOWS?"\\":"/"}t("p",Object.freeze({__proto__:null,_normalize:T,basename:F,changeBasename:L,changeExtname:U,dirname:j,extname:P,getSeperator:B,join:R,mainFileName:D,stripSep:z}));var M=t("h",function(){function t(t){this._map=null,this._count=0,t?(this._map=t,this._count=Object.keys(t).length):(this._map=i(!0),this._count=0)}var e=t.prototype;return e.add=function(t,e){return t in this._map||this._count++,this._map[t]=e},e.get=function(t){return this._map[t]},e.has=function(t){return t in this._map},e.remove=function(t){var e=this._map[t];return t in this._map&&(delete this._map[t],this._count--),e},e.clear=function(){0!==this._count&&(this._map=i(!0),this._count=0)},e.forEach=function(t){for(var e in this._map)t(this._map[e],e)},e.find=function(t){for(var e in this._map)if(t(this._map[e],e))return this._map[e];return null},e.destroy=function(){this._map=null},r(t,[{key:"map",get:function(){return this._map}},{key:"count",get:function(){return this._count}}]),t}()),q=t("P",function(){function t(e,n){this.id=t._pipelineId++,this.name="",this.pipes=[],this.name=e;for(var i=0,r=n.length;i<r;i++)this.pipes.push(n[i])}var e=t.prototype;return e.insert=function(t,e){return e>this.pipes.length?(o(4921),this):(this.pipes.splice(e,0,t),this)},e.append=function(t){return this.pipes.push(t),this},e.remove=function(t){return this.pipes.splice(t,1),this},e.sync=function(t){var e=this.pipes;if(0===e.length)return null;t.isFinished=!1;for(var n=0,i=e.length;n<i;){var r=(0,e[n])(t);if(r)return t.isFinished=!0,r;++n!==i&&(t.input=t.output,t.output=null)}return t.isFinished=!0,t.output},e.async=function(t){0!==this.pipes.length&&(t.isFinished=!1,this._flow(0,t))},e._flow=function(t,e){var n=this;(0,this.pipes[t])(e,(function(i){i?(e.isFinished=!0,e.dispatch("complete",i)):++t<n.pipes.length?(e.input=e.output,e.output=null,n._flow(t,e)):(e.isFinished=!0,e.dispatch("complete",i,e.output))}))},t}());q._pipelineId=0,t("f",new M),t("o",new M),t("n",new M);var H,$,J=t("k",new M),V=(t("w",new q("normal load",[])),t("v",new q("fetch",[])),t("x",new q("transform url",[])));t("F",null),t("z",new Map),t("R",H),function(t){t.UUID="uuid",t.PATH="path",t.DIR="dir",t.URL="url",t.SCENE="scene"}(H||t("R",H={})),t("y",{default:{priority:0},preload:{maxConcurrency:6,maxRequestsPerFrame:2,priority:-1},scene:{maxConcurrency:20,maxRequestsPerFrame:20,priority:1},bundle:{maxConcurrency:20,maxRequestsPerFrame:20,priority:2},remote:{maxRetryCount:4}}),t("B",$),function(t){t.INTERNAL="internal",t.RESOURCES="resources",t.MAIN="main",t.START_SCENE="start-scene"}($||t("B",$={}));var G=t("u",function(){function t(e){this.id=t._taskId++,this.onComplete=null,this.onProgress=null,this.onError=null,this.source=null,this.output=null,this.input=null,this.progress=null,this.options=null,this.isFinished=!0,this.set(e)}t.create=function(e){var n;return 0!==t._deadPool.length?(n=t._deadPool.pop()).set(e):n=new t(e),n};var e=t.prototype;return e.set=function(t){void 0===t&&(t=Object.create(null)),this.onComplete=t.onComplete||null,this.onProgress=t.onProgress||null,this.onError=t.onError||null,this.source=this.input=t.input,this.output=null,this.progress=t.progress,this.options=t.options||Object.create(null)},e.dispatch=function(t,e,n,i,r){switch(t){case"complete":this.onComplete&&this.onComplete(e,n);break;case"progress":this.onProgress&&this.onProgress(e,n,i,r);break;case"error":this.onError&&this.onError(e,n,i,r);break;default:var o="on"+t[0].toUpperCase()+t.substring(1);"function"==typeof this[o]&&this[o](e,n,i,r)}},e.recycle=function(){t._deadPool.length!==t.MAX_DEAD_NUM&&(this.onComplete=null,this.onProgress=null,this.onError=null,this.source=this.output=this.input=null,this.progress=null,this.options=null,t._deadPool.push(this))},r(t,[{key:"isFinish",get:function(){return this.isFinished},set:function(t){this.isFinished=t}}]),t}());G.MAX_DEAD_NUM=500,G._taskId=0,G._deadPool=[];var W="@",X="0123456789abcdef".split(""),K=["","","",""],Y=K.concat(K,"-",K,"-",K,"-",K,"-",K,K,K),Z=Y.map((function(t,e){return"-"===t?NaN:e})).filter(Number.isFinite);function Q(t){var e=t.split(W)[0];if(22!==e.length)return t;Y[0]=t[0],Y[1]=t[1];for(var n=2,i=2;n<22;n+=2){var r=s[t.charCodeAt(n)],o=s[t.charCodeAt(n+1)];Y[Z[i++]]=X[r>>2],Y[Z[i++]]=X[(3&r)<<2|o>>4],Y[Z[i++]]=X[15&o]}return t.replace(e,Y.join(""))}var tt,et,nt,it=/.*[/\\][0-9a-fA-F]{2}[/\\]([0-9a-fA-F-@]{8,}).*/;function rt(t){var e=it.exec(t);return e?e[1]:""}function ot(t,e){(e=e||Object.create(null)).__isNative__=e.isNative,e.nativeExt&&(e.ext=e.nativeExt);var n=J.find((function(e){return!!e.getAssetInfo(t)}));return n&&(e.bundle=n.name),at(t,e)}function st(t){return!!t&&(t instanceof x.SceneAsset||t instanceof x.Scene)}function ut(t){return t&&(46===t.charCodeAt(0)&&47===t.charCodeAt(1)?t=t.slice(2):47===t.charCodeAt(0)&&(t=t.slice(1))),t}function at(t,e){var n=G.create({input:t,options:e}),i=[];try{for(var r,o=V.sync(n),s=u(o);!(r=s()).done;){var c=r.value,l=c.url;c.recycle(),i.push(l)}}catch(t){for(var h,p=u(n.output);!(h=p()).done;)h.value.recycle();a(t.message,t.stack)}return n.recycle(),i.length>1?i:i[0]}t("D",Object.freeze({__proto__:null,decodeUuid:Q,getUrlWithUuid:ot,getUuidFromURL:rt,isScene:st,normalize:ut,transform:at}));var ct,lt,ht,pt=A,ft=E,dt=t("A",I("cc.Asset")((et=function(t){function e(e){var n;return(n=t.call(this,e)||this).loaded=!0,n._native=nt&&nt(),n._nativeUrl="",n._file=null,n._ref=0,Object.defineProperty(l(n),"_uuid",{value:"",writable:!0}),n}c(e,t),e.deserialize=function(t){return x.deserialize(t)};var n=e.prototype;return n.toString=function(){return this.nativeUrl},n.serialize=function(){},n._setRawAsset=function(t,e){void 0===e&&(e=!0),this._native=!1!==e?t||"":"/"+t},n.addRef=function(){return this._ref++,this},n.decRef=function(t){return void 0===t&&(t=!0),this._ref>0&&this._ref--,t&&x.assetManager.getReleaseManager().tryRelease(this),this},n.onLoaded=function(){},n.initDefault=function(t){t&&(this._uuid=t),this.isDefault=!0},n.validate=function(){return!0},n.destroy=function(){return h(p(12101,this._uuid)),t.prototype.destroy.call(this)},r(e,[{key:"nativeUrl",get:function(){if(!this._nativeUrl){if(!this._native)return"";var t=this._native;if(47===t.charCodeAt(0))return t.slice(1);46===t.charCodeAt(0)?this._nativeUrl=ot(this._uuid,{nativeExt:t,isNative:!0}):this._nativeUrl=ot(this._uuid,{__nativeName__:t,nativeExt:P(t),isNative:!0})}return this._nativeUrl}},{key:"uuid",get:function(){return this._uuid}},{key:"_nativeAsset",get:function(){return this._file},set:function(t){this._file=t}},{key:"nativeAsset",get:function(){return this._file}},{key:"_nativeDep",get:function(){if(this._native)return{__isNative__:!0,uuid:this._uuid,ext:this._native}}},{key:"refCount",get:function(){return this._ref}}]),e}(f(_)),nt=b(et.prototype,"_native",[pt],(function(){return""})),d(et.prototype,"_nativeAsset",[ft],Object.getOwnPropertyDescriptor(et.prototype,"_nativeAsset"),et.prototype),tt=et))||tt);dt.prototype.createNode=null,x.Asset=dt;var _t=t("S",I("cc.Script")(ct=function(t){function e(e){return t.call(this,e)||this}return c(e,t),e}(dt))||ct);x._Script=_t;var mt=t("J",I("cc.JavaScript")(lt=function(t){function e(e){return t.call(this,e)||this}return c(e,t),e}(_t))||lt);x._JavaScript=mt;var vt,gt,yt,Ct,bt,It,Et,At=t("T",I("cc.TypeScript")(ht=function(t){function e(e){return t.call(this,e)||this}return c(e,t),e}(_t))||ht);x._TypeScript=At;var kt,xt,Nt,Ot,St,wt,Rt,Pt,Dt=t("E",I("cc.ClickEvent")((gt=function(){function t(){this.target=yt&&yt(),this.component=Ct&&Ct(),this._componentId=bt&&bt(),this.handler=It&&It(),this.customEventData=Et&&Et()}t.emitEvents=function(e){for(var n=arguments.length,i=new Array(n>1?n-1:0),r=1;r<n;r++)i[r-1]=arguments[r];for(var o=0,s=e.length;o<s;o++){var u=e[o];u instanceof t&&u.emit(i)}};var e=t.prototype;return e.emit=function(t){var e=this.target;if(N.isValid(e)){this._genCompIdIfNeeded();var n=N.js.getClassById(this._componentId),i=e.getComponent(n);if(N.isValid(i)){var r=i[this.handler];"function"==typeof r&&(null!=this.customEventData&&""!==this.customEventData&&(t=t.slice()).push(this.customEventData),r.apply(i,t))}}},e._compName2Id=function(t){var e=N.js.getClassByName(t);return N.js.getClassId(e)},e._compId2Name=function(t){var e=N.js.getClassById(t);return N.js.getClassName(e)},e._genCompIdIfNeeded=function(){this._componentId||(this._componentName=this.component,this.component="")},r(t,[{key:"_componentName",get:function(){return this._genCompIdIfNeeded(),this._compId2Name(this._componentId)},set:function(t){this._componentId=this._compName2Id(t)}}]),t}(),yt=b(gt.prototype,"target",[A],(function(){return null})),Ct=b(gt.prototype,"component",[A],(function(){return""})),bt=b(gt.prototype,"_componentId",[A],(function(){return""})),It=b(gt.prototype,"handler",[A],(function(){return""})),Et=b(gt.prototype,"customEventData",[A],(function(){return""})),vt=gt))||vt),Ft=new C("Comp"),jt=m.IsOnLoadCalled,Ut=t("C",(kt=I("cc.Component"),xt=k(_t),kt((Pt=function(t){function e(){var e;return(e=t.call(this)||this).node=St&&St(),e._enabled=wt&&wt(),e.__prefab=Rt&&Rt(),e._sceneGetter=null,e._id=Ft.getNewId(),e}c(e,t);var n=e.prototype;return n._getRenderScene=function(){return this._sceneGetter?this._sceneGetter():this.node.scene.renderScene},n.addComponent=function(t){return this.node.addComponent(t)},n.getComponent=function(t){return this.node.getComponent(t)},n.getComponents=function(t){return this.node.getComponents(t)},n.getComponentInChildren=function(t){return this.node.getComponentInChildren(t)},n.getComponentsInChildren=function(t){return this.node.getComponentsInChildren(t)},n.destroy=function(){return!!t.prototype.destroy.call(this)&&(this._enabled&&this.node.activeInHierarchy&&N.director._compScheduler.disableComp(this),!0)},n._onPreDestroy=function(){this.unscheduleAllCallbacks(),N.director._nodeActivator.destroyComp(this),this.node._removeComponent(this)},n._instantiate=function(t){return t||(t=N.instantiate._clone(this,this)),t&&(t.node=null),t},n.schedule=function(t,e,n,i){void 0===e&&(e=0),void 0===n&&(n=N.macro.REPEAT_FOREVER),void 0===i&&(i=0),v(Boolean(t),1619),v((e=e||0)>=0,1620),n=Number.isNaN(n)?N.macro.REPEAT_FOREVER:n,i=i||0;var r=N.director.getScheduler(),o=r.isTargetPaused(this);r.schedule(t,this,e,n,i,o)},n.scheduleOnce=function(t,e){void 0===e&&(e=0),this.schedule(t,0,0,e)},n.unschedule=function(t){t&&N.director.getScheduler().unschedule(t,this)},n.unscheduleAllCallbacks=function(){N.director.getScheduler().unscheduleAllForTarget(this)},r(e,[{key:"name",get:function(){if(this._name)return this._name;var t=y(this),e=t.lastIndexOf(".");return e>=0&&(t=t.slice(e+1)),this.node?this.node.name+"<"+t+">":t},set:function(t){this._name=t}},{key:"uuid",get:function(){return this._id}},{key:"__scriptAsset",get:function(){return null}},{key:"enabled",get:function(){return this._enabled},set:function(t){if(this._enabled!==t&&(this._enabled=t,this.node.activeInHierarchy)){var e=N.director._compScheduler;t?e.enableComp(this):e.disableComp(this)}}},{key:"enabledInHierarchy",get:function(){return this._enabled&&this.node&&this.node.activeInHierarchy}},{key:"_isOnLoadCalled",get:function(){return this._objFlags&jt}},{key:"internalUpdate",get:function(){return this.update}},{key:"internalLateUpdate",get:function(){return this.lateUpdate}},{key:"internalPreload",get:function(){return this.__preload}},{key:"internalOnLoad",get:function(){return this.onLoad}},{key:"internalStart",get:function(){return this.start}},{key:"internalOnEnable",get:function(){return this.onEnable}},{key:"internalOnDisable",get:function(){return this.onDisable}},{key:"internalOnDestroy",get:function(){return this.onDestroy}}]),e}(_),Pt.EventHandler=Dt,Pt._executionOrder=0,Pt._requireComponent=null,Pt.system=null,d((Ot=Pt).prototype,"__scriptAsset",[xt],Object.getOwnPropertyDescriptor(Ot.prototype,"__scriptAsset"),Ot.prototype),St=b(Ot.prototype,"node",[A],(function(){return null})),wt=b(Ot.prototype,"_enabled",[A],(function(){return!0})),Rt=b(Ot.prototype,"__prefab",[A],(function(){return null})),Nt=Ot))||Nt));g(Ut,"_registerEditorProps",(function(t,e){var n=e.requireComponent;n&&(Array.isArray(n)&&(n=n.filter(Boolean)),t._requireComponent=n);var i=e.executionOrder;i&&"number"==typeof i&&(t._executionOrder=i)})),N.Component=Ut}}}));
