{"platform": "TAOBAO_MINIGAME", "engine": "E:\\editor_3d\\v3.8.6\\resources\\3d\\engine", "out": "E:\\editor_3d\\v3.8.6\\resources\\3d\\engine\\bin\\.cache\\editor-cache\\taobao-mini-game\\cocos", "moduleFormat": "system", "compress": true, "split": true, "mode": "BUILD", "nativeCodeBundleMode": "asmjs", "flags": {"SERVER_MODE": false, "DEBUG": false, "NATIVE_CODE_BUNDLE_MODE": 0}, "features": ["base", "gfx-webgl", "gfx-webgl2", "gfx-empty", "3d", "animation", "skeletal-animation", "2d", "rich-text", "mask", "graphics", "ui-skew", "ui", "affine-transform", "particle", "particle-2d", "physics-framework", "physics-cannon", "physics-physx", "physics-ammo", "physics-builtin", "physics-2d-framework", "physics-2d-box2d-jsb", "physics-2d-box2d", "physics-2d-builtin", "physics-2d-box2d-wasm", "intersection-2d", "primitive", "profiler", "occlusion-query", "geometry-renderer", "debug-renderer", "audio", "video", "xr", "light-probe", "terrain", "webview", "tween", "tiled-map", "spine-3.8", "dragon-bones", "marionette", "procedural-animation", "custom-pipeline", "custom-pipeline-builtin-scripts", "custom-pipeline-post-process", "legacy-pipeline", "websocket", "websocket-server", "meshopt"], "inlineEnum": false, "preserveType": false, "loose": true}