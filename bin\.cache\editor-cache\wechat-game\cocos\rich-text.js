System.register(["./gc-object-CKHc4SnS.js","./index-C5lmLqDW.js","./global-exports-CR3GRnjt.js","./sprite-frame-C6JiNTOk.js","./sprite-BN1AMlyy.js","./label-ChzmWuX_.js","./prefab-DH0xadMc.js","./scene-7MDSMR3j.js","./pipeline-state-manager-Cdpe3is6.js","./node-event-DTNosVQv.js","./component-BaGvu7EF.js","./sprite-renderer-zme-rDJ_.js","./ui-renderer-DuhVjkfF.js","./factory-D9_8ZCqM.js","./device-manager-BvjvoelW.js","./buffer-barrier-CuX_5NUR.js","./deprecated-T2yM9nsJ.js","./director-DIlqD2Nd.js","./debug-view-CKetkq9d.js","./deprecated-Ca3AjUwj.js","./create-mesh-hkbGggH3.js","./rendering-sub-mesh-CowWLfXC.js","./mesh-C8knhDLk.js","./wasm-minigame-DoCiKH-Y.js","./zlib.min-CyXMsivM.js","./touch-DB0AR-Sc.js","./camera-component-Df61RNZm.js","./deprecated-C8l6Kwy8.js","./model-renderer-BcRDUYby.js","./renderer-9hfAnqUF.js"],(function(t){"use strict";var e,i,n,o,s,r,a,l,h,u,c,_,f,d,g,p,m,y,v,x,C,b,T,S,A,L,I,O,H,W,z,E,R;return{setters:[function(t){e=t.a3,i=t._,n=t.w,o=t.a,s=t.b,r=t.d},function(t){a=t.V,l=t.c,h=t.t,u=t.C,c=t.a,_=t.i,f=t.s},function(t){d=t.c},null,function(t){g=t.S,p=t.b},function(t){m=t.o,y=t.L,t.T,v=t.t,x=t.u,C=t.f,b=t.B,T=t.b,S=t.d,A=t.i,L=t.H,I=t.n,O=t.V,H=t.j},null,function(t){W=t.N},null,function(t){z=t.N},function(t){E=t.C},null,function(t){R=t.c},null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){var j,k,F,D,U,P,w,X,N,M,V,B,G,Y,q,J,K,Q,Z,$,tt,et,it,nt,ot,st=new m,rt="RICHTEXT_CHILD",at="RICHTEXT_Image_CHILD",lt=new a,ht=new a,ut=new e((function(t){if(!d.isValid(t.node))return!1;var e=t.node.getComponent(y);return e&&(e.outlineWidth=0),!0}),20),ct=new e((function(t){return d.isValid(t.node)}),10);function _t(t){return{node:new W(t),comp:null,lineCount:0,styleIndex:0,imageOffset:"",clickParam:"",clickHandler:"",type:t}}function ft(t,e){var i;t===rt?i=ut._get():t===at&&(i=ct._get());var n=(i=i||_t(t)).node;return n||(n=new W(t)),n.hideFlags|=r.DontSave|r.HideInHierarchy,n.active=!0,t===at?(i.comp=n.getComponent(g)||n.addComponent(g),i.comp.spriteFrame=e,i.comp.type=g.Type.SLICED,i.comp.sizeMode=g.SizeMode.CUSTOM):(i.comp=n.getComponent(y)||n.addComponent(y),i.comp.string=e,i.comp.horizontalAlign=L.LEFT,i.comp.verticalAlign=O.TOP,i.comp.underlineHeight=2),n.setPosition(0,0,0),n._getUITransformComp().setAnchorPoint(.5,.5),i.node=n,i.lineCount=0,i.styleIndex=0,i.imageOffset="",i.clickParam="",i.clickHandler="",i}var dt=t("RichText",(j=l("cc.RichText"),k=_(110),F=h(L),D=h(O),U=h(u),P=h(I),w=h(H),X=h(p),j(N=k((ot=function(t){function e(){var e;return(e=t.call(this)||this)._lineHeight=V&&V(),e._string=B&&B(),e._horizontalAlign=G&&G(),e._verticalAlign=Y&&Y(),e._fontSize=q&&q(),e._fontColor=J&&J(),e._maxWidth=K&&K(),e._fontFamily=Q&&Q(),e._font=Z&&Z(),e._isSystemFontUsed=$&&$(),e._userDefinedFont=tt&&tt(),e._cacheMode=et&&et(),e._imageAtlas=it&&it(),e._handleTouchEvent=nt&&nt(),e._textArray=[],e._segments=[],e._labelSegmentsCache=[],e._linesWidth=[],e._lineCount=1,e._labelWidth=0,e._labelHeight=0,e._layoutDirty=!0,e._lineOffsetX=0,e._labelChildrenNum=0,e._updateRichTextStatus=e._updateRichText,e}i(e,t);var s=e.prototype;return s.onLoad=function(){this.node.on(z.LAYER_CHANGED,this._applyLayer,this),this.node.on(z.ANCHOR_CHANGED,this._updateRichTextPosition,this)},s.onEnable=function(){this.handleTouchEvent&&this._addEventListeners(),this._updateRichText(),this._activateChildren(!0)},s.onDisable=function(){this.handleTouchEvent&&this._removeEventListeners(),this._activateChildren(!1)},s.onRestore=function(){},s.onDestroy=function(){this._segments.forEach((function(t){t.node.removeFromParent(),t.type===rt?ut.put(t):t.type===at&&ct.put(t)})),this.node.off(z.ANCHOR_CHANGED,this._updateRichTextPosition,this),this.node.off(z.LAYER_CHANGED,this._applyLayer,this)},s._addEventListeners=function(){this.node.on(z.TOUCH_END,this._onTouchEnded,this)},s._removeEventListeners=function(){this.node.off(z.TOUCH_END,this._onTouchEnded,this)},s._updateLabelSegmentTextAttributes=function(){var t=this;this._segments.forEach((function(e){t._applyTextAttribute(e)}))},s._createFontLabel=function(t){return ft(rt,t)},s._createImage=function(t){return ft(at,t)},s._onTTFLoaded=function(){this._font,this._layoutDirty=!0,this._updateRichText()},s.splitLongStringApproximatelyIn2048=function(t,e){var i=[];if(t.length*this.fontSize<=1638.4)return i.push(t),i;if(this._calculateSize(lt,e,t),lt.x<2048)i.push(t);else for(var n=t.split("\n"),o=0;o<n.length;o++)if(this._calculateSize(lt,e,n[o]),lt.x<2048)i.push(n[o]);else{var s=this.splitLongStringOver2048(n[o],e);i.push.apply(i,s)}return i},s.splitLongStringOver2048=function(t,e){var i=[],n=t,o=0,s=n.length/2,r=n.substring(o,s),a=n.substring(s),l=this._calculateSize(lt,e,r),h=this._calculateSize(ht,e,a),u=this._maxWidth;0===this._maxWidth&&(u=2047.9);for(var c=1*u;l.x>c;){if((s/=2)<1){s*=2;break}r=r.substring(o,s),a=n.substring(s),this._calculateSize(l,e,r)}for(var _=1e3,f=1;_&&o<t.length;){for(;_&&l.x<c;){var d=v(a);d&&d.length>0&&(f=d[0].length),s+=f,r=n.substring(o,s),a=n.substring(s),this._calculateSize(l,e,r),_--}for(;_&&r.length>=2&&l.x>c;)s-=f,r=n.substring(o,s),this._calculateSize(l,e,r),f=1,_--;if(r.length>=2){var g=x(r);g&&g.length>0&&r!==g[0]&&(s-=g[0].length,r=n.substring(o,s))}if(i.push(r),o=s,s+=r.length,r=n.substring(o,s),a=n.substring(s),this._calculateSize(h,e,a),this._calculateSize(l,e,r),_--,h.x<2048&&l.x<c){i.push(r),o=t.length,s=t.length,r=a,""!==a&&i.push(r);break}}return i},s._measureText=function(t,e){var i=this,n=function(e){return i._calculateSize(lt,t,e).x};return e?n(e):n},s._calculateSize=function(t,e,i){var n;0===this._labelSegmentsCache.length?(n=this._createFontLabel(i),this._labelSegmentsCache.push(n)):(n=this._labelSegmentsCache[0]).node.getComponent(y).string=i,n.styleIndex=e,this._applyTextAttribute(n);var o=n.node._getUITransformComp().contentSize;return a.set(t,o.x,o.y),t},s._onTouchEnded=function(t){var e=this,i=this.node.getComponents(E);this._segments.forEach((function(n){var o=n.clickHandler,s=n.clickParam;o&&e._containsTouchLocation(n,t.touch.getUILocation())&&(i.forEach((function(e){var i=e[o];e.enabledInHierarchy&&i&&i.call(e,t,s)})),t.propagationStopped=!0)}))},s._containsTouchLocation=function(t,e){var i=t.node.getComponent(R);return!!i&&i.getBoundingBoxToWorld().contains(e)},s._resetState=function(){for(var t=this.node.children,e=t.length-1;e>=0;e--){var i=t[e];if(i.name===rt||i.name===at){i.parent=null;var n=_t(i.name);n.node=i,i.name===rt?(n.comp=i.getComponent(y),ut.put(n)):(n.comp=i.getComponent(g),ct.put(n)),this._labelChildrenNum--}}this._segments.length=0,this._labelSegmentsCache.length=0,this._linesWidth.length=0,this._lineOffsetX=0,this._lineCount=1,this._labelWidth=0,this._labelHeight=0,this._layoutDirty=!0},s._activateChildren=function(t){for(var e=this.node.children.length-1;e>=0;e--){var i=this.node.children[e];i.name!==rt&&i.name!==at||(i.active=t)}},s._addLabelSegment=function(t,e){var i;if(0===this._labelSegmentsCache.length)i=this._createFontLabel(t);else{var n=(i=this._labelSegmentsCache.pop()).node.getComponent(y);n&&(n.string=t)}var o=i.comp;return o.verticalAlign!==this._verticalAlign&&(o.verticalAlign=this._verticalAlign),i.styleIndex=e,i.lineCount=this._lineCount,i.node._getUITransformComp().setAnchorPoint(0,0),i.node.layer=this.node.layer,this.node.insertChild(i.node,this._labelChildrenNum++),this._applyTextAttribute(i),this._segments.push(i),i},s._updateRichTextWithMaxWidth=function(t,e,i){var n=e;if(this._lineOffsetX>0&&n+this._lineOffsetX>this._maxWidth)for(var o=0;this._lineOffsetX<=this._maxWidth;){var s=this._getFirstWordLen(t,o,t.length),r=t.substr(o,s),a=this._measureText(i,r);if(!(this._lineOffsetX+a<=this._maxWidth)){if(o>0){var l=t.substr(0,o);this._addLabelSegment(l,i),t=t.substr(o,t.length),n=this._measureText(i,t)}this._updateLineInfo();break}this._lineOffsetX+=a,o+=s}if(n>this._maxWidth)for(var h=C(t,n,this._maxWidth,this._measureText(i)),u=0;u<h.length;++u){var c=h[u],_=this._addLabelSegment(c,i).node._getUITransformComp().contentSize;this._lineOffsetX+=_.width,h.length>1&&u<h.length-1&&this._updateLineInfo()}else this._lineOffsetX+=n,this._addLabelSegment(t,i)},s._isLastComponentCR=function(t){return t.length-1===t.lastIndexOf("\n")},s._updateLineInfo=function(){this._linesWidth.push(this._lineOffsetX),this._lineOffsetX=0,this._lineCount++},s._needsUpdateTextLayout=function(t){if(this._layoutDirty||!this._textArray||!t)return!0;if(this._textArray.length!==t.length)return!0;for(var e=0;e<this._textArray.length;e++){var i=this._textArray[e],n=t[e];if(i.text!==n.text)return!0;var o=i.style,s=n.style;if(o){if(s){if(!!s.outline!=!!o.outline)return!0;if(o.size!==s.size||o.italic!==s.italic||o.isImage!==s.isImage)return!0;if(o.src!==s.src||o.imageAlign!==s.imageAlign||o.imageHeight!==s.imageHeight||o.imageWidth!==s.imageWidth||o.imageOffset!==s.imageOffset)return!0}else if(o.size||o.italic||o.isImage||o.outline)return!0}else if(s&&(s.size||s.italic||s.isImage||s.outline))return!0}return!1},s._addRichTextImageElement=function(t){if(t.style){var e=t.style,i=e.src,o=this._imageAtlas&&i&&this._imageAtlas.getSpriteFrame(i);if(o){var s=this._createImage(o);switch(s.comp,e.imageAlign){case"top":s.node._getUITransformComp().setAnchorPoint(0,1);break;case"center":s.node._getUITransformComp().setAnchorPoint(0,.5);break;default:s.node._getUITransformComp().setAnchorPoint(0,0)}e.imageOffset&&(s.imageOffset=e.imageOffset),s.node.layer=this.node.layer,this.node.insertChild(s.node,this._labelChildrenNum++),this._segments.push(s);var r=o.rect.clone(),a=1,l=r.width,h=r.height,u=e.imageWidth||0,c=e.imageHeight||0;c>0?(l*=a=c/h,h*=a):(l*=a=this._lineHeight/h,h*=a),u>0&&(l=u),this._maxWidth>0?(this._lineOffsetX+l>this._maxWidth&&this._updateLineInfo(),this._lineOffsetX+=l):(this._lineOffsetX+=l,this._lineOffsetX>this._labelWidth&&(this._labelWidth=this._lineOffsetX)),s.node._getUITransformComp().setContentSize(l,h),s.lineCount=this._lineCount,s.clickHandler="",s.clickParam="";var _=e.event;_&&(s.clickHandler=_.click,s.clickParam=_.param)}else n(4400)}},s._updateTextDefaultColor=function(){for(var t=0;t<this._segments.length;++t){var e,i,n=this._segments[t],o=n.node.getComponent(y);o&&(null!=(e=this._textArray[n.styleIndex])&&null!=(i=e.style)&&i.color||(o.color=this._fontColor))}},s._updateRichText=function(){if(this.enabledInHierarchy){var t=st.parse(this._string);if(!this._needsUpdateTextLayout(t))return this._textArray=t.slice(),void this._updateLabelSegmentTextAttributes();this._textArray=t.slice(),this._resetState();for(var e,i=!1,n=0;n<this._textArray.length;++n){var o=this._textArray[n],s=o.text;if(void 0!==s){if(""===s){if(o.style&&o.style.isNewLine){this._updateLineInfo();continue}if(o.style&&o.style.isImage&&this._imageAtlas){this._addRichTextImageElement(o);continue}}for(var r=(s=this.splitLongStringApproximatelyIn2048(s,n).join("\n")).split("\n"),a=0;a<r.length;++a){var l=r[a];if(""!==l)if(i=!1,this._maxWidth>0){var h=this._measureText(n,l);this._updateRichTextWithMaxWidth(l,h,n),r.length>1&&a<r.length-1&&this._updateLineInfo()}else e=this._addLabelSegment(l,n),this._lineOffsetX+=e.node._getUITransformComp().width,this._lineOffsetX>this._labelWidth&&(this._labelWidth=this._lineOffsetX),r.length>1&&a<r.length-1&&this._updateLineInfo();else{if(this._isLastComponentCR(s)&&a===r.length-1)continue;this._updateLineInfo(),i=!0}}}}i||this._linesWidth.push(this._lineOffsetX),this._maxWidth>0&&(this._labelWidth=this._maxWidth),this._labelHeight=(this._lineCount+b)*this._lineHeight,this.node._getUITransformComp().setContentSize(this._labelWidth,this._labelHeight),this._updateRichTextPosition(),this._layoutDirty=!1}},s._getFirstWordLen=function(t,e,i){var n=T(t,e);if(S(n)||A(n))return 1;for(var o=1,s=e+1;s<i&&(n=T(t,s),!A(n)&&!S(n));++s)o++;return o},s._updateRichTextPosition=function(){for(var t=0,e=1,i=this._lineCount,n=this.node._getUITransformComp(),o=n.anchorX,s=n.anchorY,r=0;r<this._segments.length;++r){var a=this._segments[r],l=a.lineCount;l>e&&(t=0,e=l);var h=this._labelWidth*(.5*this._horizontalAlign-o);switch(this._horizontalAlign){case L.LEFT:break;case L.CENTER:h-=this._linesWidth[l-1]/2;break;case L.RIGHT:h-=this._linesWidth[l-1]}var u=a.node.position;if(a.node.setPosition(t+h,this._lineHeight*(i-l)-this._labelHeight*s,u.z),l===e&&(t+=a.node._getUITransformComp().width),a.node.getComponent(g)){var c=a.node.position.clone(),_=this._lineHeight,f=this._lineHeight*(1+b);switch(a.node._getUITransformComp().anchorY){case 1:c.y+=_+(f-_)/2;break;case.5:c.y+=f/2;break;default:c.y+=(f-_)/2}if(a.imageOffset){var d=a.imageOffset.split(",");if(1===d.length&&d[0]){var p=parseFloat(d[0]);Number.isInteger(p)&&(c.y+=p)}else if(2===d.length){var m=parseFloat(d[0]),v=parseFloat(d[1]);Number.isInteger(m)&&(c.x+=m),Number.isInteger(v)&&(c.y+=v)}}a.node.position=c}var x=a.node.getComponent(y);if(x&&x.enableOutline){var C=a.node.position.clone();C.y-=x.outlineWidth,a.node.position=C}}},s._convertLiteralColorValue=function(t){var e=t.toUpperCase();return u[e]?u[e]:(new u).fromHEX(t)},s._applyTextAttribute=function(t){var e=t.node.getComponent(y);if(e){this._resetLabelState(e);var i,n=t.styleIndex;if(this._textArray[n]&&(i=this._textArray[n].style),i){if(i.color?e.color=this._convertLiteralColorValue(i.color):e.color=this._fontColor,e.isBold=!!i.bold,e.isItalic=!!i.italic,e.isUnderline=!!i.underline,i.outline){var o=t.node.getComponent(y);o||(o=t.node.addComponent(y)),o.enableOutline=!0,o.outlineColor=this._convertLiteralColorValue(i.outline.color),o.outlineWidth=i.outline.width}e.fontSize=i.size||this._fontSize,t.clickHandler="",t.clickParam="";var s=i.event;s&&(t.clickHandler=s.click||"",t.clickParam=s.param||"")}e.cacheMode=this._cacheMode,this._font instanceof I&&!this._isSystemFontUsed?e.font=this._font:e.fontFamily=this._fontFamily,e.useSystemFont=this._isSystemFontUsed,e.lineHeight=this._lineHeight,e.updateRenderData(!0)}},s._applyLayer=function(){var t=this;this._segments.forEach((function(e){e.node.layer=t.node.layer}))},s._resetLabelState=function(t){t.fontSize=this._fontSize,t.color=this._fontColor,t.isBold=!1,t.isItalic=!1,t.isUnderline=!1},o(e,[{key:"string",get:function(){return this._string},set:function(t){this._string!==t&&(this._string=t,this._updateRichTextStatus())}},{key:"horizontalAlign",get:function(){return this._horizontalAlign},set:function(t){this.horizontalAlign!==t&&(this._horizontalAlign=t,this._layoutDirty=!0,this._updateRichTextStatus())}},{key:"verticalAlign",get:function(){return this._verticalAlign},set:function(t){this._verticalAlign!==t&&(this._verticalAlign=t,this._layoutDirty=!0,this._updateRichTextStatus())}},{key:"fontSize",get:function(){return this._fontSize},set:function(t){this._fontSize!==t&&(this._fontSize=t,this._layoutDirty=!0,this._updateRichTextStatus())}},{key:"fontColor",get:function(){return this._fontColor},set:function(t){this._fontColor!==t&&(this._fontColor=t,this._updateTextDefaultColor())}},{key:"fontFamily",get:function(){return this._fontFamily},set:function(t){this._fontFamily!==t&&(this._fontFamily=t,this._layoutDirty=!0,this._updateRichTextStatus())}},{key:"font",get:function(){return this._font},set:function(t){this._font!==t&&(this._font=t,this._layoutDirty=!0,this._font?(this.useSystemFont=!1,this._onTTFLoaded()):this.useSystemFont=!0,this._updateRichTextStatus())}},{key:"useSystemFont",get:function(){return this._isSystemFontUsed},set:function(t){this._isSystemFontUsed!==t&&(this._isSystemFontUsed=t,this._layoutDirty=!0,this._updateRichTextStatus())}},{key:"cacheMode",get:function(){return this._cacheMode},set:function(t){this._cacheMode!==t&&(this._cacheMode=t,this._updateRichTextStatus())}},{key:"maxWidth",get:function(){return this._maxWidth},set:function(t){this._maxWidth!==t&&(this._maxWidth=t,this._layoutDirty=!0,this._updateRichTextStatus())}},{key:"lineHeight",get:function(){return this._lineHeight},set:function(t){this._lineHeight!==t&&(this._lineHeight=t,this._layoutDirty=!0,this._updateRichTextStatus())}},{key:"imageAtlas",get:function(){return this._imageAtlas},set:function(t){this._imageAtlas!==t&&(this._imageAtlas=t,this._layoutDirty=!0,this._updateRichTextStatus())}},{key:"handleTouchEvent",get:function(){return this._handleTouchEvent},set:function(t){this._handleTouchEvent!==t&&(this._handleTouchEvent=t,this.enabledInHierarchy&&(this.handleTouchEvent?this._addEventListeners():this._removeEventListeners()))}}]),e}(E),ot.HorizontalAlign=L,ot.VerticalAlign=O,s((M=ot).prototype,"horizontalAlign",[F],Object.getOwnPropertyDescriptor(M.prototype,"horizontalAlign"),M.prototype),s(M.prototype,"verticalAlign",[D],Object.getOwnPropertyDescriptor(M.prototype,"verticalAlign"),M.prototype),s(M.prototype,"fontColor",[U],Object.getOwnPropertyDescriptor(M.prototype,"fontColor"),M.prototype),s(M.prototype,"font",[P],Object.getOwnPropertyDescriptor(M.prototype,"font"),M.prototype),s(M.prototype,"cacheMode",[w],Object.getOwnPropertyDescriptor(M.prototype,"cacheMode"),M.prototype),s(M.prototype,"imageAtlas",[X],Object.getOwnPropertyDescriptor(M.prototype,"imageAtlas"),M.prototype),V=c(M.prototype,"_lineHeight",[f],(function(){return 40})),B=c(M.prototype,"_string",[f],(function(){return"<color=#00ff00>Rich</color><color=#0fffff>Text</color>"})),G=c(M.prototype,"_horizontalAlign",[f],(function(){return L.LEFT})),Y=c(M.prototype,"_verticalAlign",[f],(function(){return O.TOP})),q=c(M.prototype,"_fontSize",[f],(function(){return 40})),J=c(M.prototype,"_fontColor",[f],(function(){return u.WHITE.clone()})),K=c(M.prototype,"_maxWidth",[f],(function(){return 0})),Q=c(M.prototype,"_fontFamily",[f],(function(){return"Arial"})),Z=c(M.prototype,"_font",[f],(function(){return null})),$=c(M.prototype,"_isSystemFontUsed",[f],(function(){return!0})),tt=c(M.prototype,"_userDefinedFont",[f],(function(){return null})),et=c(M.prototype,"_cacheMode",[f],(function(){return H.NONE})),it=c(M.prototype,"_imageAtlas",[f],(function(){return null})),nt=c(M.prototype,"_handleTouchEvent",[f],(function(){return!0})),N=M))||N)||N));d.RichText=dt}}}));
