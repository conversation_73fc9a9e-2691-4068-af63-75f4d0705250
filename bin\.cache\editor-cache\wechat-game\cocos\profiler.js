System.register(["./gc-object-CKHc4SnS.js","./global-exports-CR3GRnjt.js","./mesh-renderer-5-GqFOQS.js","./prefab-DH0xadMc.js","./create-mesh-hkbGggH3.js","./rendering-sub-mesh-CowWLfXC.js","./scene-7MDSMR3j.js","./device-manager-BvjvoelW.js","./buffer-barrier-CuX_5NUR.js","./pipeline-state-manager-Cdpe3is6.js","./node-event-DTNosVQv.js","./component-BaGvu7EF.js","./index-C5lmLqDW.js","./deprecated-T2yM9nsJ.js","./director-DIlqD2Nd.js","./mesh-C8knhDLk.js","./factory-D9_8ZCqM.js","./debug-view-CKetkq9d.js","./wasm-minigame-DoCiKH-Y.js","./zlib.min-CyXMsivM.js","./deprecated-Ca3AjUwj.js","./deprecated-D4QUWou_.js","./model-renderer-BcRDUYby.js","./renderer-9hfAnqUF.js","./touch-DB0AR-Sc.js"],(function(t){"use strict";var e,i,s,r,a,n,o,h,c,f,_,u,l,d,m,p,v,g,S,w,R,E,D,x,P,T;return{setters:[function(t){e=t.a,i=t._,s=t.s,r=t.S,a=t.d,n=t.w},function(t){o=t.c,h=t.a},function(t){c=t.M},null,function(t){f=t._},null,function(t){_=t.N,u=t.b},function(t){l=t.d},function(t){d=t.T,m=t.d,p=t.e,v=t.F,g=t.f},function(t){S=t.L},null,null,function(t){w=t.c,R=t.l,E=t.m,D=t.S},function(t){x=t.g},function(t){P=t.d,T=t.D},null,null,null,null,null,null,null,null,null,null],execute:function(){var N,y=function(){function t(t,e,i){this._total=0,this._value=0,this._averageValue=0,this._accumValue=0,this._accumSamples=0,this._id=t,this._opts=e,this._accumStart=i}var i=t.prototype;return i.sample=function(t){this._average(this._value,t)},i.human=function(){var t=this._opts,e=t.average,i=t.isInteger,s=e?this._averageValue:this._value;return i?Math.round(s):Math.round(100*s)/100},i.alarm=function(){return void 0!==this._opts.below&&this._value<this._opts.below||void 0!==this._opts.over&&this._value>this._opts.over},i._average=function(t,e){if(void 0===e&&(e=0),this._opts.average){this._accumValue+=t,++this._accumSamples;var i=e;i-this._accumStart>=this._opts.average&&(this._averageValue=this._accumValue/this._accumSamples,this._accumValue=0,this._accumStart=i,this._accumSamples=0)}},e(t,[{key:"value",get:function(){return this._value},set:function(t){this._value=t}}]),t}(),F=w("cc.PerfCounter")(N=function(t){function e(e,i,s){var r;return(r=t.call(this,e,i,s)||this)._time=s,r}i(e,t);var s=e.prototype;return s.start=function(t){void 0===t&&(t=0),this._time=t},s.end=function(t){void 0===t&&(t=0),this._value=t-this._time,this._average(this._value)},s.tick=function(){this.end(),this.start()},s.frame=function(t){var e=t,i=e-this._time;this._total++,i>(this._opts.average||1e3)&&(this._value=1e3*this._total/i,this._total=0,this._time=e,this._average(this._value))},e}(y))||N,b="0123456789. ",j=500,A={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,".":10},I={fps:{desc:"Framerate (FPS)",below:30,average:j,isInteger:!0},draws:{desc:"Draw call",isInteger:!0},frame:{desc:"Frame time (ms)",min:0,max:50,average:j},instances:{desc:"Instance Count",isInteger:!0},tricount:{desc:"Triangle",isInteger:!0},logic:{desc:"Game Logic (ms)",min:0,max:50,average:j,color:"#080"},physics:{desc:"Physics (ms)",min:0,max:50,average:j},render:{desc:"Renderer (ms)",min:0,max:50,average:j,color:"#f90"},present:{desc:"Present (ms)",min:0,max:50,average:j,color:"#f90"},textureMemory:{desc:"GFX Texture Mem(M)"},bufferMemory:{desc:"GFX Buffer Mem(M)"}},H=t("Profiler",function(t){function w(){var e;return(e=t.call(this)||this)._profilerStats=null,e._showFPS=!1,e._rootNode=null,e._device=null,e._swapchain=null,e._meshRenderer=null,e._canvas=null,e._ctx=null,e._texture=null,e._region=new g,e._canvasArr=[],e._regionArr=[e._region],e.digitsData=null,e.offsetData=null,e.pass=null,e._canvasDone=!1,e._statsDone=!1,e._inited=!1,e._lineHeight=280/(Object.keys(I).length+1),e._wordHeight=0,e._eachNumWidth=0,e._totalLines=0,e.lastTime=0,e._canvas=h.document.createElement("canvas"),e._ctx=e._canvas.getContext("2d"),e._canvasArr.push(e._canvas),e}i(w,t);var D=w.prototype;return D.init=function(){s.querySettings(r.PROFILING,"showFPS")?this.showStats():this.hideStats()},D.isShowingStats=function(){return this._showFPS},D.hideStats=function(){this._showFPS&&(this._rootNode&&(this._rootNode.active=!1),P.off(T.BEFORE_UPDATE,this.beforeUpdate,this),P.off(T.AFTER_UPDATE,this.afterUpdate,this),P.off(T.BEFORE_PHYSICS,this.beforePhysics,this),P.off(T.AFTER_PHYSICS,this.afterPhysics,this),P.off(T.BEFORE_DRAW,this.beforeDraw,this),P.off(T.AFTER_RENDER,this.afterRender,this),P.off(T.AFTER_DRAW,this.afterPresent,this),this._showFPS=!1,P.root.pipeline.profiler=null,o.game.config.showFPS=!1)},D.showStats=function(){if(!this._showFPS){if(!this._device){var t=o.director.root;this._device=l.gfxDevice,this._swapchain=t.mainWindow.swapchain}this.generateCanvas(),this.generateStats(),o.game.once(o.Game.EVENT_ENGINE_INITED,this.generateNode,this),o.game.on(o.Game.EVENT_RESTART,this.generateNode,this),this._rootNode&&(this._rootNode.active=!0),P.on(T.BEFORE_UPDATE,this.beforeUpdate,this),P.on(T.AFTER_UPDATE,this.afterUpdate,this),P.on(T.BEFORE_PHYSICS,this.beforePhysics,this),P.on(T.AFTER_PHYSICS,this.afterPhysics,this),P.on(T.BEFORE_DRAW,this.beforeDraw,this),P.on(T.AFTER_RENDER,this.afterRender,this),P.on(T.AFTER_DRAW,this.afterPresent,this),this._showFPS=!0,this._canvasDone=!0,this._statsDone=!0,o.game.config.showFPS=!0}},D.generateCanvas=function(){if(!this._canvasDone){var t=this._canvas,e=this._ctx;if(e&&t){t.width=280,t.height=280,t.style.width=""+t.width,t.style.height=""+t.height,e.font="23px Arial",e.textBaseline="top",e.fillStyle="#fff",this._texture=this._device.createTexture(new d(m.TEX2D,p.SAMPLED|p.TRANSFER_DST,v.RGBA8,280,280));var i=this._region.texExtent;i.width=280,i.height=280}}},D.generateStats=function(){if(!this._statsDone&&this._ctx&&this._canvas){this._profilerStats=null;var t=performance.now();this._ctx.textAlign="left";var e=0;for(var i in I){var s=I[i];this._ctx.fillText(s.desc,0,e*this._lineHeight),s.counter=new F(i,s,t),e++}this._totalLines=e,this._wordHeight=this._totalLines*this._lineHeight/this._canvas.height;for(var r=0;r<12;++r){var a=this._ctx.measureText(b[r]).width;this._eachNumWidth=Math.max(this._eachNumWidth,a)}for(var n=0;n<12;++n)this._ctx.fillText(b[n],n*this._eachNumWidth,this._totalLines*this._lineHeight);this._eachNumWidth/=this._canvas.width,this._profilerStats=I,this._canvasArr[0]=this._canvas,this._device.copyTexImagesToTexture(this._canvasArr,this._texture,this._regionArr)}},D.generateNode=function(){if(!this._rootNode||!this._rootNode.isValid){this._rootNode=new _("PROFILER_NODE"),this._rootNode._objFlags=a.DontSave|a.HideInHierarchy,x.addPersistRootNode(this._rootNode);var t=new _("Profiler_Root");t.parent=this._rootNode;for(var e=.4,i=e/this._totalLines,s=e/this._wordHeight,r=i/23,n=this._eachNumWidth*this._canvas.width*r,o=[0,e,0,s,e,0,s,0,0,0,0,0],h=[0,2,1,0,3,2],l=[0,0,-1,0,1,0,-1,0,1,this._wordHeight,-1,0,0,this._wordHeight,-1,0],d=0,m=0;m<this._totalLines;m++)for(var p=0;p<8;p++){o.push(s+p*n,e-m*i,0),o.push(s+(p+1)*n,e-m*i,0),o.push(s+(p+1)*n,e-(m+1)*i,0),o.push(s+p*n,e-(m+1)*i,0),d=4*(8*m+p+1),h.push(0+d,2+d,1+d,0+d,3+d,2+d);var v=8*m+p,g=Math.floor(v/4),w=v-4*g;l.push(0,this._wordHeight,g,w),l.push(this._eachNumWidth,this._wordHeight,g,w),l.push(this._eachNumWidth,1,g,w),l.push(0,1,g,w)}this._meshRenderer=t.addComponent(c),this._meshRenderer.mesh=f({positions:o,indices:h,colors:l});var R=new u;R.initialize({effectName:"util/profiler"});var E=this.pass=R.passes[0],D=E.getBinding("mainTexture"),P=E.getBinding("digits"),T=E.getBinding("offset");E.bindTexture(D,this._texture),this.digitsData=E.blocks[P],this.offsetData=E.blocks[T],this.offsetData[3]=-1,this._meshRenderer.material=R,this._meshRenderer.node.layer=S.Enum.PROFILER,this._inited=!0}},D.beforeUpdate=function(){if(this._profilerStats){var t=performance.now();this._profilerStats.frame.counter.start(t),this._profilerStats.logic.counter.start(t)}},D.afterUpdate=function(){if(this._profilerStats){var t=performance.now();P.isPaused()?this._profilerStats.frame.counter.start(t):this._profilerStats.logic.counter.end(t)}},D.beforePhysics=function(){if(this._profilerStats){var t=performance.now();this._profilerStats.physics.counter.start(t)}},D.afterPhysics=function(){if(this._profilerStats){var t=performance.now();this._profilerStats.physics.counter.end(t)}},D.beforeDraw=function(){if(this._profilerStats&&this._inited){var t=this._swapchain.surfaceTransform,e=this._device.capabilities.clipSpaceSignY;if(t!==this.offsetData[3]){var i=R[t],s=-.9,r=-.9*e;E.isXR&&(s=-.5,r=-.5*e),this.offsetData[0]=s*i[0]+r*i[2],this.offsetData[1]=s*i[1]+r*i[3],this.offsetData[2]=this._eachNumWidth,this.offsetData[3]=t}this.pass.setRootBufferDirty(!0),this._meshRenderer.model?P.root.pipeline.profiler=this._meshRenderer.model:P.root.pipeline.profiler=null;var a=performance.now();this._profilerStats.render.counter.start(a)}},D.afterRender=function(){if(this._profilerStats&&this._inited){var t=performance.now();this._profilerStats.render.counter.end(t),this._profilerStats.present.counter.start(t)}},D.afterPresent=function(){if(this._profilerStats&&this._inited){var t=performance.now();if(this._profilerStats.frame.counter.end(t),this._profilerStats.fps.counter.frame(t),this._profilerStats.present.counter.end(t),!(t-this.lastTime<j)){this.lastTime=t;var e=this._device;this._profilerStats.draws.counter.value=e.numDrawCalls,this._profilerStats.instances.counter.value=e.numInstances,this._profilerStats.bufferMemory.counter.value=e.memoryStatus.bufferSize/1048576,this._profilerStats.textureMemory.counter.value=e.memoryStatus.textureSize/1048576,this._profilerStats.tricount.counter.value=e.numTris;var i=0,s=this.digitsData;for(var r in this._profilerStats){var a=this._profilerStats[r];a.counter.sample(t);for(var n=a.counter.human().toString(),o=7;o>=0;o--){var h=8*i+o,c=n[n.length-(8-o)],f=A[c];void 0===f&&(f=11),s[h]=f}i++}}}},e(w,[{key:"_stats",get:function(){return n(16381),this._profilerStats}},{key:"stats",get:function(){return this._profilerStats}}]),w}(D)),M=t("profiler",new H);P.registerSystem("profiler",M,0),o.profiler=M}}}));
