System.register(["./gc-object-D18ulfCO.js","./index-Y4La_nfG.js","./global-exports-CLZKKIY2.js","./pipeline-state-manager-DQyhxoC_.js","./component-CsuvAQKv.js","./node-event-DTNosVQv.js","./touch-B157r-vS.js","./device-manager-B8lyUDPi.js","./buffer-barrier-q_79u36H.js","./factory-BOc5khhM.js"],(function(t){"use strict";var e,i,n,r,s,o,a,u,h,l,p,c,f,_,d,g,m,v,y,b,I,T,E,D,R,S,M,A,w,k,C,x,P,L,O,N,H,B,F,U,G,V,z,W,j,X,q,K,Y,Q,J,Z,$,tt,et,it,nt,rt,st,ot,at,ut,ht,lt,pt,ct,ft,_t,dt,gt,mt,vt,yt,bt,It,Tt,Et,Dt,Rt,St,Mt,At,wt,kt,Ct,xt,Pt,Lt,Ot,Nt,Ht,Bt,Ft,Ut,Gt,Vt,zt,Wt,jt,Xt,qt,Kt,Yt,Qt,Jt,Zt,$t,te,ee,ie,ne,re,se,oe,ae,ue,he,le,pe,ce,fe,_e,de,ge,me,ve,ye,be,Ie,Te,Ee,De,Re,Se,Me,Ae,we,ke,Ce,xe,Pe,Le,Oe,Ne,He,Be,Fe,Ue,Ge,Ve,ze,We,je,Xe,qe,Ke,Ye,Qe,Je,Ze,$e,ti,ei,ii,ni,ri,si,oi,ai,ui,hi,li,pi,ci,fi,_i,di;return{setters:[function(t){e=t.r,i=t._,n=t.h,r=t.a,s=t.ao,o=t.z,a=t.H,u=t.I,h=t.q,l=t.p,p=t.ap,c=t.k,f=t.K,_=t.w,d=t.F,g=t.g,m=t.o,v=t.aK,y=t.s,b=t.S,I=t.m,T=t.t,E=t.x,D=t.E,R=t.R,S=t.d,M=t.P,A=t.al,w=t.G,k=t.aL,C=t.az,x=t.J,P=t.b,L=t.n,O=t.l,N=t.f,H=t.V},function(t){B=t.c,F=t.a,U=t.s,G=t.t,V=t.V,z=t.b,W=t.e,j=t.K,X=t.M,q=t.C,K=t.Q,Y=t.b4,Q=t.Z,J=t.u,Z=t.q,$=t.a6,tt=t.aB,et=t.$,it=t.p,nt=t.L,rt=t.j,st=t.az},function(t){ot=t.c,at=t.l,ut=t.a,ht=t.P,lt=t.b},function(t){pt=t.n,ct=t.A,ft=t.S,_t=t.H,dt=t.N,gt=t.v,mt=t.w,vt=t.x,yt=t.R,bt=t.$,It=t.i,Tt=t.P,Et=t.a0,Dt=t.a1,Rt=t.L},function(t){St=t.A,Mt=t.h,At=t.o,wt=t.t,kt=t.u,Ct=t.v,xt=t.f,Pt=t.n,Lt=t.r,Ot=t.P,Nt=t.w,Ht=t.R,Bt=t.x,Ft=t.y,Ut=t.z,Gt=t.k,Vt=t.e,zt=t.l,Wt=t.b,jt=t.B,Xt=t.D,qt=t.F,Kt=t.C},function(t){Yt=t.N},function(t){Qt=t.d,Jt=t.I},function(t){Zt=t.d},function(t){$t=t.F,te=t.ad,ee=t.m,ie=t.p,ne=t.e,re=t.f,se=t.r,oe=t.T,ae=t.d,ue=t.bi,he=t.j,le=t.aN,pe=t.az,ce=t.b5,fe=t.g,_e=t.h,de=t.U,ge=t.i,me=t.k,ve=t.bk,ye=t.bn,be=t.bl,Ie=t.bm,Te=t.bj,Ee=t.A,De=t.b6,Re=t.S,Se=t.aX,Me=t.as,Ae=t.au,we=t.B,ke=t.b,Ce=t.M,xe=t.I,Pe=t.a4,Le=t.a6,Oe=t.a3,Ne=t.P,He=t.aD,Be=t.K,Fe=t.a7,Ue=t.ao,Ge=t.ay,Ve=t.bo,ze=t.ac,We=t.J},function(t){je=t.P,Xe=t.W,qe=t.T,Ke=t.e,Ye=t.I,Qe=t.u,Je=t.g,Ze=t.d,$e=t.h,ti=t.i,ei=t.j,ii=t.k,ni=t.l,ri=t.m,si=t.o,oi=t.n,ai=t.q,ui=t.t,hi=t.v,li=t.f,pi=t.r,ci=t.w,fi=t.x,_i=t.y,di=t.z}],execute:function(){var gi,mi,vi,yi,bi,Ii,Ti,Ei,Di,Ri,Si;t({D:Gn,G:ar,I:Vn,K:zn,ab:Al,ac:Sl,ae:function(t){for(var e=t.length-1;e>=0;--e){var i=t[e];if(i.window.swapchain)return void(Br=i)}Br=null},af:Yn,ag:jn,ah:Kn,ai:Xn,aj:$n,ak:ir,al:er,am:Zn,an:function(t,e,i,n,r){if(!It()&&n&&n.enabled&&r===Br){var s=n.subModels[0],o=s.inputAssembler,a=s.passes,u=s.shaders,h=s.descriptorSet;Sr.width=Mr.width=r.window.width,Sr.height=Mr.height=r.window.height;var l=Tt.getOrCreatePipelineState(t,a[0],u[0],e,o);i.setViewport(Sr),i.setScissor(Mr),i.bindPipelineState(l),i.bindDescriptorSet(ft.MATERIAL,a[0].descriptorSet),i.bindDescriptorSet(ft.LOCAL,h),i.bindInputAssembler(o),i.draw(o)}},ao:Ar,e:jr}),e($t);var Mi,Ai=new s("Tex"),wi=t("f",B("cc.TextureBase")((Si=function(t){function e(e){var i;return(i=t.call(this,e)||this)._format=vi&&vi(),i._minFilter=yi&&yi(),i._magFilter=bi&&bi(),i._mipFilter=Ii&&Ii(),i._wrapS=Ti&&Ti(),i._wrapT=Ei&&Ei(),i._wrapR=Di&&Di(),i._anisotropy=Ri&&Ri(),i._width=1,i._height=1,i._samplerInfo=new te,i._gfxSampler=null,i._gfxDevice=null,i._textureHash=0,i._id=Ai.getNewId(),i._gfxDevice=i._getGFXDevice(),i._textureHash=ee(i._id,666),i}i(e,t);var s=e.prototype;return s.getId=function(){return this._id},s.getPixelFormat=function(){return this._format},s.getAnisotropy=function(){return this._anisotropy},s.setWrapMode=function(t,e,i){void 0===i&&(i=t),this._wrapS=t,this._samplerInfo.addressU=t,this._wrapT=e,this._samplerInfo.addressV=e,this._wrapR=i,this._samplerInfo.addressW=i,this._gfxDevice&&(this._gfxSampler=this._gfxDevice.getSampler(this._samplerInfo))},s.setFilters=function(t,e){this._minFilter=t,this._samplerInfo.minFilter=t,this._magFilter=e,this._samplerInfo.magFilter=e,this._gfxDevice&&(this._gfxSampler=this._gfxDevice.getSampler(this._samplerInfo))},s.setMipFilter=function(t){this._mipFilter=t,this._samplerInfo.mipFilter=t,this._gfxDevice&&(this._gfxSampler=this._gfxDevice.getSampler(this._samplerInfo))},s.setAnisotropy=function(t){t=Math.min(t,16),this._anisotropy=t,this._samplerInfo.maxAnisotropy=t,this._gfxDevice&&(this._gfxSampler=this._gfxDevice.getSampler(this._samplerInfo))},s.destroy=function(){var e,i=t.prototype.destroy.call(this);return i&&null!=(e=ot.director.root)&&e.batcher2D&&ot.director.root.batcher2D._releaseDescriptorSetCache(this._textureHash),i},s.getHash=function(){return this._textureHash},s.getGFXTexture=function(){return null},s.getSamplerInfo=function(){return this._samplerInfo},s.getGFXSampler=function(){return this._gfxSampler||(this._gfxDevice?this._gfxSampler=this._gfxDevice.getSampler(this._samplerInfo):n(9302)),this._gfxSampler},s._serialize=function(){return""},s._deserialize=function(t){var e=t.split(",");e.unshift(""),e.length>=5&&(this.setFilters(parseInt(e[1]),parseInt(e[2])),this.setWrapMode(parseInt(e[3]),parseInt(e[4]))),e.length>=7&&(this.setMipFilter(parseInt(e[5])),this.setAnisotropy(parseInt(e[6])))},s._getGFXDevice=function(){return Zt.gfxDevice},s._getGFXFormat=function(){return this._getGFXPixelFormat(this._format)},s._setGFXFormat=function(t){this._format=void 0===t?je.RGBA8888:t},s._getGFXPixelFormat=function(t){return t===je.RGBA_ETC1?t=je.RGB_ETC1:t===je.RGB_A_PVRTC_4BPPV1?t=je.RGB_PVRTC_4BPPV1:t===je.RGB_A_PVRTC_2BPPV1&&(t=je.RGB_PVRTC_2BPPV1),t},r(e,[{key:"isCompressed",get:function(){return this._format>=je.RGB_ETC1&&this._format<=je.RGBA_ASTC_12x12||this._format>=je.RGB_A_PVRTC_2BPPV1&&this._format<=je.RGBA_ETC1}},{key:"width",get:function(){return this._width}},{key:"height",get:function(){return this._height}}]),e}(St),Si.PixelFormat=je,Si.WrapMode=Xe,Si.Filter=qe,vi=F((mi=Si).prototype,"_format",[U],(function(){return je.RGBA8888})),yi=F(mi.prototype,"_minFilter",[U],(function(){return qe.LINEAR})),bi=F(mi.prototype,"_magFilter",[U],(function(){return qe.LINEAR})),Ii=F(mi.prototype,"_mipFilter",[U],(function(){return qe.NONE})),Ti=F(mi.prototype,"_wrapS",[U],(function(){return Xe.REPEAT})),Ei=F(mi.prototype,"_wrapT",[U],(function(){return Xe.REPEAT})),Di=F(mi.prototype,"_wrapR",[U],(function(){return Xe.REPEAT})),Ri=F(mi.prototype,"_anisotropy",[U],(function(){return 0})),gi=mi))||gi);ot.TextureBase=wi;var ki=[new re];function Ci(t,e){for(var i=Math.max(t,e),n=0;i;)i>>=1,n++;return n}function xi(t){return t&&!(t&t-1)}function Pi(t,e,i){return!(t.gfxAPI===se.WEBGL)||xi(e)&&xi(i)}var Li,Oi,Ni,Hi,Bi,Fi=B("cc.SimpleTexture")(Mi=function(t){function e(e){var i;return(i=t.call(this,e)||this)._gfxTexture=null,i._gfxTextureView=null,i._mipmapLevel=1,i._textureWidth=0,i._textureHeight=0,i._baseLevel=0,i._maxLevel=1e3,i}i(e,t);var n=e.prototype;return n.getGFXTexture=function(){return this._gfxTextureView},n.destroy=function(){return this._tryDestroyTextureView(),this._tryDestroyTexture(),t.prototype.destroy.call(this)},n.updateImage=function(){this.updateMipmaps(0)},n.updateMipmaps=function(){},n.uploadData=function(t,e,i){if(void 0===e&&(e=0),void 0===i&&(i=0),this._gfxTexture&&!(this._mipmapLevel<=e)){var n=this._getGFXDevice();if(n){var r=ki[0];r.texExtent.width=this._textureWidth>>e,r.texExtent.height=this._textureHeight>>e,r.texSubres.mipLevel=e,r.texSubres.baseArrayLayer=i,ArrayBuffer.isView(t)?n.copyBuffersToTexture([t],this._gfxTexture,ki):n.copyTexImagesToTexture([t],this._gfxTexture,ki)}}},n._assignImage=function(t,e,i){var n=t.data;if(n&&(this.uploadData(n,e,i),this._checkTextureLoaded(),o.CLEANUP_IMAGE_CACHE)){var r=Ke.getDeps(this._uuid),s=r.indexOf(t._uuid);-1!==s&&(a(r,s),t.decRef())}},n._checkTextureLoaded=function(){this._textureReady()},n._textureReady=function(){this.loaded=!0,this.emit("load")},n._setMipmapLevel=function(t){this._mipmapLevel=t<1?1:t},n._setMipRange=function(t,e){this._baseLevel=t<1?0:t,this._maxLevel=e<1?0:e},n.setMipRange=function(t,e){u(t<=e,3124),this._setMipRange(t,e);var i=this._getGFXDevice();if(i){var n=this._createTextureView(i);this._tryDestroyTextureView(),this._gfxTextureView=n}},n._getGfxTextureCreateInfo=function(){return null},n._getGfxTextureViewCreateInfo=function(){return null},n._tryReset=function(){if(this._tryDestroyTextureView(),this._tryDestroyTexture(),0!==this._mipmapLevel){var t=this._getGFXDevice();t&&(this._createTexture(t),this._gfxTextureView=this._createTextureView(t))}},n.isUsingOfflineMipmaps=function(){return!1},n._createTexture=function(t){if(0!==this._width&&0!==this._height){var e=ie.NONE;this._mipFilter!==qe.NONE&&Pi(t,this._width,this._height)&&(this._mipmapLevel=Ci(this._width,this._height),this.isUsingOfflineMipmaps()||this.isCompressed||(e=ie.GEN_MIPMAP));var i=this._getGfxTextureCreateInfo({usage:ne.SAMPLED|ne.TRANSFER_DST|ne.COLOR_ATTACHMENT,format:this._getGFXFormat(),levelCount:this._mipmapLevel,flags:e});if(i){var n=t.createTexture(i);this._textureWidth=i.width,this._textureHeight=i.height,this._gfxTexture=n}}},n._createTextureView=function(t){if(!this._gfxTexture)return null;var e=this._maxLevel<this._mipmapLevel?this._maxLevel:this._mipmapLevel-1,i=this._getGfxTextureViewCreateInfo({texture:this._gfxTexture,format:this._getGFXFormat(),baseLevel:this._baseLevel,levelCount:e-this._baseLevel+1});return i?t.createTexture(i):null},n._tryDestroyTexture=function(){this._gfxTexture&&(this._gfxTexture.destroy(),this._gfxTexture=null)},n._tryDestroyTextureView=function(){this._gfxTextureView&&(this._gfxTextureView.destroy(),this._gfxTextureView=null)},r(e,[{key:"mipmapLevel",get:function(){return this._mipmapLevel}}]),e}(wi))||Mi;ot.SimpleTexture=Fi;var Ui,Gi,Vi,zi,Wi,ji,Xi,qi,Ki,Yi=t("h",(Li=B("cc.Texture2D"),Oi=G([Ye]),Li((Hi=function(t){function e(e){var i;return(i=t.call(this,e)||this)._mipmaps=Bi&&Bi(),i._generatedMipmaps=[],i}i(e,t);var n=e.prototype;return n._setMipmapParams=function(t){var e=this;if(this._generatedMipmaps=t,this._setMipmapLevel(this._generatedMipmaps.length),this._generatedMipmaps.length>0){var i=this._generatedMipmaps[0];this.reset({width:i.width,height:i.height,format:i.format,mipmapLevel:this._generatedMipmaps.length,baseLevel:this._baseLevel,maxLevel:this._maxLevel}),this._generatedMipmaps.forEach((function(t,i){e._assignImage(t,i)}))}else this.reset({width:0,height:0,mipmapLevel:this._generatedMipmaps.length,baseLevel:this._baseLevel,maxLevel:this._maxLevel})},n.initialize=function(){this.mipmaps=this._mipmaps},n.onLoaded=function(){this.initialize()},n.reset=function(t){this._width=t.width,this._height=t.height,this._setGFXFormat(t.format);var e=void 0===t.mipmapLevel?1:t.mipmapLevel;this._setMipmapLevel(e);var i=void 0===t.baseLevel?0:t.baseLevel,n=void 0===t.maxLevel?1e3:t.maxLevel;this._setMipRange(i,n),this._tryReset()},n.create=function(t,e,i,n,r,s){void 0===i&&(i=je.RGBA8888),void 0===n&&(n=1),void 0===r&&(r=0),void 0===s&&(s=1e3),this.reset({width:t,height:e,format:i,mipmapLevel:n,baseLevel:r,maxLevel:s})},n.toString=function(){return 0!==this._mipmaps.length?this._mipmaps[0].url:""},n.updateMipmaps=function(t,e){if(void 0===t&&(t=0),void 0===e&&(e=void 0),!(t>=this._generatedMipmaps.length))for(var i=Math.min(void 0===e?this._generatedMipmaps.length:e,this._generatedMipmaps.length-t),n=0;n<i;++n){var r=t+n;this._assignImage(this._generatedMipmaps[r],r)}},n.getHtmlElementObj=function(){return this._mipmaps[0]&&this._mipmaps[0].data instanceof HTMLElement?this._mipmaps[0].data:null},n.destroy=function(){return this._mipmaps=[],this._generatedMipmaps=[],t.prototype.destroy.call(this)},n.description=function(){return"<cc.Texture2D | Name = "+(this._mipmaps[0]?this._mipmaps[0].url:"")+" | Dimension = "+this.width+" x "+this.height+">"},n.releaseTexture=function(){this.destroy()},n._serialize=function(){return null},n._deserialize=function(e,i){var n=e;t.prototype._deserialize.call(this,n.base,i),this._mipmaps=new Array(n.mipmaps.length);for(var r=0;r<n.mipmaps.length;++r)if(this._mipmaps[r]=new Ye,n.mipmaps[r]){var s=n.mipmaps[r];i.result.push(this._mipmaps,""+r,s,h(Ye))}},n._getGfxTextureCreateInfo=function(t){var e=new oe(ae.TEX2D);return e.width=this._width,e.height=this._height,Object.assign(e,t),e},n._getGfxTextureViewCreateInfo=function(t){var e=new ue;return e.type=ae.TEX2D,Object.assign(e,t),e},n.initDefault=function(e){t.prototype.initDefault.call(this,e);var i=new Ye;i.initDefault(),this.image=i},n.validate=function(){return this.mipmaps&&0!==this.mipmaps.length},r(e,[{key:"mipmaps",get:function(){return this._mipmaps},set:function(t){this._mipmaps=t;var e=[];if(1===t.length){var i=t[0];e.push.apply(e,i.extractMipmaps())}else if(t.length>1)for(var n=0;n<t.length;++n){var r=t[n];e.push(r.extractMipmap0())}this._setMipmapParams(e)}},{key:"image",get:function(){return 0===this._mipmaps.length?null:this._mipmaps[0]},set:function(t){this.mipmaps=t?[t]:[]}}]),e}(Fi),Bi=F(Hi.prototype,"_mipmaps",[Oi],(function(){return[]})),Ni=Hi))||Ni));ot.Texture2D=Yi,function(t){t[t.right=0]="right",t[t.left=1]="left",t[t.top=2]="top",t[t.bottom=3]="bottom",t[t.front=4]="front",t[t.back=5]="back"}(qi||(qi={})),function(t){t[t.NONE=0]="NONE",t[t.AUTO=1]="AUTO",t[t.BAKED_CONVOLUTION_MAP=2]="BAKED_CONVOLUTION_MAP"}(Ki||(Ki={}));var Qi=t("a6",B("cc.TextureCube")((Xi=function(t){function e(e){var i;return(i=t.call(this,e)||this).isRGBE=Vi&&Vi(),i._mipmapAtlas=zi&&zi(),i._mipmapMode=Wi&&Wi(),i._mipmaps=ji&&ji(),i._generatedMipmaps=[],i}i(e,t);var s=e.prototype;return s._setMipmapParams=function(t){var e=this;if(this._generatedMipmaps=t,this._setMipmapLevel(this._generatedMipmaps.length),this._generatedMipmaps.length>0){var i=this._generatedMipmaps[0].front;this.reset({width:i.width,height:i.height,format:i.format,mipmapLevel:this._generatedMipmaps.length,baseLevel:this._baseLevel,maxLevel:this._maxLevel}),this._generatedMipmaps.forEach((function(t,i){Ji(t,(function(t,n){e._assignImage(t,i,n)}))}))}else this.reset({width:0,height:0,mipmapLevel:this._generatedMipmaps.length,baseLevel:this._baseLevel,maxLevel:this._maxLevel})},s.isUsingOfflineMipmaps=function(){return this._mipmapMode===Ki.BAKED_CONVOLUTION_MAP},e.fromTexture2DArray=function(t,i){for(var n=[],r=t.length/6,s=0;s<r;s++){var o=6*s;n.push({front:t[o+qi.front].image,back:t[o+qi.back].image,left:t[o+qi.left].image,right:t[o+qi.right].image,top:t[o+qi.top].image,bottom:t[o+qi.bottom].image})}return(i=i||new e).mipmaps=n,i},s.onLoaded=function(){this._mipmapMode===Ki.BAKED_CONVOLUTION_MAP?this.mipmapAtlas=this._mipmapAtlas:this.mipmaps=this._mipmaps},s.reset=function(t){this._width=t.width,this._height=t.height,this._setGFXFormat(t.format);var e=void 0===t.mipmapLevel?1:t.mipmapLevel;this._setMipmapLevel(e);var i=void 0===t.baseLevel?0:t.baseLevel,n=void 0===t.maxLevel?1e3:t.maxLevel;this._setMipRange(i,n),this._tryReset()},s.updateMipmaps=function(t,e){var i=this;if(void 0===t&&(t=0),void 0===e&&(e=void 0),!(t>=this._generatedMipmaps.length))for(var n=Math.min(void 0===e?this._generatedMipmaps.length:e,this._generatedMipmaps.length-t),r=function(){var e=t+s;Ji(i._generatedMipmaps[e],(function(t,n){i._assignImage(t,e,n)}))},s=0;s<n;++s)r()},s.destroy=function(){return this._mipmaps=[],this._generatedMipmaps=[],this._mipmapAtlas=null,t.prototype.destroy.call(this)},s.releaseTexture=function(){this.destroy()},s._serialize=function(){return null},s._deserialize=function(e,i){var n=e;if(t.prototype._deserialize.call(this,n.base,i),this.isRGBE=n.rgbe,this._mipmapMode=n.mipmapMode,this._mipmapMode===Ki.BAKED_CONVOLUTION_MAP){var r=n.mipmapAtlas,s=n.mipmapLayout;this._mipmapAtlas={atlas:{},layout:s},this._mipmapAtlas.atlas={front:new Ye,back:new Ye,left:new Ye,right:new Ye,top:new Ye,bottom:new Ye};var o=h(Ye);i.result.push(this._mipmapAtlas.atlas,"front",r.front,o),i.result.push(this._mipmapAtlas.atlas,"back",r.back,o),i.result.push(this._mipmapAtlas.atlas,"left",r.left,o),i.result.push(this._mipmapAtlas.atlas,"right",r.right,o),i.result.push(this._mipmapAtlas.atlas,"top",r.top,o),i.result.push(this._mipmapAtlas.atlas,"bottom",r.bottom,o)}else{this._mipmaps=new Array(n.mipmaps.length);for(var a=0;a<n.mipmaps.length;++a){this._mipmaps[a]={front:new Ye,back:new Ye,left:new Ye,right:new Ye,top:new Ye,bottom:new Ye};var u=n.mipmaps[a],l=h(Ye);i.result.push(this._mipmaps[a],"front",u.front,l),i.result.push(this._mipmaps[a],"back",u.back,l),i.result.push(this._mipmaps[a],"left",u.left,l),i.result.push(this._mipmaps[a],"right",u.right,l),i.result.push(this._mipmaps[a],"top",u.top,l),i.result.push(this._mipmaps[a],"bottom",u.bottom,l)}}},s._getGfxTextureCreateInfo=function(t){var e=new oe(ae.CUBE);return e.width=this._width,e.height=this._height,e.layerCount=6,Object.assign(e,t),e},s._getGfxTextureViewCreateInfo=function(t){var e=new ue;return e.type=ae.CUBE,e.baseLayer=0,e.layerCount=6,Object.assign(e,t),e},s._uploadAtlas=function(){var t=this,e=this._mipmapAtlas.layout,i=e[0];this.reset({width:i.width,height:i.height,format:this._mipmapAtlas.atlas.front.format,mipmapLevel:e.length}),Ji(this._mipmapAtlas.atlas,(function(i,n){var r=new Yi;r.image=i,r.reset({width:i.width,height:i.height,format:i.format}),r.uploadData(i.data);for(var s=0;s<e.length;s++){var o=e[s],a=r.getGFXTexture().size,u=new Uint8Array(a),h=new re;h.texOffset.x=o.left,h.texOffset.y=o.top,h.texExtent.width=o.width,h.texExtent.height=o.height,t._getGFXDevice().copyTextureToBuffers(r.getGFXTexture(),[u],[h]);var l=new Ye({_data:u,_compressed:i.isCompressed,width:o.width,height:o.height,format:i.format});t._assignImage(l,o.level,n)}}))},s.initDefault=function(e){t.prototype.initDefault.call(this,e);var i=new Ye;i.initDefault(),this.mipmaps=[{front:i,back:i,top:i,bottom:i,left:i,right:i}]},s.validate=function(){if(this._mipmapMode===Ki.BAKED_CONVOLUTION_MAP){if(null===this.mipmapAtlas||0===this.mipmapAtlas.layout.length)return!1;var t=this.mipmapAtlas.atlas;return!!(t.top&&t.bottom&&t.front&&t.back&&t.left&&t.right)}return 0!==this._mipmaps.length&&!this._mipmaps.find((function(t){return!(t.top&&t.bottom&&t.front&&t.back&&t.left&&t.right)}))},r(e,[{key:"mipmaps",get:function(){return this._mipmaps},set:function(t){this._mipmaps=t;var e=[];if(1===t.length){var i=t[0],r=i.front.extractMipmaps(),s=i.back.extractMipmaps(),o=i.left.extractMipmaps(),a=i.right.extractMipmaps(),u=i.top.extractMipmaps(),h=i.bottom.extractMipmaps();if(r.length!==s.length||r.length!==o.length||r.length!==a.length||r.length!==u.length||r.length!==h.length)return n(16347),void this._setMipmapParams([]);for(var l=r.length,p=0;p<l;++p){var c={front:r[p],back:s[p],left:o[p],right:a[p],top:u[p],bottom:h[p]};e.push(c)}}else t.length>1&&t.forEach((function(t){var i={front:t.front.extractMipmap0(),back:t.back.extractMipmap0(),left:t.left.extractMipmap0(),right:t.right.extractMipmap0(),top:t.top.extractMipmap0(),bottom:t.bottom.extractMipmap0()};e.push(i)}));this._setMipmapParams(e)}},{key:"mipmapAtlas",get:function(){return this._mipmapAtlas},set:function(t){var e=this;if(this._mipmapAtlas=t,this._mipmapAtlas){var i=this._mipmapAtlas.atlas.front;if(i.data){var n=this._mipmapAtlas.atlas,r=this._mipmapAtlas.layout,s=r[0],o=Object.assign(ut.document.createElement("canvas"),{width:i.width,height:i.height}).getContext("2d");this.reset({width:s.width,height:s.height,format:i.format,mipmapLevel:r.length});for(var a=function(){var t=r[u];Ji(n,(function(n,r){o.clearRect(0,0,i.width,i.height);var s=n.data;o.drawImage(s,0,0);var a=o.getImageData(t.left,t.top,t.width,t.height),u=new Ye({_data:a.data,_compressed:n.isCompressed,width:a.width,height:a.height,format:n.format});e._assignImage(u,t.level,r)}))},u=0;u<r.length;u++)a()}}else this.reset({width:0,height:0,mipmapLevel:0})}},{key:"image",get:function(){return 0===this._mipmaps.length?null:this._mipmaps[0]},set:function(t){this.mipmaps=t?[t]:[]}}]),e}(Fi),Xi.FaceIndex=qi,Vi=F((Gi=Xi).prototype,"isRGBE",[U],(function(){return!1})),zi=F(Gi.prototype,"_mipmapAtlas",[U],(function(){return null})),Wi=F(Gi.prototype,"_mipmapMode",[U],(function(){return Ki.NONE})),ji=F(Gi.prototype,"_mipmaps",[U],(function(){return[]})),Ui=Gi))||Ui);function Ji(t,e){e(t.front,qi.front),e(t.back,qi.back),e(t.left,qi.left),e(t.right,qi.right),e(t.top,qi.top),e(t.bottom,qi.bottom)}at.TextureCube=Qi;var Zi=function(){function t(){this._loading=new Mt,this._unpackers={".json":this.unpackJson}}var e=t.prototype;return e.unpackJson=function(t,e,i,r){var s=l(!0),o=null;if(Array.isArray(e)){(e=Qe(e)).length!==t.length&&n(4915);for(var a=0;a<t.length;a++)s[t[a]+"@import"]=e[a]}else{var u=h(Yi),p=h(Ye);if(e.type===u&&e.data){var c=e.data;c.length!==t.length&&n(4915);for(var f=0;f<t.length;f++)s[t[f]+"@import"]=Je(u,{base:c[f][0],mipmaps:c[f][1]})}else{if(e.type!==p||!e.data)return void r(o=new Error("unmatched type pack!"),null);var _=e.data;_.length!==t.length&&n(4915);for(var d=0;d<t.length;d++)s[t[d]+"@import"]=_[d]}}r(o,s)},e.init=function(){this._loading.clear()},e.register=function(t,e){"object"==typeof t?p(this._unpackers,t):this._unpackers[t]=e},e.unpack=function(t,e,i,n,r){e?(0,this._unpackers[i])(t,e,n,r):r(new Error("package data is wrong!"))},e.load=function(t,e,i){var n=this;if(!t.isNative&&t.info&&t.info.packs)if(At.has(t.id))i(null,At.get(t.id));else{var r=t.info.packs,s=r.find((function(t){return n._loading.has(t.uuid)}));if(s)this._loading.get(s.uuid).push({onComplete:i,id:t.id});else{var o=r[0];this._loading.add(o.uuid,[{onComplete:i,id:t.id}]),c(t.config);var a=wt(o.uuid,{ext:o.ext,bundle:t.config.name});Ze.download(o.uuid,a,o.ext,t.options,(function(e,i){At.remove(o.uuid),e&&f(e.message,e.stack),n.unpack(o.packedUuids,i,o.ext,t.options,(function(t,i){if(!t)for(var r in i)At.add(r,i[r]);for(var s=n._loading.remove(o.uuid),a=0,u=s.length;a<u;a++){var h=s[a];if(e||t)h.onComplete(e||t);else{var l=i[h.id];l?h.onComplete(null,l):h.onComplete(new Error("can not retrieve data from package"))}}}))}))}}else Ze.download(t.id,t.url,t.ext,t.options,i)},t}(),$i=new Zi;function tn(t,e){var i=!1;t.progress||(t.progress={finish:0,total:t.input.length,canInvoke:!0},i=!0);var n=t.options,r=t.progress,s=[],o=r.total,a=n.__exclude__=n.__exclude__||Object.create(null);t.output=[],$e(t.input,(function(n,u){if(!n.isNative&&xt.has(n.uuid)){var h=xt.get(n.uuid);return n.content=h.addRef(),t.output.push(n),r.canInvoke&&t.dispatch("progress",++r.finish,r.total,n),void u()}$i.load(n,t.options,(function(h,l){h?t.isFinished||(!ot.assetManager.force||i?(f(h.message,h.stack),r.canInvoke=!1,e(h)):(t.output.push(n),r.canInvoke&&t.dispatch("progress",++r.finish,r.total,n))):t.isFinished||(n.file=l,t.output.push(n),n.isNative||(a[n.uuid]=!0,ei(n.uuid,l,a,s,n.config),r.total=o+s.length),r.canInvoke&&t.dispatch("progress",++r.finish,r.total,n)),u()}))}),(function(){if(t.isFinished)return ti(t),void t.dispatch("error");if(s.length>0){var o=kt.create({input:s,progress:r,options:n,onProgress:t.onProgress,onError:kt.prototype.recycle,onComplete:function(n){var r;n||((r=t.output).push.apply(r,o.output),o.recycle()),i&&en(t),e(n)}});Ct.async(o)}else i&&en(t),e()}))}function en(t){for(var e=t.output,i=0,n=e.length;i<n;i++)e[i].content&&e[i].content.decRef(!1)}var nn=function(t){function e(){return t.apply(this,arguments)||this}i(e,t);var n=e.prototype;return n.parse=function(t){var e=this._parseXML(t).documentElement;if("plist"!==e.tagName)return _(5100),{};for(var i=null,n=0,r=e.childNodes.length;n<r&&1!==(i=e.childNodes[n]).nodeType;n++);return this._parseNode(i)},n._parseNode=function(t){var e=null,i=t.tagName;if("dict"===i)e=this._parseDict(t);else if("array"===i)e=this._parseArray(t);else if("string"===i)if(1===t.childNodes.length)e=t.firstChild.nodeValue;else{e="";for(var n=0;n<t.childNodes.length;n++)e+=t.childNodes[n].nodeValue}else"false"===i?e=!1:"true"===i?e=!0:"real"===i?e=parseFloat(t.firstChild.nodeValue):"integer"===i&&(e=parseInt(t.firstChild.nodeValue,10));return e},n._parseArray=function(t){for(var e=[],i=0,n=t.childNodes.length;i<n;i++){var r=t.childNodes[i];1===r.nodeType&&e.push(this._parseNode(r))}return e},n._parseDict=function(t){for(var e={},i="",n=0,r=t.childNodes.length;n<r;n++){var s=t.childNodes[n];1===s.nodeType&&("key"===s.tagName?i=s.firstChild.nodeValue:e[i]=this._parseNode(s))}return e},e}(t("k",function(){function t(){this._parser=null,globalThis.DOMParser&&(this._parser=new DOMParser)}var e=t.prototype;return e.parse=function(t){return this._parseXML(t)},e._parseXML=function(t){if(this._parser)return this._parser.parseFromString(t,"text/xml");throw new Error("Dom parser is not supported in this platform!")},t}())),rn=new nn,sn=function(){function t(){this._parsing=new Mt,this._parsers={".png":this.parseImage,".jpg":this.parseImage,".bmp":this.parseImage,".jpeg":this.parseImage,".gif":this.parseImage,".ico":this.parseImage,".tiff":this.parseImage,".webp":this.parseImage,".image":this.parseImage,".pvr":this.parsePVRTex,".pkm":this.parsePKMTex,".astc":this.parseASTCTex,".plist":this.parsePlist,import:this.parseImport,".ccon":this.parseImport,".cconb":this.parseImport}}var e=t.prototype;return e.parseImage=function(t,e,i){t instanceof HTMLImageElement?i(null,t):createImageBitmap(t,{premultiplyAlpha:"none"}).then((function(t){i(null,t)}),(function(t){i(t,null)}))},e.parsePVRTex=function(t,e,i){var n=null,r=null;try{r=Ye.parseCompressedTextures(t,0)}catch(t){d(n=t)}i(n,r)},e.parsePKMTex=function(t,e,i){var n=null,r=null;try{r=Ye.parseCompressedTextures(t,1)}catch(t){d(n=t)}i(n,r)},e.parseASTCTex=function(t,e,i){var n=null,r=null;try{r=Ye.parseCompressedTextures(t,2)}catch(t){d(n=t)}i(n,r)},e.parsePlist=function(t,e,i){var n=null,r=rn.parse(t);r||(n=new Error("parse failed")),i(n,r)},e.parseImport=function(t,e,i){if(t){var n=null,r=null;try{n=ii(t,e)}catch(t){r=t}i(r,n)}else i(new Error(g(3702,e.__uuid__)))},e.init=function(){this._parsing.clear()},e.register=function(t,e){"object"==typeof t?p(this._parsers,t):this._parsers[t]=e},e.parse=function(t,e,i,n,r){var s=this,o=Pt.get(t);if(o)r(null,o);else{var a=this._parsing.get(t);if(a)a.push(r);else{var u=this._parsers[i];u?(this._parsing.add(t,[r]),u(e,n,(function(e,i){e?At.remove(t):Lt(i)||Pt.add(t,i);for(var n=s._parsing.remove(t),r=0,o=n.length;r<o;r++)n[r](e,i)}))):r(null,e)}}},r(t,null,[{key:"instance",get:function(){return this._instance||(this._instance=new t),this._instance}}]),t}();sn._instance=void 0;var on=t("a9",sn.instance);function an(t,e){var i=!1;t.progress||(t.progress={finish:0,total:t.input.length,canInvoke:!0},i=!0);var n=t.options,r=t.progress;n.__exclude__=n.__exclude__||Object.create(null),t.output=[],$e(t.input,(function(s,o){var a=kt.create({input:s,onProgress:t.onProgress,options:n,progress:r,onComplete:function(n,u){n&&!t.isFinished&&(!ot.assetManager.force||i?(f(n.message,n.stack),r.canInvoke=!1,e(n)):r.canInvoke&&t.dispatch("progress",++r.finish,r.total,s)),t.output.push(u),a.recycle(),o(null)}});un.async(a)}),(function(){if(n.__exclude__=null,t.isFinished)return ti(t),void t.dispatch("error");ui(t),ti(t),e()}))}var un=new Ot("loadOneAsset",[function(t,e){var i=t.output=t.input,n=i.options,r=i.isNative,s=i.uuid,o=i.file,a=n.reloadAsset;o||!a&&!r&&xt.has(s)?e():$i.load(i,t.options,(function(t,n){i.file=n,e(t)}))},function(t,e){var i=t.output=t.input,n=t.progress,r=t.options.__exclude__,s=i.id,o=i.file,a=i.options;if(i.isNative)on.parse(s,o,i.ext,a,(function(r,o){r?e(r):(i.content=o,n.canInvoke&&t.dispatch("progress",++n.finish,n.total,i),At.remove(s),Pt.remove(s),e())}));else{var u=i.uuid;if(u in r){var h=r[u],l=h.finish,p=h.content,c=h.err,f=h.callbacks;n.canInvoke&&t.dispatch("progress",++n.finish,n.total,i),l||ni(u,u,r)?(p&&p.addRef(),i.content=p,e(c)):f.push({done:e,item:i})}else if(!a.reloadAsset&&xt.has(u)){var _=xt.get(u);i.content=_.addRef(),n.canInvoke&&t.dispatch("progress",++n.finish,n.total,i),e()}else a.__uuid__=u,on.parse(s,o,"import",a,(function(i,n){i?e(i):hn(t,n,e)}))}}]);function hn(t,e,i){var r=t.input,s=t.progress,o=r,a=o.uuid,u=o.id,h=o.options,l=o.config,p=h.cacheAsset,c=[];e.addRef&&e.addRef(),ei(a,e,Object.create(null),c,l),s.canInvoke&&t.dispatch("progress",++s.finish,s.total+=c.length,r);var f=t.options.__exclude__[a]={content:e,finish:!1,callbacks:[{done:i,item:r}]},_=kt.create({input:c,options:t.options,onProgress:t.onProgress,onError:kt.prototype.recycle,progress:s,onComplete:function(t){if(e.decRef&&e.decRef(!1),f.finish=!0,f.err=t,!t){for(var i,r=Array.isArray(_.output)?_.output:[_.output],s=Object.create(null),o=m(r);!(i=o()).done;){var h=i.value;h&&(s[h instanceof St?h._uuid+"@import":a+"@native"]=h)}ri(a,e,s);try{"function"!=typeof e.onLoaded||si.has(e)||oi.has(e)||(e.onLoaded(),si.add(e))}catch(t){n(16352,a,t.message,t.stack)}At.remove(u),Pt.remove(u),ai(a,e,p),_.recycle()}for(var l=f.callbacks,c=0,d=l.length;c<d;c++){var g=l[c];e.addRef&&e.addRef(),g.item.content=e,g.done(t)}l.length=0}});Nt.async(_)}function ln(t,e){var i=t.options,n=Object.create(null),r=Object.create(null);for(var s in i)switch(s){case Ht.PATH:case Ht.UUID:case Ht.DIR:case Ht.SCENE:case Ht.URL:break;case"__requestType__":case"__isNative__":case"ext":case"type":case"__nativeName__":case"audioLoadMode":case"bundle":n[s]=i[s];break;case"__exclude__":case"__outputAsArray__":r[s]=i[s];break;default:n[s]=i[s],r[s]=i[s]}t.options=r;var o=kt.create({input:t.input,options:n}),a=null;try{t.output=t.source=Bt.sync(o)}catch(t){a=t;for(var u=0,h=o.output.length;u<h;u++)o.output[u].recycle()}o.recycle(),e(a)}var pn=function(){function t(){this.uuid="",this.overrideUuid="",this.url="",this.ext=".json",this.content=null,this.file=null,this.info=null,this.config=null,this.isNative=!1,this.options=Object.create(null),this._id=""}return t.create=function(){return 0!==t._deadPool.length?t._deadPool.pop():new t},t.prototype.recycle=function(){t._deadPool.length!==t.MAX_DEAD_NUM&&(this._id="",this.uuid="",this.overrideUuid="",this.url="",this.ext=".json",this.content=null,this.file=null,this.info=null,this.config=null,this.isNative=!1,this.options=Object.create(null),t._deadPool.push(this))},r(t,[{key:"id",get:function(){return this._id||(this._id=(this.overrideUuid||this.uuid)+"@"+(this.isNative?"native":"import")),this._id}}]),t}();pn.MAX_DEAD_NUM=500,pn._deadPool=[];var cn=[];function fn(t){var e=t.options,i=Array.isArray(t.input)?t.input:[t.input];t.output=[];for(var n=function(){var n=i[r],s=pn.create(),o=null,a=null;if("string"==typeof n&&((n=Object.create(null))[e.__requestType__||Ht.UUID]=i[r]),"object"==typeof n){v(n,e),n.preset&&v(n,Ft[n.preset]);var u=function(){var t;switch(h){case Ht.UUID:var e,r=s.uuid=zt(n.uuid);if(!n.bundle){var u=Gt.find((function(t){return!!t.getAssetInfo(r)}));n.bundle=u&&u.name}if(Gt.has(n.bundle)){if(o=Gt.get(n.bundle).config,(a=o.getAssetInfo(r))&&a.redirect){if(!Gt.has(a.redirect))throw new Error("Please load bundle "+a.redirect+" first");o=Gt.get(a.redirect).config,a=o.getAssetInfo(r)}s.config=o,s.info=a}s.ext=n.ext||(null==(e=a)?void 0:e.extension)||".json";break;case"__requestType__":case"ext":case"bundle":case"preset":case"type":break;case Ht.DIR:if(Gt.has(n.bundle)){Gt.get(n.bundle).config.getDirWithPath(n.dir,n.type,cn);for(var l,p=m(cn);!(l=p()).done;){var c=l.value;i.push({uuid:c.uuid,__isNative__:!1,ext:c.extension||".json",bundle:n.bundle})}cn.length=0}s.recycle(),s=null;break;case Ht.PATH:if(Gt.has(n.bundle)){if(o=Gt.get(n.bundle).config,(a=o.getInfoWithPath(n.path,n.type))&&a.redirect){if(!Gt.has(a.redirect))throw new Error("you need to load bundle "+a.redirect+" first");o=Gt.get(a.redirect).config,a=o.getAssetInfo(a.uuid)}if(!a)throw s.recycle(),new Error("Bundle "+n.bundle+" doesn't contain "+n.path);s.config=o,s.uuid=a.uuid,s.info=a}s.ext=n.ext||(null==(t=a)?void 0:t.extension)||".json";break;case Ht.SCENE:if(!n.bundle){var f=Gt.find((function(t){return!!t.getSceneInfo(n.scene)}));n.bundle=f&&f.name}if(Gt.has(n.bundle)){if(o=Gt.get(n.bundle).config,(a=o.getSceneInfo(n.scene))&&a.redirect){if(!Gt.has(a.redirect))throw new Error("you need to load bundle "+a.redirect+" first");o=Gt.get(a.redirect).config,a=o.getAssetInfo(a.uuid)}if(!a)throw s.recycle(),new Error("Bundle "+o.name+" doesn't contain scene "+n.scene);s.config=o,s.uuid=a.uuid,s.info=a}break;case"__isNative__":s.isNative=n.__isNative__;break;case Ht.URL:s.url=n.url,s.uuid=n.uuid||n.url,s.ext=n.ext||Vt(n.url),s.isNative=void 0===n.__isNative__||n.__isNative__;break;default:s.options[h]=n[h]}if(!s)return 1};for(var h in n)if(u())break}if(!s)return 1;if(t.output.push(s),!s.uuid&&!s.url)throw new Error("Can not parse this input:"+JSON.stringify(n))},r=0;r<i.length;r++)n();return null}function _n(t){for(var e=t.output=t.input,i=function(){var t=e[n];if(Ut.has(t.uuid)){var i=Ut.get(t.uuid),r=Gt.find((function(t){return!!t.getAssetInfo(i)}));if(r){var s;t.overrideUuid=i;var o=r.config,a=o.getAssetInfo(i);if(a&&a.redirect){if(!Gt.has(a.redirect))throw new Error("Please load bundle "+a.redirect+" first");a=(o=Gt.get(a.redirect).config).getAssetInfo(i)}t.config=o,t.info=a,t.ext=t.isNative?t.ext:(null==(s=a)?void 0:s.extension)||".json"}else _(16201,i,t.uuid)}},n=0;n<e.length;n++)i()}function dn(t){for(var e=t.output=t.input,i=0;i<e.length;i++){var n=e[i];if(!n.url){var r,s,o=n.config;s=n.isNative?o&&o.nativeBase?o.base+o.nativeBase:ot.assetManager.generalNativeBase:o&&o.importBase?o.base+o.importBase:ot.assetManager.generalImportBase;var a=n.overrideUuid||n.uuid,u="";n.info&&(u=n.isNative?n.info.nativeVer?"."+n.info.nativeVer:"":n.info.ver?"."+n.info.ver:""),r=".ttf"===n.ext?s+"/"+a.slice(0,2)+"/"+a+u+"/"+n.options.__nativeName__:s+"/"+a.slice(0,2)+"/"+a+u+n.ext,n.url=r}}return null}var gn=y.querySettings.bind(y),mn=b.ASSETS,vn="asset-missing",yn=t("a7",function(){function t(){this.pipeline=Nt.append(ln).append(an),this.fetchPipeline=Ct.append(ln).append(tn),this.transformPipeline=Bt.append(fn).append(_n).append(dn),this.bundles=Gt,this.assets=xt,this.assetsOverrideMap=Ut,this.generalImportBase="",this.generalNativeBase="",this.dependUtil=Ke,this.force=ht,this.allowImageBitmap=!1,this.utils=Xt,this.downloader=Ze,this.parser=on,this.packManager=$i,this.cacheAsset=!0,this.cacheManager=null,this.presets=Ft,this.factory=li,this.preprocessPipe=ln,this.fetchPipe=tn,this.loadPipe=an,this.references=qt,this._releaseManager=pi,this._files=At,this._parsed=Pt,this._parsePipeline=null,this._projectBundles=[],this._eventTarget=new I}var e=t.prototype;return e.getReleaseManager=function(){return this._releaseManager},e.onAssetMissing=function(t,e){this._eventTarget.on(vn,t,e)},e.offAssetMissing=function(t,e){this._eventTarget.off(vn,t,e)},e.dispatchAssetMissing=function(t,e,i,n){this._eventTarget.emit(vn,t,e,i,n)},e.init=function(t){void 0===t&&(t={});var e=t.server||gn(mn,"server")||"",i=t.bundleVers||gn(mn,"bundleVers")||{},n=t.remoteBundles||gn(mn,"remoteBundles")||[],r=t.downloadMaxConcurrency||gn(mn,"downloadMaxConcurrency");r&&r>0&&(this.downloader.maxConcurrency=r),this._files.clear(),this._parsed.clear(),this._releaseManager.init(),this.assets.clear(),this.bundles.clear(),this.packManager.init(),this.downloader.init(e,i,n),this.parser.init(),this.dependUtil.init();var s=t.importBase||gn(mn,"importBase")||"";s&&s.endsWith("/")&&(s=s.substring(0,s.length-1));var o=t.nativeBase||gn(mn,"nativeBase")||"";o&&o.endsWith("/")&&(o=o.substring(0,o.length-1)),this.generalImportBase=s,this.generalNativeBase=o,this._projectBundles=gn(mn,"projectBundles")||[];var a=gn(mn,"assetsOverrides")||{};for(var u in a)this.assetsOverrideMap.set(u,a[u])},e.getBundle=function(t){return Gt.get(t)||null},e.removeBundle=function(t){t._destroy(),Gt.remove(t.name)},e.loadAny=function(t,e,i,n){var r=di(e,i,n),s=r.options,o=r.onProgress,a=r.onComplete;s.preset=s.preset||"default",t=Array.isArray(t)?t.slice():t;var u=kt.create({input:t,onProgress:o,onComplete:hi(a),options:s});Nt.async(u)},e.preloadAny=function(t,e,i,n){var r=di(e,i,n),s=r.options,o=r.onProgress,a=r.onComplete;s.preset=s.preset||"preload",t=Array.isArray(t)?t.slice():t;var u=kt.create({input:t,onProgress:o,onComplete:hi(a),options:s});Ct.async(u)},e.loadRemote=function(t,e,i){var n=di(e,void 0,i),r=n.options,s=n.onComplete;r.reloadAsset||!this.assets.has(t)?(r.__isNative__=!0,r.preset=r.preset||"remote",this.loadAny({url:t},r,null,(function(e,i){e?(f(e.message,e.stack),s&&s(e,i)):li.create(t,i,r.ext||Vt(t),r,(function(t,e){s&&s(t,e)}))}))):hi(s)(null,this.assets.get(t))},e.loadBundle=function(t,e,i){var n=di(e,void 0,i),r=n.options,s=n.onComplete,o=Wt(t);this.bundles.has(o)?hi(s)(null,this.getBundle(o)):(r.preset=r.preset||"bundle",r.ext="bundle",r.__isNative__=!0,this.loadAny({url:t},r,null,(function(e,i){e?(f(e.message,e.stack),s&&s(e,i)):li.create(t,i,"bundle",r,(function(t,e){s&&s(t,e)}))})))},e.releaseAsset=function(t){pi.tryRelease(t,!0)},e.releaseUnusedAssets=function(){xt.forEach((function(t){pi.tryRelease(t)}))},e.releaseAll=function(){xt.forEach((function(t){pi.tryRelease(t,!0)}))},e.loadWithJson=function(){throw new Error("Only valid in Editor")},r(t,[{key:"files",get:function(){return this._files}},{key:"main",get:function(){return Gt.get(jt.MAIN)||null}},{key:"resources",get:function(){return Gt.get(jt.RESOURCES)||null}}],[{key:"instance",get:function(){return this._instance||(this._instance=new t),this._instance}}]),t}());yn._instance=void 0,yn.Pipeline=Ot,yn.Task=kt,yn.Cache=Mt,yn.RequestItem=pn,yn.Bundle=ci,yn.BuiltinBundleName=jt,yn.CacheManager=function(){this.cacheDir=void 0,this.cacheEnabled=void 0,this.autoClear=void 0,this.cacheInterval=void 0,this.deleteInterval=void 0,this.cachedFiles=void 0},yn.Downloader=fi,yn.Parser=sn,yn.DependUtil=_i;var bn=t("i",ot.assetManager=yn.instance);ot.AssetManager=yn;var In,Tn,En,Dn,Rn,Sn=t("a8",function(){function t(){this._resources={},this._materialsToBeCompiled=[]}var e=t.prototype;return e.init=function(){for(var t=this._resources,e=new Uint8Array(16),i=new Uint8Array(16),n=new Uint8Array(16),r=new Uint8Array(16),s=new Uint8Array(16),o=0,a=0;a<4;a++)e[o]=0,e[o+1]=0,e[o+2]=0,e[o+3]=255,i[o]=0,i[o+1]=0,i[o+2]=0,i[o+3]=0,n[o]=119,n[o+1]=119,n[o+2]=119,n[o+3]=255,r[o]=255,r[o+1]=255,r[o+2]=255,r[o+3]=255,s[o]=127,s[o+1]=127,s[o+2]=255,s[o+3]=255,o+=4;var u=new Uint8Array(1024);o=0;for(var h=0;h<256;h++)u[o]=221,u[o+1]=221,u[o+2]=221,u[o+3]=255,o+=4;o=0;for(var l=0;l<8;l++){for(var p=0;p<8;p++)u[o]=85,u[o+1]=85,u[o+2]=85,u[o+3]=255,o+=4;o+=32}o+=32;for(var c=0;c<8;c++){for(var f=0;f<8;f++)u[o]=85,u[o+1]=85,u[o+2]=85,u[o+3]=255,o+=4;o+=32}var _={width:2,height:2,_data:e,_compressed:!1,format:je.RGBA8888},d={width:2,height:2,_data:i,_compressed:!1,format:je.RGBA8888},g={width:2,height:2,_data:n,_compressed:!1,format:je.RGBA8888},m={width:2,height:2,_data:r,_compressed:!1,format:je.RGBA8888},v={width:2,height:2,_data:s,_compressed:!1,format:je.RGBA8888},y={width:16,height:16,_data:u,_compressed:!1,format:je.RGBA8888},b=new Ye(_),I=new Yi;I._uuid="black-texture",I.image=b,t[I._uuid]=I;var T=new Ye(d),E=new Yi;E._uuid="empty-texture",E.image=T,t[E._uuid]=E;var D=new Qi;D._uuid="black-cube-texture",D.setMipFilter(qe.NEAREST),D.image={front:new Ye(_),back:new Ye(_),left:new Ye(_),right:new Ye(_),top:new Ye(_),bottom:new Ye(_)},t[D._uuid]=D;var R=new Ye(g),S=new Yi;S._uuid="grey-texture",S.image=R,t[S._uuid]=S;var M=new Qi;M._uuid="grey-cube-texture",M.setMipFilter(qe.NEAREST),M.image={front:new Ye(g),back:new Ye(g),left:new Ye(g),right:new Ye(g),top:new Ye(g),bottom:new Ye(g)},t[M._uuid]=M;var A=new Ye(m),w=new Yi;w._uuid="white-texture",w.image=A,t[w._uuid]=w;var k=new Qi;k._uuid="white-cube-texture",k.setMipFilter(qe.NEAREST),k.image={front:new Ye(m),back:new Ye(m),left:new Ye(m),right:new Ye(m),top:new Ye(m),bottom:new Ye(m)},t[k._uuid]=k;var C=new Ye(v),x=new Yi;x._uuid="normal-texture",x.image=C,t[x._uuid]=x;var P=new Ye(y),L=new Yi;L._uuid="default-texture",L.image=P,t[L._uuid]=L;var O=new Qi;if(O.setMipFilter(qe.NEAREST),O._uuid="default-cube-texture",O.image={front:new Ye(y),back:new Ye(y),left:new Ye(y),right:new Ye(y),top:new Ye(y),bottom:new Ye(y)},t[O._uuid]=O,ot.SpriteFrame){var N=new ot.SpriteFrame,H=b,B=new Yi;B.image=H,N.texture=B,N._uuid="default-spriteframe",t[N._uuid]=N}},e.addAsset=function(t,e){this._resources[t]=e},e.get=function(t){return this._resources[t]},e.loadBuiltinAssets=function(){var t=this,e=y.querySettings(b.ENGINE,"builtinAssets");if(!e)return Promise.resolve();var i=this._resources;return new Promise((function(n,r){bn.loadBundle(jt.INTERNAL,(function(s){s?r(s):bn.loadAny(e,(function(e,s){e?r(e):(s.forEach((function(e){i[e.name]=e,pi.addIgnoredAsset(e),e instanceof ot.Material&&t._materialsToBeCompiled.push(e)})),n())}))}))}))},e.compileBuiltinMaterial=function(){for(var t=0;t<this._materialsToBeCompiled.length;++t)for(var e=this._materialsToBeCompiled[t],i=0;i<e.passes.length;++i)e.passes[i].tryCompile();this._materialsToBeCompiled.length=0},t}()),Mn=t("d",ot.builtinResMgr=new Sn),An=t("g",(In=new Map,Tn=0,function(t){return"number"==typeof t?t:(In.has(t)||(In.set(t,1<<Tn),Tn++),In.get(t))})),wn=4227858432,kn=66060288,Cn=1044480,xn=t("x",(function(t,e,i,n){return void 0===n&&(n=0),e<<26&wn|t<<20&kn|i<<12&Cn|4095&n})),Pn=t("J",(function(t){return(t&wn)>>>26})),Ln=t("y",(function(t){return(t&kn)>>>20})),On=t("z",(function(t){return(t&Cn)>>>12})),Nn=t("H",(function(t){return 4095&t})),Hn=t("w",(function(t,e){return 67108863&t|e<<26&wn})),Bn=t("O",((En={})[he.UNKNOWN]=function(t,e,i){return void 0===i&&(i=0),_(12010,i)},En[he.INT]=function(t,e,i){return void 0===i&&(i=0),t[i]},En[he.INT2]=function(t,e,i){return void 0===i&&(i=0),V.fromArray(e,t,i)},En[he.INT3]=function(t,e,i){return void 0===i&&(i=0),z.fromArray(e,t,i)},En[he.INT4]=function(t,e,i){return void 0===i&&(i=0),W.fromArray(e,t,i)},En[he.FLOAT]=function(t,e,i){return void 0===i&&(i=0),t[i]},En[he.FLOAT2]=function(t,e,i){return void 0===i&&(i=0),V.fromArray(e,t,i)},En[he.FLOAT3]=function(t,e,i){return void 0===i&&(i=0),z.fromArray(e,t,i)},En[he.FLOAT4]=function(t,e,i){return void 0===i&&(i=0),W.fromArray(e,t,i)},En[he.MAT3]=function(t,e,i){return void 0===i&&(i=0),j.fromArray(e,t,i)},En[he.MAT4]=function(t,e,i){return void 0===i&&(i=0),X.fromArray(e,t,i)},En)),Fn=t("R",((Dn={})[he.UNKNOWN]=function(t,e,i){return void 0===i&&(i=0),_(12010,i)},Dn[he.INT]=function(t,e,i){return void 0===i&&(i=0),t[i]=e},Dn[he.INT2]=function(t,e,i){return void 0===i&&(i=0),V.toArray(t,e,i)},Dn[he.INT3]=function(t,e,i){return void 0===i&&(i=0),z.toArray(t,e,i)},Dn[he.INT4]=function(t,e,i){return void 0===i&&(i=0),W.toArray(t,e,i)},Dn[he.FLOAT]=function(t,e,i){return void 0===i&&(i=0),t[i]=e},Dn[he.FLOAT2]=function(t,e,i){return void 0===i&&(i=0),V.toArray(t,e,i)},Dn[he.FLOAT3]=function(t,e,i){return void 0===i&&(i=0),z.toArray(t,e,i)},Dn[he.FLOAT4]=function(t,e,i){return void 0===i&&(i=0),W.toArray(t,e,i)},Dn[he.MAT3]=function(t,e,i){return void 0===i&&(i=0),j.toArray(t,e,i)},Dn[he.MAT4]=function(t,e,i){return void 0===i&&(i=0),X.toArray(t,e,i)},Dn)),Un=(t("Q",((Rn={})[he.INT]=function(t){return"number"==typeof t},Rn[he.FLOAT]=function(t){return"number"==typeof t},Rn[he.INT2]=function(t){return!!(t instanceof V)},Rn[he.FLOAT2]=function(t){return!!(t instanceof V)},Rn[he.INT3]=function(t){return!!(t instanceof z)},Rn[he.FLOAT3]=function(t){return!!(t instanceof z)},Rn[he.INT4]=function(t){return!!(t instanceof W)},Rn[he.FLOAT4]=function(t){return!!(t instanceof W||t instanceof q||t instanceof K)},Rn[he.MAT3]=function(t){return!!(t instanceof j)},Rn[he.MAT4]=function(t){return!!(t instanceof X)},Rn)),[Object.freeze([0]),Object.freeze([0,0]),Object.freeze([0,0,0,0]),Object.freeze([1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1])]);function Gn(t){switch(t){case he.BOOL:case he.INT:case he.UINT:case he.FLOAT:return Un[0];case he.BOOL2:case he.INT2:case he.UINT2:case he.FLOAT2:return Un[1];case he.BOOL4:case he.INT4:case he.UINT4:case he.FLOAT4:return Un[2];case he.MAT4:return Un[3];case he.SAMPLER2D:return"default-texture";case he.SAMPLER_CUBE:return"default-cube-texture";case he.SAMPLER2D_ARRAY:return"default-array-texture";case he.SAMPLER3D:return"default-3d-texture"}return Un[0]}function Vn(t){switch(t){case he.SAMPLER2D:return"-texture";case he.SAMPLER_CUBE:return"-cube-texture";case he.SAMPLER2D_ARRAY:return"-array-texture";case he.SAMPLER3D:return"-3d-texture";default:return"-unknown"}}function zn(t,e){for(var i=Object.entries(e),n=!1,r=0;r<i.length;r++)t[i[r][0]]!==i[r][1]&&(t[i[r][0]]=i[r][1],n=!0);return n}function Wn(t,e){switch(t.type){case"boolean":return"number"==typeof e?e.toString():e?"1":"0";case"string":return void 0!==e?e:t.options[0];case"number":return void 0!==e?e.toString():t.range[0].toString();default:return _(16369),"-1"}}function jn(t,e){for(var i=[],n=0;n<e.length;n++){var r=e[n],s=r.name,o=t[s],a=Wn(r,o),u=!o||"0"===o;i.push({name:s,value:a,isDefault:u})}return i}function Xn(t,e){return t+e.reduce((function(t,e){return e.isDefault?t:t+"|"+e.name+e.value}),"")}function qn(t,e){for(var i=0;i<t.length;i++){var n=t[i];if("!"===n[0]){if(e[n.slice(1)])return!1}else if(!e[n])return!1}return!0}function Kn(t,e,i){for(var n=[],r=t.attributes,s=0;s<r.length;s++)qn(r[s].defines,i)&&n.push(e[s]);return n}function Yn(t,e){var i=t.defines;if(t.uber){for(var n="",r=0;r<i.length;r++){var s=i[r],o=e[s.name];if(o&&s._map){var a=s._map(o);n+=""+s._offset+a+"|"}}return""+n+t.hash}for(var u=0,h=0;h<i.length;h++){var l=i[h],p=e[l.name];p&&l._map&&(u|=l._map(p)<<l._offset)}return u.toString(16)+"|"+t.hash}var Qn=new Map;function Jn(t,e){if(e.count)return t+le(e.type)*e.count;var i=Qn.get(e.name);return void 0!==i?t+le(e.type)*i:(n(16345,e.name),t)}function Zn(t){return t.reduce(Jn,0)}function $n(t){for(var e={},i=0;i<t.blocks.length;i++)for(var n=t.blocks[i],r=n.members,s=0,o=0;o<r.length;o++){var a=r[o];e[a.name]=xn(n.binding,a.type,a.count,s),s+=(le(a.type)>>2)*a.count}for(var u=0;u<t.samplerTextures.length;u++){var h=t.samplerTextures[u];e[h.name]=xn(h.binding,h.type,h.count)}return e}function tr(t){return Math.ceil(Math.log2(Math.max(t,2)))}function er(t){for(var e=0,i=function(){var i=t.defines[n],r=1;if("number"===i.type){var s=i.range;r=tr(s[1]-s[0]+1),i._map=function(t){return t-s[0]}}else"string"===i.type?(r=tr(i.options.length),i._map=function(t){return Math.max(0,i.options.findIndex((function(e){return e===t})))}):"boolean"===i.type&&(i._map=function(t){return t?1:0});i._offset=e,e+=r},n=0;n<t.defines.length;n++)i();for(var r in e>31&&(t.uber=!0),t.constantMacros="",t.builtins.statistics)t.constantMacros+="#define "+r+" "+t.builtins.statistics[r]+"\n"}function ir(t){return Object.keys(t).reduce((function(e,i){return e.reduce((function(e,n){for(var r=t[i],s=0;s<r.length;++s){var o=T({},n);o[i]=r[s],e.push(o)}return e}),[])}),[{}])}function nr(t){for(var e=0;e<t.techniques.length;e++)for(var i=t.techniques[e],n=0;n<i.passes.length;n++){var r=i.passes[n];void 0!==r.propertyIndex&&void 0===r.properties&&(r.properties=i.passes[r.propertyIndex].properties)}}Qn.set("cc_joints",pt.LAYOUT.members[0].count),Qn.set("cc_lightPos",ct.LIGHTS_PER_PASS),Qn.set("cc_lightColor",ct.LIGHTS_PER_PASS),Qn.set("cc_lightSizeRangeAngle",ct.LIGHTS_PER_PASS),Qn.set("cc_lightDir",ct.LIGHTS_PER_PASS),Qn.set("cc_lightBoundingSizeVS",ct.LIGHTS_PER_PASS);var rr=new pe;function sr(t,e,i,n){for(var r=t.builtins[n],s=[],o=function(){var t=r.blocks[a],e=i.layouts[t.name],n=e&&i.bindings.find((function(t){return t.binding===e.binding}));if(!(e&&n&&n.descriptorType&Me))return _(16348,t.name),1;s.push(e)},a=0;a<r.blocks.length;a++)o();Array.prototype.unshift.apply(e.shaderInfo.blocks,s);for(var u=[],h=function(){var t=r.samplerTextures[l],e=i.layouts[t.name],n=e&&i.bindings.find((function(t){return t.binding===e.binding}));if(!(e&&n&&n.descriptorType&Ae))return _(16349,t.name),1;u.push(e)},l=0;l<r.samplerTextures.length;l++)h();Array.prototype.unshift.apply(e.shaderInfo.samplerTextures,u)}var or=function(){function t(){this._templates={},this._cache={},this._templateInfos={}}var e=t.prototype;return e.register=function(t){for(var e=0;e<t.shaders.length;e++)this.define(t.shaders[e]).effectName=t.name;for(var i=0;i<t.techniques.length;i++)for(var n=t.techniques[i],r=0;r<n.passes.length;r++){var s=n.passes[r];void 0!==s.propertyIndex&&void 0===s.properties&&(s.properties=n.passes[s.propertyIndex].properties)}},e.define=function(t){var e=this._templates[t.name];if(e&&e.hash===t.hash)return e;var i=T({},t);if(er(i),this._templates[t.name]=i,!this._templateInfos[i.hash]){var n={};n.samplerStartBinding=i.blocks.length,n.shaderInfo=new ce,n.blockSizes=[],n.bindings=[];for(var r=0;r<i.blocks.length;r++){var s=i.blocks[r];n.blockSizes.push(Zn(s.members)),n.bindings.push(new fe(s.binding,_e.UNIFORM_BUFFER,1,s.stageFlags)),n.shaderInfo.blocks.push(new de(ft.MATERIAL,s.binding,s.name,s.members.map((function(t){return new ge(t.name,t.type,t.count)})),1))}for(var o=0;o<i.samplerTextures.length;o++){var a=i.samplerTextures[o];n.bindings.push(new fe(a.binding,_e.SAMPLER_TEXTURE,a.count,a.stageFlags)),n.shaderInfo.samplerTextures.push(new me(ft.MATERIAL,a.binding,a.name,a.type,a.count))}for(var u=0;u<i.samplers.length;u++){var h=i.samplers[u];n.bindings.push(new fe(h.binding,_e.SAMPLER,h.count,h.stageFlags)),n.shaderInfo.samplers.push(new ve(ft.MATERIAL,h.binding,h.name,h.count))}for(var l=0;l<i.textures.length;l++){var p=i.textures[l];n.bindings.push(new fe(p.binding,_e.TEXTURE,p.count,p.stageFlags)),n.shaderInfo.textures.push(new ye(ft.MATERIAL,p.binding,p.name,p.type,p.count))}for(var c=0;c<i.buffers.length;c++){var f=i.buffers[c];n.bindings.push(new fe(f.binding,_e.STORAGE_BUFFER,1,f.stageFlags)),n.shaderInfo.buffers.push(new be(ft.MATERIAL,f.binding,f.name,1,f.memoryAccess))}for(var _=0;_<i.images.length;_++){var d=i.images[_];n.bindings.push(new fe(d.binding,_e.STORAGE_IMAGE,d.count,d.stageFlags)),n.shaderInfo.images.push(new Ie(ft.MATERIAL,d.binding,d.name,d.type,d.count,d.memoryAccess))}for(var g=0;g<i.subpassInputs.length;g++){var m=i.subpassInputs[g];n.bindings.push(new fe(m.binding,_e.INPUT_ATTACHMENT,m.count,m.stageFlags)),n.shaderInfo.subpassInputs.push(new Te(ft.MATERIAL,m.binding,m.name,m.count))}n.gfxAttributes=[];for(var v=0;v<i.attributes.length;v++){var y=i.attributes[v];n.gfxAttributes.push(new Ee(y.name,y.format,y.isNormalized,0,y.isInstanced,y.location))}sr(i,n,_t,"locals"),n.shaderInfo.stages.push(new De(Re.VERTEX,"")),n.shaderInfo.stages.push(new De(Re.FRAGMENT,"")),n.handleMap=$n(i),n.setLayouts=[],this._templateInfos[i.hash]=n}return i},e.getTemplate=function(t){return this._templates[t]},e.getTemplateInfo=function(t){var e=this._templates[t].hash;return this._templateInfos[e]},e.getDescriptorSetLayout=function(t,e,i){void 0===i&&(i=!1);var n=this._templates[e],r=this._templateInfos[n.hash];return r.setLayouts.length||(rr.bindings=r.bindings,r.setLayouts[ft.MATERIAL]=t.createDescriptorSetLayout(rr),rr.bindings=_t.bindings,r.setLayouts[ft.LOCAL]=t.createDescriptorSetLayout(rr)),r.setLayouts[i?ft.LOCAL:ft.MATERIAL]},e.hasProgram=function(t){return void 0!==this._templates[t]},e.getKey=function(t,e){return Yn(this._templates[t],e)},e.destroyShaderByDefines=function(t){var e=this,i=Object.keys(t);if(i.length)for(var n=i.map((function(e){var i=t[e];return"boolean"==typeof i&&(i=i?"1":"0"),new RegExp(""+e+i)})),r=Object.keys(this._cache).filter((function(t){return n.every((function(i){return i.test(e._cache[t].name)}))})),s=0;s<r.length;s++){var o=r[s],a=this._cache[o];E("destroyed shader "+a.name),a.destroy(),delete this._cache[o]}},e.getGFXShader=function(t,e,i,r,s){Object.assign(i,r.macros),s||(s=this.getKey(e,i));var o=this._cache[s];if(o)return o;var a=this._templates[e],u=this._templateInfos[a.hash];u.pipelineLayout||(this.getDescriptorSetLayout(t,e),sr(a,u,dt,"globals"),u.setLayouts[ft.GLOBAL]=r.descriptorSetLayout,u.pipelineLayout=t.createPipelineLayout(new Se(u.setLayouts)));var h=jn(i,a.defines),l=r.constantMacros+a.constantMacros+h.reduce((function(t,e){return t+"#define "+e.name+" "+e.value+"\n"}),""),p=a.glsl3,c=ar(t);c?p=a[c]:n(16346),u.shaderInfo.stages[0].source=l+p.vert,u.shaderInfo.stages[1].source=l+p.frag,u.shaderInfo.attributes=Kn(a,u.gfxAttributes,i),u.shaderInfo.name=Xn(e,h);var f=u.shaderInfo;return this._cache[s]=t.createShader(f)},t}();function ar(t){switch(t.gfxAPI){case se.GLES2:case se.WEBGL:return"glsl1";case se.GLES3:case se.WEBGL2:return"glsl3";default:return"glsl4"}}var ur=t("L",new or);ot.programLib=ur;var hr,lr=t("U",function(){function t(t){this.instances=[],this.hasPendingModels=!1,this.dynamicOffsets=[],this._device=t.device,this.pass=t}var e=t.prototype;return e.destroy=function(){this.instances.forEach((function(t){t.vb.destroy(),t.ia.destroy()})),this.instances.length=0},e.merge=function(t,e,i){void 0===i&&(i=null);var n=t.instancedAttributeBlock,r=n.buffer.length;if(r){var s=t.inputAssembler,o=t.descriptorSet,a=o.getTexture(gt),u=o.getTexture(mt),h=o.getTexture(vt),l=t.useReflectionProbeType,p=i;p||(p=t.shaders[e]);for(var c=t.descriptorSet,f=0;f<this.instances.length;++f){var _,d,g=this.instances[f];if(!((null==(_=g.ia.indexBuffer)?void 0:_.objectID)!==(null==(d=s.indexBuffer)?void 0:d.objectID)||g.count>=1024)&&g.lightingMap.objectID===a.objectID&&g.useReflectionProbeType===l&&g.reflectionProbeCubemap.objectID===u.objectID&&g.reflectionProbePlanarMap.objectID===h.objectID&&g.stride===r){if(g.count>=g.capacity){g.capacity<<=1;var m=g.stride*g.capacity,v=g.data;g.data=new Uint8Array(m),g.data.set(v),g.vb.resize(m)}return g.shader=p,g.descriptorSet=c,g.data.set(n.buffer,g.stride*g.count++),void(this.hasPendingModels=!0)}}for(var y=this._device.createBuffer(new we(ke.VERTEX|ke.TRANSFER_DST,Ce.HOST|Ce.DEVICE,32*r,r)),b=new Uint8Array(32*r),I=s.vertexBuffers.slice(),T=s.attributes.slice(),E=s.indexBuffer,D=0;D<n.attributes.length;D++){var R=n.attributes[D],S=new Ee(R.name,R.format,R.isNormalized,I.length,!0);T.push(S)}b.set(n.buffer),I.push(y);var M=new xe(T,I,E),A=this._device.createInputAssembler(M);this.instances.push({count:1,capacity:32,vb:y,data:b,ia:A,stride:r,shader:p,descriptorSet:c,lightingMap:a,reflectionProbeCubemap:u,reflectionProbePlanarMap:h,useReflectionProbeType:l,reflectionProbeBlendCubemap:null}),this.hasPendingModels=!0}},e.uploadBuffers=function(t){for(var e=0;e<this.instances.length;++e){var i=this.instances[e];i.count&&(i.ia.instanceCount=i.count,t.updateBuffer(i.vb,i.data))}},e.clear=function(){this.instances.forEach((function(t){t.count=0})),this.hasPendingModels=!1},t}()),pr=new we(ke.UNIFORM|ke.TRANSFER_DST,Ce.DEVICE),cr=new Ue(null),fr=new Ge(null);t("B",hr),function(t){t[t.NONE=0]="NONE",t[t.INSTANCING=1]="INSTANCING"}(hr||t("B",hr={}));var _r,dr,gr,mr,vr,yr,br,Ir=t("P",function(){function t(t){this._rootBuffer=null,this._buffers=[],this._descriptorSet=null,this._pipelineLayout=null,this._passIndex=0,this._propertyIndex=0,this._programName="",this._dynamics={},this._propertyHandleMap={},this._rootBlock=null,this._blocksInt=[],this._blocks=[],this._shaderInfo=null,this._defines={},this._properties={},this._shader=null,this._bs=new Pe,this._dss=new Le,this._rs=new Oe,this._priority=yt.DEFAULT,this._stage=bt.DEFAULT,this._phase=An("default"),this._passID=4294967295,this._subpassID=4294967295,this._phaseID=4294967295,this._primitive=Ne.TRIANGLE_LIST,this._batchingScheme=hr.NONE,this._dynamicStates=He.NONE,this._instancedBuffers={},this._hash=0,this._rootBufferDirty=!1,this._root=t,this._device=Zt.gfxDevice}t.fillPipelineInfo=function(t,e){void 0!==e.priority&&(t._priority=e.priority),void 0!==e.primitive&&(t._primitive=e.primitive),void 0!==e.stage&&(t._stage=e.stage),void 0!==e.dynamicStates&&(t._dynamicStates=e.dynamicStates),void 0!==e.phase&&(t._phase=An(e.phase));var i=t._bs;if(e.blendState){var n=e.blendState,r=n.targets;r&&r.forEach((function(t,e){i.setTarget(e,t)})),void 0!==n.isA2C&&(i.isA2C=n.isA2C),void 0!==n.isIndepend&&(i.isIndepend=n.isIndepend),void 0!==n.blendColor&&(i.blendColor=n.blendColor)}t._rs.assign(e.rasterizerState),t._dss.assign(e.depthStencilState)},t.getPassHash=function(t){var e="";if(ot.rendering&&ot.rendering.enableEffectImport){var i=ot.rendering.programLib.getKey(t._phaseID,t.program,t.defines);e=t._phaseID.toString()+","+i}else e=ur.getKey(t.program,t.defines);var n,r=e+","+t._primitive+","+t._dynamicStates;return r+=Tr(t._bs),r+=Er(t._dss),r+=",rs,"+(n=t._rs).cullMode+","+n.depthBias+","+n.isFrontFaceCCW,ee(r,666)};var e=t.prototype;return e.initialize=function(t){this._doInit(t),this.resetUBOs(),this.resetTextures(),this.tryCompile()},e.getHandle=function(t,e,i){void 0===e&&(e=0),void 0===i&&(i=he.UNKNOWN);var n=this._propertyHandleMap[t];return n?(i?n=Hn(n,i):e&&(n=Hn(n,Pn(n)-e)),n+e):0},e.getBinding=function(t){var e=this.getHandle(t);return e?Ln(e):-1},e.setUniform=function(t,e){var i=Ln(t),n=Pn(t),r=Nn(t),s=this._getBlockView(n,i);Fn[n](s,e,r),this._rootBufferDirty=!0},e.getUniform=function(t,e){var i=Ln(t),n=Pn(t),r=Nn(t),s=this._getBlockView(n,i);return Bn[n](s,e,r)},e.setUniformArray=function(t,e){for(var i=Ln(t),n=Pn(t),r=le(n)>>2,s=this._getBlockView(n,i),o=Nn(t),a=0;a<e.length;a++,o+=r)null!==e[a]&&Fn[n](s,e[a],o);this._rootBufferDirty=!0},e.bindTexture=function(t,e,i){this._descriptorSet.bindTexture(t,e,i||0)},e.bindSampler=function(t,e,i){this._descriptorSet.bindSampler(t,e,i||0)},e.setDynamicState=function(t,e){var i=this._dynamics[t];i&&i.value===e||(i.value=e,i.dirty=!0)},e.overridePipelineStates=function(){_(12102)},e.update=function(){this._descriptorSet?(this._rootBuffer&&this._rootBufferDirty&&(this._rootBuffer.update(this._rootBlock),this._rootBufferDirty=!1),this._descriptorSet.update()):n(12006)},e.getInstancedBuffer=function(t){return void 0===t&&(t=0),this._instancedBuffers[t]||(this._instancedBuffers[t]=new lr(this))},e.destroy=function(){for(var t=0;t<this._shaderInfo.blocks.length;t++){var e=this._shaderInfo.blocks[t];this._buffers[e.binding].destroy()}for(var i in this._buffers=[],this._rootBuffer&&(this._rootBuffer.destroy(),this._rootBuffer=null),this._instancedBuffers)this._instancedBuffers[i].destroy();this._descriptorSet.destroy(),this._rs.destroy(),this._dss.destroy(),this._bs.destroy()},e.resetUniform=function(t){var e=this.getHandle(t);if(e){for(var i=Pn(e),n=Ln(e),r=Nn(e),s=On(e),o=this._getBlockView(i,n),a=this._properties[t],u=a&&a.value||Gn(i),h=(le(i)>>2)*s,l=0;l+u.length<=h;l+=u.length)o.set(u,r+l);this._rootBufferDirty=!0}},e.resetTexture=function(t,e){var i=this.getHandle(t);if(i){var n,r=Pn(i),s=Ln(i),o=this._properties[t],a=o&&o.value,u=(n="string"==typeof a?Mn.get(""+a+Vn(r)):a||Mn.get(Gn(r)))&&n.getGFXTexture(),h=o&&void 0!==o.samplerHash?Be.unpackFromHash(o.samplerHash):n&&n.getSamplerInfo(),l=this._device.getSampler(h);this._descriptorSet.bindSampler(s,l,e||0),this._descriptorSet.bindTexture(s,u,e||0)}},e.resetUBOs=function(){for(var t=0;t<this._shaderInfo.blocks.length;t++)for(var e=this._shaderInfo.blocks[t],i=0,n=0;n<e.members.length;n++){for(var r=e.members[n],s=this._getBlockView(r.type,e.binding),o=this._properties[r.name],a=o&&o.value||Gn(r.type),u=(le(r.type)>>2)*r.count,h=0;h+a.length<=u;h+=a.length)s.set(a,i+h);i+=u}this._rootBufferDirty=!0},e.resetTextures=function(){var t=this;if(ot.rendering)this._shaderInfo.descriptors[ft.MATERIAL].samplerTextures.forEach((function(e){for(var i=0;i<e.count;++i)t.resetTexture(e.name,i)}));else for(var e=0;e<this._shaderInfo.samplerTextures.length;e++)for(var i=this._shaderInfo.samplerTextures[e],n=0;n<i.count;n++)this.resetTexture(i.name,n)},e.tryCompile=function(){var e=this._root.pipeline;if(!e)return!1;if(this._syncBatchingScheme(),ot.rendering&&ot.rendering.enableEffectImport){var i=ot.rendering.programLib,n=i.getProgramVariant(this._device,this._phaseID,this._programName,this._defines);if(!n)return _(12103,this._programName),!1;this._shader=n.shader,this._pipelineLayout=i.getPipelineLayout(this.device,this._phaseID,this._programName)}else{var r=ur.getGFXShader(this._device,this._programName,this._defines,e);if(!r)return _(12104,this._programName),!1;this._shader=r,this._pipelineLayout=ur.getTemplateInfo(this._programName).pipelineLayout}return this._hash=t.getPassHash(this),!0},e.getShaderVariant=function(t){if(void 0===t&&(t=null),!this._shader&&!this.tryCompile())return _(12105),null;if(!t)return this._shader;for(var e=this._root.pipeline,i=0;i<t.length;i++){var n=t[i];this._defines[n.name]=n.value}this._isBlend&&(this._defines.CC_IS_TRANSPARENCY_PASS=1);var r=null;if(ot.rendering&&ot.rendering.enableEffectImport){var s=ot.rendering.programLib.getProgramVariant(this._device,this._phaseID,this._programName,this._defines);s&&(r=s.shader)}else r=ur.getGFXShader(this._device,this._programName,this._defines,e);for(var o=0;o<t.length;o++){var a=t[o];delete this._defines[a.name]}return r},e.beginChangeStatesSilently=function(){},e.endChangeStatesSilently=function(){},e._doInit=function(e,i){var r;void 0===i&&(i=!1),this._priority=yt.DEFAULT,this._stage=bt.DEFAULT;var s=null==(r=ot.rendering)?void 0:r.enableEffectImport;if(s){var o=ot.rendering;if("number"==typeof e.phase?(this._passID=e._passID,this._subpassID=e._subpassID,this._phaseID=e._phaseID):(this._passID=o.getPassID(e.pass),this._passID!==o.INVALID_ID&&(e.subpass?(this._subpassID=o.getSubpassID(this._passID,e.subpass),this._phaseID=o.getPhaseID(this._subpassID,e.phase)):this._phaseID=o.getPhaseID(this._passID,e.phase))),this._passID===o.INVALID_ID)return void n(12107,e.program);if(this._phaseID===o.INVALID_ID)return void n(12108,e.program)}else"number"==typeof e.phase?this._passID=e._passID:e.pass&&"default"!==e.pass&&(u(4294967295===this._passID,12110),this._passID=0);this._phase=An("default"),this._primitive=Ne.TRIANGLE_LIST,this._passIndex=e.passIndex,this._propertyIndex=void 0!==e.propertyIndex?e.propertyIndex:e.passIndex,this._programName=e.program,this._defines=i?T({},e.defines):e.defines,this._shaderInfo=s?ot.rendering.programLib.getProgramInfo(this._phaseID,this._programName):ur.getTemplate(e.program),this._properties=e.properties||this._properties;var a=this._device;t.fillPipelineInfo(this,e),e.stateOverrides&&t.fillPipelineInfo(this,e.stateOverrides),fr.layout=s?ot.rendering.programLib.getMaterialDescriptorSetLayout(this._device,this._phaseID,e.program):ur.getDescriptorSetLayout(this._device,e.program),this._descriptorSet=this._device.createDescriptorSet(fr);var h,l,p=this._shaderInfo.blocks;if(s){var c=ot.rendering.programLib;h=c.getBlockSizes(this._phaseID,this._programName),l=c.getHandleMap(this._phaseID,this._programName)}else{var f=ur.getTemplateInfo(e.program);h=f.blockSizes,l=f.handleMap}if(s){var _=ot.rendering.programLib.getShaderInfo(this._phaseID,this.program);this._buildMaterialUniformBlocks(a,_.blocks,h)}else this._buildUniformBlocks(a,p,h);var d=this._propertyHandleMap=l,g={};for(var m in this._properties){var v=this._properties[m];v.handleInfo&&(g[m]=this.getHandle.apply(this,v.handleInfo))}Object.assign(d,g)},e._buildUniformBlocks=function(t,e,i){for(var n=t.capabilities.uboOffsetAlignment,r=[],s=0,o=0,a=0;a<e.length;a++){var u=i[a];r.push(o),o+=Math.ceil(u/n)*n,s=u}var h=r[r.length-1]+16*Math.ceil(s/16);h&&(pr.size=16*Math.ceil(h/16),this._rootBuffer=t.createBuffer(pr),this._rootBlock=new ArrayBuffer(h));for(var l=0,p=0;l<e.length;l++){var c=e[l].binding,f=i[l];cr.buffer=this._rootBuffer,cr.offset=r[p++],cr.range=16*Math.ceil(f/16);var _=this._buffers[c]=t.createBuffer(cr);this._blocks[c]=new Float32Array(this._rootBlock,cr.offset,f/Float32Array.BYTES_PER_ELEMENT),this._blocksInt[c]=new Int32Array(this._blocks[c].buffer,this._blocks[c].byteOffset,this._blocks[c].length),this._descriptorSet.bindBuffer(c,_)}},e._buildMaterialUniformBlocks=function(t,e,i){for(var n=t.capabilities.uboOffsetAlignment,r=[],s=0,o=0,a=0;a<e.length;a++)if(1===e[a].set){var u=i[a];r.push(o),o+=Math.ceil(u/n)*n,s=u}if(0!==s){var h=r[r.length-1]+s;h&&(pr.size=16*Math.ceil(h/16),this._rootBuffer=t.createBuffer(pr),this._rootBlock=new ArrayBuffer(h))}for(var l=0,p=0;l<e.length;l++)if(1===e[l].set){var c=e[l].binding,f=i[l];cr.buffer=this._rootBuffer,cr.offset=r[p++],cr.range=16*Math.ceil(f/16);var _=this._buffers[c]=t.createBuffer(cr);this._blocks[c]=new Float32Array(this._rootBlock,cr.offset,f/Float32Array.BYTES_PER_ELEMENT),this._blocksInt[c]=new Int32Array(this._blocks[c].buffer,this._blocks[c].byteOffset,this._blocks[c].length),this._descriptorSet.bindBuffer(c,_)}},e._syncBatchingScheme=function(){this._defines.USE_INSTANCING?this._device.hasFeature(Fe.INSTANCED_ARRAYS)?this._batchingScheme=hr.INSTANCING:(this._defines.USE_INSTANCING=!1,this._batchingScheme=hr.NONE):this._batchingScheme=hr.NONE},e._getBlockView=function(t,e){return t<he.FLOAT?this._blocksInt[e]:this._blocks[e]},e._initPassFromTarget=function(t,e,i){this._priority=t.priority,this._stage=t.stage,this._phase=t.phase,this._phaseID=t._phaseID,this._passID=t._passID,this._batchingScheme=t.batchingScheme,this._primitive=t.primitive,this._dynamicStates=t.dynamicStates,this._bs=t.blendState,this._dss=e,this._descriptorSet=t.descriptorSet,this._rs=t.rasterizerState,this._passIndex=t.passIndex,this._propertyIndex=t.propertyIndex,this._programName=t.program,this._defines=t.defines,this._shaderInfo=t._shaderInfo,this._properties=t._properties,this._blocks=t._blocks,this._blocksInt=t._blocksInt,this._dynamics=t._dynamics,this._shader=t._shader,ot.rendering&&ot.rendering.enableEffectImport?this._pipelineLayout=ot.rendering.programLib.getPipelineLayout(this.device,this._phaseID,this._programName):this._pipelineLayout=ur.getTemplateInfo(this._programName).pipelineLayout,this._hash=t._hash^i},e._updatePassHash=function(){this._hash=t.getPassHash(this)},e.setRootBufferDirty=function(t){this._rootBufferDirty=t},e.setPriority=function(t){this._priority=t},r(t,[{key:"_isBlend",get:function(){return this.blendState.targets.some((function(t){return t.blend}))}},{key:"root",get:function(){return this._root}},{key:"device",get:function(){return this._device}},{key:"shaderInfo",get:function(){return this._shaderInfo}},{key:"localSetLayout",get:function(){return ot.rendering&&ot.rendering.enableEffectImport?ot.rendering.programLib.getLocalDescriptorSetLayout(this._device,this._phaseID,this._programName):ur.getDescriptorSetLayout(this._device,this._programName,!0)}},{key:"program",get:function(){return this._programName}},{key:"properties",get:function(){return this._properties}},{key:"defines",get:function(){return this._defines}},{key:"passIndex",get:function(){return this._passIndex}},{key:"propertyIndex",get:function(){return this._propertyIndex}},{key:"dynamics",get:function(){return this._dynamics}},{key:"blocks",get:function(){return this._blocks}},{key:"blocksInt",get:function(){return this._blocksInt}},{key:"rootBufferDirty",get:function(){return this._rootBufferDirty}},{key:"priority",get:function(){return this._priority}},{key:"primitive",get:function(){return this._primitive}},{key:"stage",get:function(){return this._stage}},{key:"phase",get:function(){return this._phase}},{key:"passID",get:function(){return this._passID}},{key:"phaseID",get:function(){return this._phaseID}},{key:"rasterizerState",get:function(){return this._rs}},{key:"depthStencilState",get:function(){return this._dss}},{key:"blendState",get:function(){return this._bs}},{key:"dynamicStates",get:function(){return this._dynamicStates}},{key:"batchingScheme",get:function(){return this._batchingScheme}},{key:"descriptorSet",get:function(){return this._descriptorSet}},{key:"hash",get:function(){return this._hash}},{key:"pipelineLayout",get:function(){return this._pipelineLayout}}]),t}());function Tr(t){var e=",bs,"+t.isA2C;return t.targets.forEach((function(t){e+=",bt,"+t.blend+","+t.blendEq+","+t.blendAlphaEq+","+t.blendColorMask,e+=","+t.blendSrc+","+t.blendDst+","+t.blendSrcAlpha+","+t.blendDstAlpha})),e}function Er(t){var e=",dss,"+t.depthTest+","+t.depthWrite+","+t.depthFunc;return e+=","+t.stencilTestFront+","+t.stencilFuncFront+","+t.stencilRefFront+","+t.stencilReadMaskFront,e+=","+t.stencilFailOpFront+","+t.stencilZFailOpFront+","+t.stencilPassOpFront+","+t.stencilWriteMaskFront,(e+=","+t.stencilTestBack+","+t.stencilFuncBack+","+t.stencilRefBack+","+t.stencilReadMaskBack)+","+t.stencilFailOpBack+","+t.stencilZFailOpBack+","+t.stencilPassOpBack+","+t.stencilWriteMaskBack}Ir.getTypeFromHandle=Pn,Ir.getBindingFromHandle=Ln,Ir.getCountFromHandle=On,Ir.getOffsetFromHandle=Nn;var Dr=["planar-shadow","skybox","deferred-lighting","bloom","hbao","copy-pass","post-process","profiler","splash-screen","unlit","sprite","particle","particle-gpu","particle-trail","billboard","terrain","graphics","clear-stencil","spine","occlusion-query","geometry-renderer","debug-renderer","ssss-blur","float-output-process"],Rr=t("E",B("cc.EffectAsset")((br=function(t){function e(e){var i;return(i=t.call(this,e)||this).techniques=gr&&gr(),i.shaders=mr&&mr(),i.combinations=vr&&vr(),i.hideInEditor=yr&&yr(),i}i(e,t),e.register=function(t){e._effects[t.name]=t,e._layoutValid=!1},e.remove=function(t){if("string"!=typeof t)e._effects[t.name]&&e._effects[t.name]===t&&delete e._effects[t.name];else{if(e._effects[t])return void delete e._effects[t];for(var i in e._effects)if(e._effects[i]._uuid===t)return void delete e._effects[i]}},e.get=function(t){if(e._effects[t])return e._effects[t];for(var i in e._effects)if(e._effects[i]._uuid===t)return e._effects[i];return Dr.includes(t)&&_(16101,t),null},e.getAll=function(){return e._effects},e.isLayoutValid=function(){return e._layoutValid},e.setLayoutValid=function(){e._layoutValid=!0};var n=e.prototype;return n.onLoaded=function(){if(ot.rendering&&ot.rendering.enableEffectImport){nr(this);var t=ot.rendering.programLib;t.addEffect(this),t.init(Zt.gfxDevice)}else ur.register(this);e.register(this),ot.game.once(ot.Game.EVENT_RENDERER_INITED,this._precompile,this)},n._precompile=function(){var t=this;if(ot.rendering&&ot.rendering.enableEffectImport)ot.rendering.programLib.precompileEffect(Zt.gfxDevice,this);else for(var e=ot.director.root,i=function(){var i=t.shaders[n],r=t.combinations[n];if(!r)return 1;ir(r).forEach((function(t){return ur.getGFXShader(Zt.gfxDevice,i.name,t,e.pipeline)}))},n=0;n<this.shaders.length;n++)i()},n.destroy=function(){return e.remove(this),t.prototype.destroy.call(this)},n.initDefault=function(i){t.prototype.initDefault.call(this,i);var n=e.get("builtin-unlit");this.name="builtin-unlit",this.shaders=n.shaders,this.combinations=n.combinations,this.techniques=n.techniques},n.validate=function(){return this.techniques.length>0&&this.shaders.length>0},e}(St),br._effects={},br._layoutValid=!0,gr=F((dr=br).prototype,"techniques",[U],(function(){return[]})),mr=F(dr.prototype,"shaders",[U],(function(){return[]})),vr=F(dr.prototype,"combinations",[U],(function(){return[]})),yr=F(dr.prototype,"hideInEditor",[U,Y],(function(){return!1})),_r=dr))||_r);ot.EffectAsset=Rr;var Sr=new Ve,Mr=new ze;function Ar(t,e){t.x=e.x*e.x,t.y=e.y*e.y,t.z=e.z*e.z}var wr,kr,Cr,xr,Pr,Lr,Or,Nr,Hr,Br=null,Fr=new W,Ur=t("b",(wr=B("cc.Material"),kr=G(Rr),wr((xr=function(t){function e(e){var i;return(i=t.call(this,e)||this)._effectAsset=Pr&&Pr(),i._techIdx=Lr&&Lr(),i._defines=Or&&Or(),i._states=Nr&&Nr(),i._props=Hr&&Hr(),i._passes=[],i._hash=0,i}i(e,t),e.getHash=function(t){for(var e,i=0,n=m(t.passes);!(e=n()).done;)i^=e.value.hash;return i};var n=e.prototype;return n.initialize=function(t){this._passes.length?_(12005):(this._defines||(this._defines=[]),this._states||(this._states=[]),this._props||(this._props=[]),this._fillInfo(t),this._update())},n.reset=function(t){this.initialize(t)},n.destroy=function(){return this._doDestroy(),t.prototype.destroy.call(this)},n.recompileShaders=function(){_(16370,this.name)},n.overridePipelineStates=function(){_(16371,this.name)},n.onLoaded=function(){this._update()},n.resetUniforms=function(t){void 0===t&&(t=!0),this._props.length=this._passes.length;for(var e=0;e<this._props.length;e++)this._props[e]={};if(t)for(var i,n=m(this._passes);!(i=n()).done;){var r=i.value;r.resetUBOs(),r.resetTextures()}},n.setProperty=function(t,e,i){var n=!1;if(void 0===i)for(var r=this._passes,s=r.length,o=0;o<s;o++){var a=r[o];this._uploadProperty(a,t,e)&&(this._props[a.propertyIndex][t]=e,n=!0)}else{i>=this._passes.length&&_(16372,i);var u=this._passes[i];this._uploadProperty(u,t,e)&&(this._props[u.propertyIndex][t]=e,n=!0)}n||_(16373,t)},n.getProperty=function(t,e){if(void 0===e)for(var i=this._props,n=i.length,r=0;r<n;r++){var s=i[r];if(t in s)return s[t]}else{if(e>=this._passes.length)return _(16372,e),null;var o=this._props[this._passes[e].propertyIndex];if(t in o)return o[t]}return null},n.copy=function(t,e){this._techIdx=t._techIdx,this._props.length=t._props.length;for(var i=0;i<t._props.length;i++)this._props[i]=T({},t._props[i]);this._defines.length=t._defines.length;for(var n=0;n<t._defines.length;n++)this._defines[n]=T({},t._defines[n]);this._states.length=t._states.length;for(var r=0;r<t._states.length;r++)this._states[r]=T({},t._states[r]);this._effectAsset=t._effectAsset,e&&this._fillInfo(e),this._update()},n._fillInfo=function(t){void 0!==t.technique&&(this._techIdx=t.technique),t.effectAsset?this._effectAsset=t.effectAsset:t.effectName&&(this._effectAsset=Rr.get(t.effectName)),t.defines&&this._prepareInfo(t.defines,this._defines),t.states&&this._prepareInfo(t.states,this._states)},n._prepareInfo=function(t,e){var i=t;if(!Array.isArray(i)){var n=this._effectAsset?this._effectAsset.techniques[this._techIdx].passes.length:1;i=Array(n).fill(i)}for(var r=0;r<i.length;++r)Object.assign(e[r]||(e[r]={}),i[r])},n._createPasses=function(){var t=this._effectAsset.techniques[this._techIdx||0];if(!t)return[];for(var e=t.passes.length,i=[],n=0;n<e;++n){var r=t.passes[n],s=r.passIndex=n,o=r.defines=this._defines[s]||(this._defines[s]={});if(r.stateOverrides=this._states[s]||(this._states[s]={}),void 0!==r.propertyIndex&&Object.assign(o,this._defines[r.propertyIndex]),void 0!==r.embeddedMacros&&Object.assign(o,r.embeddedMacros),!r.switch||o[r.switch]){var a=new Ir(ot.director.root);a.initialize(r),i.push(a)}}return i},n._update=function(t){var i=this;if(void 0===t&&(t=!0),this._effectAsset){this._passes=this._createPasses();var n=this._effectAsset.techniques[this._techIdx].passes.length;if(this._props.length=n,t)this._passes.forEach((function(t,e){var n=i._props[e];for(var r in n||(n=i._props[e]={}),void 0!==t.propertyIndex&&Object.assign(n,i._props[t.propertyIndex]),n)i._uploadProperty(t,r,n[r])}));else for(var r=0;r<this._props.length;r++)this._props[r]={}}this._hash=e.getHash(this)},n._uploadProperty=function(t,e,i){var n=this,r=t.getHandle(e);if(!r)return!1;if(Pn(r)<he.SAMPLER1D)if(Array.isArray(i))t.setUniformArray(r,i);else if(null!==i){var s;if(null!=(s=t.properties[e])&&s.linear){var o=i;Ar(Fr,o),Fr.w=o.w,i=Fr}t.setUniform(r,i)}else t.resetUniform(e);else Array.isArray(i)?i.forEach((function(e,i){n._bindTexture(t,r,e,i)})):i?this._bindTexture(t,r,i):t.resetTexture(e);return!0},n._bindTexture=function(t,e,i,n){var r=Ir.getBindingFromHandle(e);if(i instanceof We)t.bindTexture(r,i,n);else if(i instanceof wi){var s=i.getGFXTexture();if(!s||!s.width||!s.height)return;t.bindTexture(r,s,n),t.bindSampler(r,i.getGFXSampler(),n)}},n._doDestroy=function(){if(this._passes&&this._passes.length)for(var t,e=m(this._passes);!(t=e()).done;)t.value.destroy();this._passes.length=0},n.initDefault=function(e){t.prototype.initDefault.call(this,e),this.initialize({effectName:"builtin-unlit",defines:{USE_COLOR:!0},technique:0}),this.setProperty("mainColor",new q("#ff00ff"))},n.validate=function(){return!!this._effectAsset&&!this._effectAsset.isDefault&&this.passes.length>0},r(e,[{key:"effectAsset",get:function(){return this._effectAsset}},{key:"effectName",get:function(){return this._effectAsset?this._effectAsset.name:""}},{key:"technique",get:function(){return this._techIdx}},{key:"passes",get:function(){return this._passes}},{key:"hash",get:function(){return this._hash}},{key:"parent",get:function(){return null}},{key:"owner",get:function(){return null}}]),e}(St),Pr=F(xr.prototype,"_effectAsset",[kr],(function(){return null})),Lr=F(xr.prototype,"_techIdx",[U],(function(){return 0})),Or=F(xr.prototype,"_defines",[U],(function(){return[]})),Nr=F(xr.prototype,"_states",[U],(function(){return[]})),Hr=F(xr.prototype,"_props",[U],(function(){return[]})),Cr=xr))||Cr));ot.Material=Ur;var Gr=t("r",D({Low_256x256:256,Medium_512x512:512,High_1024x1024:1024,Ultra_2048x2048:2048})),Vr=t("s",D({Planar:0,ShadowMap:1})),zr=(t("p",D({HARD:0,SOFT:1,SOFT_2X:2,SOFT_4X:3})),t("C",D({LEVEL_1:1,LEVEL_2:2,LEVEL_3:3,LEVEL_4:4})),t("l",D({NONE:1,RemoveDuplicates:2,DisableRotationFix:3})),Vr.ShadowMap+1),Wr=t("S",function(){function t(){this.fixedSphere=new Q(0,0,0,.01),this.maxReceived=4,this._matLight=new X,this._material=null,this._instancingMaterial=null,this._enabled=!1,this._type=zr,this._distance=0,this._planeBias=1,this._normal=new z(0,1,0),this._shadowColor=new q(0,0,0,76),this._size=new V(1024,1024),this._shadowMapDirty=!1}var e=t.prototype;return e.getPlanarShader=function(t){this._material||(this._material=new Ur,this._material.initialize({effectName:"pipeline/planar-shadow"}));var e=this._material.passes;return e.length>0?e[0].getShaderVariant(t):null},e.initialize=function(t){this._enabled=t.enabled,this._type=this.enabled?t.type:zr,this.normal=t.planeDirection,this.distance=t.planeHeight,this.planeBias=t.planeBias,this.shadowColor=t.shadowColor,this.maxReceived=t.maxReceived,t.shadowMapSize!==this._size.x&&(this.size.set(t.shadowMapSize,t.shadowMapSize),this._shadowMapDirty=!0)},e.activate=function(){if(this._enabled)if(this.type===Vr.Planar)this._updatePlanarInfo();else{var t=ot.director.root;t.pipeline.macros.CC_SHADOW_TYPE=2,t.onGlobalPipelineStateChanged()}else{var e=ot.director.root;e.pipeline.macros.CC_SHADOW_TYPE=0,e.onGlobalPipelineStateChanged()}},e._updatePlanarInfo=function(){this._material||(this._material=new Ur,this._material.initialize({effectName:"pipeline/planar-shadow"}));var t=ot.director.root;t.pipeline.macros.CC_SHADOW_TYPE=1,t.onGlobalPipelineStateChanged()},e.destroy=function(){this._material&&this._material.destroy(),this._instancingMaterial&&this._instancingMaterial.destroy(),this.fixedSphere.destroy()},r(t,[{key:"enabled",get:function(){return this._enabled},set:function(t){this._enabled=t,this.activate()}},{key:"type",get:function(){return this._type},set:function(t){this._type=this.enabled?t:zr,this.activate()}},{key:"normal",get:function(){return this._normal},set:function(t){z.copy(this._normal,t)}},{key:"distance",get:function(){return this._distance},set:function(t){this._distance=t}},{key:"planeBias",get:function(){return this._planeBias},set:function(t){this._planeBias=t}},{key:"shadowColor",get:function(){return this._shadowColor},set:function(t){this._shadowColor=t}},{key:"size",get:function(){return this._size},set:function(t){this._size.set(t)}},{key:"shadowMapDirty",get:function(){return this._shadowMapDirty},set:function(t){this._shadowMapDirty=t}},{key:"matLight",get:function(){return this._matLight}},{key:"material",get:function(){return this._material}},{key:"instancingMaterial",get:function(){return this._instancingMaterial}}]),t}());function jr(){return ot.director.root.pipeline.pipelineSceneData}Wr.MAX_FAR=2e3,Wr.COEFFICIENT_OF_EXPANSION=2*Math.sqrt(3),ot.Shadows=Wr;var Xr=t("A",function(){function t(){this._groundAlbedoHDR=new W(.2,.2,.2,1),this._skyColorHDR=new W(.2,.5,.8,1),this._skyIllumHDR=0,this._groundAlbedoLDR=new W(.2,.2,.2,1),this._skyColorLDR=new W(.2,.5,.8,1),this._skyIllumLDR=0,this._mipmapCount=1,this._enabled=!1}return t.prototype.initialize=function(t){this._skyColorHDR=t.skyColorHDR,this._groundAlbedoHDR.set(t.groundAlbedoHDR),this._skyIllumHDR=t.skyIllumHDR,this._skyColorLDR=t.skyColorLDR,this._groundAlbedoLDR.set(t.groundAlbedoLDR),this._skyIllumLDR=t.skyIllumLDR},r(t,[{key:"enabled",get:function(){return this._enabled},set:function(t){this._enabled=t}},{key:"skyColor",get:function(){return jr().isHDR?this._skyColorHDR:this._skyColorLDR},set:function(t){jr().isHDR?this._skyColorHDR.set(t):this._skyColorLDR.set(t)}},{key:"skyIllum",get:function(){return jr().isHDR?this._skyIllumHDR:this._skyIllumLDR},set:function(t){jr().isHDR?this._skyIllumHDR=t:this._skyIllumLDR=t}},{key:"groundAlbedo",get:function(){return jr().isHDR?this._groundAlbedoHDR:this._groundAlbedoLDR},set:function(t){jr().isHDR?this._groundAlbedoHDR.set(t):this._groundAlbedoLDR.set(t)}}]),t}());Xr.SUN_ILLUM=65e3,Xr.SKY_ILLUM=2e4,ot.Ambient=Xr;var qr,Kr=t("v",function(t){function e(e,i){var n;(n=t.call(this,e.root)||this)._dontNotify=!1,n._parent=e,n._owner=i,n._doInit(n._parent,!0),n._shaderInfo.blocks.forEach((function(t){var e=n._blocks[t.binding],i=n._parent.blocks[t.binding];e.set(i)})),n._rootBufferDirty=!0;var r=n._parent,s=n._descriptorSet;return n._shaderInfo.samplerTextures.forEach((function(t){for(var e=0;e<t.count;e++){var i=r._descriptorSet,n=t.binding,o=i.getSampler(n,e),a=i.getTexture(n,e);s.bindSampler(n,o,e),s.bindTexture(n,a,e)}})),t.prototype.tryCompile.call(R(n)),n}i(e,t);var n=e.prototype;return n.overridePipelineStates=function(t,e){this._bs.reset(),this._rs.reset(),this._dss.reset(),Ir.fillPipelineInfo(this,t),Ir.fillPipelineInfo(this,e),this._onStateChange()},n.tryCompile=function(e){if(e&&!zn(this._defines,e))return!1;var i=t.prototype.tryCompile.call(this);return this._onStateChange(),i},n.beginChangeStatesSilently=function(){this._dontNotify=!0},n.endChangeStatesSilently=function(){this._dontNotify=!1},n._syncBatchingScheme=function(){this._defines.USE_INSTANCING=!1,this._batchingScheme=hr.NONE},n._onStateChange=function(){this._hash=Ir.getPassHash(this),this._owner.onPassStateChange(this._dontNotify)},r(e,[{key:"parent",get:function(){return this._parent}}]),e}(Ir)),Yr=t("M",function(t){function e(e){var i;return(i=t.call(this)||this)._passes=[],i._subModelIdx=0,i._parent=e.parent,i._owner=e.owner||null,i._subModelIdx=e.subModelIdx||0,i.copy(i._parent),i}i(e,t);var n=e.prototype;return n.recompileShaders=function(t,e){this._passes&&this.effectAsset&&(void 0===e?this._passes.forEach((function(e){e.tryCompile(t)})):this._passes[e].tryCompile(t))},n.overridePipelineStates=function(t,e){if(this._passes&&this.effectAsset){var i=this.effectAsset.techniques[this.technique].passes;if(void 0===e)for(var n=0;n<this._passes.length;n++){var r=this._passes[n],s=this._states[n]||(this._states[n]={});for(var o in t)s[o]=t[o];r.overridePipelineStates(i[r.passIndex],s)}else{var a=this._states[e]||(this._states[e]={});for(var u in t)a[u]=t[u];this._passes[e].overridePipelineStates(i[e],a)}}},n.destroy=function(){return this._doDestroy(),!0},n.onPassStateChange=function(t){this._hash=Ur.getHash(this),!t&&this._owner&&this._owner._onRebuildPSO(this._subModelIdx,this)},n._createPasses=function(){var t=[],e=this._parent.passes;if(!e)return t;for(var i=0;i<e.length;++i)t.push(new Kr(e[i],this));return t},r(e,[{key:"parent",get:function(){return this._parent}},{key:"owner",get:function(){return this._owner}}]),e}(Ur)),Qr=null,Jr=null;t("m",qr),function(t){t[t.HEMISPHERE_DIFFUSE=0]="HEMISPHERE_DIFFUSE",t[t.AUTOGEN_HEMISPHERE_DIFFUSE_WITH_REFLECTION=1]="AUTOGEN_HEMISPHERE_DIFFUSE_WITH_REFLECTION",t[t.DIFFUSEMAP_WITH_REFLECTION=2]="DIFFUSEMAP_WITH_REFLECTION"}(qr||t("m",qr={})),D(qr);var Zr=t("t",function(){function t(){this._envmapLDR=null,this._envmapHDR=null,this._diffuseMapLDR=null,this._diffuseMapHDR=null,this._globalDSManager=null,this._model=null,this._default=null,this._enabled=!1,this._useIBL=!1,this._useHDR=!0,this._useDiffuseMap=!1,this._editableMaterial=null,this._activated=!1,this._reflectionHDR=null,this._reflectionLDR=null,this._rotationAngle=0}var e=t.prototype;return e.initialize=function(t){this._activated=!1,this._enabled=t.enabled,this._useIBL=t.useIBL,this._useDiffuseMap=t.applyDiffuseMap,this._useHDR=t.useHDR},e.setEnvMaps=function(t,e){this._envmapHDR=t,this._envmapLDR=e,this._updateGlobalBinding(),this._updatePipeline()},e.setDiffuseMaps=function(t,e){this._diffuseMapHDR=t,this._diffuseMapLDR=e,this._updateGlobalBinding(),this._updatePipeline()},e.setSkyboxMaterial=function(t){t?(this._editableMaterial=new Yr({parent:t}),this._editableMaterial.recompileShaders({USE_RGBE_CUBEMAP:this.isRGBE})):this._editableMaterial=null,this._updatePipeline()},e.setReflectionMaps=function(t,e){this._reflectionHDR=t,this._reflectionLDR=e,this._updateGlobalBinding(),this._updatePipeline()},e.setRotationAngle=function(t){this._rotationAngle=t},e.getRotationAngle=function(){return this._rotationAngle},e.updateMaterialRenderInfo=function(){this._updateGlobalBinding(),this._updatePipeline()},e.activate=function(){var t=ot.director.root.pipeline;this._globalDSManager=t.globalDSManager,this._default=Mn.get("default-cube-texture"),this._model||(this._model=ot.director.root.createModel(ot.renderer.scene.Model));var e=this._default.isRGBE;if(this._default.isUsingOfflineMipmaps(),this.envmap&&(e=this.envmap.isRGBE,this.envmap.isUsingOfflineMipmaps()),!Jr){var i=new Ur;i.initialize({effectName:"pipeline/skybox",defines:{USE_RGBE_CUBEMAP:e}}),Jr=new Yr({parent:i})}this.enabled&&(Qr||(Qr=ot.utils.createMesh(ot.primitives.box({width:2,height:2,length:2}))),this._editableMaterial?this._model.initSubModel(0,Qr.renderingSubMeshes[0],this._editableMaterial):this._model.initSubModel(0,Qr.renderingSubMeshes[0],Jr)),this.envmap||(this.envmap=this._default),this.diffuseMap||(this.diffuseMap=this._default),this._updateGlobalBinding(),this._updatePipeline(),this._activated=!0},e._updatePipeline=function(){var t=ot.director.root,e=t.pipeline,i=this.useIBL?this.isRGBE?2:1:0,n=this.useIBL&&this.useDiffuseMap&&this.diffuseMap&&this.diffuseMap!==this._default?this.isRGBE?2:1:0,r=this.useHDR,s=this.useConvolutionMap;if(e.macros.CC_USE_IBL===i&&e.macros.CC_USE_DIFFUSEMAP===n&&e.macros.CC_USE_HDR===r&&e.macros.CC_IBL_CONVOLUTED===s||(e.macros.CC_USE_IBL=i,e.macros.CC_USE_DIFFUSEMAP=n,e.macros.CC_USE_HDR=r,e.macros.CC_IBL_CONVOLUTED=s,this._activated&&t.onGlobalPipelineStateChanged()),this.enabled){var o=this.envmap?this.envmap:this._default,a=this._editableMaterial?this._editableMaterial:Jr;a&&(a.setProperty("environmentMap",o),a.recompileShaders({USE_RGBE_CUBEMAP:this.isRGBE})),this._model&&(this._model.setSubModelMaterial(0,a),this._updateSubModes())}},e._updateGlobalBinding=function(){if(!ot.rendering&&this._globalDSManager){var t=Zt.gfxDevice;if(this.reflectionMap){var e=this.reflectionMap.getGFXTexture(),i=t.getSampler(this.reflectionMap.getSamplerInfo());this._globalDSManager.bindSampler(Et,i),this._globalDSManager.bindTexture(Et,e)}else{var n=this.envmap?this.envmap:this._default;if(n){var r=n.getGFXTexture(),s=t.getSampler(n.getSamplerInfo());this._globalDSManager.bindSampler(Et,s),this._globalDSManager.bindTexture(Et,r)}}var o=this.diffuseMap?this.diffuseMap:this._default;if(o){var a=o.getGFXTexture(),u=t.getSampler(o.getSamplerInfo());this._globalDSManager.bindSampler(Dt,u),this._globalDSManager.bindTexture(Dt,a)}this._globalDSManager.update()}},e._updateSubModes=function(){if(this._model)for(var t=this._model.subModels,e=0;e<t.length;e++)t[e].update()},r(t,[{key:"model",get:function(){return this._model}},{key:"enabled",get:function(){return this._enabled},set:function(t){this._enabled=t,t?this.activate():this._updatePipeline()}},{key:"useHDR",get:function(){return this._useHDR},set:function(t){this._useHDR=t,this.setEnvMaps(this._envmapHDR,this._envmapLDR)}},{key:"useIBL",get:function(){return this._useIBL},set:function(t){this._useIBL=t,this._updatePipeline()}},{key:"useDiffuseMap",get:function(){return this._useDiffuseMap},set:function(t){this._useDiffuseMap=t,this._updatePipeline()}},{key:"isRGBE",get:function(){return!!this.envmap&&this.envmap.isRGBE}},{key:"useConvolutionMap",get:function(){return this.reflectionMap?this.reflectionMap.isUsingOfflineMipmaps():!!this.envmap&&this.envmap.isUsingOfflineMipmaps()}},{key:"envmap",get:function(){return jr().isHDR?this._envmapHDR:this._envmapLDR},set:function(t){jr().isHDR?this.setEnvMaps(t,this._envmapLDR):this.setEnvMaps(this._envmapHDR,t)}},{key:"diffuseMap",get:function(){return jr().isHDR?this._diffuseMapHDR:this._diffuseMapLDR},set:function(t){jr().isHDR?this.setDiffuseMaps(t,this._diffuseMapLDR):this.setDiffuseMaps(this._diffuseMapHDR,t)}},{key:"reflectionMap",get:function(){return jr().isHDR?this._reflectionHDR:this._reflectionLDR}},{key:"editableMaterial",get:function(){return this._editableMaterial}}]),t}());ot.Skybox=Zr;var $r,ts,es=new W,is=t("o",D({LINEAR:0,EXP:1,EXP_SQUARED:2,LAYERED:3})),ns=t("F",is.LAYERED+1),rs=t("n",function(){function t(){this._fogColor=new q("#C8C8C8"),this._colorArray=new W(.2,.2,.2,1),this._enabled=!1,this._accurate=!1,this._type=0,this._fogDensity=.3,this._fogStart=.5,this._fogEnd=300,this._fogAtten=5,this._fogTop=1.5,this._fogRange=1.2,this._activated=!1}var e=t.prototype;return e.initialize=function(t){this._activated=!1,this.fogColor=t.fogColor,this._enabled=t.enabled,this._type=this.enabled?t.type:ns,this._accurate=t.accurate,this.fogDensity=t.fogDensity,this.fogStart=t.fogStart,this.fogEnd=t.fogEnd,this.fogAtten=t.fogAtten,this.fogTop=t.fogTop,this.fogRange=t.fogRange},e.activate=function(){this._updatePipeline(),this._activated=!0},e._updatePipeline=function(){var t=ot.director.root,e=this.enabled?this.type:ns,i=this.accurate?1:0,n=t.pipeline;n.macros.CC_USE_FOG===e&&n.macros.CC_USE_ACCURATE_FOG===i||(n.macros.CC_USE_FOG=e,n.macros.CC_USE_ACCURATE_FOG=i,this._activated&&t.onGlobalPipelineStateChanged())},r(t,[{key:"enabled",get:function(){return this._enabled},set:function(t){this._enabled=t,t?this.activate():(this._type=ns,this._updatePipeline())}},{key:"accurate",get:function(){return this._accurate},set:function(t){this._accurate=t,this._updatePipeline()}},{key:"fogColor",get:function(){return this._fogColor},set:function(t){this._fogColor.set(t),es.set(t.x,t.y,t.z,t.w),Ar(this._colorArray,es)}},{key:"type",get:function(){return this._type},set:function(t){this._type=this.enabled?t:ns,this.enabled&&this._updatePipeline()}},{key:"fogDensity",get:function(){return this._fogDensity},set:function(t){this._fogDensity=t}},{key:"fogStart",get:function(){return this._fogStart},set:function(t){this._fogStart=t}},{key:"fogEnd",get:function(){return this._fogEnd},set:function(t){this._fogEnd=t}},{key:"fogAtten",get:function(){return this._fogAtten},set:function(t){this._fogAtten=t}},{key:"fogTop",get:function(){return this._fogTop},set:function(t){this._fogTop=t}},{key:"fogRange",get:function(){return this._fogRange},set:function(t){this._fogRange=t}},{key:"colorArray",get:function(){return this._colorArray}}]),t}());ot.Fog=rs,t("V",$r),function(t){t[t.LOCAL=0]="LOCAL",t[t.WORLD=1]="WORLD"}($r||t("V",$r={})),t("T",ts),function(t){t[t.NONE=0]="NONE",t[t.POSITION=1]="POSITION",t[t.ROTATION=2]="ROTATION",t[t.SCALE=4]="SCALE",t[t.SKEW=8]="SKEW",t[t.RS=t.ROTATION|t.SCALE]="RS",t[t.RSS=t.ROTATION|t.SCALE|t.SKEW]="RSS",t[t.TRS=t.POSITION|t.ROTATION|t.SCALE]="TRS",t[t.TRS_MASK=~t.TRS]="TRS_MASK"}(ts||t("T",ts={})),at.internal.TransformBit=ts;var ss=t("c",D({Static:0,Stationary:1,Movable:2})),os=t("u",D({DEFAULT:0,LINEAR:1})),as=(t("q",function(){function t(){this._toneMappingType=os.DEFAULT,this._activated=!1}var e=t.prototype;return e.initialize=function(t){this._toneMappingType=t.toneMappingType},e.activate=function(){this._updatePipeline(),this._activated=!0},e._updatePipeline=function(){var t=ot.director.root;t.pipeline.macros.CC_TONE_MAPPING_TYPE=this._toneMappingType,this._activated&&t.onGlobalPipelineStateChanged()},r(t,[{key:"toneMappingType",get:function(){return this._toneMappingType},set:function(t){this._toneMappingType=t,this._updatePipeline()}}]),t}()),t("aa",function(){var t=e.prototype;function e(t){this._uiComp=null,this._opacity=1,this._localOpacity=1,this.colorDirty=!0,this._uiTransformComp=null,this._uiSkewComp=null,this._node=t}return t.setOpacity=function(t){this._opacity=t},t.applyOpacity=function(t){this._opacity=this._localOpacity*t},e.markOpacityTree=function(){},r(e,[{key:"uiTransformComp",get:function(){return this._uiTransformComp||(this._uiTransformComp=this._node.getComponent("cc.UITransform")),this._uiTransformComp},set:function(t){this._uiTransformComp=t}},{key:"uiComp",get:function(){return this._uiComp},set:function(t){this._uiComp&&t?_(12002):this._uiComp=t}},{key:"opacity",get:function(){return this._opacity}},{key:"localOpacity",get:function(){return this._localOpacity},set:function(t){this._localOpacity=t,this.colorDirty=!0}}]),e}()));S.Destroying,at.GAME_VIEW;var us,hs=new M((function(){return new Array(16)}),3),ls=null,ps=new V,cs=[Yt.TOUCH_START,Yt.TOUCH_MOVE,Yt.TOUCH_END,Yt.TOUCH_CANCEL],fs=[Yt.MOUSE_DOWN,Yt.MOUSE_ENTER,Yt.MOUSE_MOVE,Yt.MOUSE_LEAVE,Yt.MOUSE_UP,Yt.MOUSE_WHEEL];t("aq",us),function(t){t[t.ADD_POINTER_EVENT_PROCESSOR=0]="ADD_POINTER_EVENT_PROCESSOR",t[t.REMOVE_POINTER_EVENT_PROCESSOR=1]="REMOVE_POINTER_EVENT_PROCESSOR",t[t.MARK_LIST_DIRTY=2]="MARK_LIST_DIRTY"}(us||t("aq",us={}));var _s=new A,ds=t("ap",function(){function t(t){this.claimedTouchIdList=[],this.maskList=null,this.cachedCameraPriority=0,this.previousMouseIn=!1,this.bubblingTarget=null,this.capturingTarget=null,this.shouldHandleEventMouse=!1,this.shouldHandleEventTouch=!1,this._dispatchingTouch=null,this._isEnabled=!1,this._isMouseLeaveWindow=!1,this._node=t}var e=t.prototype;return e.setEnabled=function(t,e){if(void 0===e&&(e=!1),this._isEnabled!==t){this._isEnabled=t;var i=this.node.children;if(t&&this._attachMask(),_s.emit(us.MARK_LIST_DIRTY),e&&i.length>0)for(var n=0;n<i.length;++n)i[n].eventProcessor.setEnabled(t,!0)}},e.reattach=function(){this.node.walk((function(e){var i=e.eventProcessor,n=i._searchComponentsInParent(t._maskComp);i.maskList=n}))},e.destroy=function(){if(ls===this._node&&(ls=null),this.capturingTarget&&this.capturingTarget.clear(),this.bubblingTarget&&this.bubblingTarget.clear(),_s.emit(us.REMOVE_POINTER_EVENT_PROCESSOR,this),this._dispatchingTouch){var t=new Qt([this._dispatchingTouch],!0,Jt.TOUCH_CANCEL);t.touch=this._dispatchingTouch,this.dispatchEvent(t),this._dispatchingTouch=null}},e.on=function(t,e,i,n){var r,s;return this._tryEmittingAddEvent(t),((n=!!n)?null!==(r=this.capturingTarget)&&void 0!==r?r:this.capturingTarget=this._newCallbacksInvoker():null!==(s=this.bubblingTarget)&&void 0!==s?s:this.bubblingTarget=this._newCallbacksInvoker()).on(t,e,i),e},e.once=function(t,e,i,n){var r,s;return this._tryEmittingAddEvent(t),((n=!!n)?null!==(r=this.capturingTarget)&&void 0!==r?r:this.capturingTarget=this._newCallbacksInvoker():null!==(s=this.bubblingTarget)&&void 0!==s?s:this.bubblingTarget=this._newCallbacksInvoker()).on(t,e,i,!0),e},e.off=function(t,e,i,n){var r;null==(r=(n=!!n)?this.capturingTarget:this.bubblingTarget)||r.off(t,e,i)},e.targetOff=function(t){var e,i;null==(e=this.capturingTarget)||e.removeAll(t),null==(i=this.bubblingTarget)||i.removeAll(t),this.shouldHandleEventTouch&&!this._hasTouchListeners()&&(this.shouldHandleEventTouch=!1),this.shouldHandleEventMouse&&!this._hasMouseListeners()&&(this.shouldHandleEventMouse=!1),this._hasPointerListeners()||_s.emit(us.REMOVE_POINTER_EVENT_PROCESSOR,this)},e.emit=function(t,e,i,n,r,s){var o;null==(o=this.bubblingTarget)||o.emit(t,e,i,n,r,s)},e.dispatchEvent=function(t){var e,i=this.node,n=0;t.target=i;var r=hs.alloc();for(r.length=0,this.getCapturingTargets(t.type,r),t.eventPhase=1,n=r.length-1;n>=0;--n)if((e=r[n]).eventProcessor.capturingTarget&&(t.currentTarget=e,e.eventProcessor.capturingTarget.emit(t.type,t,r),t.propagationStopped))return void hs.free(r);if(t.eventPhase=2,t.currentTarget=i,this.capturingTarget&&this.capturingTarget.emit(t.type,t),!t.propagationImmediateStopped&&this.bubblingTarget&&this.bubblingTarget.emit(t.type,t),!t.propagationStopped&&t.bubbles)for(r.length=0,this.getBubblingTargets(t.type,r),t.eventPhase=3,n=0;n<r.length;++n)if((e=r[n]).eventProcessor.bubblingTarget&&(t.currentTarget=e,e.eventProcessor.bubblingTarget.emit(t.type,t),t.propagationStopped))return void hs.free(r);hs.free(r)},e.hasEventListener=function(t,e,i){var n=!1;return this.bubblingTarget&&(n=this.bubblingTarget.hasEventListener(t,e,i)),!n&&this.capturingTarget&&(n=this.capturingTarget.hasEventListener(t,e,i)),n},e.getCapturingTargets=function(t,e){for(var i=this._node.parent;i;){var n;null!=(n=i.eventProcessor.capturingTarget)&&n.hasEventListener(t)&&e.push(i),i=i.parent}},e.getBubblingTargets=function(t,e){for(var i=this._node.parent;i;){var n;null!=(n=i.eventProcessor.bubblingTarget)&&n.hasEventListener(t)&&e.push(i),i=i.parent}},e.onUpdatingSiblingIndex=function(){_s.emit(us.MARK_LIST_DIRTY)},e._searchComponentsInParent=function(t){var e=this.node;if(t){for(var i=0,n=[],r=e;r&&ot.Node.isNode(r);r=r.parent,++i){var s=r.getComponent(t);if(s){var o={index:i,comp:s};n?n.push(o):n=[o]}}return n.length>0?n:null}return null},e._attachMask=function(){this.maskList=this._searchComponentsInParent(t._maskComp)},e._isTouchEvent=function(t){return-1!==cs.indexOf(t)},e._isMouseEvent=function(t){return-1!==fs.indexOf(t)},e._hasTouchListeners=function(){for(var t=0;t<cs.length;++t){var e=cs[t];if(this.hasEventListener(e))return!0}return!1},e._hasMouseListeners=function(){for(var t=0;t<fs.length;++t){var e=fs[t];if(this.hasEventListener(e))return!0}return!1},e._hasPointerListeners=function(){return!!this._hasTouchListeners()||this._hasMouseListeners()},e._tryEmittingAddEvent=function(t){var e=this._isTouchEvent(t),i=this._isMouseEvent(t);e?this.shouldHandleEventTouch=!0:i&&(this.shouldHandleEventMouse=!0),!e&&!i||this._hasPointerListeners()||_s.emit(us.ADD_POINTER_EVENT_PROCESSOR,this)},e._newCallbacksInvoker=function(){var t=this,e=new A;return e._registerOffCallback((function(){t.shouldHandleEventTouch&&!t._hasTouchListeners()&&(t.shouldHandleEventTouch=!1),t.shouldHandleEventMouse&&!t._hasMouseListeners()&&(t.shouldHandleEventMouse=!1),t._hasPointerListeners()||_s.emit(us.REMOVE_POINTER_EVENT_PROCESSOR,t)})),e},e._handleEventMouse=function(t){switch(t.type){case Jt.MOUSE_DOWN:return this._handleMouseDown(t);case Jt.MOUSE_MOVE:return this._handleMouseMove(t);case Jt.MOUSE_UP:return this._handleMouseUp(t);case Jt.MOUSE_WHEEL:return this._handleMouseWheel(t);case Jt.MOUSE_LEAVE:return this._handleMouseLeave(t);case Jt.MOUSE_ENTER:return this._handleMouseEnter(t);default:return!1}},e._handleMouseDown=function(t){var e=this._node,i=e._getUITransformComp();return!(!e||!i||(t.getLocation(ps),!i.hitTest(ps,t.windowId)||(t.type=Yt.MOUSE_DOWN,t.bubbles=!0,e.dispatchEvent(t),t.propagationStopped=!0,0)))},e._handleMouseMove=function(t){var e=this._node,i=e._getUITransformComp();return!(!e||!i||this._isMouseLeaveWindow||(t.getLocation(ps),i.hitTest(ps,t.windowId)?(this.previousMouseIn||(ls&&ls!==e&&(t.type=Yt.MOUSE_LEAVE,ls.dispatchEvent(t),ls.eventProcessor.previousMouseIn=!1),ls=e,t.type=Yt.MOUSE_ENTER,e.dispatchEvent(t),this.previousMouseIn=!0),t.type=Yt.MOUSE_MOVE,t.bubbles=!0,e.dispatchEvent(t),t.propagationStopped=!0,0):(this.previousMouseIn&&(t.type=Yt.MOUSE_LEAVE,e.dispatchEvent(t),this.previousMouseIn=!1,ls=null),1)))},e._handleMouseUp=function(t){var e=this._node,i=e._getUITransformComp();return!(!e||!i||(t.getLocation(ps),!i.hitTest(ps,t.windowId)||(t.type=Yt.MOUSE_UP,t.bubbles=!0,e.dispatchEvent(t),t.propagationStopped=!0,0)))},e._handleMouseWheel=function(t){var e=this._node,i=e._getUITransformComp();return!(!e||!i||(t.getLocation(ps),!i.hitTest(ps,t.windowId)||(t.type=Yt.MOUSE_WHEEL,t.bubbles=!0,e.dispatchEvent(t),t.propagationStopped=!0,0)))},e._handleMouseLeave=function(t){return this._isMouseLeaveWindow=!0,this.previousMouseIn&&(t.type=Yt.MOUSE_LEAVE,this._node.dispatchEvent(t),this.previousMouseIn=!1,ls=null),!1},e._handleMouseEnter=function(){return this._isMouseLeaveWindow=!1,!1},e._handleEventTouch=function(t){try{switch(t.type){case Jt.TOUCH_START:return this._handleTouchStart(t);case Jt.TOUCH_MOVE:return this._handleTouchMove(t);case Jt.TOUCH_END:return this._handleTouchEnd(t);case Jt.TOUCH_CANCEL:return this._handleTouchCancel(t);default:return!1}}catch(t){throw this.claimedTouchIdList.length=0,t}},e._handleTouchStart=function(t){var e=this.node,i=e._getUITransformComp();return!(!e||!i||(t.getLocation(ps),!i.hitTest(ps,t.windowId)||(t.type=Yt.TOUCH_START,t.bubbles=!0,this._dispatchingTouch=t.touch,e.dispatchEvent(t),0)))},e._handleTouchMove=function(t){var e=this.node;return!(!e||!e._getUITransformComp()||(t.type=Yt.TOUCH_MOVE,t.bubbles=!0,this._dispatchingTouch=t.touch,e.dispatchEvent(t),0))},e._handleTouchEnd=function(t){var e=this.node,i=e._getUITransformComp();e&&i&&(t.getLocation(ps),i.hitTest(ps,t.windowId)?t.type=Yt.TOUCH_END:t.type=Yt.TOUCH_CANCEL,t.bubbles=!0,e.dispatchEvent(t),this._dispatchingTouch=null)},e._handleTouchCancel=function(t){var e=this.node;e&&e._getUITransformComp()&&(t.type=Yt.TOUCH_CANCEL,t.bubbles=!0,e.dispatchEvent(t),this._dispatchingTouch=null)},r(t,[{key:"isEnabled",get:function(){return this._isEnabled}},{key:"node",get:function(){return this._node}}]),t}());ds._maskComp=null,ds.callbacksInvoker=_s,ot.NodeEventProcessor=ds;var gs,ms,vs,ys,bs,Is,Ts,Es,Ds,Rs,Ss,Ms,As,ws,ks,Cs,xs,Ps=J(),Ls=[],Os=Math.PI/180;function Ns(t,e){if(!t)return!1;Ls.length=0;for(var i=Ls,n=null,r=t;r;r=r.parent)i.push(r),r._uiProps._uiSkewComp&&(n=r);var s=!1;if(n){e.set(n.parent._mat);for(var o=i.indexOf(n);o>=0;--o){var a=i[o];X.fromSRT(Ps,a.rotation,a.position,a.scale),X.multiply(e,e,Ps)}s=!0}else e.set(t._mat);return Ls.length=0,s}function Hs(t,e){if(t.isSkewEnabled()&&(0!==t.x||0!==t.y))if(t.rotational){var i=-t.x*Os,n=t.y*Os,r=Math.cos(i),s=Math.sin(i),o=Math.cos(n),a=Math.sin(n),u=e.m00,h=e.m01,l=e.m04,p=e.m05;e.m00=o*u-s*h,e.m01=a*u+r*h,e.m04=o*l-s*p,e.m05=a*l+r*p}else{var c=Math.tan(t.x*Os),f=Math.tan(t.y*Os),_=e.m00,d=e.m01,g=e.m04,m=e.m05;e.m00=_+g*f,e.m01=d+m*f,e.m04=g+_*c,e.m05=m+d*c}}var Bs=S.Destroying,Fs=S.DontDestroy,Us=S.Deactivating,Gs=Yt.TRANSFORM_CHANGED,Vs=Yt.ACTIVE_CHANGED,zs=t("a",1),Ws=new s("Node");function js(t){return t?"string"==typeof t?k(t):t:(n(3804),null)}var Xs,qs,Ks,Ys,Qs,Js,Zs,$s,to,eo,io,no,ro,so,oo,ao,uo,ho,lo,po,co,fo,_o,go,mo,vo,yo,bo,Io,To,Eo,Do,Ro,So,Mo,Ao,wo,ko,Co,xo,Po,Lo,Oo,No,Ho,Bo,Fo,Uo,Go,Vo,zo,Wo,jo,Xo,qo,Ko,Yo,Qo,Jo,Zo,$o,ta,ea,ia,na,ra,sa,oa,aa,ua,ha,la,pa,ca,fa,_a,da,ga,ma,va,ya,ba,Ia,Ta,Ea,Da,Ra,Sa,Ma,Aa,wa,ka,Ca,xa,Pa,La,Oa,Na,Ha,Ba,Fa,Ua,Ga,Va,za,Wa,ja,Xa,qa,Ka,Ya,Qa,Ja,Za,$a,tu,eu,iu,nu,ru,su,ou,au,uu,hu,lu,pu,cu,fu,_u,du=Z(),gu=Z(),mu=$(),vu=$(),yu=$(),bu=new j,Iu=J(),Tu=J(),Eu=[],Du=Symbol("ReserveContentsForAllSyncablePrefab"),Ru=0,Su=0,Mu=t("N",(gs=B("cc.Node"),ms=G(z),vs=G(ss),gs((xs=function(t){i(s,t);var e=s.prototype;function s(e){var i;return void 0===e&&(e="New Node"),(i=t.call(this,e)||this)._parent=Is&&Is(),i._children=Ts&&Ts(),i._active=Es&&Es(),i._components=Ds&&Ds(),i._prefab=Rs&&Rs(),i._scene=null,i._activeInHierarchy=!1,i._id=Ws.getNewId(),i._eventProcessor=new ds(R(i)),i._eventMask=0,i._siblingIndex=0,i._originalSceneId="",i._uiProps=new as(R(i)),i._static=!1,i._lpos=Ss&&Ss(),i._lrot=Ms&&Ms(),i._lscale=As&&As(),i._mobility=ws&&ws(),i._layer=ks&&ks(),i._euler=Cs&&Cs(),i._transformFlags=ts.TRS|ts.SKEW,i._eulerDirty=!1,i._flagChangeVersion=0,i._hasChangedFlags=0,i._pos=new z,i._rot=new K,i._scale=new z(1,1,1),i._mat=new X,i}return e._setActiveInHierarchy=function(t){this._activeInHierarchy=t},s._setScene=function(t){t._updateScene()},s._incSkewCompCount=function(){++Su},s._decSkewCompCount=function(){--Su},s._findComponent=function(t,e){var i=e,n=t._components;if(i._sealed)for(var r=0;r<n.length;++r){var s=n[r];if(s.constructor===e)return s}else for(var o=0;o<n.length;++o){var a=n[o];if(a instanceof e)return a}return null},s._findComponents=function(t,e,i){var n=e,r=t._components;if(n._sealed)for(var s=0;s<r.length;++s){var o=r[s];o.constructor===e&&i.push(o)}else for(var a=0;a<r.length;++a){var u=r[a];u instanceof e&&i.push(u)}},s._findChildComponent=function(t,e){for(var i=0;i<t.length;++i){var n=t[i],r=s._findComponent(n,e);if(r)return r;if(n._children.length>0&&(r=s._findChildComponent(n._children,e)))return r}return null},s._findChildComponents=function(t,e,i){for(var n=0;n<t.length;++n){var r=t[n];s._findComponents(r,e,i),r._children.length>0&&s._findChildComponents(r._children,e,i)}},e.getWritableComponents=function(){return this._components},e._updateScene=function(){null==this._parent?n(1640,this.name,this.uuid):this._scene=this._parent._scene},e.attr=function(t){p(this,t)},e.getParent=function(){return this._parent},e.modifyParent=function(t){this._parent=t},e.setParent=function(t,e){if(void 0===e&&(e=!1),e&&this.updateWorldTransform(),this._parent!==t){var i=this._parent,n=t;if(this._parent=n,this._siblingIndex=0,this._onSetParent(i,e),this.emit&&this.emit(Yt.PARENT_CHANGED,i),i&&!(i._objFlags&Bs)){var r=i._children.indexOf(this);i._children.splice(r,1),i._updateSiblingIndex(),i.emit&&i.emit(Yt.CHILD_REMOVED,this)}n&&(n._children.push(this),this._siblingIndex=n._children.length-1,n.emit&&n.emit(Yt.CHILD_ADDED,this)),this._onHierarchyChanged(i)}},e.getChildByUuid=function(t){if(!t)return w("Invalid uuid"),null;for(var e=this._children,i=0,n=e.length;i<n;i++)if(e[i]._id===t)return e[i];return null},e.getChildByName=function(t){if(!t)return w("Invalid name"),null;for(var e=this._children,i=0,n=e.length;i<n;i++)if(e[i]._name===t)return e[i];return null},e.getChildByPath=function(t){for(var e,i=t.split("/"),n=this,r=function(){var t=i[s];if(0===t.length)return 0;var e=n.children.find((function(e){return e.name===t}));if(!e)return{v:null};n=e},s=0;s<i.length;++s)if(0!==(e=r())&&e)return e.v;return n},e.addChild=function(t){t.setParent(this)},e.insertChild=function(t,e){t.setParent(this),t.setSiblingIndex(e)},e.getSiblingIndex=function(){return this._siblingIndex},e.setSiblingIndex=function(t){if(this._parent)if(this._parent._objFlags&Us)n(3821);else{var e=this._parent._children;t=t>=0?t:e.length+t;var i=e.indexOf(this);t!==i&&(e.splice(i,1),t<e.length?e.splice(t,0,this):e.push(this),this._parent._updateSiblingIndex(),this._onSiblingIndexChanged&&this._onSiblingIndexChanged(t),this._eventProcessor.onUpdatingSiblingIndex())}},e.walk=function(t,e){var i=1,n=null,r=null,o=0,a=s._stacks[s._stackId];a||(a=[],s._stacks.push(a)),s._stackId++,a.length=0,a[0]=this;for(var u=null,h=!1;i;)if(r=a[--i])if(!h&&t?t(r):h&&e&&e(r),a[i]=null,h){if(u===this._parent)break;if(h=!1,n)if(n[++o])a[i]=n[o],i++;else if(u&&(a[i]=u,i++,h=!0,u._parent?(o=(n=u._parent._children).indexOf(u),u=u._parent):(u=null,n=null),o<0))break}else r._children.length>0?(u=r,n=r._children,o=0,a[i]=n[o],i++):(a[i]=r,i++,h=!0);a.length=0,s._stackId--},e.removeFromParent=function(){this._parent&&this._parent.removeChild(this)},e.removeChild=function(t){this._children.indexOf(t)>-1&&(t.parent=null)},e.removeAllChildren=function(){for(var t=this._children,e=t.length-1;e>=0;e--){var i=t[e];i&&(i.parent=null)}this._children.length=0},e.isChildOf=function(t){var e=this;do{if(e===t)return!0;e=e._parent}while(e);return!1},e.getComponent=function(t){var e=js(t);return e?s._findComponent(this,e):null},e.getComponents=function(t){var e=js(t),i=[];return e&&s._findComponents(this,e,i),i},e.getComponentInChildren=function(t){var e=js(t);return e?s._findChildComponent(this._children,e):null},e.getComponentsInChildren=function(t){var e=js(t),i=[];return e&&(s._findComponents(this,e,i),s._findChildComponents(this._children,e,i)),i},e.addComponent=function(t){var e;if("string"==typeof t){if(!(e=k(t)))throw ot._RF.peek()&&n(3808,t),TypeError(g(3807,t))}else{if(!t)throw TypeError(g(3804));e=t}if("function"!=typeof e)throw TypeError(g(3809));if(!C(e,ot.Component))throw TypeError(g(3810));var i=e._requireComponent;if(i)if(Array.isArray(i))for(var r=0;r<i.length;r++){var s=i[r];this.getComponent(s)||this.addComponent(s)}else{var o=i;this.getComponent(o)||this.addComponent(o)}var a=new e;return a.node=this,this._components.push(a),this.emit(Yt.COMPONENT_ADDED,a),this._activeInHierarchy&&ot.director._nodeActivator.activateComp(a),a},e.removeComponent=function(t){if(t){var e=null;(e=t instanceof Kt?t:this.getComponent(t))&&e.destroy()}else n(3813)},e.on=function(t,e,i,n){switch(void 0===n&&(n=!1),t){case Gs:this._eventMask|=zs;break;case Vs:this._eventMask|=2}this._eventProcessor.on(t,e,i,n)},e.off=function(t,e,i,n){if(void 0===n&&(n=!1),this._eventProcessor.off(t,e,i,n),!this._eventProcessor.hasEventListener(t))switch(t){case Gs:this._eventMask&=-2;break;case Vs:this._eventMask&=-3}},e.once=function(t,e,i,n){this._eventProcessor.once(t,e,i,n)},e.emit=function(t,e,i,n,r,s){this._eventProcessor.emit(t,e,i,n,r,s)},e.dispatchEvent=function(t){this._eventProcessor.dispatchEvent(t)},e.hasEventListener=function(t,e,i){return this._eventProcessor.hasEventListener(t,e,i)},e.targetOff=function(t){this._eventProcessor.targetOff(t),this._eventMask&zs&&!this._eventProcessor.hasEventListener(Gs)&&(this._eventMask&=-2),2&this._eventMask&&!this._eventProcessor.hasEventListener(Vs)&&(this._eventMask&=-3)},e.destroy=function(){return!!t.prototype.destroy.call(this)&&(this.active=!1,!0)},e.destroyAllChildren=function(){for(var t=this._children,e=0;e<t.length;++e)t[e].destroy()},e._removeComponent=function(t){if(t){if(!(this._objFlags&Bs)){var e=this._components.indexOf(t);-1!==e?(this._components.splice(e,1),this.emit(Yt.COMPONENT_REMOVED,t)):t.node!==this&&n(3815)}}else n(3814)},e._updateSiblingIndex=function(){for(var t=0;t<this._children.length;++t)this._children[t]._siblingIndex=t;this.emit(Yt.CHILDREN_ORDER_CHANGED)},e._instantiate=function(t,e){return void 0===e&&(e=!1),t||(t=ot.instantiate._clone(this,this)),t._prefab,t._parent=null,t._onBatchCreated(e),t},e._onHierarchyChangedBase=function(){var t=this._parent;!this._persistNode||t instanceof ot.Scene||ot.game.removePersistRootNode(this);var e=this._active&&!(!t||!t._activeInHierarchy);this._activeInHierarchy!==e&&ot.director._nodeActivator.activateNode(this,e)},e._onPreDestroyBase=function(){this._objFlags|=Bs;var t=this._parent,e=!!t&&!!(t._objFlags&Bs);if(this._persistNode&&ot.game.removePersistRootNode(this),!e&&t){this.emit(Yt.PARENT_CHANGED,this);var i=t._children.indexOf(this);t._children.splice(i,1),this._siblingIndex=0,t._updateSiblingIndex(),t.emit&&t.emit(Yt.CHILD_REMOVED,this)}this.emit(Yt.NODE_DESTROYED,this),this._eventProcessor.destroy();for(var n=this._children,r=0;r<n.length;++r)n[r]._destroyImmediate();for(var s=this._components,o=0;o<s.length;++o)s[o]._destroyImmediate();return e},s.isNode=function(t){return t instanceof s&&(t.constructor===s||!(t instanceof ot.Scene))},e._onPreDestroy=function(){return this._onPreDestroyBase()},e[tt]=function(t){t.writeThis()},e._onSetParent=function(t,e){void 0===e&&(e=!1);var i=this,n=i._parent;if(n&&(null!=t&&t._scene===n._scene||null==n._scene||i.walk(s._setScene)),e){if(n)if(n.updateWorldTransform(),et(X.determinant(n._mat),0,nt))_(14300),i._transformFlags|=ts.TRS,i.updateWorldTransform();else{var r=n._mat;if(Su>0){if(t){var o=Ns(t,Tu);X.fromSRT(Iu,i._lrot,i._lpos,i._lscale);var a=o?Tu:t._mat;X.multiply(i._mat,a,Iu)}Ns(n,Tu)&&(r=Tu)}X.multiply(Iu,X.invert(Iu,r),i._mat),X.toSRT(Iu,i._lrot,i._lpos,i._lscale)}else z.copy(i._lpos,i._pos),K.copy(i._lrot,i._rot),z.copy(i._lscale,i._scale);i._eulerDirty=!0}i.invalidateChildren(ts.TRS)},e._onHierarchyChanged=function(t){this.eventProcessor.reattach(),this._onHierarchyChangedBase(t)},e._onBatchCreated=function(t){2&this._eventMask&&(this._activeInHierarchy||this.emit(Vs,this,!1)),this.hasChangedFlags=ts.TRS,this._children.forEach((function(e,i){e._siblingIndex=i,e._onBatchCreated(t)}))},e._onBeforeSerialize=function(){this.eulerAngles},e._onPostActivated=function(t){var e=this;2&e._eventMask&&e.emit(Vs,e,t);var i=this._eventProcessor;if(i.isEnabled===t&&ds.callbacksInvoker.emit(us.MARK_LIST_DIRTY),i.setEnabled(t),t){e.invalidateChildren(ts.TRS);var n=e._uiProps&&e._uiProps.uiComp;n&&(n.setNodeDirty(),n.setTextureDirty(),n._markForUpdateRenderData())}},e.translate=function(t,e){var i=e||$r.LOCAL;if(i===$r.LOCAL)z.transformQuat(du,t,this._lrot),this._lpos.x+=du.x,this._lpos.y+=du.y,this._lpos.z+=du.z;else if(i===$r.WORLD)if(this._parent){K.invert(mu,this._parent.worldRotation),z.transformQuat(du,t,mu);var n=this.worldScale;this._lpos.x+=du.x/n.x,this._lpos.y+=du.y/n.y,this._lpos.z+=du.z/n.z}else this._lpos.x+=t.x,this._lpos.y+=t.y,this._lpos.z+=t.z;this.invalidateChildren(ts.POSITION),this._eventMask&zs&&this.emit(Gs,ts.POSITION)},e.rotate=function(t,e){var i=e||$r.LOCAL;if(K.normalize(mu,t),i===$r.LOCAL)K.multiply(this._lrot,this._lrot,mu);else if(i===$r.WORLD){var n=this.worldRotation;K.multiply(vu,mu,n),K.invert(mu,n),K.multiply(vu,mu,vu),K.multiply(this._lrot,this._lrot,vu)}this._eulerDirty=!0,this.invalidateChildren(ts.ROTATION),this._eventMask&zs&&this.emit(Gs,ts.ROTATION)},e.lookAt=function(t,e){this.getWorldPosition(du),z.subtract(du,du,t),z.normalize(du,du),K.fromViewUp(mu,du,e),this.setWorldRotation(mu)},e.invalidateChildren=function(t){var e,i,n=0,r=0,s=0,o=0,a=t|ts.POSITION;for(Eu[0]=this;n>=0;){if(o=(e=Eu[n--]).hasChangedFlags,e.isValid&&(e._transformFlags&o&t)!==t)for(e._transformFlags|=t,e.hasChangedFlags=o|t,s=(i=e._children).length,r=0;r<s;r++)Eu[++n]=i[r];t=a}},e.updateWorldTransform=function(){if(this._transformFlags){for(var t,e,i,n=this,r=0;n&&n._transformFlags;)Eu[r++]=n,n=n._parent;for(var s=0,o=0,a=0,u=null,h=!1;r;){if(e=(t=Eu[--r])._mat,i=t._pos,o=(s|=t._transformFlags)&ts.POSITION,a=s&ts.RSS,n){if(o&&!a&&(z.transformMat4(i,t._lpos,n._mat),e.m12=i.x,e.m13=i.y,e.m14=i.z),a){var l=e;X.fromSRT(Iu,t._lrot,t._lpos,t._lscale),Su>0&&(h=Ns(n,Tu),((u=t._uiProps._uiSkewComp)||h)&&(X.multiply(Tu,Tu,Iu),u&&Hs(u,Iu),l=Tu)),X.multiply(e,n._mat,Iu);var p=s&ts.ROTATION?t._rot:null;X.toSRT(l,p,i,t._scale),h&&z.transformMat4(i,t._lpos,n._mat)}}else o&&(z.copy(i,t._lpos),e.m12=i.x,e.m13=i.y,e.m14=i.z),a&&(s&ts.ROTATION&&K.copy(t._rot,t._lrot),s&ts.SCALE&&z.copy(t._scale,t._lscale),X.fromSRT(e,t._rot,t._pos,t._scale),Su>0&&(u=t._uiProps._uiSkewComp)&&Hs(u,e));t._transformFlags=ts.NONE,n=t}}},e.setPosition=function(t,e,i){var n=this._lpos;void 0===e?z.copy(n,t):(void 0===i&&(i=n.z),z.set(n,t,e,i)),this.invalidateChildren(ts.POSITION),this._eventMask&zs&&this.emit(Gs,ts.POSITION)},e.getPosition=function(t){return t?z.set(t,this._lpos.x,this._lpos.y,this._lpos.z):z.copy(new z,this._lpos)},e.setRotation=function(t,e,i,n){void 0===e?K.copy(this._lrot,t):K.set(this._lrot,t,e,i,n),this._eulerDirty=!0,this.invalidateChildren(ts.ROTATION),this._eventMask&zs&&this.emit(Gs,ts.ROTATION)},e.setRotationFromEuler=function(t,e,i){if(void 0===e)z.copy(this._euler,t),K.fromEuler(this._lrot,t.x,t.y,t.z);else{var n=void 0===i?this._euler.z:i;z.set(this._euler,t,e,n),K.fromEuler(this._lrot,t,e,n)}this._eulerDirty=!1,this.invalidateChildren(ts.ROTATION),this._eventMask&zs&&this.emit(Gs,ts.ROTATION)},e.getRotation=function(t){return t?K.set(t,this._lrot.x,this._lrot.y,this._lrot.z,this._lrot.w):K.copy(new K,this._lrot)},e.setScale=function(t,e,i){var n=this._lscale;void 0===e?z.copy(n,t):(void 0===i&&(i=n.z),z.set(n,t,e,i)),this.invalidateChildren(ts.SCALE),this._eventMask&zs&&this.emit(Gs,ts.SCALE)},e.getScale=function(t){return t?z.set(t,this._lscale.x,this._lscale.y,this._lscale.z):z.copy(new z,this._lscale)},e.inverseTransformPoint=function(t,e){z.copy(t,e);for(var i=this,n=0;i._parent;)Eu[n++]=i,i=i._parent;for(;n>=0;)z.transformInverseRTS(t,t,i._lrot,i._lpos,i._lscale),i=Eu[--n];return t},e.setWorldPosition=function(t,e,i){var n=this._pos;void 0===e?z.copy(n,t):z.set(n,t,e,i);var r=this._parent,s=this._lpos;r?(r.updateWorldTransform(),z.transformMat4(s,n,X.invert(Iu,r._mat))):z.copy(s,n),this.invalidateChildren(ts.POSITION),this._eventMask&zs&&this.emit(Gs,ts.POSITION)},e.getWorldPosition=function(t){return this.updateWorldTransform(),t?z.copy(t,this._pos):z.copy(new z,this._pos)},e.setWorldRotation=function(t,e,i,n){var r=this._rot;void 0===e?K.copy(r,t):K.set(r,t,e,i,n),this._parent?(this._parent.updateWorldTransform(),K.multiply(this._lrot,K.conjugate(this._lrot,this._parent._rot),r)):K.copy(this._lrot,r),this._eulerDirty=!0,this.invalidateChildren(ts.ROTATION),this._eventMask&zs&&this.emit(Gs,ts.ROTATION)},e.setWorldRotationFromEuler=function(t,e,i){K.fromEuler(mu,t,e,i),this.setWorldRotation(mu)},e.getWorldRotation=function(t){return this.updateWorldTransform(),t?K.copy(t,this._rot):K.copy(new K,this._rot)},e.setWorldScale=function(t,e,i){var n=this,r=n._parent;r&&n.updateWorldTransform();var s=n._scale;void 0===e?z.copy(s,t):z.set(s,t,e,i);var o=ts.NONE;if(r){var a=n._mat;n._uiProps._uiSkewComp&&(X.fromSRT(Iu,n._lrot,n._lpos,n._lscale),X.multiply(a,r._mat,Iu));var u=z.set(gu,a.m00,a.m01,a.m02).length(),h=z.set(gu,a.m04,a.m05,a.m06).length(),l=z.set(gu,a.m08,a.m09,a.m10).length();0===u?(du.x=s.x,a.m00=1,o=ts.ROTATION):du.x=s.x/u,0===h?(du.y=s.y,a.m05=1,o=ts.ROTATION):du.y=s.y/h,0===l?(du.z=s.z,a.m10=1,o=ts.ROTATION):du.z=s.z/l,X.scale(Iu,a,du),X.multiply(Tu,X.invert(Tu,r._mat),Iu),j.fromQuat(bu,K.conjugate(yu,n._lrot)),j.multiplyMat4(bu,bu,Tu);var p=n._lscale;p.x=z.set(du,bu.m00,bu.m01,bu.m02).length(),p.y=z.set(du,bu.m03,bu.m04,bu.m05).length(),p.z=z.set(du,bu.m06,bu.m07,bu.m08).length(),0!==p.x&&0!==p.y&&0!==p.z||(o=ts.ROTATION)}else z.copy(n._lscale,s);n.invalidateChildren(ts.SCALE|o),n._eventMask&zs&&n.emit(Gs,ts.SCALE|o)},e.getWorldScale=function(t){return this.updateWorldTransform(),t?z.copy(t,this._scale):z.copy(new z,this._scale)},e.getWorldMatrix=function(t){this.updateWorldTransform();var e=t||new X;return X.copy(e,this._mat)},e.getWorldRS=function(t){this.updateWorldTransform();var e=t||new X;return X.copy(e,this._mat),e.m12=0,e.m13=0,e.m14=0,e},e.getWorldRT=function(t){this.updateWorldTransform();var e=t||new X;return X.fromRT(e,this._rot,this._pos)},e.setRTS=function(t,e,i){var n=0;t&&(n|=ts.ROTATION,void 0!==t.w?(K.copy(this._lrot,t),this._eulerDirty=!0):(z.copy(this._euler,t),K.fromEuler(this._lrot,t.x,t.y,t.z),this._eulerDirty=!1)),e&&(z.copy(this._lpos,e),n|=ts.POSITION),i&&(z.copy(this._lscale,i),n|=ts.SCALE),n&&(this.invalidateChildren(n),this._eventMask&zs&&this.emit(Gs,n))},e.isTransformDirty=function(){return this._transformFlags!==ts.NONE},e.pauseSystemEvents=function(t){this._eventProcessor.setEnabled(!1,t)},e.resumeSystemEvents=function(t){this._eventProcessor.setEnabled(!0,t)},s.resetHasChangedFlags=function(){Ru+=1},s.clearNodeArray=function(){s.ClearFrame<s.ClearRound?s.ClearFrame++:(s.ClearFrame=0,Eu.length=0)},e.getPathInHierarchy=function(){for(var t=this.name,e=this.parent;e&&!(e instanceof ot.Scene);)t=e.name+"/"+t,e=e.parent;return t},e._getUITransformComp=function(){return this._uiProps.uiTransformComp},r(s,[{key:"components",get:function(){return this._components}},{key:"_persistNode",get:function(){return(this._objFlags&Fs)>0},set:function(t){t?this._objFlags|=Fs:this._objFlags&=~Fs}},{key:"name",get:function(){return this._name},set:function(t){this._name=t}},{key:"uuid",get:function(){return this._id}},{key:"children",get:function(){return this._children}},{key:"active",get:function(){return this._active},set:function(t){if(t=!!t,this._active!==t){this._active=t;var e=this._parent;e&&e._activeInHierarchy&&ot.director._nodeActivator.activateNode(this,t)}}},{key:"activeInHierarchy",get:function(){return this._activeInHierarchy}},{key:"parent",get:function(){return this._parent},set:function(t){this.setParent(t)}},{key:"scene",get:function(){return this._scene}},{key:"eventProcessor",get:function(){return this._eventProcessor}},{key:"prefab",get:function(){return this._prefab}},{key:"id",set:function(t){this._id=t}},{key:"siblingIndex",get:function(){return this._siblingIndex},set:function(t){this._siblingIndex=t}},{key:"position",get:function(){return this._lpos},set:function(t){this.setPosition(t)}},{key:"x",get:function(){return this._lpos.x},set:function(t){this.setPosition(t,this._lpos.y,this._lpos.z)}},{key:"y",get:function(){return this._lpos.y},set:function(t){this.setPosition(this._lpos.x,t,this._lpos.z)}},{key:"z",get:function(){return this._lpos.z},set:function(t){this.setPosition(this._lpos.x,this._lpos.y,t)}},{key:"worldPosition",get:function(){return this.updateWorldTransform(),this._pos},set:function(t){this.setWorldPosition(t)}},{key:"worldPositionX",get:function(){return this.updateWorldTransform(),this._pos.x},set:function(t){this.setWorldPosition(t,this._pos.y,this._pos.z)}},{key:"worldPositionY",get:function(){return this.updateWorldTransform(),this._pos.y},set:function(t){this.setWorldPosition(this._pos.x,t,this._pos.z)}},{key:"worldPositionZ",get:function(){return this.updateWorldTransform(),this._pos.z},set:function(t){this.setWorldPosition(this._pos.x,this._pos.y,t)}},{key:"rotation",get:function(){return this._lrot},set:function(t){this.setRotation(t)}},{key:"eulerAngles",get:function(){return this._eulerDirty&&(K.toEuler(this._euler,this._lrot),this._eulerDirty=!1),this._euler},set:function(t){this.setRotationFromEuler(t.x,t.y,t.z)}},{key:"angle",get:function(){return this.eulerAngles.z},set:function(t){z.set(this._euler,0,0,t),K.fromAngleZ(this._lrot,t),this._eulerDirty=!1,this.invalidateChildren(ts.ROTATION),this._eventMask&zs&&this.emit(Gs,ts.ROTATION)}},{key:"worldRotation",get:function(){return this.updateWorldTransform(),this._rot},set:function(t){this.setWorldRotation(t)}},{key:"scale",get:function(){return this._lscale},set:function(t){this.setScale(t)}},{key:"worldScale",get:function(){return this.updateWorldTransform(),this._scale},set:function(t){this.setWorldScale(t)}},{key:"matrix",set:function(t){X.toSRT(t,this._lrot,this._lpos,this._lscale),this.invalidateChildren(ts.TRS),this._eulerDirty=!0,this._eventMask&zs&&this.emit(Gs,ts.TRS)}},{key:"worldMatrix",get:function(){return this.updateWorldTransform(),this._mat}},{key:"forward",get:function(){return z.transformQuat(new z,z.FORWARD,this.worldRotation)},set:function(t){var e=t.length();z.multiplyScalar(du,t,-1/e),K.fromViewUp(mu,du),this.setWorldRotation(mu)}},{key:"up",get:function(){return z.transformQuat(new z,z.UP,this.worldRotation)}},{key:"right",get:function(){return z.transformQuat(new z,z.RIGHT,this.worldRotation)}},{key:"mobility",get:function(){return this._mobility},set:function(t){this._mobility!==t&&(this._mobility=t,this.emit(Yt.MOBILITY_CHANGED))}},{key:"layer",get:function(){return this._layer},set:function(t){var e=this;if(e._layer!==t){e._layer=t;var i=e._uiProps&&e._uiProps.uiComp;i&&(i.setNodeDirty(),i._markForUpdateRenderData()),e.emit(Yt.LAYER_CHANGED,e._layer)}}},{key:"flagChangedVersion",get:function(){return this._flagChangeVersion}},{key:"hasChangedFlags",get:function(){return this._flagChangeVersion===Ru?this._hasChangedFlags:0},set:function(t){this._flagChangeVersion=Ru,this._hasChangedFlags=t}}]),s}(x),xs.idGenerator=Ws,xs._stacks=[[]],xs._stackId=0,xs.EventType=Yt,xs.NodeSpace=$r,xs.TransformDirtyBit=ts,xs.TransformBit=ts,xs.reserveContentsForAllSyncablePrefabTag=Du,xs.ClearFrame=0,xs.ClearRound=1e3,P((bs=xs).prototype,"_persistNode",[it],Object.getOwnPropertyDescriptor(bs.prototype,"_persistNode"),bs.prototype),Is=F(bs.prototype,"_parent",[U],(function(){return null})),Ts=F(bs.prototype,"_children",[U],(function(){return[]})),Es=F(bs.prototype,"_active",[U],(function(){return!0})),Ds=F(bs.prototype,"_components",[U],(function(){return[]})),Rs=F(bs.prototype,"_prefab",[U],(function(){return null})),Ss=F(bs.prototype,"_lpos",[U],(function(){return new z})),Ms=F(bs.prototype,"_lrot",[U],(function(){return new K})),As=F(bs.prototype,"_lscale",[U],(function(){return new z(1,1,1)})),ws=F(bs.prototype,"_mobility",[U],(function(){return ss.Static})),ks=F(bs.prototype,"_layer",[U],(function(){return Rt.Enum.DEFAULT})),Cs=F(bs.prototype,"_euler",[U],(function(){return new z})),P(bs.prototype,"eulerAngles",[ms],Object.getOwnPropertyDescriptor(bs.prototype,"eulerAngles"),bs.prototype),P(bs.prototype,"mobility",[vs],Object.getOwnPropertyDescriptor(bs.prototype,"mobility"),bs.prototype),ys=bs))||ys));ot.Node=Mu;var Au=new z(0,1,0),wu=new z,ku=new W,Cu=new q,xu=new K,Pu=function(t){var e=1/Math.max(Math.max(Math.max(t.x,t.y),t.z),1e-4);e<1&&(t.x*=e,t.y*=e,t.z*=e)},Lu=t("W",(Xs=B("cc.AmbientInfo"),qs=G(L),Ks=rt("_skyColor"),Ys=rt("_skyIllum"),Qs=rt("_groundAlbedo"),Xs((Zs=function(){function t(){this._skyColorHDR=$s&&$s(),this._skyIllumHDR=to&&to(),this._groundAlbedoHDR=eo&&eo(),this._skyColorLDR=io&&io(),this._skyIllumLDR=no&&no(),this._groundAlbedoLDR=ro&&ro(),this._resource=null}return t.prototype.activate=function(t){this._resource=t,t.initialize(this)},r(t,[{key:"skyColorHDR",get:function(){return this._skyColorHDR}},{key:"groundAlbedoHDR",get:function(){return this._groundAlbedoHDR}},{key:"skyIllumHDR",get:function(){return this._skyIllumHDR}},{key:"skyColorLDR",get:function(){return this._skyColorLDR}},{key:"groundAlbedoLDR",get:function(){return this._groundAlbedoLDR}},{key:"skyIllumLDR",get:function(){return this._skyIllumLDR}},{key:"skyLightingColor",get:function(){var t=jr().isHDR;return ku.set(t?this._skyColorHDR:this._skyColorLDR),Pu(ku),Cu.set(255*ku.x,255*ku.y,255*ku.z,255)},set:function(t){ku.set(t.x,t.y,t.z,t.w),jr().isHDR?this._skyColorHDR.set(ku):this._skyColorLDR.set(ku),this._resource&&this._resource.skyColor.set(ku)}},{key:"skyColor",set:function(t){jr().isHDR?this._skyColorHDR.set(t):this._skyColorLDR.set(t),this._resource&&this._resource.skyColor.set(t)}},{key:"skyIllum",get:function(){return jr().isHDR?this._skyIllumHDR:this._skyIllumLDR},set:function(t){jr().isHDR?this._skyIllumHDR=t:this._skyIllumLDR=t,this._resource&&(this._resource.skyIllum=t)}},{key:"groundLightingColor",get:function(){var t=jr().isHDR;return ku.set(t?this._groundAlbedoHDR:this._groundAlbedoLDR),Pu(ku),Cu.set(255*ku.x,255*ku.y,255*ku.z,255)},set:function(t){ku.set(t.x,t.y,t.z,t.w),jr().isHDR?this._groundAlbedoHDR.set(ku):this._groundAlbedoLDR.set(ku),this._resource&&this._resource.groundAlbedo.set(ku)}},{key:"groundAlbedo",set:function(t){jr().isHDR?this._groundAlbedoHDR.set(t):this._groundAlbedoLDR.set(t),this._resource&&this._resource.groundAlbedo.set(t)}}]),t}(),P(Zs.prototype,"skyIllum",[qs],Object.getOwnPropertyDescriptor(Zs.prototype,"skyIllum"),Zs.prototype),$s=F(Zs.prototype,"_skyColorHDR",[U,Ks],(function(){return new W(.2,.5,.8,1)})),to=F(Zs.prototype,"_skyIllumHDR",[U,Ys],(function(){return Xr.SKY_ILLUM})),eo=F(Zs.prototype,"_groundAlbedoHDR",[U,Qs],(function(){return new W(.2,.2,.2,1)})),io=F(Zs.prototype,"_skyColorLDR",[U],(function(){return new W(.2,.5,.8,1)})),no=F(Zs.prototype,"_skyIllumLDR",[U],(function(){return Xr.SKY_ILLUM})),ro=F(Zs.prototype,"_groundAlbedoLDR",[U],(function(){return new W(.2,.2,.2,1)})),Js=Zs))||Js));at.AmbientInfo=Lu;var Ou=t("X",(so=B("cc.SkyboxInfo"),oo=G(qr),ao=G(Qi),uo=G(L),ho=G(Qi),lo=G(Qi),po=G(Ur),co=G(Qi),fo=rt("_envmap"),_o=G(Qi),go=G(Qi),mo=G(Qi),vo=G(Ur),yo=G(Qi),bo=G(Qi),so((To=function(){function t(){this._envLightingType=Eo&&Eo(),this._envmapHDR=Do&&Do(),this._envmapLDR=Ro&&Ro(),this._diffuseMapHDR=So&&So(),this._diffuseMapLDR=Mo&&Mo(),this._enabled=Ao&&Ao(),this._useHDR=wo&&wo(),this._editableMaterial=ko&&ko(),this._reflectionHDR=Co&&Co(),this._reflectionLDR=xo&&xo(),this._rotationAngle=Po&&Po(),this._resource=null}var e=t.prototype;return e.activate=function(t){this.envLightingType=this._envLightingType,this._resource=t,t.initialize(this),t.setEnvMaps(this._envmapHDR,this._envmapLDR),t.setDiffuseMaps(this._diffuseMapHDR,this._diffuseMapLDR),t.setSkyboxMaterial(this._editableMaterial),t.setReflectionMaps(this._reflectionHDR,this._reflectionLDR),t.setRotationAngle(this._rotationAngle),t.activate()},e.updateEnvMap=function(t){t||(this.applyDiffuseMap=!1,this.useIBL=!1,this.envLightingType=qr.HEMISPHERE_DIFFUSE,_(15001));var e=this._resource;e&&(e.setEnvMaps(this._envmapHDR,this._envmapLDR),e.setDiffuseMaps(this._diffuseMapHDR,this._diffuseMapLDR),e.setReflectionMaps(this._reflectionHDR,this._reflectionLDR),e.useDiffuseMap=this.applyDiffuseMap,e.envmap=t)},e.setMaterialProperty=function(t,e,i){var n=this._resource;if(n){var r=n.editableMaterial;n.enabled&&r&&(r.setProperty(t,e,i),r.passes.forEach((function(t){t.update()})))}},r(t,[{key:"applyDiffuseMap",get:function(){return qr.DIFFUSEMAP_WITH_REFLECTION===this._envLightingType},set:function(t){this._resource&&(this._resource.useDiffuseMap=t)}},{key:"enabled",get:function(){return this._enabled},set:function(t){this._enabled!==t&&(this._enabled=t,this._resource&&(this._resource.enabled=this._enabled))}},{key:"envLightingType",get:function(){return this._envLightingType},set:function(t){this.envmap||qr.HEMISPHERE_DIFFUSE===t?(qr.HEMISPHERE_DIFFUSE===t?(this.useIBL=!1,this.applyDiffuseMap=!1):qr.AUTOGEN_HEMISPHERE_DIFFUSE_WITH_REFLECTION===t?(this.useIBL=!0,this.applyDiffuseMap=!1):qr.DIFFUSEMAP_WITH_REFLECTION===t&&(this.useIBL=!0,this.applyDiffuseMap=!0),this._envLightingType=t):(this.useIBL=!1,this.applyDiffuseMap=!1,this._envLightingType=qr.HEMISPHERE_DIFFUSE,_(15001))}},{key:"useIBL",get:function(){return qr.HEMISPHERE_DIFFUSE!==this._envLightingType},set:function(t){this._resource&&(this._resource.useIBL=t)}},{key:"useHDR",get:function(){return jr().isHDR=this._useHDR,this._useHDR},set:function(t){jr().isHDR=t,this._useHDR=t;var e=this._resource;e&&this.envLightingType===qr.DIFFUSEMAP_WITH_REFLECTION&&(null===this.diffuseMap?(this.envLightingType=qr.AUTOGEN_HEMISPHERE_DIFFUSE_WITH_REFLECTION,_(15e3)):this.diffuseMap.isDefault&&_(15002)),e&&(e.useHDR=this._useHDR,e.updateMaterialRenderInfo())}},{key:"envmap",get:function(){return jr().isHDR?this._envmapHDR:this._envmapLDR},set:function(t){var e=jr().isHDR;e?(this._envmapHDR=t,this._reflectionHDR=null):(this._envmapLDR=t,this._reflectionLDR=null),t||(e?this._diffuseMapHDR=null:this._diffuseMapLDR=null,this.applyDiffuseMap=!1,this.useIBL=!1,this.envLightingType=qr.HEMISPHERE_DIFFUSE,_(15001));var i=this._resource;i&&(i.setEnvMaps(this._envmapHDR,this._envmapLDR),i.setDiffuseMaps(this._diffuseMapHDR,this._diffuseMapLDR),i.setReflectionMaps(this._reflectionHDR,this._reflectionLDR),i.useDiffuseMap=this.applyDiffuseMap,i.envmap=t)}},{key:"rotationAngle",get:function(){return this._rotationAngle},set:function(t){this._rotationAngle=t,this._resource&&this._resource.setRotationAngle(this._rotationAngle)}},{key:"diffuseMap",get:function(){return jr().isHDR?this._diffuseMapHDR:this._diffuseMapLDR},set:function(t){jr().isHDR?this._diffuseMapHDR=t:this._diffuseMapLDR=t,this._resource&&this._resource.setDiffuseMaps(this._diffuseMapHDR,this._diffuseMapLDR)}},{key:"reflectionMap",get:function(){return jr().isHDR?this._reflectionHDR:this._reflectionLDR},set:function(t){jr().isHDR?this._reflectionHDR=t:this._reflectionLDR=t,this._resource&&this._resource.setReflectionMaps(this._reflectionHDR,this._reflectionLDR)}},{key:"skyboxMaterial",get:function(){return this._editableMaterial},set:function(t){this._editableMaterial=t,this._resource&&this._resource.setSkyboxMaterial(this._editableMaterial)}}]),t}(),P(To.prototype,"envLightingType",[oo],Object.getOwnPropertyDescriptor(To.prototype,"envLightingType"),To.prototype),P(To.prototype,"envmap",[ao],Object.getOwnPropertyDescriptor(To.prototype,"envmap"),To.prototype),P(To.prototype,"rotationAngle",[uo],Object.getOwnPropertyDescriptor(To.prototype,"rotationAngle"),To.prototype),P(To.prototype,"diffuseMap",[ho],Object.getOwnPropertyDescriptor(To.prototype,"diffuseMap"),To.prototype),P(To.prototype,"reflectionMap",[lo],Object.getOwnPropertyDescriptor(To.prototype,"reflectionMap"),To.prototype),P(To.prototype,"skyboxMaterial",[po],Object.getOwnPropertyDescriptor(To.prototype,"skyboxMaterial"),To.prototype),Eo=F(To.prototype,"_envLightingType",[U],(function(){return qr.HEMISPHERE_DIFFUSE})),Do=F(To.prototype,"_envmapHDR",[U,co,fo],(function(){return null})),Ro=F(To.prototype,"_envmapLDR",[U,_o],(function(){return null})),So=F(To.prototype,"_diffuseMapHDR",[U,go],(function(){return null})),Mo=F(To.prototype,"_diffuseMapLDR",[U,mo],(function(){return null})),Ao=F(To.prototype,"_enabled",[U],(function(){return!1})),wo=F(To.prototype,"_useHDR",[U],(function(){return!0})),ko=F(To.prototype,"_editableMaterial",[U,vo],(function(){return null})),Co=F(To.prototype,"_reflectionHDR",[U,yo],(function(){return null})),xo=F(To.prototype,"_reflectionLDR",[U,bo],(function(){return null})),Po=F(To.prototype,"_rotationAngle",[U],(function(){return 0})),Io=To))||Io));at.SkyboxInfo=Ou;var Nu=t("Y",(Lo=B("cc.FogInfo"),Oo=G(is),No=G(L),Ho=G(L),Bo=G(L),Fo=G(L),Uo=G(L),Go=G(L),Lo((ta=function(){function t(){this._type=Wo&&Wo(),this._fogColor=jo&&jo(),this._enabled=Xo&&Xo(),this._fogDensity=qo&&qo(),this._fogStart=Ko&&Ko(),this._fogEnd=Yo&&Yo(),this._fogAtten=Qo&&Qo(),this._fogTop=Jo&&Jo(),this._fogRange=Zo&&Zo(),this._accurate=$o&&$o(),this._resource=null}return t.prototype.activate=function(t){this._resource=t,t.initialize(this),t.activate()},r(t,[{key:"enabled",get:function(){return this._enabled},set:function(t){if(this._enabled!==t){this._enabled=t;var e=this._resource;e&&(e.enabled=t,t&&(e.type=this._type))}}},{key:"accurate",get:function(){return this._accurate},set:function(t){if(this._accurate!==t){this._accurate=t;var e=this._resource;e&&(e.accurate=t,t&&(e.type=this._type))}}},{key:"fogColor",get:function(){return this._fogColor},set:function(t){this._fogColor.set(t),this._resource&&(this._resource.fogColor=this._fogColor)}},{key:"type",get:function(){return this._type},set:function(t){this._type=t,this._resource&&(this._resource.type=t)}},{key:"fogDensity",get:function(){return this._fogDensity},set:function(t){this._fogDensity=t,this._resource&&(this._resource.fogDensity=t)}},{key:"fogStart",get:function(){return this._fogStart},set:function(t){this._fogStart=t,this._resource&&(this._resource.fogStart=t)}},{key:"fogEnd",get:function(){return this._fogEnd},set:function(t){this._fogEnd=t,this._resource&&(this._resource.fogEnd=t)}},{key:"fogAtten",get:function(){return this._fogAtten},set:function(t){this._fogAtten=t,this._resource&&(this._resource.fogAtten=t)}},{key:"fogTop",get:function(){return this._fogTop},set:function(t){this._fogTop=t,this._resource&&(this._resource.fogTop=t)}},{key:"fogRange",get:function(){return this._fogRange},set:function(t){this._fogRange=t,this._resource&&(this._resource.fogRange=t)}}]),t}(),ta.FogType=is,P((zo=ta).prototype,"type",[Oo],Object.getOwnPropertyDescriptor(zo.prototype,"type"),zo.prototype),P(zo.prototype,"fogDensity",[No],Object.getOwnPropertyDescriptor(zo.prototype,"fogDensity"),zo.prototype),P(zo.prototype,"fogStart",[Ho],Object.getOwnPropertyDescriptor(zo.prototype,"fogStart"),zo.prototype),P(zo.prototype,"fogEnd",[Bo],Object.getOwnPropertyDescriptor(zo.prototype,"fogEnd"),zo.prototype),P(zo.prototype,"fogAtten",[Fo],Object.getOwnPropertyDescriptor(zo.prototype,"fogAtten"),zo.prototype),P(zo.prototype,"fogTop",[Uo],Object.getOwnPropertyDescriptor(zo.prototype,"fogTop"),zo.prototype),P(zo.prototype,"fogRange",[Go],Object.getOwnPropertyDescriptor(zo.prototype,"fogRange"),zo.prototype),Wo=F(zo.prototype,"_type",[U],(function(){return is.LINEAR})),jo=F(zo.prototype,"_fogColor",[U],(function(){return new q("#C8C8C8")})),Xo=F(zo.prototype,"_enabled",[U],(function(){return!1})),qo=F(zo.prototype,"_fogDensity",[U],(function(){return.3})),Ko=F(zo.prototype,"_fogStart",[U],(function(){return.5})),Yo=F(zo.prototype,"_fogEnd",[U],(function(){return 300})),Qo=F(zo.prototype,"_fogAtten",[U],(function(){return 5})),Jo=F(zo.prototype,"_fogTop",[U],(function(){return 1.5})),Zo=F(zo.prototype,"_fogRange",[U],(function(){return 1.2})),$o=F(zo.prototype,"_accurate",[U],(function(){return!1})),Vo=zo))||Vo)),Hu=t("Z",(ea=B("cc.ShadowsInfo"),ia=G(Vr),na=G(L),ra=G(L),sa=G(O),oa=G(Gr),ea((ua=function(){function t(){this._enabled=ha&&ha(),this._type=la&&la(),this._normal=pa&&pa(),this._distance=ca&&ca(),this._planeBias=fa&&fa(),this._shadowColor=_a&&_a(),this._maxReceived=da&&da(),this._size=ga&&ga(),this._resource=null}var e=t.prototype;return e.setPlaneFromNode=function(t){t.getWorldRotation(xu),this.planeDirection=z.transformQuat(wu,Au,xu),t.getWorldPosition(wu),this.planeHeight=z.dot(this._normal,wu)},e.activate=function(t){this._resource=t,t.initialize(this),t.activate()},r(t,[{key:"enabled",get:function(){return this._enabled},set:function(t){if(this._enabled!==t){this._enabled=t;var e=this._resource;e&&(e.enabled=t,t&&(e.type=this._type))}}},{key:"type",get:function(){return this._type},set:function(t){this._type=t,this._resource&&(this._resource.type=t)}},{key:"shadowColor",get:function(){return this._shadowColor},set:function(t){this._shadowColor.set(t),this._resource&&(this._resource.shadowColor=t)}},{key:"planeDirection",get:function(){return this._normal},set:function(t){z.copy(this._normal,t),this._resource&&(this._resource.normal=t)}},{key:"planeHeight",get:function(){return this._distance},set:function(t){this._distance=t,this._resource&&(this._resource.distance=t)}},{key:"planeBias",get:function(){return this._planeBias},set:function(t){this._planeBias=t,this._resource&&(this._resource.planeBias=t)}},{key:"maxReceived",get:function(){return this._maxReceived},set:function(t){this._maxReceived=t,this._resource&&(this._resource.maxReceived=t)}},{key:"shadowMapSize",get:function(){return this._size.x},set:function(t){var e=this._resource;this._size.set(t,t),e&&(e.size.set(t,t),e.shadowMapDirty=!0)}}]),t}(),P(ua.prototype,"type",[ia],Object.getOwnPropertyDescriptor(ua.prototype,"type"),ua.prototype),P(ua.prototype,"planeHeight",[na],Object.getOwnPropertyDescriptor(ua.prototype,"planeHeight"),ua.prototype),P(ua.prototype,"planeBias",[ra],Object.getOwnPropertyDescriptor(ua.prototype,"planeBias"),ua.prototype),P(ua.prototype,"maxReceived",[sa],Object.getOwnPropertyDescriptor(ua.prototype,"maxReceived"),ua.prototype),P(ua.prototype,"shadowMapSize",[oa],Object.getOwnPropertyDescriptor(ua.prototype,"shadowMapSize"),ua.prototype),ha=F(ua.prototype,"_enabled",[U],(function(){return!1})),la=F(ua.prototype,"_type",[U],(function(){return Vr.Planar})),pa=F(ua.prototype,"_normal",[U],(function(){return new z(0,1,0)})),ca=F(ua.prototype,"_distance",[U],(function(){return 0})),fa=F(ua.prototype,"_planeBias",[U],(function(){return 1})),_a=F(ua.prototype,"_shadowColor",[U],(function(){return new q(0,0,0,76)})),da=F(ua.prototype,"_maxReceived",[U],(function(){return 4})),ga=F(ua.prototype,"_size",[U],(function(){return new V(1024,1024)})),aa=ua))||aa));at.ShadowsInfo=Hu;var Bu=t("_",new z(-1024,-1024,-1024)),Fu=t("$",new z(1024,1024,1024)),Uu=t("a0",8),Gu=t("a1",(ma=B("cc.OctreeInfo"),va=G(O),ma((ba=function(){function t(){this._enabled=Ia&&Ia(),this._minPos=Ta&&Ta(),this._maxPos=Ea&&Ea(),this._depth=Da&&Da(),this._resource=null}return t.prototype.activate=function(t){this._resource=t,t.initialize(this)},r(t,[{key:"enabled",get:function(){return this._enabled},set:function(t){this._enabled!==t&&(this._enabled=t,this._resource&&(this._resource.enabled=t))}},{key:"minPos",get:function(){return this._minPos},set:function(t){this._minPos=t,this._resource&&(this._resource.minPos=t)}},{key:"maxPos",get:function(){return this._maxPos},set:function(t){this._maxPos=t,this._resource&&(this._resource.maxPos=t)}},{key:"depth",get:function(){return this._depth},set:function(t){this._depth=t,this._resource&&(this._resource.depth=t)}}]),t}(),P(ba.prototype,"depth",[va],Object.getOwnPropertyDescriptor(ba.prototype,"depth"),ba.prototype),Ia=F(ba.prototype,"_enabled",[U],(function(){return!1})),Ta=F(ba.prototype,"_minPos",[U],(function(){return new z(Bu)})),Ea=F(ba.prototype,"_maxPos",[U],(function(){return new z(Fu)})),Da=F(ba.prototype,"_depth",[U],(function(){return Uu})),ya=ba))||ya));at.OctreeInfo=Gu;var Vu=t("a2",(Ra=B("cc.SkinInfo"),Sa=G(L),Ma=G(L),Ra((wa=function(){function t(){this._enabled=ka&&ka(),this._blurRadius=Ca&&Ca(),this._sssIntensity=xa&&xa(),this._resource=null}return t.prototype.activate=function(t){this._resource=t,t.initialize(this)},r(t,[{key:"enabled",get:function(){return this._enabled},set:function(t){this._enabled!==t&&(this._enabled=t,this._resource&&(this._resource.enabled=t))}},{key:"blurRadius",get:function(){return this._blurRadius},set:function(t){this._blurRadius=t,this._resource&&(this._resource.blurRadius=t)}},{key:"sssIntensity",get:function(){return this._sssIntensity},set:function(t){this._sssIntensity=t,this._resource&&(this._resource.sssIntensity=t)}}]),t}(),P(wa.prototype,"blurRadius",[Sa],Object.getOwnPropertyDescriptor(wa.prototype,"blurRadius"),wa.prototype),P(wa.prototype,"sssIntensity",[Ma],Object.getOwnPropertyDescriptor(wa.prototype,"sssIntensity"),wa.prototype),ka=F(wa.prototype,"_enabled",[U],(function(){return!0})),Ca=F(wa.prototype,"_blurRadius",[U],(function(){return.01})),xa=F(wa.prototype,"_sssIntensity",[U],(function(){return 3})),Aa=wa))||Aa));at.SkinInfo=Vu;var zu=t("a3",(Pa=B("cc.PostSettingsInfo"),La=G(os),Pa((Na=function(){function t(){this._toneMappingType=Ha&&Ha(),this._resource=null}return t.prototype.activate=function(t){this._resource=t,t.initialize(this),t.activate()},r(t,[{key:"toneMappingType",get:function(){return this._toneMappingType},set:function(t){this._toneMappingType=t,this._resource&&(this._resource.toneMappingType=t)}}]),t}(),P(Na.prototype,"toneMappingType",[La],Object.getOwnPropertyDescriptor(Na.prototype,"toneMappingType"),Na.prototype),Ha=F(Na.prototype,"_toneMappingType",[U],(function(){return os.DEFAULT})),Oa=Na))||Oa));at.PostSettingsInfo=zu;var Wu,ju,Xu,qu,Ku,Yu,Qu,Ju,Zu,$u,th,eh,ih,nh,rh,sh,oh,ah,uh,hh,lh,ph,ch,fh,_h,dh,gh,mh,vh,yh,bh,Ih,Th,Eh,Dh,Rh,Sh,Mh,Ah,wh,kh,Ch,xh,Ph,Lh,Oh,Nh,Hh,Bh,Fh,Uh,Gh,Vh,zh,Wh,jh,Xh,qh,Kh,Yh,Qh,Jh,Zh,$h,tl,el,il,nl,rl,sl=t("a4",(Ba=B("cc.LightProbeInfo"),Fa=G(L),Ua=G(O),Ga=G(O),Va=G(L),za=G(L),Ba((ja=function(){function t(){this._giScale=Xa&&Xa(),this._giSamples=qa&&qa(),this._bounces=Ka&&Ka(),this._reduceRinging=Ya&&Ya(),this._showProbe=Qa&&Qa(),this._showWireframe=Ja&&Ja(),this._showConvex=Za&&Za(),this._data=$a&&$a(),this._lightProbeSphereVolume=tu&&tu(),this._nodes=[],this._scene=null,this._resource=null}var e=t.prototype;return e.activate=function(t,e){this._scene=t,this._resource=e,e.initialize(this)},e.onProbeBakeFinished=function(){this.onProbeBakingChanged(this._scene)},e.onProbeBakeCleared=function(){this.clearSHCoefficients(),this.onProbeBakingChanged(this._scene)},e.onProbeBakingChanged=function(t){var e=this;t&&(t.emit(Yt.LIGHT_PROBE_BAKING_CHANGED),t.children.forEach((function(t){e.onProbeBakingChanged(t)})))},e.clearSHCoefficients=function(){this._data&&(this._data.probes.forEach((function(t){t.coefficients.length=0})),this.clearAllSHUBOs())},e.isUniqueNode=function(){return 1===this._nodes.length},e.addNode=function(t){if(!t)return!1;for(var e=0;e<this._nodes.length;e++)if(this._nodes[e].node===t)return!1;return this._nodes.push({node:t,probes:null}),!0},e.removeNode=function(t){if(!t)return!1;var e=this._nodes.findIndex((function(e){return e.node===t}));return-1!==e&&(this._nodes.splice(e,1),!0)},e.syncData=function(t,e){for(var i=0;i<this._nodes.length;i++)if(this._nodes[i].node===t)return void(this._nodes[i].probes=e)},e.update=function(t){if(void 0===t&&(t=!0),ot.internal.LightProbesData){this._data||(this._data=new ot.internal.LightProbesData,this._resource&&(this._resource.data=this._data));for(var e=[],i=0;i<this._nodes.length;i++){var n=this._nodes[i],r=n.node,s=n.probes,o=r.worldPosition;if(s)for(var a=0;a<s.length;a++){var u=Z();z.add(u,s[a],o),e.push(u)}}if(e.length<4)return this.resetAllTetraIndices(),void this._data.reset();this._data.updateProbes(e),t&&(this.resetAllTetraIndices(),this._data.updateTetrahedrons())}},e.clearAllSHUBOs=function(){if(this._scene){var t=this._scene.renderScene;t&&t.models.forEach((function(t){t.clearSHUBOs()}))}},e.resetAllTetraIndices=function(){if(this._scene){var t=this._scene.renderScene;t&&t.models.forEach((function(t){t.tetrahedronIndex=-1}))}},r(t,[{key:"giScale",get:function(){return this._giScale},set:function(t){this._giScale!==t&&(this._giScale=t,this._resource&&(this._resource.giScale=t))}},{key:"giSamples",get:function(){return this._giSamples},set:function(t){this._giSamples!==t&&(this._giSamples=t,this._resource&&(this._resource.giSamples=t))}},{key:"bounces",get:function(){return this._bounces},set:function(t){this._bounces!==t&&(this._bounces=t,this._resource&&(this._resource.bounces=t))}},{key:"reduceRinging",get:function(){return this._reduceRinging},set:function(t){this._reduceRinging!==t&&(this._reduceRinging=t,this._resource&&(this._resource.reduceRinging=t))}},{key:"showProbe",get:function(){return this._showProbe},set:function(t){this._showProbe!==t&&(this._showProbe=t,this._resource&&(this._resource.showProbe=t))}},{key:"showWireframe",get:function(){return this._showWireframe},set:function(t){this._showWireframe!==t&&(this._showWireframe=t,this._resource&&(this._resource.showWireframe=t))}},{key:"showConvex",get:function(){return this._showConvex},set:function(t){this._showConvex!==t&&(this._showConvex=t,this._resource&&(this._resource.showConvex=t))}},{key:"data",get:function(){return this._data},set:function(t){this._data!==t&&(this._data=t,this._resource&&(this._resource.data=t))}},{key:"lightProbeSphereVolume",get:function(){return this._lightProbeSphereVolume},set:function(t){this._lightProbeSphereVolume!==t&&(this._lightProbeSphereVolume=t,this._resource&&(this._resource.lightProbeSphereVolume=t))}}]),t}(),P(ja.prototype,"giScale",[Fa],Object.getOwnPropertyDescriptor(ja.prototype,"giScale"),ja.prototype),P(ja.prototype,"giSamples",[Ua],Object.getOwnPropertyDescriptor(ja.prototype,"giSamples"),ja.prototype),P(ja.prototype,"bounces",[Ga],Object.getOwnPropertyDescriptor(ja.prototype,"bounces"),ja.prototype),P(ja.prototype,"reduceRinging",[Va],Object.getOwnPropertyDescriptor(ja.prototype,"reduceRinging"),ja.prototype),P(ja.prototype,"lightProbeSphereVolume",[za],Object.getOwnPropertyDescriptor(ja.prototype,"lightProbeSphereVolume"),ja.prototype),Xa=F(ja.prototype,"_giScale",[U],(function(){return 1})),qa=F(ja.prototype,"_giSamples",[U],(function(){return 1024})),Ka=F(ja.prototype,"_bounces",[U],(function(){return 2})),Ya=F(ja.prototype,"_reduceRinging",[U],(function(){return 0})),Qa=F(ja.prototype,"_showProbe",[U],(function(){return!0})),Ja=F(ja.prototype,"_showWireframe",[U],(function(){return!0})),Za=F(ja.prototype,"_showConvex",[U],(function(){return!1})),$a=F(ja.prototype,"_data",[U],(function(){return null})),tu=F(ja.prototype,"_lightProbeSphereVolume",[U],(function(){return 1})),Wa=ja))||Wa)),ol=t("a5",(eu=B("cc.SceneGlobals"),iu=G(Ou),eu((ru=function(){function t(){this.ambient=su&&su(),this.shadows=ou&&ou(),this._skybox=au&&au(),this.fog=uu&&uu(),this.octree=hu&&hu(),this.skin=lu&&lu(),this.lightProbeInfo=pu&&pu(),this.postSettings=cu&&cu(),this.bakedWithStationaryMainLight=fu&&fu(),this.bakedWithHighpLightmap=_u&&_u(),this.disableLightmap=!1}return t.prototype.activate=function(t){var e=at.director.root.pipeline.pipelineSceneData;this.skybox.activate(e.skybox),this.ambient.activate(e.ambient),this.shadows.activate(e.shadows),this.fog.activate(e.fog),this.octree.activate(e.octree),this.skin.activate(e.skin),this.postSettings.activate(e.postSettings),this.lightProbeInfo&&e.lightProbes&&this.lightProbeInfo.activate(t,e.lightProbes),at.director.root.onGlobalPipelineStateChanged()},r(t,[{key:"skybox",get:function(){return this._skybox},set:function(t){this._skybox=t}}]),t}(),su=F(ru.prototype,"ambient",[U],(function(){return new Lu})),ou=F(ru.prototype,"shadows",[U],(function(){return new Hu})),au=F(ru.prototype,"_skybox",[U],(function(){return new Ou})),uu=F(ru.prototype,"fog",[U],(function(){return new Nu})),P(ru.prototype,"skybox",[iu],Object.getOwnPropertyDescriptor(ru.prototype,"skybox"),ru.prototype),hu=F(ru.prototype,"octree",[U],(function(){return new Gu})),lu=F(ru.prototype,"skin",[U],(function(){return new Vu})),pu=F(ru.prototype,"lightProbeInfo",[U],(function(){return new sl})),cu=F(ru.prototype,"postSettings",[U],(function(){return new zu})),fu=F(ru.prototype,"bakedWithStationaryMainLight",[U],(function(){return!1})),_u=F(ru.prototype,"bakedWithHighpLightmap",[U],(function(){return!1})),nu=ru))||nu));at.SceneGlobals=ol;var al,ul,hl,ll,pl=(Wu=B("cc.TargetInfo"),ju=G([N]),Wu((qu=function(){this.localID=Ku&&Ku()},Ku=F(qu.prototype,"localID",[U,ju],(function(){return[]})),Xu=qu))||Xu),cl=(Yu=B("cc.TargetOverrideInfo"),Qu=G(x),Ju=G(pl),Zu=G([N]),$u=G(Mu),th=G(pl),Yu((ih=function(){this.source=nh&&nh(),this.sourceInfo=rh&&rh(),this.propertyPath=sh&&sh(),this.target=oh&&oh(),this.targetInfo=ah&&ah()},nh=F(ih.prototype,"source",[U,Qu],(function(){return null})),rh=F(ih.prototype,"sourceInfo",[U,Ju],(function(){return null})),sh=F(ih.prototype,"propertyPath",[U,Zu],(function(){return[]})),oh=F(ih.prototype,"target",[U,$u],(function(){return null})),ah=F(ih.prototype,"targetInfo",[U,th],(function(){return null})),eh=ih))||eh),fl=B("cc.CompPrefabInfo")((hh=function(){this.fileId=lh&&lh()},lh=F(hh.prototype,"fileId",[U],(function(){return""})),uh=hh))||uh,_l=(ph=B("CCPropertyOverrideInfo"),ch=G(pl),fh=G([N]),ph((dh=function(){function t(){this.targetInfo=gh&&gh(),this.propertyPath=mh&&mh(),this.value=vh&&vh()}return t.prototype.isTarget=function(){},t}(),gh=F(dh.prototype,"targetInfo",[U,ch],(function(){return null})),mh=F(dh.prototype,"propertyPath",[U,fh],(function(){return[]})),vh=F(dh.prototype,"value",[U],null),_h=dh))||_h),dl=(yh=B("cc.MountedChildrenInfo"),bh=G(pl),Ih=G([Mu]),yh((Eh=function(){function t(){this.targetInfo=Dh&&Dh(),this.nodes=Rh&&Rh()}return t.prototype.isTarget=function(){},t}(),Dh=F(Eh.prototype,"targetInfo",[U,bh],(function(){return null})),Rh=F(Eh.prototype,"nodes",[U,Ih],(function(){return[]})),Th=Eh))||Th),gl=(Sh=B("cc.MountedComponentsInfo"),Mh=G(pl),Ah=G([Kt]),Sh((kh=function(){function t(){this.targetInfo=Ch&&Ch(),this.components=xh&&xh()}return t.prototype.isTarget=function(){},t}(),Ch=F(kh.prototype,"targetInfo",[U,Mh],(function(){return null})),xh=F(kh.prototype,"components",[U,Ah],(function(){return[]})),wh=kh))||wh),ml=(Ph=B("cc.PrefabInstance"),Lh=G(Mu),Oh=G([dl]),Nh=G([gl]),Hh=G([_l]),Bh=G([pl]),Ph((Uh=function(){function t(){this.fileId=Gh&&Gh(),this.prefabRootNode=Vh&&Vh(),this.mountedChildren=zh&&zh(),this.mountedComponents=Wh&&Wh(),this.propertyOverrides=jh&&jh(),this.removedComponents=Xh&&Xh(),this.targetMap={},this.expanded=!1}var e=t.prototype;return e.findPropertyOverride=function(){},e.removePropertyOverride=function(){},t}(),Gh=F(Uh.prototype,"fileId",[U],(function(){return""})),Vh=F(Uh.prototype,"prefabRootNode",[U,Lh],null),zh=F(Uh.prototype,"mountedChildren",[U,Oh],(function(){return[]})),Wh=F(Uh.prototype,"mountedComponents",[U,Nh],(function(){return[]})),jh=F(Uh.prototype,"propertyOverrides",[U,Hh],(function(){return[]})),Xh=F(Uh.prototype,"removedComponents",[U,Bh],(function(){return[]})),Fh=Uh))||Fh),vl=(qh=B("cc.PrefabInfo"),Kh=G(Mu),Yh=G(ml),Qh=G([cl]),qh((Zh=function(){this.root=$h&&$h(),this.asset=tl&&tl(),this.fileId=el&&el(),this.instance=il&&il(),this.targetOverrides=nl&&nl(),this.nestedPrefabInstanceRoots=rl&&rl()},$h=F(Zh.prototype,"root",[U,Kh],null),tl=F(Zh.prototype,"asset",[U],null),el=F(Zh.prototype,"fileId",[U],(function(){return""})),il=F(Zh.prototype,"instance",[U,Yh],null),nl=F(Zh.prototype,"targetOverrides",[U,Qh],null),rl=F(Zh.prototype,"nestedPrefabInstanceRoots",[U],null),Jh=Zh))||Jh);function yl(t){var e=null==t?void 0:t.prefab;if(e&&e.instance){if(!e.asset)return n(3701,t.name),void(e.instance=void 0);var i=t._objFlags,r=t.getParent(),s=t.uuid;t[st],ot.game._isCloning=!0;var o=e.asset.data;o._iN$t=t,ot.instantiate._clone(o,o),ot.game._isCloning=!1,t._objFlags=i,t.modifyParent(r),t.id=s,t.prefab&&(t.prefab.instance=e.instance)}}function bl(t,e,i){var n;if(e&&t){var r=e,s=null==(n=t.prefab)?void 0:n.instance;!i&&s&&(e[s.fileId]={},r=e[s.fileId]);var o=t.prefab;o&&(r[o.fileId]=t),t.components.forEach((function(t){t.__prefab&&(r[t.__prefab.fileId]=t)})),t.children.forEach((function(t){bl(t,r,!1)}))}}function Il(t,e){if(!t)return null;for(var i=e,n=0;n<t.length;n++){if(!i)return null;i=i[t[n]]}return i}function Tl(t,e,i){if(e)for(var n=0;n<e.length;n++){var r=e[n];if(r&&r.targetInfo){var s=Il(r.targetInfo.localID,i);if(!s)continue;var o=i,a=r.targetInfo.localID;if(a.length>0)for(var u=0;u<a.length-1;u++)o=o[a[u]];if(r.nodes)for(var h=0;h<r.nodes.length;h++){var l=r.nodes[h];l&&!s.children.includes(l)&&(s.children.push(l),l.modifyParent(s),bl(l,o,!1),l.siblingIndex=s.children.length-1,Ml(l,!0))}}}}function El(t,e,i){if(e)for(var n=0;n<e.length;n++){var r=e[n];if(r&&r.targetInfo){var s=Il(r.targetInfo.localID,i);if(!s)continue;if(r.components)for(var o=0;o<r.components.length;o++){var a=r.components[o];a&&(a.node=s,s.getWritableComponents().push(a))}}}}function Dl(t,e,i){if(e)for(var n=0;n<e.length;n++){var r=e[n];if(r){var s=Il(r.localID,i);if(!s||!s.node)continue;var o=s.node.components.indexOf(s);o>=0&&s.node.getWritableComponents().splice(o,1)}}}function Rl(t,e,i){if(!(e.length<=0))for(var n=null,r=0;r<e.length;r++){var s=e[r];if(s&&s.targetInfo){if(!(n=Il(s.targetInfo.localID,i)))continue;var o=n,a=s.propertyPath.slice();if(a.length>0){var u=a.pop();if(!u)continue;for(var h=0;h<a.length&&(o=o[a[h]]);h++);if(!o)continue;if(Array.isArray(o))if("length"===u)o[u]=s.value;else{var l=Number.parseInt(u);Number.isInteger(l)&&l<o.length&&(o[u]=s.value)}else o[u]instanceof H?o[u].set(s.value):o[u]=s.value}}}}function Sl(t){var e,i=null==(e=t.prefab)?void 0:e.targetOverrides;if(i)for(var n=0;n<i.length;n++){var r,s=i[n],o=s.source,a=s.sourceInfo;if(a){var u,h=s.source,l=null==h||null==(u=h.prefab)?void 0:u.instance;l&&l.targetMap&&(o=Il(a.localID,l.targetMap))}if(o){var p,c=s.targetInfo;if(c){var f=s.target,_=null==f||null==(r=f.prefab)?void 0:r.instance;if(_&&_.targetMap&&(p=Il(c.localID,_.targetMap))){var d=s.propertyPath.slice(),g=o;if(d.length>0){var m=d.pop();if(!m)return;for(var v=0;v<d.length&&(g=g[d[v]]);v++);if(!g)continue;g[m]=p}}}}}}function Ml(t,e){var i;void 0===e&&(e=!1);var n=null==t||null==(i=t.prefab)?void 0:i.instance;if(n&&!n.expanded){yl(t),e&&t&&t.children&&t.children.forEach((function(t){Ml(t,!0)}));var r={};n.targetMap=r,bl(t,r,!0),Tl(0,n.mountedChildren,r),Dl(0,n.removedComponents,r),El(0,n.mountedComponents,r),Rl(0,n.propertyOverrides,r),n.expanded=!0}else e&&t&&t.children&&t.children.forEach((function(t){Ml(t,!0)}))}function Al(t){var e=t.prefab;e&&e.nestedPrefabInstanceRoots&&e.nestedPrefabInstanceRoots.forEach((function(t){Ml(t)}))}ot._PrefabInfo=vl,t("ad",Object.freeze({__proto__:null,CompPrefabInfo:fl,MountedChildrenInfo:dl,MountedComponentsInfo:gl,PrefabInfo:vl,PrefabInstance:ml,PropertyOverrideInfo:_l,TargetInfo:pl,TargetOverrideInfo:cl,applyMountedChildren:Tl,applyMountedComponents:El,applyNodeAndComponentId:function t(e,i){for(var n=e.components,r=e.children,s=0;s<n.length;s++){var o,a,u=n[s],h=null!==(o=null==(a=u.__prefab)?void 0:a.fileId)&&void 0!==o?o:"";u._id=""+i+h}for(var l=0;l<r.length;l++){var p=r[l],c=p.prefab,f=null!=c&&c.instance?c.instance.fileId:null==c?void 0:c.fileId;f&&(p.id=""+i+f,null!=c&&c.instance||t(p,i))}},applyPropertyOverrides:Rl,applyRemovedComponents:Dl,applyTargetOverrides:Sl,createNodeWithPrefab:yl,expandNestedPrefabInstanceNode:Al,expandPrefabInstanceNode:Ml,generateTargetMap:bl,getTarget:Il}));var wl=t("j",B("cc.Scene")((ul=function(t){i(n,t);var e=n.prototype;function n(e){var i;return(i=t.call(this,e)||this).autoReleaseAssets=hl&&hl(),i._globals=ll&&ll(),i.dependAssets=null,i._renderScene=null,i._prefabSyncedInLiveReload=!1,i._activeInHierarchy=!1,at.director&&at.director.root&&(i._renderScene=at.director.root.createScene({})),i._inited=!at.game||!at.game._isCloning,i}return e._updateScene=function(){this._scene=this},e.destroy=function(){var t=x.prototype.destroy.call(this);if(t)for(var e=this._children,i=0;i<e.length;++i)e[i].active=!1;return this._renderScene&&at.director.root.destroyScene(this._renderScene),this._active=!1,this._activeInHierarchy=!1,t},e.addComponent=function(){throw new Error(g(3822))},e._onHierarchyChanged=function(){},e._onPostActivated=function(){},e._onBatchCreated=function(t){for(var e=this._children.length,i=0;i<e;++i)this._children[i]._siblingIndex=i,this._children[i]._onBatchCreated(t)},e.updateWorldTransform=function(){},e._instantiate=function(){return null},e._load=function(){this._inited||(Al(this),Sl(this),this._onBatchCreated(lt),this._inited=!0),this.walk(Mu._setScene)},e._activate=function(t){void 0===t&&(t=!0),at.director._nodeActivator.activateNode(this,t),this._globals.activate(this)},r(n,[{key:"renderScene",get:function(){return this._renderScene}},{key:"globals",get:function(){return this._globals}}]),n}(Mu),hl=F(ul.prototype,"autoReleaseAssets",[U],(function(){return!1})),ll=F(ul.prototype,"_globals",[U],(function(){return new ol})),al=ul))||al);at.Scene=wl}}}));
