System.register(["./gc-object-D18ulfCO.js","./index-Y4La_nfG.js","./touch-B157r-vS.js","./global-exports-CLZKKIY2.js"],(function(e){"use strict";var t,r,n;return{setters:[function(e){t=e._},function(e){r=e.b},function(e){n=e.E},null],execute:function(){var i,o,c;e("DeviceType",i),function(e){e[e.Other=0]="Other",e[e.Left=1]="Left",e[e.Right=2]="Right"}(i||e("DeviceType",i={})),e("XrUIPressEventType",o),function(e){e.XRUI_HOVER_ENTERED="xrui-hover-entered",e.XRUI_HOVER_EXITED="xrui-hover-exited",e.XRUI_HOVER_STAY="xrui-hover-stay",e.XRUI_CLICK="xrui-click",e.XRUI_UNCLICK="xrui-unclick"}(o||e("XrUIPressEventType",o={})),e("XrKeyboardEventType",c),function(e){e.XR_CAPS_LOCK="xr-caps-lock",e.XR_KEYBOARD_INIT="xr-keyboard-init",e.XR_KEYBOARD_INPUT="xr-keyboard-input",e.TO_LATIN="to-latin",e.TO_SYMBOL="to-symbol",e.TO_MATH_SYMBOL="to-math-symbol"}(c||e("XrKeyboardEventType",c={})),e("XrUIPressEvent",function(e){function n(){for(var t,n=arguments.length,o=new Array(n),c=0;c<n;c++)o[c]=arguments[c];return(t=e.call.apply(e,[this].concat(o))||this).deviceType=i.Other,t.hitPoint=new r,t}return t(n,e),n}(n))}}}));
