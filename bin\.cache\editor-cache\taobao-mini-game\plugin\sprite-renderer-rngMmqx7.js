System.register(["./index-Y4La_nfG.js","./gc-object-D18ulfCO.js","./global-exports-CLZKKIY2.js","./component-CsuvAQKv.js","./ui-renderer-CboX9P_t.js","./camera-component-X7pwLmnP.js","./deprecated-Bf8XgTPJ.js","./scene-ArUG4OfI.js","./factory-BOc5khhM.js","./deprecated-D5UVm7fE.js","./pipeline-state-manager-DQyhxoC_.js","./debug-view-BP17WHcy.js","./sprite-frame-n8bfYych.js","./deprecated-C_Nm0tQW.js","./model-renderer-D7qfPDfZ.js","./renderer-CZheciPr.js"],(function(e){"use strict";var t,n,o,i,r,a,s,c,m,h,l,p,u,d,_,f,C,g,y,v,S,R,b,M,E,T,F,D,I,P,O,U,w,z,N;return{setters:[function(e){t=e.U,n=e.c,o=e.d,i=e.i,r=e.J,a=e.b,s=e.t,c=e.N,m=e.B,h=e.a,l=e.s,p=e.h,u=e.r,d=e.C,_=e.o,f=e.V},function(e){C=e._,g=e.a,y=e.b,v=e.R,S=e.j},function(e){R=e.c},function(e){b=e.C},function(e){M=e.c,E=e.S,T=e.U},function(e){F=e.a,D=e.C},function(e){I=e.v},function(e){P=e.T,O=e.d},null,function(e){U=e.M},function(e){w=e.M},null,function(e){z=e.S},null,function(e){N=e.M},null],execute:function(){var j;t({RenderComponent:{newName:"UIRenderer",since:"1.2.0",removed:!0},UITransformComponent:{newName:"UITransform",since:"1.2.0",removed:!1},CanvasComponent:{newName:"Canvas",since:"1.2.0",removed:!1}}),t({UIRenderable:{newName:"UIRenderer",since:"3.0.0",removed:!0}}),t({Renderable2D:{newName:"UIRenderer",since:"3.6.0",removed:!1}});var L,x,A,G,W,H,V,k,B,X=e("R",n("cc.RenderRoot2D")(j=i(100)(j=r(M)(j=o(j=function(e){function t(){return e.apply(this,arguments)||this}C(t,e);var n=t.prototype;return n.onEnable=function(){R.director.root.batcher2D.addScreen(this)},n.onDisable=function(){R.director.root.batcher2D.removeScreen(this)},n.onDestroy=function(){R.director.root.batcher2D.removeScreen(this)},t}(b))||j)||j)||j)||j),Y=new a;!function(e){e[e.OVERLAY=0]="OVERLAY",e[e.INTERSPERSE=1]="INTERSPERSE"}(B||(B={}));var J,K=e("C",(L=n("cc.Canvas"),x=i(100),A=s(D),G=s(D),L(W=x(W=o((H=function(e){function t(){var t;return(t=e.call(this)||this)._cameraComponent=V&&V(),t._alignCanvasWithScreen=k&&k(),t._pos=new a,t._renderMode=B.OVERLAY,t._thisOnCameraResized=t._onResizeCamera.bind(v(t)),t}C(t,e);var n=t.prototype;return n.__preload=function(){var e=this.getComponent("cc.Widget");e&&e.updateAlignment(),this._cameraComponent&&(this._cameraComponent._createCamera(),this._cameraComponent.node.on(F.TARGET_TEXTURE_CHANGE,this._thisOnCameraResized)),this._onResizeCamera(),I.on("canvas-resize",this._thisOnCameraResized,this),I.on("design-resolution-changed",this._thisOnCameraResized,this)},n.onEnable=function(){e.prototype.onEnable.call(this),this._cameraComponent&&this._cameraComponent.node.on(F.TARGET_TEXTURE_CHANGE,this._thisOnCameraResized)},n.onDisable=function(){e.prototype.onDisable.call(this),this._cameraComponent&&this._cameraComponent.node.off(F.TARGET_TEXTURE_CHANGE,this._thisOnCameraResized)},n.onDestroy=function(){e.prototype.onDestroy.call(this),I.off("canvas-resize",this._thisOnCameraResized,this),I.off("design-resolution-changed",this._thisOnCameraResized,this)},n._onResizeCamera=function(){if(this._cameraComponent&&this._alignCanvasWithScreen){if(this._cameraComponent.targetTexture)this._cameraComponent.orthoHeight=c.height/2;else{var e=m.windowSize;this._cameraComponent.orthoHeight=e.height/I.getScaleY()/2}this.node.getWorldPosition(Y),this._cameraComponent.node.setWorldPosition(Y.x,Y.y,1e3)}},n._getViewPriority=function(){if(this._cameraComponent){var e,t=null==(e=this.cameraComponent)?void 0:e.priority;return this._renderMode===B.OVERLAY?t|1<<30:-1073741825&t}return 0},g(t,[{key:"renderMode",get:function(){return this._renderMode},set:function(e){this._renderMode=e,this._cameraComponent&&(this._cameraComponent.priority=this._getViewPriority())}},{key:"cameraComponent",get:function(){return this._cameraComponent},set:function(e){this._cameraComponent!==e&&(this._cameraComponent=e,this._onResizeCamera())}},{key:"alignCanvasWithScreen",get:function(){return this._alignCanvasWithScreen},set:function(e){this._alignCanvasWithScreen=e,this._onResizeCamera()}}]),t}(X),y(H.prototype,"cameraComponent",[A],Object.getOwnPropertyDescriptor(H.prototype,"cameraComponent"),H.prototype),V=h(H.prototype,"_cameraComponent",[G],(function(){return null})),k=h(H.prototype,"_alignCanvasWithScreen",[l],(function(){return!0})),W=H))||W)||W)||W));R.Canvas=K;var q,Q,Z,$,ee,te,ne,oe,ie,re,ae,se,ce=e("U",n("cc.UIComponent")(J=r(M)(J=i(110)(J=o(J=function(e){function t(){var t;return(t=e.call(this)||this)._lastParent=null,t.stencilStage=E.DISABLED,t}C(t,e);var n=t.prototype;return n.__preload=function(){this.node._uiProps.uiComp=this},n.onEnable=function(){},n.onDisable=function(){},n.onDestroy=function(){var e=this.node._uiProps;e.uiComp===this&&(e.uiComp=null)},n.postUpdateAssembler=function(){},n.markForUpdateRenderData=function(){},n.setNodeDirty=function(){},n.setTextureDirty=function(){},t}(b))||J)||J)||J)||J);p(ce.prototype,"UIComponent",[{name:"_visibility"},{name:"setVisibility"}]),u(K.prototype,"Canvas.prototype",[{name:"camera",newName:"cameraComponent.camera",customGetter:function(){var e;return null==(e=this._cameraComponent)?void 0:e.camera}},{name:"clearFlag",newName:"cameraComponent.clearFlags",customGetter:function(){return this._cameraComponent?this._cameraComponent.clearFlags:0},customSetter:function(e){this._cameraComponent&&(this._cameraComponent.clearFlags=e)}},{name:"color",newName:"cameraComponent.clearColor",customGetter:function(){return this._cameraComponent?this._cameraComponent.clearColor:d.BLACK},customSetter:function(e){this._cameraComponent&&(this._cameraComponent.clearColor=e)}},{name:"priority",newName:"cameraComponent.priority",customGetter:function(){return this._cameraComponent?this._cameraComponent.priority:0},customSetter:function(e){this._cameraComponent&&(this._cameraComponent.priority=e)}},{name:"targetTexture",newName:"cameraComponent.targetTexture",customGetter:function(){return this._cameraComponent?this._cameraComponent.targetTexture:null},customSetter:function(e){this._cameraComponent&&(this._cameraComponent.targetTexture=e)}},{name:"visibility",newName:"cameraComponent.visibility",customGetter:function(){return this._cameraComponent?this._cameraComponent.visibility:0}}]),_(M.prototype,"UITransform.prototype",[{name:"priority",suggest:"Please use setSiblingIndex to change index of the current node in its parent's children array."}]),R.UITransformComponent=M,S(M,"cc.UITransformComponent"),S(T,"cc.RenderComponent"),R.CanvasComponent=K,S(K,"cc.CanvasComponent"),R.internal.Renderable2D=T,S(T,"cc.Renderable2D"),function(e){e[e.SIMPLE=0]="SIMPLE",e[e.SLICED=1]="SLICED",e[e.TILED=2]="TILED"}(se||(se={})),e("S",(q=n("cc.SpriteRenderer"),Q=i(100),Z=s(z),q($=Q((ee=function(e){function t(){var t;return(t=e.call(this)||this)._spriteFrame=te&&te(),t._mode=ne&&ne(),t._color=oe&&oe(),t._flipX=ie&&ie(),t._flipY=re&&re(),t._size=ae&&ae(),t._model=null,t}C(t,e);var n=t.prototype;return n.onLoad=function(){this._spriteFrame&&(this._spriteFrame.mesh||this._spriteFrame.ensureMeshData(),this._spriteFrame.mesh.initialize()),this._updateModels()},n.onRestore=function(){this._updateModels(),this.enabledInHierarchy&&this._attachToScene()},n.onEnable=function(){e.prototype.onEnable.call(this),this._model||this._updateModels(),this._attachToScene()},n.onDisable=function(){this._model&&this._detachFromScene()},n.onDestroy=function(){this._model&&(R.director.root.destroyModel(this._model),this._model=null,this._models.length=0)},n._updateModels=function(){if(this._spriteFrame){var e=this._model;if(e?(e.destroy(),e.initialize(),e.node=e.transform=this.node):this._createModel(),this._model){var t=this._spriteFrame.mesh;this._model.createBoundingShape(t.struct.minPosition,t.struct.maxPosition),this._updateModelParams(),this._onUpdateLocalDescriptorSet()}}},n._createModel=function(){var e=this._model=R.director.root.createModel(U);e.visFlags=this.visibility,e.node=e.transform=this.node,this._models.length=0,this._models.push(this._model)},n._updateModelParams=function(){if(this._spriteFrame&&this._model){this._spriteFrame.ensureMeshData();var e=this._spriteFrame.mesh;this.node.hasChangedFlags|=P.POSITION,this._model.transform.hasChangedFlags|=P.POSITION;var t=e?e.renderingSubMeshes:null;if(t)for(var n=t.length,o=0;o<n;++o){var i=this.getRenderMaterial(o);i&&!i.isValid&&(i=null);var r=t[o];r&&this._model.initSubModel(o,r,i||this._getBuiltinMaterial())}this._model.enabled=!0}},n._getBuiltinMaterial=function(){return O.get("missing-material")},n._onMaterialModified=function(t,n){e.prototype._onMaterialModified.call(this,t,n),this._spriteFrame&&this._model&&this._model.inited&&this._onRebuildPSO(t,n||this._getBuiltinMaterial())},n._onRebuildPSO=function(e,t){this._model&&this._model.inited&&(this._model.setSubModelMaterial(e,t),this._onUpdateLocalDescriptorSet())},n._onUpdateLocalDescriptorSet=function(){if(this._spriteFrame&&this._model&&this._model.inited)for(var e=this._spriteFrame.getGFXTexture(),t=this._spriteFrame.getGFXSampler(),n=this._model.subModels,o=w.SAMPLER_SPRITE,i=0;i<n.length;i++){var r=n[i].descriptorSet;r.bindTexture(o,e),r.bindSampler(o,t),r.update()}},n._attachToScene=function(){if(this.node.scene&&this._model){var e=this._getRenderScene();null!==this._model.scene&&this._detachFromScene(),e.addModel(this._model)}},n._detachFromScene=function(){this._model&&this._model.scene&&this._model.scene.removeModel(this._model)},g(t,[{key:"spriteFrame",get:function(){return this._spriteFrame},set:function(e){this._spriteFrame!==e&&(this._spriteFrame,this._spriteFrame=e,this._spriteFrame&&(this._spriteFrame.ensureMeshData(),this._spriteFrame.mesh.initialize()),this._updateModels(),this.enabledInHierarchy&&this._attachToScene())}},{key:"model",get:function(){return this._model}}]),t}(N),y(ee.prototype,"spriteFrame",[Z],Object.getOwnPropertyDescriptor(ee.prototype,"spriteFrame"),ee.prototype),te=h(ee.prototype,"_spriteFrame",[l],(function(){return null})),ne=h(ee.prototype,"_mode",[l],(function(){return se.SIMPLE})),oe=h(ee.prototype,"_color",[l],(function(){return d.WHITE.clone()})),ie=h(ee.prototype,"_flipX",[l],(function(){return!1})),re=h(ee.prototype,"_flipY",[l],(function(){return!1})),ae=h(ee.prototype,"_size",[l],(function(){return new f})),$=ee))||$)||$))}}}));
