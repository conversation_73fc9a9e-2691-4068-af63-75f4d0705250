System.register(["./gc-object-D18ulfCO.js","./index-Y4La_nfG.js","./global-exports-CLZKKIY2.js","./scene-ArUG4OfI.js","./prefab-BQYc0LyR.js","./pipeline-state-manager-DQyhxoC_.js","./node-event-DTNosVQv.js","./component-CsuvAQKv.js"],(function(t){"use strict";var e,a,n,i,s,r,l,h,o,c,u,f;return{setters:[function(t){e=t._,a=t.h,n=t.w,i=t.a,s=t.b},function(t){r=t.a,l=t.c,h=t.d,o=t.t},null,function(t){c=t.M,u=t.b},null,null,null,function(t){f=t.C}],execute:function(){var m,_,M,d,g,I,p={parent:null,owner:null,subModelIdx:0},v=h,y=o;t("R",(m=l("cc.Renderer"),_=y(u),M=y([u]),m(d=v((g=function(t){function s(){var e;return(e=t.call(this)||this)._materials=I&&I(),e._materialInstances=[],e}e(s,t);var r=s.prototype;return r.getMaterial=function(t){return this.getSharedMaterial(t)},r.setMaterial=function(t,e){this.setSharedMaterial(t,e)},r.getSharedMaterial=function(t){return t<0||t>=this._materials.length?null:this._materials[t]},r.setSharedMaterial=function(t,e){if(t&&t instanceof c&&a(12012),this._materials[e]!==t){this._materials[e]=t;var n=this._materialInstances[e];n&&(n.destroy(),this._materialInstances[e]=null),this._onMaterialModified(e,this._materials[e])}},r.getMaterialInstance=function(t){if(!this._materials[t])return null;if(!this._materialInstances[t]){p.parent=this._materials[t],p.owner=this,p.subModelIdx=t;var e=new c(p);p.parent=null,p.owner=null,p.subModelIdx=0,this.setMaterialInstance(e,t)}return this._materialInstances[t]},r.setMaterialInstance=function(t,e){if("number"==typeof t){n(12007);var a=t;t=e,e=a}var i=this._materialInstances[e];t&&t.parent?t!==i&&(this._materialInstances[e]=t,this._onMaterialModified(e,t)):(t!==this._materials[e]||i)&&this.setSharedMaterial(t,e)},r.getRenderMaterial=function(t){return this._materialInstances[t]||this._materials[t]},r._onMaterialModified=function(){},r._onRebuildPSO=function(){},r._clearMaterials=function(){},i(s,[{key:"sharedMaterial",get:function(){return this.getSharedMaterial(0)}},{key:"sharedMaterials",get:function(){return this._materials},set:function(t){for(var e=0;e<t.length;e++)t[e]!==this._materials[e]&&this.setSharedMaterial(t[e],e);if(t.length<this._materials.length){for(var a=t.length;a<this._materials.length;a++)this.setSharedMaterial(null,a);this._materials.splice(t.length)}}},{key:"material",get:function(){return this.getMaterialInstance(0)},set:function(t){(1!==this._materials.length||this._materialInstances[0]||this._materials[0]!==t)&&this.setMaterialInstance(t,0)}},{key:"materials",get:function(){for(var t=0;t<this._materials.length;t++)this._materialInstances[t]=this.getMaterialInstance(t);return this._materialInstances},set:function(t){for(var e=t.length,a=this._materials.length,n=e;n<a;n++)this.setMaterialInstance(null,n);this._materials.length=e,this._materialInstances.length=e;for(var i=0;i<e;i++)this._materialInstances[i]!=t[i]&&this.setMaterialInstance(t[i],i)}}]),s}(f),s(g.prototype,"sharedMaterials",[_],Object.getOwnPropertyDescriptor(g.prototype,"sharedMaterials"),g.prototype),I=r(g.prototype,"_materials",[M],(function(){return[]})),d=g))||d)||d))}}}));
