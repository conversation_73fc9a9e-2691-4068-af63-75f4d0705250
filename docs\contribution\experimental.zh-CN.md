
# 实验性 API 规范

本节描述了在需要发布和召回 Cocos Creator 引擎实验性 API 时应当采取的措施。

## 发布实验性 API

在开发引擎功能时，你可以将一个 API 以实验性质发布。例如，如果你希望在该功能正式落地之前得到用户的反馈。

要发布一个实验性的API：

- 你 **必须** 让该 API 名称的后缀为 `_experimental`.

  > 这个要求合理，因为:

  >   - 这样一来，实验性 API 就不会占用稳定 API 的“正式”名称。

  >   - 用户可以清楚地知道这个 API 是实验性的，他们不需要检查任何警告或者查看任何文档。

- 当该 API 使用时，**可以** 在运行时附加一个信息（级别应为警告）。该信息应至多展示一次。

## 修订实验性 API

在该 API 的演进过程中，由你自行决定如何对其进行修订。**不需要保持向后兼容**。但是，你应该在发布说明中注明修改，文档中有说明更好。

## 召回实验性 API

当 API 稳定下来后（不管是否有相应的稳定版本），你 **必须** 首先将实验性的 API 变成**废弃** API，然后才能从引擎中完全删除它。

在下文中，“生命期”[`a`, `b`] 是指该 API 在版本 `a` 中发布，在版本 `b` 中稳定。

- 如果 API 的生命期只跨越了补丁版本，你可以在 **下一个次要** 版本中删除它。

  > 例如，如果该 API 存在于 [`3.7.0`, `3.7.4`]，你可以在 `3.8.0` 中删除它。

- 如果 API 的生命期只跨越了次要版本 [`X.Y.*`, `X.Z.*`]，你只允许在 `Z-Y`个次要版本之后，或在下一个主要版本中移除它。

  > 例如，如果该 API 存在于 [`3.7.1`,`3.9.0`]，你可以在 `3.11.0` 或 `4.0.0` 中删除它。

- 如果 API 的生命期跨越了主要版本。你仅可以在下一个主要版本中删除它。

  > 例如，如果该 API 存在于 [`3.0.0`, `5.6.7`]，你只能在 `6.0.0` 中删除它。

