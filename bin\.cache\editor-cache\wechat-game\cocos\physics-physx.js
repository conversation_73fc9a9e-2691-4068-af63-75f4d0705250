System.register(["./global-exports-CR3GRnjt.js","./index-DoSzW704.js","./gc-object-CKHc4SnS.js","./index-C5lmLqDW.js","./wasm-minigame-DoCiKH-Y.js","./util-D0MMXf4o.js","./deprecated-T2yM9nsJ.js","./director-DIlqD2Nd.js","./scene-7MDSMR3j.js","./collision-matrix-B7MK4XMK.js","./tuple-dictionary-By7Ih6PO.js","./prefab-DH0xadMc.js","./pipeline-state-manager-Cdpe3is6.js","./node-event-DTNosVQv.js","./component-BaGvu7EF.js","./capsule-sT4rQfGi.js","./factory-D9_8ZCqM.js","./device-manager-BvjvoelW.js","./buffer-barrier-CuX_5NUR.js","./deprecated-Ca3AjUwj.js","./debug-view-CKetkq9d.js","./mesh-C8knhDLk.js","./rendering-sub-mesh-CowWLfXC.js","./zlib.min-CyXMsivM.js","./skeleton-d3ONjcrt.js","./terrain-asset-CHGiv9LN.js","./touch-DB0AR-Sc.js"],(function(t,e){"use strict";var i,n,s,o,r,a,l,c,h,d,u,p,_,m,f,y,g,S,E,T,C,v,w,D,A,R,L,P,I,M,B,F,O,x,N,b,G,H;return{setters:[function(t){i=t.c},function(t){n=t.P,s=t.q,o=t.s},function(t){r=t.L,a=t.I,l=t.a,c=t.aH,h=t._,d=t.T},function(t){u=t.b,p=t.Q,_=t.A,m=t.C,f=t.al,y=t.am,g=t.M,S=t.f},function(t){E=t.e,T=t.i},function(t){C=t.b,v=t.T,w=t.C,D=t.c,A=t.V,R=t.a},function(t){L=t.g,P=t.G},function(t){I=t.d},function(t){M=t.T},function(t){B=t.P,F=t.a,O=t.c,x=t.E,N=t.d,b=t.e},function(t){G=t.T},null,null,null,null,function(t){H=t.c},null,null,null,null,null,null,null,null,null,null,null],execute:function(){t("loadWasmModulePhysX",(function(){return Promise.resolve()}));var k,V,U,Y,z,K,X=function(){};X.foundation=void 0,X.physics=void 0,X.cooking=void 0,X.pvd=void 0,X.queryfilterData=void 0,X.singleResult=void 0,X.mutipleResults=void 0,X.simulationCB=void 0,X.queryFilterCB=void 0,X.mutipleResultSize=12,X.singleSweepResult=void 0,X.mutipleSweepResults=void 0,function(t){t[t.QUERY_FILTER=1]="QUERY_FILTER",t[t.QUERY_CHECK_TRIGGER=2]="QUERY_CHECK_TRIGGER",t[t.QUERY_SINGLE_HIT=4]="QUERY_SINGLE_HIT",t[t.DETECT_TRIGGER_EVENT=8]="DETECT_TRIGGER_EVENT",t[t.DETECT_CONTACT_EVENT=16]="DETECT_CONTACT_EVENT",t[t.DETECT_CONTACT_POINT=32]="DETECT_CONTACT_POINT",t[t.DETECT_CONTACT_CCD=64]="DETECT_CONTACT_CCD"}(k||(k={})),function(t){t[t.ePOSITION=1]="ePOSITION",t[t.eNORMAL=2]="eNORMAL",t[t.eUV=8]="eUV",t[t.eASSUME_NO_INITIAL_OVERLAP=16]="eASSUME_NO_INITIAL_OVERLAP",t[t.eMESH_MULTIPLE=32]="eMESH_MULTIPLE",t[t.eMESH_ANY=64]="eMESH_ANY",t[t.eMESH_BOTH_SIDES=128]="eMESH_BOTH_SIDES",t[t.ePRECISE_SWEEP=256]="ePRECISE_SWEEP",t[t.eMTD=512]="eMTD",t[t.eFACE_INDEX=1024]="eFACE_INDEX",t[t.eDEFAULT=t.ePOSITION|t.eNORMAL|t.eFACE_INDEX]="eDEFAULT",t[t.eMODIFIABLE_FLAGS=t.eMESH_MULTIPLE|t.eMESH_BOTH_SIDES|t.eASSUME_NO_INITIAL_OVERLAP|t.ePRECISE_SWEEP]="eMODIFIABLE_FLAGS"}(V||(V={})),function(t){t[t.eSTATIC=1]="eSTATIC",t[t.eDYNAMIC=2]="eDYNAMIC",t[t.ePREFILTER=4]="ePREFILTER",t[t.ePOSTFILTER=8]="ePOSTFILTER",t[t.eANY_HIT=16]="eANY_HIT",t[t.eNO_BLOCK=32]="eNO_BLOCK",t[t.eRESERVED=32768]="eRESERVED"}(U||(U={})),function(t){t[t.eSOLVE_CONTACT=1]="eSOLVE_CONTACT",t[t.eMODIFY_CONTACTS=2]="eMODIFY_CONTACTS",t[t.eNOTIFY_TOUCH_FOUND=4]="eNOTIFY_TOUCH_FOUND",t[t.eNOTIFY_TOUCH_PERSISTS=8]="eNOTIFY_TOUCH_PERSISTS",t[t.eNOTIFY_TOUCH_LOST=16]="eNOTIFY_TOUCH_LOST",t[t.eNOTIFY_TOUCH_CCD=32]="eNOTIFY_TOUCH_CCD",t[t.eNOTIFY_THRESHOLD_FORCE_FOUND=64]="eNOTIFY_THRESHOLD_FORCE_FOUND",t[t.eNOTIFY_THRESHOLD_FORCE_PERSISTS=128]="eNOTIFY_THRESHOLD_FORCE_PERSISTS",t[t.eNOTIFY_THRESHOLD_FORCE_LOST=256]="eNOTIFY_THRESHOLD_FORCE_LOST",t[t.eNOTIFY_CONTACT_POINTS=512]="eNOTIFY_CONTACT_POINTS",t[t.eDETECT_DISCRETE_CONTACT=1024]="eDETECT_DISCRETE_CONTACT",t[t.eDETECT_CCD_CONTACT=2048]="eDETECT_CCD_CONTACT",t[t.ePRE_SOLVER_VELOCITY=4096]="ePRE_SOLVER_VELOCITY",t[t.ePOST_SOLVER_VELOCITY=8192]="ePOST_SOLVER_VELOCITY",t[t.eCONTACT_EVENT_POSE=16384]="eCONTACT_EVENT_POSE",t[t.eNEXT_FREE=32768]="eNEXT_FREE",t[t.eCONTACT_DEFAULT=1025]="eCONTACT_DEFAULT",t[t.eTRIGGER_DEFAULT=1044]="eTRIGGER_DEFAULT"}(Y||(Y={})),function(t){t[t.eREMOVED_SHAPE_0=1]="eREMOVED_SHAPE_0",t[t.eREMOVED_SHAPE_1=2]="eREMOVED_SHAPE_1",t[t.eACTOR_PAIR_HAS_FIRST_TOUCH=4]="eACTOR_PAIR_HAS_FIRST_TOUCH",t[t.eACTOR_PAIR_LOST_TOUCH=8]="eACTOR_PAIR_LOST_TOUCH",t[t.eINTERNAL_HAS_IMPULSES=16]="eINTERNAL_HAS_IMPULSES",t[t.eINTERNAL_CONTACTS_ARE_FLIPPED=32]="eINTERNAL_CONTACTS_ARE_FLIPPED"}(z||(z={})),function(t){t[t.eREMOVED_SHAPE_TRIGGER=1]="eREMOVED_SHAPE_TRIGGER",t[t.eREMOVED_SHAPE_OTHER=2]="eREMOVED_SHAPE_OTHER",t[t.eNEXT_FREE=4]="eNEXT_FREE"}(K||(K={}));var $={},J=i._global,W=!!J.PHYSX;function q(t){J.PhysX=t,t.EPSILON=.001,t.MULTI_THREAD=!1,t.SUB_THREAD_COUNT=1,t.CACHE_MAT={},t.IMPL_PTR={},t.MESH_CONVEX={},t.MESH_STATIC={},t.TERRAIN_STATIC={}}function Q(t){t.VECTOR_MAT=new t.PxMaterialVector,t.MeshScale=t.PxMeshScale,t.ShapeFlag=t.PxShapeFlag,t.ActorFlag=t.PxActorFlag,t.ForceMode=t.PxForceMode,t.CombineMode=t.PxCombineMode,t.BoxGeometry=t.PxBoxGeometry,t.QueryHitType=t.PxQueryHitType,t.RigidBodyFlag=t.PxRigidBodyFlag,t.PlaneGeometry=t.PxPlaneGeometry,t.SphereGeometry=t.PxSphereGeometry,t.CapsuleGeometry=t.PxCapsuleGeometry,t.ConvexMeshGeometry=t.PxConvexMeshGeometry,t.D6Motion=t.PxD6Motion,t.D6Axis=t.PxD6Axis,t.D6Drive=t.PxD6Drive,t.D6JointDrive=t.PxD6JointDrive,t.LinearLimitPair=t.PxJointLinearLimitPair,t.AngularLimitPair=t.PxJointAngularLimitPair,t.TriangleMeshGeometry=t.PxTriangleMeshGeometry,t.RigidDynamicLockFlag=t.PxRigidDynamicLockFlag,t.TolerancesScale=t.PxTolerancesScale,t.RevoluteJointFlags={eLIMIT_ENABLED:1,eDRIVE_ENABLED:2,eDRIVE_FREESPIN:4},t.JointAngularLimitPair=t.PxJointAngularLimitPair,t.createRevoluteJoint=function(e,i,n,s){return t.PxRevoluteJointCreate($.physics,e,i,n,s)},t.createFixedConstraint=function(e,i,n,s){return t.PxFixedJointCreate($.physics,e,i,n,s)},t.createSphericalJoint=function(e,i,n,s){return t.PxSphericalJointCreate($.physics,e,i,n,s)},t.createD6Joint=function(e,i,n,s){return t.PxD6JointCreate($.physics,e,i,n,s)}}L.onPostInfrastructureInitDelegate.add((function(){return E().then((function(){return Promise.all([e.import("./physx.release.wasm-DYQcUbMN.js").then((function(t){return t.p})),e.import("./physx.release.wasm-C6LLqpgQ.js")]).then((function(t){return e=t[0].default,i=t[1].default,J.PhysX=J.PHYSX?J.PHYSX:e,null!=J.PhysX?J.PhysX({instantiateWasm:function(t,e){return T(i,t).then((function(t){e(t.instance,t.module)}))}}).then((function(t){console.debug("[PHYSICS]:",(W?"External":"Internal")+" PhysX wasm libs loaded."),Q(t),q(t),$=t}),(function(t){console.error("[PHYSICS]:","PhysX wasm load failed: "+t)})):(console.error("[PHYSICS]:","Failed to load PhysX wasm libs, package may be not found."),new Promise((function(t){t()})));var e,i}))})).catch((function(t){r(t)}))}));var j={x:0,y:0,z:0},Z={x:0,y:0,z:0,w:1},tt={translation:j,rotation:Z,p:j,q:Z};function et(t,e){t.b=e>>16&255,t.g=e>>8&255,t.r=255&e,t.a=255}var it=tt;function nt(t,e){e&&e.$$&&($.IMPL_PTR[e.$$.ptr]=t)}function st(t,e){e&&e.$$&&($.IMPL_PTR[e.$$.ptr]=null,delete $.IMPL_PTR[e.$$.ptr])}function ot(t){return $.IMPL_PTR[t.$$.ptr]}function rt(t,e){return u.copy(it.translation,t),p.copy(it.rotation,e),it}function at(t,e){return u.copy(tt.p,t),p.copy(tt.q,e),tt}function lt(t,e){t.addActor(e,null)}function ct(t,e,i){t.setActors(e,i)}function ht(t,e){t.setMassAndUpdateInertia(e)}function dt(t,e){var i=t.worldPosition,n=t.worldRotation;ut(e,i)&&pt(e,n)||(t.setWorldPosition(e.translation),t.setWorldRotation(e.rotation))}function ut(t,e){return u.equals(t.translation,e,$.EPSILON)}function pt(t,e){return p.equals(t.rotation,e,$.EPSILON)}function _t(t,e,i,n){t?e.applyImpulse(i,n):e.applyLocalImpulse(i,n)}function mt(t,e,i,n){t?e.applyForce(i,n):e.applyLocalForce(i,n)}function ft(t,e){t.addTorque(e)}function yt(t,e,i){for(var n=C(t),s=n.length,o=new $.PxVec3Vector,r=0;r<s;r+=3)o.push_back({x:n[r],y:n[r+1],z:n[r+2]});var a=e.createConvexMesh(o,i);return o.delete(),a}function gt(t,e){return e?new $.PxConvexMeshGeometryFlags(t):new $.PxMeshGeometryFlags(t)}function St(t,e,i,n){for(var s=t.length,o=e.length,r=new $.PxVec3Vector,a=0;a<s;a+=3)r.push_back({x:t[a],y:t[a+1],z:t[a+2]});for(var l=new $.PxU16Vector,c=0;c<o;c+=3)l.push_back(e[c]),l.push_back(e[c+1]),l.push_back(e[c+2]);var h=i.createTriMeshExt(r,l,n);return r.delete(),l.delete(),h}function Et(t,e,i,n){for(var s=t.getVertexCountI(),o=t.getVertexCountJ(),r=new $.PxHeightFieldSampleVector,a=0;a<s;a++)for(var l=0;l<o;l++){var c=new $.PxHeightFieldSample;c.height=t.getHeight(a,l)/e,r.push_back(c)}return i.createHeightFieldExt(s,o,r,n)}function Tt(t,e,i,n,s){return new $.PxHeightFieldGeometry(t,new $.PxMeshGeometryFlags(e),i,n,s)}function Ct(t,e,i,n,s){var o=i.maxDistance,r=V.ePOSITION|V.eNORMAL,a=k.QUERY_FILTER|(i.queryTrigger?0:k.QUERY_CHECK_TRIGGER),l=U.eSTATIC|U.eDYNAMIC|U.ePREFILTER|U.eNO_BLOCK,c=X.queryfilterData,h=X.queryFilterCB,d=X.mutipleResults;c.setWords(i.mask>>>0,0),c.setWords(a,3),c.setFlags(l);var u=d,p=t.scene.raycastMultiple(e.o,e.d,o,r,u,u.size(),c,h,null);if(p>0){for(var _=0;_<p;_++){var m=u.get(_),f=ot(m.getShape()).collider,y=n.add();y._assign(m.position,m.distance,f,m.normal),s.push(y)}return!0}return-1===p&&console.error("not enough memory."),!1}function vt(t,e,i,n){i.maxDistance;var s=V.ePOSITION|V.eNORMAL,o=k.QUERY_FILTER|(i.queryTrigger?0:k.QUERY_CHECK_TRIGGER)|k.QUERY_SINGLE_HIT,r=U.eSTATIC|U.eDYNAMIC|U.ePREFILTER,a=X.queryfilterData,l=X.queryFilterCB;a.setWords(i.mask>>>0,0),a.setWords(o,3),a.setFlags(r);var c=X.singleResult;if(t.scene.raycastSingle(e.o,e.d,i.maxDistance,s,c,a,l,null)){var h=ot(c.getShape()).collider;return n._assign(c.position,c.distance,h,c.normal),!0}return!1}function wt(t,e,i,n,s,o,r){var a=s.maxDistance,l=V.ePOSITION|V.eNORMAL,c=k.QUERY_FILTER|(s.queryTrigger?0:k.QUERY_CHECK_TRIGGER),h=U.eSTATIC|U.eDYNAMIC|U.ePREFILTER|U.eNO_BLOCK,d=X.queryfilterData,u=X.queryFilterCB,p=X.mutipleSweepResults;d.setWords(s.mask>>>0,0),d.setWords(c,3),d.setFlags(h);var _=p,m=t.scene.sweepMultiple(i,rt(e.o,n),e.d,a,l,_,_.size(),d,u,null,0);if(m>0){for(var f=0;f<m;f++){var y=_.get(f),g=ot(y.getShape()).collider,S=o.add();S._assign(y.position,y.distance,g,y.normal),r.push(S)}return!0}return-1===m&&console.error("not enough memory."),!1}function Dt(t,e,i,n,s,o){var r=s.maxDistance,a=V.ePOSITION|V.eNORMAL,l=k.QUERY_FILTER|(s.queryTrigger?0:k.QUERY_CHECK_TRIGGER)|k.QUERY_SINGLE_HIT,c=U.eSTATIC|U.eDYNAMIC|U.ePREFILTER,h=X.queryfilterData;h.setWords(s.mask>>>0,0),h.setWords(l,3),h.setFlags(c);var d=X.queryFilterCB,u=X.singleSweepResult;if(t.scene.sweepSingle(i,rt(e.o,n),e.d,r,a,u,h,d,null,0)){var p=ot(u.getShape()).collider;return o._assign(u.position,u.distance,p,u.normal),!0}return!1}function At(t){if(!X.foundation){var e=$.PX_PHYSICS_VERSION,i=new $.PxDefaultAllocator,n=new $.PxDefaultErrorCallback,s=X.foundation=$.PxCreateFoundation(e,i,n);X.pvd=null;var o=new $.PxTolerancesScale;X.physics=$.physics=$.PxCreatePhysics(e,s,o,!1,X.pvd),X.cooking=$.PxCreateCooking(e,s,new $.PxCookingParams(o)),$.PxInitExtensions(X.physics,X.pvd),X.singleResult=new $.PxRaycastHit,(X.mutipleResults=new $.PxRaycastHitVector).resize(X.mutipleResultSize,X.singleResult),X.queryfilterData=new $.PxQueryFilterData,X.simulationCB=$.PxSimulationEventCallback.implement(t.callback.eventCallback),X.queryFilterCB=$.PxQueryFilterCallback.implement(t.callback.queryCallback),X.singleSweepResult=new $.PxSweepHit,(X.mutipleSweepResults=new $.PxSweepHitVector).resize(X.mutipleResultSize,X.singleSweepResult)}var r=$.getDefaultSceneDesc(X.physics.getTolerancesScale(),0,X.simulationCB);t.scene=X.physics.createScene(r),t.scene.setVisualizationParameter($.PxVisualizationParameter.eSCALE,1),t.controllerManager=$.PxCreateControllerManager(t.scene,!1)}function Rt(t,e){u.copy(e,t.position)}function Lt(t,e){u.copy(e,t.normal)}function Pt(t,e){return $.getGContacts().get(t+e)}var It=function(){function t(e,i){this.id=void 0,this.node=void 0,this.wrappedWorld=void 0,this.wrappedShapes=[],this.wrappedJoints0=[],this.wrappedJoints1=[],this._index=-1,this._ref=0,this._isStatic=!1,this._isKinematic=!1,this._dynamicActor=void 0,this._staticActor=void 0,this._wrappedBody=null,this._filterData=void 0,this.id=t.idCounter++,this.node=e,this.wrappedWorld=i,this._filterData={word0:1,word1:1,word2:1,word3:0}}t.getSharedBody=function(e,i,s){var o,r=e.uuid;if(t.sharedBodesMap.has(r)?o=t.sharedBodesMap.get(r):((o=new t(e,i)).filterData.word0=B.DEFAULT,o.filterData.word1=n.instance.collisionMatrix[B.DEFAULT],t.sharedBodesMap.set(e.uuid,o)),s){o._wrappedBody=s;var a=s.rigidBody.group,l=n.instance.collisionMatrix[a];o.filterData.word0=a,o.filterData.word1=l}return o};var e=t.prototype;return e._initActor=function(){var t=this._isStatic,e=this.wrappedBody;e?e.rigidBody.type===F.STATIC?(this._isStatic=!0,this._isKinematic=!1,this._initStaticActor()):(this._isStatic=!1,this._initDynamicActor()):(this._isStatic=!0,this._isKinematic=!1,this._initStaticActor()),t!==this._isStatic&&this._switchActor(t)},e._initStaticActor=function(){if(!this._staticActor){var t=rt(this.node.worldPosition,this.node.worldRotation);this._staticActor=X.physics.createRigidStatic(t),this._staticActor.setActorFlag($.ActorFlag.eVISUALIZATION,!0),this._staticActor.$$&&($.IMPL_PTR[this._staticActor.$$.ptr]=this)}},e._initDynamicActor=function(){if(!this._dynamicActor){var t=rt(this.node.worldPosition,this.node.worldRotation);this._dynamicActor=X.physics.createRigidDynamic(t),this._dynamicActor.$$&&($.IMPL_PTR[this._dynamicActor.$$.ptr]=this);var e=this.wrappedBody;if(e){var i=e.rigidBody;this._dynamicActor.setMass(i.mass),this._dynamicActor.setActorFlag($.ActorFlag.eVISUALIZATION,!0),this._dynamicActor.setActorFlag($.ActorFlag.eDISABLE_GRAVITY,!i.useGravity),this.setLinearDamping(i.linearDamping),this.setAngularDamping(i.angularDamping),this.setRigidBodyFlag($.RigidBodyFlag.eKINEMATIC,i.isKinematic);var n=i.linearFactor;this._dynamicActor.setRigidDynamicLockFlag($.RigidDynamicLockFlag.eLOCK_LINEAR_X,!n.x),this._dynamicActor.setRigidDynamicLockFlag($.RigidDynamicLockFlag.eLOCK_LINEAR_Y,!n.y),this._dynamicActor.setRigidDynamicLockFlag($.RigidDynamicLockFlag.eLOCK_LINEAR_Z,!n.z);var s=i.angularFactor;this._dynamicActor.setRigidDynamicLockFlag($.RigidDynamicLockFlag.eLOCK_ANGULAR_X,!s.x),this._dynamicActor.setRigidDynamicLockFlag($.RigidDynamicLockFlag.eLOCK_ANGULAR_Y,!s.y),this._dynamicActor.setRigidDynamicLockFlag($.RigidDynamicLockFlag.eLOCK_ANGULAR_Z,!s.z)}}},e._switchActor=function(t){if(this._staticActor&&this._dynamicActor){var e=t?this._staticActor:this._dynamicActor,i=t?this._dynamicActor:this._staticActor;this._index>=0&&(this.wrappedWorld.scene.removeActor(e,!1),lt(this.wrappedWorld.scene,i));for(var n=0;n<this.wrappedShapes.length;n++){var s=this.wrappedShapes[n];e.detachShape(s.impl,!1),i.attachShape(s.impl)}t&&ht(this._dynamicActor,this._wrappedBody.rigidBody.mass)}},e.addShape=function(t){this.wrappedShapes.indexOf(t)<0&&(t.setIndex(this.wrappedShapes.length),t.updateFilterData(this._filterData),this.impl.attachShape(t.impl),this.wrappedShapes.push(t),t.collider.isTrigger||this.isDynamic&&ht(this.impl,this._wrappedBody.rigidBody.mass))},e.removeShape=function(t){var e=this.wrappedShapes.indexOf(t);e>=0&&(t.setIndex(-1),this.impl.detachShape(t.impl,!0),a(this.wrappedShapes,e),t.collider.isTrigger||this.isDynamic&&ht(this.impl,this._wrappedBody.rigidBody.mass))},e.addJoint=function(t,e){e?this.wrappedJoints1.indexOf(t)<0&&this.wrappedJoints1.push(t):this.wrappedJoints0.indexOf(t)<0&&this.wrappedJoints0.push(t)},e.removeJoint=function(t,e){if(e){var i=this.wrappedJoints1.indexOf(t);i>=0&&a(this.wrappedJoints1,i)}else{var n=this.wrappedJoints0.indexOf(t);n>=0&&a(this.wrappedJoints0,n)}},e.setLinearDamping=function(t){if(this._dynamicActor){var e=n.instance.fixedTimeStep;this._dynamicActor.setLinearDamping((1-Math.pow(1-t,e))/e)}},e.setAngularDamping=function(t){if(this._dynamicActor){var e=n.instance.fixedTimeStep;this._dynamicActor.setAngularDamping((1-Math.pow(1-t,e))/e)}},e.setMass=function(t){t<=0||this.isDynamic&&ht(this.impl,t)},e.setType=function(t){if(this._initActor(),!this.isStatic)switch(t){case F.DYNAMIC:this.setRigidBodyFlag($.RigidBodyFlag.eKINEMATIC,!1);break;case F.KINEMATIC:default:this.setRigidBodyFlag($.RigidBodyFlag.eKINEMATIC,!0)}},e.setRigidBodyFlag=function(t,e){t===$.RigidBodyFlag.eKINEMATIC&&(this._isKinematic=e),this.impl.setRigidBodyFlag(t,e)},e.syncSceneToPhysics=function(){var t=this.node;if(t.hasChangedFlags)if(t.hasChangedFlags&M.SCALE&&this.syncScale(),this._isKinematic){var e=rt(t.worldPosition,t.worldRotation);this.impl.setKinematicTarget(e)}else{var i=at(t.worldPosition,t.worldRotation);this.impl.setGlobalPose(i,!0)}},e.syncSceneWithCheck=function(){var t=this.node;if(t.hasChangedFlags){t.hasChangedFlags&M.SCALE&&this.syncScale();var e=t.worldPosition,i=t.worldRotation,n=this.impl.getGlobalPose();if(!ut(n,e)||!pt(n,i))if(this._isKinematic){var s=rt(t.worldPosition,t.worldRotation);this.impl.setKinematicTarget(s)}else{var o=at(t.worldPosition,t.worldRotation);this.impl.setGlobalPose(o,!0)}}},e.syncPhysicsToScene=function(){var t,e;this.isDynamic&&(t=this._dynamicActor,e=this.node,t.isSleeping()||dt(e,t.getGlobalPose()))},e.syncScale=function(){for(var t=0;t<this.wrappedShapes.length;t++)this.wrappedShapes[t].updateScale();for(var e=0;e<this.wrappedJoints0.length;e++)this.wrappedJoints0[e].updateScale0();for(var i=0;i<this.wrappedJoints1.length;i++)this.wrappedJoints1[i].updateScale1()},e.setGroup=function(t){t>>>=0,this._filterData.word0=t,this.updateFilterData()},e.getGroup=function(){return this._filterData.word0},e.addGroup=function(t){t>>>=0,this._filterData.word0|=t,this.updateFilterData()},e.removeGroup=function(t){t>>>=0,this._filterData.word0&=~t,this.updateFilterData()},e.setMask=function(t){t>>>=0,this._filterData.word1=t,this.updateFilterData()},e.getMask=function(){return this._filterData.word1},e.addMask=function(t){t>>>=0,this._filterData.word1|=t,this.updateFilterData()},e.removeMask=function(t){t>>>=0,this._filterData.word1&=~t,this.updateFilterData()},e.updateFilterData=function(){for(var t=0;t<this.wrappedShapes.length;t++)this.wrappedShapes[t].updateFilterData(this._filterData)},e.clearForces=function(){this._isStatic||this._isKinematic||(this.impl.clearForce($.ForceMode.eFORCE),this.impl.clearForce($.ForceMode.eIMPULSE),this.impl.clearTorque($.ForceMode.eFORCE),this.impl.clearTorque($.ForceMode.eIMPULSE))},e.clearVelocity=function(){this._isStatic||this._isKinematic||(this.impl.setLinearVelocity(u.ZERO,!1),this.impl.setAngularVelocity(u.ZERO,!1))},e.destroy=function(){this._dynamicActor&&(this._dynamicActor.$$&&($.IMPL_PTR[this._dynamicActor.$$.ptr]=null,delete $.IMPL_PTR[this._dynamicActor.$$.ptr]),this._dynamicActor.release(),this._dynamicActor=null),this._staticActor&&(this._staticActor.$$&&($.IMPL_PTR[this._staticActor.$$.ptr]=null,delete $.IMPL_PTR[this._staticActor.$$.ptr]),this._staticActor.release(),this._staticActor=null),t.sharedBodesMap.delete(this.node.uuid)},l(t,[{key:"isStatic",get:function(){return this._isStatic}},{key:"isKinematic",get:function(){return this._isKinematic}},{key:"isDynamic",get:function(){return!this._isStatic&&!this._isKinematic}},{key:"wrappedBody",get:function(){return this._wrappedBody}},{key:"filterData",get:function(){return this._filterData}},{key:"isInScene",get:function(){return-1!==this._index}},{key:"impl",get:function(){return this._initActor(),this.isStatic?this._staticActor:this._dynamicActor}},{key:"reference",set:function(t){this._ref=t?this._ref+1:this._ref-1,0===this._ref&&this.destroy()}},{key:"enabled",set:function(t){if(t)this._index<0&&(this._index=this.wrappedWorld.wrappedBodies.length,this.wrappedWorld.addActor(this));else if(this._index>=0){var e=this.wrappedShapes,i=this.wrappedBody;(0===e.length&&null==i||0===e.length&&null!=i&&!i.isEnabled)&&(this._index=-1,this.clearForces(),this.clearVelocity(),this.wrappedWorld.removeActor(this))}}}]),t}();It.idCounter=0,It.sharedBodesMap=new Map;var Mt,Bt=new p,Ft=function(){function t(t){this.impl=null,this.event=void 0,this.colliderA=void 0,this.colliderB=void 0,this.event=t}var e=t.prototype;return e.getLocalPointOnA=function(t){Rt(this.impl,t,this.event.impl),u.subtract(t,t,this.colliderA.node.worldPosition)},e.getLocalPointOnB=function(t){Rt(this.impl,t,this.event.impl),u.subtract(t,t,this.colliderB.node.worldPosition)},e.getWorldPointOnA=function(t){Rt(this.impl,t,this.event.impl)},e.getWorldPointOnB=function(t){Rt(this.impl,t,this.event.impl)},e.getLocalNormalOnA=function(t){this.getWorldNormalOnA(t),p.conjugate(Bt,this.colliderA.node.worldRotation),u.transformQuat(t,t,Bt)},e.getLocalNormalOnB=function(t){this.getWorldNormalOnB(t),p.conjugate(Bt,this.colliderB.node.worldRotation),u.transformQuat(t,t,Bt)},e.getWorldNormalOnA=function(t){Lt(this.impl,t,this.event.impl),this.isBodyA||u.negate(t,t)},e.getWorldNormalOnB=function(t){Lt(this.impl,t,this.event.impl)},l(t,[{key:"isBodyA",get:function(){return this.colliderA.uuid===this.event.selfCollider.uuid}}]),t}();!function(t){t[t.SPHERE=0]="SPHERE",t[t.BOX=1]="BOX",t[t.CAPSULE=2]="CAPSULE",t[t.CYLINDER=3]="CYLINDER",t[t.CONE=4]="CONE",t[t.PLANE=5]="PLANE",t[t.TERRAIN=6]="TERRAIN",t[t.MESH=7]="MESH"}(Mt||(Mt={}));var Ot=function(){function t(e){this.id=void 0,this.type=void 0,this._impl=null,this._collider=null,this._flags=void 0,this._sharedBody=void 0,this._rotation=new p(0,0,0,1),this._index=-1,this._word3=0,this._isEnabled=!1,this.type=e,this.id=t.idCounter++}var e=t.prototype;return e.initialize=function(t){var e;this._collider=t,this._flags=(e=(t.isTrigger?$.PxShapeFlag.eTRIGGER_SHAPE.value:$.PxShapeFlag.eSIMULATION_SHAPE.value)|$.PxShapeFlag.eSCENE_QUERY_SHAPE.value|$.PxShapeFlag.eVISUALIZATION.value,new $.PxShapeFlags(e)),this._sharedBody=n.instance.physicsWorld.getSharedBody(t.node),this._sharedBody.reference=!0,this.onComponentSet(),nt(this,this._impl)},e.setIndex=function(t){this._index=t},e.onComponentSet=function(){},e.updateScale=function(){},e.onLoad=function(){this.setMaterial(this._collider.sharedMaterial),this.setCenter(this._collider.center)},e.onEnable=function(){this.addToBody(),this._isEnabled=!0,this._sharedBody.enabled=!0},e.onDisable=function(){this.removeFromBody(),this._isEnabled=!1,this._sharedBody.enabled=!1},e.onDestroy=function(){this._sharedBody.reference=!1,this._impl&&(st(0,this._impl),this._impl.release(),this._impl=null),this._flags=null,this._collider=null},e.setMaterial=function(t){var e,i=this.getSharedMaterial(t);this._impl.setMaterials((e=i,$.VECTOR_MAT.size()>0?$.VECTOR_MAT.set(0,e):$.VECTOR_MAT.push_back(e),$.VECTOR_MAT))},e.getSharedMaterial=function(t){var e=null==t?n.instance.defaultMaterial:t;if(!$.CACHE_MAT[e.id]){var i=X.physics.createMaterial(e.friction,e.friction,e.restitution);return i.setFrictionCombineMode($.CombineMode.eMULTIPLY),i.setRestitutionCombineMode($.CombineMode.eMULTIPLY),$.CACHE_MAT[e.id]=i,i}var s=$.CACHE_MAT[e.id];return s.setStaticFriction(e.friction),s.setDynamicFriction(e.friction),s.setRestitution(e.restitution),s},e.setAsTrigger=function(t){t?(this._impl.setFlag($.ShapeFlag.eSIMULATION_SHAPE,!t),this._impl.setFlag($.ShapeFlag.eTRIGGER_SHAPE,t)):(this._impl.setFlag($.ShapeFlag.eTRIGGER_SHAPE,t),this._impl.setFlag($.ShapeFlag.eSIMULATION_SHAPE,!t)),this._index>=0&&(this._sharedBody.removeShape(this),this._sharedBody.addShape(this))},e.setCenter=function(t){var e=tt.translation,i=tt.rotation;u.multiply(e,t,this._collider.node.worldScale),p.copy(i,this._rotation);var n=rt(e,i);this._impl.setLocalPose(n)},e.getAABB=function(t){var e,i,n,s;e=this.impl,i=this._sharedBody.impl,n=t,s=e.getWorldBounds(i,1),_.fromPoints(n,s.minimum,s.maximum)},e.getBoundingSphere=function(t){_.toBoundingSphere(t,this._collider.worldBounds)},e.setGroup=function(t){this._sharedBody.setGroup(t)},e.getGroup=function(){return this._sharedBody.getGroup()},e.addGroup=function(t){this._sharedBody.addGroup(t)},e.removeGroup=function(t){this._sharedBody.removeGroup(t)},e.setMask=function(t){this._sharedBody.setMask(t)},e.getMask=function(){return this._sharedBody.getMask()},e.addMask=function(t){this._sharedBody.addMask(t)},e.removeMask=function(t){this._sharedBody.removeMask(t)},e.updateFilterData=function(t){this._word3=k.DETECT_CONTACT_CCD,this._collider.needTriggerEvent&&(this._word3|=k.DETECT_TRIGGER_EVENT),this._collider.needCollisionEvent&&(this._word3|=k.DETECT_CONTACT_EVENT|k.DETECT_CONTACT_POINT),t.word3=this._word3,this.setFilerData(t)},e.updateEventListener=function(){this._sharedBody&&this.updateFilterData(this._sharedBody.filterData)},e.updateByReAdd=function(){this._isEnabled&&(this.removeFromBody(),this.addToBody())},e.setFilerData=function(t){this._impl.setQueryFilterData(t),this._impl.setSimulationFilterData(t)},e.addToBody=function(){this._sharedBody.addShape(this)},e.removeFromBody=function(){this._sharedBody.removeShape(this)},l(t,[{key:"impl",get:function(){return this._impl}},{key:"collider",get:function(){return this._collider}},{key:"attachedRigidBody",get:function(){return null}}],[{key:"MESH_SCALE",get:function(){return this._MESH_SCALE||(this._MESH_SCALE=new $.MeshScale(u.ZERO,p.IDENTITY)),this._MESH_SCALE}}]),t}();Ot._MESH_SCALE=void 0,Ot.idCounter=0;var xt=new u(0,0,0),Nt=new u(0,0,0),bt=function(){function t(){this._isEnabled=!1,this._impl=null,this._comp=null,this._pxCollisionFlags=0,this._filterData=void 0,this._queryFilterCB=null,this._word3=0,this._overlapRecovery=!0,this.id=void 0,this.id=t.idCounter++,this._filterData={word0:1,word1:1,word2:1,word3:0}}var e=t.prototype;return e.onComponentSet=function(){},e.create=function(){},e.updateScale=function(){},e.initialize=function(e){this._comp=e,this._queryFilterCB=$.PxQueryFilterCallback.implement(t.queryCallback);var i=this._comp.group;this._filterData.word0=this._comp.group;var s=n.instance.collisionMatrix[i];return this._filterData.word1=s,this.onComponentSet(),null!=this._impl||(r("[Physics]: Initialize PhysXCharacterController Failed"),!1)},e.onEnable=function(){this._isEnabled=!0,this._impl||this.onComponentSet(),this.setDetectCollisions(!0),this.setOverlapRecovery(!0),n.instance.physicsWorld.addCCT(this)},e.onDisable=function(){this._isEnabled=!1,n.instance.physicsWorld.removeCCT(this),this.onDestroy()},e.onLoad=function(){},e.release=function(){if(this._impl){if(this._impl.$$){$.IMPL_PTR[this._impl.$$.ptr]=null,delete $.IMPL_PTR[this._impl.$$.ptr];var t=this._impl.getShape().$$.ptr;$.IMPL_PTR[t]=null,delete $.IMPL_PTR[t]}this._impl.release(),this._impl=null}},e.onDestroy=function(){this.release()},e.getPosition=function(t){this._impl&&u.copy(t,this._impl.getPosition())},e.setPosition=function(t){this._impl&&(this._impl.setPosition(t),this.syncPhysicsToScene())},e.setContactOffset=function(t){this._impl&&this._impl.setContactOffset(t)},e.setStepOffset=function(t){this._impl&&this._impl.setStepOffset(t)},e.setSlopeLimit=function(t){this._impl&&this._impl.setSlopeLimit(Math.cos(c(t)))},e.setDetectCollisions=function(t){this._impl&&this._impl.setCollision(t)},e.setQuery=function(t){this._impl&&this._impl.setQuery(t)},e.setOverlapRecovery=function(t){this._overlapRecovery=t},e.onGround=function(){return(4&this._pxCollisionFlags)>0},e.syncSceneToPhysics=function(){var t=this.characterController.node;t.hasChangedFlags&&(t.hasChangedFlags&M.SCALE&&this.syncScale(),t.hasChangedFlags&M.POSITION&&(u.add(xt,t.worldPosition,this.scaledCenter),this.setPosition(xt)))},e.syncPhysicsToScene=function(){this.getPosition(xt),xt.subtract(this.scaledCenter),this._comp.node.setWorldPosition(xt)},e.syncScale=function(){this.updateScale()},e.move=function(t,e,i){this._isEnabled&&(n.instance.physicsWorld.controllerManager.setOverlapRecoveryModule(this._overlapRecovery),this._pxCollisionFlags=this._impl.move(t,e,i,this.filterData,this.queryFilterCB))},e.setGroup=function(t){t>>>=0,this._filterData.word0=t,this.updateFilterData()},e.getGroup=function(){return this._filterData.word0},e.addGroup=function(t){t>>>=0,this._filterData.word0|=t,this.updateFilterData()},e.removeGroup=function(t){t>>>=0,this._filterData.word0&=~t,this.updateFilterData()},e.setMask=function(t){t>>>=0,this._filterData.word1=t,this.updateFilterData()},e.getMask=function(){return this._filterData.word1},e.addMask=function(t){t>>>=0,this._filterData.word1|=t,this.updateFilterData()},e.removeMask=function(t){t>>>=0,this._filterData.word1&=~t,this.updateFilterData()},e.updateEventListener=function(){this.updateFilterData()},e.updateFilterData=function(){this._impl&&this._impl.setSimulationFilterData(this.filterData)},l(t,[{key:"isEnabled",get:function(){return this._isEnabled}},{key:"impl",get:function(){return this._impl}},{key:"characterController",get:function(){return this._comp}},{key:"filterData",get:function(){return this._filterData}},{key:"queryFilterCB",get:function(){return this._queryFilterCB}},{key:"scaledCenter",get:function(){return u.multiply(Nt,this._comp.center,this._comp.node.worldScale),Nt}}]),t}();bt.idCounter=0,bt.queryCallback={preFilter:function(t,e){var i=ot(e);if(!i)return $.QueryHitType.eNONE;var n=i.collider;return t.word0&n.getMask()&&t.word1&n.getGroup()?$.QueryHitType.eBLOCK:$.QueryHitType.eNONE}};var Gt=new p,Ht=new u,kt=new u,Vt=new u,Ut=new m(0,0,0,0),Yt=function(t){h(i,t);var e=i.prototype;function i(){var e;return(e=t.call(this)||this).scene=void 0,e.callback=ee,e.wrappedBodies=[],e.ccts=[],e.controllerManager=void 0,e._isNeedFetch=!1,e._debugLineCount=0,e._MAX_DEBUG_LINE_COUNT=16384,e._debugDrawFlags=O.NONE,e._debugConstraintSize=.3,At(d(e)),e}return e.setAllowSleep=function(){},e.setDefaultMaterial=function(){},e.setGravity=function(t){this.scene.setGravity(t)},e.destroy=function(){this.wrappedBodies.length&&r("You should destroy all physics component first."),this.scene.release()},e.step=function(t){if(0!==this.wrappedBodies.length){if(this._simulate(t),!$.MULTI_THREAD){this._fetchResults();for(var e=0;e<this.wrappedBodies.length;e++)this.wrappedBodies[e].syncPhysicsToScene()}this._debugDraw()}},e._simulate=function(t){var e;this._isNeedFetch||(e=t,this.scene.simulate(e,!0),this._isNeedFetch=!0)},e._fetchResults=function(){this._isNeedFetch&&(this.scene.fetchResults(!0),this._isNeedFetch=!1)},e.syncSceneToPhysics=function(){for(var t=0;t<this.wrappedBodies.length;t++)this.wrappedBodies[t].syncSceneToPhysics();for(var e=this.ccts,i=e.length,n=0;n<i;n++)e[n].syncSceneToPhysics()},e.syncPhysicsToScene=function(){this._fetchResults();for(var t=0;t<this.wrappedBodies.length;t++)this.wrappedBodies[t].syncPhysicsToScene()},e.syncAfterEvents=function(){for(var t=0;t<this.wrappedBodies.length;t++)this.wrappedBodies[t].syncSceneWithCheck()},e._setDebugDrawMode=function(){this._debugDrawFlags&O.WIRE_FRAME?this.scene.setVisualizationParameter($.PxVisualizationParameter.eCOLLISION_SHAPES,1):this.scene.setVisualizationParameter($.PxVisualizationParameter.eCOLLISION_SHAPES,0);var t=Boolean(this._debugDrawFlags&O.CONSTRAINT)?this._debugConstraintSize:0;this.scene.setVisualizationParameter($.PxVisualizationParameter.eJOINT_LOCAL_FRAMES,t),this.scene.setVisualizationParameter($.PxVisualizationParameter.eJOINT_LIMITS,t),this._debugDrawFlags&O.AABB?this.scene.setVisualizationParameter($.PxVisualizationParameter.eCOLLISION_AABBS,1):this.scene.setVisualizationParameter($.PxVisualizationParameter.eCOLLISION_AABBS,0)},e._getDebugRenderer=function(){var t,e=null==(t=I.root.mainWindow)?void 0:t.cameras;return e?0===e.length?null:e[0]?(e[0].initGeometryRenderer(),e[0].geometryRenderer):null:null},e._debugDraw=function(){if(this._getDebugRenderer()){this._debugLineCount=0;for(var t=this.scene.getRenderBufferPtr(),e=$.PxRenderBuffer_GetNbLines(t),i=0;i<e;i++){var n=$.PxRenderBuffer_GetLineAt(t,i);this._onDebugDrawLine(n)}for(var s=$.PxRenderBuffer_GetNbTriangles(t),o=0;o<s;o++){var r=$.PxRenderBuffer_GetTriangleAt(t,o);this._onDebugDrawTriangle(r)}}},e._onDebugDrawLine=function(t){var e=this._getDebugRenderer();if(e&&this._debugLineCount<this._MAX_DEBUG_LINE_COUNT){this._debugLineCount++;var i=$.HEAPF32.subarray(t/4,t/4+24),n=$.HEAPU32.subarray(t/4,t/4+24);Ht.x=i[0],Ht.y=i[1],Ht.z=i[2];var s=n[3];kt.x=i[4],kt.y=i[5],kt.z=i[6],et(Ut,s),e.addLine(Ht,kt,Ut)}},e._onDebugDrawTriangle=function(t){var e=this._getDebugRenderer();if(e&&this._MAX_DEBUG_LINE_COUNT-this._debugLineCount>=3){this._debugLineCount+=3;var i=$.HEAPF32.subarray(t/4,t/4+36),n=$.HEAPU32.subarray(t/4,t/4+36);Ht.x=i[0],Ht.y=i[1],Ht.z=i[2];var s=n[3];kt.x=i[4],kt.y=i[5],kt.z=i[6],Vt.x=i[8],Vt.y=i[9],Vt.z=i[10],et(Ut,s),e.addLine(Ht,kt,Ut),e.addLine(kt,Vt,Ut),e.addLine(Vt,Ht,Ut)}},e.getSharedBody=function(t,e){return It.getSharedBody(t,this,e)},e.addActor=function(t){this.wrappedBodies.indexOf(t)<0&&(lt(this.scene,t.impl),this.wrappedBodies.push(t))},e.removeActor=function(t){var e=this.wrappedBodies.indexOf(t);e>=0&&(this.scene.removeActor(t.impl,!0),a(this.wrappedBodies,e))},e.addCCT=function(t){this.ccts.indexOf(t)<0&&this.ccts.push(t)},e.removeCCT=function(t){var e=this.ccts.indexOf(t);e>=0&&a(this.ccts,e)},e.addConstraint=function(){},e.removeConstraint=function(){},e.raycast=function(t,e,i,n){return Ct(this,t,e,i,n)},e.raycastClosest=function(t,e,i){return vt(this,t,e,i)},e.sweepBox=function(t,e,n,s,o,r){return i._sweepBoxGeometry||(i._sweepBoxGeometry=new $.BoxGeometry(e)),i._sweepBoxGeometry.setHalfExtents(e),wt(this,t,i._sweepBoxGeometry,n,s,o,r)},e.sweepBoxClosest=function(t,e,n,s,o){return i._sweepBoxGeometry||(i._sweepBoxGeometry=new $.BoxGeometry(e)),i._sweepBoxGeometry.setHalfExtents(e),Dt(this,t,i._sweepBoxGeometry,n,s,o)},e.sweepSphere=function(t,e,n,s,o){return i._sweepSphereGeometry||(i._sweepSphereGeometry=new $.SphereGeometry(e)),i._sweepSphereGeometry.setRadius(e),wt(this,t,i._sweepSphereGeometry,p.IDENTITY,n,s,o)},e.sweepSphereClosest=function(t,e,n,s){return i._sweepSphereGeometry||(i._sweepSphereGeometry=new $.SphereGeometry(e)),i._sweepSphereGeometry.setRadius(e),Dt(this,t,i._sweepSphereGeometry,p.IDENTITY,n,s)},e.sweepCapsule=function(t,e,n,s,o,r,a){i._sweepCapsuleGeometry||(i._sweepCapsuleGeometry=new $.CapsuleGeometry(e,n/2)),i._sweepCapsuleGeometry.setRadius(e),i._sweepCapsuleGeometry.setHalfHeight(n/2);var l=Gt;return p.fromEuler(l,0,0,90),p.multiply(l,s,l),wt(this,t,i._sweepCapsuleGeometry,l,o,r,a)},e.sweepCapsuleClosest=function(t,e,n,s,o,r){i._sweepCapsuleGeometry||(i._sweepCapsuleGeometry=new $.CapsuleGeometry(e,n/2)),i._sweepCapsuleGeometry.setRadius(e),i._sweepCapsuleGeometry.setHalfHeight(n/2);var a=Gt;return p.fromEuler(a,0,0,90),p.multiply(a,s,a),Dt(this,t,i._sweepCapsuleGeometry,a,o,r)},e.emitEvents=function(){ee.emitTriggerEvent(),ee.emitCollisionEvent(),ee.emitCCTCollisionEvent(),ee.emitCCTTriggerEvent()},l(i,[{key:"impl",get:function(){return this.scene}},{key:"debugDrawFlags",get:function(){return this._debugDrawFlags},set:function(t){this._debugDrawFlags=t,this._setDebugDrawMode()}},{key:"debugDrawConstraintSize",get:function(){return this._debugConstraintSize},set:function(t){this._debugConstraintSize=t,this._setDebugDrawMode()}}]),i}(X);Yt._sweepBoxGeometry=void 0,Yt._sweepSphereGeometry=void 0,Yt._sweepCapsuleGeometry=void 0;var zt=new G,Kt=new G,Xt=[],$t=new G,Jt=[],Wt=[],qt=new G,Qt=new s,jt=new G,Zt=new G,te=[],ee={eventCallback:{onContactBegin:function(t,e,i,n,s){var o=ot(t),r=ot(e);ee.onCollision("onCollisionEnter",o,r,i,n,s)},onContactEnd:function(t,e,i,n,s){var o=ot(t),r=ot(e);ee.onCollision("onCollisionExit",o,r,i,n,s)},onContactPersist:function(t,e,i,n,s){var o=ot(t),r=ot(e);ee.onCollision("onCollisionStay",o,r,i,n,s)},onTriggerBegin:function(t,e){var i=ot(t),n=ot(e);i instanceof Ot&&n instanceof Ot?ee.onTrigger("onTriggerEnter",i,n,!0):i instanceof Ot&&n instanceof bt?ee.onTriggerCCT("onControllerTriggerEnter",i,n,!0):i instanceof bt&&n instanceof Ot&&ee.onTriggerCCT("onControllerTriggerEnter",n,i,!0)},onTriggerEnd:function(t,e){var i=ot(t),n=ot(e);i instanceof Ot&&n instanceof Ot?ee.onTrigger("onTriggerExit",i,n,!1):i instanceof Ot&&n instanceof bt?ee.onTriggerCCT("onControllerTriggerExit",i,n,!1):i instanceof bt&&n instanceof Ot&&ee.onTriggerCCT("onControllerTriggerExit",n,i,!1)}},queryCallback:{preFilter:function(t,e){var i=t.word3,n=e.getFlags();return i&k.QUERY_CHECK_TRIGGER&&n.isSet($.ShapeFlag.eTRIGGER_SHAPE)?$.QueryHitType.eNONE:i&k.QUERY_SINGLE_HIT?$.QueryHitType.eBLOCK:$.QueryHitType.eTOUCH},preFilterForByteDance:function(t,e){var i=t.word3;return i&k.QUERY_CHECK_TRIGGER&&e&$.ShapeFlag.eTRIGGER_SHAPE?$.QueryHitType.eNONE:i&k.QUERY_SINGLE_HIT?$.QueryHitType.eBLOCK:$.QueryHitType.eTOUCH}},onTrigger:function(t,e,i,n){var s;e&&i&&(e.collider.needTriggerEvent||i.collider.needTriggerEvent)&&(Xt.length>0?((s=Xt.pop()).a=e,s.b=i,s.times=0):s={a:e,b:i,times:0},n?zt.set(e.id,i.id,s):Kt.set(e.id,i.id,s))},onTriggerCCT:function(t,e,i,n){var s;e&&i&&e.collider.needTriggerEvent&&(te.length>0?((s=te.pop()).a=e,s.b=i,s.times=0):s={a:e,b:i,times:0},n?jt.set(e.id,i.id,s):Zt.set(e.id,i.id,s))},emitTriggerEvent:function(){for(var t=Kt.getLength();t--;){var e=Kt.getKeyByIndex(t),i=Kt.getDataByKey(e);Xt.push(i);var n=zt.getDataByKey(e);n&&(Xt.push(n),zt.set(i.a.id,i.b.id,null));var s=i.a.collider,o=i.b.collider;if(s&&o){var r="onTriggerExit";v.type=r,s.needTriggerEvent&&(v.selfCollider=s,v.otherCollider=o,s.emit(r,v)),o.needTriggerEvent&&(v.selfCollider=o,v.otherCollider=s,o.emit(r,v))}}for(Kt.reset(),t=zt.getLength();t--;){var a=zt.getKeyByIndex(t),l=zt.getDataByKey(a),c=l.a.collider,h=l.b.collider;if(c&&c.isValid&&h&&h.isValid){var d=l.times++?"onTriggerStay":"onTriggerEnter";v.type=d,c.needTriggerEvent&&(v.selfCollider=c,v.otherCollider=h,c.emit(d,v)),h.needTriggerEvent&&(v.selfCollider=h,v.otherCollider=c,h.emit(d,v))}else Xt.push(l),zt.set(l.a.id,l.b.id,null)}},onCollision:function(t,e,i,n,s,o){if(e&&i&&(e.collider.needCollisionEvent||i.collider.needCollisionEvent))if(Jt.length>0){var r=Jt.pop();r.type=t,r.a=e,r.b=i,r.contactCount=n,r.buffer=s,r.offset=o,$t.set(e.id,i.id,r)}else{var a={type:t,a:e,b:i,contactCount:n,buffer:s,offset:o};$t.set(e.id,i.id,a)}},emitCollisionEvent:function(){for(var t=$t.getLength();t--;){var e=$t.getKeyByIndex(t),i=$t.getDataByKey(e);Jt.push(i);var n=i.a.collider,s=i.b.collider;if(n&&n.isValid&&s&&s.isValid){w.type=i.type,w.impl=i.buffer;var o=i.contactCount;i.buffer;var r=i.offset,a=w.contacts;Wt.push.apply(Wt,a),a.length=0;for(var l=0;l<o;l++)if(Wt.length>0){var c=Wt.pop();c.colliderA=n,c.colliderB=s,c.impl=Pt(l,r),a.push(c)}else{var h=new Ft(w);h.colliderA=n,h.colliderB=s,h.impl=Pt(l,r),a.push(h)}n.needCollisionEvent&&(w.selfCollider=n,w.otherCollider=s,n.emit(w.type,w)),s.needCollisionEvent&&(w.selfCollider=s,w.otherCollider=n,s.emit(w.type,w))}}$t.reset()},controllerHitReportCB:{onShapeHit:function(t){var e=ot(t.getCurrentController()),i=ot(t.getTouchedShape()),n=qt.get(t.getCurrentController(),t.getTouchedShape());if(!n){var s=new u;s.set(t.worldPos.x,t.worldPos.y,t.worldPos.z);var o=new u;o.set(t.worldNormal.x,t.worldNormal.y,t.worldNormal.z);var r=new u;r.set(t.dir.x,t.dir.y,t.dir.z);var a=t.length;n=qt.set(t.getCurrentController(),t.getTouchedShape(),{PhysXCharacterController:e,PhysXShape:i,worldPos:s,worldNormal:o,motionDir:r,motionLength:a})}},onControllerHit:function(){}},emitCCTCollisionEvent:function(){for(var t=qt.getLength();t--;){var e=qt.getKeyByIndex(t),i=qt.getDataByKey(e),n=i.PhysXCharacterController.characterController,s=i.PhysXShape.collider;n&&n.isValid&&s&&s.isValid&&(Qt.controller=n,Qt.collider=s,Qt.worldPosition.set(i.worldPos),Qt.worldNormal.set(i.worldNormal),Qt.motionDirection.set(i.motionDir),Qt.motionLength=i.motionLength,n.emit("onControllerColliderHit",Qt))}qt.reset()},emitCCTTriggerEvent:function(){for(var t=Zt.getLength();t--;){var e=Zt.getKeyByIndex(t),i=Zt.getDataByKey(e);te.push(i);var n=jt.getDataByKey(e);n&&(te.push(n),jt.set(i.a.id,i.b.id,null));var s=i.a.collider,o=i.b.characterController;if(s&&o){var r="onControllerTriggerExit";D.type=r,s.needTriggerEvent&&(D.collider=s,D.characterController=o,s.emit(r,D)),o.needTriggerEvent&&(D.collider=s,D.characterController=o,o.emit(r,D))}}for(Zt.reset(),t=jt.getLength();t--;){var a=jt.getKeyByIndex(t),l=jt.getDataByKey(a),c=l.a.collider,h=l.b.characterController;if(c&&c.isValid&&h&&h.isValid){var d=l.times++?"onControllerTriggerStay":"onControllerTriggerEnter";D.type=d,c.needTriggerEvent&&(D.collider=c,D.characterController=h,c.emit(d,D)),h.needTriggerEvent&&(D.collider=c,D.characterController=h,h.emit(d,D))}else te.push(l),jt.set(l.a.id,l.b.id,null)}}},ie=new u,ne=function(){function t(){this.isSleepy=!1,this._isEnabled=!1,this._isUsingCCD=!1,this._rigidBody=void 0,this._sharedBody=void 0}var e=t.prototype;return e.initialize=function(t){this._rigidBody=t,this._sharedBody=n.instance.physicsWorld.getSharedBody(t.node,this),this._sharedBody.reference=!0,this.setSleepThreshold(n.instance.sleepThreshold)},e.onEnable=function(){this._isEnabled=!0,this.setMass(this._rigidBody.mass),this.setType(this._rigidBody.type),this.setAllowSleep(this._rigidBody.allowSleep),this.setLinearDamping(this._rigidBody.linearDamping),this.setAngularDamping(this._rigidBody.angularDamping),this.setLinearFactor(this._rigidBody.linearFactor),this.setAngularFactor(this._rigidBody.angularFactor),this.useGravity(this._rigidBody.useGravity),this._sharedBody.enabled=!0},e.onDisable=function(){this._isEnabled=!1,this._sharedBody.enabled=!1},e.onDestroy=function(){this._sharedBody.reference=!1,this._rigidBody=null,this._sharedBody=null},e.setType=function(t){this._sharedBody.setType(t)},e.setMass=function(t){this.isStatic||this._sharedBody.setMass(t)},e.setLinearDamping=function(t){this._sharedBody.setLinearDamping(t)},e.setAngularDamping=function(t){this._sharedBody.setAngularDamping(t)},e.useGravity=function(t){this.isStatic||this.impl.setActorFlag($.ActorFlag.eDISABLE_GRAVITY,!t)},e.useCCD=function(t){this.isStatic||(this.impl.setRigidBodyFlag($.RigidBodyFlag.eENABLE_CCD,t),this._isUsingCCD=t)},e.isUsingCCD=function(){return this._isUsingCCD},e.setLinearFactor=function(t){this.isStatic||(this.impl.setRigidDynamicLockFlag($.RigidDynamicLockFlag.eLOCK_LINEAR_X,!t.x),this.impl.setRigidDynamicLockFlag($.RigidDynamicLockFlag.eLOCK_LINEAR_Y,!t.y),this.impl.setRigidDynamicLockFlag($.RigidDynamicLockFlag.eLOCK_LINEAR_Z,!t.z))},e.setAngularFactor=function(t){this.isStatic||(this.impl.setRigidDynamicLockFlag($.RigidDynamicLockFlag.eLOCK_ANGULAR_X,!t.x),this.impl.setRigidDynamicLockFlag($.RigidDynamicLockFlag.eLOCK_ANGULAR_Y,!t.y),this.impl.setRigidDynamicLockFlag($.RigidDynamicLockFlag.eLOCK_ANGULAR_Z,!t.z))},e.setAllowSleep=function(t){if(!this.isStaticOrKinematic){var e=t?1e-4:4294967295;this.impl.setWakeCounter(e)}},e.wakeUp=function(){this.isInScene&&!this.isStatic&&this.impl.wakeUp()},e.sleep=function(){this.isInScene&&!this.isStatic&&this.impl.putToSleep()},e.clearState=function(){this.isInScene&&!this.isStatic&&(this.clearForces(),this.clearVelocity())},e.clearForces=function(){this.isInScene&&!this.isStatic&&this._sharedBody.clearForces()},e.clearVelocity=function(){this.isStatic||this._sharedBody.clearVelocity()},e.setSleepThreshold=function(t){if(!this.isStatic){var e=.5*t*t;this.impl.setSleepThreshold(e)}},e.getSleepThreshold=function(){if(this.isStatic)return 0;var t=this.impl.getSleepThreshold();return Math.sqrt(2*t)},e.getLinearVelocity=function(t){this.isStatic||u.copy(t,this.impl.getLinearVelocity())},e.setLinearVelocity=function(t){this.isStaticOrKinematic||this.impl.setLinearVelocity(t,!0)},e.getAngularVelocity=function(t){this.isStatic||u.copy(t,this.impl.getAngularVelocity())},e.setAngularVelocity=function(t){this.isStaticOrKinematic||this.impl.setAngularVelocity(t,!0)},e.applyForce=function(t,e){if(this.isInScene&&!this.isStaticOrKinematic){this._sharedBody.syncSceneToPhysics();var i=e||u.ZERO;mt(!0,this.impl,t,i)}},e.applyLocalForce=function(t,e){if(this.isInScene&&!this.isStaticOrKinematic){this._sharedBody.syncSceneToPhysics();var i=e||u.ZERO;mt(!1,this.impl,t,i)}},e.applyImpulse=function(t,e){if(this.isInScene&&!this.isStaticOrKinematic){this._sharedBody.syncSceneToPhysics();var i=e||u.ZERO;_t(!0,this.impl,t,i)}},e.applyLocalImpulse=function(t,e){if(this.isInScene&&!this.isStaticOrKinematic){this._sharedBody.syncSceneToPhysics();var i=e||u.ZERO;_t(!1,this.impl,t,i)}},e.applyTorque=function(t){this.isInScene&&!this.isStaticOrKinematic&&ft(this.impl,t)},e.applyLocalTorque=function(t){this.isInScene&&!this.isStaticOrKinematic&&(this._sharedBody.syncSceneToPhysics(),u.transformQuat(ie,t,this._sharedBody.node.worldRotation),ft(this.impl,ie))},e.setGroup=function(t){this._sharedBody.setGroup(t)},e.getGroup=function(){return this._sharedBody.getGroup()},e.addGroup=function(t){this._sharedBody.addGroup(t)},e.removeGroup=function(t){this._sharedBody.removeGroup(t)},e.setMask=function(t){this._sharedBody.setMask(t)},e.getMask=function(){return this._sharedBody.getMask()},e.addMask=function(t){this._sharedBody.addMask(t)},e.removeMask=function(t){this._sharedBody.removeMask(t)},l(t,[{key:"impl",get:function(){return this._sharedBody.impl}},{key:"isAwake",get:function(){return!(!this.isInScene||this.isStatic||this.impl.isSleeping())}},{key:"isSleeping",get:function(){return!(this.isInScene&&!this.isStatic)||this.impl.isSleeping()}},{key:"isEnabled",get:function(){return this._isEnabled}},{key:"rigidBody",get:function(){return this._rigidBody}},{key:"sharedBody",get:function(){return this._sharedBody}},{key:"isStatic",get:function(){return!this.impl||this._sharedBody.isStatic}},{key:"isStaticOrKinematic",get:function(){return!this.impl||this._sharedBody.isStatic||this._sharedBody.isKinematic}},{key:"isInScene",get:function(){return this._sharedBody.isInScene}}]),t}(),se=function(t){function e(){var i;return i=t.call(this,Mt.SPHERE)||this,e.SPHERE_GEOMETRY||(e.SPHERE_GEOMETRY=new $.SphereGeometry(.5)),i}h(e,t);var i=e.prototype;return i.updateRadius=function(){this.updateScale()},i.onComponentSet=function(){this.updateGeometry();var t=this.getSharedMaterial(this.collider.sharedMaterial);this._impl=X.physics.createShape(e.SPHERE_GEOMETRY,t,!0,this._flags)},i.updateScale=function(){this.updateGeometry(),this._impl.setGeometry(e.SPHERE_GEOMETRY),this.setCenter(this._collider.center)},i.updateGeometry=function(){var t=this.collider,i=Math.abs(f(this.collider.node.worldScale));e.SPHERE_GEOMETRY.setRadius(Math.max(1e-4,t.radius*i))},l(e,[{key:"collider",get:function(){return this._collider}}]),e}(Ot);se.SPHERE_GEOMETRY=void 0;var oe=function(t){function e(){var i;return i=t.call(this,Mt.BOX)||this,e.BOX_GEOMETRY||(A.set(.5,.5,.5),e.BOX_GEOMETRY=new $.BoxGeometry(A)),i}h(e,t);var i=e.prototype;return i.updateSize=function(){this.updateScale()},i.onComponentSet=function(){this.updateGeometry();var t=this.getSharedMaterial(this._collider.sharedMaterial);this._impl=X.physics.createShape(e.BOX_GEOMETRY,t,!0,this._flags)},i.updateScale=function(){this.updateGeometry(),this._impl.setGeometry(e.BOX_GEOMETRY),this.setCenter(this._collider.center)},i.updateGeometry=function(){var t=this.collider,i=t.node.worldScale;A.set(t.size).multiplyScalar(.5).multiply(i),e.BOX_GEOMETRY.setHalfExtents(R(A))},l(e,[{key:"collider",get:function(){return this._collider}}]),e}(Ot);oe.BOX_GEOMETRY=void 0;var re=function(t){function e(){var i;return i=t.call(this,Mt.CAPSULE)||this,e.CAPSULE_GEOMETRY||(e.CAPSULE_GEOMETRY=new $.CapsuleGeometry(.5,.5)),i}h(e,t);var i=e.prototype;return i.setCylinderHeight=function(){this.updateScale()},i.setDirection=function(){this.updateScale()},i.setRadius=function(){this.updateScale()},i.onComponentSet=function(){this.updateGeometry();var t=this.getSharedMaterial(this._collider.sharedMaterial);this._impl=X.physics.createShape(e.CAPSULE_GEOMETRY,t,!0,this._flags)},i.updateScale=function(){this.updateGeometry(),this._impl.setGeometry(e.CAPSULE_GEOMETRY),this.setCenter(this._collider.center)},i.updateGeometry=function(){var t=this.collider,i=t.node.worldScale,n=t.direction,s=.5,o=.5;n===x.Y_AXIS?(s=t.radius*Math.abs(y(i.x,i.z)),o=t.cylinderHeight/2*Math.abs(i.y),p.fromEuler(this._rotation,0,0,90)):n===x.X_AXIS?(s=t.radius*Math.abs(y(i.y,i.z)),o=t.cylinderHeight/2*Math.abs(i.x),p.fromEuler(this._rotation,0,0,0)):(s=t.radius*Math.abs(y(i.x,i.y)),o=t.cylinderHeight/2*Math.abs(i.z),p.fromEuler(this._rotation,0,90,0)),e.CAPSULE_GEOMETRY.setRadius(Math.max(1e-4,s)),e.CAPSULE_GEOMETRY.setHalfHeight(Math.max(1e-4,o))},l(e,[{key:"collider",get:function(){return this._collider}}]),e}(Ot);re.CAPSULE_GEOMETRY=void 0;var ae=function(t){function e(){var i;return i=t.call(this,Mt.PLANE)||this,e.PLANE_GEOMETRY||(e.PLANE_GEOMETRY=new $.PlaneGeometry),i}h(e,t);var i=e.prototype;return i.setNormal=function(){this.setCenter()},i.setConstant=function(){this.setCenter()},i.setCenter=function(){var t=this.collider,e=tt.translation,i=tt.rotation;u.scaleAndAdd(e,t.center,t.normal,t.constant),p.rotationTo(i,u.UNIT_X,t.normal);var n=rt(e,i);this._impl.setLocalPose(n)},i.onComponentSet=function(){var t=this.collider,i=this.getSharedMaterial(t.sharedMaterial);this._impl=X.physics.createShape(e.PLANE_GEOMETRY,i,!0,this._flags),this.setCenter()},i.updateScale=function(){this.setCenter()},l(e,[{key:"collider",get:function(){return this._collider}}]),e}(Ot);ae.PLANE_GEOMETRY=void 0;var le=function(t){function e(){var e;return(e=t.call(this,Mt.MESH)||this).geometry=void 0,e}h(e,t);var i=e.prototype;return i.setMesh=function(t){if(t&&t.renderingSubMeshes.length>0){null!=this._impl&&(this.removeFromBody(),st(0,this._impl),this._impl.release(),this._impl=null);var e=X.physics,i=this.collider,n=this.getSharedMaterial(i.sharedMaterial),s=Ot.MESH_SCALE;s.setScale(u.ONE),s.setRotation(p.IDENTITY);var o=t.renderingSubMeshes[0].geometricInfo.positions,r=t.renderingSubMeshes[0].geometricInfo.indices;if(r instanceof Uint16Array&&(r=new Uint32Array(r)),r instanceof Uint8Array&&(r=new Uint32Array(r)),i.convex||void 0===r){if(null==$.MESH_CONVEX[t._uuid]){var a=X.cooking;$.MESH_CONVEX[t._uuid]=yt(o,a,e)}var l=$.MESH_CONVEX[t._uuid];this.geometry=new $.ConvexMeshGeometry(l,s,gt(0,!0))}else{if(null==$.MESH_STATIC[t._uuid]){var c=X.cooking;$.MESH_STATIC[t._uuid]=St(o,r,c,e)}var h=$.MESH_STATIC[t._uuid];this.geometry=new $.TriangleMeshGeometry(h,s,gt(0,!1))}this.updateGeometry(),this._impl=e.createShape(this.geometry,n,!0,this._flags),this.addToBody(),nt(this,this._impl)}},i.onComponentSet=function(){this.setMesh(this.collider.mesh)},i.updateScale=function(){this.updateGeometry(),this.setCenter(this._collider.center)},i.updateGeometry=function(){var t=Ot.MESH_SCALE;t.setScale(this.collider.node.worldScale),t.setRotation(p.IDENTITY),this.geometry.setScale(t)},i.setMaterial=function(e){this._impl&&t.prototype.setMaterial.call(this,e)},i.setCenter=function(e){this._impl&&t.prototype.setCenter.call(this,e)},i.setAsTrigger=function(e){this._impl&&t.prototype.setAsTrigger.call(this,e)},i.setFilerData=function(e){this._impl&&t.prototype.setFilerData.call(this,e)},i.addToBody=function(){this._impl&&t.prototype.addToBody.call(this)},i.removeFromBody=function(){this._impl&&t.prototype.removeFromBody.call(this)},l(e,[{key:"collider",get:function(){return this._collider}}]),e}(Ot),ce=function(t){function e(){return t.call(this,Mt.TERRAIN)||this}h(e,t);var i=e.prototype;return i.setTerrain=function(t){if(t&&null==this._impl){var i=X.physics,n=this.collider;if(null==$.TERRAIN_STATIC[t._uuid]){var s=X.cooking;$.TERRAIN_STATIC[t._uuid]=Et(t,e.heightScale,s,i)}var o=$.TERRAIN_STATIC[t._uuid],r=this.getSharedMaterial(n.sharedMaterial),a=Tt(o,0,e.heightScale,t.tileSize,t.tileSize);this._impl=i.createShape(a,r,!0,this._flags),this.updateByReAdd()}},i.onComponentSet=function(){this.setTerrain(this.collider.terrain)},i.updateScale=function(){this.setCenter(this._collider.center)},i.setCenter=function(t){this._impl&&this._impl.setLocalPose(rt(t,this._rotation))},i.setMaterial=function(e){this._impl&&t.prototype.setMaterial.call(this,e)},i.setAsTrigger=function(e){this._impl&&t.prototype.setAsTrigger.call(this,e)},i.setFilerData=function(e){this._impl&&t.prototype.setFilerData.call(this,e)},i.addToBody=function(){this._impl&&t.prototype.addToBody.call(this)},i.removeFromBody=function(){this._impl&&t.prototype.removeFromBody.call(this)},l(e,[{key:"collider",get:function(){return this._collider}}]),e}(Ot);ce.heightScale=1/512;var he=function(t){function e(){var e;return(e=t.call(this,Mt.CYLINDER)||this).geometry=void 0,e}h(e,t);var i=e.prototype;return i.setRadius=function(){this.updateGeometry()},i.setHeight=function(){this.updateGeometry()},i.setDirection=function(){this.updateGeometry()},i.onComponentSet=function(){var t=this.collider,i=X.physics;if(!e.CONVEX_MESH){var n=X.cooking,s=H(.5,.5,2,{radialSegments:32,heightSegments:1});e.CONVEX_MESH=yt(s.positions,n,i)}var o=Ot.MESH_SCALE;o.setScale(u.ONE),o.setRotation(p.IDENTITY);var r=e.CONVEX_MESH,a=this.getSharedMaterial(t.sharedMaterial);this.geometry=new $.ConvexMeshGeometry(r,o,gt(0,!0)),this.updateGeometry(),this._impl=i.createShape(this.geometry,a,!0,this._flags)},i.updateScale=function(){this.updateGeometry(),this.setCenter(this._collider.center)},i.updateGeometry=function(){var t=this.collider,e=t.radius,i=t.height,n=t.direction,s=tt.translation;u.copy(s,t.node.worldScale),s.y*=Math.max(1e-4,i/2);var o=Math.max(1e-4,e/.5),r=Math.max(s.x,s.z);s.x=s.z=r*o;var a=tt.rotation;switch(n){case x.X_AXIS:p.fromEuler(a,0,0,90);break;case x.Y_AXIS:default:p.copy(a,p.IDENTITY);break;case x.Z_AXIS:p.fromEuler(a,90,0,0)}var l=Ot.MESH_SCALE;l.setScale(s),l.setRotation(a),this.geometry.setScale(l),p.copy(this._rotation,a)},l(e,[{key:"collider",get:function(){return this._collider}}]),e}(Ot);he.CONVEX_MESH=void 0;var de=function(t){function e(){var e;return(e=t.call(this,Mt.CONE)||this).geometry=void 0,e}h(e,t);var i=e.prototype;return i.setRadius=function(){this.updateGeometry()},i.setHeight=function(){this.updateGeometry()},i.setDirection=function(){this.updateGeometry()},i.onComponentSet=function(){var t=this.collider,i=X.physics;if(!e.CONVEX_MESH){var n=X.cooking,s=H(0,.5,1,{radialSegments:32,heightSegments:1});e.CONVEX_MESH=yt(s.positions,n,i)}var o=Ot.MESH_SCALE;o.setScale(u.ONE),o.setRotation(p.IDENTITY);var r=e.CONVEX_MESH,a=this.getSharedMaterial(t.sharedMaterial);this.geometry=new $.ConvexMeshGeometry(r,o,gt(0,!0)),this.updateGeometry(),this._impl=i.createShape(this.geometry,a,!0,this._flags)},i.updateScale=function(){this.updateGeometry(),this.setCenter(this._collider.center)},i.updateGeometry=function(){var t=this.collider,e=t.radius,i=t.height,n=t.direction,s=tt.translation;u.copy(s,t.node.worldScale),s.y*=Math.max(1e-4,i/1);var o=Math.max(1e-4,e/.5),r=Math.max(s.x,s.z);s.x=s.z=r*o;var a=tt.rotation;switch(n){case x.X_AXIS:p.fromEuler(a,0,0,90);break;case x.Y_AXIS:default:p.copy(a,p.IDENTITY);break;case x.Z_AXIS:p.fromEuler(a,90,0,0)}var l=Ot.MESH_SCALE;l.setScale(s),l.setRotation(a),this.geometry.setScale(l),p.copy(this._rotation,a)},l(e,[{key:"collider",get:function(){return this._collider}}]),e}(Ot);de.CONVEX_MESH=void 0;var ue=function(){function t(){this._impl=void 0,this._com=void 0,this._rigidBody=void 0,this._connectedBody=null}var e=t.prototype;return e.setConnectedBody=function(t){if(this._connectedBody!==t){var e=this._connectedBody;e&&e.body.sharedBody.removeJoint(this,1);var i=this._rigidBody.body.sharedBody;if(i.removeJoint(this,0),i.addJoint(this,0),t){var n=t.body.sharedBody;ct(this._impl,i.impl,n.impl),n.addJoint(this,1)}else ct(this._impl,i.impl,null);e&&e.wakeUp(),this._connectedBody=t,this.updateScale0(),this.updateScale1()}},e.setEnableCollision=function(t){this._impl.setConstraintFlag(8,t)},e.initialize=function(t){this._com=t,this._rigidBody=t.attachedBody,this._connectedBody=t.connectedBody,this.onComponentSet(),this.setEnableCollision(this._com.enableCollision),this._impl.$$&&($.IMPL_PTR[this._impl.$$.ptr]=this)},e.enableDebugVisualization=function(t){this.impl&&this.impl.setConstraintFlag(16,t)},e.onComponentSet=function(){},e.updateScale0=function(){},e.updateScale1=function(){},e.onEnable=function(){var t=this._rigidBody.body.sharedBody,e=this._com.connectedBody;if(t.addJoint(this,0),e){var i=e.body.sharedBody;ct(this._impl,t.impl,i.impl),i.addJoint(this,1)}else ct(this._impl,t.impl,null)},e.onDisable=function(){ct(this._impl,t.tempActor,null),this._rigidBody.body.sharedBody.removeJoint(this,0);var e=this.constraint.connectedBody;e&&e.body.sharedBody.removeJoint(this,1)},e.onDestroy=function(){this._impl.$$&&($.IMPL_PTR[this._impl.$$.ptr]=null,delete $.IMPL_PTR[this._impl.$$.ptr]),this._impl.release(),this._com=null,this._rigidBody=null,this._connectedBody=null,this._impl=null},l(t,[{key:"impl",get:function(){return this._impl}},{key:"constraint",get:function(){return this._com}}],[{key:"tempActor",get:function(){return this._tempActor||(this._tempActor=X.physics.createRigidDynamic(it)),this._tempActor}}]),t}();ue._tempActor=void 0;var pe=new u,_e=new p,me=new g,fe=function(t){function e(){for(var e,i=arguments.length,n=new Array(i),s=0;s<i;s++)n[s]=arguments[s];return(e=t.call.apply(t,[this].concat(n))||this)._breakForce=0,e._breakTorque=0,e}h(e,t);var i=e.prototype;return i.setBreakForce=function(){this._breakForce=this.constraint.breakForce,this._impl.setBreakForce(this._breakForce,this._breakTorque)},i.setBreakTorque=function(){this._breakTorque=this.constraint.breakTorque,this._impl.setBreakForce(this._breakForce,this._breakTorque)},i.onComponentSet=function(){this._impl=$.createFixedConstraint(ue.tempActor,it,null,it),this.setBreakForce(this.constraint.breakForce),this.setBreakTorque(this.constraint.breakTorque),this.updateFrame(),this.enableDebugVisualization(!0)},i.updateFrame=function(){var t=this._rigidBody.body.sharedBody,e=this.constraint.connectedBody;if(g.fromRT(me,t.node.worldRotation,t.node.worldPosition),g.invert(me,me),g.getRotation(_e,me),g.getTranslation(pe,me),this._impl.setLocalPose(0,rt(pe,_e)),e){var i=e.body.sharedBody;g.fromRT(me,i.node.worldRotation,i.node.worldPosition),g.invert(me,me),g.getRotation(_e,me),g.getTranslation(pe,me),this._impl.setLocalPose(1,rt(pe,_e))}else this._impl.setLocalPose(1,rt(u.ZERO,p.IDENTITY))},i.updateScale0=function(){this.updateFrame()},i.updateScale1=function(){this.updateFrame()},l(e,[{key:"constraint",get:function(){return this._com}}]),e}(ue),ye=function(t){function e(){return t.apply(this,arguments)||this}h(e,t);var i=e.prototype;return i.setPivotA=function(){var t=this.constraint,e=tt.translation,i=tt.rotation;u.multiply(e,t.node.worldScale,t.pivotA),p.copy(i,p.IDENTITY),this._impl.setLocalPose(0,rt(e,i)),t.connectedBody||this.setPivotB(t.pivotB)},i.setPivotB=function(){var t=this.constraint,e=t.connectedBody,i=tt.translation,n=tt.rotation;if(u.copy(i,t.pivotB),p.copy(n,p.IDENTITY),e)u.multiply(i,e.node.worldScale,t.pivotB);else{var s=t.node;u.multiply(i,s.worldScale,t.pivotA),u.transformQuat(i,i,s.worldRotation),u.add(i,i,s.worldPosition),p.multiply(n,n,s.worldRotation)}this._impl.setLocalPose(1,rt(i,n))},i.onComponentSet=function(){this._impl=$.createSphericalJoint(ue.tempActor,it,null,it),this.setPivotA(this.constraint.pivotA),this.setPivotB(this.constraint.pivotB),this.enableDebugVisualization(!0)},i.updateScale0=function(){this.setPivotA(this.constraint.pivotA)},i.updateScale1=function(){this.setPivotB(this.constraint.pivotB)},l(e,[{key:"constraint",get:function(){return this._com}}]),e}(ue),ge=new u,Se=new u,Ee=new u,Te=new p,Ce=new g,ve=function(t){function e(){for(var e,i=arguments.length,n=new Array(i),s=0;s<i;s++)n[s]=arguments[s];return(e=t.call.apply(t,[this].concat(n))||this)._limitPair=new $.PxJointAngularLimitPair(0,0),e}h(e,t);var i=e.prototype;return i.setLimitEnabled=function(t){this._impl.setRevoluteJointFlag($.RevoluteJointFlags.eLIMIT_ENABLED,t),t&&this._impl.setLimit(this._limitPair)},i.setLowerLimit=function(){this._limitPair.lower=S(this.constraint.lowerLimit),this.constraint.limitEnabled&&this._impl.setLimit(this._limitPair)},i.setUpperLimit=function(){this._limitPair.upper=S(this.constraint.upperLimit),this.constraint.limitEnabled&&this._impl.setLimit(this._limitPair)},i.setMotorEnabled=function(t){this._impl.setRevoluteJointFlag($.RevoluteJointFlags.eDRIVE_ENABLED,t),t&&(this._impl.setDriveVelocity(this.constraint.motorVelocity/60,!0),this._impl.setDriveForceLimit(this.constraint.motorForceLimit))},i.setMotorVelocity=function(t){this.constraint.motorEnabled&&this._impl.setDriveVelocity(t/60,!0)},i.setMotorForceLimit=function(t){this.constraint.motorEnabled&&this._impl.setDriveForceLimit(t)},i.setPivotA=function(){this.updateFrames()},i.setPivotB=function(){this.updateFrames()},i.setAxis=function(){this.updateFrames()},i.onComponentSet=function(){this._impl=$.createRevoluteJoint(ue.tempActor,it,null,it),this._limitPair.stiffness=0,this._limitPair.damping=0,this._limitPair.restitution=.4,this._limitPair.contactDistance=.01,this._impl.setConstraintFlag(6,!0),this._impl.setConstraintFlag(32,!0),this._impl.setProjectionAngularTolerance(.2),this._impl.setProjectionLinearTolerance(.2),this.setPivotA(this.constraint.pivotA),this.setPivotB(this.constraint.pivotB),this.setLimitEnabled(this.constraint.limitEnabled),this.setMotorEnabled(this.constraint.motorEnabled),this.setLowerLimit(this.constraint.lowerLimit),this.setUpperLimit(this.constraint.upperLimit),this.setMotorVelocity(this.constraint.motorVelocity),this.setMotorForceLimit(this.constraint.motorForceLimit),this.updateFrames(),this.enableDebugVisualization(!0)},i.updateFrames=function(){var t=this.constraint,e=t.connectedBody,i=tt.translation,n=tt.rotation,s=t.node;u.normalize(ge,t.axis),u.cross(Ee,ge,u.UNIT_Y),u.len(Ee)<1e-4?(u.cross(Se,u.UNIT_Z,ge),u.cross(Ee,ge,Se)):u.cross(Se,Ee,ge),u.normalize(Se,Se),u.normalize(Ee,Ee),Ce.set(ge.x,ge.y,ge.z,0,Se.x,Se.y,Se.z,0,Ee.x,Ee.y,Ee.z,0,0,0,0,1),Ce.getRotation(Te),u.multiply(i,t.node.worldScale,t.pivotA),this._impl.setLocalPose(0,rt(i,Te)),e?(p.multiply(Te,s.worldRotation,Te),p.invert(n,e.node.worldRotation),p.multiply(Te,n,Te),u.multiply(i,e.node.worldScale,t.pivotB)):(p.multiply(Te,s.worldRotation,Te),u.multiply(i,s.worldScale,t.pivotA),u.transformQuat(i,i,s.worldRotation),u.add(i,i,s.worldPosition)),this._impl.setLocalPose(1,rt(i,Te))},i.updateScale0=function(){this.updateFrames()},i.updateScale1=function(){this.updateFrames()},l(e,[{key:"constraint",get:function(){return this._com}}]),e}(ue),we=new u,De=new u,Ae=new p,Re=new g;function Le(t){switch(t){case N.FREE:return $.D6Motion.eFREE;case N.LIMITED:return $.D6Motion.eLIMITED;case N.LOCKED:return $.D6Motion.eLOCKED;default:return $.D6Motion.eFREE}}var Pe=function(t){function e(){return t.apply(this,arguments)||this}h(e,t);var i=e.prototype;return i._setLinearLimit=function(){var t=this.constraint.linearLimitSettings,i=e._linearLimitX,n=e._linearLimitY,s=e._linearLimitZ,o=function(e){t.enableSoftConstraint?(e.stiffness=t.stiffness,e.damping=t.damping):(e.stiffness=0,e.damping=0),e.bounceThreshold=.1,e.contactDistance=.1,e.restitution=t.restitution};o(i),o(n),o(s);var r=t.lower,a=t.upper;t.xMotion===N.LIMITED&&(i.lower=r.x,i.upper=a.x,this._impl.setLinearLimit($.D6Axis.eX,i)),t.yMotion===N.LIMITED&&(n.lower=r.y,n.upper=a.y,this._impl.setLinearLimit($.D6Axis.eY,n)),t.zMotion===N.LIMITED&&(s.lower=r.z,s.upper=a.z,this._impl.setLinearLimit($.D6Axis.eZ,s))},i._setSwingLimit=function(){var t=this.constraint.angularLimitSettings,i=e._swingLimit;t.enableSoftConstraintSwing?(i.stiffness=t.swingStiffness,i.damping=t.swingDamping):(i.stiffness=0,i.springDamping=0),i.yAngle=Math.PI,i.zAngle=Math.PI,i.contactDistance=.1,i.bounceThreshold=.1,i.restitution=t.swingRestitution,t.swingMotion1===N.LIMITED&&(i.yAngle=.5*S(Math.max(t.swingExtent1,1e-9)),this._impl.setSwingLimit(i)),t.swingMotion2===N.LIMITED&&(i.zAngle=.5*S(Math.max(t.swingExtent2,1e-9)),this._impl.setSwingLimit(i))},i._setTwistLimit=function(){var t=this.constraint.angularLimitSettings,i=e._twistLimit;t.enableSoftConstraintTwist?(i.stiffness=t.twistStiffness,i.damping=t.twistDamping):(i.stiffness=0,i.damping=0),i.contactDistance=.1,i.bounceThreshold=.1,i.restitution=t.twistRestitution,t.twistMotion===N.LIMITED&&(i.lower=-.5*S(Math.max(t.twistExtent,1e-9)),i.upper=.5*S(Math.max(t.twistExtent,1e-9)),this._impl.setTwistLimit(i))},i._updateDrive=function(t){var i=this,n=$.D6Axis.eX,s=b.DISABLED,o=this.constraint,r=o.linearDriverSettings,a=o.angularDriverSettings;switch(t){case 0:n=$.D6Drive.eX,s=r.xDrive;break;case 1:n=$.D6Drive.eY,s=r.yDrive;break;case 2:n=$.D6Drive.eZ,s=r.zDrive;break;case 3:n=$.D6Drive.eTWIST,s=a.twistDrive;break;case 4:case 5:n=$.D6Drive.eSWING,s=function(){var t=i.constraint.angularDriverSettings;return t.swingDrive1===b.INDUCTION||t.swingDrive2===b.INDUCTION?b.INDUCTION:t.swingDrive1===b.SERVO||t.swingDrive2===b.SERVO?b.SERVO:b.DISABLED}()}var l=e._drive[t];t>=0&&t<3?l.forceLimit=o.linearDriverSettings.strength:t<6&&(l.forceLimit=o.angularDriverSettings.strength),s===b.DISABLED?l.forceLimit=0:s===b.SERVO?(l.damping=0,l.stiffness=1e3):s===b.INDUCTION&&(l.damping=1e3,l.stiffness=0),this._impl.setDrive(n,l)},i._updateDriveTarget=function(){var t=this.constraint.linearDriverSettings.targetPosition,e=this.constraint.angularDriverSettings.targetOrientation;rt(t,p.fromEuler(Ae,e.x,e.y,e.z)),this._impl.setDrivePosition(it,!0)},i._updateDriveVelocity=function(){var t=this.constraint.linearDriverSettings.targetVelocity,e=this.constraint.angularDriverSettings.targetVelocity,i=u.set(we,t.x,t.y,t.z),n=u.set(De,S(-e.x),S(-e.y),S(-e.z));this._impl.setDriveVelocity(i,n,!0)},i._updateDriveSettings=function(){this._updateDrive(0),this._updateDrive(1),this._updateDrive(2),this._updateDrive(3),this._updateDrive(4),this._updateDrive(5),this._updateDriveTarget(),this._updateDriveVelocity()},i.setConstraintMode=function(t,e){var i=$.D6Axis.eX;switch(t){case 0:i=$.D6Axis.eX;break;case 1:i=$.D6Axis.eY;break;case 2:i=$.D6Axis.eZ;break;case 3:i=$.D6Axis.eTWIST;break;case 4:i=$.D6Axis.eSWING1;break;case 5:i=$.D6Axis.eSWING2}var n=Le(e);this._impl.setMotion(i,n)},i.setLinearLimit=function(){this._setLinearLimit()},i.setAngularExtent=function(){this._setSwingLimit(),this._setTwistLimit()},i.setLinearRestitution=function(){this._setLinearLimit()},i.setSwingRestitution=function(){this._setSwingLimit()},i.setTwistRestitution=function(){this._setTwistLimit()},i.setLinearSoftConstraint=function(){this._setLinearLimit()},i.setLinearStiffness=function(){this._setLinearLimit()},i.setLinearDamping=function(){this._setLinearLimit()},i.setSwingSoftConstraint=function(){this._setSwingLimit()},i.setSwingStiffness=function(){this._setSwingLimit()},i.setSwingDamping=function(){this._setSwingLimit()},i.setTwistSoftConstraint=function(){this._setTwistLimit()},i.setTwistStiffness=function(){this._setTwistLimit()},i.setTwistDamping=function(){this._setTwistLimit()},i.setDriverMode=function(t){this._updateDrive(t)},i.setLinearMotorTarget=function(){this._updateDriveTarget()},i.setLinearMotorVelocity=function(){this._updateDriveVelocity()},i.setLinearMotorForceLimit=function(){this._updateDrive(0),this._updateDrive(1),this._updateDrive(2)},i.setAngularMotorTarget=function(){this._updateDriveTarget()},i.setAngularMotorVelocity=function(){this._updateDriveVelocity()},i.setAngularMotorForceLimit=function(){this._updateDrive(3),this._updateDrive(4),this._updateDrive(5)},i.setPivotA=function(){this.updateFrames()},i.setPivotB=function(){this.updateFrames()},i.setAutoPivotB=function(){this.updateFrames()},i.setAxis=function(){this.updateFrames()},i.setSecondaryAxis=function(){this.updateFrames()},i.setBreakForce=function(){var t=this.constraint.breakForce,e=this.constraint.breakTorque;this._impl.setBreakForce(t,e)},i.setBreakTorque=function(){var t=this.constraint.breakForce,e=this.constraint.breakTorque;this._impl.setBreakForce(t,e)},i.onComponentSet=function(){e._initCache();var t=this.constraint;this._impl=$.createD6Joint(ue.tempActor,it,null,it),this.setBreakForce(t.breakForce),this.setBreakTorque(t.breakTorque);var i=t.linearLimitSettings,n=t.angularLimitSettings;this.setConstraintMode(0,i.xMotion),this.setConstraintMode(1,i.yMotion),this.setConstraintMode(2,i.zMotion),this.setConstraintMode(3,n.twistMotion),this.setConstraintMode(4,n.swingMotion1),this.setConstraintMode(5,n.swingMotion2),this._setLinearLimit(),this._setSwingLimit(),this._setTwistLimit(),this._updateDriveSettings(),this.updateFrames(),this.enableDebugVisualization(!0)},i.updateFrames=function(){var t=this.constraint,e=t.node,i=tt.translation,n=tt.rotation,s=t.connectedBody,o=t.axis,r=t.secondaryAxis,a=u.cross(we,o,r),l=Ae;g.set(Re,o.x,o.y,o.z,0,r.x,r.y,r.z,0,a.x,a.y,a.z,0,0,0,0,1).getRotation(l),u.multiply(i,t.node.worldScale,t.pivotA),this._impl.setLocalPose(0,rt(i,l)),s?(p.multiply(l,e.worldRotation,l),p.invert(n,s.node.worldRotation),p.multiply(l,n,l),t.autoPivotB?(u.multiply(i,t.node.worldScale,t.pivotA),u.transformQuat(i,i,e.worldRotation),u.add(i,i,e.worldPosition),u.subtract(i,i,s.node.worldPosition),u.transformQuat(i,i,n)):u.multiply(i,s.node.worldScale,t.pivotB)):(u.multiply(i,e.worldScale,t.pivotA),u.transformQuat(i,i,e.worldRotation),u.add(i,i,e.worldPosition),p.multiply(l,e.worldRotation,l)),this._impl.setLocalPose(1,rt(i,l))},i.updateScale0=function(){this.updateFrames()},i.updateScale1=function(){this.updateFrames()},e._initCache=function(){e._jointToleranceScale||(e._jointToleranceScale=X.physics.getTolerancesScale(),e._linearLimitX=new $.PxJointLinearLimitPair(e._jointToleranceScale,0,0),e._linearLimitY=new $.PxJointLinearLimitPair(e._jointToleranceScale,0,0),e._linearLimitZ=new $.PxJointLinearLimitPair(e._jointToleranceScale,0,0),e._twistLimit=new $.PxJointAngularLimitPair(0,0),e._swingLimit=new $.PxJointLimitCone(1.5,1.5),e._drive_x=new $.D6JointDrive,e._drive_y=new $.D6JointDrive,e._drive_z=new $.D6JointDrive,e._drive_twist=new $.D6JointDrive,e._drive_swing1=new $.D6JointDrive,e._drive_swing2=new $.D6JointDrive,e._drive=[e._drive_x,e._drive_y,e._drive_z,e._drive_twist,e._drive_swing1,e._drive_swing2])},l(e,[{key:"constraint",get:function(){return this._com}}]),e}(ue);Pe._jointToleranceScale=null,Pe._linearLimitX=null,Pe._linearLimitY=null,Pe._linearLimitZ=null,Pe._twistLimit=null,Pe._swingLimit=null,Pe._drive_x=null,Pe._drive_y=null,Pe._drive_z=null,Pe._drive_twist=null,Pe._drive_swing1=null,Pe._drive_swing2=null,Pe._drive=[];var Ie=new u(0,0,0),Me=new u(0,1,0),Be=function(t){function e(){return t.apply(this,arguments)||this}h(e,t);var i=e.prototype;return i.onComponentSet=function(){this.create()},i.create=function(){t.prototype.release.call(this),this.component.node.getWorldPosition(Ie),Ie.add(this.scaledCenter);var e=X.physics.createMaterial(.5,.5,.5),i=n.instance.physicsWorld,s=new $.PxBoxControllerDesc;if(s.halfHeight=this.component.halfHeight,s.halfSideExtent=this.component.halfSideExtent,s.halfForwardExtent=this.component.halfForwardExtent,s.density=10,s.scaleCoeff=.8,s.volumeGrowth=1.5,s.contactOffset=this.component.skinWidth,s.stepOffset=this.component.stepOffset,s.slopeLimit=Math.cos(c(this.component.slopeLimit)),s.upDirection=Me,s.position={x:Ie.x,y:Ie.y,z:Ie.z},s.setMaterial(e),s.setReportCallback($.PxUserControllerHitReport.implement(i.callback.controllerHitReportCB)),this._impl=$.createBoxCharacterController(i.controllerManager,s),this._impl.$$){$.IMPL_PTR[this._impl.$$.ptr]=this;var o=this._impl.getShape().$$.ptr;$.IMPL_PTR[o]=this}this.updateScale()},i.setHalfHeight=function(){this.updateScale()},i.setHalfSideExtent=function(){this.updateScale()},i.setHalfForwardExtent=function(){this.updateScale()},i.updateScale=function(){this.updateGeometry()},i.updateGeometry=function(){var t=this.component.node.worldScale;this._impl.setHalfSideExtent(this.component.halfSideExtent*t.x),this._impl.setHalfHeight(this.component.halfHeight*t.y),this._impl.setHalfForwardExtent(this.component.halfForwardExtent*t.z)},l(e,[{key:"component",get:function(){return this._comp}}]),e}(bt),Fe=new u(0,0,0),Oe=new u(0,1,0),xe=function(t){function e(){return t.apply(this,arguments)||this}h(e,t);var i=e.prototype;return i.onComponentSet=function(){this.create()},i.create=function(){t.prototype.release.call(this),this.component.node.getWorldPosition(Fe),Fe.add(this.scaledCenter);var e=X.physics.createMaterial(.5,.5,.5),i=n.instance.physicsWorld,s=new $.PxCapsuleControllerDesc;if(s.radius=this.component.radius,s.height=this.component.height,s.climbingMode=$.PxCapsuleClimbingMode.eCONSTRAINED,s.density=10,s.scaleCoeff=.8,s.volumeGrowth=1.5,s.contactOffset=this.component.skinWidth,s.stepOffset=this.component.stepOffset,s.slopeLimit=Math.cos(c(this.component.slopeLimit)),s.upDirection=Oe,s.position={x:Fe.x,y:Fe.y,z:Fe.z},s.setMaterial(e),s.setReportCallback($.PxUserControllerHitReport.implement(i.callback.controllerHitReportCB)),this._impl=$.createCapsuleCharacterController(i.controllerManager,s),this._impl.$$){$.IMPL_PTR[this._impl.$$.ptr]=this;var o=this._impl.getShape().$$.ptr;$.IMPL_PTR[o]=this}this.updateScale()},i.setRadius=function(){this._impl&&this.updateScale()},i.setHeight=function(){this._impl&&this.updateScale()},i.updateScale=function(){this.updateGeometry()},i.updateGeometry=function(){var t=this.component.node.worldScale,e=this.component.radius*Math.abs(y(t.x,t.z)),i=this.component.height*Math.abs(t.y);this._impl.setRadius(Math.max(1e-4,e)),this._impl.setHeight(Math.max(1e-4,i))},l(e,[{key:"component",get:function(){return this._comp}}]),e}(bt);L.once(P.EVENT_PRE_SUBSYSTEM_INIT,(function(){o.register("physx",{PhysicsWorld:Yt,RigidBody:ne,BoxShape:oe,SphereShape:se,CapsuleShape:re,TrimeshShape:le,CylinderShape:he,ConeShape:de,TerrainShape:ce,PlaneShape:ae,PointToPointConstraint:ye,HingeConstraint:ve,FixedConstraint:fe,ConfigurableConstraint:Pe,BoxCharacterController:Be,CapsuleCharacterController:xe})}))}}}));
