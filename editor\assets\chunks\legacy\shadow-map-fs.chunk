// Copyright (c) 2017-2020 Xiamen Yaji Software Co., Ltd.

in highp vec4 v_shadowPos;

#include <legacy/shadow-map-base>

#pragma define CC_SHADOW_POSITION v_shadowPos

// Just to be compatible with old projects only
#if CC_RECEIVE_SHADOW && CC_SHADOW_TYPE == CC_SHADOW_MAP
void CC_SPOT_SHADOW_FACTOR(out float realtimeShadow, vec3 worldPos, vec3 lightPos, vec3 normal, vec2 shadowBias) {
    realtimeShadow = CCSpotShadowFactorBase(CC_SHADOW_POSITION, worldPos, shadowBias);
}

void CC_SHADOW_FACTOR(out float realtimeShadow, vec3 N, float NL, vec2 shadowBias) {
    realtimeShadow = CCShadowFactorBase(CC_SHADOW_POSITION, N, shadowBias);
}

void CC_CSM_FACTOR(out float realtimeShadow, vec3 worldPos, vec3 N, float NL, vec2 shadowBias) {
    realtimeShadow = CCCSMFactorBase(worldPos, N, shadowBias);
}
#endif
