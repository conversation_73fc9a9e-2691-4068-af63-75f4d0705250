System.register(["./prefab-BQYc0LyR.js","./create-mesh-o_2FMF_K.js","./debug-view-BP17WHcy.js","./global-exports-CLZKKIY2.js","./mesh-renderer-CTCVb-Pf.js","./mesh-Ba1cTOGw.js","./index-Y4La_nfG.js","./util-K-7Ucy5K.js","./skeleton-BrWwQslM.js","./gc-object-D18ulfCO.js","./component-CsuvAQKv.js","./deprecated-D5UVm7fE.js","./scene-ArUG4OfI.js","./device-manager-B8lyUDPi.js","./buffer-barrier-q_79u36H.js","./deprecated-6ty78xzL.js","./pipeline-state-manager-DQyhxoC_.js","./deprecated-CuuzltLj.js","./node-event-DTNosVQv.js","./factory-BOc5khhM.js","./camera-component-X7pwLmnP.js","./touch-B157r-vS.js","./model-renderer-D7qfPDfZ.js","./renderer-CZheciPr.js","./wasm-minigame-DBi57dFz.js","./deprecated-Bf8XgTPJ.js","./director-8iUu7HD2.js","./zlib.min-CyXMsivM.js","./capsule-DyZvzyLy.js"],(function(e){"use strict";var t,i,n,o,r,s,a,h,l,c,u,p,d,_,g,y,f,m,b,w,D,P,L,O,v,R,S,M,C,B,k,z,T,x,E,A,N,j,I,F,U,H,G,W,X,V,K,Z,Y,q,$,Q,J,ee,te,ie,ne,oe,re,se,ae,he,le,ce,ue,pe,de,_e,ge,ye,fe,me,be,we,De,Pe;return{setters:[function(e){t=e.f},function(e){i=e.M,n=e._,o=e.r},function(e){r=e.m,s=e.r,a=e.w,h=e.L,l=e.l,c=e.f,u=e.t,p=e.o,d=e.p,_=e.n},function(e){g=e.c,y=e.l},function(t){f=t.M,e({MeshRenderer:t.M,ModelComponent:t.M})},function(t){m=t.M,e("Mesh",t.M)},function(e){b=e.M,w=e.h,D=e.b,P=e.c,L=e.a,O=e.t,v=e.s,R=e.C,S=e.p,M=e.g,C=e.j,B=e.f,k=e.r,z=e.n,T=e.q,x=e.B},function(e){E=e.a},function(t){e("Skeleton",t.S)},function(e){A=e.j,N=e.E,j=e.a,I=e._,F=e.b,U=e.l,H=e.C,G=e.n,W=e.s,X=e.S,V=e.w,K=e.K,Z=e.F,Y=e.o,q=e.d},function(e){$=e.C},function(t){Q=t.D,J=t.g,ee=t.S,te=t.P,ie=t.f,ne=t.L,oe=t.e,re=t.R,e("ReflectionProbeType",t.R)},function(e){se=e.p,ae=e.C,he=e.l,le=e.e,ce=e.S,ue=e.h,pe=e.T,de=e.N,_e=e.a6},null,null,null,function(e){ge=e.L,ye=e.C},function(t){e({BatchedSkinningModelComponent:t.h,SkinnedMeshBatchRenderer:t.h,SkinnedMeshRenderer:t.b,SkinnedMeshUnit:t.i,SkinningModelComponent:t.b,SkinningModelUnit:t.i})},function(e){fe=e.N},function(e){me=e.I,be=e.P,we=e.T,De=e.W},function(e){Pe=e.C},null,null,null,null,null,null,null,null],execute:function(){var Le,Oe,ve,Re,Se,Me,Ce,Be,ke,ze,Te,xe,Ee,Ae,Ne,je,Ie=Object.freeze({__proto__:null,MeshUtils:i,createMesh:n,find:t,mapBuffer:r,readBuffer:s,readMesh:o,toPPM:function(e,t,i){return"P3 "+t+" "+i+" 255\n"+e.filter((function(e,t){return t%4<3})).toString()+"\n"},writeBuffer:a});function Fe(e,t){var i=e.sharedMaterials.length;if(i!==t.sharedMaterials.length)return!1;for(var n=0;n<i;n++)if(e.getRenderMaterial(n)!==t.getRenderMaterial(n))return!1;return!0}e("utils",Ie),e("BatchingUtility",function(){function e(){}return e.batchStaticModel=function(e,t){var i=e.getComponentsInChildren(f);if(i.length<2)return console.error("the number of static models to batch is less than 2,it needn't batch."),!1;for(var n=1;n<i.length;n++){if(!i[0].mesh.validateMergingMesh(i[n].mesh))return console.error("the meshes of "+i[0].node.name+" and "+i[n].node.name+" can't be merged"),!1;if(!Fe(i[0],i[n]))return console.error("the materials of "+i[0].node.name+" and "+i[n].node.name+" can't be merged"),!1}var o=new m,r=new b,s=new b;e.getWorldMatrix(s),b.invert(s,s);for(var a=0;a<i.length;a++){var h=i[a];h.node.getWorldMatrix(r),b.multiply(r,s,r),o.merge(i[a].mesh,r),h.enabled=!1}var l=t.addComponent(f);return l.mesh=o,l.sharedMaterials=i[0].sharedMaterials,!0},e.unbatchStaticModel=function(e,t){for(var i=e.getComponentsInChildren(f),n=0;n<i.length;n++)i[n].enabled=!0;var o=t.getComponent(f);return o&&(o.mesh&&o.mesh.destroyRenderingMesh(),o.destroy()),!0},e}()),w(f.prototype,"MeshRenderer.prototype",[{name:"enableDynamicBatching"},{name:"recieveShadows"}]),g.ModelComponent=f,A(f,"cc.ModelComponent");var Ue,He,Ge,We,Xe,Ve,Ke,Ze,Ye,qe,$e,Qe,Je,et,tt,it,nt,ot,rt,st,at,ht,lt,ct,ut,pt,dt,_t,gt,yt,ft,mt,bt,wt,Dt,Pt,Lt,Ot,vt,Rt,St,Mt,Ct,Bt,kt,zt,Tt,xt,Et,At,Nt,jt,It,Ft,Ut,Ht,Gt,Wt,Xt,Vt,Kt=new D,Zt=N({LUMINOUS_FLUX:0,LUMINANCE:1}),Yt=P("cc.StaticLightSettings")((Oe=function(){function e(){this._baked=ve&&ve(),this._editorOnly=Re&&Re(),this._castShadow=Se&&Se()}return j(e,[{key:"editorOnly",get:function(){return this._editorOnly},set:function(e){this._editorOnly=e}},{key:"baked",get:function(){return this._baked},set:function(e){this._baked=e}},{key:"castShadow",get:function(){return this._castShadow},set:function(e){this._castShadow=e}}]),e}(),ve=L(Oe.prototype,"_baked",[v],(function(){return!1})),Re=L(Oe.prototype,"_editorOnly",[v],(function(){return!1})),Se=L(Oe.prototype,"_castShadow",[v],(function(){return!1})),Le=Oe))||Le,qt=(Me=P("cc.Light"),Ce=O(Yt),Be=O(ge.BitMask),Me((je=function(e){function t(){var t;return(t=e.call(this)||this)._color=Te&&Te(),t._useColorTemperature=xe&&xe(),t._colorTemperature=Ee&&Ee(),t._staticSettings=Ae&&Ae(),t._visibility=Ne&&Ne(),t._type=h.UNKNOWN,t._lightType=void 0,t._light=null,t._lightType=l,t}I(t,e);var i=t.prototype;return i.onLoad=function(){this._createLight()},i.onEnable=function(){this._attachToScene()},i.onDisable=function(){this._detachFromScene()},i.onDestroy=function(){this._destroyLight()},i._createLight=function(){this._light||(this._light=g.director.root.createLight(this._lightType)),this.color=this._color,this.useColorTemperature=this._useColorTemperature,this.colorTemperature=this._colorTemperature,this._light.node=this.node,this._light.baked=this.baked,this._light.visibility=this.visibility},i._destroyLight=function(){this._light&&(g.director.root.recycleLight(this._light),this._light=null)},i._attachToScene=function(){if(this._detachFromScene(),this._light&&!this._light.scene&&this.node.scene){var e=this._getRenderScene();switch(this._type){case h.DIRECTIONAL:e.addDirectionalLight(this._light),e.setMainLight(this._light);break;case h.SPHERE:e.addSphereLight(this._light);break;case h.SPOT:e.addSpotLight(this._light);break;case h.POINT:e.addPointLight(this._light);break;case h.RANGED_DIRECTIONAL:e.addRangedDirLight(this._light)}}},i._detachFromScene=function(){if(this._light&&this._light.scene){var e=this._light.scene;switch(this._type){case h.DIRECTIONAL:e.removeDirectionalLight(this._light),e.unsetMainLight(this._light);break;case h.SPHERE:e.removeSphereLight(this._light);break;case h.SPOT:e.removeSpotLight(this._light);break;case h.POINT:e.removePointLight(this._light);break;case h.RANGED_DIRECTIONAL:e.removeRangedDirLight(this._light)}}},i._onUpdateReceiveDirLight=function(){},j(t,[{key:"color",get:function(){return this._color},set:function(e){this._color=e.clone(),this._light&&(Kt.x=e.r/255,Kt.y=e.g/255,Kt.z=e.b/255,this._light.color=Kt)}},{key:"useColorTemperature",get:function(){return this._useColorTemperature},set:function(e){this._useColorTemperature=e,this._light&&(this._light.useColorTemperature=e)}},{key:"colorTemperature",get:function(){return this._colorTemperature},set:function(e){this._colorTemperature=e,this._light&&(this._light.colorTemperature=e)}},{key:"staticSettings",get:function(){return this._staticSettings},set:function(e){this._staticSettings=e}},{key:"type",get:function(){return this._type}},{key:"baked",get:function(){return this.staticSettings.baked},set:function(e){this.staticSettings.baked=e,null!==this._light&&(this._light.baked=e)}},{key:"visibility",get:function(){return this._visibility},set:function(e){this._visibility=e,this._light&&(this._light.visibility=e),this._onUpdateReceiveDirLight()}}]),t}($),je.Type=h,je.PhotometricTerm=Zt,Te=L((ze=je).prototype,"_color",[v],(function(){return R.WHITE.clone()})),xe=L(ze.prototype,"_useColorTemperature",[v],(function(){return!1})),Ee=L(ze.prototype,"_colorTemperature",[v],(function(){return 6550})),Ae=L(ze.prototype,"_staticSettings",[v],(function(){return new Yt})),Ne=L(ze.prototype,"_visibility",[v],(function(){return ye})),F(ze.prototype,"staticSettings",[Ce],Object.getOwnPropertyDescriptor(ze.prototype,"staticSettings"),ze.prototype),F(ze.prototype,"visibility",[Be],Object.getOwnPropertyDescriptor(ze.prototype,"visibility"),ze.prototype),ke=ze))||ke);e({Light:qt,LightComponent:qt});var $t,Qt,Jt,ei,ti,ii,ni,oi,ri,si,ai,hi,li,ci,ui=S,pi=v,di=C,_i=O,gi=(Ue=P("cc.DirectionalLight"),He=di("_illuminance"),Ge=_i(U),We=ui({group:{name:"DynamicShadowSettings",displayOrder:1}}),Xe=_i(H),Ve=ui({group:{name:"DynamicShadowSettings",displayOrder:5}}),Ke=_i(se),Ze=ui({group:{name:"DynamicShadowSettings",displayOrder:6}}),Ye=_i(G),qe=ui({group:{name:"DynamicShadowSettings",displayOrder:7}}),$e=_i(G),Qe=ui({group:{name:"DynamicShadowSettings",displayOrder:8}}),Je=_i(G),et=ui({group:{name:"DynamicShadowSettings",displayOrder:9}}),tt=_i(G),it=ui({group:{name:"DynamicShadowSettings",displayOrder:22}}),nt=_i(G),ot=ui({group:{name:"DynamicShadowSettings",displayOrder:10}}),rt=_i(ae),st=ui({group:{name:"DynamicShadowSettings",displayOrder:11}}),at=_i(H),ht=ui({group:{name:"DynamicShadowSettings",displayOrder:12}}),lt=_i(G),ct=ui({group:{name:"DynamicShadowSettings",displayOrder:13}}),ut=_i(he),pt=ui({group:{name:"DynamicShadowSettings",displayOrder:14}}),dt=_i(H),_t=ui({group:{name:"DynamicShadowSettings",displayOrder:15}}),gt=_i(G),yt=ui({group:{name:"DynamicShadowSettings",displayOrder:16}}),ft=_i(G),mt=ui({group:{name:"DynamicShadowSettings",displayOrder:17}}),bt=_i(G),wt=ui({group:{name:"DynamicShadowSettings",displayOrder:19}}),Dt=_i(H),Pt=ui({group:{name:"DynamicShadowSettings",displayOrder:20}}),Lt=_i(H),Ot=ui({group:{name:"DynamicShadowSettings",displayOrder:21}}),vt=_i(G),Ue((St=function(e){function t(){var t;return(t=e.call(this)||this)._illuminanceHDR=Mt&&Mt(),t._illuminanceLDR=Ct&&Ct(),t._shadowEnabled=Bt&&Bt(),t._shadowPcf=kt&&kt(),t._shadowBias=zt&&zt(),t._shadowNormalBias=Tt&&Tt(),t._shadowSaturation=xt&&xt(),t._shadowDistance=Et&&Et(),t._shadowInvisibleOcclusionRange=At&&At(),t._csmLevel=Nt&&Nt(),t._csmLayerLambda=jt&&jt(),t._csmOptimizationMode=It&&It(),t._csmAdvancedOptions=Ft&&Ft(),t._csmLayersTransition=Ut&&Ut(),t._csmTransitionRange=Ht&&Ht(),t._shadowFixedArea=Gt&&Gt(),t._shadowNear=Wt&&Wt(),t._shadowFar=Xt&&Xt(),t._shadowOrthoSize=Vt&&Vt(),t._lightType=Q,W.querySettings(X.RENDERING,"highQualityMode")&&(t._shadowPcf=se.SOFT_2X,t._shadowDistance=50,t.enableCSM=!0,t.staticSettings.castShadow=!0),t}I(t,e);var i=t.prototype;return i._createLight=function(){if(e.prototype._createLight.call(this),this._type=h.DIRECTIONAL,this._light){var t=this._light;t.illuminanceHDR=this._illuminanceHDR,t.illuminanceLDR=this._illuminanceLDR,t.shadowEnabled=this._shadowEnabled,t.shadowPcf=this._shadowPcf,t.shadowBias=this._shadowBias,t.shadowNormalBias=this._shadowNormalBias,t.shadowSaturation=this._shadowSaturation,t.shadowDistance=this._shadowDistance,t.shadowInvisibleOcclusionRange=this._shadowInvisibleOcclusionRange,t.shadowFixedArea=this._shadowFixedArea,t.shadowNear=this._shadowNear,t.shadowFar=this._shadowFar,t.shadowOrthoSize=this._shadowOrthoSize,t.csmLevel=this._csmLevel,t.csmLayerLambda=this._csmLayerLambda,t.csmOptimizationMode=this._csmOptimizationMode,t.csmLayersTransition=this._csmLayersTransition,t.csmTransitionRange=this._csmTransitionRange}},i._onUpdateReceiveDirLight=function(){if(this._light){e.prototype._onUpdateReceiveDirLight.call(this);var t=this.node.scene;if(t&&t.renderScene&&t.renderScene.mainLight===this._light)for(var i=t.renderScene.models,n=0;n<i.length;n++){var o=i[n];if(o.node){var r=o.node.getComponent(f);r&&r.onUpdateReceiveDirLight(this._visibility)}}}},j(t,[{key:"illuminance",get:function(){return le().isHDR?this._illuminanceHDR:this._illuminanceLDR},set:function(e){le().isHDR?(this._illuminanceHDR=e,this._light&&(this._light.illuminanceHDR=this._illuminanceHDR)):(this._illuminanceLDR=e,this._light&&(this._light.illuminanceLDR=this._illuminanceLDR))}},{key:"shadowEnabled",get:function(){return this._shadowEnabled},set:function(e){this._shadowEnabled=e,this._light&&(this._light.shadowEnabled=this._shadowEnabled)}},{key:"shadowPcf",get:function(){return this._shadowPcf},set:function(e){this._shadowPcf=e,this._light&&(this._light.shadowPcf=this._shadowPcf)}},{key:"shadowBias",get:function(){return this._shadowBias},set:function(e){this._shadowBias=e,this._light&&(this._light.shadowBias=this._shadowBias)}},{key:"shadowNormalBias",get:function(){return this._shadowNormalBias},set:function(e){this._shadowNormalBias=e,this._light&&(this._light.shadowNormalBias=this._shadowNormalBias)}},{key:"shadowSaturation",get:function(){return this._shadowSaturation},set:function(e){this._shadowSaturation=M(e,0,1),this._light&&(this._light.shadowSaturation=this._shadowSaturation)}},{key:"shadowDistance",get:function(){return this._shadowDistance},set:function(e){this._shadowDistance=Math.min(e,ce.MAX_FAR),this._shadowDistance/.1<10&&V(15003,this._shadowDistance),this._light&&(this._light.shadowDistance=this._shadowDistance,this._light.csmNeedUpdate=!0)}},{key:"shadowInvisibleOcclusionRange",get:function(){return this._shadowInvisibleOcclusionRange},set:function(e){this._shadowInvisibleOcclusionRange=Math.min(e,ce.MAX_FAR),this._light&&(this._light.shadowInvisibleOcclusionRange=this._shadowInvisibleOcclusionRange)}},{key:"csmLevel",get:function(){return this._csmLevel},set:function(e){this._csmLevel=e,this._light&&(this._light.csmLevel=this._csmLevel,this._light.csmNeedUpdate=!0)}},{key:"enableCSM",get:function(){return this._csmLevel>ae.LEVEL_1},set:function(e){this._csmLevel=e?ae.LEVEL_4:ae.LEVEL_1,this._light&&(this._light.csmLevel=this._csmLevel,this._light.csmNeedUpdate=!0)}},{key:"csmLayerLambda",get:function(){return this._csmLayerLambda},set:function(e){this._csmLayerLambda=e,this._light&&(this._light.csmLayerLambda=this._csmLayerLambda,this._light.csmNeedUpdate=!0)}},{key:"csmOptimizationMode",get:function(){return this._csmOptimizationMode},set:function(e){this._csmOptimizationMode=e,this._light&&(this._light.csmOptimizationMode=this._csmOptimizationMode)}},{key:"shadowFixedArea",get:function(){return this._shadowFixedArea},set:function(e){this._shadowFixedArea=e,this._light&&(this._light.shadowFixedArea=this._shadowFixedArea)}},{key:"shadowNear",get:function(){return this._shadowNear},set:function(e){this._shadowNear=e,this._light&&(this._light.shadowNear=this._shadowNear)}},{key:"shadowFar",get:function(){return this._shadowFar},set:function(e){this._shadowFar=Math.min(e,ce.MAX_FAR),this._light&&(this._light.shadowFar=this._shadowFar)}},{key:"shadowOrthoSize",get:function(){return this._shadowOrthoSize},set:function(e){this._shadowOrthoSize=e,this._light&&(this._light.shadowOrthoSize=this._shadowOrthoSize)}},{key:"csmAdvancedOptions",get:function(){return this._csmAdvancedOptions},set:function(e){this._csmAdvancedOptions=e}},{key:"csmLayersTransition",get:function(){return this._csmLayersTransition},set:function(e){this._csmLayersTransition=e,this._light&&(this._light.csmLayersTransition=e)}},{key:"csmTransitionRange",get:function(){return this._csmTransitionRange},set:function(e){this._csmTransitionRange=e,this._light&&(this._light.csmTransitionRange=e)}}]),t}(qt),Mt=L(St.prototype,"_illuminanceHDR",[ui,He],(function(){return 65e3})),Ct=L(St.prototype,"_illuminanceLDR",[pi],(function(){return 65e3*c.standardExposureValue})),Bt=L(St.prototype,"_shadowEnabled",[pi],(function(){return!1})),kt=L(St.prototype,"_shadowPcf",[pi],(function(){return se.HARD})),zt=L(St.prototype,"_shadowBias",[pi],(function(){return 1e-5})),Tt=L(St.prototype,"_shadowNormalBias",[pi],(function(){return 0})),xt=L(St.prototype,"_shadowSaturation",[pi],(function(){return 1})),Et=L(St.prototype,"_shadowDistance",[pi],(function(){return 50})),At=L(St.prototype,"_shadowInvisibleOcclusionRange",[pi],(function(){return 200})),Nt=L(St.prototype,"_csmLevel",[pi],(function(){return ae.LEVEL_4})),jt=L(St.prototype,"_csmLayerLambda",[pi],(function(){return.75})),It=L(St.prototype,"_csmOptimizationMode",[pi],(function(){return he.RemoveDuplicates})),Ft=L(St.prototype,"_csmAdvancedOptions",[pi],(function(){return!1})),Ut=L(St.prototype,"_csmLayersTransition",[pi],(function(){return!1})),Ht=L(St.prototype,"_csmTransitionRange",[pi],(function(){return.05})),Gt=L(St.prototype,"_shadowFixedArea",[pi],(function(){return!1})),Wt=L(St.prototype,"_shadowNear",[pi],(function(){return.1})),Xt=L(St.prototype,"_shadowFar",[pi],(function(){return 10})),Vt=L(St.prototype,"_shadowOrthoSize",[pi],(function(){return 5})),F(St.prototype,"illuminance",[Ge],Object.getOwnPropertyDescriptor(St.prototype,"illuminance"),St.prototype),F(St.prototype,"shadowEnabled",[We,Xe],Object.getOwnPropertyDescriptor(St.prototype,"shadowEnabled"),St.prototype),F(St.prototype,"shadowPcf",[Ve,Ke],Object.getOwnPropertyDescriptor(St.prototype,"shadowPcf"),St.prototype),F(St.prototype,"shadowBias",[Ze,Ye],Object.getOwnPropertyDescriptor(St.prototype,"shadowBias"),St.prototype),F(St.prototype,"shadowNormalBias",[qe,$e],Object.getOwnPropertyDescriptor(St.prototype,"shadowNormalBias"),St.prototype),F(St.prototype,"shadowSaturation",[Qe,Je],Object.getOwnPropertyDescriptor(St.prototype,"shadowSaturation"),St.prototype),F(St.prototype,"shadowDistance",[et,tt],Object.getOwnPropertyDescriptor(St.prototype,"shadowDistance"),St.prototype),F(St.prototype,"shadowInvisibleOcclusionRange",[it,nt],Object.getOwnPropertyDescriptor(St.prototype,"shadowInvisibleOcclusionRange"),St.prototype),F(St.prototype,"csmLevel",[ot,rt],Object.getOwnPropertyDescriptor(St.prototype,"csmLevel"),St.prototype),F(St.prototype,"enableCSM",[st,at],Object.getOwnPropertyDescriptor(St.prototype,"enableCSM"),St.prototype),F(St.prototype,"csmLayerLambda",[ht,lt],Object.getOwnPropertyDescriptor(St.prototype,"csmLayerLambda"),St.prototype),F(St.prototype,"csmOptimizationMode",[ct,ut],Object.getOwnPropertyDescriptor(St.prototype,"csmOptimizationMode"),St.prototype),F(St.prototype,"shadowFixedArea",[pt,dt],Object.getOwnPropertyDescriptor(St.prototype,"shadowFixedArea"),St.prototype),F(St.prototype,"shadowNear",[_t,gt],Object.getOwnPropertyDescriptor(St.prototype,"shadowNear"),St.prototype),F(St.prototype,"shadowFar",[yt,ft],Object.getOwnPropertyDescriptor(St.prototype,"shadowFar"),St.prototype),F(St.prototype,"shadowOrthoSize",[mt,bt],Object.getOwnPropertyDescriptor(St.prototype,"shadowOrthoSize"),St.prototype),F(St.prototype,"csmAdvancedOptions",[wt,Dt],Object.getOwnPropertyDescriptor(St.prototype,"csmAdvancedOptions"),St.prototype),F(St.prototype,"csmLayersTransition",[Pt,Lt],Object.getOwnPropertyDescriptor(St.prototype,"csmLayersTransition"),St.prototype),F(St.prototype,"csmTransitionRange",[Ot,vt],Object.getOwnPropertyDescriptor(St.prototype,"csmTransitionRange"),St.prototype),Rt=St))||Rt);e({DirectionalLight:gi,DirectionalLightComponent:gi});var yi,fi,mi,bi,wi,Di,Pi,Li,Oi,vi,Ri,Si,Mi,Ci,Bi,ki,zi,Ti,xi,Ei,Ai,Ni,ji,Ii,Fi,Ui=($t=P("cc.SphereLight"),Qt=C("_luminance"),Jt=O(U),ei=O(U),ti=O(Zt),ii=O(G),ni=O(G),$t((ri=function(e){function t(){var t;return(t=e.call(this)||this)._size=si&&si(),t._luminanceHDR=ai&&ai(),t._luminanceLDR=hi&&hi(),t._term=li&&li(),t._range=ci&&ci(),t._lightType=J,t}return I(t,e),t.prototype._createLight=function(){e.prototype._createLight.call(this),this._type=h.SPHERE,this.size=this._size,this.range=this._range,this._light&&(this._light.luminanceHDR=this._luminanceHDR,this._light.luminanceLDR=this._luminanceLDR)},j(t,[{key:"luminousFlux",get:function(){return le().isHDR?this._luminanceHDR*u(this._size):this._luminanceLDR},set:function(e){var t=0;le().isHDR?(this._luminanceHDR=e/u(this._size),t=this._luminanceHDR):(this._luminanceLDR=e,t=this._luminanceLDR),this._light&&(this._light.luminance=t)}},{key:"luminance",get:function(){return le().isHDR?this._luminanceHDR:this._luminanceLDR},set:function(e){le().isHDR?(this._luminanceHDR=e,this._light&&(this._light.luminanceHDR=this._luminanceHDR)):(this._luminanceLDR=e,this._light&&(this._light.luminanceLDR=this._luminanceLDR))}},{key:"term",get:function(){return this._term},set:function(e){this._term=e}},{key:"size",get:function(){return this._size},set:function(e){this._size=e,this._light&&(this._light.size=e)}},{key:"range",get:function(){return this._range},set:function(e){this._range=e,this._light&&(this._light.range=e)}}]),t}(qt),si=L(ri.prototype,"_size",[v],(function(){return.15})),ai=L(ri.prototype,"_luminanceHDR",[v,Qt],(function(){return 1700/u(.15)})),hi=L(ri.prototype,"_luminanceLDR",[v],(function(){return 1700/u(.15)*c.standardExposureValue*c.standardLightMeterScale})),li=L(ri.prototype,"_term",[v],(function(){return Zt.LUMINOUS_FLUX})),ci=L(ri.prototype,"_range",[v],(function(){return 1})),F(ri.prototype,"luminousFlux",[Jt],Object.getOwnPropertyDescriptor(ri.prototype,"luminousFlux"),ri.prototype),F(ri.prototype,"luminance",[ei],Object.getOwnPropertyDescriptor(ri.prototype,"luminance"),ri.prototype),F(ri.prototype,"term",[ti],Object.getOwnPropertyDescriptor(ri.prototype,"term"),ri.prototype),F(ri.prototype,"size",[ii],Object.getOwnPropertyDescriptor(ri.prototype,"size"),ri.prototype),F(ri.prototype,"range",[ni],Object.getOwnPropertyDescriptor(ri.prototype,"range"),ri.prototype),oi=ri))||oi);e({SphereLight:Ui,SphereLightComponent:Ui});var Hi,Gi,Wi,Xi,Vi,Ki,Zi,Yi,qi,$i,Qi,Ji,en,tn,nn,on,rn,sn,an,hn,ln,cn,un,pn,dn,_n,gn,yn,fn,mn,bn,wn,Dn,Pn,Ln,On,vn=O,Rn=v,Sn=C,Mn=S,Cn=(yi=P("cc.SpotLight"),fi=Sn("_luminance"),mi=vn(Zt),bi=vn(G),wi=Mn({group:{name:"DynamicShadowSettings",displayOrder:1}}),Di=vn(H),Pi=Mn({group:{name:"DynamicShadowSettings",displayOrder:2}}),Li=vn(se),Oi=Mn({group:{name:"DynamicShadowSettings",displayOrder:3}}),vi=vn(G),Ri=Mn({group:{name:"DynamicShadowSettings",displayOrder:4}}),Si=vn(G),yi((Ci=function(e){function t(){var t;return(t=e.call(this)||this)._size=Bi&&Bi(),t._luminanceHDR=ki&&ki(),t._luminanceLDR=zi&&zi(),t._term=Ti&&Ti(),t._range=xi&&xi(),t._spotAngle=Ei&&Ei(),t._angleAttenuationStrength=Ai&&Ai(),t._shadowEnabled=Ni&&Ni(),t._shadowPcf=ji&&ji(),t._shadowBias=Ii&&Ii(),t._shadowNormalBias=Fi&&Fi(),t._lightType=ee,t}return I(t,e),t.prototype._createLight=function(){if(e.prototype._createLight.call(this),this._type=h.SPOT,this.size=this._size,this.range=this._range,this.spotAngle=this._spotAngle,this.angleAttenuationStrength=this._angleAttenuationStrength,this._light){var t=this._light;t.luminanceHDR=this._luminanceHDR,t.luminanceLDR=this._luminanceLDR,t.shadowEnabled=this._shadowEnabled,t.shadowPcf=this._shadowPcf,t.shadowBias=this._shadowBias,t.shadowNormalBias=this._shadowNormalBias}},j(t,[{key:"luminousFlux",get:function(){return le().isHDR?this._luminanceHDR*u(this._size):this._luminanceLDR},set:function(e){var t=0;le().isHDR?(this._luminanceHDR=e/u(this._size),t=this._luminanceHDR):(this._luminanceLDR=e,t=this._luminanceLDR),this._light&&(this._light.luminance=t)}},{key:"luminance",get:function(){return le().isHDR?this._luminanceHDR:this._luminanceLDR},set:function(e){le().isHDR?(this._luminanceHDR=e,this._light&&(this._light.luminanceHDR=this._luminanceHDR)):(this._luminanceLDR=e,this._light&&(this._light.luminanceLDR=this._luminanceLDR))}},{key:"term",get:function(){return this._term},set:function(e){this._term=e}},{key:"size",get:function(){return this._size},set:function(e){this._size=e,this._light&&(this._light.size=e)}},{key:"range",get:function(){return this._range},set:function(e){this._range=e,this._light&&(this._light.range=e)}},{key:"spotAngle",get:function(){return this._spotAngle},set:function(e){this._spotAngle=e,this._light&&(this._light.spotAngle=B(e))}},{key:"angleAttenuationStrength",get:function(){return this._angleAttenuationStrength},set:function(e){this._angleAttenuationStrength=e,this._light&&(this._light.angleAttenuationStrength=e)}},{key:"shadowEnabled",get:function(){return this._shadowEnabled},set:function(e){this._shadowEnabled=e,this._light&&(this._light.shadowEnabled=e)}},{key:"shadowPcf",get:function(){return this._shadowPcf},set:function(e){this._shadowPcf=e,this._light&&(this._light.shadowPcf=e)}},{key:"shadowBias",get:function(){return this._shadowBias},set:function(e){this._shadowBias=e,this._light&&(this._light.shadowBias=e)}},{key:"shadowNormalBias",get:function(){return this._shadowNormalBias},set:function(e){this._shadowNormalBias=e,this._light&&(this._light.shadowNormalBias=e)}}]),t}(qt),Bi=L(Ci.prototype,"_size",[Rn],(function(){return.15})),ki=L(Ci.prototype,"_luminanceHDR",[Rn,fi],(function(){return 1700/u(.15)})),zi=L(Ci.prototype,"_luminanceLDR",[Rn],(function(){return 1700/u(.15)*c.standardExposureValue*c.standardLightMeterScale})),Ti=L(Ci.prototype,"_term",[Rn],(function(){return Zt.LUMINOUS_FLUX})),xi=L(Ci.prototype,"_range",[Rn],(function(){return 1})),Ei=L(Ci.prototype,"_spotAngle",[Rn],(function(){return 60})),Ai=L(Ci.prototype,"_angleAttenuationStrength",[Rn],(function(){return 0})),Ni=L(Ci.prototype,"_shadowEnabled",[Rn],(function(){return!1})),ji=L(Ci.prototype,"_shadowPcf",[Rn],(function(){return se.HARD})),Ii=L(Ci.prototype,"_shadowBias",[Rn],(function(){return 1e-5})),Fi=L(Ci.prototype,"_shadowNormalBias",[Rn],(function(){return 0})),F(Ci.prototype,"term",[mi],Object.getOwnPropertyDescriptor(Ci.prototype,"term"),Ci.prototype),F(Ci.prototype,"size",[bi],Object.getOwnPropertyDescriptor(Ci.prototype,"size"),Ci.prototype),F(Ci.prototype,"shadowEnabled",[wi,Di],Object.getOwnPropertyDescriptor(Ci.prototype,"shadowEnabled"),Ci.prototype),F(Ci.prototype,"shadowPcf",[Pi,Li],Object.getOwnPropertyDescriptor(Ci.prototype,"shadowPcf"),Ci.prototype),F(Ci.prototype,"shadowBias",[Oi,vi],Object.getOwnPropertyDescriptor(Ci.prototype,"shadowBias"),Ci.prototype),F(Ci.prototype,"shadowNormalBias",[Ri,Si],Object.getOwnPropertyDescriptor(Ci.prototype,"shadowNormalBias"),Ci.prototype),Mi=Ci))||Mi);e({SpotLight:Cn,SpotLightComponent:Cn}),e("PointLight",(Hi=P("cc.PointLight"),Gi=C("_luminance"),Wi=O(U),Xi=O(U),Vi=O(Zt),Ki=O(G),Hi((Yi=function(e){function t(){var t;return(t=e.call(this)||this)._luminanceHDR=qi&&qi(),t._luminanceLDR=$i&&$i(),t._term=Qi&&Qi(),t._range=Ji&&Ji(),t._lightType=te,t}return I(t,e),t.prototype._createLight=function(){e.prototype._createLight.call(this),this._type=h.POINT,this.range=this._range,this._light&&(this._light.luminanceHDR=this._luminanceHDR,this._light.luminanceLDR=this._luminanceLDR)},j(t,[{key:"luminousFlux",get:function(){return le().isHDR?this._luminanceHDR*u(1):this._luminanceLDR},set:function(e){var t=0;le().isHDR?(this._luminanceHDR=e/u(1),t=this._luminanceHDR):(this._luminanceLDR=e,t=this._luminanceLDR),this._light&&(this._light.luminance=t)}},{key:"luminance",get:function(){return le().isHDR?this._luminanceHDR:this._luminanceLDR},set:function(e){le().isHDR?(this._luminanceHDR=e,this._light&&(this._light.luminanceHDR=this._luminanceHDR)):(this._luminanceLDR=e,this._light&&(this._light.luminanceLDR=this._luminanceLDR))}},{key:"term",get:function(){return this._term},set:function(e){this._term=e}},{key:"range",get:function(){return this._range},set:function(e){this._range=e,this._light&&(this._light.range=e)}}]),t}(qt),qi=L(Yi.prototype,"_luminanceHDR",[v,Gi],(function(){return 1700/u(.15)})),$i=L(Yi.prototype,"_luminanceLDR",[v],(function(){return 1700/u(.15)*c.standardExposureValue*c.standardLightMeterScale})),Qi=L(Yi.prototype,"_term",[v],(function(){return Zt.LUMINOUS_FLUX})),Ji=L(Yi.prototype,"_range",[v],(function(){return 1})),F(Yi.prototype,"luminousFlux",[Wi],Object.getOwnPropertyDescriptor(Yi.prototype,"luminousFlux"),Yi.prototype),F(Yi.prototype,"luminance",[Xi],Object.getOwnPropertyDescriptor(Yi.prototype,"luminance"),Yi.prototype),F(Yi.prototype,"term",[Vi],Object.getOwnPropertyDescriptor(Yi.prototype,"term"),Yi.prototype),F(Yi.prototype,"range",[Ki],Object.getOwnPropertyDescriptor(Yi.prototype,"range"),Yi.prototype),Zi=Yi))||Zi)),e("RangedDirectionalLight",(en=P("cc.RangedDirectionalLight"),tn=C("_illuminance"),nn=O(U),en((rn=function(e){function t(){var t;return(t=e.call(this)||this)._illuminanceHDR=sn&&sn(),t._illuminanceLDR=an&&an(),t._lightType=ie,t}return I(t,e),t.prototype._createLight=function(){e.prototype._createLight.call(this),this._type=h.RANGED_DIRECTIONAL,this._light&&(this._light.illuminanceHDR=this._illuminanceHDR,this._light.illuminanceLDR=this._illuminanceLDR)},j(t,[{key:"illuminance",get:function(){return le().isHDR?this._illuminanceHDR:this._illuminanceLDR},set:function(e){le().isHDR?(this._illuminanceHDR=e,this._light&&(this._light.illuminanceHDR=this._illuminanceHDR)):(this._illuminanceLDR=e,this._light&&(this._light.illuminanceLDR=this._illuminanceLDR))}}]),t}(qt),sn=L(rn.prototype,"_illuminanceHDR",[S,tn],(function(){return 65e3})),an=L(rn.prototype,"_illuminanceLDR",[v],(function(){return 65e3*c.standardExposureValue})),F(rn.prototype,"illuminance",[nn],Object.getOwnPropertyDescriptor(rn.prototype,"illuminance"),rn.prototype),on=rn))||on)),g.LightComponent=qt,A(qt,"cc.LightComponent"),g.DirectionalLightComponent=gi,A(gi,"cc.DirectionalLightComponent"),g.SphereLightComponent=Ui,A(Ui,"cc.SphereLightComponent"),g.SpotLightComponent=Cn,A(Cn,"cc.SpotLightComponent"),k(Cn.prototype,"SpotLight.prototype",[{name:"luminousPower",newName:"luminousFlux",customGetter:function(){return this.luminousFlux},customSetter:function(e){this.luminousFlux=e}}]),k(Ui.prototype,"SphereLight.prototype",[{name:"luminousPower",newName:"luminousFlux",customGetter:function(){return this.luminousFlux},customSetter:function(e){this.luminousFlux=e}}]),k(qt.PhotometricTerm,"Light.PhotometricTerm",[{name:"LUMINOUS_POWER",newName:"LUMINOUS_FLUX"}]);var Bn,kn,zn,Tn,xn,En,An,Nn,jn,In,Fn,Un,Hn,Gn,Wn,Xn,Vn,Kn,Zn,Yn,qn,$n,Qn,Jn=[.25,.125,.01],eo=e("LOD",(hn=P("cc.LOD"),ln=O([f]),cn=O(G),un=O([f]),pn=O([U]),hn((_n=function(){function e(){this._screenUsagePercentage=gn&&gn(),this._renderers=yn&&yn(),this._LODData=new ne,this._modelAddedCallback=void 0,this._LODData.screenUsagePercentage=this._screenUsagePercentage,this._modelAddedCallback=null}var t=e.prototype;return t.insertRenderer=function(e,t){(e<0||e>this._renderers.length)&&(e=this._renderers.length),this._renderers.splice(e,0,t);var i=!1;return t.model&&(i=!0,this._LODData.addModel(t.model)),this._modelAddedCallback&&i&&this._modelAddedCallback(),t},t.deleteRenderer=function(e){var t,i=this._renderers.splice(e,1),n=i.length>0?null==(t=i[0])?void 0:t.model:null;return n&&this._LODData.eraseModel(n),i[0]},t.getRenderer=function(e){return this._renderers[e]||null},t.setRenderer=function(e,t){e<0||e>=this.rendererCount?K("setRenderer to LOD error, index out of range"):(this.deleteRenderer(e),this.insertRenderer(e,t))},j(e,[{key:"screenUsagePercentage",get:function(){return this._screenUsagePercentage},set:function(e){this._screenUsagePercentage=e,this._LODData.screenUsagePercentage=e}},{key:"renderers",get:function(){return this._renderers},set:function(e){if(e!==this._renderers){var t=!1;this._renderers.length=0,this._LODData.clearModels();for(var i=0;i<e.length;i++){var n;this._renderers[i]=e[i];var o=null==(n=e[i])?void 0:n.model;o&&(t=!0,this._LODData.addModel(o))}this._modelAddedCallback&&t&&this._modelAddedCallback()}}},{key:"triangleCount",get:function(){var e=[];return this._renderers.forEach((function(t){var i=0;if(t&&t.mesh){var n=t.mesh.struct.primitives;null==n||n.forEach((function(e){e&&e.indexView&&(i+=e.indexView.count)}))}e.push(i/3)})),e}},{key:"rendererCount",get:function(){return this._renderers.length}},{key:"lodData",get:function(){return this._LODData}},{key:"modelAddedCallback",set:function(e){this._modelAddedCallback=e}}]),e}(),gn=L(_n.prototype,"_screenUsagePercentage",[v],(function(){return 1})),yn=L(_n.prototype,"_renderers",[ln,v],(function(){return[]})),F(_n.prototype,"screenUsagePercentage",[cn],Object.getOwnPropertyDescriptor(_n.prototype,"screenUsagePercentage"),_n.prototype),F(_n.prototype,"renderers",[un],Object.getOwnPropertyDescriptor(_n.prototype,"renderers"),_n.prototype),F(_n.prototype,"triangleCount",[pn],Object.getOwnPropertyDescriptor(_n.prototype,"triangleCount"),_n.prototype),dn=_n))||dn)),to=(e("LODGroup",(fn=P("cc.LODGroup"),mn=O(G),bn=O([eo]),fn((Dn=function(e){function t(){var t;return(t=e.call(this)||this)._localBoundaryCenter=Pn&&Pn(),t._objectSize=Ln&&Ln(),t._LODs=On&&On(),t._lodGroup=new oe,t._eventRegistered=!1,t._forceUsedLevels=[],t}I(t,e);var i=t.prototype;return i.onLodModelAddedCallback=function(){0===this.objectSize&&this.recalculateBounds()},i.insertLOD=function(e,t,i){if((e<0||e>this.lodCount)&&(e=this.lodCount),i||(i=new eo),i.modelAddedCallback=this.onLodModelAddedCallback.bind(this),!t){var n=this.getLOD(e-1),o=this.getLOD(e);if(n&&o)t=(n.screenUsagePercentage+o.screenUsagePercentage)/2;else if(n&&!o)(t=n.screenUsagePercentage/2)>.01&&(t=.01);else if(o&&!n){t=o.screenUsagePercentage;var r=this.getLOD(e+1);o.screenUsagePercentage=(t+(r?r.screenUsagePercentage:0))/2}else t=Jn[0]}return i.screenUsagePercentage=t,this._LODs.splice(e,0,i),this._lodGroup.insertLOD(e,i.lodData),this._updateDataToScene(),this.node&&this._emitChangeNode(this.node),i},i.eraseLOD=function(e){if(e<0||e>=this.lodCount)return Z("eraseLOD error, index out of range"),null;var t=this._LODs[e];return t?(this._LODs.splice(e,1),this._lodGroup.eraseLOD(e),this._updateDataToScene(),this._emitChangeNode(this.node),t):(Z("eraseLOD error, LOD not exist at specified index."),null)},i.getLOD=function(e){return e<0||e>=this.lodCount?(Z("getLOD error, index out of range"),null):this._LODs[e]},i.setLOD=function(e,t){e<0||e>=this.lodCount?Z("setLOD error, index out of range"):(this._LODs[e]=t,t.modelAddedCallback=this.onLodModelAddedCallback.bind(this),this.lodGroup.updateLOD(e,t.lodData),this._updateDataToScene())},i.recalculateBounds=function(){for(var e=new D,t=new D,i=null,n=new D,o=0;o<this.lodCount;++o){var r=this.getLOD(o);if(r)for(var s=0;s<r.rendererCount;++s){var a,h,l=r.getRenderer(s);if(l){null==(a=l.model)||a.updateWorldBound();var c=null==(h=l.model)?void 0:h.worldBounds;c&&(c.getBoundary(e,t),i?(D.min(i,i,e),D.max(n,n,t)):(i=e.clone(),n=t.clone()))}}}if(i){var u=i,p=new D(.5*(n.x+u.x),.5*(n.y+u.y),.5*(n.z+u.z)),d=new D(.5*(n.x-u.x),.5*(n.y-u.y),.5*(n.z-u.z)),_=function(e,t,i){var n,o,r=new Array(new D(e.x-t.x,e.y-t.y,e.z-t.z),new D(e.x-t.x,e.y+t.y,e.z-t.z),new D(e.x+t.x,e.y+t.y,e.z-t.z),new D(e.x+t.x,e.y-t.y,e.z-t.z),new D(e.x-t.x,e.y-t.y,e.z+t.z),new D(e.x-t.x,e.y+t.y,e.z+t.z),new D(e.x+t.x,e.y+t.y,e.z+t.z),new D(e.x+t.x,e.y-t.y,e.z+t.z));o=(n=r[0].transformMat4(i)).clone();for(var s=1;s<8;++s){var a=r[s].transformMat4(i);n=D.min(n,n,a),o=D.max(o,o,a)}return[n,o]}(p,d,this.node.worldMatrix.clone().invert()),g=_[0],y=_[1];p.set(.5*(y.x+g.x),.5*(y.y+g.y),.5*(y.z+g.z)),d.set(.5*(y.x-g.x),.5*(y.y-g.y),.5*(y.z-g.z)),this.localBoundaryCenter=p,this.objectSize=2*Math.max(d.x,d.y,d.z)}else this.localBoundaryCenter=new D(0,0,0),this.objectSize=0;this._emitChangeNode(this.node)},i.resetObjectSize=function(){if(1!==this.objectSize){0===this.objectSize&&(this.objectSize=1);var e=1/this.objectSize;this.objectSize=1;for(var t=0;t<this.lodCount;++t){var i=this.getLOD(t);i&&(i.screenUsagePercentage*=e)}this._emitChangeNode(this.node)}},i.forceLOD=function(e){this._forceUsedLevels=e<0?[]:[e],this.lodGroup.lockLODLevels(this._forceUsedLevels)},i.forceLODs=function(){},i.onLoad=function(){this._lodGroup.node=this.node,this._lodGroup.objectSize=this._objectSize,this._lodGroup.localBoundaryCenter=this._localBoundaryCenter,this._eventRegistered||(this.node.on(fe.COMPONENT_REMOVED,this._onRemove,this),this._eventRegistered=!0),this._constructLOD()},i._onRemove=function(e){e===this&&this.onDisable()},i._constructLOD=function(){if(this.lodCount<1)for(var e=Jn.length,t=0;t<e;t++)this.insertLOD(t,Jn[t])},i.onRestore=function(){this._constructLOD(),this.enabledInHierarchy&&this._attachToScene()},i.onEnable=function(){var e=this;this._attachToScene(),0===this.objectSize&&this.recalculateBounds(),this.lodGroup.lockLODLevels(this._forceUsedLevels),this.lodCount>0&&this._lodGroup.lodCount<1&&this._LODs.forEach((function(t,i){t.lodData.screenUsagePercentage=t.screenUsagePercentage;var n=t.renderers;if(null!==n&&n.length>0)for(var o=0;o<n.length;o++){var r=t.lodData,s=n[o];r&&s&&s.model&&r.addModel(s.model)}e._lodGroup.insertLOD(i,t.lodData)}))},i.onDisable=function(){this._detachFromScene(),this.lodGroup.lockLODLevels([])},i._attachToScene=function(){if(this.node&&this.node.scene){var e=this._getRenderScene();this._lodGroup.scene&&this._detachFromScene(),e.addLODGroup(this._lodGroup)}},i._detachFromScene=function(){this._lodGroup.scene&&this._lodGroup.scene.removeLODGroup(this._lodGroup)},i._emitChangeNode=function(){},i._updateDataToScene=function(){this._detachFromScene(),this._attachToScene()},j(t,[{key:"localBoundaryCenter",get:function(){return this._localBoundaryCenter.clone()},set:function(e){this._localBoundaryCenter.set(e),this._lodGroup.localBoundaryCenter=e}},{key:"lodCount",get:function(){return this._LODs.length}},{key:"objectSize",get:function(){return this._objectSize},set:function(e){this._objectSize=e,this._lodGroup.objectSize=e}},{key:"LODs",get:function(){return this._LODs},set:function(e){var t=this;e!==this._LODs?(this._LODs.length=0,this.lodGroup.clearLODs(),e.forEach((function(e,i){t.lodGroup.insertLOD(i,e.lodData),t._LODs[i]=e,e.modelAddedCallback=t.onLodModelAddedCallback.bind(t)})),this._updateDataToScene()):this._updateDataToScene()}},{key:"lodGroup",get:function(){return this._lodGroup}}]),t}($),Pn=L(Dn.prototype,"_localBoundaryCenter",[v],(function(){return new D(0,0,0)})),Ln=L(Dn.prototype,"_objectSize",[v],(function(){return 0})),On=L(Dn.prototype,"_LODs",[v],(function(){return[]})),F(Dn.prototype,"objectSize",[mn],Object.getOwnPropertyDescriptor(Dn.prototype,"objectSize"),Dn.prototype),F(Dn.prototype,"LODs",[bn],Object.getOwnPropertyDescriptor(Dn.prototype,"LODs"),Dn.prototype),wn=Dn))||wn)),ge.makeMaskExclude([ge.BitMask.UI_2D,ge.BitMask.UI_3D,ge.BitMask.GIZMOS,ge.BitMask.EDITOR,ge.BitMask.SCENE_GIZMO,ge.BitMask.PROFILER,ge.Enum.IGNORE_RAYCAST])),io=e("ReflectionProbeManager",function(){function e(){this._probes=[],this._useCubeModels=new Map,this._usePlanarModels=new Map,this._updateForRuntime=!0,this._dataTexture=null,this._registeredEvent=!1}var t=e.prototype;return t.registerEvent=function(){this._registeredEvent||(g.director.on(g.DirectorEvent.BEFORE_UPDATE,this.onUpdateProbes,this),this._registeredEvent=!0)},t.onUpdateProbes=function(){if(0!==this._probes.length){var e=g.director.getScene();if(e&&e.renderScene)for(var t=e.renderScene.models,i=0;i<t.length;i++){var n=t[i];n.node&&n.node.layer&to&&(n.reflectionProbeType===re.BAKED_CUBEMAP||this._isUsedBlending(n)?this.selectReflectionProbe(n):n.reflectionProbeType===re.PLANAR_REFLECTION&&this.selectPlanarReflectionProbe(n))}}},t.filterModelsForPlanarReflection=function(){if(0!==this._probes.length){var e=g.director.getScene();if(e&&e.renderScene)for(var t=e.renderScene.models,i=0;i<t.length;i++){var n=t[i];n.node&&n.node.layer&to&&n.reflectionProbeType===re.PLANAR_REFLECTION&&this.selectPlanarReflectionProbe(n)}}},t.clearPlanarReflectionMap=function(e){for(var t,i=Y(this._usePlanarModels.entries());!(t=i()).done;){var n=t.value;n[1]===e&&this._updatePlanarMapOfModel(n[0],null,null)}},t.register=function(e){-1===this._probes.indexOf(e)&&(this._probes.push(e),this.updateProbeData())},t.unregister=function(e){for(var t=0;t<this._probes.length;t++)if(this._probes[t]===e){var i=this._probes.splice(t,1);i[0]&&this._removeDependentModels(i[0]);break}this.updateProbeData()},t.exists=function(e){if(0===this._probes.length)return!1;for(var t=0;t<this._probes.length;t++)if(this._probes[t].getProbeId()===e)return!0;return!1},t.getNewReflectionProbeId=function(){for(var e=0;;){if(!this.exists(e))return e;e++}},t.getProbes=function(){return this._probes},t.getProbeById=function(e){for(var t=0;t<this._probes.length;t++)if(this._probes[t].getProbeId()===e)return this._probes[t];return null},t.clearAll=function(){this._probes=[]},t.getProbeByCamera=function(e){for(var t=0;t<this._probes.length;t++)if(this._probes[t].camera===e)return this._probes[t];return null},t.updateBakedCubemap=function(e){var t=this._getModelsByProbe(e);if(e.cubemap){for(var i=0;i<t.length;i++){var n=t[i];this._updateCubemapOfModel(n,e)}if(e.needRefresh=!1,0===t.length)for(var o,r=Y(this._useCubeModels.entries());!(o=r()).done;){var s=o.value;s[0].reflectionProbeBlendId===e.getProbeId()&&this._updateBlendCubemap(s[0],e)}}},t.updatePlanarMap=function(e,t){if(e.node&&e.node.scene){for(var i=this._getModelsByProbe(e),n=0;n<i.length;n++)this._updatePlanarMapOfModel(i[n],t,e);if(e.previewPlane){var o=e.previewPlane.getComponent(f);o&&o.updateProbePlanarMap(t)}}},t.selectPlanarReflectionProbe=function(e){if(e.node&&e.worldBounds&&e.reflectionProbeType===re.PLANAR_REFLECTION){for(var t=0;t<this._probes.length;t++){var i=this._probes[t];i.probeType===p.PLANAR&&e.node.layer&to&&(e.updateWorldBound(),z.aabbWithAABB(e.worldBounds,i.boundingBox)?this._usePlanarModels.set(e,i):this._usePlanarModels.has(e)&&this._usePlanarModels.get(e)===i&&(this._usePlanarModels.delete(e),this._updatePlanarMapOfModel(e,null,null)))}for(var n=0;n<this._probes.length;n++)this._probes[n].probeType===p.PLANAR&&(this._probes[n].realtimePlanarTexture?this.updatePlanarMap(this._probes[n],this._probes[n].realtimePlanarTexture.getGFXTexture()):this.updatePlanarMap(this._probes[n],null))}},t.selectReflectionProbe=function(e){if(e.node&&e.worldBounds&&e.node.layer&to){e.updateWorldBound();var t=this._getNearestProbe(e);t?this._useCubeModels.has(e)?(this._useCubeModels.get(e)!==t&&this._useCubeModels.set(e,t),t.needRefresh=!0):(this._useCubeModels.set(e,t),t.needRefresh=!0):(this._updateCubemapOfModel(e,null),this._useCubeModels.delete(e))}for(var i=0;i<this._probes.length;i++)(this._probes[i].needRefresh&&this._probes[i].probeType===p.CUBE||this._isUsedBlending(e))&&this.updateBakedCubemap(this._probes[i])},t.updatePreviewSphere=function(e){if(e&&e.previewSphere){var t=e.previewSphere.getComponent(f);t&&(t.updateProbeCubemap(e.cubemap),t.updateReflectionProbeId(e.getProbeId()))}},t.updatePreviewPlane=function(e){e&&e.previewPlane&&e.previewPlane.getComponent(f)&&e.realtimePlanarTexture&&this.updatePlanarMap(e,e.realtimePlanarTexture.getGFXTexture())},t.updateProbeData=function(){if(0!==this._probes.length){var e=this.getMaxProbeId(),t=e+1;this._dataTexture&&this._dataTexture.destroy();for(var i=new Float32Array(12*t),n=0,o=0;o<=e;o++){var r=this.getProbeById(o);if(r){if(r.probeType===p.CUBE){i[n]=r.node.worldPosition.x,i[n+1]=r.node.worldPosition.y,i[n+2]=r.node.worldPosition.z,i[n+3]=0,i[n+4]=r.size.x,i[n+5]=r.size.y,i[n+6]=r.size.z,i[n+7]=0;var s=r.isRGBE()?1e3:0;i[n+8]=r.cubemap?r.cubemap.mipmapLevel+s:1+s}else i[n]=r.node.up.x,i[n+1]=r.node.up.y,i[n+2]=r.node.up.z,i[n+3]=1,i[n+4]=1,i[n+5]=1,i[n+6]=0,i[n+7]=0,i[n+8]=1;n+=12}else n+=12}var a=new Uint8Array(i.buffer),h=new me({_data:a,_compressed:!1,width:12,height:t,format:be.RGBA8888});this._dataTexture=new ue,this._dataTexture.setFilters(we.NONE,we.NONE),this._dataTexture.setMipFilter(we.NONE),this._dataTexture.setWrapMode(De.CLAMP_TO_EDGE,De.CLAMP_TO_EDGE,De.CLAMP_TO_EDGE),this._dataTexture.image=h,this._dataTexture.uploadData(a);for(var l=0;l<this._probes.length;l++)for(var c=this._probes[l],u=this._getModelsByProbe(c),d=0;d<u.length;d++){var _=u[d].node.getComponent(f);_&&_.updateReflectionProbeDataMap(this._dataTexture)}}},t.getMaxProbeId=function(){return 0===this._probes.length?-1:1===this._probes.length?this._probes[0].getProbeId():(this._probes.sort((function(e,t){return e.getProbeId()-t.getProbeId()})),this._probes[this._probes.length-1].getProbeId())},t.getUsedReflectionProbe=function(e,t){if(t){if(this._usePlanarModels.has(e))return this._usePlanarModels.get(e)}else if(this._useCubeModels.has(e))return this._useCubeModels.get(e);return null},t.setReflectionProbe=function(e,t,i){void 0===i&&(i=null),t&&(this._useCubeModels.set(e,t),this._updateCubemapOfModel(e,t),i&&this._updateBlendProbeInfo(e,t,i))},t.updateProbeOfModels=function(){if(0!==this._probes.length){var e=g.director.getScene();if(e&&e.renderScene)for(var t=e.renderScene.models,i=0;i<t.length;i++){var n=t[i];n.node&&n.node.layer&to&&(n.reflectionProbeType===re.BAKED_CUBEMAP||n.reflectionProbeType===re.PLANAR_REFLECTION||this._isUsedBlending(n))&&n.updateReflectionProbeId()}}},t._getNearestProbe=function(e){if(!e.node||!e.worldBounds||0===this._probes.length)return null;for(var t,i=null,n=1/0,o=Y(this._probes);!(t=o()).done;){var r=t.value;if(r.probeType===p.CUBE&&r.validate()&&z.aabbWithAABB(e.worldBounds,r.boundingBox)){var s=D.distance(e.node.worldPosition,r.node.worldPosition);s<n&&(n=s,i=r)}}return i},t._getBlendProbe=function(e){if(!e||!e.node||!e.worldBounds||this._probes.length<2)return null;for(var t=[],i=0;i<this._probes.length;i++)this._probes[i].probeType===p.CUBE&&this._probes[i].validate()&&z.aabbWithAABB(e.worldBounds,this._probes[i].boundingBox)&&t.push(this._probes[i]);return t.sort((function(t,i){return D.distance(e.node.worldPosition,t.node.worldPosition)-D.distance(e.node.worldPosition,i.node.worldPosition)})),t.length>1?t[1]:null},t._getModelsByProbe=function(e){var t=[],i=this._useCubeModels;e.probeType===p.PLANAR&&(i=this._usePlanarModels);for(var n,o=Y(i.entries());!(n=o()).done;){var r=n.value;r[1]===e&&t.push(r[0])}return t},t._removeDependentModels=function(e){for(var t,i=Y(this._useCubeModels.keys());!(t=i()).done;){var n=t.value,o=this._useCubeModels.get(n);void 0!==o&&o===e&&(this._useCubeModels.delete(n),this.selectReflectionProbe(n))}for(var r,s=Y(this._usePlanarModels.keys());!(r=s()).done;){var a=r.value,h=this._usePlanarModels.get(a);void 0!==h&&h===e&&(this._usePlanarModels.delete(a),this.selectPlanarReflectionProbe(a))}},t._updateCubemapOfModel=function(e,t){var i=e.node;if(i){var n=i.getComponent(f);if(n&&(n.updateProbeCubemap(t?t.cubemap:null),n.updateReflectionProbeId(t&&t.cubemap?t.getProbeId():-1),t&&(n.updateReflectionProbeDataMap(this._dataTexture),this._isUsedBlending(e)))){var o=this._getBlendProbe(e);this._updateBlendProbeInfo(e,t,o)}}},t._updatePlanarMapOfModel=function(e,t,i){var n=e.node.getComponent(f);n&&(n.updateProbePlanarMap(t),i?(n.updateReflectionProbeId(i.getProbeId()),n.updateReflectionProbeDataMap(this._dataTexture)):n.updateReflectionProbeId(-1))},t._isUsedBlending=function(e){return e.reflectionProbeType===re.BLEND_PROBES||e.reflectionProbeType===re.BLEND_PROBES_AND_SKYBOX},t._updateBlendProbeInfo=function(e,t,i){var n=e.node;if(n){var o=n.getComponent(f);o&&(o.updateReflectionProbeBlendId(-1),e.reflectionProbeType===re.BLEND_PROBES_AND_SKYBOX&&o.updateReflectionProbeBlendWeight(this._calculateBlendWeight(e,t,i)))}},t._updateBlendCubemap=function(e,t){var i=e.node;if(i&&this._isUsedBlending(e)){var n=i.getComponent(f);n&&n.updateProbeBlendCubemap(t.cubemap)}},t._calculateBlendWeight=function(e,t,i){if(i){var n=D.distance(e.node.worldPosition,t.node.worldPosition),o=D.distance(e.node.worldPosition,i.node.worldPosition);return 1-o/(n+o)}return e.reflectionProbeType===re.BLEND_PROBES?0:e.reflectionProbeType===re.BLEND_PROBES_AND_SKYBOX?this._calculateBlendOfSkybox(e.worldBounds,t.boundingBox):0},t._calculateBlendOfSkybox=function(e,t){if(!e)return 1;var i=new D,n=new D,o=new D,r=new D;if(D.subtract(i,e.center,e.halfExtents),D.add(n,e.center,e.halfExtents),D.subtract(o,t.center,t.halfExtents),D.add(r,t.center,t.halfExtents),i.x<=r.x&&n.x>=o.x&&i.y<=r.y&&n.y>=o.y&&i.z<=r.z&&n.z>=o.z){var s=new D;D.multiplyScalar(s,e.halfExtents,2);var a=i.x+s.x<=r.x&&n.x+s.x>=o.x,h=i.x-s.x<=r.x&&n.x-s.x>=o.x,l=i.y+s.y<=r.y&&n.y+s.y>=o.y,c=i.y-s.y<=r.y&&n.y-s.y>=o.y,u=i.z+s.z<=r.z&&n.z+s.z>=o.z,p=i.z-s.z<=r.z&&n.z-s.z>=o.z,d=[];if(!a){var _=n.x-r.x;d.push(_/s.x)}if(!h){var g=Math.abs(i.x-o.x);d.push(g/s.x)}if(!l){var y=n.y-r.y;d.push(y/s.y)}if(!c){var f=Math.abs(i.y-o.y);d.push(f/s.y)}if(!u){var m=n.z-r.z;d.push(m/s.z)}if(!p){var b=Math.abs(i.z-o.z);d.push(b/s.z)}return d.length>0?(d.sort((function(e,t){return t-e})),d[0]):0}return 1},j(e,[{key:"updateForRuntime",get:function(){return this._updateForRuntime},set:function(e){this._updateForRuntime=e}}]),e}());io.probeManager=void 0,io.probeManager=new io,g.internal.reflectionProbeManager=io.probeManager;var no,oo=T();!function(e){e[e.Low_256x256=256]="Low_256x256",e[e.Medium_512x512=512]="Medium_512x512",e[e.High_768x768=768]="High_768x768"}(no||(no={})),e("ReflectionProbe",(Bn=P("cc.ReflectionProbe"),kn=O(D),zn=O(N(p)),Tn=O(N(no)),xn=O(N(_)),En=O(R),An=O(ge.BitMask),Nn=O(Pe),jn=O(H),In=O(_e),Bn((Qn=function(e){function t(){for(var t,i=arguments.length,n=new Array(i),o=0;o<i;o++)n[o]=arguments[o];return(t=e.call.apply(e,[this].concat(n))||this)._lastSize=T(),t._resolution=Hn&&Hn(),t._clearFlag=Gn&&Gn(),t._backgroundColor=Wn&&Wn(),t._visibility=Xn&&Xn(),t._probeType=Vn&&Vn(),t._cubemap=Kn&&Kn(),t._size=Zn&&Zn(),t._sourceCamera=Yn&&Yn(),t._probeId=qn&&qn(),t._fastBake=$n&&$n(),t._probe=null,t._previewSphere=null,t._previewPlane=null,t._sourceCameraPos=T(),t._position=T(),t}I(t,e);var i=t.prototype;return i.onLoad=function(){this._createProbe()},i._handleResize$=function(){this.probe&&this.sourceCamera&&this.probeType===p.PLANAR&&this.probe.renderPlanarReflection(this.sourceCamera.camera)},i.onEnable=function(){if(this._probe){var e=io.probeManager.getProbeById(this._probeId);null!==e&&e!==this._probe&&(this._probeId=io.probeManager.getNewReflectionProbeId(),this._probe.updateProbeId(this._probeId)),io.probeManager.register(this._probe),io.probeManager.onUpdateProbes(),this._probe.enable()}x.on("window-resize",this._handleResize$,this),x.on("fullscreen-change",this._handleResize$,this)},i.onDisable=function(){this._probe&&(io.probeManager.unregister(this._probe),this._probe.disable()),x.off("window-resize",this._handleResize$,this),x.off("fullscreen-change",this._handleResize$,this)},i.start=function(){this._sourceCamera&&this.probeType===p.PLANAR&&(this.probe.renderPlanarReflection(this.sourceCamera.camera),io.probeManager.filterModelsForPlanarReflection()),io.probeManager.updateProbeData(),this.node.getWorldPosition(this._position)},i.onDestroy=function(){this.probe&&this.probe.destroy()},i.update=function(){this.probe&&(this.probeType===p.PLANAR&&this.sourceCamera&&(this.sourceCamera.node.hasChangedFlags&pe.TRS||!this._sourceCameraPos.equals(this.sourceCamera.node.getWorldPosition()))&&(this._sourceCameraPos=this.sourceCamera.node.getWorldPosition(),this.probe.renderPlanarReflection(this.sourceCamera.camera)),this.node.hasChangedFlags&pe.POSITION&&(this.probe.updateBoundingBox(),io.probeManager.onUpdateProbes(),io.probeManager.updateProbeData()),this.node.getWorldPosition(oo),this._position.equals(oo)||(this._position.set(oo),this.probe.updateBoundingBox(),io.probeManager.updateProbeData(),io.probeManager.updateProbeOfModels()))},i.clearBakedCubemap=function(){this.cubemap=null,io.probeManager.updateBakedCubemap(this.probe),io.probeManager.updatePreviewSphere(this.probe)},i._createProbe=function(){if((-1===this._probeId||io.probeManager.exists(this._probeId))&&(this._probeId=io.probeManager.getNewReflectionProbeId()),this._probe=new d(this._probeId),this._probe){var e=new de("ReflectionProbeCamera");e.hideFlags|=q.DontSave|q.HideInHierarchy,this.node.scene.addChild(e),this._probe.initialize(this.node,e),this.enabled&&io.probeManager.register(this._probe),this._probe.resolution=this._resolution,this._probe.clearFlag=this._clearFlag,this._probe.backgroundColor=this._backgroundColor,this._probe.visibility=this._visibility,this._probe.probeType=this._probeType,this._probe.size=this._size,this._probe.cubemap=this._cubemap}},j(t,[{key:"size",get:function(){return this._size},set:function(e){this._size.set(e),E(this._size),this.probe.size=this._size,this.probe&&(this.probe.updateBoundingBox(),io.probeManager.onUpdateProbes(),io.probeManager.updateProbeData(),io.probeManager.updateProbeOfModels())}},{key:"probeType",get:function(){return this._probeType},set:function(e){if(this.probe.probeType=e,e!==this._probeType){var i=this._size.clone(),n=D.equals(this._lastSize,D.ZERO);this._probeType=e,this._probeType===p.CUBE?(n&&this._size.set(t.DEFAULT_CUBE_SIZE),this.probe.switchProbeType(e,null),io.probeManager.clearPlanarReflectionMap(this.probe)):(n&&this._size.set(t.DEFAULT_PLANER_SIZE),this._sourceCamera?this.probe.switchProbeType(e,this._sourceCamera.camera):Z("the reflection camera is invalid, please set the reflection camera")),n||this._size.set(this._lastSize),this._lastSize.set(i),this.size=this._size}}},{key:"resolution",get:function(){return this._resolution},set:function(e){this._resolution=e,this.probe.resolution=e}},{key:"clearFlag",get:function(){return this._clearFlag},set:function(e){this._clearFlag=e,this.probe.clearFlag=this._clearFlag}},{key:"backgroundColor",get:function(){return this._backgroundColor},set:function(e){this._backgroundColor=e,this.probe.backgroundColor=this._backgroundColor}},{key:"visibility",get:function(){return this._visibility},set:function(e){this._visibility=e,this.probe.visibility=this._visibility}},{key:"sourceCamera",get:function(){return this._sourceCamera},set:function(e){this._sourceCamera=e,e&&(this.visibility=e.visibility,this.clearFlag=e.clearFlags,this.backgroundColor=e.clearColor,this.probeType===p.PLANAR&&this.probe.switchProbeType(this.probeType,e.camera))}},{key:"fastBake",get:function(){return this._fastBake},set:function(e){this._fastBake=e}},{key:"cubemap",get:function(){return this._cubemap},set:function(e){this._cubemap=e,this.probe.cubemap=e,io.probeManager.onUpdateProbes()}},{key:"probe",get:function(){return this._probe}},{key:"previewSphere",get:function(){return this._previewSphere},set:function(e){this._previewSphere=e,this.probe&&(this.probe.previewSphere=e,this._previewSphere&&io.probeManager.updatePreviewSphere(this.probe))}},{key:"previewPlane",get:function(){return this._previewPlane},set:function(e){this._previewPlane=e,this.probe&&(this.probe.previewPlane=e,this._previewPlane&&io.probeManager.updatePreviewPlane(this.probe))}}]),t}($),Qn.DEFAULT_CUBE_SIZE=T(1,1,1),Qn.DEFAULT_PLANER_SIZE=T(5,.5,5),Hn=L((Un=Qn).prototype,"_resolution",[v],(function(){return 256})),Gn=L(Un.prototype,"_clearFlag",[v],(function(){return _.SKYBOX})),Wn=L(Un.prototype,"_backgroundColor",[v],(function(){return new R(0,0,0,255)})),Xn=L(Un.prototype,"_visibility",[v],(function(){return ye})),Vn=L(Un.prototype,"_probeType",[v],(function(){return p.CUBE})),Kn=L(Un.prototype,"_cubemap",[v],(function(){return null})),Zn=L(Un.prototype,"_size",[v],(function(){return T(1,1,1)})),Yn=L(Un.prototype,"_sourceCamera",[v],(function(){return null})),qn=L(Un.prototype,"_probeId",[v],(function(){return-1})),$n=L(Un.prototype,"_fastBake",[v],(function(){return!1})),F(Un.prototype,"size",[kn],Object.getOwnPropertyDescriptor(Un.prototype,"size"),Un.prototype),F(Un.prototype,"probeType",[zn],Object.getOwnPropertyDescriptor(Un.prototype,"probeType"),Un.prototype),F(Un.prototype,"resolution",[Tn],Object.getOwnPropertyDescriptor(Un.prototype,"resolution"),Un.prototype),F(Un.prototype,"clearFlag",[xn],Object.getOwnPropertyDescriptor(Un.prototype,"clearFlag"),Un.prototype),F(Un.prototype,"backgroundColor",[En],Object.getOwnPropertyDescriptor(Un.prototype,"backgroundColor"),Un.prototype),F(Un.prototype,"visibility",[An],Object.getOwnPropertyDescriptor(Un.prototype,"visibility"),Un.prototype),F(Un.prototype,"sourceCamera",[Nn],Object.getOwnPropertyDescriptor(Un.prototype,"sourceCamera"),Un.prototype),F(Un.prototype,"fastBake",[jn],Object.getOwnPropertyDescriptor(Un.prototype,"fastBake"),Un.prototype),F(Un.prototype,"cubemap",[In],Object.getOwnPropertyDescriptor(Un.prototype,"cubemap"),Un.prototype),Fn=Un))||Fn)),y.utils=Ie}}}));
