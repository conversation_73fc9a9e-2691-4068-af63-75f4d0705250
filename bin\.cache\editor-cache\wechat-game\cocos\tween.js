System.register(["./index-C5lmLqDW.js","./global-exports-CR3GRnjt.js","./gc-object-CKHc4SnS.js","./prefab-DH0xadMc.js","./scene-7MDSMR3j.js","./pipeline-state-manager-Cdpe3is6.js","./node-event-DTNosVQv.js","./component-BaGvu7EF.js","./deprecated-T2yM9nsJ.js","./director-DIlqD2Nd.js","./renderer-9hfAnqUF.js","./touch-DB0AR-Sc.js","./device-manager-BvjvoelW.js","./buffer-barrier-CuX_5NUR.js","./factory-D9_8ZCqM.js","./deprecated-Ca3AjUwj.js","./debug-view-CKetkq9d.js"],(function(t){"use strict";var n,i,e,r,o,s,a,u,c,h,l,f,_,g,p,v,d,A,T,w,m,y,D;return{setters:[function(t){n=t.b,i=t.a2,e=t.a3,r=t.a4,o=t.S,s=t.a5},function(t){a=t.c,u=t.e,c=t.l},function(t){h=t._,l=t.h,f=t.R,_=t.i,g=t.a,p=t.A,v=t.T,d=t.w,A=t.G},null,function(t){T=t.N},null,function(t){w=t.N},null,null,function(t){m=t.d,y=t.D},function(t){D=t.R},null,null,null,null,null,null],execute:function(){t({tween:ct,tweenUtil:ht});var S=new n,k=new n;function b(t,i){var r=null;return{value:i.length>0?i[i.length-1]:n.ZERO,progress:function(t,n,i,e){return r.getPoint(e)},clone:function(t){return n.clone(t)},add:function(t,n){return t.clone().add(n)},sub:function(t,n){return t.clone().subtract(n)},onStart:function(o){var s=o.start,a=o.end,u=o.relative,c=o.reversed;(r=e.create(t)).addKnot(s);var h=null;u&&c&&(h=k,n.subtract(h,s,i[i.length-1]));for(var l=0,f=i.length;l<f;++l){var _=c?i[f-1-l]:i[l];u?c?l>0&&r.addKnot(n.copy(S,h).add(_)):r.addKnot(n.copy(S,s).add(_)):r.addKnot(_)}u&&c&&r.addKnot(a)},onComplete:function(){r=null},onStop:function(){r=null},legacyProgress:!1}}var W,I=Object.freeze({__proto__:null,bezier:function(){for(var t=arguments.length,n=new Array(t),e=0;e<t;e++)n[e]=arguments[e];return b(i.BEZIER,n)},catmullRom:function(){for(var t=arguments.length,n=new Array(t),e=0;e<t;e++)n[e]=arguments[e];return b(i.CATMULL_ROM,n)}});t("tweenProgress",I),function(t){t[t.TAG_INVALID=-1]="TAG_INVALID"}(W||(W={}));var N=function(t){function n(){var n;return(n=t.call(this)||this)._duration=0,n}h(n,t);var i=n.prototype;return i.getDurationScaled=function(){return this._duration},i.getDuration=function(){return this._duration},i.setDuration=function(t){this._duration=t},n}(function(){function t(){this.originalTarget=null,this.target=null,this._owner=null,this.tag=W.TAG_INVALID,this._id=void 0,this._paused=!1}var n=t.prototype;return n.isDone=function(){return!0},n.startWithTarget=function(t){this.originalTarget=t,this.target=t},n.stop=function(){this.target=null},n.getTarget=function(){return this.target},n.setTarget=function(t){this.target=t},n.getOriginalTarget=function(){return this.originalTarget},n.setOriginalTarget=function(t){this.originalTarget=t},n._getWorkerTarget=function(){var t,n=null==(t=this._owner)?void 0:t.getTarget();return null!=n?n:this.target},n.getTag=function(){return this.tag},n.setTag=function(t){this.tag=t},n.setId=function(t){this._id=t},n.getId=function(){return this._id},n.setPaused=function(t){this._paused=t},t}()),E=function(){this.actions=[],this.target=null,this.actionIndex=0,this.currentAction=null,this.paused=!1,this.lock=!1},U=function(){function t(){this._hashTargets=new Map,this._arrayTargets=[],this._currentTarget=void 0,this._elementPool=[]}var n=t.prototype;return n._getElement=function(t,n){var i=this._elementPool.pop();return i||(i=new E),i.target=t,i.paused=!!n,i},n._putElement=function(t){t.actions.length=0,t.actionIndex=0,t.currentAction=null,t.paused=!1,t.target=null,t.lock=!1,this._elementPool.push(t)},n._onNodeActiveChanged=function(t,n){n?this.resumeTarget(t):this.pauseTarget(t)},n._onNodeDestroy=function(t){this._removeAllActionsFromTarget(t,!1)},n._registerNodeEvent=function(t){t.isValid&&(t.on(w.ACTIVE_CHANGED,this._onNodeActiveChanged,this),t.on(w.NODE_DESTROYED,this._onNodeDestroy,this))},n._unregisterNodeEvent=function(t){t.isValid&&(t.off(w.ACTIVE_CHANGED,this._onNodeActiveChanged,this),t.off(w.NODE_DESTROYED,this._onNodeDestroy,this))},n.addAction=function(t,n,i){if(t&&n){var e=this._hashTargets.get(n);e?e.actions||(e.actions=[]):(e=this._getElement(n,i),this._hashTargets.set(n,e),this._arrayTargets.push(e)),0===e.actions.length&&n instanceof T&&this._registerNodeEvent(n),e.target=n,e.actions.push(t),t.startWithTarget(n)}else l(1e3)},n.removeAllActions=function(){for(var t=this._arrayTargets,n=0;n<t.length;n++){var i=t[n];i&&(i.target instanceof T&&this._unregisterNodeEvent(i.target),this._putElement(i))}this._arrayTargets.length=0,this._hashTargets=new Map},n.removeAllActionsFromTarget=function(t){this._removeAllActionsFromTarget(t,!0)},n._removeAllActionsFromTarget=function(t,n){if(null!=t){var i=this._hashTargets.get(t);i&&(n&&t instanceof T&&this._unregisterNodeEvent(t),i.actions.length=0,this._deleteHashElement(i))}},n.removeAction=function(t){if(null!=t){var n=t.getOriginalTarget(),i=this._hashTargets.get(n);if(i)for(var e=0;e<i.actions.length;e++)if(i.actions[e]===t){i.actions.splice(e,1),i.actionIndex>=e&&i.actionIndex--;break}}},n._removeActionByTag=function(t,n,i){for(var e=0,r=n.actions.length;e<r;++e){var o=n.actions[e];if(o&&o.getTag()===t){if(i&&o.getOriginalTarget()!==i)continue;this._removeActionAtIndex(e,n);break}}},n._removeAllActionsByTag=function(t,n,i){for(var e=n.actions.length-1;e>=0;--e){var r=n.actions[e];if(r&&r.getTag()===t){if(i&&r.getOriginalTarget()!==i)continue;this._removeActionAtIndex(e,n)}}},n.removeActionByTag=function(t,n){var i=this;t===W.TAG_INVALID&&f(1002);var e=this._hashTargets;if(n){var r=e.get(n);r&&this._removeActionByTag(t,r,n)}else e.forEach((function(n){i._removeActionByTag(t,n)}))},n.removeAllActionsByTag=function(t,n){var i=this;t===W.TAG_INVALID&&f(1002);var e=this._hashTargets;if(n){var r=e.get(n);r&&this._removeAllActionsByTag(t,r,n)}else e.forEach((function(n){i._removeAllActionsByTag(t,n)}))},n.getActionByTag=function(t,n){t===W.TAG_INVALID&&f(1004);var i=this._hashTargets.get(n);if(i){if(null!=i.actions)for(var e=0;e<i.actions.length;++e){var r=i.actions[e];if(r&&r.getTag()===t)return r}f(1005,t)}return null},n.getNumberOfRunningActionsInTarget=function(t){var n=this._hashTargets.get(t);return n&&n.actions?n.actions.length:0},n.pauseTarget=function(t){var n=this._hashTargets.get(t);n&&(n.paused=!0)},n.resumeTarget=function(t){var n=this._hashTargets.get(t);n&&(n.paused=!1)},n.pauseAllRunningActions=function(){for(var t=[],n=this._arrayTargets,i=0;i<n.length;i++){var e=n[i];e&&!e.paused&&(e.paused=!0,e.target&&t.push(e.target))}return t},n.resumeTargets=function(t){if(t)for(var n=0;n<t.length;n++)t[n]&&this.resumeTarget(t[n])},n.pauseTargets=function(t){if(t)for(var n=0;n<t.length;n++)t[n]&&this.pauseTarget(t[n])},n.isActionRunning=function(t){var n=this._hashTargets.get(t.getOriginalTarget()),i=-1;return n&&(i=n.actions.indexOf(t)),-1!==i},n._removeActionAtIndex=function(t,n){n.actions.splice(t,1),n.actionIndex>=t&&n.actionIndex--,0===n.actions.length&&(n.target instanceof T&&this._unregisterNodeEvent(n.target),this._deleteHashElement(n))},n._deleteHashElement=function(t){var n=!1;if(t&&!t.lock&&this._hashTargets.get(t.target)){this._hashTargets.delete(t.target);for(var i=this._arrayTargets,e=0,r=i.length;e<r;e++)if(i[e]===t){i.splice(e,1);break}this._putElement(t),n=!0}return n},n.update=function(t){for(var n,i=this._arrayTargets,e=0;e<i.length;e++){this._currentTarget=i[e];var r=(n=this._currentTarget).target;if(!_(r)||r.isValid){if(!n.paused&&n.actions){for(n.lock=!0,n.actionIndex=0;n.actionIndex<n.actions.length;n.actionIndex++)if(n.currentAction=n.actions[n.actionIndex],n.currentAction){if(n.currentAction.step(t),n.currentAction&&n.currentAction.isDone()){n.currentAction.stop();var o=n.currentAction;n.currentAction=null,this.removeAction(o)}n.currentAction=null}n.lock=!1}0===n.actions.length&&(r instanceof T&&this._unregisterNodeEvent(r),this._deleteHashElement(n)&&e--)}else this.removeAllActionsFromTarget(r),e--}},t}(),C=t("TweenSystem",function(t){function n(){var n;return(n=t.call(this)||this).actionMgr=new U,n}return h(n,t),n.prototype.update=function(t){this.actionMgr.update(t)},g(n,[{key:"ActionManager",get:function(){return this.actionMgr}}]),n}(o));C.ID="TWEEN",C.instance=void 0,m.on(y.INIT,(function(){var t=new C;C.instance=t,m.registerSystem(C.ID,t,r.MEDIUM)}));var O=function(t){function n(){return t.apply(this,arguments)||this}h(n,t);var i=n.prototype;return i.isDone=function(){return!0},i.step=function(){this.update(1)},i.update=function(){},i.reverse=function(){return this.clone()},i.isUnknownDuration=function(){return!1},n}(N),x=function(t){function n(){return t.apply(this,arguments)||this}h(n,t);var i=n.prototype;return i.update=function(){var t=this._getWorkerTarget();if(t)for(var n=t.getComponentsInChildren(D),i=0;i<n.length;++i)n[i].enabled=!0},i.reverse=function(){return new j},i.clone=function(){var t=new n;return t._id=this._id,t},i.toString=function(){return"<Show>"},n}(O),j=function(t){function n(){return t.apply(this,arguments)||this}h(n,t);var i=n.prototype;return i.update=function(){var t=this._getWorkerTarget();if(t)for(var n=t.getComponentsInChildren(D),i=0;i<n.length;++i)n[i].enabled=!1},i.reverse=function(){return new x},i.clone=function(){var t=new n;return t._id=this._id,t},i.toString=function(){return"<Hide>"},n}(O);!function(t){function n(){return t.apply(this,arguments)||this}h(n,t);var i=n.prototype;i.update=function(){var t=this._getWorkerTarget();if(t)for(var n=t.getComponentsInChildren(D),i=0;i<n.length;++i){var e=n[i];e.enabled=!e.enabled}},i.reverse=function(){return new n},i.clone=function(){var t=new n;return t._id=this._id,t},i.toString=function(){return"<ToggleVisibility>"}}(O);var F=function(t){function n(n){var i;return(i=t.call(this)||this)._isNeedCleanUp=!0,void 0!==n&&i.init(n),i}h(n,t);var i=n.prototype;return i.update=function(){var t=this._getWorkerTarget();t&&(t.removeFromParent(),this._isNeedCleanUp&&t.destroy())},i.init=function(t){return this._isNeedCleanUp=t,!0},i.reverse=function(){return new n(this._isNeedCleanUp)},i.clone=function(){var t=new n(this._isNeedCleanUp);return t._id=this._id,t},i.toString=function(){return"<RemoveSelf>"},n}(O);function P(t){return new F(t)}var R=function(t){function n(n,i,e){var r;return(r=t.call(this)||this)._callbackThis=void 0,r._callback=void 0,r._data=void 0,r.initWithFunction(n,i,e),r}h(n,t);var i=n.prototype;return i.initWithFunction=function(t,n,i){return t&&(this._callback=t),n&&(this._callbackThis=n),void 0!==i&&(this._data=i),!0},i.execute=function(){if(this._callback){var t=this._getWorkerTarget();this._callback.call(this._callbackThis,t,this._data)}},i.update=function(){this.execute()},i.getTargetCallback=function(){return this._callbackThis},i.setTargetCallback=function(t){t!==this._callbackThis&&(this._callbackThis=t)},i.clone=function(){var t=new n;return t._id=this._id,this._callback&&t.initWithFunction(this._callback,this._callbackThis,this._data),t},i.toString=function(){return"<CallFunc>"},n}(O);function M(t,n,i){return new R(t,n,i)}var B=function(t){function n(){return t.apply(this,arguments)||this}h(n,t);var i=n.prototype;return i.clone=function(){return new n},i.reverse=function(){return this.clone()},i.update=function(){},i.step=function(){},i.isUnknownDuration=function(){return!1},i.toString=function(){return"DummyAction"},n}(N),V=function(t){function n(n){var i;return(i=t.call(this)||this).MAX_VALUE=2,i._elapsed=0,i._startTime=0,i._firstTick=!1,i._speed=1,void 0===n||Number.isNaN(n)||i.initWithDuration(n),i}h(n,t);var i=n.prototype;return i.setStartTime=function(t){t=t<0?0:t>this._duration?this._duration:t,this._startTime=t},i.getElapsed=function(){return this._elapsed},i.initWithDuration=function(t){return this._duration=0===t?p.FLT_EPSILON:t,this._elapsed=0,this._firstTick=!0,!0},i.isDone=function(){return this._elapsed>=this._duration&&!this.isUnknownDuration()},i._cloneDecoration=function(t){t._speed=this._speed},i.step=function(t){if(!this._paused&&0!==this._speed){t*=this._speed,this._firstTick?this._elapsed=this._startTime:this._elapsed+=t;var n=this._elapsed/(this._duration>1.192092896e-7?this._duration:1.192092896e-7);n=n<1?n:1,this.update(n>0?n:0),this.isUnknownDuration()&&!this._firstTick&&(n<1?this._elapsed-=t:this._elapsed=this._startTime+this._duration),this._firstTick&&(this._firstTick=!1,this._startTime>0&&(this._startTime=0))}},i.startWithTarget=function(n){t.prototype.startWithTarget.call(this,n),this._elapsed=0,this._firstTick=!0},i.getSpeed=function(){return this._speed},i.setSpeed=function(t){this._speed=t},i.getDurationScaled=function(){return this._duration/this._speed},n}(N);function L(t,n){var i=new q;return i.initWithTwoActions(t,n),i}var q=function(t){function n(n){var i;if((i=t.call(this)||this)._actions=[],i._split=0,i._last=0,i._reversed=!1,!n||0===n.length)return v(i);1===n.length&&n.push(new B);var e=n.length-1;if(e>=0&&null==n[e]&&f(1015),e>=0){for(var r=n[0],o=1;o<e;o++)n[o]&&(r=L(r,n[o]));i.initWithTwoActions(r,n[e])}return i}h(n,t);var i=n.prototype;return i.initWithTwoActions=function(t,n){if(!t||!n)return l(1025),!1;var i=t.getDurationScaled()+n.getDurationScaled();return this.initWithDuration(i),this._actions[0]=t,this._actions[1]=n,!0},i.clone=function(){var t=new n;return t._id=this._id,t._speed=this._speed,this._cloneDecoration(t),t.initWithTwoActions(this._actions[0].clone(),this._actions[1].clone()),t},i.startWithTarget=function(n){t.prototype.startWithTarget.call(this,n),0!==this._actions.length&&(this._split=this._actions[0].getDurationScaled()/this._duration,this._last=-1)},i.stop=function(){0!==this._actions.length&&(-1!==this._last&&this._actions[this._last].stop(),t.prototype.stop.call(this))},i.update=function(t){var n=this._actions;if(0!==n.length){var i=0,e=0,r=this._split,o=this._last;if(t<r){if(i=0!==r?t/r:1,0===e&&1===o&&this._reversed){var s=n[1];if(s.update(0),s.isUnknownDuration())return;s.stop()}}else{var a=n[0];if(e=1,i=1===r?1:(t-r)/(1-r),-1===o){if(a.startWithTarget(this.target),a.update(1),a.isUnknownDuration())return;a.stop()}if(0===o){if(a.update(1),a.isUnknownDuration())return;a.stop()}}var u=n[e];o===e&&u.isDone()||(o!==e&&u.startWithTarget(this.target),u.update(i>1?i%1:i),this._last=e)}},i.reverse=function(){var t=L(this._actions[1].reverse(),this._actions[0].reverse());return this._cloneDecoration(t),t._reversed=!0,t},i.updateOwner=function(t){if(!(this._actions.length<2)){var i=this._actions[0],e=this._actions[1];e._owner||(e._owner=t),i instanceof n||i instanceof Y?i.updateOwner(t):i._owner||(i._owner=t)}},i.findAction=function(t){for(var i=0,e=this._actions.length;i<e;++i){var r=this._actions[i];if(r.getId()===t)return r;if((r instanceof n||r instanceof Y)&&(r=r.findAction(t))&&r.getId()===t)return r}return null},i.isUnknownDuration=function(){if(0===this._actions.length)return!1;var t=this._actions[0],n=this._actions[1];return this._last<1?t.isUnknownDuration():n.isUnknownDuration()},i.toString=function(){return"<Sequence>"},n}(V);function G(t){return new q(t)}var H=function(t){function n(n,i){var e;return(e=t.call(this)||this)._times=0,e._total=0,e._nextDt=0,e._actionInstant=!1,e._innerAction=null,e.initWithAction(n,i),e}h(n,t);var i=n.prototype;return i.initWithAction=function(t,n){if(!t||void 0===n)return!1;var i=t.getDurationScaled()*n;return!!this.initWithDuration(i)&&(this._times=n,this._innerAction=t,t instanceof O&&(this._actionInstant=!0,this._times-=1),this._total=0,!0)},i.clone=function(){var t=new n;return t._id=this._id,t._speed=this._speed,this._cloneDecoration(t),this._innerAction&&t.initWithAction(this._innerAction.clone(),this._times),t},i.startWithTarget=function(n){this._total=0,this._nextDt=(this._innerAction?this._innerAction.getDurationScaled():0)/this._duration,t.prototype.startWithTarget.call(this,n),this._innerAction&&this._innerAction.startWithTarget(n)},i.stop=function(){this._innerAction&&this._innerAction.stop(),t.prototype.stop.call(this)},i.update=function(t){var n=this._innerAction,i=this._duration,e=this._times,r=this._nextDt;if(n)if(t>=r){for(;t>r&&this._total<e;){if(n.update(1),n.isUnknownDuration())return;this._total++,n.stop(),n.startWithTarget(this.target),r+=n.getDurationScaled()/i,this._nextDt=r>1?1:r}if(t>=1&&this._total<e){if(n.update(1),n.isUnknownDuration())return;this._total++}this._actionInstant||(this._total===e?n.stop():n.update(t-(r-n.getDurationScaled()/i)))}else n.update(t*e%1)},i.isDone=function(){return this._total===this._times},i.reverse=function(){var t=new n(this._innerAction?this._innerAction.reverse():void 0,this._times);return this._cloneDecoration(t),t},i.setInnerAction=function(t){this._innerAction!==t&&(this._innerAction=t)},i.getInnerAction=function(){return this._innerAction},i.isUnknownDuration=function(){return!!this._innerAction&&this._innerAction.isUnknownDuration()},i.toString=function(){return"<Repeat>"},n}(V);function K(t,n){return new H(t,n)}var Z=function(t){function n(n){var i;return(i=t.call(this)||this)._innerAction=null,n&&i.initWithAction(n),i}h(n,t);var i=n.prototype;return i.initWithAction=function(t){return t?(this._innerAction=t,this._duration=1/0,!0):(l(1026),!1)},i.clone=function(){var t=new n;return t._id=this._id,t._speed=this._speed,this._cloneDecoration(t),this._innerAction&&t.initWithAction(this._innerAction.clone()),t},i.startWithTarget=function(n){t.prototype.startWithTarget.call(this,n),this._innerAction&&this._innerAction.startWithTarget(n)},i.stop=function(){this._innerAction&&this._innerAction.stop(),t.prototype.stop.call(this)},i.step=function(t){if(!this._paused&&0!==this._speed){var n=this._innerAction;n&&(t*=this._speed,n.step(t),n.isDone()&&(n.startWithTarget(this.target),n.step(n.getElapsed()-n.getDurationScaled())))}},i.update=function(){f(1007)},i.isDone=function(){return!1},i.reverse=function(){if(this._innerAction){var t=new n(this._innerAction.reverse());return this._cloneDecoration(t),t}return this},i.setInnerAction=function(t){this._innerAction!==t&&(this._innerAction=t)},i.getInnerAction=function(){return this._innerAction},i.isUnknownDuration=function(){return!!this._innerAction&&this._innerAction.isUnknownDuration()},i.toString=function(){return"<RepeatForever>"},n}(V);function z(t){return new Z(t)}function X(t,n){var i=new Y;return i.initWithTwoActions(t,n),i}var Y=function(t){function n(n){var i;if((i=t.call(this)||this)._one=null,i._two=null,i._finished=!1,!n||0===n.length)return v(i);1===n.length&&n.push(new B);var e=n.length-1;if(e>=0&&null==n[e]&&f(1015),e>=0){for(var r=n[0],o=1;o<e;o++)n[o]&&(r=X(r,n[o]));i.initWithTwoActions(r,n[e])}return i}h(n,t);var i=n.prototype;return i.initWithTwoActions=function(t,n){if(!t||!n)return l(1027),!1;var i=!1,e=t.getDurationScaled(),r=n.getDurationScaled();return this.initWithDuration(Math.max(e,r))&&(this._one=t,this._two=n,e>r?this._two=L(n,Q(e-r)):e<r&&(this._one=L(t,Q(r-e))),i=!0),i},i.clone=function(){var t=new n;return t._id=this._id,t._speed=this._speed,this._cloneDecoration(t),this._one&&this._two&&t.initWithTwoActions(this._one.clone(),this._two.clone()),t},i.startWithTarget=function(n){t.prototype.startWithTarget.call(this,n),this._one&&this._one.startWithTarget(n),this._two&&this._two.startWithTarget(n)},i.stop=function(){this._one&&this._one.stop(),this._two&&this._two.stop(),t.prototype.stop.call(this)},i.update=function(t){this._one&&(this._finished&&!this._one.isUnknownDuration()||this._one.update(t)),this._two&&(this._finished&&!this._two.isUnknownDuration()||this._two.update(t)),this._finished=1===t},i.reverse=function(){if(this._one&&this._two){var t=X(this._one.reverse(),this._two.reverse());return this._cloneDecoration(t),t}return this},i.updateOwner=function(t){if(this._one&&this._two){this._two._owner||(this._two._owner=t);var i=this._one;i instanceof n||i instanceof q?i.updateOwner(t):i._owner||(i._owner=t)}},i.findAction=function(t){var i=this._one,e=this._two,r=null,o=function(i){if(i.getId()===t)return i;if(i instanceof q||i instanceof n){var e=i.findAction(t);if(e)return e}return null};return i&&(r=o(i))||e&&(r=o(e))?r:null},i.isUnknownDuration=function(){var t=this._one,n=this._two;if(null==t||null==n)return!1;var i=t.isUnknownDuration(),e=n.isUnknownDuration();if(i||e){if(i&&e)return!0;if(this._finished)return!0}return!1},i.toString=function(){return"<Spawn>"},n}(V),J=function(t){function n(){return t.apply(this,arguments)||this}h(n,t);var i=n.prototype;return i.update=function(){},i.reverse=function(){var t=new n(this._duration);return this._cloneDecoration(t),t},i.clone=function(){var t=new n;return t._id=this._id,t._speed=this._speed,this._cloneDecoration(t),t.initWithDuration(this._duration),t},i.isUnknownDuration=function(){return!1},i.toString=function(){return"<DelayTime>"},n}(V);function Q(t){return new J(t)}var $=function(t){function n(n){var i;return(i=t.call(this)||this)._other=null,n&&i.initWithAction(n),i}h(n,t);var i=n.prototype;return i.initWithAction=function(n){return n?n===this._other?(l(1029),!1):!!t.prototype.initWithDuration.call(this,n.getDurationScaled())&&(this._other=n,!0):(l(1028),!1)},i.clone=function(){var t=new n;return t._id=this._id,t._speed=this._speed,this._cloneDecoration(t),this._other&&t.initWithAction(this._other.clone()),t},i.startWithTarget=function(n){t.prototype.startWithTarget.call(this,n),this._other&&this._other.startWithTarget(n)},i.update=function(t){this._other&&this._other.update(1-t)},i.reverse=function(){return this._other?this._other.clone():this},i.stop=function(){this._other&&this._other.stop(),t.prototype.stop.call(this)},i.isUnknownDuration=function(){return!1},i.toString=function(){return"<ReverseTime>"},n}(V);function tt(t){return new $(t)}var nt=function(t){function n(n,i,e){var r;return(r=t.call(this,n)||this)._cb=i,r._args=e,r}h(n,t);var i=n.prototype;return i.clone=function(){return new n(this._duration,this._cb,this._args)},i.update=function(t){this._cb.apply(this,[this.target,t].concat(this._args))},i.reverse=function(){return this.clone()},i.isUnknownDuration=function(){return!1},i.toString=function(){return"<ActionCustomUpdate>"},n}(V),it=function(t){function n(n,i){var e;return(e=t.call(this)||this)._finished=!1,e._cb=n,e._args=i,e}h(n,t);var i=n.prototype;return i.clone=function(){return new n(this._cb,this._args)},i.reverse=function(){return this.clone()},i.step=function(){throw new Error("should never go here")},i.update=function(){var t=a.game.deltaTime;this._finished=this._cb.apply(this,[this.target,t].concat(this._args))},i.isDone=function(){return this._finished},i.isUnknownDuration=function(){return!this.isDone()},i.toString=function(){return"<ActionUnknownDuration>"},n}(N);function et(t){var n=t.charAt(0);if(/[A-Z]/.test(n)){var i=(t=t.replace(n,n.toLowerCase())).split("-");if(2===i.length){var e=i[0];if("linear"===e)t="linear";else{var r=i[1];switch(e){case"quadratic":t="quad"+r;break;case"quartic":t="quart"+r;break;case"quintic":t="quint"+r;break;case"sinusoidal":t="sine"+r;break;case"exponential":t="expo"+r;break;case"circular":t="circ"+r;break;default:t=e+r}}}}return t}function rt(t){var n=" [Tween:] ",i=" option is not support in v + "+u,e=t;e.delay&&A(n+"delay"+i),e.repeat&&A(n+"repeat"+i),e.repeatDelay&&A(n+"repeatDelay"+i),e.interpolation&&A(n+"interpolation"+i),e.onStop&&A(n+"onStop"+i)}var ot=t("TweenAction",function(t){function n(n,i,e){var r;if((r=t.call(this)||this)._reversed=!1,null==e)e=Object.create(null);else if(rt(e),e.easing&&"string"==typeof e.easing&&(e.easing=et(e.easing)),e.progress||(e.progress=r.progress),e.easing&&"string"==typeof e.easing){var o=e.easing;e.easing=s[o],e.easing||d(1031,o)}for(var a in r._opts=e,r._props=Object.create(null),i){var u;if(i.hasOwnProperty(a)){var c=i[a];if("function"==typeof c)c=c();else if(null==c)continue;var h=void 0,l=void 0,f=void 0;void 0!==c.value?("function"==typeof(f=c.value)&&(f=f()),void 0!==c.easing&&("string"==typeof c.easing?(h=s[c.easing])||d(1031,c.easing):h=c.easing),void 0!==c.progress&&(l=c.progress)):f=c;var _=Object.create(null);_.start=_.current=_.end=null,_.keys=null,_.value=f,_.easing=h,_.progress=l,_.convert=c.convert,_.clone=c.clone,_.add=c.add,_.sub=c.sub,_.legacyProgress=null===(u=c.legacyProgress)||void 0===u||u,_.toFixed=c.toFixed,_.onStart=c.onStart,_.onStop=c.onStop,_.onComplete=c.onComplete,_.valid=!0,r._props[a]=_}}return r._originProps=i,r.initWithDuration(n),r}h(n,t);var i=n.prototype;return i.clone=function(){var t=new n(this._duration,this._originProps,this._opts);return t._reversed=this._reversed,t._owner=this._owner,t._id=this._id,this._cloneDecoration(t),t},i.reverse=function(){if(!this._opts.relative)return d(16382),new n(0,{});var t=new n(this._duration,this._originProps,this._opts);return this._cloneDecoration(t),t._reversed=!this._reversed,t._owner=this._owner,t},i.startWithTarget=function(n){t.prototype.startWithTarget.call(this,n);var i=this._getWorkerTarget();if(i){var e=!!this._opts.relative,r=this._props,o=this._reversed,s=function(){var t=i[a];if(void 0===t)return 0;var n=r[a],s=n.value;if("number"==typeof t)n.start=t,n.current=t,n.end=e?o?t-s:t+s:s;else if("object"==typeof t)if(n.legacyProgress){if(null==n.start){var u=t.constructor;n.start=new u,n.current=new u,n.end=new u}var c;c=s.getModifiableProperties?s.getModifiableProperties():Object.keys(s),n.keys=c;for(var h=0,l=c.length;h<l;++h){var f=c[h];isNaN(t[f])||(n.start[f]=t[f],n.current[f]=t[f],n.end[f]=e?o?t[f]-s[f]:t[f]+s[f]:s[f])}}else{var _=n.clone;if(!_)return d(16383,a),n.valid=!1,0;var g=n.add,p=n.sub;if(e&&(g||(d(16384,a),n.valid=!1),o&&!p&&(d(16385,a),n.valid=!1),!n.valid))return 0;n.start=_(t),n.current=_(t),n.end=e?o?p(t,s):g(t,s):_(s)}else if("string"==typeof t){var v=n.convert,A=function(t){if("number"==typeof t)return t;var n=t;return v&&(n=v(t)),"number"!=typeof n&&(n=Number(n),Number.isNaN(n))?(d(16386,""+t),null):n},T=A(s),w=A(t);if(null==T||null==w)return n.valid=!1,0;n.start=w,n.current=t,n.end=e?o?w-T:w+T:T}n.onStart&&n.onStart({relative:e,reversed:o,start:n.start,end:n.end})};for(var a in r)s();this._opts.onStart&&this._opts.onStart(i)}},i.stop=function(){var n=this._props;for(var i in n){var e=n[i];e.valid&&e.onStop&&e.onStop()}t.prototype.stop.call(this)},i.update=function(t){var n=this._getWorkerTarget();if(n&&this._opts){var i=this._props,e=this._opts,r=t;"function"==typeof e.easing&&(r=e.easing(t));var o=e.progress;for(var s in i){var a=i[s];if(a.valid){var u=a.easing?a.easing(t):r,c=a.progress?a.progress:o,h=a.start,l=a.end,f=a.current;if("number"==typeof f)a.current=c(h,l,a.current,u);else if("object"==typeof h)if(a.legacyProgress)for(var _=a.keys,g=0,p=_.length;g<p;++g){var v=_[g];a.current[v]=c(h[v],l[v],a.current[v],u)}else a.current=c(h,l,a.current,u);else if("string"==typeof f){var A,T=c(h,l,a.current,u);if("number"==typeof T)T=T.toFixed(null!==(A=a.toFixed)&&void 0!==A?A:0);else if("string"!=typeof T){d(16387);continue}a.current=T}n[s]=a.current,1===t&&a.onComplete&&a.onComplete()}}e.onUpdate&&e.onUpdate(n,t),1===t&&e.onComplete&&e.onComplete(n)}},i.progress=function(t,n,i,e){return t+(n-t)*e},i.isUnknownDuration=function(){return!1},g(n,[{key:"relative",get:function(){return!!this._opts.relative}}]),n}(V)),st=function(t){function n(n){var i;return(i=t.call(this)||this)._props=void 0,i._props={},n&&i.init(n),i}h(n,t);var i=n.prototype;return i.init=function(t){for(var n in t)this._props[n]=t[n];return!0},i.update=function(){var t=this._props,n=this._getWorkerTarget();for(var i in t)n[i]=t[i]},i.clone=function(){var t=new n;return t._id=this._id,t.init(this._props),t},i.isUnknownDuration=function(){return!1},n}(O);function at(){return C.instance.ActionManager}var ut=t("Tween",function(){function t(t){this._actions=[],this._finalAction=null,this._target=null,this._tag=W.TAG_INVALID,this._timeScale=1,this._target=void 0===t?null:t}var n=t.prototype;return n.tag=function(t){return this._tag=t,this},n.id=function(t){function n(n){return t.apply(this,arguments)}return n.toString=function(){return t.toString()},n}((function(t){return this._actions.length>0&&this._actions[this._actions.length-1].setId(t),this})),n.then=function(t){var n=t._union(!0);return n&&(n.setSpeed(t._timeScale),this._actions.push(n)),this},n.reverse=function(n,i){if(null==n&&null==i)return this.reverseTween();var e,r;if(n instanceof t?(e=n,void 0!==i&&(r=i)):"number"==typeof n&&(e=this,r=n),e){var o=t.reverseAction(e,r);o&&this._actions.push(o)}return this},n.reverseTween=function(){if(0===this._actions.length)return d(16388),this.clone(this._target);var t=this._union(!1),n=ct(this._target);return n._timeScale=this._timeScale,t&&n.insertAction(t.reverse()),n},t.reverseAction=function(t,n){var i=t._actions;if(0===i.length)return null;var e=null,r=null;return"number"==typeof n?e=t.findAction(n,i):t&&(e=t._union(!1)),e?(r=e.reverse())._owner=t:d(16391,""+n),r},n.findAction=function(t,n){for(var i=null,e=0,r=n.length;e<r;++e){if((i=n[e]).getId()===t)return i;if((i instanceof q||i instanceof Y)&&(i=i.findAction(t)))return i}return null},n.insertAction=function(t){var n=t.clone();return this.updateOwnerForAction(n),this._actions.push(n),this},n.updateOwnerForAction=function(t){t&&(t instanceof q||t instanceof Y?t.updateOwner(this):t._owner||(t._owner=this))},n.target=function(t){function n(n){return t.apply(this,arguments)}return n.toString=function(){return t.toString()},n}((function(t){return this._target=t,this})),n.getTarget=function(){return this._target},n.start=function(t){if(void 0===t&&(t=0),!this._target)return d(16392),this;this._finalAction&&at().removeAction(this._finalAction);var n=this._unionForStart();return this._finalAction=n,n?(n.setTag(this._tag),n.setSpeed(this._timeScale),n.setStartTime(t),n.setPaused(!1),at().addAction(n,this._target,!1)):d(16393),this},n.stop=function(){return this._finalAction&&(this._finalAction.stop(),at().removeAction(this._finalAction),this._finalAction=null),this},n.pause=function(){return this._finalAction?this._finalAction.setPaused(!0):d(16389),this},n.resume=function(){return this._finalAction?this._finalAction.setPaused(!1):d(16390),this},n.clone=function(t){var n=this._union(!1),i=ct(null!=t?t:this._target);return i._timeScale=this._timeScale,n?i.insertAction(n):i},n.union=function(t){var n,i=this;if(void 0===t)return n=i._union(!1),i._actions.length=0,n&&i._actions.push(n),this;var e=this._actions,r=e.findIndex((function(n){return n.getId()===t}));if(e.length>1){var o=e.splice(r);1===o.length?e.push(o[0]):e.push(G(o))}return this},n.to=function(t,n,i){var e=i||Object.create(null);e.relative=!1;var r=new ot(t,n,e);return this._actions.push(r),this},n.by=function(t,n,i){var e=i||Object.create(null);e.relative=!0;var r=new ot(t,n,e);return this._actions.push(r),this},n.update=function(t,n){for(var i=arguments.length,e=new Array(i>2?i-2:0),r=2;r<i;r++)e[r-2]=arguments[r];var o=new nt(t,n,e);return this._actions.push(o),this},n.updateUntil=function(t){for(var n=arguments.length,i=new Array(n>1?n-1:0),e=1;e<n;e++)i[e-1]=arguments[e];var r=new it(t,i);return this._actions.push(r),this},n.set=function(t){var n=new st(t);return this._actions.push(n),this},n.delay=function(t){var n=Q(t);return this._actions.push(n),this},n.call=function(t,n,i){var e=M(t,n,i);return this._actions.push(e),this},n.sequence=function(){for(var n=arguments.length,i=new Array(n),e=0;e<n;e++)i[e]=arguments[e];var r=t._wrappedSequence(i);return r&&this._actions.push(r),this},n.parallel=function(){for(var n=arguments.length,i=new Array(n),e=0;e<n;e++)i[e]=arguments[e];var r=t._wrappedParallel(i);return r&&this._actions.push(r),this},n.timeScale=function(t){return this._timeScale=t,this._finalAction&&this._finalAction.setSpeed(t),this},n.getTimeScale=function(){return this._timeScale},n.repeat=function(n,i){if(n===1/0)return this.repeatForever(i);var e,r=this._actions;return(e=i instanceof t?i._union(!1):r.pop())&&r.push(K(e,n)),this},n.repeatForever=function(n){var i,e=this._actions;return(i=n instanceof t?n._union(!1):e.pop())&&0!==e.length?e.push(K(i,Number.MAX_SAFE_INTEGER)):i instanceof V?e.push(z(i)):d(16394),this},n.reverseTime=function(n){var i,e=this._actions;return(i=n instanceof t?n._union(!1):e.pop())instanceof V?e.push(tt(i)):d(16395),this},n.hide=function(){if(this._target instanceof T){var t=new j;this._actions.push(t)}return this},n.show=function(){if(this._target instanceof T){var t=new x;this._actions.push(t)}return this},n.removeSelf=function(){if(this._target instanceof T){var t=P(!1);this._actions.push(t)}return this},n.destroySelf=function(){if(this._target instanceof T){var t=P(!0);this._actions.push(t)}return this},t.getRunningCount=function(t){return at().getNumberOfRunningActionsInTarget(t)},t.stopAll=function(){at().removeAllActions()},t.stopAllByTag=function(t,n){at().removeAllActionsByTag(t,n)},t.stopAllByTarget=function(t){at().removeAllActionsFromTarget(t)},t.pauseAllByTarget=function(t){at().pauseTarget(t)},t.resumeAllByTarget=function(t){at().resumeTarget(t)},n._union=function(t){var n=this._actions;if(0===n.length)return null;var i=G(n);return t&&this.updateOwnerForAction(i),i},n._unionForStart=function(){var t=this._actions;return 0===t.length?null:1===t.length&&t[0]instanceof Z?t[0]:G(t)},t._tweenToActions=function(n){var i=t._tmpArgs;i.length=0;for(var e=n.length,r=0;r<e;r++){var o=n[r],s=o._union(!0);s&&(s.setSpeed(o._timeScale),i.push(s))}},t._wrappedSequence=function(n){t._tweenToActions(n);var i=G(t._tmpArgs);return this._tmpArgs.length=0,i},t._wrappedParallel=function(n){t._tweenToActions(n);var i,e=(i=t._tmpArgs,new Y(i));return this._tmpArgs.length=0,e},g(t,[{key:"running",get:function(){return!!this._finalAction&&at().isActionRunning(this._finalAction)}},{key:"duration",get:function(){return this._finalAction?this._finalAction.getDuration():0}}]),t}());function ct(t){return new ut(t)}function ht(t){return d(16396),new ut(t)}ut._tmpArgs=[],c.Tween=ut,c.tween=ct,c.tweenUtil=ht}}}));
