System.register(["./global-exports-CLZKKIY2.js","./index-Y4La_nfG.js","./deprecated-Bf8XgTPJ.js","./director-8iUu7HD2.js","./wasm-minigame-DBi57dFz.js","./gc-object-D18ulfCO.js","./debug-view-BP17WHcy.js","./component-CsuvAQKv.js","./factory-BOc5khhM.js","./scene-ArUG4OfI.js","./prefab-BQYc0LyR.js","./pipeline-state-manager-DQyhxoC_.js","./node-event-DTNosVQv.js","./ui-renderer-CboX9P_t.js","./buffer-barrier-q_79u36H.js","./device-manager-B8lyUDPi.js","./deprecated-D5UVm7fE.js","./deprecated-6ty78xzL.js","./touch-B157r-vS.js","./renderer-CZheciPr.js"],(function(t,e){"use strict";var n,i,s,a,r,o,h,l,u,c,d,_,f,m,p,v,k,g,C,y,S,E,A,D,T,b,M,I,x,w,N,R,U,L,P,B,O,F,j,z,H,V,W,Y,J,X,q,G,Q,K,Z,$;return{setters:[function(t){n=t.l},function(t){i=t.c,s=t.t,a=t.a,r=t.s,o=t.a2,h=t.S,l=t.M,u=t.C,c=t.H,d=t.b,_=t.L},function(t){f=t.g},function(t){m=t.d},function(t){t.i,p=t.f,v=t.e},function(t){k=t.aq,g=t.K,C=t.F,y=t.a,S=t._,E=t.E,A=t.f,D=t.o,T=t.r,b=t.j,M=t.w,I=t.Q,x=t.ah,w=t.d,N=t.h,R=t.b,U=t.a5},null,function(t){L=t.A},null,function(t){P=t.N,B=t.h,O=t.i,F=t.d,j=t.M,z=t.b},null,null,function(t){H=t.N},function(t){V=t.a,W=t.j,Y=t.k,J=t.R,X=t.b,q=t.U,G=t.i,Q=t.h},function(t){K=t.m,Z=t.ai,$=t.af},null,null,null,null,null],execute:function(){var tt;t("loadWasmModuleSpine",dn),function(t,e){function n(t){for(var e in t)t[t[e]]=e}var i=t.MixBlend={setup:0,first:1,replace:2,add:3};n(i),n(i=t.MixDirection={mixIn:0,mixOut:1}),n(i=t.TimelineType={rotate:0,translate:1,scale:2,shear:3,attachment:4,color:5,deform:6,event:7,drawOrder:8,ikConstraint:9,transformConstraint:10,pathConstraintPosition:11,pathConstraintSpacing:12,pathConstraintMix:13,twoColor:14}),n(i=t.EventType={start:0,interrupt:1,end:2,dispose:3,complete:4,event:5}),n(i=t.BlendMode={Normal:0,Additive:1,Multiply:2,Screen:3}),n(i=t.TransformMode={Normal:0,OnlyTranslation:1,NoRotationOrReflection:2,NoScale:3,NoScaleOrReflection:4}),n(i=t.PositionMode={Fixed:0,Percent:1}),n(i=t.SpacingMode={Length:0,Fixed:1,Percent:2}),n(i=t.RotateMode={Tangent:0,Chain:1,ChainScale:2}),n(i=t.TextureFilter={Nearest:9728,Linear:9729,MipMap:9987,MipMapNearestNearest:9984,MipMapLinearNearest:9985,MipMapNearestLinear:9986,MipMapLinearLinear:9987}),n(i=t.TextureWrap={MirroredRepeat:33648,ClampToEdge:33071,Repeat:10497}),n(i=t.AttachmentType={Region:0,BoundingBox:1,Mesh:2,LinkedMesh:3,Path:4,Point:5,Clipping:6}),i=null;var s=3.1415927,a=2*s,r=180/s,o=r,h=s/180,l=h;t.MathUtils=((e=function(){function t(){}return t.clamp=function(t,e,n){return t<e?e:t>n?n:t},t.cosDeg=function(t){return Math.cos(t*l)},t.sinDeg=function(t){return Math.sin(t*l)},t.signum=function(t){return t>0?1:t<0?-1:0},t.toInt=function(t){return t>0?Math.floor(t):Math.ceil(t)},t.cbrt=function(t){var e=Math.pow(Math.abs(t),1/3);return t<0?-e:e},t.randomTriangular=function(e,n){return t.randomTriangularWith(e,n,.5*(e+n))},t.randomTriangularWith=function(t,e,n){var i=Math.random(),s=e-t;return i<=(n-t)/s?t+Math.sqrt(i*s*(n-t)):e-Math.sqrt((1-i)*s*(e-n))},t}()).PI=s,e.PI2=a,e.radiansToDegrees=r,e.radDeg=o,e.degreesToRadians=h,e.degRad=l,e)}(tt||(tt={}));var et=tt;function nt(t,e){return t?e===t.length?t:e<t.length?t.slice(0,e):new Array(e):new Array(e)}function it(t,e,n){var i="_"+n;Object.defineProperty(t,n,{get:function(){var t=e.call(this),n=t.size(),s=this[i];s=nt(s,n);for(var a=0;a<n;a++)s[a]=t.get(a);return this[i]=s,s}})}function st(t,e,n){var i="_"+n;Object.defineProperty(t,n,{get:function(){var t=e.call(this),n=t.size(),s=this[i];s=nt(s,n);for(var a=0;a<n;a++){var r=t.get(a),o=r.size(),h=s[a];h=nt(h,o);for(var l=0;l<o;l++)h[l]=r.get(l);s[a]=h}return this[i]=s,s}})}function at(t,e,n){var i="_"+n;Object.defineProperty(t,n,{value:function(){var t=e.call(this),n=t.size(),s=this[i];s=nt(s,n);for(var a=0;a<n;a++)s[a]=t.get(a);return this[i]=s,s}})}function rt(t){for(var e in et.wasmUtil=t.SpineWasmUtil,et.wasmUtil.wasm=t,et.wasmUtil.spineWasmInit(),t){var n=t[e];et[e]||(et[e]=n)}}function ot(){var t=et.PathConstraintData.prototype;it(t,t.getBones,"bones")}function ht(){var t=et.VertexAttachment.prototype;it(t,t.getBones,"bones"),it(t,t.getVertices,"vertices");var e=t.computeWorldVertices,n=new et.SPVectorFloat;Object.defineProperty(t,"computeWorldVertices",{value:function(t,i,s,a,r,o){var h=a.length;n.resize(h,0);for(var l=0;l<h;l++)n.set(l,a[l]);e.call(this,t,i,s,n,r,o);for(var u=0;u<h;u++)a[u]=n.get(u)}})}function lt(){var t=et.MeshAttachment.prototype;it(t,t.getRegionUVs,"regionUVs"),it(t,t.getUVs,"uvs"),it(t,t.getTriangles,"triangles"),it(t,t.getEdges,"edges")}function ut(){var t=et.PathAttachment.prototype;it(t,t.getLengths,"lengths")}function ct(){var t=et.RegionAttachment.prototype;it(t,t.getOffset,"offset");var e=t.getUVs,n=t.setUVs,i="_uvs";Object.defineProperty(t,"uvs",{get:function(){var n=e.call(this),s=n.size(),a=t[i];a=nt(a,s);for(var r=0;r<s;r++)a[r]=n.get(r);return t[i]=a,a},set:function(t){n.call(this,t[0],t[1],t[2],t[3],1===t[4])}});var s=t.computeWorldVertices,a=new et.SPVectorFloat;Object.defineProperty(t,"computeWorldVertices",{value:function(t,e,n,i){var r=e.length;a.resize(r,0);for(var o=0;o<r;o++)a.set(o,e[o]);s.call(this,t,a,n,i);for(var h=0;h<r;h++)e[h]=a.get(h)}})}function dt(){var t=et.IkConstraint.prototype;it(t,t.getBones,"bones")}function _t(){var t=et.PathConstraint.prototype;it(t,t.getBones,"bones")}function ft(){var t=et.TransformConstraintData.prototype;it(t,t.getBones,"bones")}function mt(){var t=et.TransformConstraint.prototype;it(t,t.getBones,"bones")}function pt(){var t=et.Bone.prototype;it(t,t.getChildren,"children")}function vt(){var t=et.Slot.prototype;it(t,t.getDeform,"deform")}function kt(){var t=et.Skin.prototype;it(t,t.getBones,"bones"),it(t,t.getAttachments,"attachments"),it(t,t.getConstraints,"constraints"),at(t,t.getAttachments,"getAttachments");var e=t.getAttachmentsForSlot;Object.defineProperty(t,"getAttachmentsForSlot",{value:function(t,n){var i=e.call(this,t),s=i.size();n.length=s;for(var a=0;a<s;a++)n[a]=i.get(a);i.delete()}});var n=t.findNamesForSlot;Object.defineProperty(t,"findNamesForSlot",{value:function(t,e){var i=n.call(this,t),s=i.size();e.length=s;for(var a=0;a<s;a++)e[a]=i.get(a);i.delete()}})}function gt(){var t=et.SkinEntry.prototype;[["name",t.getName],["attachment",t.getAttachment]].forEach((function(e){k(t,e[0],e[1])}))}function Ct(){var t=et.SkeletonData.prototype;it(t,t.getBones,"bones"),it(t,t.getSlots,"slots"),it(t,t.getSkins,"skins"),it(t,t.getAnimations,"animations"),it(t,t.getEvents,"events"),it(t,t.getIkConstraints,"ikConstraints"),it(t,t.getTransformConstraints,"transformConstraints"),it(t,t.getPathConstraints,"pathConstraints")}function yt(){var t=et.RotateTimeline.prototype;it(t,t.getFrames,"frames")}function St(){var t=et.ColorTimeline.prototype;it(t,t.getFrames,"frames")}function Et(){var t=et.AttachmentTimeline.prototype;it(t,t.getFrames,"frames"),it(t,t.getAttachmentNames,"attachmentNames")}function At(){var t=et.DeformTimeline.prototype;it(t,t.getFrames,"frames"),st(t,t.getFrameVertices,"frameVertices")}function Dt(){var t=et.EventTimeline.prototype;it(t,t.getFrames,"frames"),it(t,t.getEvents,"events")}function Tt(){var t=et.DrawOrderTimeline.prototype;it(t,t.getFrames,"frames")}function bt(){var t=et.AnimationState.prototype;it(t,t.getTracks,"tracks")}function Mt(){var t=et.Animation.prototype;it(t,t.getTimelines,"timelines")}function It(){var t=et.Skeleton.prototype;it(t,t.getBones,"bones"),it(t,t.getSlots,"slots"),it(t,t.getDrawOrder,"drawOrder"),it(t,t.getIkConstraints,"ikConstraints"),it(t,t.getTransformConstraints,"transformConstraints"),it(t,t.getPathConstraints,"pathConstraints"),it(t,t.getUpdateCacheList,"_updateCache")}var xt=null,wt=[];wt.push((function(t){var e;rt(t),it(e=et.IkConstraintData.prototype,e.getBones,"bones"),ot(),lt(),ut(),ct(),ht(),dt(),_t(),ft(),mt(),pt(),vt(),kt(),gt(),Ct(),yt(),St(),Et(),At(),Dt(),Tt(),bt(),Mt(),It()})),f.onPostInfrastructureInitDelegate.add((function(){return v().then((function(){return Promise.all([e.import("./spine.asm-CuMHFj9t.js"),e.import("./spine.js-BXJrVNao.js")]).then((function(t){return e=t[0].default,n=t[1].default,new Promise((function(t,i){p(n).then((function(t){var n={};return n.buffer=new ArrayBuffer(33554432),e({wasmMemory:n,memoryInitializerRequest:{response:t,status:200}}).then((function(t){xt=t,wt.forEach((function(t){t(xt)}))}))})).then(t).catch(i)}));var e,n}))})).catch((function(t){g(t)}))}));var Nt,Rt=0,Ut=0,Lt=function(){function t(){this.start=void 0,this.interrupt=void 0,this.end=void 0,this.dispose=void 0,this.complete=void 0,this.event=void 0}return t.getListeners=function(e,n){if(!e.listener){e.listener=new t;var i=++Ut;n.setTrackEntryListener(i,e),t._trackSet.set(i,e)}return e.listener},t.emitListener=function(e,n,i,s){var a=t._listenerSet.get(e);if(a)switch(s){case et.EventType.event:a.event&&a.event(n,i);break;case et.EventType.start:a.start&&a.start(n);break;case et.EventType.interrupt:a.interrupt&&a.interrupt(n);break;case et.EventType.end:a.end&&a.end(n);break;case et.EventType.dispose:a.dispose&&a.dispose(n);break;case et.EventType.complete:a.complete&&a.complete(n);break;default:C("emitListener doesn't handled",s)}},t.emitTrackEntryListener=function(t,e,n,i){var s=this._trackSet.get(t);if(s)switch(i){case et.EventType.start:s.listener.start&&s.listener.start(e);break;case et.EventType.interrupt:s.listener.interrupt&&s.listener.interrupt(e);break;case et.EventType.end:s.listener.end&&s.listener.end(e);break;case et.EventType.dispose:s.listener.dispose&&s.listener.dispose(e),this._trackSet.delete(t),s.listener=null;break;case et.EventType.complete:s.listener.complete&&s.listener.complete(e);break;case et.EventType.event:s.listener.event&&s.listener.event(e,n);break;default:C("TrackEntry doesn't handled",i)}},t.addListener=function(e){var n=++Rt;return t._listenerSet.set(n,e),n},t.removeListener=function(e){t._listenerSet.delete(e)},t}();Lt._listenerSet=new Map,Lt._trackSet=new Map,globalThis.TrackEntryListeners=Lt;var Pt=1/60;V(W);var Bt,Ot,Ft,jt,zt,Ht,Vt,Wt,Yt,Jt,Xt=V(Y),qt=function(){this.a=0,this.b=0,this.c=0,this.d=0,this.worldX=0,this.worldY=0},Gt=function(){this.vCount=0,this.iCount=0,this.vData=null,this.iData=null,this.meshes=[]},Qt=function(){this.iCount=0,this.blendMode=0,this.textureID=0},Kt=function(){function t(t){this._instance=null,this._state=null,this._skeletonData=null,this._skeleton=null,this._privateMode=!1,this._curIndex=-1,this._isCompleted=!1,this._maxFrameIdex=0,this._frameIdx=-1,this._inited=!1,this._invalid=!0,this._enableCacheAttachedInfo=!1,this._skeletonInfo=null,this._animationName=null,this.isCompleted=!1,this.totalTime=0,this.frames=[],this._privateMode=!1,this._inited=!1,this._invalid=!0,this._instance=new et.SkeletonInstance,this._instance.isCache=!0,this._skeletonData=t,this._skeleton=this._instance.initSkeleton(t),this._instance.setUseTint(!0)}var e=t.prototype;return e.init=function(t,e){this._inited=!0,this._animationName=e,this._skeletonInfo=t},e.setSkin=function(t){this._skeleton&&this._skeleton.setSkinByName(t),this._instance.setSkin(t)},e.setAnimation=function(t){var e=this._skeletonData.animations,n=null;e.forEach((function(e){e.name===t&&(n=e)})),n?(this._maxFrameIdex=Math.floor(n.duration/Pt),this._maxFrameIdex<=0&&(this._maxFrameIdex=1),this._instance.setAnimation(0,t,!1)):C("find no animation named "+t+" !!!")},e.updateToFrame=function(t){if(this._inited&&(this.begin(),this.needToUpdate(t)))do{this._frameIdx++,this.totalTime+=Pt,this._instance.updateAnimation(Pt);var e=this._instance.updateRenderData();this.updateRenderData(this._frameIdx,e),this._frameIdx>=this._maxFrameIdex&&(this.isCompleted=!0)}while(this.needToUpdate(t))},e.getFrame=function(t){var e=t%this._maxFrameIdex;return this.frames[e]},e.invalidAnimationFrames=function(){this._curIndex=-1,this._isCompleted=!1,this.frames.length=0},e.updateRenderData=function(t,e){var n=e.vCount,i=e.iCount,s=Xt/Float32Array.BYTES_PER_ELEMENT,a=new Uint8Array(Float32Array.BYTES_PER_ELEMENT*s*n),r=new Uint16Array(i),o=et.wasmUtil.wasm.HEAPU8,h=e.vPtr,l=n*Float32Array.BYTES_PER_ELEMENT*s;a.set(o.subarray(h,h+l));var u=e.iPtr,c=Uint16Array.BYTES_PER_ELEMENT*i;new Uint8Array(r.buffer).set(o.subarray(u,u+c));var d=new Gt;d.vCount=n,d.iCount=i,d.vData=a,d.iData=r;for(var _=e.getData(),f=e.getTextures(),m=_.size(),p=0;p<m;p+=5){var v=new Qt;v.iCount=_.get(p+3),v.blendMode=_.get(p+4),v.textureID=f.get(p/5),d.meshes.push(v)}var k=this._skeleton.bones,g=[];k.forEach((function(t){var e=new qt;e.a=t.a,e.b=t.b,e.c=t.c,e.d=t.d,e.worldX=t.worldX,e.worldY=t.worldY,g.push(e)})),this.frames[t]={model:d,boneInfos:g}},e.begin=function(){if(this._invalid){var t=this._skeletonInfo,e=null==t?void 0:t.curAnimationCache;e&&e!==this&&(this._privateMode?e.invalidAllFrame():e.updateToFrame(0));var n=null==t?void 0:t.listener;this._instance.setAnimation(0,this._animationName,!1),this.bind(n),t.curAnimationCache=this,this._frameIdx=-1,this.isCompleted=!1,this.totalTime=0,this._invalid=!1}},e.end=function(){this.needToUpdate()||(this._skeletonInfo.curAnimationCache=null,this.frames.length=this._frameIdx+1,this.isCompleted=!0,this.unbind(this._skeletonInfo.listener))},e.bind=function(t){var e=this;t.complete=function(t){t&&t.animation.name===e._animationName&&(e.isCompleted=!0)}},e.unbind=function(t){t.complete=null},e.needToUpdate=function(t){return!this.isCompleted&&this.totalTime<30&&(void 0===t||this._frameIdx<t)},e.isInited=function(){return this._inited},e.isInvalid=function(){return this._invalid},e.invalidAllFrame=function(){this.isCompleted=!1,this._invalid=!0},e.enableCacheAttachedInfo=function(){this._enableCacheAttachedInfo||(this._enableCacheAttachedInfo=!0,this.invalidAllFrame())},e.clear=function(){this._inited=!1,this.invalidAllFrame()},e.destroy=function(){this._instance&&(this._instance.destroy(),this._instance=null)},y(t,[{key:"skeleton",get:function(){return this._skeleton}}]),t}(),Zt=function(){function t(){this._privateMode=void 0,this._skeletonCache=void 0,this._animationPool=void 0,this._sharedCacheMap=new Map,this._privateMode=!1,this._animationPool={},this._skeletonCache={}}var e=t.prototype;return e.enablePrivateMode=function(){this._privateMode=!0},e.clear=function(){this._animationPool={},this._skeletonCache={}},e.invalidAnimationCache=function(t){var e=this._skeletonCache[t];if(e&&e.skeleton){var n=e.animationsCache;for(var i in n)n[i].invalidAllFrame()}},e.destroySkeleton=function(t){var e=this;if(!this._privateMode){var n=this._sharedCacheMap.get(t);if(n){if((n-=1)>0)return void this._sharedCacheMap.set(t,n);this._sharedCacheMap.delete(t)}}var i=this._skeletonCache[t];if(i){var s=this._privateMode?function(t,e){e.destroy()}:function(n,i){e._animationPool[t+"#"+n]=i,i.clear()},a=i.animationsCache;for(var r in a){var o=a[r];o&&s(r,o)}i.skeleton&&et.wasmUtil.destroySpineSkeleton(i.skeleton),delete this._skeletonCache[t]}},e.createSkeletonInfo=function(t){var e=t.uuid,n=t.getRuntimeData();if(!this._privateMode){var i=this._sharedCacheMap.get(e);i?i+=1:i=1,this._sharedCacheMap.set(e,i)}if(this._skeletonCache[e])return this._skeletonCache[e];var s=new et.Skeleton(n),a=new Lt;return this._skeletonCache[e]={skeleton:s,clipper:null,state:null,listener:a,animationsCache:{},curAnimationCache:null,assetUUID:e}},e.getSkeletonInfo=function(t){var e=t.uuid;return this._skeletonCache[e]},e.getAnimationCache=function(t,e){var n=this._skeletonCache[t];return n?n.animationsCache[e]:null},e.initAnimationCache=function(t,e,n){var i=e.getRuntimeData();if(!i)return null;var s=this._skeletonCache[t];if(!s||!s.skeleton)return null;var a=s.animationsCache,r=a[n];if(!r){var o=t+"#"+n;(r=this._animationPool[o])?delete this._animationPool[o]:(r=new Kt(i))._privateMode=this._privateMode,r.init(s,n),a[n]=r}return r.init(s,n),r.setAnimation(n),r},e.destroyCachedAnimations=function(t){if(t){var e=this._animationPool;for(var n in e)n.includes(t)&&(e[n].destroy(),delete e[n]);var i=this._skeletonCache[t],s=i&&i.skeleton;s&&et.wasmUtil.destroySpineSkeleton(s),i&&delete this._skeletonCache[t]}else{var a=this._animationPool;for(var r in a)a[r].destroy(),delete a[r]}},t}();Nt=Zt,Zt.FrameTime=Pt,Zt.sharedCache=new Nt;var $t=(Bt=i("sp.SkeletonData"),Ot=s([B]),Ft=s([A]),Bt((zt=function(t){function e(){var e;return(e=t.call(this)||this)._skeletonJson=Ht&&Ht(),e.textures=Vt&&Vt(),e.textureNames=Wt&&Wt(),e.scale=Yt&&Yt(),e._atlasText=Jt&&Jt(),e._buffer=void 0,e._skeletonCache=null,e._skinsEnum=null,e._animsEnum=null,e.reset(),e}S(e,t);var n=e.prototype;return n.createNode=function(t){var e=new P(this.name);return e.addComponent("cc.Skeleton").skeletonData=this,t(null,e)},n.reset=function(){this._skeletonCache=null},n.resetEnums=function(){},n.getRuntimeData=function(t){if(this._skeletonCache)return this._skeletonCache;if(!(this.textures&&this.textures.length>0)&&this.textureNames&&this.textureNames.length>0)return t||g(this.name+" no textures found!"),null;var e=this.mergedUUID(),n=et.wasmUtil.querySpineSkeletonDataByUUID(e);if(n)this._skeletonCache=n;else{for(var i=this.textures.length,s=[],a=0;a<i;++a){var r=this.textures[a];s.push(r.uuid||r.getId())}if(this._skeletonJson)this._skeletonCache=et.wasmUtil.createSpineSkeletonDataWithJson(this.skeletonJsonStr,this._atlasText,this.textureNames,s),et.wasmUtil.registerSpineSkeletonDataWithUUID(this._skeletonCache,e);else{var o=new Uint8Array(this._nativeAsset),h=o.length,l=et.wasmUtil.createStoreMemory(h);et.wasmUtil.wasm.HEAPU8.subarray(l,l+h).set(o),this._skeletonCache=et.wasmUtil.createSpineSkeletonDataWithBinary(h,this._atlasText,this.textureNames,s),et.wasmUtil.registerSpineSkeletonDataWithUUID(this._skeletonCache,e),et.wasmUtil.freeStoreMemory()}}return this._skeletonCache},n.getSkinsEnum=function(){if(this._skinsEnum)return this._skinsEnum;var t=this.getRuntimeData(!0);if(t){for(var e=t.skins,n={},i=0;i<e.length;i++)n[e[i].name]=i;return this._skinsEnum=E(n)}return null},n.getAnimsEnum=function(){if(this._animsEnum&&Object.keys(this._animsEnum).length>1)return this._animsEnum;var t=this.getRuntimeData(!0);if(t){for(var e={"<None>":0},n=t.animations,i=0;i<n.length;i++)e[n[i].name]=i+1;return this._animsEnum=E(e)}return null},n.mergedUUID=function(){var t=[this._atlasText].concat(this.textures.map((function(t){return t.getId()}))).join("");return""+this._uuid+K(t,668)},n.destroy=function(){return Zt.sharedCache.destroyCachedAnimations(this._uuid),et.wasmUtil.destroySpineSkeletonDataWithUUID(this.mergedUUID()),t.prototype.destroy.call(this)},y(e,[{key:"skeletonJsonStr",get:function(){return this._skeletonJson?JSON.stringify(this._skeletonJson):""}},{key:"skeletonJson",get:function(){return this._skeletonJson},set:function(t){this.reset(),this._skeletonJson="string"==typeof t?JSON.parse(t):t,!this._uuid&&t.skeleton&&(this._uuid=t.skeleton.hash)}},{key:"atlasText",get:function(){return this._atlasText},set:function(t){this._atlasText=t,this.reset()}},{key:"_nativeAsset",get:function(){return this._buffer},set:function(t){this._buffer=t,this.reset()}}]),e}(L),Ht=a(zt.prototype,"_skeletonJson",[r],(function(){return null})),Vt=a(zt.prototype,"textures",[r,Ot],(function(){return[]})),Wt=a(zt.prototype,"textureNames",[r,Ft],(function(){return[]})),Yt=a(zt.prototype,"scale",[r],(function(){return 1})),Jt=a(zt.prototype,"_atlasText",[r],(function(){return""})),jt=zt))||jt);n.internal.SpineSkeletonData=$t;var te=function(t){function e(){var e;return(e=t.call(this)||this)._skeletons=new Set,e}S(e,t),e.getInstance=function(){return e._instance||(e._instance=new e,m.registerSystem(e.ID,e._instance,o.HIGH)),e._instance};var n=e.prototype;return n.add=function(t){t&&(this._skeletons.has(t)||this._skeletons.add(t))},n.remove=function(t){t&&this._skeletons.has(t)&&this._skeletons.delete(t)},n.postUpdate=function(t){this._skeletons&&this._skeletons.forEach((function(e){e.updateAnimation(t)}))},n.prepareRenderData=function(){this._skeletons&&this._skeletons.forEach((function(t){t._markForUpdateRenderData()}))},e}(h);te.ID="SKELETON",te._instance=void 0,n.internal.SpineSkeletonSystem=te;var ee,ne,ie,se,ae,re,oe,he,le,ue,ce,de,_e,fe,me,pe,ve,ke,ge,Ce,ye,Se,Ee,Ae,De,Te,be,Me,Ie,xe,we,Ne=new l,Re=function(){function t(){this._isInitialized=!1,this._skeletonBones=null,this._socketNodes=null,this._keysToDelete=[],this._isInitialized=!1}var e=t.prototype;return e.init=function(t){var e;this._isInitialized=!1,t&&0!==(null==(e=t.socketNodes)?void 0:e.size)&&(this._skeletonBones=t._skeleton.bones,!this._skeletonBones||this._skeletonBones.length<1||(this._socketNodes=t.socketNodes,!this._socketNodes||this._socketNodes.size<=0||(this._isInitialized=!0,this._syncAttachedNode())))},e.updateSkeletonBones=function(t){this._skeletonBones=t},e.reset=function(){this._isInitialized=!1,this._skeletonBones=null,this._socketNodes=null,this._keysToDelete.length=0},e._syncAttachedNode=function(){if(this._isInitialized){for(var t,e=this._socketNodes,n=D(e);!(t=n()).done;){var i=t.value,s=i[0],a=i[1];if(a&&a.isValid){var r=this._skeletonBones[s];r&&this.matrixHandle(a,r)}else this._keysToDelete.push(s)}if(!(this._keysToDelete.length<=0)){for(var o,h=D(this._keysToDelete);!(o=h()).done;){var l=o.value;e.delete(l)}this._keysToDelete.length=0}}},e.matrixHandle=function(t,e){var n=Ne;n.m00=e.a,n.m01=e.c,n.m04=e.b,n.m05=e.d,n.m12=e.worldX,n.m13=e.worldY,t.matrix=Ne},t}();!function(t){t[t.UNSET=-1]="UNSET",t[t.REALTIME=0]="REALTIME",t[t.SHARED_CACHE=1]="SHARED_CACHE",t[t.PRIVATE_CACHE=2]="PRIVATE_CACHE"}(we||(we={})),T(we);var Ue,Le,Pe=we;!function(t){t[t.default=0]="default"}(Ue||(Ue={})),T(Ue),function(t){t[t["<None>"]=0]="<None>"}(Le||(Le={})),T(Le);var Be,Oe=Le;!function(t){t[t.COLORED_TEXTURED=0]="COLORED_TEXTURED",t[t.TWO_COLORED=1]="TWO_COLORED"}(Be||(Be={}));var Fe=(ee=i("sp.Skeleton.SpineSocket"),ne=s(P),ee((se=function(t,e){void 0===t&&(t=""),void 0===e&&(e=null),this.path=ae&&ae(),this.target=re&&re(),this.path=t,this.target=e},ae=a(se.prototype,"path",[r],(function(){return""})),re=a(se.prototype,"target",[ne,r],(function(){return null})),ie=se))||ie);b(Fe,"sp.Skeleton.SpineSocket");var je=(oe=i("sp.Skeleton"),he=s($t),le=s(Ue),ue=s(Le),ce=s(we),de=s([Fe]),_e=s(z),oe((xe=function(t){function e(){var e;return(e=t.call(this)||this)._skeletonData=pe&&pe(),e.defaultSkin=ve&&ve(),e.defaultAnimation=ke&&ke(),e._premultipliedAlpha=ge&&ge(),e._timeScale=Ce&&Ce(),e._preCacheMode=ye&&ye(),e._cacheMode=Se&&Se(),e._sockets=Ee&&Ee(),e._useTint=Ae&&Ae(),e._debugMesh=De&&De(),e._debugBones=Te&&Te(),e._debugSlots=be&&be(),e._enableBatch=Me&&Me(),e._runtimeData=null,e._skeleton=null,e._instance=null,e._state=null,e._textures=[],e._skeletonInfo=null,e._animationName="",e._skinName="",e._drawList=new U((function(){return{material:null,texture:null,indexOffset:0,indexCount:0}}),1),e._materialCache={},e.paused=!1,e._enumSkins=E({}),e._enumAnimations=E({}),e.attachUtil=void 0,e._socketNodes=new Map,e._cachedSockets=new Map,e._startEntry=void 0,e._endEntry=void 0,e._paused=!1,e._accTime=0,e._playCount=0,e._skeletonCache=null,e._animCache=null,e._animationQueue=[],e._headAniInfo=null,e._isAniComplete=!0,e._playTimes=0,e._curFrame=null,e._needUpdateSkeltonData=!0,e._listener=null,e._debugRenderer=null,e._startSlotIndex=void 0,e._endSlotIndex=void 0,e._customMaterialInstance=null,e._vLength=0,e._vBuffer=null,e._iLength=0,e._iBuffer=null,e._model=void 0,e._tempColor={r:0,g:0,b:0,a:0},e._eventListenerID=-1,e._slotTextures=null,e.loop=Ie&&Ie(),e._useVertexOpacity=!0,e._startEntry={animation:{name:""},trackIndex:0},e._endEntry={animation:{name:""},trackIndex:0},e._startSlotIndex=-1,e._endSlotIndex=-1,e._instance=new et.SkeletonInstance,e._instance.dtRate=1*e._timeScale,e._instance.isCache=e.isAnimationCached(),e.attachUtil=new Re,e}S(e,t);var n=e.prototype;return n.__preload=function(){t.prototype.__preload.call(this),this._updateSkeletonData(),this._updateDebugDraw()},n.onRestore=function(){this.updateMaterial(),this._markForUpdateRenderData()},n.getState=function(){return this._state},n.onEnable=function(){t.prototype.onEnable.call(this),this._instance&&(this._instance.enable=!0),this._flushAssembler(),te.getInstance().add(this)},n.onDisable=function(){t.prototype.onDisable.call(this),this._instance&&(this._instance.enable=!1),te.getInstance().remove(this)},n.onDestroy=function(){var e;this._eventListenerID>0&&(Lt.removeListener(this._eventListenerID),this._eventListenerID=-1),this._drawList.destroy(),this.destroyRenderData(),this._cleanMaterialCache(),this._vBuffer=null,this._iBuffer=null,this.attachUtil.reset(),null==(e=this._slotTextures)||e.clear(),this._slotTextures=null,this._cachedSockets.clear(),this._socketNodes.clear(),this._animCache=null,te.getInstance().remove(this),this._instance&&(this._instance.destroy(),this._instance=null),this._destroySkeletonInfo(this._skeletonCache),this._skeletonCache=null,t.prototype.onDestroy.call(this)},n.clearAnimation=function(t){this.isAnimationCached()||(this.clearTrack(t||0),this.setToSetupPose())},n.clearAnimations=function(){this.isAnimationCached()||(this.clearTracks(),this.setToSetupPose())},n._updateSkeletonData=function(){var t=this._skeletonData;if(!t)return this._runtimeData=null,this._state=null,this._skeleton=null,this._textures=[],void this._refreshInspector();this._instance&&(this._instance.dtRate=1*this._timeScale),this._needUpdateSkeltonData=!1,this._runtimeData=t.getRuntimeData(),this._runtimeData&&(this.setSkeletonData(this._runtimeData),this._textures=t.textures,this._refreshInspector(),this.defaultSkin&&""!==this.defaultSkin?this.setSkin(this.defaultSkin):this._skinName&&""!==this._skinName&&this.setSkin(this._skinName),this.defaultAnimation?this.animation=this.defaultAnimation.toString():this._animationName?this.animation=this._animationName:this.animation="",this._updateUseTint(),this._indexBoneSockets(),this._updateSocketBindings(),this.attachUtil.init(this),this._preCacheMode=this._cacheMode)},n.setSkeletonData=function(t){var e=this._skeletonCache;if(this._cacheMode===we.SHARED_CACHE?this._skeletonCache=Zt.sharedCache:this._cacheMode===we.PRIVATE_CACHE?(this._skeletonCache=new Zt,this._skeletonCache.enablePrivateMode()):this._skeletonCache=null,e!==this._skeletonCache&&this._destroySkeletonInfo(e),this.isAnimationCached()){(this.debugBones||this.debugSlots)&&M(16410);var n=this._skeletonCache.getSkeletonInfo(this._skeletonData);this._skeletonInfo!==n&&(this._destroySkeletonInfo(this._skeletonCache),n||this._cacheMode!==we.PRIVATE_CACHE||(this._animCache=this._skeletonCache.initAnimationCache(this.skeletonData.uuid,this._skeletonData,this._animationName)),this._skeletonInfo=this._skeletonCache.createSkeletonInfo(this._skeletonData)),this._skeletonInfo&&(this._skeleton=this._skeletonInfo.skeleton)}else this._skeleton=this._instance.initSkeleton(t),this._state=this._instance.getAnimationState(),this._instance.setPremultipliedAlpha(this._premultipliedAlpha);this._flushAssembler()},n.setSlotsRange=function(t,e){this.isAnimationCached()?M(16411):(this._startSlotIndex=t,this._endSlotIndex=e)},n.getAttachment=function(t,e){return this._skeleton?this._skeleton.getAttachmentByName(t,e):null},n.setAttachment=function(t,e){this._skeleton&&this._skeleton.setAttachment(t,e),this.invalidAnimationCache()},n.getTextureAtlas=function(t){return t.region},n.setAnimation=function(t,e,n){if("string"!=typeof e)return I(7511),null;var i=this._skeleton;if(!i||!i.data.findAnimation(e))return I(7509,e),null;var s=null;if(void 0===n&&(n=!0),this._playTimes=n?0:1,this.isAnimationCached()){if(0!==t&&M(16412),!this._skeletonCache)return null;var a=this._skeletonCache.getAnimationCache(this._skeletonData.uuid,e);a||(a=this._skeletonCache.initAnimationCache(this.skeletonData.uuid,this._skeletonData,e))&&this._skinName&&a.setSkin(this._skinName),a&&(this._animationName=e,this._isAniComplete=!1,this._accTime=0,this._playCount=0,this._animCache=a,this._socketNodes.size>0&&this._animCache.enableCacheAttachedInfo(),this._animCache.updateToFrame(0),this._curFrame=this._animCache.frames[0])}else this._animationName=e,s=this._instance.setAnimation(t,e,n);return this._markForUpdateRenderData(),s},n.addAnimation=function(t,e,n,i){if(i=i||0,this.isAnimationCached())return 0!==t&&M(16413),this._animationQueue.push({animationName:e,loop:n,delay:i}),null;if(this._skeleton){var s,a=this._skeleton.data.findAnimation(e);return a?null==(s=this._state)?void 0:s.addAnimationWith(t,a,n,i):(I(7510,e),null)}return null},n.findAnimation=function(t){return this._skeleton?this._skeleton.data.findAnimation(t):null},n.getCurrent=function(t){if(this.isAnimationCached())M(16414);else if(this._state)return this._state.getCurrent(t);return null},n.setSkin=function(t){t&&(this._skeleton&&this._skeleton.setSkinByName(t),this._instance.setSkin(t),this.isAnimationCached()&&this._animCache&&this._animCache.setSkin(t),this._skinName=t,this.invalidAnimationCache())},n.updateAnimation=function(t){if(this._markForUpdateRenderData(),!this.paused)if(this.isAnimationCached()){if(t*=1*this._timeScale,this._isAniComplete){var e;if(0===this._animationQueue.length&&!this._headAniInfo){var n=this._animCache;if(n&&n.isInvalid()){n.updateToFrame(0);var i=n.frames;this._curFrame=i[i.length-1]}return}if(this._headAniInfo||(this._headAniInfo=this._animationQueue.shift()),this._accTime+=t,this._accTime>(null==(e=this._headAniInfo)?void 0:e.delay)){var s=this._headAniInfo;this._headAniInfo=null,this.setAnimation(0,null==s?void 0:s.animationName,null==s?void 0:s.loop)}return}this._updateCache(t)}else this._instance.updateAnimation(t)},n._updateCache=function(t){var e=this._animCache;if(e.isInited()){var n=e.frames,i=Zt.FrameTime;0===this._accTime&&0===this._playCount&&(this._startEntry.animation.name=this._animationName,this._listener&&this._listener.start&&this._listener.start(this._startEntry)),this._accTime+=t;var s=Math.floor(this._accTime/i);if(e.isCompleted||e.updateToFrame(s),this._curFrame=n[s],void 0!==this._curFrame&&this.attachUtil.updateSkeletonBones(this._curFrame.boneInfos),e.isCompleted&&s>=n.length){if(this._playCount++,this._playTimes>0&&this._playCount>=this._playTimes)return this._curFrame=n[n.length-1],this._accTime=0,this._playCount=0,this._isAniComplete=!0,void this._emitCacheCompleteEvent();this._accTime=0,s=0,this._curFrame=n[s],this._emitCacheCompleteEvent()}}},n._emitCacheCompleteEvent=function(){this._listener&&(this._endEntry.animation.name=this._animationName,this._listener.complete&&this._listener.complete(this._endEntry),this._listener.end&&this._listener.end(this._endEntry))},n.updateRenderData=function(){return this.isAnimationCached()?this._curFrame?this._curFrame.model:null:this._instance.updateRenderData()},n._flushAssembler=function(){var t=e.Assembler.getAssembler(this);this._assembler!==t&&(this._assembler=t),this._skeleton&&this._assembler&&this._assembler.createData&&(this._renderData=this._assembler.createData(this),this._markForUpdateRenderData(),this._updateColor())},n._render=function(t){var e=0;if(this.renderData&&this._drawList.length>0){for(var n=this.renderData,i=n.chunk,s=i.vertexAccessor,a=n.getMeshBuffer(),r=a.indexOffset,o=0;o<this._drawList.length;o++){var h=this._drawList.data[o];h.texture&&t.commitMiddleware(this,a,r+h.indexOffset,h.indexCount,h.texture,h.material,this._enableBatch),e+=h.indexCount}var l=n.indices.subarray(0,e);s.appendIndices(i.bufferId,l),s.getMeshBuffer(i.bufferId).setDirty()}},n.requestDrawData=function(t,e,n,i){var s=this._drawList.add();s.material=t;var a,r,o=O.assets.get(e);return o||(o=null==(a=this.skeletonData)?void 0:a.textures.find((function(t){return t.uuid===e||t.getId()===e})))||(o=null==(r=this._slotTextures)?void 0:r.get(e)),s.texture=o,s.indexOffset=n,s.indexCount=i,s},n._updateBuiltinMaterial=function(){return F.get("default-spine-material")},n.updateMaterial=function(){var t;t=this._customMaterial?this._customMaterial:this._updateBuiltinMaterial(),this.setSharedMaterial(t,0),this._cleanMaterialCache()},n.getMaterialTemplate=function(){return null!==this.customMaterial?this.customMaterial:(this.material||this.updateMaterial(),this.material)},n._cleanMaterialCache=function(){for(var t in this._materialCache)this._materialCache[t].destroy();this._materialCache={}},n.getMaterialForBlendAndTint=function(t,e,n){var i=n+"/"+t+"/"+e,s=this._materialCache[i];if(s)return s;if(this._customMaterialInstance)s=this._customMaterialInstance;else{var a=this.getMaterialTemplate();s=new j({parent:a,subModelIdx:0,owner:this})}this._materialCache[i]=s,s.overridePipelineStates({blendState:{blendColor:u.WHITE,targets:[{blendEq:Z.ADD,blendAlphaEq:Z.ADD,blendSrc:t,blendDst:e,blendSrcAlpha:t,blendDstAlpha:e}]}});var r=!1;n===Be.TWO_COLORED&&(r=!0);var o=!this._enableBatch;return s.recompileShaders({TWO_COLORED:r,USE_LOCAL:o}),s},n._updateAnimEnum=function(){var t;t=this.skeletonData?this.skeletonData.getAnimsEnum():Le,this._enumAnimations=E({}),Object.assign(this._enumAnimations,t),E.update(this._enumAnimations),x(this,"_animationIndex",this._enumAnimations)},n._updateSkinEnum=function(){var t;t=this.skeletonData?this.skeletonData.getSkinsEnum():Ue,this._enumSkins=E({}),Object.assign(this._enumSkins,t),E.update(this._enumSkins),x(this,"_defaultSkinIndex",this._enumSkins)},n._refreshInspector=function(){},n.destroyRenderData=function(){this._drawList.reset(),t.prototype.destroyRenderData.call(this)},n.createRenderEntity=function(){var t=new J(X.DYNAMIC);return t.setUseLocal(!0),t},n.markForUpdateRenderData=function(e){void 0===e&&(e=!0),t.prototype._markForUpdateRenderData.call(this,e),this._debugRenderer&&this._debugRenderer._markForUpdateRenderData(e)},n.syncAttachedNode=function(){this.attachUtil._syncAttachedNode()},n.isAnimationCached=function(){return this._cacheMode!==we.REALTIME},n.setAnimationCacheMode=function(t){this._preCacheMode!==t&&(this._cacheMode=t,this._preCacheMode=t,this._instance&&(this._instance.isCache=this.isAnimationCached()),this._updateSkeletonData(),this._markForUpdateRenderData())},n.setToSetupPose=function(){this._skeleton&&this._skeleton.setToSetupPose()},n.setBonesToSetupPose=function(){this._skeleton&&this._skeleton.setBonesToSetupPose()},n.setSlotsToSetupPose=function(){this._skeleton&&this._skeleton.setSlotsToSetupPose()},n.invalidAnimationCache=function(){this.isAnimationCached()&&this._skeletonCache&&this._skeletonCache.invalidAnimationCache(this._skeletonData.uuid)},n.findBone=function(t){return this._skeleton?this._skeleton.findBone(t):null},n.findSlot=function(t){return this._skeleton?this._skeleton.findSlot(t):null},n.setMix=function(t,e,n){this.isAnimationCached()?M(16415):this._state&&this._instance.setMix(t,e,n)},n.clearTracks=function(){this.isAnimationCached()?M(16416):this._state&&(this._state.clearTracks(),this.setToSetupPose())},n.clearTrack=function(t){this.isAnimationCached()?M(16417):this._state&&this._state.clearTrack(t)},n.updateWorldTransform=function(){this.isAnimationCached()&&this._skeleton&&this._skeleton.updateWorldTransform()},n._verifySockets=function(t){for(var e=0,n=t.length;e<n;e++){var i=t[e].target;!i||i.parent&&i.parent===this.node||g("Target node "+i.name+" is expected to be a direct child of "+this.node.name)}var s=new Map;t.forEach((function(t){t.target&&(s.get(t.target)?g("Target node "+t.target.name+" has existed."):s.set(t.target,!0))}))},n._updateSocketBindings=function(){if(this._skeleton){this._socketNodes.clear();for(var t=0,e=this._sockets.length;t<e;t++){var n=this._sockets[t];if(n.path&&n.target){var i=this._cachedSockets.get(n.path);if(!i){g("Skeleton data does not contain path "+n.path);continue}this._socketNodes.set(i,n.target)}}}},n._indexBoneSockets=function(){if(this._skeleton){this._cachedSockets.clear();for(var t=this._skeleton.bones,e=function e(n){return null==n.parent?n.data.name||"<Unamed>":e(t[n.parent.data.index])+"/"+n.data.name},n=0,i=t.length;n<i;n++){var s=t[n].data,a=e(t[n]);this._cachedSockets.set(a,s.index)}}},n.querySockets=function(){return this._skeleton?(0===this._cachedSockets.size&&this._indexBoneSockets(),this._cachedSockets.size>0?Array.from(this._cachedSockets.keys()).sort():[]):[]},n._updateUseTint=function(){this._cleanMaterialCache(),this.destroyRenderData(),this.isAnimationCached()||this._instance.setUseTint(this._useTint);var t=this._assembler;t&&t.createData&&this._skeleton&&(this._renderData=t.createData(this),this._markForUpdateRenderData())},n._updateBatch=function(){this._cleanMaterialCache(),this._markForUpdateRenderData()},n._updateDebugDraw=function(){if(this.debugBones||this.debugSlots||this.debugMesh){if(!this._debugRenderer){var t=new P("DEBUG_DRAW_NODE");t.layer=this.node.layer,t.hideFlags|=w.DontSave|w.HideInHierarchy;var e=null;try{(e=t.addComponent("cc.Graphics")).lineWidth=5,e.strokeColor=new u(255,0,0,255),this._debugRenderer=e,t.parent=this.node,this.node.on(H.LAYER_CHANGED,this._applyLayer,this)}catch(e){N(4501,e.message),t.destroy(),t=null}}this.isAnimationCached()?M(16418):this._instance.setDebugMode(!0)}else this._debugRenderer&&(this.node.off(H.LAYER_CHANGED,this._applyLayer,this),this._debugRenderer.node.destroy(),this._debugRenderer=null,this.isAnimationCached()||this._instance&&this._instance.setDebugMode(!1))},n._updateUITransform=function(){var t=this.node._getUITransformComp(),e=this._runtimeData;if(!e)return t.setContentSize(100,100),t.anchorX=.5,void(t.anchorX=.5);var n=e.width,i=e.height;n&&i&&(t.setContentSize(n,i),0!==n&&(t.anchorX=Math.abs(e.x)/n),0!==i&&(t.anchorY=Math.abs(e.y)/i))},n._updateColor=function(){var t=this,e=t.node._uiProps,n=t._tempColor,i=t._color,s=t.node.parent?t.node.parent._uiProps.opacity:1,a=e.localOpacity*s*i.a/255;if(n.r!==i.r||n.g!==i.g||n.b!==i.b||n.a!==a){e.colorDirty=!0,n.r=i.r,n.g=i.g,n.b=i.b,n.a=a;var r=i.r/255,o=i.g/255,h=i.b/255;this._instance.setColor(r,o,h,a)}},n.setVertexEffectDelegate=function(t){if(this._instance)if(t){var e=null==t?void 0:t.getEffectType();if("jitter"===e){var n=null==t?void 0:t.getJitterVertexEffect();this._instance.setJitterEffect(n)}else if("swirl"===e){var i=null==t?void 0:t.getJitterVertexEffect();this._instance.setSwirlEffect(i)}}else this._instance.clearEffect()},n._ensureListener=function(){this._listener||(this._listener=new Lt,this._eventListenerID=Lt.addListener(this._listener),this._instance.setListener(this._eventListenerID))},n.setStartListener=function(t){this._ensureListener(),this._listener.start=t},n.setInterruptListener=function(t){this._ensureListener(),this._listener.interrupt=t},n.setEndListener=function(t){this._ensureListener(),this._listener.end=t},n.setDisposeListener=function(t){this._ensureListener(),this._listener.dispose=t},n.setCompleteListener=function(t){this._ensureListener(),this._listener.complete=t},n.setEventListener=function(t){this._ensureListener(),this._listener.event=t},n.setTrackStartListener=function(t,e){Lt.getListeners(t,this._instance).start=e},n.setTrackInterruptListener=function(t,e){Lt.getListeners(t,this._instance).interrupt=e},n.setTrackEndListener=function(t,e){Lt.getListeners(t,this._instance).end=e},n.setTrackDisposeListener=function(t,e){Lt.getListeners(t,this._instance).dispose=e},n.setTrackCompleteListener=function(t,e){Lt.getListeners(t,this._instance).complete=function(t){var n=Math.floor(t.trackTime/t.animationEnd);e(t,n)}},n.setTrackEventListener=function(t,e){Lt.getListeners(t,this._instance).event=e},n.getDebugShapes=function(){return this._instance.getDebugShapes()},n.setSlotTexture=function(t,e,n){if(this.isAnimationCached())g("Cached mode can't change texture of slot");else if(this.findSlot(t)){var i=e.width,s=e.height,a=n||!1;this._instance.resizeSlotRegion(t,i,s,a);var r=e.uuid;r||(r=e.getId()),this._instance.setSlotTexture(t,r),this._slotTextures||(this._slotTextures=new Map),this._slotTextures.set(r,e)}else g("No slot named:"+t)},n._destroySkeletonInfo=function(t){t&&this._skeletonInfo&&(t.destroySkeleton(this._skeletonInfo.assetUUID),this._skeletonInfo=null)},n._applyLayer=function(){this._debugRenderer&&(this._debugRenderer.node.layer=this.node.layer)},y(e,[{key:"drawList",get:function(){return this._drawList}},{key:"skeletonData",get:function(){return this._skeletonData},set:function(t){t&&t.resetEnums(),this._skeletonData!==t&&(this.destroyRenderData(),this._skeletonData=t,this.defaultSkin="",this.defaultAnimation="",this._animationName="",this._skinName="",this._updateSkeletonData(),this._updateUITransform())}},{key:"_defaultSkinIndex",get:function(){if(this.skeletonData){var t=this.skeletonData.getSkinsEnum();if(t)if(""===this.defaultSkin){if(t.hasOwnProperty(0))return this._defaultSkinIndex=0,0}else{var e=t[this.defaultSkin];if(void 0!==e)return e}}return 0},set:function(t){var e;if(this.skeletonData&&(e=this.skeletonData.getSkinsEnum()),e){var n=e[t];void 0!==n?(this.defaultSkin=String(n),this.setSkin(this.defaultSkin),this._refreshInspector(),this._markForUpdateRenderData()):g(this.name+" skin enums are invalid")}else g(this.name+" skin enums are invalid")}},{key:"_animationIndex",get:function(){var t=this.animation;if(this.skeletonData)if(t){var e=this.skeletonData.getAnimsEnum();if(e){var n=e[t];if(void 0!==n)return n}}else this._refreshInspector();return 0},set:function(t){var e;if(this.skeletonData&&(e=this.skeletonData.getAnimsEnum()),e){var n=String(e[t]);void 0!==n?(this.animation=n,this.animation=n):g(this.name+" animation enums are invalid")}else g(this.name+" animation enums are invalid")}},{key:"defaultCacheMode",get:function(){return this._cacheMode},set:function(t){this._cacheMode=t,this.setAnimationCacheMode(this._cacheMode)}},{key:"premultipliedAlpha",get:function(){return this._premultipliedAlpha},set:function(t){t!==this._premultipliedAlpha&&(this._premultipliedAlpha=t,this._instance.setPremultipliedAlpha(t),this._markForUpdateRenderData())}},{key:"timeScale",get:function(){return this._timeScale},set:function(t){t!==this._timeScale&&(this._timeScale=t,this._instance&&(this._instance.dtRate=1*this._timeScale))}},{key:"useTint",get:function(){return this._useTint},set:function(t){t!==this._useTint&&(this._useTint=t,this._updateUseTint())}},{key:"enableBatch",get:function(){return this._enableBatch},set:function(t){t!==this._enableBatch&&(this._enableBatch=t,this._updateBatch())}},{key:"sockets",get:function(){return this._sockets},set:function(t){this._sockets=t,this._updateSocketBindings(),this.attachUtil.init(this)}},{key:"debugSlots",get:function(){return this._debugSlots},set:function(t){t!==this._debugSlots&&(this._debugSlots=t,this._updateDebugDraw(),this._markForUpdateRenderData())}},{key:"debugBones",get:function(){return this._debugBones},set:function(t){t!==this._debugBones&&(this._debugBones=t,this._updateDebugDraw(),this._markForUpdateRenderData())}},{key:"debugMesh",get:function(){return this._debugMesh},set:function(t){t!==this._debugMesh&&(this._debugMesh=t,this._updateDebugDraw(),this._markForUpdateRenderData())}},{key:"socketNodes",get:function(){return this._socketNodes}},{key:"animation",get:function(){return this._animationName},set:function(t){t?this.setAnimation(0,t,this.loop):this.clearAnimation(0)}},{key:"customMaterial",get:function(){return this._customMaterial},set:function(t){this._customMaterial=t,this.updateMaterial(),this._markForUpdateRenderData()}},{key:"customMaterialInstance",get:function(){if(!this._customMaterial)return null;if(!this._customMaterialInstance){var t={parent:this._customMaterial,subModelIdx:0,owner:this};this._customMaterialInstance=new j(t)}return this._customMaterialInstance}}]),e}(q),xe.SpineSocket=Fe,xe.AnimationCacheMode=we,pe=a((me=xe).prototype,"_skeletonData",[r],(function(){return null})),ve=a(me.prototype,"defaultSkin",[r],(function(){return""})),ke=a(me.prototype,"defaultAnimation",[r],(function(){return""})),ge=a(me.prototype,"_premultipliedAlpha",[r],(function(){return!0})),Ce=a(me.prototype,"_timeScale",[r],(function(){return 1})),ye=a(me.prototype,"_preCacheMode",[r],(function(){return we.UNSET})),Se=a(me.prototype,"_cacheMode",[r],(function(){return we.REALTIME})),Ee=a(me.prototype,"_sockets",[r],(function(){return[]})),Ae=a(me.prototype,"_useTint",[r],(function(){return!1})),De=a(me.prototype,"_debugMesh",[r],(function(){return!1})),Te=a(me.prototype,"_debugBones",[r],(function(){return!1})),be=a(me.prototype,"_debugSlots",[r],(function(){return!1})),Me=a(me.prototype,"_enableBatch",[r],(function(){return!1})),R(me.prototype,"skeletonData",[he],Object.getOwnPropertyDescriptor(me.prototype,"skeletonData"),me.prototype),R(me.prototype,"_defaultSkinIndex",[le],Object.getOwnPropertyDescriptor(me.prototype,"_defaultSkinIndex"),me.prototype),R(me.prototype,"_animationIndex",[ue],Object.getOwnPropertyDescriptor(me.prototype,"_animationIndex"),me.prototype),R(me.prototype,"defaultCacheMode",[ce],Object.getOwnPropertyDescriptor(me.prototype,"defaultCacheMode"),me.prototype),Ie=a(me.prototype,"loop",[r],(function(){return!0})),R(me.prototype,"sockets",[de],Object.getOwnPropertyDescriptor(me.prototype,"sockets"),me.prototype),R(me.prototype,"customMaterial",[c,_e],Object.getOwnPropertyDescriptor(me.prototype,"customMaterial"),me.prototype),fe=me))||fe);n.internal.SpineSkeleton=je;var ze,He,Ve,We,Ye=function(){function t(){this.name="sp.VertexEffectDelegate",this._vertexEffect=void 0,this._interpolation=void 0,this._effectType=void 0,this._vertexEffect=null,this._interpolation=null,this._effectType="none"}var e=t.prototype;return e.clear=function(){this._vertexEffect=null,this._interpolation=null,this._effectType="none"},e.initJitter=function(t,e){return this._effectType="jitter",this._vertexEffect=new et.JitterEffect(t,e),this._vertexEffect},e.initSwirlWithPow=function(t,e){return this._effectType="swirl",this._interpolation=new et.Pow(e),this._vertexEffect=new et.SwirlEffect(t,this._interpolation),this._vertexEffect},e.initSwirlWithPowOut=function(t,e){return this._effectType="swirl",this._interpolation=new et.PowOut(e),this._vertexEffect=new et.SwirlEffect(t,this._interpolation),this._vertexEffect},e.getJitterVertexEffect=function(){return this._vertexEffect},e.getSwirlVertexEffect=function(){return this._vertexEffect},e.getVertexEffect=function(){return this._vertexEffect},e.getEffectType=function(){return this._effectType},t}(),Je=new u(0,0,255,255),Xe=new u(255,0,0,255),qe=new u(0,255,0,255),Ge=new u(255,255,0,255),Qe=null,Ke=null,Ze=!1,$e=!1,tn=V(W),en=V(Y),nn=new d(0,0,0);function sn(t,e){var n,i;switch(t){case 1:n=Ze?$.ONE:$.SRC_ALPHA,i=$.ONE;break;case 2:n=$.DST_COLOR,i=$.ONE_MINUS_SRC_ALPHA;break;case 3:n=Ze?$.ONE:$.SRC_ALPHA,i=$.ONE_MINUS_SRC_COLOR;break;default:n=Ze?$.ONE:$.SRC_ALPHA,i=$.ONE_MINUS_SRC_ALPHA}return e.getMaterialForBlendAndTint(n,i,$e?Be.TWO_COLORED:Be.COLORED_TEXTURED)}var an=new(function(){function t(){this.vCount=32767}var e=t.prototype;return e.ensureAccessor=function(t){var e=t?Ke:Qe;if(!e){var n=m.root.device,i=m.root.batcher2D,s=t?Y:W;t?(e=Ke=new G(n,s,this.vCount),i.registerBufferAccessor(Number.parseInt("SPINETINT",36),Ke)):(e=Qe=new G(n,s,this.vCount),i.registerBufferAccessor(Number.parseInt("SPINE",36),Qe))}return e},e.createData=function(t){var e=t.renderData;if(!e){var n=t.useTint||t.isAnimationCached(),i=this.ensureAccessor(n);e=Q.add(n?Y:W,i)}return e},e.updateRenderData=function(t){var e;t._skeleton&&t.node.active&&null!=(e=t.skeletonData)&&e.isValid&&rn(t)},t}());function rn(t){if(t.drawList.reset(),0!==t.color.a){t._updateColor(),Ze=t.premultipliedAlpha,$e=t.useTint||t.isAnimationCached(),t.isAnimationCached()?hn(t):on(t);var e=t.renderData,n=$e?Ke:Qe;t.syncAttachedNode(),(e.vertexCount>0||e.indexCount>0)&&n.getMeshBuffer(e.chunk.bufferId).setDirty()}}function on(t){var e,n,i=(t.useTint?en:tn)/Float32Array.BYTES_PER_ELEMENT,s=t.updateRenderData(),a=s.vCount,r=s.iCount;if(!(a<1||r<1)){var o=t.renderData;o.vertexCount===a&&o.indexCount===r||(o.resize(a,r),o.indices=new Uint16Array(r),t._vLength=a*Float32Array.BYTES_PER_ELEMENT*i,t._vBuffer=new Uint8Array(o.chunk.vb.buffer,o.chunk.vb.byteOffset,Float32Array.BYTES_PER_ELEMENT*o.chunk.vb.length),t._iLength=Uint16Array.BYTES_PER_ELEMENT*r,t._iBuffer=new Uint8Array(o.indices.buffer));var h=o.chunk.vb,l=s.vPtr,u=s.iPtr,c=o.indices,d=et.wasmUtil.wasm.HEAPU8;null==(e=t._vBuffer)||e.set(d.subarray(l,l+t._vLength),0),null==(n=t._iBuffer)||n.set(d.subarray(u,u+t._iLength),0);for(var _=o.chunk.vertexOffset,f=0;f<r;f++)c[f]+=_;for(var m=s.getData(),p=s.getTextures(),v=m.size(),k=0,g=0,C=0;C<v;C+=5){g=m.get(C+3);var y=sn(m.get(C+4),t);t.requestDrawData(y,p.get(C/5),k,g),k+=g}if(t.enableBatch)for(var S=t.node.worldMatrix,E=0,A=0;A<a;A++)E=A*i,nn.x=h[E],nn.y=h[E+1],nn.z=0,nn.transformMat4(S),h[E]=nn.x,h[E+1]=nn.y,h[E+2]=nn.z;var D=t._debugRenderer,T=t._skeleton;if(D&&(t.debugBones||t.debugSlots||t.debugMesh)){D.clear();for(var b=t.getDebugShapes(),M=b.size(),I=0;I<M;I++){var x=b.get(I);if(0===x.type&&t.debugSlots){D.strokeColor=Je;var w=x.vOffset*i,N=x.vCount*i;D.moveTo(h[w],h[w+1]);for(var R=w+i,U=w+N;R<U;R+=i)D.lineTo(h[R],h[R+1]);D.close(),D.stroke()}else if(1===x.type&&t.debugMesh){D.strokeColor=Ge;for(var L=x.iCount,P=x.iOffset,B=P,O=P+L;B<O;B+=3){var F=c[B]*i,j=c[B+1]*i,z=c[B+2]*i;D.moveTo(h[F],h[F+1]),D.lineTo(h[j],h[j+1]),D.lineTo(h[z],h[z+1]),D.close(),D.stroke()}}}if(t.debugBones){D.strokeColor=Xe,D.fillColor=Je;for(var H=0,V=T.bones.length;H<V;H++){var W=T.bones[H],Y=W.data.length*W.a+W.worldX,J=W.data.length*W.c+W.worldY;D.moveTo(W.worldX,W.worldY),D.lineTo(Y,J),D.stroke(),D.circle(W.worldX,W.worldY,1.5*Math.PI),D.fill(),0===H&&(D.fillColor=qe)}}}}}function hn(t){var e=t.updateRenderData();if(e){var n=e.vCount,i=e.iCount;if(!(n<1||i<1)){var s=t.renderData;s.vertexCount===n&&s.indexCount===i||(s.resize(n,i),s.indices=new Uint16Array(i));var a=s.chunk.vb,r=new Uint8Array(a.buffer,a.byteOffset,Float32Array.BYTES_PER_ELEMENT*a.length);r.set(e.vData);var o=t.color,h=t.node._uiProps.opacity;if(1-h>_||4294967295!==u.toUint32(o)||Ze){ze=o.r/255,He=o.g/255,Ve=o.b/255,We=h;for(var l=0;l<n;l++){var c=l*en+5*Float32Array.BYTES_PER_ELEMENT,d=r[c],f=r[c+1],m=r[c+2],p=r[c+3]*We,v=Ze?p/255:1;r[c]=Math.floor(v*d*ze),r[c+1]=Math.floor(v*f*He),r[c+2]=Math.floor(v*m*Ve),r[c+3]=Math.floor(p),r[c+4]=Math.floor(r[c+4]*ze),r[c+5]=Math.floor(r[c+5]*He),r[c+6]=Math.floor(r[c+6]*Ve),r[c+7]=Ze?255:0}}var k=s.indices;k.set(e.iData);for(var g=s.chunk.vertexOffset,C=0;C<i;C++)k[C]+=g;for(var y=e.meshes,S=y.length,E=0,A=0,D=0;D<S;D++){var T=y[D],b=sn(T.blendMode,t),M=T.textureID;A=T.iCount,t.requestDrawData(b,M,E,A),E+=A}var I=en/Float32Array.BYTES_PER_ELEMENT;if(t.enableBatch)for(var x=t.node.worldMatrix,w=0,N=0;N<n;N++)w=N*I,nn.x=a[w],nn.y=a[w+1],nn.z=0,nn.transformMat4(x),a[w]=nn.x,a[w+1]=nn.y,a[w+2]=nn.z}}}n.internal.SpineAssembler=an;var ln,un,cn={getAssembler:function(){return an}};function dn(){return Promise.resolve()}je.Assembler=cn,function(t){t[t.REGION=0]="REGION",t[t.BOUNDING_BOX=1]="BOUNDING_BOX",t[t.MESH=2]="MESH",t[t.SKINNED_MESH=3]="SKINNED_MESH"}(ln||(ln={})),T(ln),function(t){t[t.START=0]="START",t[t.INTERRUPT=1]="INTERRUPT",t[t.END=2]="END",t[t.DISPOSE=3]="DISPOSE",t[t.COMPLETE=4]="COMPLETE",t[t.EVENT=5]="EVENT"}(un||(un={})),T(un),t("sp",Object.freeze({__proto__:null,get ATTACHMENT_TYPE(){return ln},AnimationCacheMode:Pe,get AnimationEventType(){return un},DefaultAnimsEnum:Oe,get DefaultSkinsEnum(){return Ue},SPINE_VERSION:"3.8",Skeleton:je,SkeletonData:$t,get SpineAnimationCacheMode(){return we},get SpineDefaultAnimsEnum(){return Le},get SpineMaterialType(){return Be},SpineSocket:Fe,VertexEffectDelegate:Ye,isBinaryCompatible:function(){return!1},isJsonCompatible:function(){return!1},loadWasmModuleSpine:dn,simpleSpineAssembler:cn,spine:et,timeScale:1}))}}}));
