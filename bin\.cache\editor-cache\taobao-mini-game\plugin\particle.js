System.register(["./gc-object-D18ulfCO.js","./index-Y4La_nfG.js","./global-exports-CLZKKIY2.js","./scene-ArUG4OfI.js","./component-CsuvAQKv.js","./factory-BOc5khhM.js","./deprecated-D5UVm7fE.js","./prefab-BQYc0LyR.js","./create-mesh-o_2FMF_K.js","./debug-view-BP17WHcy.js","./device-manager-B8lyUDPi.js","./buffer-barrier-q_79u36H.js","./deprecated-6ty78xzL.js","./deprecated-C_Nm0tQW.js","./camera-component-X7pwLmnP.js","./model-renderer-D7qfPDfZ.js","./renderer-CZheciPr.js","./3d.js","./director-8iUu7HD2.js","./mesh-Ba1cTOGw.js","./util-K-7Ucy5K.js","./skeleton-BrWwQslM.js","./instantiate-BgGIVvKs.js","./pipeline-state-manager-DQyhxoC_.js","./node-event-DTNosVQv.js","./touch-B157r-vS.js","./mesh-renderer-CTCVb-Pf.js","./deprecated-CuuzltLj.js","./deprecated-Bf8XgTPJ.js","./wasm-minigame-DBi57dFz.js","./zlib.min-CyXMsivM.js","./capsule-DyZvzyLy.js"],(function(t){"use strict";var e,i,r,s,n,o,a,l,u,h,c,_,d,p,f,m,v,y,M,g,S,b,x,T,w,A,O,R,F,C,E,z,D,P,I,B,L,V,U,k,N,G,H,X,W,j,Y,Z,q,Q,K,J,$,tt,et,it,rt,st,nt,ot,at,lt,ut,ht,ct,_t,dt,pt,ft,mt,vt,yt,Mt,gt,St,bt,xt,Tt,wt,At,Ot,Rt,Ft,Ct,Et,zt,Dt,Pt,It,Bt;return{setters:[function(t){e=t._,i=t.a,r=t.b,s=t.ac,n=t.E,o=t.ah,a=t.aI,l=t.K,u=t.F,h=t.o,c=t.a5,_=t.h,d=t.w,p=t.P,f=t.aJ,m=t.n,v=t.l,y=t.C,M=t.R,g=t.j},function(t){S=t.c,b=t.t,x=t.e,T=t.C,w=t.a,A=t.X,O=t.f,R=t.s,F=t.b,C=t.ab,E=t.$,z=t.b7,D=t.aE,P=t.L,I=t.b8,B=t.aN,L=t.H,V=t.V,U=t.Q,k=t.M,N=t.ad,G=t.W,H=t.ae,X=t.af,W=t.ai,j=t.j,Y=t.aj,Z=t.g,q=t.q,Q=t.F,K=t.n,J=t.i,$=t.h,tt=t.r},function(t){et=t.c,it=t.b},function(t){rt=t.b,st=t.d,nt=t.h,ot=t.M,at=t.y,lt=t.T,ut=t.N},function(t){ht=t.C},function(t){ct=t.I,_t=t.P,dt=t.T,pt=t.W},function(t){ft=t.M,mt=t.d},null,function(t){vt=t._},function(t){yt=t.g},function(t){Mt=t.d},function(t){gt=t.P,St=t.A,bt=t.a,xt=t.F,Tt=t.c,wt=t.B,At=t.b,Ot=t.M,Rt=t.a7,Ft=t.r,Ct=t.o},null,null,null,function(t){Et=t.M},function(t){zt=t.R},null,function(t){Dt=t.d,Pt=t.D},function(t){It=t.M},null,null,function(t){Bt=t.i},null,null,null,null,null,null,null,null,null],execute:function(){var Lt,Vt,Ut,kt,Nt,Gt,Ht,Xt,Wt,jt,Yt=(Lt=S("cc.Billboard"),Vt=b(nt),Ut=b(nt),Lt((Nt=function(t){e(s,t);var r=s.prototype;function s(){var e;return(e=t.call(this)||this)._texture=Gt&&Gt(),e._height=Ht&&Ht(),e._width=Xt&&Xt(),e._rotation=Wt&&Wt(),e._techIndex=jt&&jt(),e._model=null,e._mesh=null,e._material=null,e._uniform=new x(1,1,0,0),e}return r.updateTexture=function(){this._material&&this._material.setProperty("mainTexture",this._texture)},r.updateHeight=function(){this._material&&(this._uniform.y=this._height,this._material.setProperty("cc_size_rotation",this._uniform))},r.updateWidth=function(){this._material&&(this._uniform.x=this._width,this._material.setProperty("cc_size_rotation",this._uniform))},r.updateRotation=function(){this._material&&(this._uniform.z=this._rotation,this._material.setProperty("cc_size_rotation",this._uniform))},r.updateTechnique=function(){this._model&&this._mesh&&this._material&&this._material.technique!==this._techIndex&&(this.detachFromScene(),this._model.destroy(),this._model=null,this._material.destroy(),this._material=null,this._mesh.destroy(),this._mesh=null,this.createModel(),this.updateWidth(),this.updateHeight(),this.updateRotation(),this.updateTexture(),this.enabled?(this.attachToScene(),this._model.enabled=!0):this._model.enabled=!1)},r.onLoad=function(){this.createModel()},r.onEnable=function(){this.attachToScene(),this._model.enabled=!0,this.updateWidth(),this.updateHeight(),this.updateRotation(),this.updateTexture(),this.updateTechnique()},r.onDisable=function(){this.detachFromScene()},r.attachToScene=function(){this._model&&this.node&&this.node.scene&&(this._model.scene&&this.detachFromScene(),this._getRenderScene().addModel(this._model))},r.detachFromScene=function(){this._model&&this._model.scene&&this._model.scene.removeModel(this._model)},r.createModel=function(){this._mesh=vt({primitiveMode:gt.TRIANGLE_LIST,positions:[0,0,0,0,0,0,0,0,0,0,0,0],uvs:[0,0,1,0,0,1,1,1],colors:[T.WHITE.r,T.WHITE.g,T.WHITE.b,T.WHITE.a,T.WHITE.r,T.WHITE.g,T.WHITE.b,T.WHITE.a,T.WHITE.r,T.WHITE.g,T.WHITE.b,T.WHITE.a,T.WHITE.r,T.WHITE.g,T.WHITE.b,T.WHITE.a],attributes:[new St(bt.ATTR_POSITION,xt.RGB32F),new St(bt.ATTR_TEX_COORD,xt.RG32F),new St(bt.ATTR_COLOR,xt.RGBA8UI,!0)],indices:[0,1,2,1,2,3]},void 0,{calculateBounds:!1});var t=this._model=et.director.root.createModel(ft);t.node=t.transform=this.node,null==this._material&&(this._material=new rt,this._material.copy(st.get("default-billboard-material"),{technique:this._techIndex})),t.initSubModel(0,this._mesh.renderingSubMeshes[0],this._material)},i(s,[{key:"texture",get:function(){return this._texture},set:function(t){this._texture=t,this.updateTexture()}},{key:"height",get:function(){return this._height},set:function(t){this._height=t,this.updateHeight()}},{key:"width",get:function(){return this._width},set:function(t){this._width=t,this.updateWidth()}},{key:"rotation",get:function(){return Math.round(100*A(this._rotation))/100},set:function(t){this._rotation=O(t),this.updateRotation()}},{key:"technique",get:function(){return this._techIndex},set:function(t){var e,i;t=Math.floor(t);var r=null==(e=this._material)||null==(i=e.effectAsset)?void 0:i.techniques;r&&t>=r.length&&(t=r.length-1),t<0&&(t=0),this._techIndex=t,this.updateTechnique()}}]),s}(ht),Gt=w(Nt.prototype,"_texture",[Vt],(function(){return null})),r(Nt.prototype,"texture",[Ut],Object.getOwnPropertyDescriptor(Nt.prototype,"texture"),Nt.prototype),Ht=w(Nt.prototype,"_height",[R],(function(){return 0})),Xt=w(Nt.prototype,"_width",[R],(function(){return 0})),Wt=w(Nt.prototype,"_rotation",[R],(function(){return 0})),jt=w(Nt.prototype,"_techIndex",[R],(function(){return 0})),kt=Nt))||kt);t({Billboard:Yt,BillboardComponent:Yt});var Zt,qt,Qt,Kt,Jt,$t,te,ee,ie,re,se,ne,oe,ae,le,ue,he,ce,_e=[new St(bt.ATTR_POSITION,xt.RGB32F),new St(bt.ATTR_TEX_COORD,xt.RGBA32F),new St(bt.ATTR_TEX_COORD1,xt.RGB32F),new St(bt.ATTR_COLOR,xt.RGBA8,!0)],de=new F,pe=new F,fe=function(t){function i(){var e;return(e=t.call(this)||this)._capacity=void 0,e._vertSize=0,e._vBuffer=null,e._vertAttrsFloatCount=0,e._vdataF32=null,e._vdataUint32=null,e._subMeshData=null,e._vertCount=0,e._indexCount=0,e._material=null,e._iaVertCount=0,e._iaIndexCount=0,e.type=mt.LINE,e._capacity=100,e}e(i,t);var r=i.prototype;return r.setCapacity=function(t){this._capacity=t,this.createBuffer()},r.createBuffer=function(){this._vertSize=0;for(var t=0,e=_e;t<e.length;t++){var i=e[t];i.offset=this._vertSize,this._vertSize+=Tt[i.format].size}this._vertAttrsFloatCount=this._vertSize/4,this._vBuffer=this.createSubMeshData(),this._vdataF32=new Float32Array(this._vBuffer),this._vdataUint32=new Uint32Array(this._vBuffer)},r.updateMaterial=function(e){this._material=e,t.prototype.setSubModelMaterial.call(this,0,e)},r.createSubMeshData=function(){this._subMeshData&&this.destroySubMeshData(),this._vertCount=2,this._indexCount=6;var t=this._device.createBuffer(new wt(At.VERTEX|At.TRANSFER_DST,Ot.DEVICE,this._vertSize*this._capacity*this._vertCount,this._vertSize)),e=new ArrayBuffer(this._vertSize*this._capacity*this._vertCount);t.update(e);for(var i=new Uint16Array((this._capacity-1)*this._indexCount),r=0,s=0;s<this._capacity-1;++s){var n=2*s;i[r++]=n,i[r++]=n+1,i[r++]=n+2,i[r++]=n+3,i[r++]=n+2,i[r++]=n+1}var o=this._device.createBuffer(new wt(At.INDEX|At.TRANSFER_DST,Ot.DEVICE,(this._capacity-1)*this._indexCount*Uint16Array.BYTES_PER_ELEMENT,Uint16Array.BYTES_PER_ELEMENT));return o.update(i),this._iaVertCount=this._capacity*this._vertCount,this._iaIndexCount=(this._capacity-1)*this._indexCount,this._subMeshData=new yt([t],_e,gt.TRIANGLE_LIST,o),this.initSubModel(0,this._subMeshData,this._material),e},r.addLineVertexData=function(t,e,i){if(t.length>1){var r=0;F.subtract(de,t[1],t[0]),this._vdataF32[r++]=t[0].x,this._vdataF32[r++]=t[0].y,this._vdataF32[r++]=t[0].z,this._vdataF32[r++]=0,this._vdataF32[r++]=e.evaluate(0,1),this._vdataF32[r++]=0,this._vdataF32[r++]=0,this._vdataF32[r++]=de.x,this._vdataF32[r++]=de.y,this._vdataF32[r++]=de.z,this._vdataUint32[r++]=T.toUint32(i.evaluate(0,1)),this._vdataF32[r++]=t[0].x,this._vdataF32[r++]=t[0].y,this._vdataF32[r++]=t[0].z,this._vdataF32[r++]=1,this._vdataF32[r++]=e.evaluate(0,1),this._vdataF32[r++]=0,this._vdataF32[r++]=1,this._vdataF32[r++]=de.x,this._vdataF32[r++]=de.y,this._vdataF32[r++]=de.z,this._vdataUint32[r++]=T.toUint32(i.evaluate(0,1));for(var s=1;s<t.length-1;s++){F.subtract(de,t[s-1],t[s]),F.subtract(pe,t[s+1],t[s]),F.subtract(pe,pe,de);var n=s/t.length;this._vdataF32[r++]=t[s].x,this._vdataF32[r++]=t[s].y,this._vdataF32[r++]=t[s].z,this._vdataF32[r++]=0,this._vdataF32[r++]=e.evaluate(n,1),this._vdataF32[r++]=n,this._vdataF32[r++]=0,this._vdataF32[r++]=pe.x,this._vdataF32[r++]=pe.y,this._vdataF32[r++]=pe.z,this._vdataUint32[r++]=T.toUint32(i.evaluate(n,1)),this._vdataF32[r++]=t[s].x,this._vdataF32[r++]=t[s].y,this._vdataF32[r++]=t[s].z,this._vdataF32[r++]=1,this._vdataF32[r++]=e.evaluate(n,1),this._vdataF32[r++]=n,this._vdataF32[r++]=1,this._vdataF32[r++]=pe.x,this._vdataF32[r++]=pe.y,this._vdataF32[r++]=pe.z,this._vdataUint32[r++]=T.toUint32(i.evaluate(n,1))}F.subtract(de,t[t.length-1],t[t.length-2]),this._vdataF32[r++]=t[t.length-1].x,this._vdataF32[r++]=t[t.length-1].y,this._vdataF32[r++]=t[t.length-1].z,this._vdataF32[r++]=0,this._vdataF32[r++]=e.evaluate(1,1),this._vdataF32[r++]=1,this._vdataF32[r++]=0,this._vdataF32[r++]=de.x,this._vdataF32[r++]=de.y,this._vdataF32[r++]=de.z,this._vdataUint32[r++]=T.toUint32(i.evaluate(1,1)),this._vdataF32[r++]=t[t.length-1].x,this._vdataF32[r++]=t[t.length-1].y,this._vdataF32[r++]=t[t.length-1].z,this._vdataF32[r++]=1,this._vdataF32[r++]=e.evaluate(1,1),this._vdataF32[r++]=1,this._vdataF32[r++]=1,this._vdataF32[r++]=de.x,this._vdataF32[r++]=de.y,this._vdataF32[r++]=de.z,this._vdataUint32[r++]=T.toUint32(i.evaluate(1,1))}this.updateIA(Math.max(0,t.length-1))},r.updateIA=function(t){var e=this._subModels[0].inputAssembler;e.vertexBuffers[0].update(this._vdataF32),e.firstIndex=0,e.indexCount=this._indexCount*t,e.vertexCount=this._iaVertCount},r.destroySubMeshData=function(){this._subMeshData&&(this._subMeshData.destroy(),this._subMeshData=null)},i}(ft),me=s.Attr.setClassAttr,ve=[["mode","constant","multiplier"],["mode","spline","multiplier"],["mode","splineMin","splineMax","multiplier"],["mode","constantMin","constantMax","multiplier"]],ye=n({Constant:0,Curve:1,TwoCurves:2,TwoConstants:3}),Me=t("CurveRange",S("cc.CurveRange")((qt=function(){function t(){this.constant=0,this.constantMin=0,this.constantMax=0,this.multiplier=1,this._mode=ye.Constant}var e=t.prototype;return e.evaluate=function(t,e){switch(this._mode){default:case ye.Constant:return this.constant;case ye.Curve:return this.spline.evaluate(t)*this.multiplier;case ye.TwoCurves:return C(this.splineMin.evaluate(t),this.splineMax.evaluate(t),e)*this.multiplier;case ye.TwoConstants:return C(this.constantMin,this.constantMax,e)}},e.getMax=function(){switch(this._mode){default:case ye.Constant:return this.constant;case ye.Curve:return this.multiplier;case ye.TwoConstants:return this.constantMax;case ye.TwoCurves:return this.multiplier}},e.isZero=function(){switch(this._mode){default:case ye.Constant:return E(this.constant,0,P);case ye.Curve:return E(this.multiplier,0,P);case ye.TwoConstants:return E(Math.max(Math.abs(this.constantMax),Math.abs(this.constantMin)),0,P);case ye.TwoCurves:return E(this.multiplier,0,P)}},e._onBeforeSerialize=function(){return ve[this._mode]},i(t,[{key:"mode",get:function(){return this._mode},set:function(t){switch(this._mode=t,t){case ye.Constant:case ye.TwoConstants:break;case ye.Curve:this.spline||(this.spline=z());break;case ye.TwoCurves:this.splineMax||(this.splineMax=z()),this.splineMin||(this.splineMin=z())}}},{key:"curve",get:function(){var t;return null!==(t=this._curve)&&void 0!==t?t:this._curve=new I(this.spline)},set:function(t){this._curve=t,this.spline=t._internalCurve}},{key:"curveMin",get:function(){var t;return null!==(t=this._curveMin)&&void 0!==t?t:this._curveMin=new I(this.splineMin)},set:function(t){this._curveMin=t,this.splineMin=t._internalCurve}},{key:"curveMax",get:function(){var t;return null!==(t=this._curveMax)&&void 0!==t?t:this._curveMax=new I(this.splineMax)},set:function(t){this._curveMax=t,this.splineMax=t._internalCurve}}]),t}(),qt.Mode=ye,Zt=qt))||Zt);function ge(t,e,i){switch(t.mode){case ye.Constant:return t.constant;case ye.Curve:return t.spline.evaluate(e)*t.multiplier;case ye.TwoCurves:return 0===i?t.splineMin.evaluate(e)*t.multiplier:t.splineMax.evaluate(e)*t.multiplier;case ye.TwoConstants:return 0===i?t.constantMin:t.constantMax;default:return 0}}function Se(t){switch(t.mode){case ye.TwoConstants:case ye.TwoCurves:return 2;default:return 1}}function be(t,e,i){var r=new ct({width:e,height:i,_data:t,_compressed:!1,format:_t.RGBA32F}),s=new nt;return s.setFilters(dt.NEAREST,dt.NEAREST),s.setMipFilter(dt.NONE),s.setWrapMode(pt.CLAMP_TO_EDGE,pt.CLAMP_TO_EDGE,pt.CLAMP_TO_EDGE),s.image=r,s}function xe(t,e,i,r){return null===t||i!==t.width||r!==t.height?(t&&t.destroy(),t=be(e,i,r)):t.uploadData(e),t}function Te(t,e,i,r){var s=Se(r),n=i*s*4;null!==e&&e.length===n||(e=new Float32Array(i*s*4));for(var o=1/(i-1),a=0,l=0;l<s;l++)for(var u=0;u<i;u++){var h=ge(r,o*u,l);e[a+2]=h,a+=4}return{texture:xe(t,e,i,s),texdata:e}}function we(t,e,i,r){var s=Se(r),n=i*s*4;null!==e&&e.length===n||(e=new Float32Array(i*s*4));for(var o=1/(i-1),a=0,l=0,u=0;u<s;u++)for(var h=0;h<i;h++)a=ge(r,o*h,u),e[l]=a,e[l+1]=a,e[l+2]=a,l+=4;return{texture:xe(t,e,i,s),texdata:e}}function Ae(t,e,i,r,s){var n=Math.max(Se(r),Se(s)),o=i*n*4;null!==e&&e.length===o||(e=new Float32Array(i*n*4));for(var a=[r,s],l=1/(i-1),u=0;u<n;u++)for(var h=0;h<2;h++)for(var c=a[h],_=0,d=0;d<i;d++)_=ge(c,l*d,u),e[4*(u*i+d)+h]=_;return{texture:xe(t,e,i,n),texdata:e}}function Oe(t,e,i,r,s,n,o){var a=Math.max(Se(r),Se(s),Se(n)),l=i*a*4;null!==e&&e.length===l||(e=new Float32Array(i*a*4));for(var u=[r,s,n],h=1/(i-1),c=0;c<a;c++)for(var _=0;_<3;_++)for(var d=u[_],p=0,f=0,m=0;m<i;m++){var v=ge(d,h*m,c);f=o?v:(p+=v)/(m+1),e[4*(c*i+m)+_]=f}return{texture:xe(t,e,i,a),texdata:e}}function Re(t,e,i,r,s,n,o){var a=Math.max(Se(r),Se(s),Se(n),Se(o)),l=i*a*4;null!==e&&e.length===l||(e=new Float32Array(i*a*4));for(var u=[r,s,n,o],h=1/(i-1),c=0;c<a;c++)for(var _=0;_<4;_++)for(var d=u[_],p=0,f=0,m=0;m<i;m++)f=(p+=ge(d,h*m,c))/(m+1),e[4*(c*i+m)+_]=f;return{texture:xe(t,e,i,a),texdata:e}}s.fastDefine("cc.CurveRange",Me,{multiplier:1,constantMax:0,constantMin:0,constant:0,mode:ye.Constant,splineMax:Object.freeze(z()),splineMin:Object.freeze(z()),spline:Object.freeze(z())}),me(Me,"multiplier","visible",!0),me(Me,"constantMax","visible",!0),me(Me,"constantMin","visible",!0),me(Me,"constant","visible",!0),o(Me,"mode",ye),me(Me,"mode","visible",!0),me(Me,"splineMax","type","Object"),me(Me,"splineMax","ctor",D),me(Me,"splineMax","visible",!0),me(Me,"splineMin","type","Object"),me(Me,"splineMin","ctor",D),me(Me,"splineMin","visible",!0),me(Me,"spline","type","Object"),me(Me,"spline","ctor",D),me(Me,"spline","visible",!0);var Fe,Ce,Ee,ze,De,Pe,Ie,Be,Le,Ve,Ue,ke,Ne,Ge,He,Xe,We,je,Ye,Ze,qe=it,Qe=n({Color:0,Gradient:1,TwoColors:2,TwoGradients:3,RandomColor:4}),Ke=new T,Je=new T,$e=t("GradientRange",(Qt=S("cc.GradientRange"),Kt=b(Qe),Jt=b(B),$t=b(B),te=b(B),ee=b(Qe),Qt((ce=function(){function t(){this.color=se&&se(),this.colorMin=ne&&ne(),this.colorMax=oe&&oe(),this.gradient=ae&&ae(),this.gradientMin=le&&le(),this.gradientMax=ue&&ue(),this._mode=he&&he(),this._color=T.WHITE.clone()}var e=t.prototype;return e.evaluate=function(t,e){switch(this._mode){case Qe.Color:return this.color;case Qe.TwoColors:return T.lerp(this._color,this.colorMin,this.colorMax,e),this._color;case Qe.RandomColor:return this.gradient.getRandomColor(this._color);case Qe.Gradient:return this.gradient.evaluateFast(this._color,t);case Qe.TwoGradients:return T.lerp(this._color,this.gradientMin.evaluateFast(Ke,t),this.gradientMax.evaluateFast(Je,t),e),this._color;default:return this.color}},e._onBeforeSerialize=function(){return qe[this._mode]},i(t,[{key:"mode",get:function(){return this._mode},set:function(t){this._mode=t}}]),t}(),ce.Mode=Qe,r((re=ce).prototype,"mode",[Kt],Object.getOwnPropertyDescriptor(re.prototype,"mode"),re.prototype),se=w(re.prototype,"color",[R],(function(){return T.WHITE.clone()})),ne=w(re.prototype,"colorMin",[R],(function(){return T.WHITE.clone()})),oe=w(re.prototype,"colorMax",[R],(function(){return T.WHITE.clone()})),ae=w(re.prototype,"gradient",[Jt],(function(){return new B})),le=w(re.prototype,"gradientMin",[$t],(function(){return new B})),ue=w(re.prototype,"gradientMax",[te],(function(){return new B})),he=w(re.prototype,"_mode",[ee],(function(){return Qe.Color})),ie=re))||ie));function ti(t,e,i){switch(t.mode){case Qe.Color:return t.color;case Qe.TwoColors:return 0===i?t.colorMin:t.colorMax;case Qe.RandomColor:return t.gradient.getRandomColor(Ke);case Qe.Gradient:return t.gradient.evaluateFast(Ke,e);case Qe.TwoGradients:return 0===i?t.gradientMin.evaluateFast(Ke,e):t.gradientMax.evaluateFast(Ke,e);default:return t.color}}function ei(t){switch(t.mode){case Qe.TwoColors:case Qe.TwoGradients:return 2;default:return 1}}function ii(t,e,i,r){var s=ei(r),n=i*s*4;null!==e&&e.length===n||(e=new Uint8Array(i*s*4));for(var o=1/(i-1),a=0,l=0;l<s;l++)for(var u=0;u<i;u++){var h=ti(r,o*u,l);e[a]=h.r,e[a+1]=h.g,e[a+2]=h.b,e[a+3]=h.a,a+=4}return null!==t&&i===t.width&&s===t.height||(t&&t.destroy(),(t=new nt).create(i,s,_t.RGBA8888),t.setFilters(dt.LINEAR,dt.LINEAR),t.setWrapMode(pt.CLAMP_TO_EDGE,pt.CLAMP_TO_EDGE)),t.uploadData(e),{texture:t,texdata:e}}var ri="CC_USE_WORLD_SPACE",si={CC_USE_WORLD_SPACE:!1,CC_USE_WORLD_SCALE:!0},ni=(Fe=S("cc.Line"),Ce=b(nt),Ee=b(nt),ze=b(rt),De=b([F]),Pe=b([F]),Ie=b(Me),Be=b($e),Le=b(V),Ve=b(V),Fe((ke=function(t){function r(){var e;return(e=t.call(this)||this)._texture=Ne&&Ne(),e._material=Ge&&Ge(),e._worldSpace=He&&He(),e._positions=Xe&&Xe(),e._width=We&&We(),e._color=je&&je(),e._tile=Ye&&Ye(),e._tile_offset=new x,e._offset=Ze&&Ze(),e}e(r,t);var s=r.prototype;return s.onLoad=function(){var t=et.director.root.createModel(fe);if(0===this._models.length?this._models.push(t):this._models[0]=t,t.node=t.transform=this.node,this._material&&(this.lineMaterial=this._material,this._material=null),null===this.lineMaterial){var e=st.get("default-trail-material");this.material=e}var i=this.getMaterialInstance(0);i&&(si[ri]=this.worldSpace,i.recompileShaders(si),t.updateMaterial(i)),t.setCapacity(100)},s.onEnable=function(){t.prototype.onEnable.call(this),0!==this._models.length&&this._models[0]&&(this._attachToScene(),this.texture=this._texture,this.tile=this._tile,this.offset=this._offset,this._models[0].addLineVertexData(this._positions,this.width,this.color))},s.onDisable=function(){this._models.length>0&&this._models[0]&&this._detachFromScene()},s._attachToScene=function(){if(t.prototype._attachToScene.call(this),this._models.length>0&&this._models[0]&&this.node&&this.node.scene){var e=this._models[0];e.scene&&this._detachFromScene(),this._getRenderScene().addModel(e)}},s._detachFromScene=function(){if(t.prototype._detachFromScene.call(this),this._models.length>0&&this._models[0]){var e=this._models[0];e.scene&&e.scene.removeModel(e)}},s._onMaterialModified=function(e,i){t.prototype._onMaterialModified.call(this,e,i);var r=this.getMaterialInstance(0);r&&(si[ri]=this.worldSpace,r.recompileShaders(si),this._models[0]&&this._models[0].updateMaterial(r))},i(r,[{key:"texture",get:function(){return this._texture},set:function(t){this._texture=t,this.material&&this.material.setProperty("mainTexture",t)}},{key:"lineMaterial",get:function(){return this.getSharedMaterial(0)},set:function(t){this.setSharedMaterial(t,0)}},{key:"sharedMaterials",get:function(){return t.prototype.sharedMaterials},set:function(t){this.sharedMaterials=t}},{key:"worldSpace",get:function(){return this._worldSpace},set:function(t){this._worldSpace=t;var e=this.getMaterialInstance(0);e&&(si[ri]=this.worldSpace,e.recompileShaders(si),this._models[0]&&this._models[0].setSubModelMaterial(0,e))}},{key:"positions",get:function(){return this._positions},set:function(t){this._positions=t,this._models[0]&&this._models[0].addLineVertexData(this._positions,this.width,this.color)}},{key:"width",get:function(){return this._width},set:function(t){this._width=t,this._models[0]&&this._models[0].addLineVertexData(this._positions,this._width,this._color)}},{key:"color",get:function(){return this._color},set:function(t){this._color=t,this._models[0]&&this._models[0].addLineVertexData(this._positions,this._width,this._color)}},{key:"tile",get:function(){return this._tile},set:function(t){this._tile.set(t),this.material&&(this._tile_offset.x=this._tile.x,this._tile_offset.y=this._tile.y,this.material.setProperty("mainTiling_Offset",this._tile_offset))}},{key:"offset",get:function(){return this._offset},set:function(t){this._offset.set(t),this.material&&(this._tile_offset.z=this._offset.x,this._tile_offset.w=this._offset.y,this.material.setProperty("mainTiling_Offset",this._tile_offset))}}]),r}(Et),Ne=w(ke.prototype,"_texture",[Ce],(function(){return null})),r(ke.prototype,"texture",[Ee],Object.getOwnPropertyDescriptor(ke.prototype,"texture"),ke.prototype),Ge=w(ke.prototype,"_material",[R],(function(){return null})),r(ke.prototype,"lineMaterial",[ze],Object.getOwnPropertyDescriptor(ke.prototype,"lineMaterial"),ke.prototype),r(ke.prototype,"sharedMaterials",[L,R],Object.getOwnPropertyDescriptor(ke.prototype,"sharedMaterials"),ke.prototype),He=w(ke.prototype,"_worldSpace",[R],(function(){return!1})),Xe=w(ke.prototype,"_positions",[De],(function(){return[]})),r(ke.prototype,"positions",[Pe],Object.getOwnPropertyDescriptor(ke.prototype,"positions"),ke.prototype),r(ke.prototype,"width",[Ie],Object.getOwnPropertyDescriptor(ke.prototype,"width"),ke.prototype),We=w(ke.prototype,"_width",[R],(function(){return new Me})),r(ke.prototype,"color",[Be],Object.getOwnPropertyDescriptor(ke.prototype,"color"),ke.prototype),je=w(ke.prototype,"_color",[R],(function(){return new $e})),Ye=w(ke.prototype,"_tile",[R],(function(){return new V(1,1)})),r(ke.prototype,"tile",[Le],Object.getOwnPropertyDescriptor(ke.prototype,"tile"),ke.prototype),Ze=w(ke.prototype,"_offset",[R],(function(){return new V(0,0)})),r(ke.prototype,"offset",[Ve],Object.getOwnPropertyDescriptor(ke.prototype,"offset"),ke.prototype),Ue=ke))||Ue);t({Line:ni,LineComponent:ni});var oi=function(){function t(t){this.particleSystem=void 0,this.position=void 0,this.velocity=void 0,this.animatedVelocity=void 0,this.ultimateVelocity=void 0,this.angularVelocity=void 0,this.axisOfRotation=void 0,this.rotation=void 0,this.startEuler=void 0,this.startRotation=void 0,this.startRotated=void 0,this.deltaQuat=void 0,this.deltaMat=void 0,this.localMat=void 0,this.startSize=void 0,this.size=void 0,this.startColor=void 0,this.color=void 0,this.randomSeed=void 0,this.remainingLifetime=void 0,this.loopCount=void 0,this.lastLoop=void 0,this.trailDelay=void 0,this.startLifetime=void 0,this.emitAccumulator0=void 0,this.emitAccumulator1=void 0,this.frameIndex=void 0,this.startRow=void 0,this.particleSystem=t,this.position=new F(0,0,0),this.velocity=new F(0,0,0),this.animatedVelocity=new F(0,0,0),this.ultimateVelocity=new F(0,0,0),this.angularVelocity=new F(0,0,0),this.axisOfRotation=new F(0,0,0),this.rotation=new F(0,0,0),this.startEuler=new F(0,0,0),this.startRotation=new U,this.startRotated=!1,this.deltaQuat=new U,this.deltaMat=new k,this.localMat=new k,this.startSize=new F(0,0,0),this.size=new F(0,0,0),this.startColor=T.WHITE.clone(),this.color=T.WHITE.clone(),this.randomSeed=0,this.remainingLifetime=0,this.loopCount=0,this.lastLoop=0,this.trailDelay=0,this.startLifetime=0,this.emitAccumulator0=0,this.emitAccumulator1=0,this.frameIndex=0,this.startRow=0}return t.prototype.reset=function(){this.rotation.set(0,0,0),this.startEuler.set(0,0,0),this.startRotation.set(0,0,0,1),this.startRotated=!1,this.deltaQuat.set(0,0,0,1),this.deltaMat.identity(),this.localMat.identity()},t}();oi.INDENTIFY_NEG_QUAT=10,oi.R2D=180/Math.PI;var ai,li,ui,hi,ci,_i,di,pi,fi,mi,vi="noiseModule",yi=["sizeModule","colorModule","forceModule","velocityModule","limitModule","rotationModule","textureModule","noiseModule"],Mi=["_colorOverLifetimeModule","_shapeModule","_sizeOvertimeModule","_velocityOvertimeModule","_forceOvertimeModule","_limitVelocityOvertimeModule","_rotationOvertimeModule","_textureAnimationModule","_noiseModule","_trailModule"],gi=function(){function t(){this.target=null,this.needUpdate=!1,this.needAnimate=!0,this.name=void 0}var e=t.prototype;return e.bindTarget=function(t){this.target=t},e.update=function(){},t}();!function(t){t[t.World=0]="World",t[t.Local=1]="Local",t[t.Custom=2]="Custom"}(ai||(ai={})),n(ai),function(t){t[t.Pause=0]="Pause",t[t.PauseAndCatchup=1]="PauseAndCatchup",t[t.AlwaysSimulate=2]="AlwaysSimulate"}(li||(li={})),n(li),function(t){t[t.World=0]="World",t[t.Local=1]="Local",t[t.View=2]="View"}(ui||(ui={})),n(ui),function(t){t[t.Billboard=0]="Billboard",t[t.StrecthedBillboard=1]="StrecthedBillboard",t[t.HorizontalBillboard=2]="HorizontalBillboard",t[t.VerticalBillboard=3]="VerticalBillboard",t[t.Mesh=4]="Mesh"}(hi||(hi={})),n(hi),function(t){t[t.Box=0]="Box",t[t.Circle=1]="Circle",t[t.Cone=2]="Cone",t[t.Sphere=3]="Sphere",t[t.Hemisphere=4]="Hemisphere"}(ci||(ci={})),n(ci),function(t){t[t.Base=0]="Base",t[t.Edge=1]="Edge",t[t.Shell=2]="Shell",t[t.Volume=3]="Volume"}(_i||(_i={})),n(_i),function(t){t[t.Random=0]="Random",t[t.Loop=1]="Loop",t[t.PingPong=2]="PingPong"}(di||(di={})),n(di),function(t){t[t.Particles=0]="Particles"}(pi||(pi={})),n(pi),function(t){t[t.Stretch=0]="Stretch"}(fi||(fi={})),n(fi),function(t){t[t.LIMIT=23541]="LIMIT",t[t.SIZE=39825]="SIZE",t[t.TEXTURE=90794]="TEXTURE",t[t.COLOR=91041]="COLOR",t[t.FORCE=212165]="FORCE",t[t.ROTATION=125292]="ROTATION",t[t.VELOCITY_X=197866]="VELOCITY_X",t[t.VELOCITY_Y=156497]="VELOCITY_Y",t[t.VELOCITY_Z=984136]="VELOCITY_Z"}(mi||(mi={}));var Si,bi,xi,Ti,wi,Ai,Oi=new F(0,0,-1);function Ri(t,e,i,r){return e!==t?(t===ai.World||k.invert(i,i),k.getRotation(r,i),!0):(U.set(r,0,0,0,1),!1)}function Fi(t,e){V.set(t,Math.cos(e),Math.sin(e))}function Ci(t){var e=N(-1,1),i=N(0,2*Math.PI),r=Math.sqrt(1-e*e),s=r*Math.cos(i),n=r*Math.sin(i);F.set(t,s,n,e)}function Ei(t,e,i){Ci(t),F.multiplyScalar(t,t,e+(i-e)*G())}function zi(t,e,i,r){Fi(t,r),t.z=0,F.multiplyScalar(t,t,e+(i-e)*G())}function Di(t){for(var e=0;e<t.length;e++){var i=e+H(0,t.length-e),r=t[i];t[i]=t[e],t[e]=r}}function Pi(){var t=N(-1,1);return 0===t&&t++,a(t)}function Ii(t){var e=Me.Mode;switch(t.mode){case e.TwoCurves:case e.TwoConstants:return!0;default:return!1}}function Bi(t){var e=$e.Mode;switch(t.mode){case e.TwoGradients:case e.TwoColors:return!0;default:return!1}}var Li,Vi,Ui,ki,Ni,Gi,Hi,Xi,Wi,ji,Yi,Zi,qi,Qi,Ki,Ji,$i,tr,er,ir,rr,sr,nr,or,ar,lr,ur,hr,cr,_r,dr,pr,fr,mr,vr,yr,Mr,gr,Sr,br=mi.COLOR,xr=(Si=S("cc.ColorOvertimeModule"),bi=b($e),Si((Ti=function(t){function r(){var e;return(e=t.call(this)||this)._enable=wi&&wi(),e.color=Ai&&Ai(),e.name="colorModule",e}return e(r,t),r.prototype.animate=function(t){t.color.set(t.startColor);var e=Bi(this.color)?X(t.randomSeed+br):0;t.color.multiply(this.color.evaluate(1-t.remainingLifetime/t.startLifetime,e))},i(r,[{key:"enable",get:function(){return this._enable},set:function(t){this._enable!==t&&(this._enable=t,this.target&&this.target.enableModule(this.name,t,this))}}]),r}(gi),wi=w(Ti.prototype,"_enable",[R],(function(){return!1})),Ai=w(Ti.prototype,"color",[bi,R],(function(){return new $e})),xi=Ti))||xi),Tr=mi.FORCE,wr=new F,Ar=(Li=S("cc.ForceOvertimeModule"),Vi=b(Me),Ui=b(Me),ki=b(Me),Ni=b(ai),Li((Hi=function(t){function r(){var e;return(e=t.call(this)||this)._enable=Xi&&Xi(),e.x=Wi&&Wi(),e.y=ji&&ji(),e.z=Yi&&Yi(),e.space=Zi&&Zi(),e.randomized=!1,e.rotation=void 0,e.needTransform=void 0,e.name="forceModule",e.rotation=new U,e.needTransform=!1,e.needUpdate=!0,e}e(r,t);var s=r.prototype;return s.update=function(t,e){this.needTransform=Ri(t,this.space,e,this.rotation)},s.animate=function(t,e){var i=1-t.remainingLifetime/t.startLifetime,r=Ii(this.x)?X(t.randomSeed+Tr):0,s=Ii(this.y)?X(t.randomSeed+Tr):0,n=Ii(this.z)?X(t.randomSeed+Tr):0,o=F.set(wr,this.x.evaluate(i,r),this.y.evaluate(i,s),this.z.evaluate(i,n));this.needTransform&&F.transformQuat(o,o,this.rotation),F.scaleAndAdd(t.velocity,t.velocity,o,e),F.copy(t.ultimateVelocity,t.velocity)},i(r,[{key:"enable",get:function(){return this._enable},set:function(t){this._enable!==t&&(this._enable=t,this.target&&this.target.enableModule(this.name,t,this))}}]),r}(gi),Xi=w(Hi.prototype,"_enable",[R],(function(){return!1})),Wi=w(Hi.prototype,"x",[Vi,R],(function(){return new Me})),ji=w(Hi.prototype,"y",[Ui,R],(function(){return new Me})),Yi=w(Hi.prototype,"z",[ki,R],(function(){return new Me})),Zi=w(Hi.prototype,"space",[Ni,R],(function(){return ai.Local})),Gi=Hi))||Gi),Or=mi.LIMIT,Rr=new F,Fr=new F,Cr=(qi=S("cc.LimitVelocityOvertimeModule"),Qi=b(Me),Ki=b(Me),Ji=b(Me),$i=b(Me),tr=b(ai),qi((ir=function(t){function r(){var e;return(e=t.call(this)||this)._enable=rr&&rr(),e.limitX=sr&&sr(),e.limitY=nr&&nr(),e.limitZ=or&&or(),e.limit=ar&&ar(),e.dampen=lr&&lr(),e.separateAxes=ur&&ur(),e.space=hr&&hr(),e.drag=null,e.multiplyDragByParticleSize=!1,e.multiplyDragByParticleVelocity=!1,e.name="limitModule",e.rotation=void 0,e.needTransform=void 0,e.rotation=new U,e.needTransform=!1,e.needUpdate=!0,e}e(r,t);var s=r.prototype;return s.update=function(t,e){this.needTransform=Ri(t,this.space,e,this.rotation)},s.animate=function(t){var e=1-t.remainingLifetime/t.startLifetime,i=Rr;if(this.separateAxes){var r=Ii(this.limitX)?X(t.randomSeed+Or):0,s=Ii(this.limitY)?X(t.randomSeed+Or):0,n=Ii(this.limitZ)?X(t.randomSeed+Or):0;F.set(Fr,this.limitX.evaluate(e,r),this.limitY.evaluate(e,s),this.limitZ.evaluate(e,n)),this.needTransform&&F.transformQuat(Fr,Fr,this.rotation),F.set(i,Er(t.ultimateVelocity.x,Fr.x,this.dampen),Er(t.ultimateVelocity.y,Fr.y,this.dampen),Er(t.ultimateVelocity.z,Fr.z,this.dampen))}else{F.normalize(i,t.ultimateVelocity);var o=Ii(this.limit)?X(t.randomSeed+Or):0;F.multiplyScalar(i,i,Er(t.ultimateVelocity.length(),this.limit.evaluate(e,o),this.dampen))}F.copy(t.ultimateVelocity,i),F.copy(t.velocity,t.ultimateVelocity)},i(r,[{key:"enable",get:function(){return this._enable},set:function(t){this._enable!==t&&(this._enable=t,this.target&&this.target.enableModule(this.name,t,this))}}]),r}(gi),rr=w(ir.prototype,"_enable",[R],(function(){return!1})),sr=w(ir.prototype,"limitX",[Qi,R],(function(){return new Me})),nr=w(ir.prototype,"limitY",[Ki,R],(function(){return new Me})),or=w(ir.prototype,"limitZ",[Ji,R],(function(){return new Me})),ar=w(ir.prototype,"limit",[$i,R],(function(){return new Me})),lr=w(ir.prototype,"dampen",[R],(function(){return 3})),ur=w(ir.prototype,"separateAxes",[R],(function(){return!1})),hr=w(ir.prototype,"space",[tr,R],(function(){return ai.Local})),er=ir))||er);function Er(t,e,i){var r=Math.sign(t),s=Math.abs(t);if(s>e){var n=s-s*i;s=n>e?n:e}return s*r}var zr,Dr,Pr,Ir,Br,Lr,Vr,Ur,kr,Nr,Gr,Hr,Xr,Wr,jr,Yr,Zr,qr,Qr,Kr,Jr,$r,ts,es,is,rs,ss,ns,os,as,ls,us,hs,cs,_s,ds,ps,fs,ms,vs,ys,Ms,gs,Ss,bs,xs,Ts,ws,As,Os,Rs,Fs,Cs,Es,zs,Ds,Ps,Is,Bs,Ls,Vs,Us,ks,Ns,Gs,Hs,Xs,Ws,js,Ys,Zs,qs,Qs,Ks,Js,$s,tn,en,rn,sn,nn,on,an,ln,un,hn,cn=mi.ROTATION,_n=(cr=S("cc.RotationOvertimeModule"),_r=b(Me),dr=b(Me),pr=b(Me),cr((mr=function(t){function r(){var e;return(e=t.call(this)||this)._enable=vr&&vr(),e._separateAxes=yr&&yr(),e.x=Mr&&Mr(),e.y=gr&&gr(),e.z=Sr&&Sr(),e.name="rotationModule",e._startMat=new k,e._matRot=new k,e._quatRot=new U,e._otherEuler=new F,e}e(r,t);var s=r.prototype;return s._processRotation=function(t){var e=t.particleSystem.processor.getInfo().renderMode;e!==hi.Mesh&&e===hi.StrecthedBillboard&&this._quatRot.set(0,0,0,1),U.normalize(this._quatRot,this._quatRot),this._quatRot.w<0&&(this._quatRot.x+=oi.INDENTIFY_NEG_QUAT)},s.animate=function(t,e){var i=1-t.remainingLifetime/t.startLifetime,r=Ii(this.z)?X(t.randomSeed+cn):0,s=t.particleSystem.processor.getInfo().renderMode;if(this._separateAxes&&s!==hi.VerticalBillboard&&s!==hi.HorizontalBillboard){var n=Ii(this.x)?X(t.randomSeed+cn):0,o=Ii(this.y)?X(t.randomSeed+cn):0;U.fromEuler(t.deltaQuat,this.x.evaluate(i,n)*e*oi.R2D,this.y.evaluate(i,o)*e*oi.R2D,this.z.evaluate(i,r)*e*oi.R2D)}else U.fromEuler(t.deltaQuat,0,0,this.z.evaluate(i,r)*e*oi.R2D);t.deltaMat=k.fromQuat(t.deltaMat,t.deltaQuat),t.localMat=t.localMat.multiply(t.deltaMat),t.startRotated||(s!==hi.Mesh&&(s===hi.StrecthedBillboard?t.startEuler.set(0,0,0):s!==hi.Billboard&&t.startEuler.set(0,0,t.startEuler.z)),U.fromEuler(t.startRotation,t.startEuler.x*oi.R2D,t.startEuler.y*oi.R2D,t.startEuler.z*oi.R2D),t.startRotated=!0),this._startMat=k.fromQuat(this._startMat,t.startRotation),this._matRot=this._startMat.multiply(t.localMat),k.getRotation(this._quatRot,this._matRot),this._processRotation(t,oi.R2D),t.rotation.set(this._quatRot.x,this._quatRot.y,this._quatRot.z)},i(r,[{key:"enable",get:function(){return this._enable},set:function(t){this._enable!==t&&(this._enable=t,this.target&&this.target.enableModule(this.name,t,this))}},{key:"separateAxes",get:function(){return this._separateAxes},set:function(t){this._separateAxes=t}}]),r}(gi),vr=w(mr.prototype,"_enable",[R],(function(){return!1})),yr=w(mr.prototype,"_separateAxes",[R],(function(){return!1})),Mr=w(mr.prototype,"x",[_r,R],(function(){return new Me})),gr=w(mr.prototype,"y",[dr,R],(function(){return new Me})),Sr=w(mr.prototype,"z",[pr,R],(function(){return new Me})),fr=mr))||fr),dn=mi.SIZE,pn=(zr=S("cc.SizeOvertimeModule"),Dr=b(Me),Pr=b(Me),Ir=b(Me),Br=b(Me),zr((Vr=function(t){function r(){var e;return(e=t.call(this)||this)._enable=Ur&&Ur(),e.separateAxes=kr&&kr(),e.size=Nr&&Nr(),e.x=Gr&&Gr(),e.y=Hr&&Hr(),e.z=Xr&&Xr(),e.name="sizeModule",e}return e(r,t),r.prototype.animate=function(t){if(this.separateAxes){var e=1-t.remainingLifetime/t.startLifetime,i=Ii(this.x)?X(t.randomSeed+dn):0,r=Ii(this.y)?X(t.randomSeed+dn):0,s=Ii(this.z)?X(t.randomSeed+dn):0;t.size.x=t.startSize.x*this.x.evaluate(e,i),t.size.y=t.startSize.y*this.y.evaluate(e,r),t.size.z=t.startSize.z*this.z.evaluate(e,s)}else{var n=Ii(this.size)?X(t.randomSeed+dn):0;F.multiplyScalar(t.size,t.startSize,this.size.evaluate(1-t.remainingLifetime/t.startLifetime,n))}},i(r,[{key:"enable",get:function(){return this._enable},set:function(t){this._enable!==t&&(this._enable=t,this.target&&this.target.enableModule(this.name,t,this))}}]),r}(gi),Ur=w(Vr.prototype,"_enable",[R],(function(){return!1})),kr=w(Vr.prototype,"separateAxes",[R],(function(){return!1})),Nr=w(Vr.prototype,"size",[Dr,R],(function(){return new Me})),Gr=w(Vr.prototype,"x",[Pr,R],(function(){return new Me})),Hr=w(Vr.prototype,"y",[Ir,R],(function(){return new Me})),Xr=w(Vr.prototype,"z",[Br,R],(function(){return new Me})),Lr=Vr))||Lr),fn=mi.TEXTURE,mn=n({Grid:0}),vn=n({WholeSheet:0,SingleRow:1}),yn=(Wr=S("cc.TextureAnimationModule"),jr=j("numTilesX"),Yr=j("numTilesY"),Zr=b(mn),qr=b(mn),Qr=b(vn),Kr=b(Me),Jr=b(Me),Wr((ts=function(t){function r(){var e;return(e=t.call(this)||this)._enable=es&&es(),e._numTilesX=is&&is(),e._numTilesY=rs&&rs(),e._mode=ss&&ss(),e.animation=ns&&ns(),e.frameOverTime=os&&os(),e.startFrame=as&&as(),e.cycleCount=ls&&ls(),e._flipU=us&&us(),e._flipV=hs&&hs(),e._uvChannelMask=cs&&cs(),e.randomRow=_s&&_s(),e.rowIndex=ds&&ds(),e.name="textureModule",e}e(r,t);var s=r.prototype;return s.init=function(t){t.startRow=Math.floor(G()*this.numTilesY)},s.animate=function(t){var e=1-t.remainingLifetime/t.startLifetime,i=Ii(this.startFrame)?X(t.randomSeed+fn):0,r=Ii(this.frameOverTime)?X(t.randomSeed+fn):0,s=this.startFrame.evaluate(e,i)/(this.numTilesX*this.numTilesY);if(this.animation===vn.WholeSheet)t.frameIndex=W(this.cycleCount*(this.frameOverTime.evaluate(e,r)+s),1);else if(this.animation===vn.SingleRow){var n=1/this.numTilesY;if(this.randomRow){var o=W(this.cycleCount*(this.frameOverTime.evaluate(e,r)+s),1),a=t.startRow*n,l=a+n;t.frameIndex=C(a,l,o)}else{var u=this.rowIndex*n,h=u+n;t.frameIndex=C(u,h,W(this.cycleCount*(this.frameOverTime.evaluate(e,r)+s),1))}}},i(r,[{key:"enable",get:function(){return this._enable},set:function(t){this._enable!==t&&(this._enable=t,this.target&&(this.target.updateMaterialParams(),this.target.enableModule(this.name,t,this)))}},{key:"mode",get:function(){return this._mode},set:function(t){t!==mn.Grid&&l("particle texture animation's sprites is not supported!")}},{key:"numTilesX",get:function(){return this._numTilesX},set:function(t){this._numTilesX!==t&&(this._numTilesX=t,this.target.updateMaterialParams())}},{key:"numTilesY",get:function(){return this._numTilesY},set:function(t){this._numTilesY!==t&&(this._numTilesY=t,this.target.updateMaterialParams())}},{key:"flipU",get:function(){return this._flipU},set:function(){l("particle texture animation's flipU is not supported!")}},{key:"flipV",get:function(){return this._flipV},set:function(){l("particle texture animation's flipV is not supported!")}},{key:"uvChannelMask",get:function(){return this._uvChannelMask},set:function(){l("particle texture animation's uvChannelMask is not supported!")}}]),r}(gi),es=w(ts.prototype,"_enable",[R],(function(){return!1})),is=w(ts.prototype,"_numTilesX",[jr],(function(){return 0})),rs=w(ts.prototype,"_numTilesY",[Yr],(function(){return 0})),ss=w(ts.prototype,"_mode",[Zr],(function(){return mn.Grid})),r(ts.prototype,"mode",[qr],Object.getOwnPropertyDescriptor(ts.prototype,"mode"),ts.prototype),ns=w(ts.prototype,"animation",[Qr,R],(function(){return vn.WholeSheet})),os=w(ts.prototype,"frameOverTime",[Kr,R],(function(){return new Me})),as=w(ts.prototype,"startFrame",[Jr,R],(function(){return new Me})),ls=w(ts.prototype,"cycleCount",[R],(function(){return 0})),us=w(ts.prototype,"_flipU",[R],(function(){return 0})),hs=w(ts.prototype,"_flipV",[R],(function(){return 0})),cs=w(ts.prototype,"_uvChannelMask",[R],(function(){return-1})),_s=w(ts.prototype,"randomRow",[R],(function(){return!1})),ds=w(ts.prototype,"rowIndex",[R],(function(){return 0})),$r=ts))||$r),Mn=mi.VELOCITY_X,gn=mi.VELOCITY_Y,Sn=mi.VELOCITY_Z,bn=new F,xn=(ps=S("cc.VelocityOvertimeModule"),fs=b(Me),ms=b(Me),vs=b(Me),ys=b(Me),Ms=b(ai),ps((Ss=function(t){function r(){var e;return(e=t.call(this)||this)._enable=bs&&bs(),e.x=xs&&xs(),e.y=Ts&&Ts(),e.z=ws&&ws(),e.speedModifier=As&&As(),e.space=Os&&Os(),e.rotation=void 0,e.needTransform=void 0,e.name="velocityModule",e.rotation=new U,e.speedModifier.constant=1,e.needTransform=!1,e.needUpdate=!0,e}e(r,t);var s=r.prototype;return s.update=function(t,e){this.needTransform=Ri(t,this.space,e,this.rotation)},s.animate=function(t){var e=1-t.remainingLifetime/t.startLifetime,i=Ii(this.x)?X(t.randomSeed^Mn):0,r=Ii(this.y)?X(t.randomSeed^gn):0,s=Ii(this.z)?X(t.randomSeed^Sn):0,n=Ii(this.speedModifier)?X(t.randomSeed+Mn):0,o=F.set(bn,this.x.evaluate(e,i),this.y.evaluate(e,r),this.z.evaluate(e,s));this.needTransform&&F.transformQuat(o,o,this.rotation),F.add(t.animatedVelocity,t.animatedVelocity,o),F.add(t.ultimateVelocity,t.velocity,t.animatedVelocity),F.multiplyScalar(t.ultimateVelocity,t.ultimateVelocity,this.speedModifier.evaluate(1-t.remainingLifetime/t.startLifetime,n))},i(r,[{key:"enable",get:function(){return this._enable},set:function(t){this._enable!==t&&(this._enable=t,this.target&&this.target.enableModule(this.name,t,this))}}]),r}(gi),bs=w(Ss.prototype,"_enable",[R],(function(){return!1})),xs=w(Ss.prototype,"x",[fs,R],(function(){return new Me})),Ts=w(Ss.prototype,"y",[ms,R],(function(){return new Me})),ws=w(Ss.prototype,"z",[vs,R],(function(){return new Me})),As=w(Ss.prototype,"speedModifier",[ys,R],(function(){return new Me})),Os=w(Ss.prototype,"space",[Ms,R],(function(){return ai.Local})),gs=Ss))||gs),Tn=t("Burst",(Rs=S("cc.Burst"),Fs=b(Me),Rs((Es=function(){function t(){this._time=zs&&zs(),this._repeatCount=Ds&&Ds(),this.repeatInterval=Ps&&Ps(),this.count=Is&&Is(),this._remainingCount=0,this._curTime=0}var e=t.prototype;return e.update=function(t,e){if(0===this._remainingCount&&(this._remainingCount=this._repeatCount,this._curTime=this._time),this._remainingCount>0){var i=W(t.time-t.startDelay.evaluate(0,1),t.duration)-e;i=i>0?i:0;var r=W(t.time-t.startDelay.evaluate(0,1),t.duration);this._curTime>=i&&this._curTime<r&&(t.emit(this.count.evaluate(this._curTime/t.duration,1),e-(r-this._curTime)),this._curTime+=this.repeatInterval,--this._remainingCount)}},e.reset=function(){this._remainingCount=0,this._curTime=0},e.getMaxCount=function(t){return this.count.getMax()*Math.min(Math.ceil(t.duration/this.repeatInterval),this.repeatCount)},i(t,[{key:"time",get:function(){return this._time},set:function(t){this._time=t,this._curTime=t}},{key:"repeatCount",get:function(){return this._repeatCount},set:function(t){this._repeatCount=t,this._remainingCount=t}}]),t}(),zs=w(Es.prototype,"_time",[R],(function(){return 0})),Ds=w(Es.prototype,"_repeatCount",[R],(function(){return 1})),Ps=w(Es.prototype,"repeatInterval",[R],(function(){return 1})),Is=w(Es.prototype,"count",[Fs,R],(function(){return new Me})),Cs=Es))||Cs)),wn=new F(0,0,0),An=[0,0,0],On=new F(.5,.5,.5),Rn=(Bs=S("cc.ShapeModule"),Ls=b(ci),Vs=j("shapeType"),Us=b(ci),ks=b(_i),Ns=b(di),Gs=b(Me),Bs((Xs=function(){function t(){this._enable=Ws&&Ws(),this._shapeType=js&&js(),this.emitFrom=Ys&&Ys(),this.alignToDirection=Zs&&Zs(),this.randomDirectionAmount=qs&&qs(),this.sphericalDirectionAmount=Qs&&Qs(),this.randomPositionAmount=Ks&&Ks(),this.radius=Js&&Js(),this.radiusThickness=$s&&$s(),this.arcMode=tn&&tn(),this.arcSpread=en&&en(),this.arcSpeed=rn&&rn(),this.length=sn&&sn(),this.boxThickness=nn&&nn(),this._position=on&&on(),this._rotation=an&&an(),this._scale=ln&&ln(),this._arc=un&&un(),this._angle=hn&&hn(),this.mat=new k,this.quat=new U,this.particleSystem=null,this.lastTime=0,this.totalAngle=0}var e=t.prototype;return e.onInit=function(t){this.particleSystem=t,this.constructMat(),this.lastTime=this.particleSystem.time},e.emit=function(t){switch(this.shapeType){case ci.Box:zn(this.emitFrom,this.boxThickness,t.position,t.velocity);break;case ci.Circle:e=this.radius,i=this.radiusThickness,r=this.generateArcAngle(),s=t.position,n=t.velocity,zi(s,e*(1-i),e,r),F.normalize(n,s);break;case ci.Cone:En(this.emitFrom,this.radius,this.radiusThickness,this.generateArcAngle(),this._angle,this.length,t.position,t.velocity);break;case ci.Sphere:Fn(this.emitFrom,this.radius,this.radiusThickness,t.position,t.velocity);break;case ci.Hemisphere:Cn(this.emitFrom,this.radius,this.radiusThickness,t.position,t.velocity);break;default:u(this.shapeType+" shapeType is not supported by ShapeModule.")}var e,i,r,s,n;if(this.randomPositionAmount>0&&(t.position.x+=N(-this.randomPositionAmount,this.randomPositionAmount),t.position.y+=N(-this.randomPositionAmount,this.randomPositionAmount),t.position.z+=N(-this.randomPositionAmount,this.randomPositionAmount)),F.transformQuat(t.velocity,t.velocity,this.quat),F.transformMat4(t.position,t.position,this.mat),this.sphericalDirectionAmount>0){var o=F.normalize(wn,t.position);F.lerp(t.velocity,t.velocity,o,this.sphericalDirectionAmount)}this.lastTime=this.particleSystem.time},e.constructMat=function(){U.fromEuler(this.quat,this._rotation.x,this._rotation.y,this._rotation.z),k.fromRTS(this.mat,this.quat,this._position,this._scale)},e.generateArcAngle=function(){if(this.arcMode===di.Random)return N(0,this._arc);var t=this.totalAngle+2*Math.PI*this.arcSpeed.evaluate(this.particleSystem.time,1)*(this.particleSystem.time-this.lastTime);switch(this.totalAngle=t,0!==this.arcSpread&&(t=Math.floor(t/(this._arc*this.arcSpread))*this._arc*this.arcSpread),this.arcMode){case di.Loop:return W(t,this._arc);case di.PingPong:return Y(t,this._arc);default:return W(t,this._arc)}},i(t,[{key:"position",get:function(){return this._position},set:function(t){this._position=t,this.constructMat()}},{key:"rotation",get:function(){return this._rotation},set:function(t){this._rotation=t,this.constructMat()}},{key:"scale",get:function(){return this._scale},set:function(t){this._scale=t,this.constructMat()}},{key:"arc",get:function(){return A(this._arc)},set:function(t){this._arc=O(t)}},{key:"angle",get:function(){return Math.round(100*A(this._angle))/100},set:function(t){this._angle=O(t)}},{key:"enable",get:function(){return this._enable},set:function(t){this._enable=t}},{key:"shapeType",get:function(){return this._shapeType},set:function(t){switch(this._shapeType=t,this._shapeType){case ci.Box:this.emitFrom===_i.Base&&(this.emitFrom=_i.Volume);break;case ci.Cone:this.emitFrom===_i.Edge&&(this.emitFrom=_i.Base);break;case ci.Sphere:case ci.Hemisphere:this.emitFrom!==_i.Base&&this.emitFrom!==_i.Edge||(this.emitFrom=_i.Volume)}}}]),t}(),Ws=w(Xs.prototype,"_enable",[R],(function(){return!1})),js=w(Xs.prototype,"_shapeType",[Ls,Vs],(function(){return ci.Cone})),r(Xs.prototype,"shapeType",[Us],Object.getOwnPropertyDescriptor(Xs.prototype,"shapeType"),Xs.prototype),Ys=w(Xs.prototype,"emitFrom",[ks,R],(function(){return _i.Volume})),Zs=w(Xs.prototype,"alignToDirection",[R],(function(){return!1})),qs=w(Xs.prototype,"randomDirectionAmount",[R],(function(){return 0})),Qs=w(Xs.prototype,"sphericalDirectionAmount",[R],(function(){return 0})),Ks=w(Xs.prototype,"randomPositionAmount",[R],(function(){return 0})),Js=w(Xs.prototype,"radius",[R],(function(){return 1})),$s=w(Xs.prototype,"radiusThickness",[R],(function(){return 1})),tn=w(Xs.prototype,"arcMode",[Ns,R],(function(){return di.Random})),en=w(Xs.prototype,"arcSpread",[R],(function(){return 0})),rn=w(Xs.prototype,"arcSpeed",[Gs,R],(function(){return new Me})),sn=w(Xs.prototype,"length",[R],(function(){return 5})),nn=w(Xs.prototype,"boxThickness",[R],(function(){return new F(0,0,0)})),on=w(Xs.prototype,"_position",[R],(function(){return new F(0,0,0)})),an=w(Xs.prototype,"_rotation",[R],(function(){return new F(0,0,0)})),ln=w(Xs.prototype,"_scale",[R],(function(){return new F(1,1,1)})),un=w(Xs.prototype,"_arc",[R],(function(){return O(360)})),hn=w(Xs.prototype,"_angle",[R],(function(){return O(25)})),Hs=Xs))||Hs);function Fn(t,e,i,r,s){switch(t){case _i.Volume:Ei(r,e*(1-i),e),F.normalize(s,r);break;case _i.Shell:Ci(r),F.multiplyScalar(r,r,e),F.normalize(s,r);break;default:u(t+" is not supported for sphere emitter.")}}function Cn(t,e,i,r,s){switch(t){case _i.Volume:Ei(r,e*(1-i),e),r.z>0&&(r.z*=-1),F.normalize(s,r);break;case _i.Shell:Ci(r),F.multiplyScalar(r,r,e),r.z>0&&(r.z*=-1),F.normalize(s,r);break;default:u(t+" is not supported for hemisphere emitter.")}}function En(t,e,i,r,s,n,o,a){switch(t){case _i.Base:zi(o,e*(1-i),e,r),V.multiplyScalar(a,o,Math.sin(s)),a.z=-Math.cos(s)*e,F.normalize(a,a),o.z=0;break;case _i.Shell:Fi(o,r),V.multiplyScalar(a,o,Math.sin(s)),a.z=-Math.cos(s),F.normalize(a,a),V.multiplyScalar(o,o,e),o.z=0;break;case _i.Volume:zi(o,e*(1-i),e,r),V.multiplyScalar(a,o,Math.sin(s)),a.z=-Math.cos(s)*e,F.normalize(a,a),o.z=0,F.add(o,o,F.multiplyScalar(wn,a,n*G()/-a.z));break;default:u(t+" is not supported for cone emitter.")}}function zn(t,e,i,r){switch(t){case _i.Volume:s=i,n=On,F.set(s,N(-n.x,n.x),N(-n.y,n.y),N(-n.z,n.z));break;case _i.Shell:An[0]=N(-.5,.5),An[1]=N(-.5,.5),An[2]=.5*Pi(),Di(An),Dn(An,e),F.set(i,An[0],An[1],An[2]);break;case _i.Edge:An[0]=N(-.5,.5),An[1]=.5*Pi(),An[2]=.5*Pi(),Di(An),Dn(An,e),F.set(i,An[0],An[1],An[2]);break;default:u(t+" is not supported for box emitter.")}var s,n;F.copy(r,Oi)}function Dn(t,e){e.x>0&&(t[0]+=.5*N(-e.x,e.x),t[0]=Z(t[0],-.5,.5)),e.y>0&&(t[1]+=.5*N(-e.y,e.y),t[1]=Z(t[1],-.5,.5)),e.z>0&&(t[2]+=.5*N(-e.z,e.z),t[2]=Z(t[2],-.5,.5))}var Pn=[0,0,1,0,0,1,1,1],In=[0,0,0,1,0,0,0,1,0,1,1,0],Bn=function(t){function r(){var e;return(e=t.call(this)||this)._capacity=void 0,e._bufferSize=void 0,e._vertAttrs=void 0,e._vertAttribSize=void 0,e._vBuffer=void 0,e._vertAttrsFloatCount=void 0,e._vdataF32=void 0,e._vdataUint32=void 0,e._subMeshData=void 0,e._mesh=void 0,e._vertCount=0,e._indexCount=0,e._startTimeOffset=0,e._lifeTimeOffset=0,e._material=null,e._vertAttribSizeStatic=void 0,e._vertStaticAttrsFloatCount=void 0,e._insBuffers=void 0,e._insIndices=void 0,e._useInstance=void 0,e._iaVertCount=0,e._iaIndexCount=0,e.type=mt.PARTICLE_BATCH,e._capacity=0,e._bufferSize=16,e._vertAttrs=null,e._vertAttribSize=0,e._vBuffer=null,e._vertAttrsFloatCount=0,e._vdataF32=null,e._vdataUint32=null,e._vertAttribSizeStatic=0,e._vertStaticAttrsFloatCount=0,e._insBuffers=[],e._insIndices=null,Mt.gfxDevice.hasFeature(Rt.INSTANCED_ARRAYS)?e._useInstance=!0:e._useInstance=!1,e._subMeshData=null,e._mesh=null,e}e(r,t);var s=r.prototype;return s.setCapacity=function(t){var e=this._capacity!==t;this._capacity=t,this._bufferSize=Math.max(this._capacity,16),this._subMeshData&&e&&this.rebuild()},s.setVertexAttributes=function(t,e){if(this._useInstance)this.setVertexAttributesIns(t,e);else{if(this._mesh===t&&this._vertAttrs===e)return;this._mesh=t,this._vertAttrs=e,this._vertAttribSize=0;for(var i,r=h(this._vertAttrs);!(i=r()).done;){var s=i.value;s.offset=this._vertAttribSize,this._vertAttribSize+=Tt[s.format].size}this._vertAttrsFloatCount=this._vertAttribSize/4,this.rebuild()}},s.setVertexAttributesIns=function(t,e){if(this._mesh!==t||this._vertAttrs!==e){this._mesh=t,this._vertAttrs=e,this._vertAttribSize=0,this._vertAttribSizeStatic=0;for(var i,r=h(this._vertAttrs);!(i=r()).done;){var s=i.value;0===s.stream?(s.offset=this._vertAttribSize,this._vertAttribSize+=Tt[s.format].size):1===s.stream&&(s.offset=this._vertAttribSizeStatic,this._vertAttribSizeStatic+=Tt[s.format].size)}this._vertAttrsFloatCount=this._vertAttribSize/4,this._vertStaticAttrsFloatCount=this._vertAttribSizeStatic/4,this.rebuild()}},s.createSubMeshData=function(){this.destroySubMeshData(),this._vertCount=4,this._indexCount=6,this._mesh&&(this._vertCount=this._mesh.struct.vertexBundles[this._mesh.struct.primitives[0].vertexBundelIndices[0]].view.count,this._indexCount=this._mesh.struct.primitives[0].indexView.count);var t=this._device.createBuffer(new wt(At.VERTEX|At.TRANSFER_DST,Ot.HOST|Ot.DEVICE,this._vertAttribSize*this._bufferSize*this._vertCount,this._vertAttribSize)),e=new ArrayBuffer(this._vertAttribSize*this._bufferSize*this._vertCount);if(this._mesh&&this._capacity>0){var i=this._vertAttrs[this._vertAttrs.findIndex((function(t){return t.name===bt.ATTR_TEX_COORD}))].offset;this._mesh.copyAttribute(0,bt.ATTR_TEX_COORD,e,this._vertAttribSize,i);var r=this._vertAttrs.findIndex((function(t){return t.name===bt.ATTR_TEX_COORD3}));if(i=this._vertAttrs[r++].offset,this._mesh.copyAttribute(0,bt.ATTR_POSITION,e,this._vertAttribSize,i),i=this._vertAttrs[r++].offset,this._mesh.copyAttribute(0,bt.ATTR_NORMAL,e,this._vertAttribSize,i),i=this._vertAttrs[r++].offset,!this._mesh.copyAttribute(0,bt.ATTR_COLOR,e,this._vertAttribSize,i))for(var s=new Uint32Array(e),n=0;n<this._vertCount;++n)s[n*this._vertAttrsFloatCount+i/4]=T.toUint32(T.WHITE);for(var o=new Float32Array(e),a=1;a<this._capacity;a++)o.copyWithin(a*this._vertAttribSize*this._vertCount/4,0,this._vertAttribSize*this._vertCount/4)}t.update(e);var l=new Uint16Array(this._bufferSize*this._indexCount);if(this._mesh&&this._capacity>0){this._mesh.copyIndices(0,l);for(var u=1;u<this._capacity;u++)for(var h=0;h<this._indexCount;h++)l[u*this._indexCount+h]=l[h]+u*this._vertCount}else for(var c=0,_=0;_<this._capacity;++_){var d=4*_;l[c++]=d,l[c++]=d+1,l[c++]=d+2,l[c++]=d+3,l[c++]=d+2,l[c++]=d+1}var p=this._device.createBuffer(new wt(At.INDEX|At.TRANSFER_DST,Ot.DEVICE,this._bufferSize*this._indexCount*Uint16Array.BYTES_PER_ELEMENT,Uint16Array.BYTES_PER_ELEMENT));return p.update(l),this._iaVertCount=this._capacity*this._vertCount,this._iaIndexCount=this._capacity*this._indexCount,this._subMeshData=new yt([t],this._vertAttrs,gt.TRIANGLE_LIST,p),this.initSubModel(0,this._subMeshData,this._material),e},s.createSubMeshDataInsDynamic=function(){this._insBuffers.length=0,this.destroySubMeshData();var t=this._device.createBuffer(new wt(At.VERTEX|At.TRANSFER_DST,Ot.HOST|Ot.DEVICE,this._vertAttribSize*this._bufferSize,this._vertAttribSize)),e=new ArrayBuffer(this._vertAttribSize*this._bufferSize);return t.update(e),this._insBuffers.push(t),e},s.createSubMeshDataInsStatic=function(){this._vertCount=4,this._indexCount=6,this._mesh&&(this._vertCount=this._mesh.struct.vertexBundles[this._mesh.struct.primitives[0].vertexBundelIndices[0]].view.count,this._indexCount=this._mesh.struct.primitives[0].indexView.count);var t=this._device.createBuffer(new wt(At.VERTEX|At.TRANSFER_DST,Ot.HOST|Ot.DEVICE,this._vertAttribSizeStatic*this._vertCount,this._vertAttribSizeStatic)),e=new ArrayBuffer(this._vertAttribSizeStatic*this._vertCount);if(this._mesh){var i=this._vertAttrs.findIndex((function(t){return t.name===bt.ATTR_TEX_COORD})),r=this._vertAttrs[i].offset;if(this._mesh.copyAttribute(0,bt.ATTR_TEX_COORD,e,this._vertAttribSizeStatic,r),i=this._vertAttrs.findIndex((function(t){return t.name===bt.ATTR_TEX_COORD3})),r=this._vertAttrs[i++].offset,this._mesh.copyAttribute(0,bt.ATTR_POSITION,e,this._vertAttribSizeStatic,r),r=this._vertAttrs[i++].offset,this._mesh.copyAttribute(0,bt.ATTR_NORMAL,e,this._vertAttribSizeStatic,r),r=this._vertAttrs[i++].offset,!this._mesh.copyAttribute(0,bt.ATTR_COLOR,e,this._vertAttribSizeStatic,r))for(var s=new Uint32Array(e),n=0;n<this._vertCount;++n)s[n*this._vertStaticAttrsFloatCount+r/4]=T.toUint32(T.WHITE)}else for(var o=new Float32Array(e),a=0;a<In.length;++a)o[a]=In[a];t.update(e);var l=new Uint16Array(this._indexCount);this._mesh?this._mesh.copyIndices(0,l):(l[0]=0,l[1]=1,l[2]=2,l[3]=3,l[4]=2,l[5]=1);var u=this._device.createBuffer(new wt(At.INDEX|At.TRANSFER_DST,Ot.DEVICE,this._indexCount*Uint16Array.BYTES_PER_ELEMENT,Uint16Array.BYTES_PER_ELEMENT));u.update(l),this._insIndices=u,this._iaVertCount=this._vertCount,this._iaIndexCount=this._indexCount,this._insBuffers.push(t)},s.createInsSubmesh=function(){this._subMeshData=new yt(this._insBuffers,this._vertAttrs,gt.TRIANGLE_LIST,this._insIndices),this.initSubModel(0,this._subMeshData,this._material)},s.updateMaterial=function(t){this._material=t,this.setSubModelMaterial(0,t)},s.addParticleVertexData=function(t,e){if(this._useInstance)this.addParticleVertexDataIns(t,e);else if(this._mesh)for(var i=0;i<this._vertCount;i++){var r=(t*this._vertCount+i)*this._vertAttrsFloatCount;this._vdataF32[r++]=e.position.x,this._vdataF32[r++]=e.position.y,this._vdataF32[r++]=e.position.z,r+=2,this._vdataF32[r++]=e.texcoord.z,this._vdataF32[r++]=e.size.x,this._vdataF32[r++]=e.size.y,this._vdataF32[r++]=e.size.z,this._vdataF32[r++]=e.rotation.x,this._vdataF32[r++]=e.rotation.y,this._vdataF32[r++]=e.rotation.z,this._vdataUint32[r++]=e.color}else{var s=t*this._vertAttrsFloatCount;this._vdataF32[s++]=e.position.x,this._vdataF32[s++]=e.position.y,this._vdataF32[s++]=e.position.z,this._vdataF32[s++]=e.texcoord.x,this._vdataF32[s++]=e.texcoord.y,this._vdataF32[s++]=e.texcoord.z,this._vdataF32[s++]=e.size.x,this._vdataF32[s++]=e.size.y,this._vdataF32[s++]=e.size.z,this._vdataF32[s++]=e.rotation.x,this._vdataF32[s++]=e.rotation.y,this._vdataF32[s++]=e.rotation.z,this._vdataUint32[s++]=e.color,e.velocity&&(this._vdataF32[s++]=e.velocity.x,this._vdataF32[s++]=e.velocity.y,this._vdataF32[s++]=e.velocity.z)}},s.addParticleVertexDataIns=function(t,e){var i=t*this._vertAttrsFloatCount;this._mesh?(this._vdataF32[i++]=e.position.x,this._vdataF32[i++]=e.position.y,this._vdataF32[i++]=e.position.z,this._vdataF32[i++]=e.texcoord.z,this._vdataF32[i++]=e.size.x,this._vdataF32[i++]=e.size.y,this._vdataF32[i++]=e.size.z,this._vdataF32[i++]=e.rotation.x,this._vdataF32[i++]=e.rotation.y,this._vdataF32[i++]=e.rotation.z,this._vdataUint32[i++]=e.color):(this._vdataF32[i++]=e.position.x,this._vdataF32[i++]=e.position.y,this._vdataF32[i++]=e.position.z,this._vdataF32[i++]=e.texcoord.z,this._vdataF32[i++]=e.size.x,this._vdataF32[i++]=e.size.y,this._vdataF32[i++]=e.size.z,this._vdataF32[i++]=e.rotation.x,this._vdataF32[i++]=e.rotation.y,this._vdataF32[i++]=e.rotation.z,this._vdataUint32[i++]=e.color,e.velocity&&(this._vdataF32[i++]=e.velocity.x,this._vdataF32[i++]=e.velocity.y,this._vdataF32[i++]=e.velocity.z))},s.addGPUParticleVertexData=function(t,e,i){if(this._useInstance)this.addGPUParticleVertexDataIns(t,e,i);else for(var r=e*this._vertAttrsFloatCount*this._vertCount,s=0;s<this._vertCount;s++){var n=r;this._vdataF32[n++]=t.position.x,this._vdataF32[n++]=t.position.y,this._vdataF32[n++]=t.position.z,this._vdataF32[n++]=i,this._vdataF32[n++]=t.startSize.x,this._vdataF32[n++]=t.startSize.y,this._vdataF32[n++]=t.startSize.z,this._vdataF32[n++]=Pn[2*s],this._vdataF32[n++]=t.rotation.x,this._vdataF32[n++]=t.rotation.y,this._vdataF32[n++]=t.rotation.z,this._vdataF32[n++]=Pn[2*s+1],this._vdataF32[n++]=t.startColor.r/255,this._vdataF32[n++]=t.startColor.g/255,this._vdataF32[n++]=t.startColor.b/255,this._vdataF32[n++]=t.startColor.a/255,this._vdataF32[n++]=t.velocity.x,this._vdataF32[n++]=t.velocity.y,this._vdataF32[n++]=t.velocity.z,this._vdataF32[n++]=t.startLifetime,this._vdataF32[n++]=t.randomSeed,r+=this._vertAttrsFloatCount}},s.addGPUParticleVertexDataIns=function(t,e,i){var r=e*this._vertAttrsFloatCount,s=r;this._vdataF32[s++]=t.position.x,this._vdataF32[s++]=t.position.y,this._vdataF32[s++]=t.position.z,this._vdataF32[s++]=i,this._vdataF32[s++]=t.startSize.x,this._vdataF32[s++]=t.startSize.y,this._vdataF32[s++]=t.startSize.z,this._vdataF32[s++]=t.frameIndex,this._vdataF32[s++]=t.rotation.x,this._vdataF32[s++]=t.rotation.y,this._vdataF32[s++]=t.rotation.z,this._vdataF32[s++]=t.startColor.r/255,this._vdataF32[s++]=t.startColor.g/255,this._vdataF32[s++]=t.startColor.b/255,this._vdataF32[s++]=t.startColor.a/255,this._vdataF32[s++]=t.velocity.x,this._vdataF32[s++]=t.velocity.y,this._vdataF32[s++]=t.velocity.z,this._vdataF32[s++]=t.startLifetime,this._vdataF32[s++]=t.randomSeed,r+=this._vertAttrsFloatCount},s.updateGPUParticles=function(t,e,i){if(this._useInstance)return this.updateGPUParticlesIns(t,e,i);for(var r=this._vertAttrsFloatCount*this._vertCount,s=0,n=0,o=0,a=0;a<t;++a)s=a*r,n=this._vdataF32[s+this._startTimeOffset],this._vdataF32[s+this._lifeTimeOffset]-(e-n)<i&&(o=--t*r,this._vdataF32.copyWithin(s,o,o+r),a--);return t},s.updateGPUParticlesIns=function(t,e,i){for(var r=this._vertAttrsFloatCount,s=0,n=0,o=0,a=0;a<t;++a)s=a*r,n=this._vdataF32[s+this._startTimeOffset],this._vdataF32[s+this._lifeTimeOffset]-(e-n)<i&&(o=--t*r,this._vdataF32.copyWithin(s,o,o+r),a--);return t},s.constructAttributeIndex=function(){if(this._vertAttrs){var t=this._vertAttrs.findIndex((function(t){return"a_position_starttime"===t.name})),e=this._vertAttrs[t].offset;this._startTimeOffset=e/4+3,t=this._vertAttrs.findIndex((function(t){return"a_dir_life"===t.name})),e=this._vertAttrs[t].offset,this._lifeTimeOffset=e/4+3}},s.updateIA=function(t){if(this._useInstance)this.updateIAIns(t);else{if(t<=0)return;var e=this._subModels[0].inputAssembler;e.vertexBuffers[0].update(this._vdataF32),e.firstIndex=0,e.indexCount=this._indexCount*t,e.vertexCount=this._iaVertCount}},s.updateIAIns=function(t){if(!(t<=0)){var e=this._subModels[0].inputAssembler;e.vertexBuffers[0].update(this._vdataF32),e.instanceCount=t,e.firstIndex=0,e.indexCount=this._indexCount,e.instanceCount=t,e.vertexCount=this._iaVertCount}},s.clear=function(){this._useInstance?this.clearIns():this._subModels[0].inputAssembler.indexCount=0},s.clearIns=function(){this._subModels[0].inputAssembler.instanceCount=0},s.destroy=function(){t.prototype.destroy.call(this),this.doDestroy()},s.doDestroy=function(){this._vBuffer=null,this._vdataF32=null,this._vdataUint32=null,this._insBuffers=[],this._insIndices=null,this._vertAttrs=null,this._material=null,this._mesh=null,this.destroySubMeshData()},s.rebuild=function(){this._useInstance?this.rebuildIns():(this._vBuffer=this.createSubMeshData(),this._vdataF32=new Float32Array(this._vBuffer),this._vdataUint32=new Uint32Array(this._vBuffer))},s.rebuildIns=function(){this._vBuffer=this.createSubMeshDataInsDynamic(),this._vdataF32=new Float32Array(this._vBuffer),this._vdataUint32=new Uint32Array(this._vBuffer),this.createSubMeshDataInsStatic(),this.createInsSubmesh()},s.destroySubMeshData=function(){this._subMeshData&&(this._subMeshData.destroy(),this._subMeshData=null)},i(r,[{key:"useInstance",get:function(){return this._useInstance},set:function(t){this._useInstance!==t&&(this._useInstance=t)}}]),r}(ft),Ln=function(){function t(t){this._particleSystem=null,this._model=null,this._renderInfo=null,this._vertAttrs=[],this._useInstance=void 0,this._renderInfo=t,Mt.gfxDevice.hasFeature(Rt.INSTANCED_ARRAYS)?this._useInstance=!0:this._useInstance=!1}var e=t.prototype;return e.getUseInstance=function(){return this._useInstance},e.getInfo=function(){return this._renderInfo},e.onInit=function(t){this._particleSystem=t},e.onEnable=function(){if(this._particleSystem){this.attachToScene();var t=this._model;t&&(t.node=t.transform=this._particleSystem.node)}},e.onDisable=function(){this.detachFromScene()},e.onDestroy=function(){this._model&&(et.director.root.destroyModel(this._model),this._model=null)},e.attachToScene=function(){var t;this._model&&(this._model.scene&&this.detachFromScene(),null==(t=this._particleSystem)||t._getRenderScene().addModel(this._model))},e.detachFromScene=function(){this._model&&this._model.scene&&this._model.scene.removeModel(this._model)},e.setVertexAttributes=function(){this._model&&(this.updateVertexAttrib(),this._model.setVertexAttributes(this._renderInfo.renderMode===hi.Mesh?this._renderInfo.mesh:null,this._vertAttrs))},e.clear=function(){this._model&&(this._model.enabled=!1)},e.getModel=function(){return this._model},e._initModel=function(){!this._model&&this._particleSystem&&(this._model=et.director.root.createModel(Bn),this._model.setCapacity(this._particleSystem.capacity),this._model.visFlags=this._particleSystem.visibility)},e.updateTrailMaterial=function(){},e.getDefaultTrailMaterial=function(){return null},i(t,[{key:"model",get:function(){return this._model}}]),t}(),Vn=function(){function t(t){this.permutation=[151,160,137,91,90,15,131,13,201,95,96,53,194,233,7,225,140,36,103,30,69,142,8,99,37,240,21,10,23,190,6,148,247,120,234,75,0,26,197,62,94,252,219,203,117,35,11,32,57,177,33,88,237,149,56,87,174,20,125,136,171,168,68,175,74,165,71,134,139,48,27,166,77,146,158,231,83,111,229,122,60,211,133,230,220,105,92,41,55,46,245,40,244,102,143,54,65,25,63,161,1,216,80,73,209,76,132,187,208,89,18,169,200,196,135,130,116,188,159,86,164,100,109,198,173,186,3,64,52,217,226,250,124,123,5,202,38,147,118,126,255,82,85,212,207,206,59,227,47,16,58,17,182,189,28,42,223,183,170,213,119,248,152,2,44,154,163,70,221,153,101,155,167,43,172,9,129,22,39,253,19,98,108,110,79,113,224,232,178,185,112,104,218,246,97,228,251,34,242,193,238,210,144,12,191,179,162,241,81,51,145,235,249,14,239,107,49,192,214,31,181,199,106,157,184,84,204,176,115,121,50,45,127,4,150,254,138,236,205,93,222,114,67,29,24,72,243,141,128,195,78,66,215,61,156,180],this.accSpeed=new F,this.noiseSpeed=new F,this.noiseFrequency=0,this.noiseAbs=new F,this.noiseAmplitude=new F,this.octaves=new F,this.dt=0,this.point=new F,this.result=new F,this.mixOut=new V,t&&(this.permutation=t)}var e=t.prototype;return e.noise=function(t,e,i,r,s){void 0===r&&(r=0),void 0===s&&(s=1);for(var n=new Array(512),o=0;o<256;o++)n[256+o]=n[o]=this.permutation[o];var a=255&Math.floor(t),l=255&Math.floor(e),u=255&Math.floor(i);t-=Math.floor(t),e-=Math.floor(e),i-=Math.floor(i);var h=this.fade(t),c=this.fade(e),_=this.fade(i),d=n[a]+l,p=n[d]+u,f=n[d+1]+u,m=n[a+1]+l,v=n[m]+u,y=n[m+1]+u;return r+this.scale(this.lerp(_,this.lerp(c,this.lerp(h,this.grad(n[p],t,e,i),this.grad(n[v],t-1,e,i)),this.lerp(h,this.grad(n[f],t,e-1,i),this.grad(n[y],t-1,e-1,i))),this.lerp(c,this.lerp(h,this.grad(n[p+1],t,e,i-1),this.grad(n[v+1],t-1,e,i-1)),this.lerp(h,this.grad(n[f+1],t,e-1,i-1),this.grad(n[y+1],t-1,e-1,i-1)))))*(s-r)},e.fade=function(t){return t*t*t*(t*(6*t-15)+10)},e.lerp=function(t,e,i){return e+t*(i-e)},e.grad=function(t,e,i,r){var s=15&t,n=s<8?e:i,o=s<4?i:12===s||14===s?e:r;return(1&s?-n:n)+(2&s?-o:o)},e.scale=function(t){return(1+t)/2},e.setSpeed=function(t,e,i){this.noiseSpeed.set(t,e,i)},e.setFrequency=function(t){this.noiseFrequency=t},e.setAbs=function(t,e,i){this.noiseAbs.set(t,e,i)},e.setAmplititude=function(t,e,i){this.noiseAmplitude.set(t,e,i)},e.setOctaves=function(t,e,i){this.octaves.set(t,e,i)},e.setTime=function(t){this.dt=t},e.setSamplePoint=function(t){this.point.set(t)},e.getResult=function(){return this.result},e.getNoise=function(t,e,i,r,s,n,o){var a=n,l=0;if(l+=this.noise(t*a,e*a,i*a,-1,1),1===o.x)return l;for(var u=1,h=1,c=1;c<o.x;++c)u*=o.y,a*=o.z,h+=u,l+=this.noise(t*a,e*a,i*a,-1,1)*u;return l/h},e.getNoiseMix=function(t,e,i,r,s,n){t.x=this.getNoise(e.x,e.y,e.z,i,r,s,n),t.y=this.getNoise(e.y,e.z,e.x,i,r,s,n)},e.getNoiseParticle=function(){this.accSpeed.set(this.noiseSpeed.x*this.dt,this.noiseSpeed.y*this.dt,this.noiseSpeed.z*this.dt);var t=this.getNoise(this.point.z+this.accSpeed.x,this.point.y,this.point.x,this.dt,this.accSpeed,this.noiseFrequency,this.octaves),e=this.getNoise(this.point.x+1e3,this.point.z+this.accSpeed.y,this.point.y,this.dt,this.accSpeed,this.noiseFrequency,this.octaves),i=this.getNoise(this.point.y,this.point.x+1e3,this.point.z+this.accSpeed.z,this.dt,this.accSpeed,this.noiseFrequency,this.octaves);this.result.set(t*this.noiseAmplitude.x,e*this.noiseAmplitude.y,i*this.noiseAmplitude.z)},e.getPreview=function(t,e,i){for(var r=0;r<i;++r)for(var s=0;s<e;++s){var n=(s-.5*e)/e+this.noiseSpeed.x*this.dt,o=(r-.5*i)/i+this.noiseSpeed.y*this.dt,a=this.getNoise(n,o,0,this.dt,this.accSpeed,this.noiseFrequency,this.octaves);t[r*e+s]=.5*(a+1)}},t}(),Un=new x,kn=q(),Nn=new k,Gn=new k,Hn=new U,Xn=["_colorOverLifetimeModule","_sizeOvertimeModule","_velocityOvertimeModule","_forceOvertimeModule","_limitVelocityOvertimeModule","_rotationOvertimeModule","_textureAnimationModule","_noiseModule"],Wn=[0,0,1,0,0,1,1,1],jn="CC_USE_WORLD_SPACE",Yn="CC_RENDER_MODE",Zn=bt.ATTR_POSITION,qn=bt.ATTR_NORMAL,Qn=bt.ATTR_COLOR,Kn=bt.ATTR_COLOR1,Jn=bt.ATTR_TEX_COORD,$n=bt.ATTR_TEX_COORD1,to=bt.ATTR_TEX_COORD2,eo=bt.ATTR_TEX_COORD3,io=bt.ATTR_TEX_COORD4;function ro(t,e,i,r,s,n){return void 0===i&&(i=!1),void 0===r&&(r=0),void 0===s&&(s=!1),void 0===n&&(n=0),new St(t,e,i,r,s,n)}var so=[ro(Zn,xt.RGB32F),ro(Jn,xt.RGB32F),ro($n,xt.RGB32F),ro(to,xt.RGB32F),ro(Qn,xt.RGBA8,!0)],no=[ro(Zn,xt.RGB32F),ro(Jn,xt.RGB32F),ro($n,xt.RGB32F),ro(to,xt.RGB32F),ro(Qn,xt.RGBA8,!0),ro(Kn,xt.RGB32F)],oo=[ro(Zn,xt.RGB32F),ro(Jn,xt.RGB32F),ro($n,xt.RGB32F),ro(to,xt.RGB32F),ro(Qn,xt.RGBA8,!0),ro(eo,xt.RGB32F),ro(qn,xt.RGB32F),ro(Kn,xt.RGBA8,!0)],ao=[ro(io,xt.RGBA32F,!1,0,!0),ro($n,xt.RGB32F,!1,0,!0),ro(to,xt.RGB32F,!1,0,!0),ro(Qn,xt.RGBA8,!0,0,!0),ro(Jn,xt.RGB32F,!1,1)],lo=[ro(io,xt.RGBA32F,!1,0,!0),ro($n,xt.RGB32F,!1,0,!0),ro(to,xt.RGB32F,!1,0,!0),ro(Qn,xt.RGBA8,!0,0,!0),ro(Kn,xt.RGB32F,!1,0,!0),ro(Jn,xt.RGB32F,!1,1)],uo=[ro(io,xt.RGBA32F,!1,0,!0),ro($n,xt.RGB32F,!1,0,!0),ro(to,xt.RGB32F,!1,0,!0),ro(Qn,xt.RGBA8,!0,0,!0),ro(Jn,xt.RGB32F,!1,1),ro(eo,xt.RGB32F,!1,1),ro(qn,xt.RGB32F,!1,1),ro(Kn,xt.RGBA8,!0,1)],ho={parent:null,owner:null,subModelIdx:0},co=function(){this.position=void 0,this.texcoord=void 0,this.size=void 0,this.rotation=void 0,this.color=void 0,this.velocity=void 0,this.position=q(),this.texcoord=q(),this.size=q(),this.rotation=q(),this.color=0,this.velocity=null},_o=function(t){function i(e){var i;return(i=t.call(this,e)||this)._defines=void 0,i._trailDefines=void 0,i._frameTile_velLenScale=void 0,i._tmp_velLenScale=void 0,i._defaultMat=null,i._node_scale=void 0,i._particleVertexData=void 0,i._particles=null,i._defaultTrailMat=null,i._updateList=new Map,i._animateList=new Map,i._runAnimateList=[],i._fillDataFunc=null,i._uScaleHandle=0,i._uLenHandle=0,i._uNodeRotHandle=0,i._alignSpace=ui.View,i._inited=!1,i._localMat=new k,i._gravity=new x,i.noise=new Vn,i._model=null,i._frameTile_velLenScale=new x(1,1,0,0),i._tmp_velLenScale=i._frameTile_velLenScale.clone(),i._node_scale=q(),i._particleVertexData=new co,i._defines={CC_USE_WORLD_SPACE:!0,CC_USE_BILLBOARD:!0,CC_USE_STRETCHED_BILLBOARD:!1,CC_USE_HORIZONTAL_BILLBOARD:!1,CC_USE_VERTICAL_BILLBOARD:!1},i._trailDefines={CC_USE_WORLD_SPACE:!0},i}e(i,t);var r=i.prototype;return r.onInit=function(e){var i=this;t.prototype.onInit.call(this,e),this._particles=new c((function(){return new oi(i)}),16),this._setVertexAttrib(),this._setFillFunc(),this._initModuleList(),this._initModel(),this.updateMaterialParams(),this.updateTrailMaterial(),this.setVertexAttributes(),this._inited=!0},r.clear=function(){t.prototype.clear.call(this),this._particles.reset(),this._particleSystem&&this._particleSystem._trailModule&&this._particleSystem._trailModule.clear(),this.updateRenderData(),this._model.enabled=!1},r.updateRenderMode=function(){this._setVertexAttrib(),this._setFillFunc(),this.updateMaterialParams(),this.setVertexAttributes()},r.onDestroy=function(){var e;null==(e=this._particles)||e.destroy(),t.prototype.onDestroy.call(this)},r.getFreeParticle=function(){return this._particleSystem&&this._particles.length>=this._particleSystem.capacity?null:this._particles.add()},r.getDefaultTrailMaterial=function(){return this._defaultTrailMat},r.setNewParticle=function(){},r._initModuleList=function(){var t=this;Xn.forEach((function(e){if(t._particleSystem){var i=t._particleSystem[e];i&&i.enable&&(i.needUpdate&&t._updateList.set(i.name,i),i.needAnimate&&t._animateList.set(i.name,i))}})),this._runAnimateList.length=0;for(var e=0,i=yi.length;e<i;e++){var r=this._animateList.get(yi[e]);r&&this._runAnimateList.push(r)}},r.enableModule=function(t,e,i){e?(i.needUpdate&&this._updateList.set(i.name,i),i.needAnimate&&this._animateList.set(i.name,i)):(this._animateList.delete(t),this._updateList.delete(t)),this._runAnimateList.length=0;for(var r=0,s=yi.length;r<s;r++){var n=this._animateList.get(yi[r]);n&&this._runAnimateList.push(n)}this.updateMaterialParams()},r.updateAlignSpace=function(t){this._alignSpace=t},r.getDefaultMaterial=function(){return this._defaultMat},r.updateRotation=function(t){t&&this.doUpdateRotation(t)},r.doUpdateRotation=function(t){if(this._renderInfo.renderMode===hi.Mesh||this._alignSpace!==ui.View){var e;if(this._alignSpace===ui.Local)null==(e=this._particleSystem)||e.node.getRotation(Hn);else if(this._alignSpace===ui.World){var i;null==(i=this._particleSystem)||i.node.getWorldRotation(Hn)}else if(this._alignSpace===ui.View){var r,s;Hn.set(0,0,0,1);var n=null==(r=this._particleSystem)||null==(s=r.node.scene.renderScene)?void 0:s.cameras;if(void 0!==n)for(var o=0;o<(null==n?void 0:n.length);++o){var a=n[o];if((a.visibility&this._particleSystem.node.layer)===this._particleSystem.node.layer){U.fromViewUp(Hn,a.forward);break}}}else Hn.set(0,0,0,1);t.setUniform(this._uNodeRotHandle,Hn)}},r.updateScale=function(t){t&&this.doUpdateScale(t)},r.doUpdateScale=function(t){var e,i,r,s=this._node_scale;switch(null==(e=this._particleSystem)?void 0:e.scaleSpace){case ai.Local:null==(i=this._particleSystem)||i.node.getScale(s);break;case ai.World:null==(r=this._particleSystem)||r.node.getWorldScale(s)}t.setUniform(this._uScaleHandle,Un.set(s.x,s.y,s.z))},r.updateParticles=function(t){var e=this,i=this._particleSystem;if(!i)return this._particles.length;i.node.getWorldMatrix(Nn);var r=(i.getMaterialInstance(0)||this._defaultMat).passes[0];this.doUpdateScale(r),this.doUpdateRotation(r),this._updateList.forEach((function(){}));var s=i._trailModule,n=s&&s.enable;n&&s.update();var o=!i.gravityModifier.isZero();if(o){if(i.simulationSpace===ai.Local){var a=i.node.getRotation();k.fromQuat(this._localMat,a),this._localMat.transpose()}if(i.node.parent){var l=i.node.parent.worldRotation;k.fromQuat(Gn,l),Gn.transpose()}}for(var u=function(r){var a=e._particles.data[r];if(a.remainingLifetime-=t,F.set(a.animatedVelocity,0,0,0),a.remainingLifetime<0)return n&&s.removeParticle(a),e._particles.removeAt(r),--r,h=r,1;if(o){var l=Ii(i.gravityModifier)?X(a.randomSeed):0;if(i.simulationSpace===ai.Local){var u=1-a.remainingLifetime/a.startLifetime,c=9.8*-i.gravityModifier.evaluate(u,l)*t;e._gravity.x=0,e._gravity.y=c,e._gravity.z=0,e._gravity.w=1,E(c,0,P)||(i.node.parent&&(e._gravity=e._gravity.transformMat4(Gn)),e._gravity=e._gravity.transformMat4(e._localMat),a.velocity.x+=e._gravity.x,a.velocity.y+=e._gravity.y,a.velocity.z+=e._gravity.z)}else a.velocity.y-=9.8*i.gravityModifier.evaluate(1-a.remainingLifetime/a.startLifetime,l)*t}F.copy(a.ultimateVelocity,a.velocity),e._runAnimateList.forEach((function(e){e.animate(a,t)})),F.scaleAndAdd(a.position,a.position,a.ultimateVelocity,t),n&&s.animate(a,t),h=r},h=0;h<this._particles.length;++h)u(h);return this._model.enabled=this._particles.length>0,this._particles.length},r.getNoisePreview=function(t,e,i){var r=this;this._runAnimateList.forEach((function(s){s.name===vi&&s.getNoisePreview(t,r._particleSystem,e,i)}))},r.updateRenderData=function(){for(var t=0,e=0;e<this._particles.length;++e){var i=this._particles.data[e],r=0,s=this._particleSystem._textureAnimationModule;s&&s.enable&&(r=i.frameIndex),t=4*e,this._fillDataFunc(i,t,r)}},r.beforeRender=function(){this._model.updateIA(this._particles.length)},r.getParticleCount=function(){return this._particles.length},r.onMaterialModified=function(t){this._inited&&(0===t?this.updateMaterialParams():this.updateTrailMaterial())},r.onRebuildPSO=function(t,e){this._model&&0===t&&this._model.setSubModelMaterial(0,e);var i=this._particleSystem._trailModule,r=null==i?void 0:i.getModel();r&&1===t&&r.setSubModelMaterial(0,e)},r._setFillFunc=function(){this._renderInfo.renderMode===hi.Mesh?this._fillDataFunc=this._fillMeshData:this._renderInfo.renderMode===hi.StrecthedBillboard?this._fillDataFunc=this._fillStrecthedData:this._fillDataFunc=this._fillNormalData},r._fillMeshData=function(t,e,i){var r=this._particleVertexData,s=e/4;F.copy(r.position,t.position),kn.z=i,F.copy(r.texcoord,kn),F.copy(r.size,t.size),F.copy(r.rotation,t.rotation),r.color=T.toUint32(t.color),this._model.addParticleVertexData(s,r)},r._fillStrecthedData=function(t,e,i){var r=this._particleVertexData;if(this._useInstance)this._fillStrecthedDataIns(t,e,i);else for(var s=0;s<4;++s)F.copy(r.position,t.position),kn.x=Wn[2*s],kn.y=Wn[2*s+1],kn.z=i,F.copy(r.texcoord,kn),F.copy(r.size,t.size),F.copy(r.rotation,t.rotation),r.color=T.toUint32(t.color),r.velocity=t.ultimateVelocity,this._model.addParticleVertexData(e++,r)},r._fillStrecthedDataIns=function(t,e,i){var r=this._particleVertexData,s=e/4;F.copy(r.position,t.position),kn.z=i,F.copy(r.texcoord,kn),F.copy(r.size,t.size),F.copy(r.rotation,t.rotation),r.color=T.toUint32(t.color),r.velocity=t.ultimateVelocity,this._model.addParticleVertexData(s,r)},r._fillNormalData=function(t,e,i){var r=this._particleVertexData;if(this._useInstance)this._fillNormalDataIns(t,e,i);else for(var s=0;s<4;++s)F.copy(r.position,t.position),kn.x=Wn[2*s],kn.y=Wn[2*s+1],kn.z=i,F.copy(r.texcoord,kn),F.copy(r.size,t.size),F.copy(r.rotation,t.rotation),this._particleVertexData.color=T.toUint32(t.color),this._model.addParticleVertexData(e++,r)},r._fillNormalDataIns=function(t,e,i){var r=this._particleVertexData,s=e/4;F.copy(r.position,t.position),kn.z=i,F.copy(r.texcoord,kn),F.copy(r.size,t.size),F.copy(r.rotation,t.rotation),this._particleVertexData.color=T.toUint32(t.color),this._model.addParticleVertexData(s,r)},r.updateVertexAttrib=function(){if(this._renderInfo.renderMode===hi.Mesh&&this._renderInfo.mesh){var t=this._renderInfo.mesh.readAttributeFormat(0,bt.ATTR_COLOR);if(t){for(var e=xt.RGBA8,i=0;i<Tt.length;++i)if(Tt[i].name===t.name){e=i;break}this._vertAttrs[7]=ro(Kn,e,!0,this._useInstance?1:0)}else{var r=xt.RGBA8;this._vertAttrs[7]=ro(Kn,r,!0,this._useInstance?1:0)}}},r._setVertexAttrib=function(){if(this._useInstance)this._setVertexAttribIns();else switch(this._renderInfo.renderMode){case hi.StrecthedBillboard:this._vertAttrs=no.slice();break;case hi.Mesh:this._vertAttrs=oo.slice();break;default:this._vertAttrs=so.slice()}},r._setVertexAttribIns=function(){switch(this._renderInfo.renderMode){case hi.StrecthedBillboard:this._vertAttrs=lo.slice();break;case hi.Mesh:this._vertAttrs=uo.slice();break;default:this._vertAttrs=ao.slice()}},r.updateMaterialParams=function(){if(this._particleSystem){var t=this._particleSystem,e=t.sharedMaterial;null!=e&&(this._renderInfo.mainTexture=e.getProperty("mainTexture",0)),null==t.sharedMaterial&&null==this._defaultMat&&(ho.parent=st.get("default-particle-material"),ho.owner=this._particleSystem,ho.subModelIdx=0,this._defaultMat=new ot(ho),ho.parent=null,ho.owner=null,ho.subModelIdx=0,null!==this._renderInfo.mainTexture&&this._defaultMat.setProperty("mainTexture",this._renderInfo.mainTexture));var i=t.getMaterialInstance(0)||this._defaultMat;t.simulationSpace===ai.World?this._defines[jn]=!0:this._defines[jn]=!1;var r=i.passes[0];this._uScaleHandle=r.getHandle("scale"),this._uLenHandle=r.getHandle("frameTile_velLenScale"),this._uNodeRotHandle=r.getHandle("nodeRotation");var s=this._renderInfo.renderMode,n=this._frameTile_velLenScale;s===hi.Billboard?this._defines[Yn]=0:s===hi.StrecthedBillboard?(this._defines[Yn]=1,n.z=this._renderInfo.velocityScale,n.w=this._renderInfo.lengthScale):s===hi.HorizontalBillboard?this._defines[Yn]=2:s===hi.VerticalBillboard?this._defines[Yn]=3:s===hi.Mesh?this._defines[Yn]=4:u("particle system renderMode "+s+" not support.");var o=t._textureAnimationModule;o&&o.enable?(x.copy(this._tmp_velLenScale,n),V.set(this._tmp_velLenScale,o.numTilesX,o.numTilesY),r.setUniform(this._uLenHandle,this._tmp_velLenScale)):r.setUniform(this._uLenHandle,n);var a,l=this._particleSystem._rotationOvertimeModule;a=!!l&&l.enable,this._defines.ROTATION_OVER_TIME_MODULE_ENABLE=a,this._defines.CC_INSTANCE_PARTICLE=this._useInstance,i.recompileShaders(this._defines),this._model&&this._model.updateMaterial(i)}},r.updateTrailMaterial=function(){if(this._particleSystem){var t=this._particleSystem,e=t._trailModule;if(e&&e.enable){t.simulationSpace===ai.World||e.space===ai.World?this._trailDefines[jn]=!0:this._trailDefines[jn]=!1;var i=t.getMaterialInstance(1);null===i&&null===this._defaultTrailMat&&(ho.parent=st.get("default-trail-material"),ho.owner=this._particleSystem,ho.subModelIdx=1,this._defaultTrailMat=new ot(ho),ho.parent=null,ho.owner=null,ho.subModelIdx=0),(i=i||this._defaultTrailMat).recompileShaders(this._trailDefines),e.updateMaterial()}}},r.setUseInstance=function(t){this._useInstance!==t&&(this._useInstance=t,this._model&&(this._model.useInstance=t,this._model.doDestroy()),this.updateRenderMode())},i}(Ln),po=new x,fo=new k,mo=new x,vo=new U,yo=new U;new F;var Mo,go,So,bo,xo,To,wo,Ao,Oo,Ro,Fo,Co,Eo,zo,Do,Po,Io,Bo,Lo,Vo,Uo,ko=32,No="CC_USE_WORLD_SPACE",Go="CC_RENDER_MODE",Ho="a_position_starttime",Xo="a_size_uv",Wo="a_rotation_uv",jo="a_color",Yo="a_dir_life",Zo="a_rndSeed",qo="a_size_fid",Qo="a_rotation",Ko=[new St(Ho,xt.RGBA32F),new St(Xo,xt.RGBA32F),new St(Wo,xt.RGBA32F),new St(jo,xt.RGBA32F),new St(Yo,xt.RGBA32F),new St(Zo,xt.R32F)],Jo=[new St(Ho,xt.RGBA32F),new St(Xo,xt.RGBA32F),new St(Wo,xt.RGBA32F),new St(jo,xt.RGBA32F),new St(Yo,xt.RGBA32F),new St(Zo,xt.R32F),new St(bt.ATTR_TEX_COORD,xt.RGB32F),new St(bt.ATTR_TEX_COORD3,xt.RGB32F),new St(bt.ATTR_NORMAL,xt.RGB32F),new St(bt.ATTR_COLOR1,xt.RGBA8,!0)],$o=[new St(Ho,xt.RGBA32F,!1,0,!0),new St(qo,xt.RGBA32F,!1,0,!0),new St(Qo,xt.RGB32F,!1,0,!0),new St(jo,xt.RGBA32F,!1,0,!0),new St(Yo,xt.RGBA32F,!1,0,!0),new St(Zo,xt.R32F,!1,0,!0),new St("a_uv",xt.RGB32F,!1,1)],ta=[new St(Ho,xt.RGBA32F,!1,0,!0),new St(qo,xt.RGBA32F,!1,0,!0),new St(Qo,xt.RGB32F,!1,0,!0),new St(jo,xt.RGBA32F,!1,0,!0),new St(Yo,xt.RGBA32F,!1,0,!0),new St(Zo,xt.R32F,!1,0,!0),new St(bt.ATTR_TEX_COORD,xt.RGB32F,!1,1),new St(bt.ATTR_TEX_COORD3,xt.RGB32F,!1,1),new St(bt.ATTR_NORMAL,xt.RGB32F,!1,1),new St(bt.ATTR_COLOR1,xt.RGBA8,!0,1)],ea={parent:null,owner:null,subModelIdx:0},ia=function(t){function i(e){var i;return(i=t.call(this,e)||this)._defines=void 0,i._frameTile_velLenScale=void 0,i._unifrom_velLenScale=void 0,i._tmp_velLenScale=void 0,i._node_scale=void 0,i._vertAttrs=[],i._defaultMat=null,i._particleNum=0,i._tempParticle=null,i._colorTexture=null,i._forceTexture=null,i._velocityTexture=null,i._rotationTexture=null,i._sizeTexture=null,i._animTexture=null,i._colorData=null,i._forceData=null,i._velocityData=null,i._rotationData=null,i._sizeData=null,i._animData=null,i._uTimeHandle=0,i._uRotHandle=0,i._uNodeRotHandle=0,i._alignSpace=ui.View,i._inited=!1,i._frameTile_velLenScale=new x(1,1,0,0),i._unifrom_velLenScale=i._frameTile_velLenScale.clone(),i._tmp_velLenScale=i._frameTile_velLenScale.clone(),i._node_scale=new F,i._defines={CC_USE_WORLD_SPACE:!0,CC_USE_BILLBOARD:!0,CC_USE_STRETCHED_BILLBOARD:!1,CC_USE_HORIZONTAL_BILLBOARD:!1,CC_USE_VERTICAL_BILLBOARD:!1,COLOR_OVER_TIME_MODULE_ENABLE:!1},i._tempParticle=new oi(null),i._particleNum=0,i}e(i,t);var r=i.prototype;return r.onInit=function(e){t.prototype.onInit.call(this,e),this._setVertexAttrib(),this._initModel(),this.updateMaterialParams(),this.setVertexAttributes(),this._inited=!0},r.updateRenderMode=function(){this._setVertexAttrib(),this.updateMaterialParams(),this.setVertexAttributes()},r.setVertexAttributes=function(){t.prototype.setVertexAttributes.call(this),this._model.constructAttributeIndex()},r.clear=function(){t.prototype.clear.call(this),this._particleNum=0,this.updateRenderData()},r.onDestroy=function(){t.prototype.onDestroy.call(this),this._forceTexture&&this._forceTexture.destroy(),this._velocityTexture&&this._velocityTexture.destroy(),this._colorTexture&&this._colorTexture.destroy(),this._sizeTexture&&this._sizeTexture.destroy(),this._rotationTexture&&this._rotationTexture.destroy(),this._animTexture&&this._animTexture.destroy(),this._forceData=null,this._velocityData=null,this._colorData=null,this._sizeData=null,this._rotationData=null,this._animData=null},r.enableModule=function(){var t,e=(null==(t=this._particleSystem)?void 0:t.getMaterialInstance(0))||this._defaultMat;e&&(this.initShaderUniform(e),e.recompileShaders(this._defines),this._model&&this._model.setSubModelMaterial(0,e))},r.getFreeParticle=function(){var t;return this._particleSystem&&this._particleNum>=(null==(t=this._particleSystem)?void 0:t.capacity)?null:this._tempParticle},r.setNewParticle=function(t){this._particleSystem&&(this._model.addGPUParticleVertexData(t,this._particleNum,this._particleSystem.time),this._particleNum++)},r.getDefaultMaterial=function(){return this._defaultMat},r.updateRotation=function(t){t&&this.doUpdateRotation(t)},r.doUpdateRotation=function(t){if(this._renderInfo.renderMode===hi.Mesh||this._alignSpace!==ui.View){var e;if(this._alignSpace===ui.Local)null==(e=this._particleSystem)||e.node.getRotation(yo);else if(this._alignSpace===ui.World){var i;null==(i=this._particleSystem)||i.node.getWorldRotation(yo)}else if(this._alignSpace===ui.View){var r,s;yo.set(0,0,0,1);var n=null==(r=this._particleSystem)||null==(s=r.node.scene.renderScene)?void 0:s.cameras;if(void 0!==n&&this._particleSystem)for(var o=0;o<(null==n?void 0:n.length);++o){var a=n[o];if((a.visibility&this._particleSystem.node.layer)===this._particleSystem.node.layer){U.fromViewUp(yo,a.forward);break}}}else yo.set(0,0,0,1);t.setUniform(this._uNodeRotHandle,yo)}},r.updateScale=function(t){t&&this.doUpdateScale(t)},r.doUpdateScale=function(t){var e,i=this._node_scale;switch(null==(e=this._particleSystem)?void 0:e.scaleSpace){case ai.Local:this._particleSystem.node.getScale(i);break;case ai.World:this._particleSystem.node.getWorldScale(i)}t.setUniform(t.getHandle("scale"),po.set(i.x,i.y,i.z))},r.updateParticles=function(t){return this._particleSystem?(this._particleNum=this._model.updateGPUParticles(this._particleNum,this._particleSystem.time,t),this.updateShaderUniform(t),this._model.enabled=this._particleNum>0,this._particleNum):this._particleNum},r.updateRenderData=function(){},r.beforeRender=function(){this._model.updateIA(this._particleNum)},r.updateAlignSpace=function(t){this._alignSpace=t},r.updateShaderUniform=function(t){if(this._particleSystem){var e=this._particleSystem.getMaterialInstance(0)||this._defaultMat;if(e){var i=e.passes[0];mo.x=this._particleSystem.time,mo.y=t,i.setUniform(this._uTimeHandle,mo),this._particleSystem.node.getWorldRotation(vo),i.setUniform(this._uRotHandle,vo),this.doUpdateRotation(i)}}},r.initShaderUniform=function(t){var e,i,r,s,n,o,a=t.passes[0];this._uTimeHandle=a.getHandle("u_timeDelta"),this._uRotHandle=a.getHandle("u_worldRot"),this._uNodeRotHandle=a.getHandle("nodeRotation"),this.doUpdateScale(a),a.setUniform(a.getHandle("frameTile_velLenScale"),this._unifrom_velLenScale),mo.x=ko,mo.y=.03125,a.setUniform(a.getHandle("u_sampleInfo"),mo);var l=!1,u=null==(e=this._particleSystem)?void 0:e._forceOvertimeModule;if(l=!!u&&u.enable,this._defines.FORCE_OVER_TIME_MODULE_ENABLE=l,l){var h=Oe(this._forceTexture,this._forceData,ko,u.x,u.y,u.z);this._forceTexture=h.texture,this._forceData=h.texdata;var c=a.getHandle("force_over_time_tex0"),_=at(c);a.bindSampler(_,this._forceTexture.getGFXSampler()),a.bindTexture(_,this._forceTexture.getGFXTexture());var d=a.getHandle("u_force_space");a.setUniform(d,u.space);var p=a.getHandle("u_force_mode");a.setUniform(p,this._forceTexture.height)}var f=null==(i=this._particleSystem)?void 0:i._velocityOvertimeModule;if(l=!!f&&f.enable,this._defines.VELOCITY_OVER_TIME_MODULE_ENABLE=l,l){var m=Re(this._velocityTexture,this._velocityData,ko,f.x,f.y,f.z,f.speedModifier);this._velocityTexture=m.texture,this._velocityData=m.texdata;var v=a.getHandle("velocity_over_time_tex0"),y=at(v);a.bindSampler(y,this._velocityTexture.getGFXSampler()),a.bindTexture(y,this._velocityTexture.getGFXTexture());var M=a.getHandle("u_velocity_space");a.setUniform(M,f.space);var g=a.getHandle("u_velocity_mode");a.setUniform(g,this._velocityTexture.height)}var S=null==(r=this._particleSystem)?void 0:r._colorOverLifetimeModule;if(l=!!S&&S.enable,this._defines.COLOR_OVER_TIME_MODULE_ENABLE=l,l){var b=ii(this._colorTexture,this._colorData,ko,S.color);this._colorTexture=b.texture,this._colorData=b.texdata;var x=a.getHandle("color_over_time_tex0"),T=at(x);a.bindSampler(T,this._colorTexture.getGFXSampler()),a.bindTexture(T,this._colorTexture.getGFXTexture());var w=a.getHandle("u_color_mode");a.setUniform(w,this._colorTexture.height)}var A,O=null==(s=this._particleSystem)?void 0:s._rotationOvertimeModule;if(l=!!O&&O.enable,this._defines.ROTATION_OVER_TIME_MODULE_ENABLE=l,l&&(A=O.separateAxes?Oe(this._rotationTexture,this._rotationData,ko,O.x,O.y,O.z):Te(this._rotationTexture,this._rotationData,ko,O.z),this._rotationTexture=A.texture,this._rotationData=A.texdata,this._rotationTexture)){var R=a.getHandle("rotation_over_time_tex0"),F=at(R);a.bindSampler(F,this._rotationTexture.getGFXSampler()),a.bindTexture(F,this._rotationTexture.getGFXTexture());var C=a.getHandle("u_rotation_mode");a.setUniform(C,this._rotationTexture.height)}var E,z=null==(n=this._particleSystem)?void 0:n._sizeOvertimeModule;if(l=!!z&&z.enable,this._defines.SIZE_OVER_TIME_MODULE_ENABLE=l,l&&(E=z.separateAxes?Oe(this._sizeTexture,this._sizeData,ko,z.x,z.y,z.z,!0):we(this._sizeTexture,this._sizeData,ko,z.size),this._sizeTexture=E.texture,this._sizeData=E.texdata,this._sizeTexture)){var D=a.getHandle("size_over_time_tex0"),P=at(D);a.bindSampler(P,this._sizeTexture.getGFXSampler()),a.bindTexture(P,this._sizeTexture.getGFXTexture());var I=a.getHandle("u_size_mode");a.setUniform(I,this._sizeTexture.height)}var B=null==(o=this._particleSystem)?void 0:o._textureAnimationModule;if(l=!!B&&B.enable,this._defines.TEXTURE_ANIMATION_MODULE_ENABLE=l,l){var L=Ae(this._animTexture,this._animData,ko,B.startFrame,B.frameOverTime);this._animTexture=L.texture,this._animData=L.texdata;var V=a.getHandle("texture_animation_tex0"),U=at(V);a.bindSampler(U,this._animTexture.getGFXSampler()),a.bindTexture(U,this._animTexture.getGFXTexture());var k=a.getHandle("u_anim_info");mo.x=this._animTexture.height,mo.y=B.numTilesX*B.numTilesY,mo.z=B.cycleCount,a.setUniform(k,mo)}this._defines.USE_VK_SHADER=Mt.gfxDevice.gfxAPI===Ft.VULKAN,this._defines.CC_INSTANCE_PARTICLE=this._useInstance},r.getParticleCount=function(){return this._particleNum},r.onMaterialModified=function(){this._inited&&this.updateMaterialParams()},r.onRebuildPSO=function(t,e){this._model&&0===t&&this._model.setSubModelMaterial(0,e)},r.updateVertexAttrib=function(){if(this._renderInfo.renderMode===hi.Mesh&&this._renderInfo.mesh){var t=this._renderInfo.mesh.readAttributeFormat(0,bt.ATTR_COLOR);if(t){for(var e=xt.RGBA8,i=0;i<Tt.length;++i)if(Tt[i].name===t.name){e=i;break}this._vertAttrs[9]=new St(bt.ATTR_COLOR1,e,!0,this._useInstance?1:0)}else{var r=xt.RGBA8;this._vertAttrs[9]=new St(bt.ATTR_COLOR1,r,!0,this._useInstance?1:0)}}},r._setVertexAttrib=function(){if(this._useInstance)this._setVertexAttribIns();else switch(this._renderInfo.renderMode){case hi.StrecthedBillboard:this._vertAttrs=Ko.slice();break;case hi.Mesh:this._vertAttrs=Jo.slice();break;default:this._vertAttrs=Ko.slice()}},r._setVertexAttribIns=function(){switch(this._renderInfo.renderMode){case hi.StrecthedBillboard:this._vertAttrs=$o.slice();break;case hi.Mesh:this._vertAttrs=ta.slice();break;default:this._vertAttrs=$o.slice()}},r.updateMaterialParams=function(){if(this._particleSystem){var t=this._particleSystem,e=t.sharedMaterial;null!==e&&(this._renderInfo.mainTexture=e.getProperty("mainTexture",0)),null==t.sharedMaterial&&null==this._defaultMat&&(ea.parent=st.get("default-particle-gpu-material"),ea.owner=t,ea.subModelIdx=0,this._defaultMat=new ot(ea),ea.parent=null,ea.owner=null,ea.subModelIdx=0,null!==this._renderInfo.mainTexture&&this._defaultMat.setProperty("mainTexture",this._renderInfo.mainTexture));var i=t.getMaterialInstance(0)||this._defaultMat;t.node.getWorldMatrix(fo),t.simulationSpace===ai.World?this._defines[No]=!0:this._defines[No]=!1;var r=this._renderInfo.renderMode;r===hi.Billboard?this._defines[Go]=0:r===hi.StrecthedBillboard?(this._defines[Go]=1,this._frameTile_velLenScale.z=this._renderInfo.velocityScale,this._frameTile_velLenScale.w=this._renderInfo.lengthScale):r===hi.HorizontalBillboard?this._defines[Go]=2:r===hi.VerticalBillboard?this._defines[Go]=3:r===hi.Mesh?this._defines[Go]=4:u("particle system renderMode "+r+" not support.");var s=t._textureAnimationModule;s&&s.enable?(V.set(this._frameTile_velLenScale,s.numTilesX,s.numTilesY),x.copy(this._unifrom_velLenScale,this._frameTile_velLenScale)):(this._tmp_velLenScale.z=this._frameTile_velLenScale.z,this._tmp_velLenScale.w=this._frameTile_velLenScale.w,x.copy(this._unifrom_velLenScale,this._tmp_velLenScale)),this.initShaderUniform(i),i.recompileShaders(this._defines),this._model&&this._model.updateMaterial(i)}},r.setUseInstance=function(t){this._useInstance!==t&&(this._useInstance=t,this._model&&(this._model.useInstance=t,this._model.doDestroy()),this.updateRenderMode())},r.getNoisePreview=function(){},i}(Ln);function ra(){var t=Dt.root.device;return!!(t.capabilities.maxVertexTextureUnits>=8&&t.getFormatFeatures(xt.RGBA32F)&(Ct.RENDER_TARGET|Ct.SAMPLED_TEXTURE))||(et.warn("Maybe the device has restrictions on vertex textures or does not support float textures."),!1)}var sa,na,oa,aa,la,ua,ha,ca,_a,da,pa,fa,ma,va,ya,Ma,ga,Sa,ba,xa,Ta,wa,Aa,Oa,Ra,Fa,Ca,Ea,za,Da,Pa,Ia,Ba,La,Va,Ua,ka,Na,Ga,Ha,Xa,Wa,ja,Ya,Za,qa,Qa,Ka,Ja,$a,tl,el,il,rl,sl,nl,ol,al,ll,ul,hl,cl,_l,dl,pl,fl,ml,vl,yl,Ml,gl,Sl,bl,xl,Tl,wl,Al,Ol,Rl,Fl,Cl,El,zl,Dl,Pl,Il,Bl,Ll,Vl,Ul,kl,Nl,Gl,Hl,Xl,Wl,jl,Yl,Zl,ql,Ql,Kl,Jl,$l,tu,eu,iu,ru,su,nu,ou,au,lu,uu,hu,cu,_u,du,pu,fu,mu,vu,yu,Mu,gu,Su,bu,xu,Tu,wu,Au,Ou,Ru,Fu,Cu,Eu,zu,Du,Pu,Iu,Bu,Lu,Vu,Uu,ku,Nu=(Mo=S("cc.ParticleSystemRenderer"),go=b(hi),So=b(hi),bo=b(It),xo=b(rt),To=b(rt),wo=b(rt),Ao=b(rt),Oo=b(ui),Mo((Uo=function(){function t(){this._renderMode=Co&&Co(),this._velocityScale=Eo&&Eo(),this._lengthScale=zo&&zo(),this._mesh=Do&&Do(),this._cpuMaterial=Po&&Po(),this._gpuMaterial=Io&&Io(),this._mainTexture=Bo&&Bo(),this._useGPU=Lo&&Lo(),this._alignSpace=Vo&&Vo(),this._particleSystem=null}var e=t.prototype;return e.create=function(t){null===this._particleSystem?this._particleSystem=t:this._particleSystem!==t&&_(6033)},e.onInit=function(t){this.create(t);var e=this._useGPU&&ra();this._particleSystem.processor?_(6034):(this._particleSystem.processor=e?new ia(this):new _o(this),this._particleSystem.processor.updateAlignSpace(this.alignSpace),this._particleSystem.processor.onInit(t)),e?this.gpuMaterial=this.particleMaterial:(this.particleMaterial&&-1!==this.particleMaterial.effectName.indexOf("particle-gpu")&&(this.particleMaterial=null,d(6035)),this.cpuMaterial=this.particleMaterial)},e._switchProcessor=function(){if(this._particleSystem){this._particleSystem.processor&&(this._particleSystem.processor.detachFromScene(),this._particleSystem.processor.clear(),this._particleSystem.processor=null);var t=this._useGPU&&ra();this.particleMaterial=t?this.gpuMaterial:this.cpuMaterial,this._particleSystem.processor=t?new ia(this):new _o(this),this._particleSystem.processor.updateAlignSpace(this.alignSpace),this._particleSystem.processor.onInit(this._particleSystem),this._particleSystem.processor.onEnable(),this._particleSystem.bindModule()}},i(t,[{key:"renderMode",get:function(){return this._renderMode},set:function(t){this._renderMode!==t&&(this._renderMode=t,this._particleSystem&&this._particleSystem.processor.updateRenderMode())}},{key:"velocityScale",get:function(){return this._velocityScale},set:function(t){this._velocityScale=t,this._particleSystem&&this._particleSystem.processor.updateMaterialParams()}},{key:"lengthScale",get:function(){return this._lengthScale},set:function(t){this._lengthScale=t,this._particleSystem&&this._particleSystem.processor.updateMaterialParams()}},{key:"mesh",get:function(){return this._mesh},set:function(t){this._mesh=t,this._particleSystem&&this._particleSystem.processor.setVertexAttributes()}},{key:"particleMaterial",get:function(){return this._particleSystem?this._particleSystem.getSharedMaterial(0):null},set:function(t){this._particleSystem&&this._particleSystem.setSharedMaterial(t,0)}},{key:"cpuMaterial",get:function(){return this._cpuMaterial},set:function(t){if(t){var e=t.effectName;if(-1===e.indexOf("particle")||-1!==e.indexOf("particle-gpu"))return void d(6035)}this._cpuMaterial=t,this.particleMaterial=this._cpuMaterial}},{key:"gpuMaterial",get:function(){return this._gpuMaterial},set:function(t){t&&-1===t.effectName.indexOf("particle-gpu")?d(6035):(this._gpuMaterial=t,this.particleMaterial=this._gpuMaterial)}},{key:"trailMaterial",get:function(){return this._particleSystem?this._particleSystem.getSharedMaterial(1):null},set:function(t){this._particleSystem&&this._particleSystem.setSharedMaterial(t,1)}},{key:"mainTexture",get:function(){return this._mainTexture},set:function(t){this._mainTexture=t}},{key:"useGPU",get:function(){return this._useGPU},set:function(t){this._useGPU!==t&&(ra()?this._useGPU=t:this._useGPU=!1,this._switchProcessor())}},{key:"alignSpace",get:function(){return this._alignSpace},set:function(t){this._alignSpace=t,this._particleSystem.processor.updateAlignSpace(this._alignSpace)}}]),t}(),Uo.AlignmentSpace=ui,r((Fo=Uo).prototype,"renderMode",[go],Object.getOwnPropertyDescriptor(Fo.prototype,"renderMode"),Fo.prototype),Co=w(Fo.prototype,"_renderMode",[So,R],(function(){return hi.Billboard})),Eo=w(Fo.prototype,"_velocityScale",[R],(function(){return 1})),zo=w(Fo.prototype,"_lengthScale",[R],(function(){return 1})),Do=w(Fo.prototype,"_mesh",[R],(function(){return null})),r(Fo.prototype,"mesh",[bo],Object.getOwnPropertyDescriptor(Fo.prototype,"mesh"),Fo.prototype),r(Fo.prototype,"particleMaterial",[xo],Object.getOwnPropertyDescriptor(Fo.prototype,"particleMaterial"),Fo.prototype),r(Fo.prototype,"cpuMaterial",[To],Object.getOwnPropertyDescriptor(Fo.prototype,"cpuMaterial"),Fo.prototype),Po=w(Fo.prototype,"_cpuMaterial",[R],(function(){return null})),r(Fo.prototype,"gpuMaterial",[wo],Object.getOwnPropertyDescriptor(Fo.prototype,"gpuMaterial"),Fo.prototype),Io=w(Fo.prototype,"_gpuMaterial",[R],(function(){return null})),r(Fo.prototype,"trailMaterial",[Ao],Object.getOwnPropertyDescriptor(Fo.prototype,"trailMaterial"),Fo.prototype),Bo=w(Fo.prototype,"_mainTexture",[R],(function(){return null})),Lo=w(Fo.prototype,"_useGPU",[R],(function(){return!1})),r(Fo.prototype,"alignSpace",[Oo],Object.getOwnPropertyDescriptor(Fo.prototype,"alignSpace"),Fo.prototype),Vo=w(Fo.prototype,"_alignSpace",[R],(function(){return ui.View})),Ro=Fo))||Ro),Gu=Math.cos(O(100)),Hu={position:new F,velocity:new F},Xu=new U,Wu=new F,ju=new F,Yu=new T,Zu=function(){function t(t){for(this.start=void 0,this.end=void 0,this.trailElements=void 0,this.start=-1,this.end=-1,this.trailElements=[];t--;)this.trailElements.push({position:new F,lifetime:0,width:0,velocity:new F,direction:0,color:new T})}var e=t.prototype;return e.getElement=function(t){return-1===this.start?null:(t<0&&(t=(t+this.trailElements.length)%this.trailElements.length),t>=this.trailElements.length&&(t%=this.trailElements.length),this.trailElements[t])},e.addElement=function(){if(0===this.trailElements.length)return null;if(-1===this.start)return this.start=0,this.end=1,this.trailElements[0];this.start===this.end&&(this.trailElements.splice(this.end,0,{position:new F,lifetime:0,width:0,velocity:new F,direction:0,color:new T}),this.start++,this.start%=this.trailElements.length);var t=this.end++;return this.end%=this.trailElements.length,this.trailElements[t]},e.iterateElement=function(t,e,i,r){for(var s=this.start>=this.end?this.end+this.trailElements.length:this.end,n=this.start;n<s;n++)e(t,this.trailElements[n%this.trailElements.length],i,r)&&(this.start++,this.start%=this.trailElements.length);this.start===s&&(this.start=-1,this.end=-1)},e.count=function(){return this.start<this.end?this.end-this.start:this.trailElements.length+this.end-this.start},e.clear=function(){this.start=-1,this.end=-1},t}(),qu=(sa=S("cc.TrailModule"),na=b(pi),oa=b(Me),aa=b(ai),la=b(fi),ua=b(Me),ha=b($e),ca=b($e),_a=b(ai),sa((pa=function(){var t=e.prototype;function e(){this._enable=fa&&fa(),this.mode=ma&&ma(),this.lifeTime=va&&va(),this._minParticleDistance=ya&&ya(),this.existWithParticles=Ma&&Ma(),this.textureMode=ga&&ga(),this.widthFromParticle=Sa&&Sa(),this.widthRatio=ba&&ba(),this.colorFromParticle=xa&&xa(),this.colorOverTrail=Ta&&Ta(),this.colorOvertime=wa&&wa(),this._space=Aa&&Aa(),this._particleSystem=Oa&&Oa(),this._minSquaredDistance=0,this._vertSize=void 0,this._trailNum=0,this._trailLifetime=0,this.vbOffset=0,this.ibOffset=0,this._trailSegments=null,this._particleTrail=void 0,this._trailModel=null,this._subMeshData=null,this._vertAttrs=void 0,this._vbF32=null,this._vbUint32=null,this._iBuffer=null,this._needTransform=!1,this._material=null,this._psTransform=new k,this._iaVertCount=0,this._iaIndexCount=0,this._vertAttrs=[new St(bt.ATTR_POSITION,xt.RGB32F),new St(bt.ATTR_TEX_COORD,xt.RGBA32F),new St(bt.ATTR_TEX_COORD1,xt.RGB32F),new St(bt.ATTR_COLOR,xt.RGBA8,!0)],this._vertSize=this._vertAttrs.reduce((function(t,e){return t+Tt[e.format].size}),0),this._particleTrail=new Map,this._inited=!1}return t.getModel=function(){return this._trailModel},t.onInit=function(t){this._particleSystem=t,this.minParticleDistance=this._minParticleDistance;for(var e=0,i=t.startLifetime.getMax(),r=t.rateOverTime.getMax(),s=t.duration,n=0,o=t.bursts.length;n<o;n++)e+=t.bursts[n].getMaxCount(t)*Math.ceil(i/s);this.lifeTime.getMax()<1&&d(6036),this._trailNum=Math.ceil(i*Math.ceil(this.lifeTime.getMax())*60*(r*s+e)),this._trailSegments=new p((function(){return new Zu(10)}),Math.ceil(r*s),(function(t){t.trailElements.length=0})),this._enable&&(this.enable=this._enable),this._inited=!0},t.onEnable=function(){this._attachToScene()},t.onDisable=function(){this._particleTrail.clear(),this._detachFromScene()},t._attachToScene=function(){this._trailModel&&(this._trailModel.scene&&this._detachFromScene(),this._particleSystem._getRenderScene().addModel(this._trailModel))},t._detachFromScene=function(){this._trailModel&&this._trailModel.scene&&this._trailModel.scene.removeModel(this._trailModel)},t.destroy=function(){this.destroySubMeshData(),this._trailModel&&(Dt.root.destroyModel(this._trailModel),this._trailModel=null),this._trailSegments&&(this._trailSegments.destroy(),this._trailSegments=null)},t.play=function(){this._trailModel&&this._enable&&(this._trailModel.enabled=!0)},t.clear=function(){if(this.enable){for(var t=this._particleTrail.values(),e=t.next();!e.done;)e.value.clear(),e=t.next();this._particleTrail.clear(),this.updateRenderData(),this._trailModel&&(this._trailModel.enabled=!1)}},t.updateMaterial=function(){this._particleSystem&&(this._material=this._particleSystem.getMaterialInstance(1)||this._particleSystem.processor.getDefaultTrailMaterial(),this._trailModel&&this._trailModel.setSubModelMaterial(0,this._material))},t.update=function(){this._trailLifetime=this.lifeTime.evaluate(this._particleSystem.time,1),this.space===ai.World&&this._particleSystem.simulationSpace===ai.Local?(this._needTransform=!0,this._particleSystem.node.getWorldMatrix(this._psTransform),this._particleSystem.node.getWorldRotation(Xu)):this._needTransform=!1},t.animate=function(t,e){if(this._trailSegments)if(t.loopCount>t.lastLoop)t.trailDelay>1?(t.lastLoop=t.loopCount,t.trailDelay=0):t.trailDelay++;else{var i=this._particleTrail.get(t);if(!i)return i=this._trailSegments.alloc(),void this._particleTrail.set(t,i);var r=i.getElement(i.end-1);if(this._needTransform?F.transformMat4(Wu,t.position,this._psTransform):F.copy(Wu,t.position),!(r&&(i.iterateElement(this,this._updateTrailElement,t,e),F.squaredDistance(r.position,Wu)<this._minSquaredDistance))&&(r=i.addElement())){F.copy(r.position,Wu),r.lifetime=0,this.widthFromParticle?r.width=t.size.x*this.widthRatio.evaluate(0,1):r.width=this.widthRatio.evaluate(0,1);var s=i.count();if(2===s){var n=i.getElement(i.end-2);F.subtract(n.velocity,r.position,n.position)}else if(s>2){var o=i.getElement(i.end-2),a=i.getElement(i.end-3);F.subtract(Wu,a.position,o.position),F.subtract(ju,r.position,o.position),F.subtract(o.velocity,ju,Wu),F.equals(F.ZERO,o.velocity)&&F.copy(o.velocity,Wu),F.normalize(o.velocity,o.velocity),this._checkDirectionReverse(o,a)}this.colorFromParticle?r.color.set(t.color):r.color.set(this.colorOvertime.evaluate(0,1))}}},t.removeParticle=function(t){var e=this._particleTrail.get(t);e&&this._trailSegments&&(e.clear(),this._trailSegments.free(e),this._particleTrail.delete(t))},t.updateRenderData=function(){this.vbOffset=0,this.ibOffset=0;for(var t,e=h(this._particleTrail.keys());!(t=e()).done;){var i=t.value,r=this._particleTrail.get(i);if(-1!==r.start){var s=4*this.vbOffset/this._vertSize,n=r.start>=r.end?r.end+r.trailElements.length:r.end,o=n-r.start,a=1/o,l=r.trailElements[r.start];this._fillVertexBuffer(l,this.colorOverTrail.evaluate(1,1),s,1,0,4);for(var u=r.start+1;u<n;u++){var c=r.trailElements[u%r.trailElements.length],_=u-r.start;this._fillVertexBuffer(c,this.colorOverTrail.evaluate(1-_/o,1),s,1-_*a,_,5)}this._needTransform?F.transformMat4(Hu.position,i.position,this._psTransform):F.copy(Hu.position,i.position);var d=this._trailModel;if(d&&d.node.invalidateChildren(lt.POSITION),1===o||2===o){var p=r.getElement(r.end-1);F.subtract(p.velocity,Hu.position,p.position);var f=this._vbF32,m=this.vbOffset,v=this._vertSize/4,y=p.velocity;f[m-v-4]=y.x,f[m-v-3]=y.y,f[m-v-2]=y.z,f[m-4]=y.x,f[m-3]=y.y,f[m-2]=y.z,F.subtract(Hu.velocity,Hu.position,p.position),this._checkDirectionReverse(Hu,p)}else if(o>2){var M=r.getElement(r.end-1),g=r.getElement(r.end-2);F.subtract(Wu,g.position,M.position),F.subtract(ju,Hu.position,M.position),F.normalize(Wu,Wu),F.normalize(ju,ju),F.subtract(M.velocity,ju,Wu),F.normalize(M.velocity,M.velocity),this._checkDirectionReverse(M,g),this.vbOffset-=this._vertSize/4*2,this.ibOffset-=6,this._fillVertexBuffer(M,this.colorOverTrail.evaluate(a,1),s,a,o-1,5),F.subtract(Hu.velocity,Hu.position,M.position),F.normalize(Hu.velocity,Hu.velocity),this._checkDirectionReverse(Hu,M)}this.widthFromParticle?Hu.width=i.size.x*this.widthRatio.evaluate(0,1):Hu.width=this.widthRatio.evaluate(0,1),Hu.color=i.color,F.equals(Hu.velocity,F.ZERO)?this.ibOffset-=3:this._fillVertexBuffer(Hu,this.colorOverTrail.evaluate(0,1),s,0,o,1)}}this._trailModel&&(this._trailModel.enabled=this.ibOffset>0)},t.updateIA=function(t){var e=this._trailModel&&this._trailModel.subModels;if(e&&e.length>0){var i=e[0];i.inputAssembler.vertexBuffers[0].update(this._vbF32),i.inputAssembler.indexBuffer.update(this._iBuffer),i.inputAssembler.firstIndex=0,i.inputAssembler.indexCount=t,i.inputAssembler.vertexCount=this._iaVertCount}},t.beforeRender=function(){this.updateIA(this.ibOffset)},t._createModel=function(){this._trailModel||(this._trailModel=et.director.root.createModel(ft))},t.rebuild=function(){var t=this,e=Dt.root.device,i=e.createBuffer(new wt(At.VERTEX|At.TRANSFER_DST,Ot.HOST|Ot.DEVICE,t._vertSize*(t._trailNum+1)*2,t._vertSize)),r=new ArrayBuffer(t._vertSize*(t._trailNum+1)*2);t._vbF32=new Float32Array(r),t._vbUint32=new Uint32Array(r),i.update(r);var s=e.createBuffer(new wt(At.INDEX|At.TRANSFER_DST,Ot.HOST|Ot.DEVICE,6*Math.max(1,t._trailNum)*Uint16Array.BYTES_PER_ELEMENT,Uint16Array.BYTES_PER_ELEMENT));t._iBuffer=new Uint16Array(6*Math.max(1,t._trailNum)),s.update(t._iBuffer),t._iaVertCount=2*(t._trailNum+1),t._iaIndexCount=6*t._trailNum,t._subMeshData=new yt([i],t._vertAttrs,gt.TRIANGLE_LIST,s);var n=t._trailModel;n&&t._material&&(n.node=n.transform=t._particleSystem.node,n.visFlags=t._particleSystem.visibility,n.initSubModel(0,t._subMeshData,t._material),n.enabled=!0)},t._updateTrailElement=function(t,e,i,r){return e.lifetime+=r,t.colorFromParticle?(e.color.set(i.color),e.color.multiply(t.colorOvertime.evaluate(1-i.remainingLifetime/i.startLifetime,1))):e.color.set(t.colorOvertime.evaluate(1-i.remainingLifetime/i.startLifetime,1)),t.widthFromParticle?e.width=i.size.x*t.widthRatio.evaluate(e.lifetime/t._trailLifetime,1):e.width=t.widthRatio.evaluate(e.lifetime/t._trailLifetime,1),e.lifetime>t._trailLifetime},t._fillVertexBuffer=function(t,e,i,r,s,n){this._vbF32[this.vbOffset++]=t.position.x,this._vbF32[this.vbOffset++]=t.position.y,this._vbF32[this.vbOffset++]=t.position.z,this._vbF32[this.vbOffset++]=t.direction,this._vbF32[this.vbOffset++]=t.width,this._vbF32[this.vbOffset++]=r,this._vbF32[this.vbOffset++]=0,this._vbF32[this.vbOffset++]=t.velocity.x,this._vbF32[this.vbOffset++]=t.velocity.y,this._vbF32[this.vbOffset++]=t.velocity.z,Yu.set(t.color),Yu.multiply(e),this._vbUint32[this.vbOffset++]=T.toUint32(Yu),this._vbF32[this.vbOffset++]=t.position.x,this._vbF32[this.vbOffset++]=t.position.y,this._vbF32[this.vbOffset++]=t.position.z,this._vbF32[this.vbOffset++]=1-t.direction,this._vbF32[this.vbOffset++]=t.width,this._vbF32[this.vbOffset++]=r,this._vbF32[this.vbOffset++]=1,this._vbF32[this.vbOffset++]=t.velocity.x,this._vbF32[this.vbOffset++]=t.velocity.y,this._vbF32[this.vbOffset++]=t.velocity.z,this._vbUint32[this.vbOffset++]=T.toUint32(Yu),1&n&&(this._iBuffer[this.ibOffset++]=i+2*s,this._iBuffer[this.ibOffset++]=i+2*s-1,this._iBuffer[this.ibOffset++]=i+2*s+1),4&n&&(this._iBuffer[this.ibOffset++]=i+2*s,this._iBuffer[this.ibOffset++]=i+2*s+1,this._iBuffer[this.ibOffset++]=i+2*s+2)},t._checkDirectionReverse=function(t,e){F.dot(t.velocity,e.velocity)<Gu?t.direction=1-e.direction:t.direction=e.direction},t.destroySubMeshData=function(){this._subMeshData&&(this._subMeshData.destroy(),this._subMeshData=null)},i(e,[{key:"enable",get:function(){return this._enable},set:function(t){t===this._enable&&this._trailModel||(t&&!this._enable&&(this._enable=t,this._particleSystem.processor&&this._particleSystem.processor.updateTrailMaterial()),t&&!this._trailModel&&(this._createModel(),this.rebuild()),this._enable=t,this._trailModel&&(this._trailModel.enabled=t),t?this.onEnable():this.onDisable())}},{key:"minParticleDistance",get:function(){return this._minParticleDistance},set:function(t){this._minParticleDistance=t,this._minSquaredDistance=t*t}},{key:"space",get:function(){return this._space},set:function(t){this._space=t;var e=this._particleSystem;e&&e.processor&&e.processor.updateTrailMaterial()}},{key:"inited",get:function(){return this._inited}}]),e}(),fa=w(pa.prototype,"_enable",[R],(function(){return!1})),ma=w(pa.prototype,"mode",[na,R],(function(){return pi.Particles})),va=w(pa.prototype,"lifeTime",[oa,R],(function(){return new Me})),ya=w(pa.prototype,"_minParticleDistance",[R],(function(){return.1})),r(pa.prototype,"space",[aa],Object.getOwnPropertyDescriptor(pa.prototype,"space"),pa.prototype),Ma=w(pa.prototype,"existWithParticles",[R],(function(){return!0})),ga=w(pa.prototype,"textureMode",[la,R],(function(){return fi.Stretch})),Sa=w(pa.prototype,"widthFromParticle",[R],(function(){return!0})),ba=w(pa.prototype,"widthRatio",[ua,R],(function(){return new Me})),xa=w(pa.prototype,"colorFromParticle",[R],(function(){return!1})),Ta=w(pa.prototype,"colorOverTrail",[ha,R],(function(){return new $e})),wa=w(pa.prototype,"colorOvertime",[ca,R],(function(){return new $e})),Aa=w(pa.prototype,"_space",[_a],(function(){return ai.World})),Oa=w(pa.prototype,"_particleSystem",[R],(function(){return null})),da=pa))||da),Qu=new k,Ku=new k,Ju=new U,$u=new F,th=["_colorOverLifetimeModule","_sizeOvertimeModule","_velocityOvertimeModule","_forceOvertimeModule","_limitVelocityOvertimeModule","_rotationOvertimeModule","_textureAnimationModule"],eh=function(){function t(t){this._particleSystem=void 0,this._processor=void 0,this._node=void 0,this._particlesAll=void 0,this._updateList=new Map,this._animateList=new Map,this._runAnimateList=[],this._localMat=new k,this._gravity=new x,this.minPos=new F,this.maxPos=new F,this._nodePos=new F,this._nodeSize=new F,this._particleSystem=t,this._processor=this._particleSystem.processor,this._node=t.node,this._particlesAll=[],this._initModuleList()}var e=t.prototype;return e._updateBoundingNode=function(){this._nodeSize.set(this.maxPos.x-this.minPos.x,this.maxPos.y-this.minPos.y,this.maxPos.z-this.minPos.z),this._nodePos.set(this.minPos.x+.5*this._nodeSize.x,this.minPos.y+.5*this._nodeSize.y,this.minPos.z+.5*this._nodeSize.z)},e.setBoundingBoxSize=function(t){this.maxPos.x=this._nodePos.x+t.x,this.maxPos.y=this._nodePos.y+t.y,this.maxPos.z=this._nodePos.z+t.z,this.minPos.x=this._nodePos.x-t.x,this.minPos.y=this._nodePos.y-t.y,this.minPos.z=this._nodePos.z-t.z,this._updateBoundingNode()},e.setBoundingBoxCenter=function(t,e,i){this.maxPos.x=t+.5*this._nodeSize.x,this.maxPos.y=e+.5*this._nodeSize.y,this.maxPos.z=i+.5*this._nodeSize.z,this.minPos.x=t-.5*this._nodeSize.x,this.minPos.y=e-.5*this._nodeSize.y,this.minPos.z=i-.5*this._nodeSize.z,this._updateBoundingNode()},e._initModuleList=function(){var t=this;th.forEach((function(e){var i=t._particleSystem[e];i&&i.enable&&(i.needUpdate&&t._updateList.set(i.name,i),i.needAnimate&&t._animateList.set(i.name,i))})),this._runAnimateList.length=0;for(var e=0,i=yi.length;e<i;e++){var r=this._animateList.get(yi[e]);r&&this._runAnimateList.push(r)}},e._emit=function(t,e,i){var r=this._particleSystem,s=this._node,n=r.time%r.duration/r.duration;s.invalidateChildren(lt.POSITION),r.simulationSpace===ai.World&&(s.getWorldMatrix(Qu),s.getWorldRotation(Ju));for(var o=0;o<t;++o){var a=new oi(r);a.particleSystem=r,a.reset();var l=X(H(0,f));r._shapeModule&&r._shapeModule.enable?r._shapeModule.emit(a):(F.set(a.position,0,0,0),F.copy(a.velocity,Oi)),r._textureAnimationModule&&r._textureAnimationModule.enable&&r._textureAnimationModule.init(a);var u=r.startSpeed.evaluate(n,l);F.multiplyScalar(a.velocity,a.velocity,u),r.simulationSpace===ai.World&&(F.transformMat4(a.position,a.position,Qu),F.transformQuat(a.velocity,a.velocity,Ju)),F.copy(a.ultimateVelocity,a.velocity),F.set(a.rotation,0,0,0),r.startSize3D?F.set(a.startSize,r.startSizeX.evaluate(n,l),r.startSizeY.evaluate(n,l),r.startSizeZ.evaluate(n,l)):(F.set(a.startSize,r.startSizeX.evaluate(n,l),1,1),a.startSize.y=a.startSize.x),F.copy(a.size,a.startSize),a.startLifetime=r.startLifetime.evaluate(n,l)+e,a.remainingLifetime=a.startLifetime,i.push(a)}},e._updateParticles=function(t,e){var i=this,r=this._particleSystem;switch(r.node.getWorldMatrix(Qu),r.scaleSpace){case ai.Local:r.node.getScale($u);break;case ai.World:r.node.getWorldScale($u)}if(this._updateList.forEach((function(){})),r.simulationSpace===ai.Local){var s=r.node.getRotation();k.fromQuat(this._localMat,s),this._localMat.transpose()}r.node.parent&&(r.node.parent.getWorldMatrix(Ku),Ku.invert());for(var n=function(){var s=e[o];if(s.remainingLifetime-=t,F.set(s.animatedVelocity,0,0,0),r.gravityModifier.mode!==ye.Constant||0!==r.gravityModifier.constant){var n=Ii(r.gravityModifier)?X(s.randomSeed):0;if(r.simulationSpace===ai.Local){var a=9.8*-r.gravityModifier.evaluate(1-s.remainingLifetime/s.startLifetime,n)*t;i._gravity.x=0,i._gravity.y=a,i._gravity.z=0,i._gravity.w=1,E(a,0,P)||(r.node.parent&&(i._gravity=i._gravity.transformMat4(Ku)),i._gravity=i._gravity.transformMat4(i._localMat),s.velocity.x+=i._gravity.x,s.velocity.y+=i._gravity.y,s.velocity.z+=i._gravity.z)}else s.velocity.y-=9.8*r.gravityModifier.evaluate(1-s.remainingLifetime/s.startLifetime,n)*t}F.copy(s.ultimateVelocity,s.velocity),i._runAnimateList.forEach((function(e){e.animate(s,t)})),F.scaleAndAdd(s.position,s.position,s.ultimateVelocity,t)},o=0;o<e.length;++o)n()},e._calculateBounding=function(t){var e=new F,i=new F,r=new F,s=new F,n=new F(1,1,1);if(this._processor.getInfo().renderMode===hi.Mesh){var o=this._processor.getInfo().mesh;if(o&&o.struct.minPosition&&o.struct.maxPosition){var a=new Q;Q.fromPoints(a,o.struct.minPosition,o.struct.maxPosition);var l=Math.max(a.halfExtents.x,a.halfExtents.y,a.halfExtents.z);n.set(l,l,l)}}for(var u=this._particleSystem.node.worldMatrix,h=0;h<this._particlesAll.length;++h){var c=this._particlesAll[h];F.multiply(e,$u,c.size),F.multiply(e,e,n),i.set(c.position),this._particleSystem.simulationSpace!==ai.World&&F.transformMat4(i,i,u),t&&0===h?(F.subtract(this.minPos,i,e),F.add(this.maxPos,i,e)):(F.subtract(r,i,e),F.add(s,i,e),F.min(this.minPos,this.minPos,r),F.max(this.maxPos,this.maxPos,s))}},e.calculatePositions=function(){this._emit(this._particleSystem.capacity,0,this._particlesAll);var t=Ii(this._particleSystem.startLifetime)?X(H(0,f)):0;this._updateParticles(0,this._particlesAll),this._calculateBounding(!0),this._updateParticles(this._particleSystem.startLifetime.evaluate(0,t),this._particlesAll),this._calculateBounding(!1),this._updateBoundingNode()},e.clear=function(){this._particlesAll.length=0},e.destroy=function(){},t}(),ih=R,rh=b,sh=(Ra=S("cc.NoiseModule"),Fa=rh(m),Ca=rh(m),Ea=rh(m),za=rh(m),Da=rh(m),Pa=rh(m),Ia=rh(m),Ba=rh(m),La=rh(m),Va=rh(m),Ua=rh(v),ka=rh(m),Na=rh(m),Ra((Ha=function(t){function r(){var e;return(e=t.call(this)||this)._enable=Xa&&Xa(),e._strengthX=Wa&&Wa(),e._strengthY=ja&&ja(),e._strengthZ=Ya&&Ya(),e._noiseSpeedX=Za&&Za(),e._noiseSpeedY=qa&&qa(),e._noiseSpeedZ=Qa&&Qa(),e._noiseFrequency=Ka&&Ka(),e._remapX=Ja&&Ja(),e._remapY=$a&&$a(),e._remapZ=tl&&tl(),e._octaves=el&&el(),e._octaveMultiplier=il&&il(),e._octaveScale=rl&&rl(),e.name=vi,e.noise=new Vn,e.samplePosition=new F,e}e(r,t);var s=r.prototype;return s.animate=function(t,e){this.noise.setTime(t.particleSystem.time),this.noise.setSpeed(this.noiseSpeedX,this.noiseSpeedY,this.noiseSpeedZ),this.noise.setFrequency(this.noiseFrequency),this.noise.setAbs(this.remapX,this.remapY,this.remapZ),this.noise.setAmplititude(this.strengthX,this.strengthY,this.strengthZ),this.noise.setOctaves(this.octaves,this.octaveMultiplier,this.octaveScale),this.samplePosition.set(t.position),this.samplePosition.add3f(1*G(),1*G(),1*G()),this.noise.setSamplePoint(this.samplePosition),this.noise.getNoiseParticle();var i=this.noise.getResult();i.multiply3f(G(),G(),G()),F.add(t.position,t.position,i.multiplyScalar(e))},s.getNoisePreview=function(t,e,i,r){this.noise.setTime(e.time),this.noise.setSpeed(this.noiseSpeedX,this.noiseSpeedY,this.noiseSpeedZ),this.noise.setFrequency(this.noiseFrequency),this.noise.setAbs(this.remapX,this.remapY,this.remapZ),this.noise.setAmplititude(this.strengthX,this.strengthY,this.strengthZ),this.noise.setOctaves(this.octaves,this.octaveMultiplier,this.octaveScale),this.noise.getNoiseParticle(),this.noise.getPreview(t,i,r)},i(r,[{key:"enable",get:function(){return this._enable},set:function(t){this._enable!==t&&(this._enable=t,this.target&&this.target.enableModule(this.name,t,this))}},{key:"strengthX",get:function(){return this._strengthX},set:function(t){this._strengthX=t}},{key:"strengthY",get:function(){return this._strengthY},set:function(t){this._strengthY=t}},{key:"strengthZ",get:function(){return this._strengthZ},set:function(t){this._strengthZ=t}},{key:"noiseSpeedX",get:function(){return this._noiseSpeedX},set:function(t){this._noiseSpeedX=t}},{key:"noiseSpeedY",get:function(){return this._noiseSpeedY},set:function(t){this._noiseSpeedY=t}},{key:"noiseSpeedZ",get:function(){return this._noiseSpeedZ},set:function(t){this._noiseSpeedZ=t}},{key:"noiseFrequency",get:function(){return this._noiseFrequency},set:function(t){this._noiseFrequency=t}},{key:"remapX",get:function(){return this._remapX},set:function(t){this._remapX=t}},{key:"remapY",get:function(){return this._remapY},set:function(t){this._remapY=t}},{key:"remapZ",get:function(){return this._remapZ},set:function(t){this._remapZ=t}},{key:"octaves",get:function(){return this._octaves},set:function(t){this._octaves=t}},{key:"octaveMultiplier",get:function(){return this._octaveMultiplier},set:function(t){this._octaveMultiplier=t}},{key:"octaveScale",get:function(){return this._octaveScale},set:function(t){this._octaveScale=t}}]),r}(gi),Xa=w(Ha.prototype,"_enable",[ih],(function(){return!1})),r(Ha.prototype,"strengthX",[Fa],Object.getOwnPropertyDescriptor(Ha.prototype,"strengthX"),Ha.prototype),Wa=w(Ha.prototype,"_strengthX",[ih],(function(){return 10})),r(Ha.prototype,"strengthY",[Ca],Object.getOwnPropertyDescriptor(Ha.prototype,"strengthY"),Ha.prototype),ja=w(Ha.prototype,"_strengthY",[ih],(function(){return 10})),r(Ha.prototype,"strengthZ",[Ea],Object.getOwnPropertyDescriptor(Ha.prototype,"strengthZ"),Ha.prototype),Ya=w(Ha.prototype,"_strengthZ",[ih],(function(){return 10})),r(Ha.prototype,"noiseSpeedX",[za],Object.getOwnPropertyDescriptor(Ha.prototype,"noiseSpeedX"),Ha.prototype),Za=w(Ha.prototype,"_noiseSpeedX",[ih],(function(){return 0})),r(Ha.prototype,"noiseSpeedY",[Da],Object.getOwnPropertyDescriptor(Ha.prototype,"noiseSpeedY"),Ha.prototype),qa=w(Ha.prototype,"_noiseSpeedY",[ih],(function(){return 0})),r(Ha.prototype,"noiseSpeedZ",[Pa],Object.getOwnPropertyDescriptor(Ha.prototype,"noiseSpeedZ"),Ha.prototype),Qa=w(Ha.prototype,"_noiseSpeedZ",[ih],(function(){return 0})),r(Ha.prototype,"noiseFrequency",[Ia],Object.getOwnPropertyDescriptor(Ha.prototype,"noiseFrequency"),Ha.prototype),Ka=w(Ha.prototype,"_noiseFrequency",[ih],(function(){return 1})),r(Ha.prototype,"remapX",[Ba],Object.getOwnPropertyDescriptor(Ha.prototype,"remapX"),Ha.prototype),Ja=w(Ha.prototype,"_remapX",[ih],(function(){return 0})),r(Ha.prototype,"remapY",[La],Object.getOwnPropertyDescriptor(Ha.prototype,"remapY"),Ha.prototype),$a=w(Ha.prototype,"_remapY",[ih],(function(){return 0})),r(Ha.prototype,"remapZ",[Va],Object.getOwnPropertyDescriptor(Ha.prototype,"remapZ"),Ha.prototype),tl=w(Ha.prototype,"_remapZ",[ih],(function(){return 0})),r(Ha.prototype,"octaves",[Ua],Object.getOwnPropertyDescriptor(Ha.prototype,"octaves"),Ha.prototype),el=w(Ha.prototype,"_octaves",[ih],(function(){return 1})),r(Ha.prototype,"octaveMultiplier",[ka],Object.getOwnPropertyDescriptor(Ha.prototype,"octaveMultiplier"),Ha.prototype),il=w(Ha.prototype,"_octaveMultiplier",[ih],(function(){return.5})),r(Ha.prototype,"octaveScale",[Na],Object.getOwnPropertyDescriptor(Ha.prototype,"octaveScale"),Ha.prototype),rl=w(Ha.prototype,"_octaveScale",[ih],(function(){return 2})),Ga=Ha))||Ga),nh=new k,oh=new U,ah=Object.getOwnPropertyDescriptor(zt.prototype,"sharedMaterials"),lh=(sl=S("cc.ParticleSystem"),nl=J(99),ol=b($e),al=b(ai),ll=j("startSize"),ul=b(Me),hl=b(Me),cl=b(Me),_l=b(Me),dl=b(Me),pl=b(Me),fl=b(Me),ml=j("startRotation"),vl=b(Me),yl=b(Me),Ml=b(ai),gl=b(Me),Sl=b(Me),bl=b(Me),xl=b([Tn]),Tl=b(y),wl=b(li),Al=b(m),Ol=b(m),Rl=b(m),Fl=j("enableCulling"),Cl=b(xr),El=b(xr),zl=b(Rn),Dl=b(Rn),Pl=b(pn),Il=b(pn),Bl=b(xn),Ll=b(xn),Vl=b(Ar),Ul=b(Ar),kl=b(Cr),Nl=b(Cr),Gl=b(_n),Hl=b(_n),Xl=b(yn),Wl=b(yn),jl=b(sh),Yl=b(sh),Zl=b(qu),ql=b(qu),Ql=b(Nu),sl(Kl=nl((ku=function(t){function r(){var e;(e=t.call(this)||this).startColor=$l&&$l(),e.scaleSpace=tu&&tu(),e.startSize3D=eu&&eu(),e.startSizeX=iu&&iu(),e.startSizeY=ru&&ru(),e.startSizeZ=su&&su(),e.startSpeed=nu&&nu(),e.startRotation3D=ou&&ou(),e.startRotationX=au&&au(),e.startRotationY=lu&&lu(),e.startRotationZ=uu&&uu(),e.startDelay=hu&&hu(),e.startLifetime=cu&&cu(),e.duration=_u&&_u(),e.loop=du&&du(),e.simulationSpeed=pu&&pu(),e.playOnAwake=fu&&fu(),e.gravityModifier=mu&&mu(),e.rateOverTime=vu&&vu(),e.rateOverDistance=yu&&yu(),e.bursts=Mu&&Mu(),e._renderCulling=gu&&gu(),e._cullingMode=Su&&Su(),e._aabbHalfX=bu&&bu(),e._aabbHalfY=xu&&xu(),e._aabbHalfZ=Tu&&Tu(),e._dataCulling=wu&&wu(),e._colorOverLifetimeModule=Au&&Au(),e._shapeModule=Ou&&Ou(),e._sizeOvertimeModule=Ru&&Ru(),e._velocityOvertimeModule=Fu&&Fu(),e._forceOvertimeModule=Cu&&Cu(),e._limitVelocityOvertimeModule=Eu&&Eu(),e._rotationOvertimeModule=zu&&zu(),e._textureAnimationModule=Du&&Du(),e._noiseModule=Pu&&Pu(),e._trailModule=Iu&&Iu(),e.renderer=Bu&&Bu(),e._prewarm=Lu&&Lu(),e._capacity=Vu&&Vu(),e._simulationSpace=Uu&&Uu(),e.processor=null;var i=M(e);return i.rateOverTime.constant=10,i.startLifetime.constant=5,i.startSizeX.constant=1,i.startSpeed.constant=5,i._isPlaying=!1,i._isPaused=!1,i._isStopped=!0,i._isEmitting=!1,i._needToRestart=!1,i._needRefresh=!0,i._needAttach=!1,i._time=0,i._emitRateTimeCounter=0,i._emitRateDistanceCounter=0,i._oldWPos=new F,i._curWPos=new F,i._boundingBox=null,i._culler=null,i._oldPos=null,i._curPos=null,i._isCulled=!1,i._isSimulating=!0,i._customData1=new V,i._customData2=new V,i._subEmitters=[],e}e(r,t);var s=r.prototype;return s.onFocusInEditor=function(){this.renderer.create(this)},s.onLoad=function(){this.renderer.onInit(this),this._shapeModule&&this._shapeModule.onInit(this),this._trailModule&&!this.renderer.useGPU&&this._trailModule.enable&&this._trailModule.onInit(this),this.bindModule(),this._resetPosition()},s._onMaterialModified=function(t,e){null!==this.processor&&this.processor.onMaterialModified(t,e)},s._onRebuildPSO=function(t,e){this.processor.onRebuildPSO(t,e)},s._collectModels=function(){return this._models.length=0,this._models.push(this.processor.model),this._trailModule&&this._trailModule.enable&&this._trailModule.getModel()&&this._models.push(this._trailModule.getModel()),this._models},s._attachToScene=function(){this.processor.attachToScene(),this._trailModule&&this._trailModule.enable&&this._trailModule._attachToScene()},s._detachFromScene=function(){this.processor.detachFromScene(),this._trailModule&&this._trailModule.enable&&this._trailModule._detachFromScene(),this._boundingBox&&(this._boundingBox=null),this._culler&&(this._culler.clear(),this._culler.destroy(),this._culler=null)},s.bindModule=function(){this._colorOverLifetimeModule&&this._colorOverLifetimeModule.bindTarget(this.processor),this._sizeOvertimeModule&&this._sizeOvertimeModule.bindTarget(this.processor),this._rotationOvertimeModule&&this._rotationOvertimeModule.bindTarget(this.processor),this._forceOvertimeModule&&this._forceOvertimeModule.bindTarget(this.processor),this._limitVelocityOvertimeModule&&this._limitVelocityOvertimeModule.bindTarget(this.processor),this._velocityOvertimeModule&&this._velocityOvertimeModule.bindTarget(this.processor),this._textureAnimationModule&&this._textureAnimationModule.bindTarget(this.processor),this._noiseModule&&this._noiseModule.bindTarget(this.processor)},s.play=function(){if(this._needToRestart&&(this.reset(),this._needToRestart=!1),this._isPaused&&(this._isPaused=!1),this._isStopped&&(this._isStopped=!1),this._isPlaying=!0,this._isEmitting=!0,this._resetPosition(),this._prewarm&&this._prewarmSystem(),this._trailModule&&this._trailModule.play(),this.processor){var t=this.processor.getModel();t&&(t.enabled=this.enabledInHierarchy)}},s.pause=function(){this._isStopped?u("pause(): particle system is already stopped."):(this._isPlaying&&(this._isPlaying=!1),this._isPaused=!0)},s.stopEmitting=function(){this._isEmitting=!1,this._needToRestart=!0},s.stop=function(){(this._isPlaying||this._isPaused)&&this.clear(),this._isPlaying&&(this._isPlaying=!1),this._isPaused&&(this._isPaused=!1),this._isEmitting&&(this._isEmitting=!1),this._isStopped=!0,this._needRefresh=!0,this.reset()},s.reset=function(){this._time=0,this._emitRateTimeCounter=0,this._emitRateDistanceCounter=0,this._resetPosition(),this.bursts.forEach((function(t){t.reset()}))},s.clear=function(){this.enabledInHierarchy&&(this.processor.clear(),this._trailModule&&this._trailModule.clear()),this._calculateBounding(!1)},s.getParticleCount=function(){return this.processor?this.processor.getParticleCount():0},s.setCustomData1=function(t,e){V.set(this._customData1,t,e)},s.setCustomData2=function(t,e){V.set(this._customData2,t,e)},s.onDestroy=function(){var t;this.stop(),null!=(t=this.processor.getModel())&&t.scene&&(this.processor.detachFromScene(),this._trailModule&&this._trailModule.enable&&this._trailModule._detachFromScene()),Dt.off(Pt.BEFORE_COMMIT,this.beforeRender,this),this.processor.onDestroy(),this._trailModule&&this._trailModule.destroy(),this._culler&&(this._culler.clear(),this._culler.destroy(),this._culler=null)},s.onEnable=function(){t.prototype.onEnable.call(this),Dt.on(Pt.BEFORE_COMMIT,this.beforeRender,this),this.playOnAwake&&this.play(),this.processor.onEnable(),this._trailModule&&this._trailModule.onEnable()},s.onDisable=function(){Dt.off(Pt.BEFORE_COMMIT,this.beforeRender,this),this.processor.onDisable(),this._trailModule&&this._trailModule.onDisable(),this._boundingBox&&(this._boundingBox=null),this._oldPos=null,this._culler&&(this._culler.clear(),this._culler.destroy(),this._culler=null)},s._calculateBounding=function(t){var e=this;e._boundingBox&&(e._culler||(e._culler=new eh(e)),e._culler.calculatePositions(),Q.fromPoints(e._boundingBox,e._culler.minPos,e._culler.maxPos),t?(e.aabbHalfX=e._boundingBox.halfExtents.x,e.aabbHalfY=e._boundingBox.halfExtents.y,e.aabbHalfZ=e._boundingBox.halfExtents.z):(e.aabbHalfX?e.setBoundingX(e.aabbHalfX):e.aabbHalfX=e._boundingBox.halfExtents.x,e.aabbHalfY?e.setBoundingY(e.aabbHalfY):e.aabbHalfY=e._boundingBox.halfExtents.y,e.aabbHalfZ?e.setBoundingZ(e.aabbHalfZ):e.aabbHalfZ=e._boundingBox.halfExtents.z),e._culler.clear())},s.update=function(t){var e,i,r=this,s=r.processor,n=r.trailModule,o=t*r.simulationSpeed;if(r.renderCulling){r._boundingBox||(r._boundingBox=new Q,r._calculateBounding(!1)),r._curPos||(r._curPos=new F),r.node.getWorldPosition(r._curPos),r._oldPos||(r._oldPos=new F,r._oldPos.set(r._curPos));var a=r._curPos,l=r._oldPos;if(!a.equals(l)&&r._boundingBox&&r._culler){var u=a.x-l.x,h=a.y-l.y,c=a.z-l.z,_=r._boundingBox.center;_.x+=u,_.y+=h,_.z+=c,r._culler.setBoundingBoxCenter(_.x,_.y,_.z),l.set(a)}var d=r.node.scene.renderScene,p=d?d.cameras:void 0,f=!0;if(void 0!==p&&r._boundingBox)for(var m=0;m<p.length;++m){var v=p[m];if((v.visibility&r.node.layer)===r.node.layer&&K.aabbFrustum(r._boundingBox,v.frustum)){f=!1;break}}if(f){if(r._cullingMode!==li.AlwaysSimulate&&(r._isSimulating=!1),r._isCulled||(s.detachFromScene(),r._isCulled=!0),n&&n.enable&&n._detachFromScene(),r._cullingMode===li.PauseAndCatchup&&(r._time+=o),r._cullingMode!==li.AlwaysSimulate)return}else r._isCulled&&(r._attachToScene(),r._isCulled=!1),r._isSimulating||(r._isSimulating=!0);if(!r._isSimulating)return}else r._boundingBox&&(r._boundingBox=null),r._culler&&(r._culler.clear(),r._culler.destroy(),r._culler=null),r._isSimulating=!0;if(r._isPlaying)r._time+=o,r._emit(o),0!==s.updateParticles(o)||r._isEmitting||r.stop();else{var y=(r.getMaterialInstance(0)||s.getDefaultMaterial()).passes[0];s.updateRotation(y),s.updateScale(y)}r._needAttach&&r.getParticleCount()>0&&!r._isCulled&&(null!=(e=s.getModel())&&e.scene||s.attachToScene(),n&&n.enable&&(null!=(i=n.getModel())&&i.scene||n._attachToScene()),r._needAttach=!1);!r.renderer.useGPU&&n&&n.enable&&(n.inited||(n.clear(),n.destroy(),n.onInit(this),n.enable=!1,n.enable=!0))},s.beforeRender=function(){var t,e,i=this,r=i.processor,s=i.trailModule;i.getParticleCount()<=0?null!=(e=r.getModel())&&e.scene&&(r.detachFromScene(),s&&s.enable&&s._detachFromScene(),i._needAttach=!1):null!=(t=r.getModel())&&t.scene||(i._needAttach=!0),i._isPlaying&&(r.updateRenderData(),r.beforeRender(),s&&s.enable&&(s.updateRenderData(),s.beforeRender()))},s._onVisibilityChange=function(t){this.processor.model&&(this.processor.model.visFlags=t)},s.emit=function(t,e){var i=this,r=i.node,s=i._time%i.duration/i.duration;i._needRefresh&&(r.invalidateChildren(lt.POSITION),i._needRefresh=!1),i._simulationSpace===ai.World&&(r.getWorldMatrix(nh),r.getWorldRotation(oh));for(var n=0;n<t;++n){var o=i.processor.getFreeParticle();if(null===o)return;o.particleSystem=i,o.reset();var a=X(H(0,f));i._shapeModule&&i._shapeModule.enable?i._shapeModule.emit(o):(F.set(o.position,0,0,0),F.copy(o.velocity,Oi)),i._textureAnimationModule&&i._textureAnimationModule.enable&&i._textureAnimationModule.init(o);var l=i.startSpeed.evaluate(s,a);F.multiplyScalar(o.velocity,o.velocity,l),i._simulationSpace===ai.World&&(F.transformMat4(o.position,o.position,nh),F.transformQuat(o.velocity,o.velocity,oh)),F.copy(o.ultimateVelocity,o.velocity),i.startRotation3D?o.startEuler.set(i.startRotationX.evaluate(s,a),i.startRotationY.evaluate(s,a),i.startRotationZ.evaluate(s,a)):o.startEuler.set(0,0,i.startRotationZ.evaluate(s,a)),o.rotation.set(o.startEuler),i.startSize3D?F.set(o.startSize,i.startSizeX.evaluate(s,a),i.startSizeY.evaluate(s,a),i.startSizeZ.evaluate(s,a)):(F.set(o.startSize,i.startSizeX.evaluate(s,a),1,1),o.startSize.y=o.startSize.x),F.copy(o.size,o.startSize),o.startColor.set(i.startColor.evaluate(s,a)),o.color.set(o.startColor),o.startLifetime=i.startLifetime.evaluate(s,a)+e,o.remainingLifetime=o.startLifetime,o.randomSeed=H(0,233280),o.loopCount++,i.processor.setNewParticle(o)}},s._prewarmSystem=function(){this.startDelay.mode=ye.Constant,this.startDelay.constant=0;for(var t=this.duration/1,e=0;e<t;++e)this._time+=1,this._emit(1),this.processor.updateParticles(1)},s._emit=function(t){var e=this,i=e.startDelay.evaluate(0,1);if(e._time>i){if(e._time>e.duration+i&&(e.loop||(e._isEmitting=!1)),!e._isEmitting)return;if(e._emitRateTimeCounter+=e.rateOverTime.evaluate(e._time/e.duration,1)*t,e._emitRateTimeCounter>1){var r=Math.floor(e._emitRateTimeCounter);e._emitRateTimeCounter-=r,e.emit(r,t)}var s=e.rateOverDistance.evaluate(e._time/e.duration,1);if(s>0){F.copy(e._oldWPos,e._curWPos),e.node.getWorldPosition(e._curWPos);var n=F.distance(e._curWPos,e._oldWPos);e._emitRateDistanceCounter+=n*s}if(e._emitRateDistanceCounter>1){var o=Math.floor(e._emitRateDistanceCounter);e._emitRateDistanceCounter-=o,e.emit(o,t)}for(var a,l=h(e.bursts);!(a=l()).done;)a.value.update(e,t)}},s._resetPosition=function(){this.node.getWorldPosition(this._oldWPos),F.copy(this._curWPos,this._oldWPos)},s.addSubEmitter=function(t){this._subEmitters.push(t)},s.removeSubEmitter=function(t){this._subEmitters.splice(this._subEmitters.indexOf(t),1)},s.addBurst=function(t){this.bursts.push(t)},s.removeBurst=function(t){var e=this.bursts.indexOf(t);e>-1&&this.bursts.splice(e,1)},s.getBoundingX=function(){return this._aabbHalfX},s.getBoundingY=function(){return this._aabbHalfY},s.getBoundingZ=function(){return this._aabbHalfZ},s.setBoundingX=function(t){this._boundingBox&&this._culler&&(this._boundingBox.halfExtents.x=t,this._culler.setBoundingBoxSize(this._boundingBox.halfExtents),this._aabbHalfX=t)},s.setBoundingY=function(t){this._boundingBox&&this._culler&&(this._boundingBox.halfExtents.y=t,this._culler.setBoundingBoxSize(this._boundingBox.halfExtents),this._aabbHalfY=t)},s.setBoundingZ=function(t){this._boundingBox&&this._culler&&(this._boundingBox.halfExtents.z=t,this._culler.setBoundingBoxSize(this._boundingBox.halfExtents),this._aabbHalfZ=t)},s._onBeforeSerialize=function(t){var e=this;return this.dataCulling?t.filter((function(t){return!Mi.includes(t)||e[t]&&e[t].enable})):t},s.getNoisePreview=function(t,e){var i=[];return this.processor&&this.processor.getNoisePreview(i,t,e),i},i(r,[{key:"capacity",get:function(){return this._capacity},set:function(t){this._capacity=Math.floor(t>0?t:0),this.processor&&this.processor.model&&this.processor.model.setCapacity(this._capacity)}},{key:"prewarm",get:function(){return this._prewarm},set:function(t){!0===t&&this.loop,this._prewarm=t}},{key:"simulationSpace",get:function(){return this._simulationSpace},set:function(t){t!==this._simulationSpace&&(this._simulationSpace=t,this.processor&&(this.processor.updateMaterialParams(),this.processor.updateTrailMaterial()))}},{key:"renderCulling",get:function(){return this._renderCulling},set:function(t){this._renderCulling=t,t&&(this._boundingBox||(this._boundingBox=new Q,this._calculateBounding(!1)))}},{key:"cullingMode",get:function(){return this._cullingMode},set:function(t){this._cullingMode=t}},{key:"aabbHalfX",get:function(){return this.getBoundingX()||0},set:function(t){this.setBoundingX(t)}},{key:"aabbHalfY",get:function(){return this.getBoundingY()||0},set:function(t){this.setBoundingY(t)}},{key:"aabbHalfZ",get:function(){return this.getBoundingZ()||0},set:function(t){this.setBoundingZ(t)}},{key:"dataCulling",get:function(){return this._dataCulling},set:function(t){this._dataCulling=t}},{key:"sharedMaterials",get:function(){return ah.get.call(this)},set:function(t){ah.set.call(this,t)}},{key:"colorOverLifetimeModule",get:function(){return this._colorOverLifetimeModule},set:function(t){t&&(this._colorOverLifetimeModule=t)}},{key:"shapeModule",get:function(){return this._shapeModule},set:function(t){t&&(this._shapeModule=t)}},{key:"sizeOvertimeModule",get:function(){return this._sizeOvertimeModule},set:function(t){t&&(this._sizeOvertimeModule=t)}},{key:"velocityOvertimeModule",get:function(){return this._velocityOvertimeModule},set:function(t){t&&(this._velocityOvertimeModule=t)}},{key:"forceOvertimeModule",get:function(){return this._forceOvertimeModule},set:function(t){t&&(this._forceOvertimeModule=t)}},{key:"limitVelocityOvertimeModule",get:function(){return this._limitVelocityOvertimeModule},set:function(t){t&&(this._limitVelocityOvertimeModule=t)}},{key:"rotationOvertimeModule",get:function(){return this._rotationOvertimeModule},set:function(t){t&&(this._rotationOvertimeModule=t)}},{key:"textureAnimationModule",get:function(){return this._textureAnimationModule},set:function(t){t&&(this._textureAnimationModule=t)}},{key:"noiseModule",get:function(){return this._noiseModule},set:function(t){t&&(this._noiseModule=t)}},{key:"trailModule",get:function(){return this._trailModule},set:function(t){t&&(this._trailModule=t)}},{key:"isPlaying",get:function(){return this._isPlaying}},{key:"isPaused",get:function(){return this._isPaused}},{key:"isStopped",get:function(){return this._isStopped}},{key:"isEmitting",get:function(){return this._isEmitting}},{key:"time",get:function(){return this._time}}]),r}(Et),ku.CullingMode=li,$l=w((Jl=ku).prototype,"startColor",[ol,R],(function(){return new $e})),tu=w(Jl.prototype,"scaleSpace",[al,R],(function(){return ai.Local})),eu=w(Jl.prototype,"startSize3D",[R],(function(){return!1})),iu=w(Jl.prototype,"startSizeX",[ll,ul],(function(){return new Me})),ru=w(Jl.prototype,"startSizeY",[hl,R],(function(){return new Me})),su=w(Jl.prototype,"startSizeZ",[cl,R],(function(){return new Me})),nu=w(Jl.prototype,"startSpeed",[_l,R],(function(){return new Me})),ou=w(Jl.prototype,"startRotation3D",[R],(function(){return!1})),au=w(Jl.prototype,"startRotationX",[dl,R],(function(){return new Me})),lu=w(Jl.prototype,"startRotationY",[pl,R],(function(){return new Me})),uu=w(Jl.prototype,"startRotationZ",[fl,ml],(function(){return new Me})),hu=w(Jl.prototype,"startDelay",[vl,R],(function(){return new Me})),cu=w(Jl.prototype,"startLifetime",[yl,R],(function(){return new Me})),_u=w(Jl.prototype,"duration",[R],(function(){return 5})),du=w(Jl.prototype,"loop",[R],(function(){return!0})),r(Jl.prototype,"simulationSpace",[Ml,R],Object.getOwnPropertyDescriptor(Jl.prototype,"simulationSpace"),Jl.prototype),pu=w(Jl.prototype,"simulationSpeed",[R],(function(){return 1})),fu=w(Jl.prototype,"playOnAwake",[R],(function(){return!0})),mu=w(Jl.prototype,"gravityModifier",[gl,R],(function(){return new Me})),vu=w(Jl.prototype,"rateOverTime",[Sl,R],(function(){return new Me})),yu=w(Jl.prototype,"rateOverDistance",[bl,R],(function(){return new Me})),Mu=w(Jl.prototype,"bursts",[xl,R],(function(){return[]})),r(Jl.prototype,"renderCulling",[Tl],Object.getOwnPropertyDescriptor(Jl.prototype,"renderCulling"),Jl.prototype),gu=w(Jl.prototype,"_renderCulling",[R],(function(){return!1})),r(Jl.prototype,"cullingMode",[wl],Object.getOwnPropertyDescriptor(Jl.prototype,"cullingMode"),Jl.prototype),Su=w(Jl.prototype,"_cullingMode",[R],(function(){return li.Pause})),r(Jl.prototype,"aabbHalfX",[Al],Object.getOwnPropertyDescriptor(Jl.prototype,"aabbHalfX"),Jl.prototype),bu=w(Jl.prototype,"_aabbHalfX",[R],(function(){return 0})),r(Jl.prototype,"aabbHalfY",[Ol],Object.getOwnPropertyDescriptor(Jl.prototype,"aabbHalfY"),Jl.prototype),xu=w(Jl.prototype,"_aabbHalfY",[R],(function(){return 0})),r(Jl.prototype,"aabbHalfZ",[Rl],Object.getOwnPropertyDescriptor(Jl.prototype,"aabbHalfZ"),Jl.prototype),Tu=w(Jl.prototype,"_aabbHalfZ",[R],(function(){return 0})),wu=w(Jl.prototype,"_dataCulling",[R,Fl],(function(){return!1})),r(Jl.prototype,"sharedMaterials",[L,R],Object.getOwnPropertyDescriptor(Jl.prototype,"sharedMaterials"),Jl.prototype),Au=w(Jl.prototype,"_colorOverLifetimeModule",[Cl],(function(){return null})),r(Jl.prototype,"colorOverLifetimeModule",[El],Object.getOwnPropertyDescriptor(Jl.prototype,"colorOverLifetimeModule"),Jl.prototype),Ou=w(Jl.prototype,"_shapeModule",[zl],(function(){return null})),r(Jl.prototype,"shapeModule",[Dl],Object.getOwnPropertyDescriptor(Jl.prototype,"shapeModule"),Jl.prototype),Ru=w(Jl.prototype,"_sizeOvertimeModule",[Pl],(function(){return null})),r(Jl.prototype,"sizeOvertimeModule",[Il],Object.getOwnPropertyDescriptor(Jl.prototype,"sizeOvertimeModule"),Jl.prototype),Fu=w(Jl.prototype,"_velocityOvertimeModule",[Bl],(function(){return null})),r(Jl.prototype,"velocityOvertimeModule",[Ll],Object.getOwnPropertyDescriptor(Jl.prototype,"velocityOvertimeModule"),Jl.prototype),Cu=w(Jl.prototype,"_forceOvertimeModule",[Vl],(function(){return null})),r(Jl.prototype,"forceOvertimeModule",[Ul],Object.getOwnPropertyDescriptor(Jl.prototype,"forceOvertimeModule"),Jl.prototype),Eu=w(Jl.prototype,"_limitVelocityOvertimeModule",[kl],(function(){return null})),r(Jl.prototype,"limitVelocityOvertimeModule",[Nl],Object.getOwnPropertyDescriptor(Jl.prototype,"limitVelocityOvertimeModule"),Jl.prototype),zu=w(Jl.prototype,"_rotationOvertimeModule",[Gl],(function(){return null})),r(Jl.prototype,"rotationOvertimeModule",[Hl],Object.getOwnPropertyDescriptor(Jl.prototype,"rotationOvertimeModule"),Jl.prototype),Du=w(Jl.prototype,"_textureAnimationModule",[Xl],(function(){return null})),r(Jl.prototype,"textureAnimationModule",[Wl],Object.getOwnPropertyDescriptor(Jl.prototype,"textureAnimationModule"),Jl.prototype),Pu=w(Jl.prototype,"_noiseModule",[jl],(function(){return null})),r(Jl.prototype,"noiseModule",[Yl],Object.getOwnPropertyDescriptor(Jl.prototype,"noiseModule"),Jl.prototype),Iu=w(Jl.prototype,"_trailModule",[Zl],(function(){return null})),r(Jl.prototype,"trailModule",[ql],Object.getOwnPropertyDescriptor(Jl.prototype,"trailModule"),Jl.prototype),Bu=w(Jl.prototype,"renderer",[Ql,R],(function(){return new Nu})),Lu=w(Jl.prototype,"_prewarm",[R],(function(){return!1})),Vu=w(Jl.prototype,"_capacity",[R],(function(){return 100})),Uu=w(Jl.prototype,"_simulationSpace",[R],(function(){return ai.Local})),Kl=Jl))||Kl)||Kl);t({ParticleSystem:lh,ParticleSystemComponent:lh});var uh=t("ParticleUtils",function(){function t(){}return t.instantiate=function(t){this.registeredSceneEvent||(Dt.on(Pt.BEFORE_SCENE_LAUNCH,this.onSceneUnload,this),this.registeredSceneEvent=!0);var e=t._uuid;if(!this.particleSystemPool.has(e)){var i=new p((function(){return Bt(t)||new ut}),1,(function(t){return t.destroy()}));this.particleSystemPool.set(e,i)}return this.particleSystemPool.get(e).alloc()},t.destroy=function(t){var e,i,r=null==(e=t.prefab)||null==(i=e.asset)?void 0:i.uuid;r&&this.particleSystemPool.has(r)&&(this.stop(t),this.particleSystemPool.get(r).free(t))},t.play=function(t){for(var e,i=h(t.getComponentsInChildren(lh));!(e=i()).done;)e.value.play()},t.stop=function(t){for(var e,i=h(t.getComponentsInChildren(lh));!(e=i()).done;)e.value.stop()},t.onSceneUnload=function(){this.particleSystemPool.forEach((function(t){return t.destroy()})),this.particleSystemPool.clear()},t}());uh.particleSystemPool=new Map,uh.registeredSceneEvent=!1,$(Tn.prototype,"Burst.prototype",[{name:"minCount"},{name:"maxCount"}]),tt(lh.prototype,"ParticleSystem.prototype",[{name:"enableCulling",newName:"dataCulling"}]),et.ParticleSystemComponent=lh,g(lh,"cc.ParticleSystemComponent"),et.BillboardComponent=Yt,g(Yt,"cc.BillboardComponent"),et.LineComponent=ni,g(ni,"cc.LineComponent"),et.ParticleUtils=uh}}}));
