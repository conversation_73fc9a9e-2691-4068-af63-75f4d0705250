System.register(["./index-C5lmLqDW.js","./scene-7MDSMR3j.js","./debug-view-CKetkq9d.js","./gc-object-CKHc4SnS.js","./device-manager-BvjvoelW.js","./buffer-barrier-CuX_5NUR.js","./global-exports-CR3GRnjt.js"],(function(e){"use strict";var t,i,r,n,s,f,u,o,a,d,h,v,c,l,x,B,_,p;return{setters:[function(e){t=e.h,i=e.r,r=e.m,n=e.b,s=e.q},function(e){f=e.i},function(e){u=e.R},function(e){o=e.a},null,function(e){a=e.u,d=e.F,h=e.c,v=e.A,c=e.B,l=e.b,x=e.M,B=e.I,_=e.a},function(e){p=e.c}],execute:function(){var I;e({m:w,r:function(e,t,i,n,s,f){void 0===t&&(t=d.R32F),void 0===i&&(i=0),void 0===n&&(n=e.byteLength-i),void 0===s&&(s=0),void 0===f&&(f=[]);var u=h[t];s||(s=u.size);for(var o="get"+b(u),a=u.size/u.count,v=Math.floor(n/s),c=r.isLittleEndian,l=0;l<v;++l)for(var x=i+s*l,B=0;B<u.count;++B){var _=x+a*B;f[u.count*l+B]=e[o](_,c)}return f},w:function(e,t,i,n,s){void 0===i&&(i=d.R32F),void 0===n&&(n=0),void 0===s&&(s=0);var f=h[i];s||(s=f.size);for(var u="set"+b(f),o=f.size/f.count,a=Math.floor(t.length/f.count),v=r.isLittleEndian,c=0;c<a;++c)for(var l=n+s*c,x=0;x<f.count;++x){var B=l+o*x;e[u](B,t[f.count*c+x],v)}}}),t(f.prototype,"TextureBase.prototype",[{name:"hasPremultipliedAlpha"},{name:"setPremultiplyAlpha"},{name:"setFlipY"}]),i(u.prototype,"RenderTexture.prototype",[{name:"getGFXWindow",customFunction:function(){return this.window}}]);var g=((I={})[a.UNORM]="Uint",I[a.SNORM]="Int",I[a.UINT]="Uint",I[a.INT]="Int",I[a.UFLOAT]="Float",I[a.FLOAT]="Float",I.default="Uint",I);function b(e){return""+(g[e.type]||g.default)+e.size/e.count*8}function w(e,t,i,n,s,f,u){void 0===i&&(i=d.R32F),void 0===n&&(n=0),void 0===s&&(s=e.byteLength-n),void 0===f&&(f=0),u||(u=new DataView(e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength)));var o=h[i];f||(f=o.size);for(var a="set"+b(o),v="get"+b(o),c=o.size/o.count,l=Math.floor(s/f),x=r.isLittleEndian,B=0;B<l;++B)for(var _=n+f*B,p=0;p<o.count;++p){var I=_+c*p,g=e[v](I,x);u[a](I,t(g,p,e),x)}return u}var y={positions:new Float32Array,indices:new Uint8Array,boundingBox:{min:n.ZERO,max:n.ZERO}};e("R",function(){function e(e,t,i,r,n,s){void 0===r&&(r=null),void 0===n&&(n=null),void 0===s&&(s=!0),this.mesh=void 0,this.subMeshIdx=void 0,this._flatBuffers=[],this._jointMappedBuffers=void 0,this._jointMappedBufferIndices=void 0,this._vertexIdChannel=void 0,this._geometricInfo=void 0,this._vertexBuffers=void 0,this._drawInfo=null,this._attributes=t,this._vertexBuffers=e,this._indexBuffer=r,this._indirectBuffer=n,this._primitiveMode=i,this._iaInfo=new B(t,e,r,n),this._isOwnerOfIndexBuffer=s}var t=e.prototype;return t.invalidateGeometricInfo=function(){this._geometricInfo=void 0},t.genFlatBuffers=function(){if(!this._flatBuffers.length&&this.mesh&&void 0!==this.subMeshIdx){var e=this.mesh,t=0,i=e.struct.primitives[this.subMeshIdx];i.indexView&&(t=i.indexView.count);for(var r=0;r<i.vertexBundelIndices.length;r++){var n=i.vertexBundelIndices[r],s=e.struct.vertexBundles[n],f=i.indexView?i.indexView.count:s.view.count,u=s.view.stride,o=u*f,a=new Uint8Array(e.data.buffer,s.view.offset,s.view.length),d=new Uint8Array(i.indexView?o:s.view.length);if(i.indexView){for(var h=e.readIndices(this.subMeshIdx),v=0;v<t;++v)for(var c=v*u,l=h[v]*u,x=0;x<u;++x)d[c+x]=a[l+x];this._flatBuffers.push({stride:u,count:f,buffer:d})}else d.set(e.data.subarray(s.view.offset,s.view.offset+s.view.length)),this._flatBuffers.push({stride:u,count:f,buffer:d})}}},t.destroy=function(){for(var e=0;e<this.vertexBuffers.length;e++)this.vertexBuffers[e].destroy();if(this.vertexBuffers.length=0,this._indexBuffer&&(this._isOwnerOfIndexBuffer&&this._indexBuffer.destroy(),this._indexBuffer=null),this._jointMappedBuffers&&this._jointMappedBufferIndices){for(var t=0;t<this._jointMappedBufferIndices.length;t++)this._jointMappedBuffers[this._jointMappedBufferIndices[t]].destroy();this._jointMappedBuffers=void 0,this._jointMappedBufferIndices=void 0}this._indirectBuffer&&(this._indirectBuffer.destroy(),this._indirectBuffer=null)},t.enableVertexIdChannel=function(e){if(!this._vertexIdChannel){var t=this.vertexBuffers.length,i=this.attributes.length,r=this._allocVertexIdBuffer(e);this._vertexBuffers.push(r),this._attributes.push(new v("a_vertexId",d.R32F,!1,t)),this._iaInfo.attributes=this._attributes,this._iaInfo.vertexBuffers=this._vertexBuffers,this._vertexIdChannel={stream:t,index:i}}},t._allocVertexIdBuffer=function(e){for(var t=0===this.vertexBuffers.length||0===this.vertexBuffers[0].stride?0:this.vertexBuffers[0].size/this.vertexBuffers[0].stride,i=new Float32Array(t),r=0;r<t;++r)i[r]=r+.5;var n=e.createBuffer(new c(l.VERTEX|l.TRANSFER_DST,x.DEVICE,i.byteLength,i.BYTES_PER_ELEMENT));return n.update(i),n},o(e,[{key:"attributes",get:function(){return this._attributes}},{key:"vertexBuffers",get:function(){return this._vertexBuffers}},{key:"indexBuffer",get:function(){return this._indexBuffer}},{key:"indirectBuffer",get:function(){return this._indirectBuffer}},{key:"primitiveMode",get:function(){return this._primitiveMode}},{key:"geometricInfo",get:function(){if(this._geometricInfo)return this._geometricInfo;if(void 0===this.mesh)return y;if(void 0===this.subMeshIdx)return y;var e,t=this.mesh,i=this.subMeshIdx,r=this.attributes.find((function(e){return e.name===_.ATTR_POSITION}));if(!r)return y;switch(r.format){case d.RG32F:case d.RGB32F:if(!(e=t.readAttribute(i,_.ATTR_POSITION)))return y;break;case d.RGBA32F:var f=t.readAttribute(i,_.ATTR_POSITION);if(!f)return y;var u=f.length/4;e=new Float32Array(3*u);for(var o=0;o<u;++o){var a=3*o,v=4*o;e[a]=f[v],e[a+1]=f[v+1],e[a+2]=f[v+2]}break;case d.RG16F:case d.RGB16F:var c=t.readAttribute(i,_.ATTR_POSITION);if(!c)return y;e=new Float32Array(c.length);for(var l=0;l<c.length;++l)e[l]=s(c[l]);break;case d.RGBA16F:var x=t.readAttribute(i,_.ATTR_POSITION);if(!x)return y;var B=x.length/4;e=new Float32Array(3*B);for(var p=0;p<B;++p){var I=3*p,g=4*p;e[I]=s(x[g]),e[I+1]=s(x[g+1]),e[I+2]=s(x[g+2])}break;default:return y}var b=t.readIndices(i)||void 0,w=new n,m=new n,M=h[r.format].count;2===M?(w.set(e[0],e[1],0),m.set(e[0],e[1],0)):(w.set(e[0],e[1],e[2]),m.set(e[0],e[1],e[2]));for(var T=0;T<e.length;T+=M)2===M?(w.x=e[T]>w.x?e[T]:w.x,w.y=e[T+1]>w.y?e[T+1]:w.y,m.x=e[T]<m.x?e[T]:m.x,m.y=e[T+1]<m.y?e[T+1]:m.y):(w.x=e[T]>w.x?e[T]:w.x,w.y=e[T+1]>w.y?e[T+1]:w.y,w.z=e[T+2]>w.z?e[T+2]:w.z,m.x=e[T]<m.x?e[T]:m.x,m.y=e[T+1]<m.y?e[T+1]:m.y,m.z=e[T+2]<m.z?e[T+2]:m.z);return this._geometricInfo={positions:e,indices:b,boundingBox:{max:w,min:m}},this._geometricInfo}},{key:"drawInfo",get:function(){return this._drawInfo},set:function(e){this._drawInfo=e}},{key:"flatBuffers",get:function(){return this._flatBuffers}},{key:"jointMappedBuffers",get:function(){var e=this;if(this._jointMappedBuffers)return this._jointMappedBuffers;var t=this._jointMappedBuffers=[],i=this._jointMappedBufferIndices=[];if(!this.mesh||void 0===this.subMeshIdx)return this._jointMappedBuffers=this.vertexBuffers;var r,n,s=this.mesh.struct,f=s.primitives[this.subMeshIdx];if(!s.jointMaps||void 0===f.jointMapIndex||!s.jointMaps[f.jointMapIndex])return this._jointMappedBuffers=this.vertexBuffers;for(var u=p.director.root.device,o=function(){var o=s.vertexBundles[f.vertexBundelIndices[a]];n=0,r=d.UNKNOWN;for(var v=0;v<o.attributes.length;v++){var B=o.attributes[v];if(B.name===_.ATTR_JOINTS){r=B.format;break}n+=h[B.format].size}if(r){var p=new Uint8Array(e.mesh.data.buffer,o.view.offset,o.view.length),I=new DataView(p.slice().buffer),g=s.jointMaps[f.jointMapIndex];w(I,(function(e){return g.indexOf(e)}),r,n,o.view.length,o.view.stride,I);var b=u.createBuffer(new c(l.VERTEX|l.TRANSFER_DST,x.DEVICE,o.view.length,o.view.stride));b.update(I.buffer),t.push(b),i.push(a)}else t.push(e.vertexBuffers[f.vertexBundelIndices[a]])},a=0;a<f.vertexBundelIndices.length;a++)o();return this._vertexIdChannel&&t.push(this._allocVertexIdBuffer(u)),t}},{key:"iaInfo",get:function(){return this._iaInfo}}]),e}())}}}));
