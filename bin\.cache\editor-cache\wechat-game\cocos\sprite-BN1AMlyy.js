System.register(["./gc-object-CKHc4SnS.js","./index-C5lmLqDW.js","./global-exports-CR3GRnjt.js","./rendering-sub-mesh-CowWLfXC.js","./component-BaGvu7EF.js","./factory-D9_8ZCqM.js","./debug-view-CKetkq9d.js","./scene-7MDSMR3j.js","./sprite-frame-C6JiNTOk.js","./ui-renderer-DuhVjkfF.js","./node-event-DTNosVQv.js"],(function(t){"use strict";var e,r,i,a,s,n,o,p,l,u,_,c,h,f,y,d,m,D,F,T,A,M,R,S;return{setters:[function(t){e=t._,r=t.q,i=t.r,a=t.t,s=t.w,n=t.a,o=t.b},function(t){p=t.c,l=t.a,u=t.s,_=t.t,c=t.i,h=t.g,f=t.V},function(t){y=t.c},null,function(t){d=t.A},function(t){m=t.P},function(t){D=t.R},function(t){F=t.i,T=t.b},function(t){A=t.S,M=t.a},function(t){R=t.I,S=t.U},null],execute:function(){var g,E,U,v,L,I,C,P,b,V,O,z,k,j,x,G,w,B,H,N,W,Z,q,Y,X,J,K,Q=t("b",p("cc.SpriteAtlas")((E=function(t){function a(e){var r;return(r=t.call(this,e)||this).spriteFrames=U&&U(),r}e(a,t);var s=a.prototype;return s.getTexture=function(){var t=Object.keys(this.spriteFrames);if(t.length>0){var e=this.spriteFrames[t[0]];return e&&e.texture}return null},s.getSpriteFrame=function(t){var e=this.spriteFrames[t];return e?(e.name||(e.name=t),e):null},s.getSpriteFrames=function(){var t=[],e=this.spriteFrames;for(var r in e)t.push(e[r]);return t},s._serialize=function(){return null},s._deserialize=function(t,e){var a=t;this._name=a.name;var s=a.spriteFrames;this.spriteFrames=r();for(var n=0;n<s.length;n+=2)e.result.push(this.spriteFrames,s[n],s[n+1],i(A))},a}(d),U=l(E.prototype,"spriteFrames",[u],(function(){return r()})),g=E))||g);y.SpriteAtlas=Q,function(t){t[t.SIMPLE=0]="SIMPLE",t[t.SLICED=1]="SLICED",t[t.TILED=2]="TILED",t[t.FILLED=3]="FILLED"}(Y||(Y={})),a(Y),function(t){t[t.HORIZONTAL=0]="HORIZONTAL",t[t.VERTICAL=1]="VERTICAL",t[t.RADIAL=2]="RADIAL"}(X||(X={})),a(X),function(t){t[t.CUSTOM=0]="CUSTOM",t[t.TRIMMED=1]="TRIMMED",t[t.RAW=2]="RAW"}(J||(J={})),a(J),t("a",K),function(t){t.SPRITE_FRAME_CHANGED="spriteframe-changed"}(K||t("a",K={}));var $=t("S",(v=p("cc.Sprite"),L=c(110),I=_(Q),C=_(A),P=_(Y),b=_(X),V=_(J),v(O=L((q=function(t){function r(){var e;return(e=t.call(this)||this)._spriteFrame=k&&k(),e._type=j&&j(),e._fillType=x&&x(),e._sizeMode=G&&G(),e._fillCenter=w&&w(),e._fillStart=B&&B(),e._fillRange=H&&H(),e._isTrimmedMode=N&&N(),e._useGrayscale=W&&W(),e._atlas=Z&&Z(),e}e(r,t);var i=r.prototype;return i.__preload=function(){this.changeMaterialForDefine(),t.prototype.__preload.call(this)},i.onEnable=function(){t.prototype.onEnable.call(this),this._activateMaterial();var e=this._spriteFrame;e&&(this._updateUVs(),this._type===Y.SLICED&&e.on(M.UV_UPDATED,this._updateUVs,this))},i.onDisable=function(){t.prototype.onDisable.call(this),this._spriteFrame&&this._type===Y.SLICED&&this._spriteFrame.off(M.UV_UPDATED,this._updateUVs,this)},i.onDestroy=function(){t.prototype.onDestroy.call(this)},i.changeSpriteFrameFromAtlas=function(t){if(this._atlas){var e=this._atlas.getSpriteFrame(t);this.spriteFrame=e}else s(16377)},i.changeMaterialForDefine=function(){var t,e=this._instanceMaterialType;this._spriteFrame&&(t=this._spriteFrame.texture);var r=!1;if(t instanceof F){var i=t.getPixelFormat();r=i===m.RGBA_ETC1||i===m.RGB_A_PVRTC_4BPPV1||i===m.RGB_A_PVRTC_2BPPV1}r&&this.grayscale?this._instanceMaterialType=R.USE_ALPHA_SEPARATED_AND_GRAY:r?this._instanceMaterialType=R.USE_ALPHA_SEPARATED:this.grayscale?this._instanceMaterialType=R.GRAYSCALE:this._instanceMaterialType=R.ADD_COLOR_AND_TEXTURE,e!==this._instanceMaterialType&&this.updateMaterial()},i._updateBuiltinMaterial=function(){var e=t.prototype._updateBuiltinMaterial.call(this);if(this.spriteFrame&&this.spriteFrame.texture instanceof D){var r=new T;r.copy(e,{defines:{SAMPLE_FROM_RT:!0}}),e=r}return e},i._render=function(t){t.commitComp(this,this.renderData,this._spriteFrame,this._assembler,null)},i._canRender=function(){if(!t.prototype._canRender.call(this))return!1;var e=this._spriteFrame;return!(!e||!e.texture)},i._flushAssembler=function(){var t=this,e=r.Assembler.getAssembler(t);t._assembler!==e&&(t.destroyRenderData(),t._assembler=e),t._renderData||e&&e.createData&&((t._renderData=e.createData(t)).material=t.getRenderMaterial(0),t._markForUpdateRenderData(),t.spriteFrame&&e.updateUVs(t),t._updateColor());var i=t._spriteFrame;i&&(t._type===Y.SLICED?i.on(M.UV_UPDATED,t._updateUVs,t):i.off(M.UV_UPDATED,t._updateUVs,t))},i._applySpriteSize=function(){var t=this,e=t._spriteFrame;if(e){var r=t.node._uiProps;if(J.RAW===t._sizeMode){var i=e.originalSize;r.uiTransformComp.setContentSize(i)}else if(J.TRIMMED===t._sizeMode){var a=e.rect;r.uiTransformComp.setContentSize(a.width,a.height)}}},i._resized=function(){},i._activateMaterial=function(){var t=this._spriteFrame,e=this.getRenderMaterial(0);t&&e&&this._markForUpdateRenderData(),this.renderData&&(this.renderData.material=e)},i._updateUVs=function(){this._assembler&&this._assembler.updateUVs(this)},i._applySpriteFrame=function(t){var e=this,r=e._spriteFrame;t&&e._type===Y.SLICED&&t.off(M.UV_UPDATED,e._updateUVs,e);var i=!1;r&&(t&&t.texture===r.texture||(i=!0),i&&(e.renderData&&(e.renderData.textureDirty=!0),(!!t&&t.texture instanceof D)!=r.texture instanceof D&&(e._instanceMaterialType=-1),e.changeMaterialForDefine()),e._applySpriteSize(),e._type===Y.SLICED&&r.on(M.UV_UPDATED,e._updateUVs,e))},n(r,[{key:"spriteAtlas",get:function(){return this._atlas},set:function(t){this._atlas!==t&&(this._atlas=t)}},{key:"spriteFrame",get:function(){return this._spriteFrame},set:function(t){if(this._spriteFrame!==t){var e=this._spriteFrame;this._spriteFrame=t,this._markForUpdateRenderData(),this._applySpriteFrame(e)}}},{key:"type",get:function(){return this._type},set:function(t){this._type!==t&&(this._type=t,this._flushAssembler())}},{key:"fillType",get:function(){return this._fillType},set:function(t){this._fillType!==t&&(t===X.RADIAL||this._fillType===X.RADIAL?this.destroyRenderData():this.renderData&&this._markForUpdateRenderData(!0)),this._fillType=t,this._flushAssembler()}},{key:"fillCenter",get:function(){return this._fillCenter},set:function(t){this._fillCenter.x=t.x,this._fillCenter.y=t.y,this._type===Y.FILLED&&this.renderData&&this._markForUpdateRenderData()}},{key:"fillStart",get:function(){return this._fillStart},set:function(t){this._fillStart=h(t,0,1),this._type===Y.FILLED&&this.renderData&&(this._markForUpdateRenderData(),this._updateUVs())}},{key:"fillRange",get:function(){return this._fillRange},set:function(t){this._fillRange=h(t,-1,1),this._type===Y.FILLED&&this.renderData&&(this._markForUpdateRenderData(),this._updateUVs())}},{key:"trim",get:function(){return this._isTrimmedMode},set:function(t){this._isTrimmedMode!==t&&(this._isTrimmedMode=t,this._type===Y.SIMPLE&&this.renderData&&this._markForUpdateRenderData(!0))}},{key:"grayscale",get:function(){return this._useGrayscale},set:function(t){this._useGrayscale!==t&&(this._useGrayscale=t,this.changeMaterialForDefine(),this.updateMaterial())}},{key:"sizeMode",get:function(){return this._sizeMode},set:function(t){this._sizeMode!==t&&(this._sizeMode=t,t!==J.CUSTOM&&this._applySpriteSize())}}]),r}(S),q.FillType=X,q.Type=Y,q.SizeMode=J,q.EventType=K,o((z=q).prototype,"spriteAtlas",[I],Object.getOwnPropertyDescriptor(z.prototype,"spriteAtlas"),z.prototype),o(z.prototype,"spriteFrame",[C],Object.getOwnPropertyDescriptor(z.prototype,"spriteFrame"),z.prototype),o(z.prototype,"type",[P],Object.getOwnPropertyDescriptor(z.prototype,"type"),z.prototype),o(z.prototype,"fillType",[b],Object.getOwnPropertyDescriptor(z.prototype,"fillType"),z.prototype),o(z.prototype,"sizeMode",[V],Object.getOwnPropertyDescriptor(z.prototype,"sizeMode"),z.prototype),k=l(z.prototype,"_spriteFrame",[u],(function(){return null})),j=l(z.prototype,"_type",[u],(function(){return Y.SIMPLE})),x=l(z.prototype,"_fillType",[u],(function(){return X.HORIZONTAL})),G=l(z.prototype,"_sizeMode",[u],(function(){return J.TRIMMED})),w=l(z.prototype,"_fillCenter",[u],(function(){return new f(0,0)})),B=l(z.prototype,"_fillStart",[u],(function(){return 0})),H=l(z.prototype,"_fillRange",[u],(function(){return 0})),N=l(z.prototype,"_isTrimmedMode",[u],(function(){return!0})),W=l(z.prototype,"_useGrayscale",[u],(function(){return!1})),Z=l(z.prototype,"_atlas",[u],(function(){return null})),O=z))||O)||O));y.Sprite=$}}}));
