System.register(["./gc-object-D18ulfCO.js","./index-Y4La_nfG.js","./global-exports-CLZKKIY2.js","./component-CsuvAQKv.js","./sprite-renderer-rngMmqx7.js","./ui-renderer-CboX9P_t.js","./label-DLrq6Qwj.js","./ZipUtils-0TovRG4S.js","./zlib.min-CyXMsivM.js","./scene-ArUG4OfI.js","./prefab-BQYc0LyR.js","./pipeline-state-manager-DQyhxoC_.js","./node-event-DTNosVQv.js","./deprecated-Bf8XgTPJ.js","./director-8iUu7HD2.js","./sprite-V3bxgKTL.js","./device-manager-B8lyUDPi.js","./buffer-barrier-q_79u36H.js","./sprite-frame-n8bfYych.js","./debug-view-BP17WHcy.js","./factory-BOc5khhM.js","./camera-component-X7pwLmnP.js","./deprecated-D5UVm7fE.js","./deprecated-C_Nm0tQW.js","./model-renderer-D7qfPDfZ.js","./renderer-CZheciPr.js","./touch-B157r-vS.js","./create-mesh-o_2FMF_K.js","./mesh-Ba1cTOGw.js","./wasm-minigame-DBi57dFz.js"],(function(t){"use strict";var e,i,r,n,s,o,a,h,l,u,f,g,d,_,p,c,m,y,v,A,w,S,x,I,T,b,C,D,N,O,E,G,F,L,P,z,R,M,U,X,j,H,k,B,Y,V,Z,K,W,q;return{setters:[function(t){e=t.r,i=t.Q,r=t.K,n=t.h,s=t.F,o=t.a,a=t._,h=t.b,l=t.l,u=t.w,f=t.d,g=t.C,d=t.f,_=t.o},function(t){p=t.V,c=t.C,m=t.z,y=t.k,v=t.c,A=t.t,w=t.a,S=t.J,x=t.M,I=t.b,T=t.s,b=t.m},function(t){C=t.b},function(t){D=t.C,N=t.A},null,function(t){O=t.c,E=t.e,G=t.f,F=t.h,L=t.R,P=t.b,z=t.U,R=t.i,M=t.d},function(t){U=t.L},function(t){X=t.c},function(t){j=t._},function(t){H=t.k,k=t.N},null,null,function(t){B=t.N},null,function(t){Y=t.d,V=t.D},function(t){Z=t.S},null,function(t){K=t.af},function(t){W=t.S},null,function(t){q=t.a},null,null,null,null,null,null,null,null,null],execute:function(){var J,Q,$,tt,et,it,rt;!function(t){t[t.ORTHO=0]="ORTHO",t[t.HEX=1]="HEX",t[t.ISO=2]="ISO"}(J||(J={})),e(J),function(t){t[t.NONE=0]="NONE",t[t.MAP=1]="MAP",t[t.LAYER=2]="LAYER",t[t.OBJECTGROUP=3]="OBJECTGROUP",t[t.OBJECT=4]="OBJECT",t[t.TILE=5]="TILE"}(Q||(Q={})),e(Q),function(t){t[t.HORIZONTAL=2147483648]="HORIZONTAL",t[t.VERTICAL=1073741824]="VERTICAL",t[t.DIAGONAL=536870912]="DIAGONAL",t[t.FLIPPED_ALL=4026531840]="FLIPPED_ALL",t[t.FLIPPED_MASK=268435455]="FLIPPED_MASK"}($||($={})),e($),function(t){t[t.STAGGERAXIS_X=0]="STAGGERAXIS_X",t[t.STAGGERAXIS_Y=1]="STAGGERAXIS_Y"}(tt||(tt={})),e(tt),function(t){t[t.STAGGERINDEX_ODD=0]="STAGGERINDEX_ODD",t[t.STAGGERINDEX_EVEN=1]="STAGGERINDEX_EVEN"}(et||(et={})),e(et),function(t){t[t.RightDown=0]="RightDown",t[t.RightUp=1]="RightUp",t[t.LeftDown=2]="LeftDown",t[t.LeftUp=3]="LeftUp"}(it||(it={})),e(it),function(t){t[t.RECT=0]="RECT",t[t.ELLIPSE=1]="ELLIPSE",t[t.POLYGON=2]="POLYGON",t[t.POLYLINE=3]="POLYLINE",t[t.IMAGE=4]="IMAGE",t[t.TEXT=5]="TEXT"}(rt||(rt={})),e(rt);var nt=function(){function t(){this.name="",this.firstGid=0,this.spacing=0,this.margin=0,this.sourceImage=void 0,this.imageName=null,this.imageOffset=null,this.imageSize=new m(0,0),this.tileOffset=new p(0,0),this._tileSize=new m(0,0),this.collection=!1}return t.prototype.rectForGID=function(t,e){var i=e||new y(0,0,0,0);i.width=this._tileSize.width,i.height=this._tileSize.height;var r=t;if(r&=$.FLIPPED_MASK,r-=this.firstGid,this.imageOffset)i.x=this.imageOffset.x,i.y=this.imageOffset.y;else{var n=Math.floor((this.imageSize.width-2*this.margin+this.spacing)/(this._tileSize.width+this.spacing));i.x=Math.round(r%n*(this._tileSize.width+this.spacing)+this.margin),i.y=Math.round(Math.floor(r/n)*(this._tileSize.height+this.spacing)+this.margin)}return i},t}(),st=function(){function t(){this.properties={},this.name="",this.objects=[],this.visible=!0,this.opacity=0,this.color=new c(255,255,255,255),this.offset=new p(0,0),this.draworder="topdown",this.tintColor=null}var e=t.prototype;return e.getProperties=function(){return this.properties},e.setProperties=function(t){this.properties=t},t}(),ot=function(){function t(){this.properties={},this.name="",this.layerSize=null,this.tiles=[],this.visible=!0,this.opacity=0,this.ownTiles=!0,this.minGID=1e5,this.maxGID=0,this.offset=new p(0,0),this.tintColor=null}var e=t.prototype;return e.getProperties=function(){return this.properties},e.setProperties=function(t){this.properties=t},t}();ot.ATTRIB_NONE=1,ot.ATTRIB_BASE64=2,ot.ATTRIB_GZIP=4,ot.ATTRIB_ZLIB=8;var at=function(){this.name="",this.visible=!0,this.width=0,this.height=0,this.offset=new p(0,0),this.opacity=0,this.trans=new c(255,255,255,255),this.sourceImage=void 0,this.tintColor=null};function ht(t){if(t.length%4!=0)return null;for(var e=t.length/4,i=window.Uint32Array?new Uint32Array(e):[],r=0;r<e;r++){var n=4*r;i[r]=t[n]+256*t[n+1]+65536*t[n+2]+t[n+3]*(1<<24)}return i}function lt(t){var e=U.HorizontalAlign;switch(t){case"center":return e.CENTER;case"right":return e.RIGHT;default:return e.LEFT}}function ut(t){var e=U.VerticalAlign;switch(t){case"center":return e.CENTER;case"bottom":return e.BOTTOM;default:return e.TOP}}function ft(t){if(!t)return new c(0,0,0,255);if(8===(t=-1!==t.indexOf("#")?t.substring(1):t).length){var e=parseInt(t.substr(0,2),16)||255,i=parseInt(t.substr(2,2),16)||0,r=parseInt(t.substr(4,2),16)||0,n=parseInt(t.substr(6,2),16)||0;return new c(i,r,n,e)}var s=parseInt(t.substr(0,2),16)||0,o=parseInt(t.substr(2,2),16)||0,a=parseInt(t.substr(4,2),16)||0;return new c(s,o,a,255)}function gt(t,e){for(var i=[],r=Array.from(t.getElementsByTagName("properties")).filter((function(e){return e.parentNode===t})),n=0;n<r.length;++n)for(var s=r[n].getElementsByTagName("property"),o=0;o<s.length;++o)i.push(s[o]);e=e||{};for(var a=0;a<i.length;a++){var h=i[a],l=h.getAttribute("name"),u=h.getAttribute("type")||"string",f=h.getAttribute("value");"int"===u?f=parseInt(f):"float"===u?f=parseFloat(f):"bool"===u?f="true"===f:"color"===u&&(f=ft(f)),e[l]=f}return e}var dt,_t,pt,ct,mt,yt,vt,At,wt,St,xt,It,Tt,bt=function(){function t(t,e,i,r,n){this.properties={},this.orientation=null,this.parentElement=null,this.parentGID=0,this.layerAttrs=0,this.storingCharacters=!1,this.currentString=null,this.renderOrder=it.RightDown,this._supportVersion=[1,4,0],this._objectGroups=[],this._allChildren=[],this._mapSize=new m(0,0),this._tileSize=new m(0,0),this._layers=[],this._tilesets=[],this._imageLayers=[],this._tileProperties=new Map,this._tileAnimations={},this._tsxContentMap=null,this._spriteFrameMap=null,this._spfSizeMap={},this._staggerAxis=null,this._staggerIndex=null,this._hexSideLength=0,this._imageLayerSPF=null,this.initWithXML(t,e,i,r,n)}var e=t.prototype;return e.getOrientation=function(){return this.orientation},e.setOrientation=function(t){this.orientation=t},e.getStaggerAxis=function(){return this._staggerAxis},e.setStaggerAxis=function(t){this._staggerAxis=t},e.getStaggerIndex=function(){return this._staggerIndex},e.setStaggerIndex=function(t){this._staggerIndex=t},e.getHexSideLength=function(){return this._hexSideLength},e.setHexSideLength=function(t){this._hexSideLength=t},e.getMapSize=function(){return new m(this._mapSize.width,this._mapSize.height)},e.setMapSize=function(t){this._mapSize.width=t.width,this._mapSize.height=t.height},e.getTileSize=function(){return new m(this._tileSize.width,this._tileSize.height)},e.setTileSize=function(t){this._tileSize.width=t.width,this._tileSize.height=t.height},e.getLayers=function(){return this._layers},e.setLayers=function(t){this._allChildren.push(t),this._layers.push(t)},e.getImageLayers=function(){return this._imageLayers},e.setImageLayers=function(t){this._allChildren.push(t),this._imageLayers.push(t)},e.getTilesets=function(){return this._tilesets},e.setTilesets=function(t){this._tilesets.push(t)},e.getObjectGroups=function(){return this._objectGroups},e.setObjectGroups=function(t){this._allChildren.push(t),this._objectGroups.push(t)},e.getAllChildren=function(){return this._allChildren},e.getParentElement=function(){return this.parentElement},e.setParentElement=function(t){this.parentElement=t},e.getParentGID=function(){return this.parentGID},e.setParentGID=function(t){this.parentGID=t},e.getLayerAttribs=function(){return this.layerAttrs},e.setLayerAttribs=function(t){this.layerAttrs=t},e.getStoringCharacters=function(){return this.storingCharacters},e.setStoringCharacters=function(t){this.storingCharacters=t},e.getProperties=function(){return this.properties},e.setProperties=function(t){this.properties=t},e.initWithXML=function(t,e,i,r,n){return this._tilesets.length=0,this._layers.length=0,this._imageLayers.length=0,this._tsxContentMap=e,this._spriteFrameMap=i,this._imageLayerSPF=n,this._spfSizeMap=r,this._objectGroups.length=0,this._allChildren.length=0,this.properties={},this._tileProperties=new Map,this._tileAnimations=new Map,this.currentString="",this.storingCharacters=!1,this.layerAttrs=ot.ATTRIB_NONE,this.parentElement=null,this.parseXMLString(t)},e.parseXMLString=function(e,o){var a,h=(new H).parse(e).documentElement,l=h.getAttribute("orientation"),u=h.getAttribute("staggeraxis"),f=h.getAttribute("staggerindex"),g=h.getAttribute("hexsidelength"),d=h.getAttribute("renderorder"),_=h.getAttribute("version")||"1.0.0";if("map"===h.nodeName){var c=_.split("."),y=this._supportVersion;for(a=0;a<y.length;a++){var v=parseInt(c[a])||0;if(y[a]<v){i(7216,_);break}}"orthogonal"===l?this.orientation=J.ORTHO:"isometric"===l?this.orientation=J.ISO:"hexagonal"===l?this.orientation=J.HEX:null!==l&&i(7217,l),this.renderOrder="right-up"===d?it.RightUp:"left-up"===d?it.LeftUp:"left-down"===d?it.LeftDown:it.RightDown,"x"===u?this.setStaggerAxis(tt.STAGGERAXIS_X):"y"===u&&this.setStaggerAxis(tt.STAGGERAXIS_Y),"odd"===f?this.setStaggerIndex(et.STAGGERINDEX_ODD):"even"===f&&this.setStaggerIndex(et.STAGGERINDEX_EVEN),g&&this.setHexSideLength(parseFloat(g));var A=new m(0,0);A.width=parseFloat(h.getAttribute("width")),A.height=parseFloat(h.getAttribute("height")),this.setMapSize(A),(A=new m(0,0)).width=parseFloat(h.getAttribute("tilewidth")),A.height=parseFloat(h.getAttribute("tileheight")),this.setTileSize(A),this.properties=gt(h)}var w=h.getElementsByTagName("tileset");for("map"!==h.nodeName&&(w=[]).push(h),a=0;a<w.length;a++){var S=w[a],x=S.getAttribute("source");if(x){var I=parseInt(S.getAttribute("firstgid")),T=this._tsxContentMap[x];T&&this.parseXMLString(T,I)}else{var b=S.getElementsByTagName("image"),C=b.length>1,D=b[0],N=D.getAttribute("source");N=N.replace(/\\/g,"/");var O=S.getElementsByTagName("tile"),E=O&&O.length||1,G=null,F=S.getAttribute("name")||"",L=parseInt(S.getAttribute("spacing"))||0,P=parseInt(S.getAttribute("margin"))||0,z=o||parseInt(S.getAttribute("firstgid"))||0,R=new m(0,0);R.width=parseFloat(S.getAttribute("tilewidth")),R.height=parseFloat(S.getAttribute("tileheight"));var M=S.getElementsByTagName("tileoffset")[0],U=0,X=0;M&&(U=parseFloat(M.getAttribute("x"))||0,X=parseFloat(M.getAttribute("y"))||0);for(var j=null,k=0;k<E;k++){var B=b[k]?b[k]:D;if(B){var Y=B.getAttribute("source");if(Y=Y.replace(/\\/g,"/"),!j||C){if((j=new nt).name=F,j.firstGid=z&$.FLIPPED_MASK,j.tileOffset.x=U,j.tileOffset.y=X,j.collection=C,!C&&(j.imageName=Y,j.imageSize.width=parseFloat(B.getAttribute("width"))||0,j.imageSize.height=parseFloat(B.getAttribute("height"))||0,j.sourceImage=this._spriteFrameMap[Y],!j.sourceImage)){var V=t.getNameWithPostfix(Y);if(j.imageName=V,j.sourceImage=this._spriteFrameMap[V],!j.sourceImage){var Z=t.getShortName(Y);j.imageName=Z,j.sourceImage=this._spriteFrameMap[Z],j.sourceImage||(r("[error]: "+Z+" not find in ["+Object.keys(this._spriteFrameMap).join(", ")+"]"),n(7221,Y),s("Please try asset type of "+Y+" to 'sprite-frame'"))}}j.spacing=L,j.margin=P,j._tileSize.width=R.width,j._tileSize.height=R.height,this.setTilesets(j)}if(G=O&&O[k]){this.parentGID=z+(parseInt(G.getAttribute("id"))||0);var K=G.getElementsByTagName("image");G.hasAttribute("x")&&G.hasAttribute("y")&&(j.imageOffset=new p(parseFloat(G.getAttribute("x"))||0,parseFloat(G.getAttribute("y"))||0));var W=G.hasAttribute("width")&&G.hasAttribute("height");if(W&&(j._tileSize.width=parseFloat(G.getAttribute("width"))||0,j._tileSize.height=parseFloat(G.getAttribute("height"))||0),K&&K.length>0){var q=K[0],Q=q.getAttribute("source");if(Q=Q.replace(/\\/g,"/"),j.imageName=Q,j.imageSize.width=parseFloat(q.getAttribute("width"))||0,j.imageSize.height=parseFloat(q.getAttribute("height"))||0,W||(j._tileSize.width=j.imageSize.width,j._tileSize.height=j.imageSize.height),j.sourceImage=this._spriteFrameMap[Q],!j.sourceImage){var rt=t.getNameWithPostfix(Q);if(j.imageName=rt,j.sourceImage=this._spriteFrameMap[rt],!j.sourceImage){var st=t.getShortName(Q);j.imageName=st,j.sourceImage=this._spriteFrameMap[st],j.sourceImage||(n(7221,Q),s("Please try asset type of "+Q+" to 'sprite-frame'"))}}j.firstGid=this.parentGID&$.FLIPPED_MASK}var ot=($.FLIPPED_MASK&this.parentGID)>>>0;this._tileProperties.set(ot,gt(G));var at=G.getElementsByTagName("animation");if(at&&at.length>0){var ht=at[0].getElementsByTagName("frame"),lt={frames:[],dt:0,frameIdx:0};this._tileAnimations.set(ot,lt);for(var ut=lt.frames,ft=0;ft<ht.length;ft++){var dt=ht[ft],_t=z+(parseInt(dt.getAttribute("tileid"))||0),pt=parseFloat(dt.getAttribute("duration"))||0;ut.push({tileid:_t,duration:pt/1e3,grid:null})}}}}}}}var ct=h.childNodes;for(a=0;a<ct.length;a++){var mt=ct[a];if(!this._shouldIgnoreNode(mt)){if("imagelayer"===mt.nodeName){var yt=this._parseImageLayer(mt);yt&&this.setImageLayers(yt)}if("layer"===mt.nodeName){var vt=this._parseLayer(mt);this.setLayers(vt)}if("objectgroup"===mt.nodeName){var At=this._parseObjectGroup(mt);this.setObjectGroups(At)}}}return h},e._shouldIgnoreNode=function(t){return 3===t.nodeType||8===t.nodeType||4===t.nodeType},e._parseImageLayer=function(t){var e=t.getElementsByTagName("image");if(!e||0===e.length)return null;var i=new at;i.name=t.getAttribute("name"),i.offset.x=parseFloat(t.getAttribute("offsetx"))||0,i.offset.y=parseFloat(t.getAttribute("offsety"))||0;var r=t.getAttribute("visible");i.visible=!("0"===r);var o=t.getAttribute("opacity");i.opacity=o?Math.round(255*parseFloat(o)):255;var a=t.getAttribute("tintcolor");i.tintColor=a?ft(a):null;var h=e[0],l=h.getAttribute("source");return i.sourceImage=this._imageLayerSPF[l],i.width=parseInt(h.getAttribute("width"))||0,i.height=parseInt(h.getAttribute("height"))||0,i.trans=ft(h.getAttribute("trans")),i.sourceImage?i:(n(7221,l),s("Please try asset type of "+l+" to 'sprite-frame'"),null)},e._parseLayer=function(t){var e=t.getElementsByTagName("data")[0],r=new ot;r.name=t.getAttribute("name");var n=new m(0,0);n.width=parseFloat(t.getAttribute("width")),n.height=parseFloat(t.getAttribute("height")),r.layerSize=n;var s=t.getAttribute("visible");r.visible=!("0"===s);var o=t.getAttribute("opacity");r.opacity=o?Math.round(255*parseFloat(o)):255,r.offset=new p(parseFloat(t.getAttribute("offsetx"))||0,parseFloat(t.getAttribute("offsety"))||0);var a=t.getAttribute("tintcolor");r.tintColor=a?ft(a):null;for(var h="",l=0;l<e.childNodes.length;l++)h+=e.childNodes[l].nodeValue;h=h.trim();var u,f=e.getAttribute("compression"),g=e.getAttribute("encoding");if(f&&"gzip"!==f&&"zlib"!==f)return i(7218),null;switch(f){case"gzip":u=X.unzipBase64AsArray(h,4);break;case"zlib":u=ht(new j.Inflate(X.Base64.decodeAsArray(h,1)).decompress());break;case null:case"":if("base64"===g)u=X.Base64.decodeAsArray(h,4);else if("csv"===g){u=[];for(var d=h.split(","),_=0;_<d.length;_++)u.push(parseInt(d[_]))}else{var c=e.getElementsByTagName("tile");u=[];for(var y=0;y<c.length;y++)u.push(parseInt(c[y].getAttribute("gid")))}break;default:this.layerAttrs===ot.ATTRIB_NONE&&i(7219)}return u&&(r.tiles=new Uint32Array(u)),r.properties=gt(t),r},e._parseObjectGroup=function(t){var e=new st;e.name=t.getAttribute("name")||"",e.offset=new p(parseFloat(t.getAttribute("offsetx")),parseFloat(t.getAttribute("offsety")));var i=t.getAttribute("opacity");e.opacity=i?Math.round(255*parseFloat(i)):255;var r=t.getAttribute("tintcolor");e.tintColor=r?ft(r):null;var n=t.getAttribute("visible");n&&0===parseInt(n)&&(e.visible=!1);var s=t.getAttribute("color");s&&e.color.fromHEX(s);var o=t.getAttribute("draworder");o&&(e.draworder=o),e.setProperties(gt(t));var a=t.getElementsByTagName("object");if(a){for(var h=0;h<a.length;h++){var l=a[h],u={};u.id=l.getAttribute("id")||h,u.name=l.getAttribute("name")||"",u.width=parseFloat(l.getAttribute("width"))||0,u.height=parseFloat(l.getAttribute("height"))||0,u.x=parseFloat(l.getAttribute("x"))||0,u.y=parseFloat(l.getAttribute("y"))||0,u.rotation=parseFloat(l.getAttribute("rotation"))||0,u.properties=gt(l);var f=l.getAttribute("visible");u.visible=!(f&&0===parseInt(f));var g=l.getElementsByTagName("text");if(g&&g.length>0){var d=g[0];u.type=rt.TEXT,u.wrap="1"===d.getAttribute("wrap"),u.color=ft(d.getAttribute("color")),u.halign=lt(d.getAttribute("halign")),u.valign=ut(d.getAttribute("valign")),u.pixelsize=parseInt(d.getAttribute("pixelsize"))||16,u.text=d.childNodes[0].nodeValue}var _=l.getAttribute("gid");_&&(u.gid=parseInt(_),u.type=rt.IMAGE);var c=l.getElementsByTagName("ellipse");c&&c.length>0&&(u.type=rt.ELLIPSE);var m=l.getElementsByTagName("polygon");if(m&&m.length>0){u.type=rt.POLYGON;var y=m[0].getAttribute("points");y&&(u.points=this._parsePointsString(y))}var v=l.getElementsByTagName("polyline");if(v&&v.length>0){u.type=rt.POLYLINE;var A=v[0].getAttribute("points");A&&(u.polylinePoints=this._parsePointsString(A))}u.type||(u.type=rt.RECT),e.objects.push(u)}"index"!==o&&e.objects.sort((function(t,e){return t.y-e.y}))}return e},e._parsePointsString=function(t){if(!t)return null;for(var e=[],i=t.split(" "),r=0;r<i.length;r++){var n=i[r].split(",");e.push({x:parseFloat(n[0]),y:parseFloat(n[1])})}return e},e.setTileAnimations=function(t){this._tileAnimations=t},e.getTileAnimations=function(){return this._tileAnimations},e.getTileProperties=function(){return this._tileProperties},e.setTileProperties=function(t){this._tileProperties=t},e.getCurrentString=function(){return this.currentString},e.setCurrentString=function(t){this.currentString=t},t.getNameWithPostfix=function(t){var e=(t=t.replace(/\\/g,"/")).lastIndexOf("/")+1,i=t.length;return t.substring(e,i)},t.getShortName=function(t){var e=(t=t.replace(/\\/g,"/")).lastIndexOf("/")+1,i=t.lastIndexOf(".");return i=i<0?t.length:i,t.substring(e,i)},o(t,[{key:"mapSize",get:function(){return this._mapSize}},{key:"tileSize",get:function(){return this._tileSize}},{key:"mapWidth",get:function(){return this._mapSize.width},set:function(t){this._mapSize.width=t}},{key:"mapHeight",get:function(){return this._mapSize.height},set:function(t){this._mapSize.height=t}},{key:"tileWidth",get:function(){return this._tileSize.width},set:function(t){this._tileSize.width=t}},{key:"tileHeight",get:function(){return this._tileSize.height},set:function(t){this._tileSize.height=t}}]),t}(),Ct=t("TiledTile",(dt=v("cc.TiledTile"),_t=S(O),pt=A(l),ct=A(l),mt=A(l),yt=A(l),vt=A(l),dt(At=_t((wt=function(t){function e(){var e;return(e=t.call(this)||this)._layer=null,e._x=St&&St(),e._y=xt&&xt(),e}a(e,t);var i=e.prototype;return i.onEnable=function(){var t=this.node.parent;this._layer=t.getComponent("cc.TiledLayer"),this.node.on(B.TRANSFORM_CHANGED,this._updatePosition,this),this.node.on(B.SIZE_CHANGED,this._updatePosition,this),this._resetTile(),this.updateInfo()},i.onDisable=function(){this._resetTile(),this.node.off(B.TRANSFORM_CHANGED,this._updatePosition,this),this.node.off(B.SIZE_CHANGED,this._updatePosition,this)},i._resetTile=function(){this._layer&&this._layer.getTiledTileAt(this._x,this._y)===this&&this._layer.setTiledTileAt(this._x,this._y,null)},i.updateInfo=function(){if(this._layer){var t=this._x,e=this._y;if(this._layer.getTiledTileAt(t,e))s("There is already a TiledTile at [%s, %s]",t,e);else{var i=this._layer.getPositionAt(t,e);this.node.setPosition(i.x,i.y),this._layer.setTiledTileAt(t,e,this),this._layer._markForUpdateRenderData()}}},i._updatePosition=function(){this._layer._markForUpdateRenderData()},o(e,[{key:"x",get:function(){return this._x},set:function(t){t!==this._x&&(this._layer&&this._layer.isInvalidPosition(t,this._y)?s("Invalid x, the valid value is between [%s] ~ [%s]",0,this._layer.layerSize.width):(this._resetTile(),this._x=t,this.updateInfo()))}},{key:"y",get:function(){return this._y},set:function(t){t!==this._y&&(this._layer&&this._layer.isInvalidPosition(this._x,t)?s("Invalid y, the valid value is between [%s] ~ [%s]",0,this._layer.layerSize.height):(this._resetTile(),this._y=t,this.updateInfo()))}},{key:"grid",get:function(){return this._layer?this._layer.getTileGIDAt(this._x,this._y):0},set:function(t){this._layer&&this._layer.setTileGIDAt(t,this._x,this._y)}}]),e}(D),St=w(wt.prototype,"_x",[pt],(function(){return 0})),xt=w(wt.prototype,"_y",[ct],(function(){return 0})),h(wt.prototype,"x",[mt],Object.getOwnPropertyDescriptor(wt.prototype,"x"),wt.prototype),h(wt.prototype,"y",[yt],Object.getOwnPropertyDescriptor(wt.prototype,"y"),wt.prototype),h(wt.prototype,"grid",[vt],Object.getOwnPropertyDescriptor(wt.prototype,"grid"),wt.prototype),At=wt))||At)||At)),Dt=!1;function Nt(t){Dt=t}function Ot(t,e,i){var r=i||t.sourceImage,n=r.texture,s=t.collection;if(!t.imageSize.width||!t.imageSize.height){var o=t.sourceImage;t.imageSize.width=o.width,t.imageSize.height=o.height}var a=t.imageSize.width,h=t.imageSize.height,l=t._tileSize.width,u=t._tileSize.height,f=r.width,g=r.height,d=t.spacing,_=t.margin,p=1;if(!s){var c=Math.floor((a-2*_+d)/(l+d)),m=Math.floor((h-2*_+d)/(u+d));p=Math.max(1,m*c)}for(var v=t.firstGid,A=null,w=!!e.get(v),S=t.firstGid+p,x=v;x<S&&(w&&!e.get(x)&&(w=!1),w||!e.get(x));++x){if(A={tileset:t,x:0,y:0,width:l,height:u,t:0,l:0,r:0,b:0,cx:0,cy:0,offsetX:0,offsetY:0,rotated:!1,gid:x,spriteFrame:r,texture:n},t.rectForGID(x,A),!i||p>1||t.imageOffset)if(i){A._name=i.name;var I=i.unbiasUV[0],T=i.rotated?i.unbiasUV[1]:i.unbiasUV[5];Dt?(A.l=I+(A.x+.5)/f,A.t=T+(A.y+.5)/g,A.r=I+(A.x+A.width-.5)/f,A.b=T+(A.y+A.height-.5)/g):(A.l=I+A.x/f,A.t=T+A.y/g,A.r=I+(A.x+A.width)/f,A.b=T+(A.y+A.height)/g),A._rect=new y(A.x,A.y,A.width,A.height)}else A.l=A.x/f,A.t=A.y/g,A.r=(A.x+A.width)/f,A.b=(A.y+A.height)/g,A._rect=new y(A.x,A.y,A.width,A.height);else i.rotated?(A._rotated=!0,A._name=i.name,A._rect=i.getRect(),A.l=i.unbiasUV[0],A.t=i.unbiasUV[1],A.r=i.unbiasUV[4],A.b=i.unbiasUV[3]):(A._name=i.name,A._rect=i.getRect(),A.l=i.unbiasUV[0],A.t=i.unbiasUV[5],A.r=i.unbiasUV[2],A.b=i.unbiasUV[1]);A.cx=(A.l+A.r)/2,A.cy=(A.t+A.b)/2,e.set(x,A)}}var Et=new x,Gt=new p,Ft=new I,Lt=new I,Pt={row:0,col:0};function zt(t){return"subNodes"in t}for(var Rt,Mt,Ut,Xt,jt,Ht,kt,Bt,Yt,Vt,Zt,Kt,Wt,qt,Jt,Qt,$t,te,ee,ie,re,ne,se,oe,ae,he,le,ue,fe,ge,de,_e,pe=t("TiledUserNodeData",v("cc.TiledUserNodeData")(It=function(t){function e(){var e;return(e=t.call(this)||this)._index=-1,e._row=-1,e._col=-1,e._tiledLayer=null,e}return a(e,t),e}(D))||It),ce=t("TiledLayer",v("cc.TiledLayer")(Tt=function(t){a(r,t);var e=r.prototype;function r(){var e;return(e=t.call(this)||this)._userNodeGrid={},e._userNodeMap={},e._userNodeDirty=!1,e.tiledTiles=[],e._viewPort={x:-1,y:-1,width:-1,height:-1},e._cullingRect={leftDown:{row:-1,col:-1},rightTop:{row:-1,col:-1}},e._cullingDirty=!0,e._rightTop={row:-1,col:-1},e._layerInfo=null,e._mapInfo=null,e._topOffset=0,e._downOffset=0,e._leftOffset=0,e._rightOffset=0,e.tiles=[],e.vertices=[],e._verticesDirty=!0,e._layerName="",e._layerSize=void 0,e._minGID=void 0,e._maxGID=void 0,e._layerOrientation=null,e._opacity=void 0,e._tintColor=void 0,e.texGrids=null,e._textures=[],e._tilesets=[],e._leftDownToCenterX=0,e._leftDownToCenterY=0,e._hasTiledNodeGrid=!1,e._hasAniGrid=!1,e._animations=null,e._enableCulling=void 0,e.colorChanged=!1,e._properties=void 0,e.renderOrder=void 0,e._staggerAxis=void 0,e._staggerIndex=void 0,e._hexSideLength=void 0,e._mapTileSize=void 0,e._odd_even=void 0,e._diffX1=void 0,e._diffY1=void 0,e._useAutomaticVertexZ=void 0,e._vertexZvalue=void 0,e._offset=void 0,e._tiledDataArray=[],e._cameraNode=void 0,e._drawInfoList=[],e._tiledDataArrayIdx=0,e}return e.requestDrawInfo=function(t){return this._drawInfoList[t]||(this._drawInfoList[t]=new E,this._drawInfoList[t].setDrawInfoType(G.MIDDLEWARE)),this._drawInfoList[t]},e.hasTiledNode=function(){return this._hasTiledNodeGrid},e.hasAnimation=function(){return this._hasAniGrid},e.addUserNode=function(t){var e=t.getComponent(pe);return e?(u(7242),!1):(e=t.addComponent(pe),t.parent=this.node,this._userNodeMap[t.uuid]=e,e._row=-1,e._col=-1,e._tiledLayer=this,this._nodeLocalPosToLayerPos(t.getPosition(),Gt),this._positionToRowCol(Gt.x,Gt.y,Pt),this._addUserNodeToGrid(e,Pt),this._updateCullingOffsetByUserNode(t),t.on(B.TRANSFORM_CHANGED,this._userNodePosChange,e),t.on(B.SIZE_CHANGED,this._userNodeSizeChange,e),!0)},e.removeUserNode=function(t){var e=t.getComponent(pe);return e?(t.off(B.TRANSFORM_CHANGED,this._userNodePosChange,e),t.off(B.SIZE_CHANGED,this._userNodeSizeChange,e),this._removeUserNodeFromGrid(e),delete this._userNodeMap[t.uuid],t._removeComponent(e),e.destroy(),t.removeFromParent(),!0):(u(7243),!1)},e.destroyUserNode=function(t){this.removeUserNode(t),t.destroy()},e._nodeLocalPosToLayerPos=function(t,e){e.x=t.x+this._leftDownToCenterX,e.y=t.y+this._leftDownToCenterY},e.getNodesByRowCol=function(t,e){var i=this._userNodeGrid[t];return i?i[e]:null},e.getNodesCountByRow=function(t){var e=this._userNodeGrid[t];return e?e.count:0},e._updateAllUserNode=function(){for(var t in this._userNodeGrid={},this._userNodeMap){var e=this._userNodeMap[t];this._nodeLocalPosToLayerPos(e.node.getPosition(),Gt),this._positionToRowCol(Gt.x,Gt.y,Pt),this._addUserNodeToGrid(e,Pt),this._updateCullingOffsetByUserNode(e.node)}},e._updateCullingOffsetByUserNode=function(t){var e=t._getUITransformComp().contentSize;this._topOffset<e.height&&(this._topOffset=e.height),this._downOffset<e.height&&(this._downOffset=e.height),this._leftOffset<e.width&&(this._leftOffset=e.width),this._rightOffset<e.width&&(this._rightOffset=e.width)},e._userNodeSizeChange=function(){var t=this.node,e=this._tiledLayer;e._updateCullingOffsetByUserNode(t),e._userNodeDirty=!0,e._markForUpdateRenderData()},e._userNodePosChange=function(){var t=this,e=t.node,i=t._tiledLayer;i._nodeLocalPosToLayerPos(e.getPosition(),Gt),i._positionToRowCol(Gt.x,Gt.y,Pt),i._limitInLayer(Pt),Pt.row===t._row&&Pt.col===t._col||(i._removeUserNodeFromGrid(t),i._addUserNodeToGrid(t,Pt))},e._removeUserNodeFromGrid=function(t){var e=t._row,i=t._col,r=t._index,n=this._userNodeGrid[e],s=n&&n[i];s&&(n.count--,s.count--,s.list[r]=null,s.count<=0&&(s.list.length=0,s.count=0)),t._row=-1,t._col=-1,t._index=-1,this._userNodeDirty=!0,this._markForUpdateRenderData()},e._limitInLayer=function(t){var e=t.row,i=t.col;e<0&&(t.row=0),e>this._rightTop.row&&(t.row=this._rightTop.row),i<0&&(t.col=0),i>this._rightTop.col&&(t.col=this._rightTop.col)},e._addUserNodeToGrid=function(t,e){var i=e.row,r=e.col,n=this._userNodeGrid[i]=this._userNodeGrid[i]||{count:0},s=n[r]=n[r]||{count:0,list:[]};t._row=i,t._col=r,t._index=s.list.length,n.count++,s.count++,s.list.push(t),this._userNodeDirty=!0,this._markForUpdateRenderData()},e.isUserNodeDirty=function(){return this._userNodeDirty},e.setUserNodeDirty=function(t){this._userNodeDirty=t},e._reinstallCamera=function(){var t=Y.root.batcher2D.getFirstRenderCamera(this.node),e=null==t?void 0:t.node;return this._cameraNode!==e&&(this._uninstallCamera(),e&&(e.on(B.TRANSFORM_CHANGED,this.updateCulling,this),e.on(B.SIZE_CHANGED,this.updateCulling,this),this._cameraNode=e)),t},e._uninstallCamera=function(){this._cameraNode&&(this._cameraNode.off(B.TRANSFORM_CHANGED,this.updateCulling,this),this._cameraNode.off(B.SIZE_CHANGED,this.updateCulling,this),delete this._cameraNode)},e.onEnable=function(){t.prototype.onEnable.call(this),this.node.on(B.ANCHOR_CHANGED,this._syncAnchorPoint,this),this.node.on(B.TRANSFORM_CHANGED,this.updateCulling,this),this.node.on(B.SIZE_CHANGED,this.updateCulling,this),this.node.parent.on(B.TRANSFORM_CHANGED,this.updateCulling,this),this.node.parent.on(B.SIZE_CHANGED,this.updateCulling,this),this._markForUpdateRenderData(),this.scheduleOnce(this.updateCulling.bind(this))},e.onDisable=function(){var e,i;t.prototype.onDisable.call(this),null==(e=this.node.parent)||e.off(B.SIZE_CHANGED,this.updateCulling,this),null==(i=this.node.parent)||i.off(B.TRANSFORM_CHANGED,this.updateCulling,this),this.node.off(B.SIZE_CHANGED,this.updateCulling,this),this.node.off(B.TRANSFORM_CHANGED,this.updateCulling,this),this.node.off(B.ANCHOR_CHANGED,this._syncAnchorPoint,this),this._uninstallCamera()},e._syncAnchorPoint=function(){var t=this.node,e=t._getUITransformComp(),i=t.getScale();this._leftDownToCenterX=e.width*e.anchorX*i.x,this._leftDownToCenterY=e.height*e.anchorY*i.y,this._cullingDirty=!0,this._markForUpdateRenderData()},e.getLayerName=function(){return this._layerName},e.setLayerName=function(t){this._layerName=t},e.getProperty=function(t){return this._properties[t]},e.getPositionAt=function(t,e){var i;switch(void 0!==e?(i=Math.floor(t),e=Math.floor(e)):(i=Math.floor(t.x),e=Math.floor(t.y)),this._layerOrientation){case J.ORTHO:return this._positionForOrthoAt(i,e);case J.ISO:return this._positionForIsoAt(i,e);case J.HEX:return this._positionForHexAt(i,e)}return null},e.isInvalidPosition=function(t,e){return t>=this._layerSize.width||e>=this._layerSize.height||t<0||e<0},e._positionForIsoAt=function(t,e){var i=0,r=0,n=Math.floor(t)+Math.floor(e)*this._layerSize.width,s=this.tiles[n];if(s){var o=(s&$.FLIPPED_MASK)>>>0,a=this.texGrids.get(o).tileset.tileOffset;i=a.x,r=a.y}return new p(.5*this._mapTileSize.width*(this._layerSize.height+t-e-1)+i,.5*this._mapTileSize.height*(this._layerSize.width-t+this._layerSize.height-e-2)-r)},e._positionForOrthoAt=function(t,e){var i=0,r=0,n=Math.floor(t)+Math.floor(e)*this._layerSize.width,s=this.tiles[n];if(s){var o=(s&$.FLIPPED_MASK)>>>0,a=this.texGrids.get(o).tileset.tileOffset;i=a.x,r=a.y}return new p(t*this._mapTileSize.width+i,(this._layerSize.height-e-1)*this._mapTileSize.height-r)},e._positionForHexAt=function(t,e){var i,r=this._mapTileSize.width,n=this._mapTileSize.height,s=this._layerSize.height,o=Math.floor(t)+Math.floor(e)*this._layerSize.width,a=(this.tiles[o]&$.FLIPPED_MASK)>>>0;i=this.texGrids.get(a)?this.texGrids.get(a).tileset.tileOffset:{x:0,y:0};var h=this._staggerIndex===et.STAGGERINDEX_ODD?1:-1,l=0,u=0,f=0,g=0;switch(this._staggerAxis){case tt.STAGGERAXIS_Y:f=0,e%2==1&&(f=r/2*h),l=t*r+f+i.x,u=(s-e-1)*(n-(n-this._hexSideLength)/2)-i.y;break;case tt.STAGGERAXIS_X:g=0,t%2==1&&(g=n/2*-h),l=t*(r-(r-this._hexSideLength)/2)+i.x,u=(s-e-1)*n+g-i.y}return new p(l,u)},e.setTilesGIDAt=function(t,e,i,r){if(t&&0!==t.length&&!(r<=0)){i<0&&(i=0),e<0&&(e=0);for(var n=0,s=e+r,o=i;;o++)for(var a=e;a<s;a++){if(n>=t.length)return;this._updateTileForGID(t[n],a,o),n++}}},e.setTileGIDAt=function(t,e,r,n){var s=(t&$.FLIPPED_MASK)>>>0;if(e=Math.floor(e),r=Math.floor(r),this.isInvalidPosition(e,r))throw new Error("cc.TiledLayer.setTileGIDAt(): invalid position");this.tiles&&this._tilesets&&0!==this._tilesets.length?0!==s&&s<this._tilesets[0].firstGid?i(7239,t):(n=n||0,this._updateTileForGID((s|n)>>>0,e,r)):i(7238)},e._updateTileForGID=function(t,e,i){var r=0|e+i*this._layerSize.width;if(!(r>=this.tiles.length)&&t!==this.tiles[r]){var n=(t&$.FLIPPED_MASK)>>>0;this.texGrids.get(n)?(this.tiles[r]=t,this._updateVertex(e,i)):this.tiles[r]=0,this._cullingDirty=!0}},e.getTileGIDAt=function(t,e){if(this.isInvalidPosition(t,e))throw new Error("cc.TiledLayer.getTileGIDAt(): invalid position");if(!this.tiles)return i(7237),null;var r=Math.floor(t)+Math.floor(e)*this._layerSize.width;return(this.tiles[r]&$.FLIPPED_MASK)>>>0},e.getTileFlagsAt=function(t,e){if(this.isInvalidPosition(t,e))throw new Error("TiledLayer.getTileFlagsAt: invalid position");if(!this.tiles)return i(7240),null;var r=Math.floor(t)+Math.floor(e)*this._layerSize.width;return(this.tiles[r]&$.FLIPPED_ALL)>>>0},e.setCullingDirty=function(t){this._cullingDirty=t},e.isCullingDirty=function(){return this._cullingDirty},e.updateViewPort=function(t,e,i,r){if(this._viewPort.width!==i||this._viewPort.height!==r||this._viewPort.x!==t||this._viewPort.y!==e){this._viewPort.x=t,this._viewPort.y=e,this._viewPort.width=i,this._viewPort.height=r;var n=1;this._layerOrientation===J.ISO&&(n=2);var s=this._viewPort.x-this._offset.x+this._leftDownToCenterX,o=this._viewPort.y-this._offset.y+this._leftDownToCenterY,a=s-this._leftOffset,h=o-this._downOffset,l=s+i+this._rightOffset,u=o+r+this._topOffset,f=this._cullingRect.leftDown,g=this._cullingRect.rightTop;a<0&&(a=0),h<0&&(h=0),this._positionToRowCol(a,h,Pt),Pt.row-=n,Pt.col-=n,Pt.row=Pt.row>0?Pt.row:0,Pt.col=Pt.col>0?Pt.col:0,Pt.row===f.row&&Pt.col===f.col||(f.row=Pt.row,f.col=Pt.col,this._cullingDirty=!0),l<0||u<0?(Pt.row=-1,Pt.col=-1):(this._positionToRowCol(l,u,Pt),Pt.row++,Pt.col++),Pt.row===g.row&&Pt.col===g.col||(g.row=Pt.row,g.col=Pt.col,this._cullingDirty=!0),this._cullingDirty&&this._markForUpdateRenderData()}},e._positionToRowCol=function(t,e,i){var r=this._mapTileSize.width,n=this._mapTileSize.height,s=.5*r,o=.5*n,a=0,h=0,l=0,u=0,f=this._staggerAxis;switch(this._layerOrientation){case J.ORTHO:h=Math.floor(t/r),a=Math.floor(e/n);break;case J.ISO:h=Math.floor(t/s),a=Math.floor(e/o);break;case J.HEX:f===tt.STAGGERAXIS_Y?(l=(a=Math.floor(e/(n-this._diffY1)))%2==1?s*this._odd_even:0,h=Math.floor((t-l)/r)):(u=(h=Math.floor(t/(r-this._diffX1)))%2==1?o*-this._odd_even:0,a=Math.floor((e-u)/n))}return i.row=a,i.col=h,i},e.updateCulling=function(){if(this._enableCulling){this.node.updateWorldTransform(),x.invert(Et,this.node.getWorldMatrix());var t=this._reinstallCamera();t&&(Ft.x=0,Ft.y=0,Ft.z=0,Lt.x=t.width,Lt.y=t.height,Lt.z=0,t.screenToWorld(Ft,Ft),t.screenToWorld(Lt,Lt),I.transformMat4(Ft,Ft,Et),I.transformMat4(Lt,Lt,Et),this.updateViewPort(Ft.x,Ft.y,Lt.x-Ft.x,Lt.y-Ft.y))}},e.getLayerOrientation=function(){return this._layerOrientation},e.getProperties=function(){return this._properties},e._updateVertex=function(t,e){var i=$.FLIPPED_MASK,r=this.vertices,n=this._layerOrientation,s=this.tiles;if(s){var o,a,h,l,u=this._rightTop,f=this._mapTileSize.width,g=this._mapTileSize.height,d=.5*f,_=.5*g,p=this._layerSize.height,c=this._layerSize.width,m=this.texGrids,y=0,v=0;n===J.HEX&&(o=this._staggerAxis,a=this._diffX1,h=this._diffY1,l=this._odd_even);var A,w,S,x=0,I=0,T=0,b=0,C=e*c+t;A=(s[C]&i)>>>0;var D=m.get(A);if(D){switch(this._animations.get(A)&&(this._hasAniGrid=this._hasAniGrid||!0),n){case J.ORTHO:y=(x=t)*f,v=(I=p-e-1)*g;break;case J.ISO:y=d*(x=p+t-e-1),v=_*(I=p+c-t-e-2);break;case J.HEX:y=t*(f-a)+(o===tt.STAGGERAXIS_Y&&e%2==1?d*l:0),v=(p-e-1)*(g-h)+(o===tt.STAGGERAXIS_X&&t%2==1?_*-l:0),x=t,I=p-e-1}var N=r[I]=r[I]||{minCol:0,maxCol:0},O=N[x]=N[x]||{left:0,bottom:0,index:0};N.minCol>x&&(N.minCol=x),N.maxCol<x&&(N.maxCol=x),u.row<I&&(u.row=I,n===J.ISO&&(u.row+=1)),u.col<x&&(u.col=x,n===J.ISO&&(u.col+=1));var E=D.tileset.tileOffset;y+=this._offset.x+E.x+D.offsetX,v+=this._offset.y-E.y-D.offsetY,T=(T=-E.y+D.tileset._tileSize.height-g)<0?0:T,w=E.y<0?0:E.y,S=-E.x<0?0:-E.x,b=(b=E.x+D.tileset._tileSize.width-f)<0?0:b,this._rightOffset<S&&(this._rightOffset=S),this._leftOffset<b&&(this._leftOffset=b),this._topOffset<w&&(this._topOffset=w),this._downOffset<T&&(this._downOffset=T),O.left=y,O.bottom=v,O.index=C,this._cullingDirty=!0}}},e._updateVertices=function(){if(this.vertices.length=0,this.tiles){var t=this._rightTop;t.row=-1,t.col=-1;var e=this._layerSize.height,i=this._layerSize.width;this._topOffset=0,this._downOffset=0,this._leftOffset=0,this._rightOffset=0,this._hasAniGrid=!1;for(var r=0;r<e;++r)for(var n=0;n<i;++n)this._updateVertex(n,r);this._verticesDirty=!1}},e.getTiledTileAt=function(t,e,r){if(this.isInvalidPosition(t,e))throw new Error("TiledLayer.getTiledTileAt: invalid position");if(!this.tiles)return i(7236),null;var n=Math.floor(t)+Math.floor(e)*this._layerSize.width,s=this.tiledTiles[n];if(!s&&r){var o=new k;return(s=o.addComponent(Ct))._x=t,s._y=e,s._layer=this,s.updateInfo(),o.parent=this.node,s}return s},e.setTiledTileAt=function(t,e,r){if(this.isInvalidPosition(t,e))throw new Error("TiledLayer.setTiledTileAt: invalid position");if(!this.tiles)return i(7236),null;var n=Math.floor(t)+Math.floor(e)*this._layerSize.width;return this.tiledTiles[n]=r,this._cullingDirty=!0,this._hasTiledNodeGrid=!!r||this.tiledTiles.some((function(t){return!!t})),r},e.getTexture=function(t){return t=t||0,this._textures&&t>=0&&this._textures.length>t?this._textures[t]:null},e.getTextures=function(){return this._textures},e.setTexture=function(t){this.setTextures([t])},e.setTextures=function(t){this._textures=t,this._markForUpdateRenderData()},e.getLayerSize=function(){return this._layerSize},e.getMapTileSize=function(){return this._mapTileSize},e.getTileSet=function(t){return t=t||0,this._tilesets&&t>=0&&this._tilesets.length>t?this._tilesets[t]:null},e.getTileSets=function(){return this._tilesets},e.setTileSet=function(t){this.setTileSets([t])},e.setTileSets=function(t){this._tilesets=t;var e=this._textures=[],i=this.texGrids;i.clear();for(var r=0;r<t.length;r++){var n=t[r];n&&(e[r]=n.sourceImage)}for(var s=0,o=t.length;s<o;++s){var a=t[s];a&&Ot(a,i,a.sourceImage)}this._prepareToRender()},e.init=function(t,e,i,r,n){var s=this;s._cullingDirty=!0,s._layerInfo=t,s._mapInfo=e;var o=t.layerSize;s._layerName=t.name,s.tiles=t.tiles,s._properties=t.properties,s._layerSize=o,s._minGID=t.minGID,s._maxGID=t.maxGID,s._opacity=t.opacity,t.tintColor&&(s._tintColor=t.tintColor),s.renderOrder=e.renderOrder,s._staggerAxis=e.getStaggerAxis(),s._staggerIndex=e.getStaggerIndex(),s._hexSideLength=e.getHexSideLength(),s._animations=e.getTileAnimations(),s._tilesets=i,s._textures=r,s.texGrids=n,s._layerOrientation=e.orientation,s._mapTileSize=e.getTileSize();var a=s._mapTileSize.width,h=s._mapTileSize.height,l=s._layerSize.width,u=s._layerSize.height,f=s.node._getUITransformComp();if(s._layerOrientation===J.HEX){var g=0,d=0,_=-2&a,c=-2&h;s._odd_even=s._staggerIndex===et.STAGGERINDEX_ODD?1:-1,s._staggerAxis===tt.STAGGERAXIS_X?(s._diffX1=(_-s._hexSideLength)/2,s._diffY1=0,g=(s._diffX1+s._hexSideLength)*l+s._diffX1,d=c*u+c/2):(s._diffX1=0,s._diffY1=(c-s._hexSideLength)/2,g=_*l+_/2,d=(s._diffY1+s._hexSideLength)*u+s._diffY1),f.setContentSize(g,d)}else if(s._layerOrientation===J.ISO){var m=l+u;f.setContentSize(.5*a*m,.5*h*m)}else f.setContentSize(l*a,u*h);s._offset=new p(t.offset.x,-t.offset.y),s._useAutomaticVertexZ=!1,s._vertexZvalue=0,s._syncAnchorPoint(),s._prepareToRender()},e._prepareToRender=function(){this._updateVertices(),this._updateAllUserNode()},e.requestTiledRenderData=function(){for(var t=this._tiledDataArray;t.length>0&&t[t.length-1].subNodes&&0===t[t.length-1].subNodes.length;)t.pop();if(t.length>0){var e=t[t.length-1];if(e.renderData&&0===e.renderData.vertexCount)return e}var i={renderData:null,texture:null};return this._tiledDataArray.push(i),i},e.requestSubNodesData=function(){var t=this._tiledDataArray;if(t.length>0){var e=t[t.length-1];if(zt(e)&&0===e.subNodes.length)return e}var i={subNodes:[]};return this._tiledDataArray.push(i),i},e.destroyRenderData=function(){this._tiledDataArray.forEach((function(t){var e=t.renderData;e&&F.remove(e)})),this._tiledDataArray.length=0,t.prototype.destroyRenderData.call(this)},e._flushAssembler=function(){var t=r.Assembler.getAssembler(this);this._assembler!==t&&(this._assembler=t,this._assembler.createData(this)),0===this._tiledDataArray.length&&(this._markForUpdateRenderData(),this._updateColor())},e._render=function(t){for(var e=0;e<this._tiledDataArray.length;e++){this._tiledDataArrayIdx=e;var i=this._tiledDataArray[e];if(i.subNodes)i.subNodes.forEach((function(e){e&&t.walk(e.node)}));else{var r=i;r.texture&&t.commitComp(this,r.renderData,r.texture,this._assembler,null)}}this.node._static=!0},e.createRenderEntity=function(){return new L(P.CROSSED)},e.fillIndicesBuffer=function(t,e){var i=t.chunk.meshBuffer.iData,r=t.chunk.meshBuffer.indexOffset;e.setIndexOffset(r);for(var n=t.chunk.vertexOffset,s=t.vertexCount/4,o=0;o<s;o+=1)i[r]=n,i[r+1]=n+1,i[r+2]=n+2,i[r+3]=n+2,i[r+4]=n+1,i[r+5]=n+3,r+=6,n+=4;t.chunk.meshBuffer.indexOffset=r,e.setIBCount(6*s)},e.prepareDrawData=function(){var t=this;this._drawInfoList.length=0;var e=this.renderEntity;e.clearDynamicRenderDrawInfos();var i=this._tiledDataArray,r=0;i.forEach((function(i){if(zt(i))i.subNodes.forEach((function(i){if(i){t._drawInfoList[r]||(t._drawInfoList[r]=new E);var n=t._drawInfoList[r];n.setDrawInfoType(G.SUB_NODE),n.setSubNode(i.node),e.setDynamicRenderDrawInfo(n,r),r++}}));else{var n=i;if(n.texture){t._drawInfoList[r]||(t._drawInfoList[r]=new E);var s=t._drawInfoList[r];n.renderData.fillDrawInfoAttributes(s),s.setTexture(n.texture.getGFXTexture()),s.setSampler(n.texture.getGFXSampler()),s.setMaterial(t.getRenderMaterial(0)),t.fillIndicesBuffer(n.renderData,s),e.setDynamicRenderDrawInfo(s,r),r++}}}))},o(r,[{key:"cullingRect",get:function(){return this._cullingRect}},{key:"rightTop",get:function(){return this._rightTop}},{key:"layerSize",get:function(){return this._layerSize}},{key:"tiledDataArray",get:function(){return this._tiledDataArray}},{key:"leftDownToCenterX",get:function(){return this._leftDownToCenterX}},{key:"leftDownToCenterY",get:function(){return this._leftDownToCenterY}},{key:"enableCulling",get:function(){return this._enableCulling},set:function(t){this._enableCulling!==t&&(this._enableCulling=t,this._cullingDirty=!0,this._markForUpdateRenderData())}}]),r}(z))||Tt),me=t("TiledObjectGroup",(Rt=v("cc.TiledObjectGroup"),Mt=S(O),Ut=A(g),Rt(Xt=Mt((jt=function(t){function e(){var e;return(e=t.call(this)||this)._premultiplyAlpha=!1,e._groupName=void 0,e._positionOffset=void 0,e._mapInfo=void 0,e._properties=void 0,e._offset=void 0,e._opacity=void 0,e._tintColor=null,e._animations=void 0,e._hasAniObj=void 0,e._texGrids=void 0,e.aniObjects=void 0,e._objects=[],e}a(e,t);var i=e.prototype;return i.getPositionOffset=function(){return this._positionOffset},i.getProperties=function(){return this._properties},i.getGroupName=function(){return this._groupName},i.getProperty=function(t){return this._properties[t.toString()]},i.getObject=function(t){for(var e=0,i=this._objects.length;e<i;e++){var r=this._objects[e];if(r&&r.name===t)return r}return null},i.getObjects=function(){return this._objects},i._init=function(t,e,i){var r=$.FLIPPED_MASK,n=$.HORIZONTAL,s=$.VERTICAL;this._groupName=t.name,this._positionOffset=t.offset,this._mapInfo=e,this._properties=t.getProperties(),this._offset=new p(t.offset.x,-t.offset.y),this._opacity=t.opacity,t.tintColor&&(this._tintColor=t.tintColor),this._texGrids=i,this._animations=e.getTileAnimations(),this.aniObjects=[],this._hasAniObj=!1;var o=e.mapSize,a=e.tileSize,h=0,l=0,u=new c,g=J.ISO===e.orientation;if(e.orientation===J.HEX)e.getStaggerAxis()===tt.STAGGERAXIS_X?(l=a.height*(o.height+.5),h=(a.width+e.getHexSideLength())*Math.floor(o.width/2)+a.width*(o.width%2)):(h=a.width*(o.width+.5),l=(a.height+e.getHexSideLength())*Math.floor(o.height/2)+a.height*(o.height%2));else if(g){var d=o.width+o.height;h=.5*a.width*d,l=.5*a.height*d}else h=o.width*a.width,l=o.height*a.height;var _=this.node._getUITransformComp();_.setContentSize(h,l);for(var m=h*_.anchorX,y=l*(1-_.anchorY),v=t.objects,A={},w=0,S=v.length;w<S;w++){var x=v[w],I=x.type;x.offset=new p(x.x,x.y);var T=x.points||x.polylinePoints;if(T)for(var b=0;b<T.length;b++)T[b].y*=-1;if(g){var C=x.x/a.height,D=x.y/a.height;x.x=.5*a.width*(o.height+C-D),x.y=.5*a.height*(o.width+o.height-C-D)}else x.y=l-x.y;if(I===rt.TEXT){var N="text"+x.id;A[N]=!0;var O=this.node.getChildByName(N);O||(O=new k),O.setRotationFromEuler(0,0,-x.rotation),O.setPosition(x.x-m,x.y-y),O.name=N,O.parent=this.node,O.setSiblingIndex(w),O.layer=this.node.layer;var E=O.getComponent(U);E||(E=O.addComponent(U));var G=O._getUITransformComp();O.active=x.visible,G.anchorX=0,G.anchorY=1,this._tintColor?(u.set(this._tintColor),u.a*=this._opacity/255,E.color.set(u)):E.color.a*=this._opacity/255,E.overflow=U.Overflow.SHRINK,E.lineHeight=x.height,E.string=x.text,E.horizontalAlign=x.halign,E.verticalAlign=x.valign,E.fontSize=x.pixelsize,G.setContentSize(x.width,x.height)}else if(I===rt.IMAGE){var F=x.gid,L=(F&r)>>>0,P=i.get(L);if(!P)continue;var z=P.tileset,R="img"+x.id;A[R]=!0;var M=this.node.getChildByName(R);x.width=x.width||P.width,x.height=x.height||P.height,M&&M._objFlags&f.HideInHierarchy&&(M.removeFromParent(),M.hideFlags|=f.DontSave,M.destroy(),M=null),M||(M=new k),this._animations.get(L)&&(this.aniObjects.push({object:x,imgNode:M,gridGID:L}),this._hasAniObj=!0);var X=z.tileOffset.x,j=z.tileOffset.y;M.active=x.visible,M.setRotationFromEuler(0,0,-x.rotation),M.setPosition(x.x-m,x.y-y),M.name=R,M.parent=this.node,M.setSiblingIndex(w),M.layer=this.node.layer;var H=M.getComponent(Z);H||(H=M.addComponent(Z));var B=M._getUITransformComp();g?(B.anchorX=.5+X/x.width,B.anchorY=j/x.height):(B.anchorX=X/x.width,B.anchorY=j/x.height),this._tintColor?(u.set(this._tintColor),u.a*=this._opacity/255,H.color.set(u)):H.color.a*=this._opacity/255,H.sizeMode=Z.SizeMode.CUSTOM;var Y=this._premultiplyAlpha?K.ONE:K.SRC_ALPHA;H.srcBlendFactor!==Y&&(H.srcBlendFactor=Y,H.material&&H._updateBlendFunc());var V=P.spriteFrame;V=V?V.clone():new W,(F&n)>>>0&&(V.flipUVX=!V.flipUVX),(F&s)>>>0&&(V.flipUVY=!V.flipUVY),V.rotated=P._rotated,V.rect=P._rect,H.spriteFrame=V,B.setContentSize(x.width,x.height),H._markForUpdateRenderData()}}this._objects=v;for(var q=this.node.children,Q=/^(?:img|text)\d+$/,et=0,it=q.length;et<it;et++){var nt=q[et],st=nt.name;Q.test(st)&&!A[st]&&nt.destroy()}},i.update=function(){if(this._hasAniObj)for(var t=this.aniObjects,e=this._texGrids,i=J.ISO===this._mapInfo.orientation,r=0,n=t.length;r<n;r++){var s=t[r],o=s.gridGID,a=e.get(o);if(a){var h=a.tileset,l=s.object,u=s.imgNode,f=h.tileOffset.x,g=h.tileOffset.y,d=u._getUITransformComp();i?(d.anchorX=.5+f/l.width,d.anchorY=g/l.height):(d.anchorX=f/l.width,d.anchorY=g/l.height);var _=u.getComponent(Z),p=_.spriteFrame;p.rotated=a._rotated,p.rect=a._rect,_.spriteFrame=p,_._markForUpdateRenderData()}}},o(e,[{key:"premultiplyAlpha",get:function(){return this._premultiplyAlpha},set:function(t){this._premultiplyAlpha=t}},{key:"offset",get:function(){return this._offset}}]),e}(D),h(jt.prototype,"premultiplyAlpha",[Ut],Object.getOwnPropertyDescriptor(jt.prototype,"premultiplyAlpha"),jt.prototype),Xt=jt))||Xt)||Xt)),ye=t("TiledMapAsset",(Ht=v("cc.TiledMapAsset"),kt=A([q]),Bt=A([d]),Yt=A([W]),Vt=A([W]),Zt=A([d]),Kt=A([d]),Wt=A([m]),Ht((Jt=function(t){function e(){var e;return(e=t.call(this)||this).tmxXmlStr=Qt&&Qt(),e.tsxFiles=$t&&$t(),e.tsxFileNames=te&&te(),e.spriteFrames=ee&&ee(),e.imageLayerSpriteFrame=ie&&ie(),e.imageLayerSpriteFrameNames=re&&re(),e.spriteFrameNames=ne&&ne(),e.spriteFrameSizes=se&&se(),e}return a(e,t),e}(N),Qt=w(Jt.prototype,"tmxXmlStr",[T],(function(){return""})),$t=w(Jt.prototype,"tsxFiles",[T,kt],(function(){return[]})),te=w(Jt.prototype,"tsxFileNames",[T,Bt],(function(){return[]})),ee=w(Jt.prototype,"spriteFrames",[T,Yt],(function(){return[]})),ie=w(Jt.prototype,"imageLayerSpriteFrame",[T,Vt],(function(){return[]})),re=w(Jt.prototype,"imageLayerSpriteFrameNames",[T,Zt],(function(){return[]})),ne=w(Jt.prototype,"spriteFrameNames",[T,Kt],(function(){return[]})),se=w(Jt.prototype,"spriteFrameSizes",[T,Wt],(function(){return[]})),qt=Jt))||qt)),ve=(t("TiledMap",(oe=v("cc.TiledMap"),ae=S(O),he=A(ye),oe(le=ae((_e=function(t){function e(){var e;return(e=t.call(this)||this)._texGrids=new Map,e._textures=[],e._tilesets=[],e._animations=new Map,e._imageLayers=[],e._layers=[],e._groups=[],e._images=[],e._properties={},e._tileProperties=new Map,e._mapInfo=null,e._mapSize=new m(0,0),e._tileSize=new m(0,0),e._mapOrientation=J.ORTHO,e._isApplied=!1,e._tmxFile=fe&&fe(),e._enableCulling=ge&&ge(),e.cleanupImageCache=de&&de(),e}a(e,t);var r=e.prototype;return r.getMapSize=function(){return this._mapSize},r.getTileSize=function(){return this._tileSize},r.getMapOrientation=function(){return this._mapOrientation},r.getObjectGroups=function(){return this._groups},r.getObjectGroup=function(t){for(var e=this._groups,i=0,r=e.length;i<r;i++){var n=e[i];if(n&&n.getGroupName()===t)return n}return null},r.getProperties=function(){return this._properties},r.getLayers=function(){return this._layers},r.getLayer=function(t){for(var e=this._layers,i=0,r=e.length;i<r;i++){var n=e[i];if(n&&n.getLayerName()===t)return n}return null},r._changeLayer=function(t,e){for(var i=this._layers,r=0,n=i.length;r<n;r++){var s=i[r];if(s&&s.getLayerName()===t)return void(i[r]=e)}},r.getProperty=function(t){return this._properties[t.toString()]},r.getPropertiesForGID=function(t){return this._tileProperties.get(t)},r.enableTexelOffset=function(t){Nt(t)},r.__preload=function(){this._tmxFile&&!1===this._isApplied&&(this._applyFile(),this._isApplied=!0)},r.onEnable=function(){this.node.on(B.ANCHOR_CHANGED,this._syncAnchorPoint,this)},r.onDisable=function(){this.node.off(B.ANCHOR_CHANGED,this._syncAnchorPoint,this)},r._applyFile=function(){var t=[],e={},r=this._tmxFile;if(r){for(var n=r.spriteFrameNames,s=r.spriteFrameSizes,o=r.spriteFrames,a={},h={},l=0;l<n.length;++l){var u=n[l];h[u]=s[l],t[l]=o[l];var f=t[l];f&&(e[f.name]=f,a[u]=f)}var g={},d=r.imageLayerSpriteFrame;n=r.imageLayerSpriteFrameNames;for(var _=0;_<d.length;++_)g[n[_]]=d[_];for(var p=r.tsxFileNames,c=r.tsxFiles,m={},y=0;y<p.length;++y)p[y].length>0&&(m[p[y]]=c[y].text);var v=new bt(r.tmxXmlStr,m,a,h,g),A=v.getTilesets();A&&0!==A.length||i(7241),this._buildWithMapInfo(v)}else this._releaseMapInfo()},r._releaseMapInfo=function(){for(var t=this._layers,e=0,i=t.length;e<i;e++){var r,n;null==(r=t[e].node.parent)||r.off(B.SIZE_CHANGED,t[e].updateCulling,t[e]),null==(n=t[e].node.parent)||n.off(B.TRANSFORM_CHANGED,t[e].updateCulling,t[e]),t[e].node.removeFromParent(),t[e].node.destroy()}t.length=0;for(var s=this._groups,o=0,a=s.length;o<a;o++)s[o].node.removeFromParent(),s[o].node.destroy();s.length=0;for(var h=this._images,l=0,u=h.length;l<u;l++)h[l].removeFromParent(),h[l].destroy();h.length=0},r._syncAnchorPoint=function(){var t,e,i=this.node._getUITransformComp().anchorPoint,r=this.node._getUITransformComp().width*i.x,n=this.node._getUITransformComp().height*(1-i.y);for(t=0,e=this._layers.length;t<e;t++)this._layers[t].node._getUITransformComp().setAnchorPoint(i);for(t=0,e=this._groups.length;t<e;t++){var s=this._groups[t],o=s.node._getUITransformComp();o.anchorX=.5,o.anchorY=.5;var a=s.offset.x-r+o.width*o.anchorX,h=s.offset.y+n-o.height*o.anchorY;s.node.setPosition(a,h)}for(t=0,e=this._images.length;t<e;t++){var l=this._images[t]._getUITransformComp();l.anchorX=.5,l.anchorY=.5;var u=this._images[t]._offset.x-r+l.width*l.anchorX,f=this._images[t]._offset.y+n-l.height*l.anchorY;this._images[t].setPosition(u,f)}},r._fillAniGrids=function(t,e){for(var i,r=_(e.keys());!(i=r()).done;){var n=i.value,s=e.get(n);if(s)for(var o=s.frames,a=0;a<o.length;a++){var h=o[a];h.grid=t.get(h.tileid)}}},r._buildLayerAndGroup=function(){var t=this._tilesets,e=this._texGrids,i=this._animations;e.clear();for(var r=0,n=t.length;r<n;++r){var s=t[r];s&&(s.sourceImage?Ot(s,e,s.sourceImage):u(16406,r))}this._fillAniGrids(e,i);for(var o=this._layers,a=this._groups,h=this._images,l={},f=0,g=o.length;f<g;f++)l[o[f].node.name]=!0;for(var d=0,_=a.length;d<_;d++)l[a[d].node.name]=!0;for(var c=0,m=h.length;c<m;c++)l[h[c].name]=!0;o=this._layers=[],a=this._groups=[],h=this._images=[];var y=this._mapInfo,v=this.node,A=y.getAllChildren(),w=this._textures,S=0,x=0;if(A&&A.length>0)for(var I=0,T=A.length;I<T;I++){var b=A[I],C=b.name,D=this.node.getChildByName(C);if(l[C]=!1,D||((D=new k).name=C,D.layer=v.layer,v.addChild(D)),D.setSiblingIndex(I),D.active=b.visible,b instanceof ot){var N=D.getComponent(ce);N||(N=D.addComponent(ce)),N.init(b,y,t,w,e),N.enableCulling=this._enableCulling,b.ownTiles=!1,o.push(N)}else if(b instanceof st){var O=D.getComponent(me);O||(O=D.addComponent(me)),O._init(b,y,e),a.push(O)}else if(b instanceof at){var E=b.sourceImage;D.layerInfo=b,D._offset=new p(b.offset.x,-b.offset.y);var G=D.getComponent(Z);G||(G=D.addComponent(Z)),G.color.a*=b.opacity,G.spriteFrame=E;var F=E.width,L=E.height;E.original&&(F=E.originalSize.width,L=E.originalSize.height),D._getUITransformComp().setContentSize(F,L),h.push(D)}S=Math.max(S,D._getUITransformComp().width),x=Math.max(x,D._getUITransformComp().height)}for(var P=v.children,z=0,R=P.length;z<R;z++){var M=P[z];l[M.name]&&M.destroy()}this.node._getUITransformComp().setContentSize(S,x),this._syncAnchorPoint()},r._buildWithMapInfo=function(t){var e=this;this._mapInfo=t,this._mapSize=t.getMapSize(),this._tileSize=t.getTileSize(),this._mapOrientation=t.orientation,this._properties=t.properties,this._tileProperties=t.getTileProperties(),this._imageLayers=t.getImageLayers(),this._animations=t.getTileAnimations(),this._tilesets=t.getTilesets();var i=this._tilesets;this._textures.length=0;for(var r=[],n=0,s=i.length;n<s;++n){var o=i[n];o&&o.sourceImage&&(this._textures[n]=o.sourceImage,r.push(o.sourceImage))}for(var a=0;a<this._imageLayers.length;a++){var h=this._imageLayers[a];h&&h.sourceImage&&r.push(h.sourceImage)}this._buildLayerAndGroup(),this.cleanupImageCache&&this._textures.forEach((function(t){e.doCleanupImageCache(t)}))},r.doCleanupImageCache=function(t){t._image instanceof HTMLImageElement?t._image.src="":b.hasFeature(b.Feature.IMAGE_BITMAP)&&t._image instanceof ImageBitmap&&t._image.close&&t._image.close(),t._image=null},r.lateUpdate=function(t){for(var e,i=this._animations,r=this._texGrids,n=_(i.keys());!(e=n()).done;){var s=e.value,o=i.get(s),a=o.frames,h=a[o.frameIdx];o.dt+=t,h.duration<o.dt&&(o.dt=0,o.frameIdx++,o.frameIdx>=a.length&&(o.frameIdx=0),h=a[o.frameIdx]),r.set(s,h.grid)}for(var l=this.getLayers(),u=0,f=l.length;u<f;u++){var g=l[u];(g.hasAnimation()||g.node.hasChangedFlags)&&g._markForUpdateRenderData()}},o(e,[{key:"tmxAsset",get:function(){return this._tmxFile},set:function(t){(this._tmxFile!==t||C)&&(this._tmxFile=t,this._applyFile(),this._isApplied=!0)}},{key:"enableCulling",get:function(){return this._enableCulling},set:function(t){this._enableCulling=t;for(var e=this._layers,i=0;i<e.length;++i)e[i].enableCulling=t}}]),e}(D),_e.Orientation=J,_e.Property=Q,_e.TileFlag=$,_e.StaggerAxis=tt,_e.StaggerIndex=et,_e.TMXObjectType=rt,_e.RenderOrder=it,fe=w((ue=_e).prototype,"_tmxFile",[T],(function(){return null})),h(ue.prototype,"tmxAsset",[he],Object.getOwnPropertyDescriptor(ue.prototype,"tmxAsset"),ue.prototype),ge=w(ue.prototype,"_enableCulling",[T],(function(){return!0})),de=w(ue.prototype,"cleanupImageCache",[T],(function(){return!0})),le=ue))||le)||le)),Math.ceil(10922.5)),Ae=[],we=0;we<4;we++)Ae.push(new I);var Se,xe,Ie,Te=new x,be=new I,Ce={row:0,col:0},De={x:0,y:0},Ne={x:0,y:0},Oe={x:0,y:0},Ee={x:0,y:0},Ge=0,Fe=0,Le=0,Pe=0,ze=null,Re=null,Me=new(function(){function t(){}var e=t.prototype;return e.ensureAccessor=function(){if(!Re){var t=Y.root.device;Y.root.batcher2D,Re=new R(t,M),Y.on(V.BEFORE_DRAW,(function(){Re.reset()}))}},e.createData=function(){return null},e.fillBuffers=function(t){if(t&&0!==t.tiledDataArray.length){for(var e=t.tiledDataArray[t._tiledDataArrayIdx].renderData,i=e.chunk.meshBuffer.iData,r=e.chunk.meshBuffer.indexOffset,n=e.chunk.vertexOffset,s=e.vertexCount/4,o=0;o<s;o+=1)i[r]=n,i[r+1]=n+1,i[r+2]=n+2,i[r+3]=n+2,i[r+4]=n+1,i[r+5]=n+3,r+=6,n+=4;e.chunk.meshBuffer.indexOffset=r}},e.updateRenderData=function(t){if(t.updateCulling(),Fe=t.leftDownToCenterX,Le=t.leftDownToCenterY,t.colorChanged||t.isCullingDirty()||t.isUserNodeDirty()||t.hasAnimation()||t.hasTiledNode()||t.node.hasChangedFlags){var e,i;if(t.colorChanged=!1,t.destroyRenderData(),t.enableCulling){var r=t.cullingRect;e=r.leftDown,i=r.rightTop}else e=Ce,i=t.rightTop;switch(t.renderOrder){case it.RightDown:je(e,i,-1,1,t);break;case it.LeftDown:je(e,i,-1,-1,t);break;case it.RightUp:je(e,i,1,1,t);break;case it.LeftUp:default:je(e,i,1,-1,t)}t.setCullingDirty(!1),t.setUserNodeDirty(!1)}},e.updateColor=function(t){var e=t.color,i=new Float32Array(4);i[0]=e.r/255,i[1]=e.g/255,i[2]=e.b/255,i[3]=e.a/255;for(var r=t.tiledDataArray,n=0;n<r.length;n++){var s=r[n];if(s.renderData)for(var o=s.renderData,a=o.vData,h=o.vertexStart,l=o.vertexCount;h<l;h++)a.set(i,9*h+5)}},t}());function Ue(t,e){var i;t._rotated?(De.x=t.r,De.y=t.t,Ne.x=t.l,Ne.y=t.t,Oe.x=t.r,Oe.y=t.b,Ee.x=t.l,Ee.y=t.b):(De.x=t.l,De.y=t.t,Ne.x=t.l,Ne.y=t.b,Oe.x=t.r,Oe.y=t.t,Ee.x=t.r,Ee.y=t.b),(e&$.DIAGONAL)>>>0&&(i=Ne,Ne=Oe,Oe=i),(e&$.HORIZONTAL)>>>0&&(i=De,De=Oe,Oe=i,i=Ne,Ne=Ee,Ee=i),(e&$.VERTICAL)>>>0&&(i=De,De=Ne,Ne=i,i=Oe,Oe=Ee,Ee=i)}function Xe(){if(!(Pe<1)&&ze){var t=4*Pe,e=6*Pe,i=xe.requestTiledRenderData();i.renderData=F.add(M),i.texture=ze;var r=i.renderData;r.resize(t,e),r.chunk.vb.set(Se.subarray(0,9*t),0),Pe=0,ze=null}}function je(t,e,i,r,n){if(!(e.row<0||e.col<0)){xe=n;var s=n.node.worldMatrix;Ge=0;var o,a,h,l,u,f,g,d,_,p=n.tiledTiles,c=n.texGrids,m=n.tiles,y=18,v=27,A=n.vertices,w=0,S=0,x=0,I=0,T=0,b=0,C=!0;Ie=Ue;var D=new Float32Array(4);D[0]=n.color.r/255,D[1]=n.color.g/255,D[2]=n.color.b/255,D[3]=n.color.a/255,-1===i?(l=e.row,u=t.row):(l=t.row,u=e.row);var N=Math.abs(t.row-e.row)+1,O=Math.abs(e.col-t.col)+1;Se=new Float32Array(N*O*36),Pe=0;for(var E=Se;(u-l)*i>=0;l+=i)for(o=A[l],b=n.getNodesCountByRow(l),C=o&&0===b,1===r?(a=C&&t.col<o.minCol?o.minCol:t.col,h=C&&e.col>o.maxCol?o.maxCol:e.col):(a=C&&e.col>o.maxCol?o.maxCol:e.col,h=C&&t.col<o.minCol?o.minCol:t.col);(h-a)*r>=0;a+=r){if(f=o&&o[a],b>0){Xe();var G=n.requestSubNodesData(),F=n.getNodesByRowCol(l,a);F&&F.count>0&&(G.subNodes=F.list)}f&&(w=m[f.index],(d=c.get((w&$.FLIPPED_MASK)>>>0))&&(ze!==d.texture&&(Xe(),ze=d.texture),g=d.tileset._tileSize,S=f.left-Fe,x=f.bottom-Le,I=S+g.width,T=x+g.height,_=p[f.index],Ge=36*Pe,_?_.node.active&&He(_.node,D,E,S,I,T,x):(Ae[0].x=S,Ae[0].y=T,Ae[1].x=S,Ae[1].y=x,Ae[2].x=I,Ae[2].y=T,Ae[3].x=I,Ae[3].y=x,Ae[0].transformMat4(s),E[Ge]=Ae[0].x,E[Ge+1]=Ae[0].y,E[Ge+2]=Ae[0].z,Ae[1].transformMat4(s),E[Ge+9]=Ae[1].x,E[Ge+9+1]=Ae[1].y,E[Ge+9+2]=Ae[1].z,Ae[2].transformMat4(s),E[Ge+y]=Ae[2].x,E[Ge+y+1]=Ae[2].y,E[Ge+y+2]=Ae[2].z,Ae[3].transformMat4(s),E[Ge+v]=Ae[3].x,E[Ge+v+1]=Ae[3].y,E[Ge+v+2]=Ae[3].z,E.set(D,Ge+5),E.set(D,Ge+9+5),E.set(D,Ge+y+5),E.set(D,Ge+v+5)),Ie(d,w),E[Ge+3]=De.x,E[Ge+4]=De.y,E[Ge+9+3]=Ne.x,E[Ge+9+4]=Ne.y,E[Ge+y+3]=Oe.x,E[Ge+y+4]=Oe.y,E[Ge+v+3]=Ee.x,E[Ge+v+4]=Ee.y,++Pe>=ve&&Xe()))}Xe()}}function He(t,e,i,r,n,s,o){t.updateWorldTransform(),x.fromRTS(Te,t.rotation,t.position,t.scale),I.set(be,-(r+Fe),-(o+Le),0),x.transform(Te,Te,be),x.multiply(Te,t.parent.worldMatrix,Te);var a=Te,h=a.m12,l=a.m13,u=a.m00,f=a.m01,g=a.m04,d=a.m05;1===u&&0===f&&0===g&&1===d?(i[Ge]=r+h,i[Ge+1]=s+l,i[Ge+9]=r+h,i[Ge+9+1]=o+l,i[Ge+18]=n+h,i[Ge+18+1]=s+l,i[Ge+27]=n+h,i[Ge+27+1]=o+l):(i[Ge]=r*u+s*g+h,i[Ge+1]=r*f+s*d+l,i[Ge+9]=r*u+o*g+h,i[Ge+9+1]=r*f+o*d+l,i[Ge+18]=n*u+s*g+h,i[Ge+18+1]=n*f+s*d+l,i[Ge+27]=n*u+o*g+h,i[Ge+27+1]=n*f+o*d+l),i.set(e,Ge+5),i.set(e,Ge+9+5),i.set(e,Ge+18+5),i.set(e,Ge+27+5)}var ke=t("tiledLayerAssembler",{getAssembler:function(){return Me}});ce.Assembler=ke}}}));
