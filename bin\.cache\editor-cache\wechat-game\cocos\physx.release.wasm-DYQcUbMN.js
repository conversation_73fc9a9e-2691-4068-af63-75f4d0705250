System.register(["./gc-object-CKHc4SnS.js","./_commonjsHelpers-gZMueHPa.js"],(function(t){"use strict";var e,r;return{setters:[function(t){e=t.j},function(t){r=t.g}],execute:function(){function n(t,e){return e.forEach((function(e){e&&"string"!=typeof e&&!Array.isArray(e)&&Object.keys(e).forEach((function(r){if("default"!==r&&!(r in t)){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}}))})),Object.freeze(t)}var o,i,a,s={exports:{}};o=s,i="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0,a=function(t){void 0===t&&(t={});var r,n,o=void 0!==t?t:{};o.ready=new Promise((function(t,e){r=t,n=e}));var a=Object.assign({},o),s=function(t,e){throw e},u="";"undefined"!=typeof document&&document.currentScript&&(u=document.currentScript.src),i&&(u=i),u=0!==u.indexOf("blob:")?u.substr(0,u.replace(/[?#].*/,"").lastIndexOf("/")+1):"";var c,l=o.print||console.log.bind(console),f=o.printErr||console.error.bind(console);Object.assign(o,a),a=null,o.arguments&&o.arguments,o.thisProgram&&o.thisProgram,o.quit&&(s=o.quit),o.wasmBinary&&(c=o.wasmBinary);var p,d=o.noExitRuntime||!0;"object"!=typeof WebAssembly&&D("no native wasm support detected");var h,v,y,m,g,b,C,w,$,P=!1;function T(){var t=p.buffer;o.HEAP8=h=new Int8Array(t),o.HEAP16=y=new Int16Array(t),o.HEAP32=g=new Int32Array(t),o.HEAPU8=v=new Uint8Array(t),o.HEAPU16=m=new Uint16Array(t),o.HEAPU32=b=new Uint32Array(t),o.HEAPF32=C=new Float32Array(t),o.HEAPF64=w=new Float64Array(t)}var A=[],_=[],O=[];function j(){if(o.preRun)for("function"==typeof o.preRun&&(o.preRun=[o.preRun]);o.preRun.length;)t=o.preRun.shift(),A.unshift(t);var t;H(A)}function W(){if(o.postRun)for("function"==typeof o.postRun&&(o.postRun=[o.postRun]);o.postRun.length;)t=o.postRun.shift(),O.unshift(t);var t;H(O)}var S,E,k=0,F=null;function R(){if(k--,o.monitorRunDependencies&&o.monitorRunDependencies(k),0==k&&F){var t=F;F=null,t()}}function D(t){o.onAbort&&o.onAbort(t),f(t="Aborted("+t+")"),P=!0,t+=". Build with -sASSERTIONS for more info.";var e=new WebAssembly.RuntimeError(t);throw n(e),e}function x(t){return t.startsWith("data:application/octet-stream;base64,")}function I(t){try{if(t==S&&c)return new Uint8Array(c);throw"both async and sync fetching of the wasm failed"}catch(t){D(t)}}function U(t){return c||"function"!=typeof fetch?Promise.resolve().then((function(){return I(t)})):fetch(t,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+t+"'";return e.arrayBuffer()})).catch((function(){return I(t)}))}function V(t,e,r){return U(t).then((function(t){return WebAssembly.instantiate(t,e)})).then((function(t){return t})).then(r,(function(t){f("failed to asynchronously prepare wasm: "+t),D(t)}))}function z(t){this.name="ExitStatus",this.message="Program terminated with exit("+t+")",this.status=t}function H(t){for(;t.length>0;)t.shift()(o)}function B(t){if(void 0===t)return"_unknown";var e=(t=t.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return e>=48&&e<=57?"_"+t:t}function M(t,e){var r;return(r={},r[t=B(t)]=function(){return e.apply(this,arguments)},r)[t]}x(S="physx.release.wasm.wasm")||(E=S,S=o.locateFile?o.locateFile(E,u):u+E);var N=new function(){this.allocated=[void 0],this.freelist=[],this.get=function(t){return this.allocated[t]},this.has=function(t){return void 0!==this.allocated[t]},this.allocate=function(t){var e=this.freelist.pop()||this.allocated.length;return this.allocated[e]=t,e},this.free=function(t){this.allocated[t]=void 0,this.freelist.push(t)}};function q(t,e){var r=M(e,(function(t){this.name=e,this.message=t;var r=new Error(t).stack;void 0!==r&&(this.stack=this.toString()+"\n"+r.replace(/^Error(:[^\n]*)?\n/,""))}));return r.prototype=Object.create(t.prototype),r.prototype.constructor=r,r.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},r}var G=void 0;function L(t){throw new G(t)}var J=function(t){return t||L("Cannot use deleted val. handle = "+t),N.get(t).value},K=function(t){switch(t){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:return N.allocate({refcount:1,value:t})}},Q=void 0,Z=void 0;function X(t){for(var e="",r=t;v[r];)e+=Z[v[r++]];return e}var Y=[];function tt(){for(;Y.length;){var t=Y.pop();t.$$.deleteScheduled=!1,t.delete()}}var et=void 0;var rt={};function nt(t,e){for(void 0===e&&L("ptr should not be undefined");t.baseClass;)e=t.upcast(e),t=t.baseClass;return e}var ot={};function it(t){var e=Ce(t),r=X(e);return be(e),r}function at(t,e){var r=ot[t];return void 0===r&&L(e+" has unknown type "+it(t)),r}function st(){}var ut=!1;function ct(t){t.smartPtr?t.smartPtrType.rawDestructor(t.smartPtr):t.ptrType.registeredClass.rawDestructor(t.ptr)}function lt(t){t.count.value-=1,0===t.count.value&&ct(t)}function ft(t,e,r){if(e===r)return t;if(void 0===r.baseClass)return null;var n=ft(t,e,r.baseClass);return null===n?null:r.downcast(n)}var pt={};function dt(t,e){return e=nt(t,e),rt[e]}var ht=void 0;function vt(t){throw new ht(t)}function yt(t,e){return e.ptrType&&e.ptr||vt("makeClassHandle requires ptr and ptrType"),!!e.smartPtrType!=!!e.smartPtr&&vt("Both smartPtrType and smartPtr must be specified"),e.count={value:1},mt(Object.create(t,{$$:{value:e}}))}function mt(t){return"undefined"==typeof FinalizationRegistry?(mt=function(t){return t},t):(ut=new FinalizationRegistry((function(t){lt(t.$$)})),mt=function(t){var e=t.$$;if(e.smartPtr){var r={$$:e};ut.register(t,r,t)}return t},st=function(t){return ut.unregister(t)},mt(t))}var gt={};function bt(t){for(;t.length;){var e=t.pop();t.pop()(e)}}function Ct(t){return this.fromWireType(g[t>>2])}var wt={},$t={};function Pt(t,e,r){function n(e){var n=r(e);n.length!==t.length&&vt("Mismatched type converter count");for(var o=0;o<t.length;++o)At(t[o],n[o])}t.forEach((function(t){$t[t]=e}));var o=new Array(e.length),i=[],a=0;e.forEach((function(t,e){ot.hasOwnProperty(t)?o[e]=ot[t]:(i.push(t),wt.hasOwnProperty(t)||(wt[t]=[]),wt[t].push((function(){o[e]=ot[t],++a===i.length&&n(o)})))})),0===i.length&&n(o)}function Tt(t){switch(t){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+t)}}function At(t,e,r){if(void 0===r&&(r={}),!("argPackAdvance"in e))throw new TypeError("registerType registeredInstance requires argPackAdvance");var n=e.name;if(t||L('type "'+n+'" must have a positive integer typeid pointer'),ot.hasOwnProperty(t)){if(r.ignoreDuplicateRegistrations)return;L("Cannot register type '"+n+"' twice")}if(ot[t]=e,delete $t[t],wt.hasOwnProperty(t)){var o=wt[t];delete wt[t],o.forEach((function(t){return t()}))}}function _t(t){L(t.$$.ptrType.registeredClass.name+" instance already deleted")}function Ot(){}function jt(t,e,r){if(void 0===t[e].overloadTable){var n=t[e];t[e]=function(){return t[e].overloadTable.hasOwnProperty(arguments.length)||L("Function '"+r+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+t[e].overloadTable+")!"),t[e].overloadTable[arguments.length].apply(this,arguments)},t[e].overloadTable=[],t[e].overloadTable[n.argCount]=n}}function Wt(t,e,r){o.hasOwnProperty(t)?((void 0===r||void 0!==o[t].overloadTable&&void 0!==o[t].overloadTable[r])&&L("Cannot register public name '"+t+"' twice"),jt(o,t,t),o.hasOwnProperty(r)&&L("Cannot register multiple overloads of a function with the same number of arguments ("+r+")!"),o[t].overloadTable[r]=e):(o[t]=e,void 0!==r&&(o[t].numArguments=r))}function St(t,e,r,n,o,i,a,s){this.name=t,this.constructor=e,this.instancePrototype=r,this.rawDestructor=n,this.baseClass=o,this.getActualType=i,this.upcast=a,this.downcast=s,this.pureVirtualFunctions=[]}function Et(t,e,r){for(;e!==r;)e.upcast||L("Expected null or instance of "+r.name+", got an instance of "+e.name),t=e.upcast(t),e=e.baseClass;return t}function kt(t,e){if(null===e)return this.isReference&&L("null is not a valid "+this.name),0;e.$$||L('Cannot pass "'+Kt(e)+'" as a '+this.name),e.$$.ptr||L("Cannot pass deleted object as a pointer of type "+this.name);var r=e.$$.ptrType.registeredClass;return Et(e.$$.ptr,r,this.registeredClass)}function Ft(t,e){var r;if(null===e)return this.isReference&&L("null is not a valid "+this.name),this.isSmartPointer?(r=this.rawConstructor(),null!==t&&t.push(this.rawDestructor,r),r):0;e.$$||L('Cannot pass "'+Kt(e)+'" as a '+this.name),e.$$.ptr||L("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&e.$$.ptrType.isConst&&L("Cannot convert argument of type "+(e.$$.smartPtrType?e.$$.smartPtrType.name:e.$$.ptrType.name)+" to parameter type "+this.name);var n=e.$$.ptrType.registeredClass;if(r=Et(e.$$.ptr,n,this.registeredClass),this.isSmartPointer)switch(void 0===e.$$.smartPtr&&L("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:e.$$.smartPtrType===this?r=e.$$.smartPtr:L("Cannot convert argument of type "+(e.$$.smartPtrType?e.$$.smartPtrType.name:e.$$.ptrType.name)+" to parameter type "+this.name);break;case 1:r=e.$$.smartPtr;break;case 2:if(e.$$.smartPtrType===this)r=e.$$.smartPtr;else{var o=e.clone();r=this.rawShare(r,K((function(){o.delete()}))),null!==t&&t.push(this.rawDestructor,r)}break;default:L("Unsupporting sharing policy")}return r}function Rt(t,e){if(null===e)return this.isReference&&L("null is not a valid "+this.name),0;e.$$||L('Cannot pass "'+Kt(e)+'" as a '+this.name),e.$$.ptr||L("Cannot pass deleted object as a pointer of type "+this.name),e.$$.ptrType.isConst&&L("Cannot convert argument of type "+e.$$.ptrType.name+" to parameter type "+this.name);var r=e.$$.ptrType.registeredClass;return Et(e.$$.ptr,r,this.registeredClass)}function Dt(t,e,r,n,o,i,a,s,u,c,l){this.name=t,this.registeredClass=e,this.isReference=r,this.isConst=n,this.isSmartPointer=o,this.pointeeType=i,this.sharingPolicy=a,this.rawGetPointee=s,this.rawConstructor=u,this.rawShare=c,this.rawDestructor=l,o||void 0!==e.baseClass?this.toWireType=Ft:n?(this.toWireType=kt,this.destructorFunction=null):(this.toWireType=Rt,this.destructorFunction=null)}function xt(t,e,r){o.hasOwnProperty(t)||vt("Replacing nonexistant public symbol"),void 0!==o[t].overloadTable&&void 0!==r?o[t].overloadTable[r]=e:(o[t]=e,o[t].argCount=r)}function It(t,e,r){var n=o["dynCall_"+t];return r&&r.length?n.apply(null,[e].concat(r)):n.call(null,e)}var Ut=[];function Vt(t){var e=Ut[t];return e||(t>=Ut.length&&(Ut.length=t+1),Ut[t]=e=$.get(t)),e}function zt(t,e,r){return t.includes("j")?It(t,e,r):Vt(e).apply(null,r)}function Ht(t,e){var r,n,o,i=(t=X(t)).includes("j")?(r=t,n=e,o=[],function(){return o.length=0,Object.assign(o,arguments),zt(r,n,o)}):Vt(e);return"function"!=typeof i&&L("unknown function pointer with signature "+t+": "+e),i}var Bt=void 0;function Mt(t,e){var r=[],n={};throw e.forEach((function t(e){n[e]||ot[e]||($t[e]?$t[e].forEach(t):(r.push(e),n[e]=!0))})),new Bt(t+": "+r.map(it).join([", "]))}function Nt(t,e,r,n,o){var i=e.length;i<2&&L("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var a=null!==e[1]&&null!==r,s=!1,u=1;u<e.length;++u)if(null!==e[u]&&void 0===e[u].destructorFunction){s=!0;break}var c="void"!==e[0].name,l=i-2,f=new Array(l),p=[],d=[];return function(){var r;arguments.length!==l&&L("function "+t+" called with "+arguments.length+" arguments, expected "+l+" args!"),d.length=0,p.length=a?2:1,p[0]=o,a&&(r=e[1].toWireType(d,this),p[1]=r);for(var i=0;i<l;++i)f[i]=e[i+2].toWireType(d,arguments[i]),p.push(f[i]);return function(t){if(s)bt(d);else for(var n=a?1:2;n<e.length;n++){var o=1===n?r:f[n-2];null!==e[n].destructorFunction&&e[n].destructorFunction(o)}if(c)return e[0].fromWireType(t)}(n.apply(null,p))}}function qt(t,e){for(var r=[],n=0;n<t;n++)r.push(b[e+4*n>>2]);return r}function Gt(t,e,r){return t instanceof Object||L(r+' with invalid "this": '+t),t instanceof e.registeredClass.constructor||L(r+' incompatible with "this" of type '+t.constructor.name),t.$$.ptr||L("cannot call emscripten binding method "+r+" on deleted object"),Et(t.$$.ptr,t.$$.ptrType.registeredClass,e.registeredClass)}function Lt(t){t>=N.reserved&&0==--N.get(t).refcount&&N.free(t)}function Jt(t,e,r){switch(e){case 0:return function(t){var e=r?h:v;return this.fromWireType(e[t])};case 1:return function(t){var e=r?y:m;return this.fromWireType(e[t>>1])};case 2:return function(t){var e=r?g:b;return this.fromWireType(e[t>>2])};default:throw new TypeError("Unknown integer type: "+t)}}function Kt(t){if(null===t)return"null";var e=typeof t;return"object"===e||"array"===e||"function"===e?t.toString():""+t}function Qt(t,e){switch(e){case 2:return function(t){return this.fromWireType(C[t>>2])};case 3:return function(t){return this.fromWireType(w[t>>3])};default:throw new TypeError("Unknown float type: "+t)}}function Zt(t,e,r){switch(e){case 0:return r?function(t){return h[t]}:function(t){return v[t]};case 1:return r?function(t){return y[t>>1]}:function(t){return m[t>>1]};case 2:return r?function(t){return g[t>>2]}:function(t){return b[t>>2]};default:throw new TypeError("Unknown integer type: "+t)}}function Xt(t,e,r,n){if(!(n>0))return 0;for(var o=r,i=r+n-1,a=0;a<t.length;++a){var s=t.charCodeAt(a);if(s>=55296&&s<=57343&&(s=65536+((1023&s)<<10)|1023&t.charCodeAt(++a)),s<=127){if(r>=i)break;e[r++]=s}else if(s<=2047){if(r+1>=i)break;e[r++]=192|s>>6,e[r++]=128|63&s}else if(s<=65535){if(r+2>=i)break;e[r++]=224|s>>12,e[r++]=128|s>>6&63,e[r++]=128|63&s}else{if(r+3>=i)break;e[r++]=240|s>>18,e[r++]=128|s>>12&63,e[r++]=128|s>>6&63,e[r++]=128|63&s}}return e[r]=0,r-o}function Yt(t){for(var e=0,r=0;r<t.length;++r){var n=t.charCodeAt(r);n<=127?e++:n<=2047?e+=2:n>=55296&&n<=57343?(e+=4,++r):e+=3}return e}var te="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function ee(t,e,r){for(var n=e+r,o=e;t[o]&&!(o>=n);)++o;if(o-e>16&&t.buffer&&te)return te.decode(t.subarray(e,o));for(var i="";e<o;){var a=t[e++];if(128&a){var s=63&t[e++];if(192!=(224&a)){var u=63&t[e++];if((a=224==(240&a)?(15&a)<<12|s<<6|u:(7&a)<<18|s<<12|u<<6|63&t[e++])<65536)i+=String.fromCharCode(a);else{var c=a-65536;i+=String.fromCharCode(55296|c>>10,56320|1023&c)}}else i+=String.fromCharCode((31&a)<<6|s)}else i+=String.fromCharCode(a)}return i}var re="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function ne(t,e){for(var r=t,n=r>>1,o=n+e/2;!(n>=o)&&m[n];)++n;if((r=n<<1)-t>32&&re)return re.decode(v.subarray(t,r));for(var i="",a=0;!(a>=e/2);++a){var s=y[t+2*a>>1];if(0==s)break;i+=String.fromCharCode(s)}return i}function oe(t,e,r){if(void 0===r&&(r=2147483647),r<2)return 0;for(var n=e,o=(r-=2)<2*t.length?r/2:t.length,i=0;i<o;++i){var a=t.charCodeAt(i);y[e>>1]=a,e+=2}return y[e>>1]=0,e-n}function ie(t){return 2*t.length}function ae(t,e){for(var r=0,n="";!(r>=e/4);){var o=g[t+4*r>>2];if(0==o)break;if(++r,o>=65536){var i=o-65536;n+=String.fromCharCode(55296|i>>10,56320|1023&i)}else n+=String.fromCharCode(o)}return n}function se(t,e,r){if(void 0===r&&(r=2147483647),r<4)return 0;for(var n=e,o=n+r-4,i=0;i<t.length;++i){var a=t.charCodeAt(i);if(a>=55296&&a<=57343&&(a=65536+((1023&a)<<10)|1023&t.charCodeAt(++i)),g[e>>2]=a,(e+=4)+4>o)break}return g[e>>2]=0,e-n}function ue(t){for(var e=0,r=0;r<t.length;++r){var n=t.charCodeAt(r);n>=55296&&n<=57343&&++r,e+=4}return e}function ce(t){var e=[];return b[t>>2]=K(e),e}var le={};function fe(t){var e=le[t];return void 0===e?X(t):e}var pe=[];function de(t,e){for(var r=new Array(t),n=0;n<t;++n)r[n]=at(b[e+4*n>>2],"parameter "+n);return r}var he,ve=[];function ye(t){var e=t-p.buffer.byteLength+65535>>>16;try{return p.grow(e),T(),1}catch(t){}}he=function(){return performance.now()};var me=[null,[],[]];G=o.BindingError=q(Error,"BindingError"),N.allocated.push({value:void 0},{value:null},{value:!0},{value:!1}),N.reserved=N.allocated.length,o.count_emval_handles=function(){for(var t=0,e=N.reserved;e<N.allocated.length;++e)void 0!==N.allocated[e]&&++t;return t},Q=o.PureVirtualError=q(Error,"PureVirtualError"),function(){for(var t=new Array(256),e=0;e<256;++e)t[e]=String.fromCharCode(e);Z=t}(),o.getInheritedInstanceCount=function(){return Object.keys(rt).length},o.getLiveInheritedInstances=function(){var t=[];for(var e in rt)rt.hasOwnProperty(e)&&t.push(rt[e]);return t},o.flushPendingDeletes=tt,o.setDelayFunction=function(t){et=t,Y.length&&et&&et(tt)},ht=o.InternalError=q(Error,"InternalError"),Ot.prototype.isAliasOf=function(t){if(!(this instanceof Ot))return!1;if(!(t instanceof Ot))return!1;for(var e=this.$$.ptrType.registeredClass,r=this.$$.ptr,n=t.$$.ptrType.registeredClass,o=t.$$.ptr;e.baseClass;)r=e.upcast(r),e=e.baseClass;for(;n.baseClass;)o=n.upcast(o),n=n.baseClass;return e===n&&r===o},Ot.prototype.clone=function(){if(this.$$.ptr||_t(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var t,e=mt(Object.create(Object.getPrototypeOf(this),{$$:{value:(t=this.$$,{count:t.count,deleteScheduled:t.deleteScheduled,preservePointerOnDelete:t.preservePointerOnDelete,ptr:t.ptr,ptrType:t.ptrType,smartPtr:t.smartPtr,smartPtrType:t.smartPtrType})}}));return e.$$.count.value+=1,e.$$.deleteScheduled=!1,e},Ot.prototype.delete=function(){this.$$.ptr||_t(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&L("Object already scheduled for deletion"),st(this),lt(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)},Ot.prototype.isDeleted=function(){return!this.$$.ptr},Ot.prototype.deleteLater=function(){return this.$$.ptr||_t(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&L("Object already scheduled for deletion"),Y.push(this),1===Y.length&&et&&et(tt),this.$$.deleteScheduled=!0,this},Dt.prototype.getPointee=function(t){return this.rawGetPointee&&(t=this.rawGetPointee(t)),t},Dt.prototype.destructor=function(t){this.rawDestructor&&this.rawDestructor(t)},Dt.prototype.argPackAdvance=8,Dt.prototype.readValueFromPointer=Ct,Dt.prototype.deleteObject=function(t){null!==t&&t.delete()},Dt.prototype.fromWireType=function(t){var e=this.getPointee(t);if(!e)return this.destructor(t),null;var r=dt(this.registeredClass,e);if(void 0!==r){if(0===r.$$.count.value)return r.$$.ptr=e,r.$$.smartPtr=t,r.clone();var n=r.clone();return this.destructor(t),n}function o(){return this.isSmartPointer?yt(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:e,smartPtrType:this,smartPtr:t}):yt(this.registeredClass.instancePrototype,{ptrType:this,ptr:t})}var i,a=this.registeredClass.getActualType(e),s=pt[a];if(!s)return o.call(this);i=this.isConst?s.constPointerType:s.pointerType;var u=ft(e,this.registeredClass,i.registeredClass);return null===u?o.call(this):this.isSmartPointer?yt(i.registeredClass.instancePrototype,{ptrType:i,ptr:u,smartPtrType:this,smartPtr:t}):yt(i.registeredClass.instancePrototype,{ptrType:i,ptr:u})},Bt=o.UnboundTypeError=q(Error,"UnboundTypeError");var ge={t:function(t,e,r){t=X(t),e=at(e,"wrapper"),r=J(r);var n=[].slice,o=e.registeredClass,i=o.instancePrototype,a=o.baseClass.instancePrototype,s=o.baseClass.constructor,u=M(t,(function(){o.baseClass.pureVirtualFunctions.forEach(function(t){if(this[t]===a[t])throw new Q("Pure virtual function "+t+" must be implemented in JavaScript")}.bind(this)),Object.defineProperty(this,"__parent",{value:i}),this.__construct.apply(this,n.call(arguments))}));for(var c in i.__construct=function(){this===i&&L("Pass correct 'this' to __construct");var t=s.implement.apply(void 0,[this].concat(n.call(arguments)));st(t);var e,r=t.$$;t.notifyOnDestruction(),r.preservePointerOnDelete=!0,Object.defineProperties(this,{$$:{value:r}}),mt(this),e=nt(o,e=r.ptr),rt.hasOwnProperty(e)?L("Tried to register registered instance: "+e):rt[e]=this},i.__destruct=function(){var t;this===i&&L("Pass correct 'this' to __destruct"),st(this),t=nt(o,t=this.$$.ptr),rt.hasOwnProperty(t)?delete rt[t]:L("Tried to unregister unregistered instance: "+t)},u.prototype=Object.create(i),r)u.prototype[c]=r[c];return K(u)},s:function(t){var e=gt[t];delete gt[t];var r=e.rawConstructor,n=e.rawDestructor,o=e.fields;Pt([t],o.map((function(t){return t.getterReturnType})).concat(o.map((function(t){return t.setterArgumentType}))),(function(t){var i={};return o.forEach((function(e,r){var n=e.fieldName,a=t[r],s=e.getter,u=e.getterContext,c=t[r+o.length],l=e.setter,f=e.setterContext;i[n]={read:function(t){return a.fromWireType(s(u,t))},write:function(t,e){var r=[];l(f,t,c.toWireType(r,e)),bt(r)}}})),[{name:e.name,fromWireType:function(t){var e={};for(var r in i)e[r]=i[r].read(t);return n(t),e},toWireType:function(t,e){for(var o in i)if(!(o in e))throw new TypeError('Missing field: "'+o+'"');var a=r();for(o in i)i[o].write(a,e[o]);return null!==t&&t.push(n,a),a},argPackAdvance:8,readValueFromPointer:Ct,destructorFunction:n}]}))},C:function(){},I:function(t,e,r,n,o){var i=Tt(r);At(t,{name:e=X(e),fromWireType:function(t){return!!t},toWireType:function(t,e){return e?n:o},argPackAdvance:8,readValueFromPointer:function(t){var n;if(1===r)n=h;else if(2===r)n=y;else{if(4!==r)throw new TypeError("Unknown boolean type size: "+e);n=g}return this.fromWireType(n[t>>i])},destructorFunction:null})},c:function(t,e,r,n,o,i,a,s,u,c,l,f,p){l=X(l),i=Ht(o,i),s&&(s=Ht(a,s)),c&&(c=Ht(u,c)),p=Ht(f,p);var d=B(l);Wt(d,(function(){Mt("Cannot construct "+l+" due to unbound types",[n])})),Pt([t,e,r],n?[n]:[],(function(e){var r,o;e=e[0],o=n?(r=e.registeredClass).instancePrototype:Ot.prototype;var a=M(d,(function(){if(Object.getPrototypeOf(this)!==u)throw new G("Use 'new' to construct "+l);if(void 0===f.constructor_body)throw new G(l+" has no accessible constructor");var t=f.constructor_body[arguments.length];if(void 0===t)throw new G("Tried to invoke ctor of "+l+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(f.constructor_body).toString()+") parameters instead!");return t.apply(this,arguments)})),u=Object.create(o,{constructor:{value:a}});a.prototype=u;var f=new St(l,a,u,p,r,i,s,c);f.baseClass&&(void 0===f.baseClass.__derivedClasses&&(f.baseClass.__derivedClasses=[]),f.baseClass.__derivedClasses.push(f));var h=new Dt(l,f,!0,!1,!1),v=new Dt(l+"*",f,!1,!1,!1),y=new Dt(l+" const*",f,!1,!0,!1);return pt[t]={pointerType:v,constPointerType:y},xt(d,a),[h,v,y]}))},o:function(t,r,n,o,i,a,s){var u=qt(n,o);r=X(r),a=Ht(i,a),Pt([],[t],(function(t){var o=(t=t[0]).name+"."+r;function i(){Mt("Cannot call "+o+" due to unbound types",u)}r.startsWith("@@")&&(r=Symbol[r.substring(2)]);var c=t.registeredClass.constructor;return void 0===c[r]?(i.argCount=n-1,c[r]=i):(jt(c,r,o),c[r].overloadTable[n-1]=i),Pt([],u,(function(i){var u=[i[0],null].concat(i.slice(1)),l=Nt(o,u,null,a,s);if(void 0===c[r].overloadTable?(l.argCount=n-1,c[r]=l):c[r].overloadTable[n-1]=l,t.registeredClass.__derivedClasses)for(var f,p=e(t.registeredClass.__derivedClasses);!(f=p()).done;){var d=f.value;d.constructor.hasOwnProperty(r)||(d.constructor[r]=l)}return[]})),[]}))},e:function(t,e,r,n,o,i){e>0||D(undefined);var a=qt(e,r);o=Ht(n,o),Pt([],[t],(function(t){var r="constructor "+(t=t[0]).name;if(void 0===t.registeredClass.constructor_body&&(t.registeredClass.constructor_body=[]),void 0!==t.registeredClass.constructor_body[e-1])throw new G("Cannot register multiple constructors with identical number of parameters ("+(e-1)+") for class '"+t.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return t.registeredClass.constructor_body[e-1]=function(){Mt("Cannot construct "+t.name+" due to unbound types",a)},Pt([],a,(function(n){return n.splice(1,0,null),t.registeredClass.constructor_body[e-1]=Nt(r,n,null,o,i),[]})),[]}))},a:function(t,e,r,n,o,i,a,s){var u=qt(r,n);e=X(e),i=Ht(o,i),Pt([],[t],(function(t){var n=(t=t[0]).name+"."+e;function o(){Mt("Cannot call "+n+" due to unbound types",u)}e.startsWith("@@")&&(e=Symbol[e.substring(2)]),s&&t.registeredClass.pureVirtualFunctions.push(e);var c=t.registeredClass.instancePrototype,l=c[e];return void 0===l||void 0===l.overloadTable&&l.className!==t.name&&l.argCount===r-2?(o.argCount=r-2,o.className=t.name,c[e]=o):(jt(c,e,n),c[e].overloadTable[r-2]=o),Pt([],u,(function(o){var s=Nt(n,o,t,i,a);return void 0===c[e].overloadTable?(s.argCount=r-2,c[e]=s):c[e].overloadTable[r-2]=s,[]})),[]}))},d:function(t,e,r,n,o,i,a,s,u,c){e=X(e),o=Ht(n,o),Pt([],[t],(function(t){var n=(t=t[0]).name+"."+e,l={get:function(){Mt("Cannot access "+n+" due to unbound types",[r,a])},enumerable:!0,configurable:!0};return l.set=u?function(){Mt("Cannot access "+n+" due to unbound types",[r,a])}:function(){L(n+" is a read-only property")},Object.defineProperty(t.registeredClass.instancePrototype,e,l),Pt([],u?[r,a]:[r],(function(r){var a=r[0],l={get:function(){var e=Gt(this,t,n+" getter");return a.fromWireType(o(i,e))},enumerable:!0};if(u){u=Ht(s,u);var f=r[1];l.set=function(e){var r=Gt(this,t,n+" setter"),o=[];u(c,r,f.toWireType(o,e)),bt(o)}}return Object.defineProperty(t.registeredClass.instancePrototype,e,l),[]})),[]}))},x:function(t,e,r){t=X(t),Pt([],[e],(function(e){return e=e[0],o[t]=e.fromWireType(r),[]}))},H:function(t,e){At(t,{name:e=X(e),fromWireType:function(t){var e=J(t);return Lt(t),e},toWireType:function(t,e){return K(e)},argPackAdvance:8,readValueFromPointer:Ct,destructorFunction:null})},h:function(t,e,r,n){var o=Tt(r);function i(){}e=X(e),i.values={},At(t,{name:e,constructor:i,fromWireType:function(t){return this.constructor.values[t]},toWireType:function(t,e){return e.value},argPackAdvance:8,readValueFromPointer:Jt(e,o,n),destructorFunction:null}),Wt(e,i)},b:function(t,e,r){var n=at(t,"enum");e=X(e);var o=n.constructor,i=Object.create(n.constructor.prototype,{value:{value:r},constructor:{value:M(n.name+"_"+e,(function(){}))}});o.values[r]=i,o[e]=i},A:function(t,e,r){var n=Tt(r);At(t,{name:e=X(e),fromWireType:function(t){return t},toWireType:function(t,e){return e},argPackAdvance:8,readValueFromPointer:Qt(e,n),destructorFunction:null})},g:function(t,e,r,n,o,i){var a=qt(e,r);t=X(t),o=Ht(n,o),Wt(t,(function(){Mt("Cannot call "+t+" due to unbound types",a)}),e-1),Pt([],a,(function(r){var n=[r[0],null].concat(r.slice(1));return xt(t,Nt(t,n,null,o,i),e-1),[]}))},p:function(t,e,r,n){e=X(e);var o=Tt(r),i=function(t){return t};if(0===n){var a=32-8*r;i=function(t){return t<<a>>>a}}var s=e.includes("unsigned");At(t,{name:e,fromWireType:i,toWireType:s?function(t,e){return this.name,e>>>0}:function(t,e){return this.name,e},argPackAdvance:8,readValueFromPointer:Zt(e,o,0!==n),destructorFunction:null})},j:function(t,e,r){var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][e];function o(t){var e=b,r=e[t>>=2],o=e[t+1];return new n(e.buffer,o,r)}At(t,{name:r=X(r),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},z:function(t,e){var r="std::string"===(e=X(e));At(t,{name:e,fromWireType:function(t){var e,n,o=b[t>>2],i=t+4;if(r)for(var a=i,s=0;s<=o;++s){var u=i+s;if(s==o||0==v[u]){var c=(n=a)?ee(v,n,u-a):"";void 0===e?e=c:(e+=String.fromCharCode(0),e+=c),a=u+1}}else{var l=new Array(o);for(s=0;s<o;++s)l[s]=String.fromCharCode(v[i+s]);e=l.join("")}return be(t),e},toWireType:function(t,e){var n;e instanceof ArrayBuffer&&(e=new Uint8Array(e));var o="string"==typeof e;o||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Int8Array||L("Cannot pass non-string to std::string"),n=r&&o?Yt(e):e.length;var i=$e(4+n+1),a=i+4;if(b[i>>2]=n,r&&o)Xt(e,v,a,n+1);else if(o)for(var s=0;s<n;++s){var u=e.charCodeAt(s);u>255&&(be(a),L("String has UTF-16 code units that do not fit in 8 bits")),v[a+s]=u}else for(s=0;s<n;++s)v[a+s]=e[s];return null!==t&&t.push(be,i),i},argPackAdvance:8,readValueFromPointer:Ct,destructorFunction:function(t){be(t)}})},w:function(t,e,r){var n,o,i,a,s;r=X(r),2===e?(n=ne,o=oe,a=ie,i=function(){return m},s=1):4===e&&(n=ae,o=se,a=ue,i=function(){return b},s=2),At(t,{name:r,fromWireType:function(t){for(var r,o=b[t>>2],a=i(),u=t+4,c=0;c<=o;++c){var l=t+4+c*e;if(c==o||0==a[l>>s]){var f=n(u,l-u);void 0===r?r=f:(r+=String.fromCharCode(0),r+=f),u=l+e}}return be(t),r},toWireType:function(t,n){"string"!=typeof n&&L("Cannot pass non-string to C++ string type "+r);var i=a(n),u=$e(4+i+e);return b[u>>2]=i>>s,o(n,u+4,i+e),null!==t&&t.push(be,u),u},argPackAdvance:8,readValueFromPointer:Ct,destructorFunction:function(t){be(t)}})},r:function(t,e,r,n,o,i){gt[t]={name:X(e),rawConstructor:Ht(r,n),rawDestructor:Ht(o,i),fields:[]}},m:function(t,e,r,n,o,i,a,s,u,c){gt[t].fields.push({fieldName:X(e),getterReturnType:r,getter:Ht(n,o),getterContext:i,setterArgumentType:a,setter:Ht(s,u),setterContext:c})},J:function(t,e){At(t,{isVoid:!0,name:e=X(e),argPackAdvance:0,fromWireType:function(){},toWireType:function(){}})},v:function(t,e,r,n,o){return(t=pe[t])(e=J(e),r=fe(r),ce(n),o)},l:function(t,e,r,n){(t=pe[t])(e=J(e),r=fe(r),null,n)},n:Lt,i:function(t,e){var r=de(t,e),n=r[0],o=n.name+"_$"+r.slice(1).map((function(t){return t.name})).join("_")+"$",i=ve[o];if(void 0!==i)return i;var a,s,u=new Array(t-1);return a=function(e,o,i,a){for(var s=0,c=0;c<t-1;++c)u[c]=r[c+1].readValueFromPointer(a+s),s+=r[c+1].argPackAdvance;var l=e[o].apply(e,u);for(c=0;c<t-1;++c)r[c+1].deleteObject&&r[c+1].deleteObject(u[c]);if(!n.isVoid)return n.toWireType(i,l)},s=pe.length,pe.push(a),i=s,ve[o]=i,i},B:function(t){t>4&&(N.get(t).refcount+=1)},u:function(t){bt(J(t)),Lt(t)},q:function(t,e){var r=(t=at(t,"_emval_take_value")).readValueFromPointer(e);return K(r)},k:function(){D("")},F:function(){return Date.now()},f:he,G:function(t,e,r){v.copyWithin(t,e,e+r)},D:function(t){var e=v.length,r=2147483648;if((t>>>=0)>r)return!1;for(var n,o=1;o<=4;o*=2){var i=e*(1+.2/o);if(i=Math.min(i,t+100663296),ye(Math.min(r,(n=Math.max(t,i))+(65536-n%65536)%65536)))return!0}return!1},E:function(t){var e;e=t,d||(o.onExit&&o.onExit(e),P=!0),s(e,new z(e))},y:function(t,e,r,n){for(var o,i,a,s=0,u=0;u<r;u++){var c=b[e>>2],p=b[e+4>>2];e+=8;for(var d=0;d<p;d++)o=t,i=v[c+d],a=void 0,a=me[o],0===i||10===i?((1===o?l:f)(ee(a,0)),a.length=0):a.push(i);s+=p}return b[n>>2]=s,0}};!function(){var t,e,r,i,a={a:ge};function s(t){var e,r=t.exports;return o.asm=r,p=o.asm.K,T(),$=o.asm.N,e=o.asm.L,_.unshift(e),R(),r}if(k++,o.monitorRunDependencies&&o.monitorRunDependencies(k),o.instantiateWasm)try{return o.instantiateWasm(a,s)}catch(t){f("Module.instantiateWasm callback failed with error: "+t),n(t)}(t=c,e=S,r=a,i=function(t){s(t.instance)},t||"function"!=typeof WebAssembly.instantiateStreaming||x(e)||"function"!=typeof fetch?V(e,r,i):fetch(e,{credentials:"same-origin"}).then((function(t){return WebAssembly.instantiateStreaming(t,r).then(i,(function(t){return f("wasm streaming compile failed: "+t),f("falling back to ArrayBuffer instantiation"),V(e,r,i)}))}))).catch(n)}();var be=function(){return(be=o.asm.M).apply(null,arguments)},Ce=function(){return(Ce=o.asm.O).apply(null,arguments)};o.__embind_initialize_bindings=function(){return(o.__embind_initialize_bindings=o.asm.P).apply(null,arguments)};var we,$e=function(){return($e=o.asm.Q).apply(null,arguments)};function Pe(){function t(){we||(we=!0,o.calledRun=!0,P||(H(_),r(o),o.onRuntimeInitialized&&o.onRuntimeInitialized(),W()))}k>0||(j(),k>0||(o.setStatus?(o.setStatus("Running..."),setTimeout((function(){setTimeout((function(){o.setStatus("")}),1),t()}),1)):t()))}if(o.dynCall_iifiiiijii=function(){return(o.dynCall_iifiiiijii=o.asm.R).apply(null,arguments)},o.dynCall_vifijii=function(){return(o.dynCall_vifijii=o.asm.S).apply(null,arguments)},o.dynCall_jiji=function(){return(o.dynCall_jiji=o.asm.T).apply(null,arguments)},F=function t(){we||Pe(),we||(F=t)},o.preInit)for("function"==typeof o.preInit&&(o.preInit=[o.preInit]);o.preInit.length>0;)o.preInit.pop()();return Pe(),t.ready},o.exports=a;var u=s.exports,c=r(u);t("p",n({__proto__:null,default:c},[u]))}}}));
