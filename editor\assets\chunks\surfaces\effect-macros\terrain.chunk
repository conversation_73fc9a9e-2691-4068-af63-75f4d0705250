// Copyright (c) 2017-2022 Xiamen Yaji Software Co., Ltd.

// Rename some user macros to surface internal macros

#define CC_SURFACES_USE_VERTEX_COLOR 0
#define CC_SURFACES_USE_SECOND_UV 0
#define CC_SURFACES_USE_TWO_SIDED 0

// vs-fs varing addon data
#define CC_SURFACES_TRANSFER_LOCAL_POS 1

#if USE_REFLECTION_DENOISE
  #define CC_SURFACES_USE_REFLECTION_DENOISE USE_REFLECTION_DENOISE
#else
  #define CC_SURFACES_USE_REFLECTION_DENOISE 0
#endif

#include <surfaces/effect-macros/common-macros>
