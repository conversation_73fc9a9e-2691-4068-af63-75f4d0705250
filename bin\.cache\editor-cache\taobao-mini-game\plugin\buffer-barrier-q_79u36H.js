System.register(["./gc-object-D18ulfCO.js"],(function(t){"use strict";var e,i,s;return{setters:[function(t){e=t._,i=t.a,s=t.ag}],execute:function(){t({aI:Ee,aJ:de,aN:Se,aP:Ae,bq:Ne,br:Ce,m:Ie,u:pe});var n,r,o,u,h,a,c,R,_,A,T,E,d,f,S,p,C,N,l,B,I,O,v,y,G,m,L,U,g,x,D,M,P,F,b,X,w,H,k,W,V,Y,z,K,Q,Z,j=function(t,e,i){for(var s=0;s<e.length;++s)t.length<=s&&t.push(new i),t[s].copy(e[s]);t.length=e.length};t("aT",n),function(t){t[t.UNKNOWN=0]="UNKNOWN",t[t.SWAPCHAIN=1]="SWAPCHAIN",t[t.BUFFER=2]="BUFFER",t[t.TEXTURE=3]="TEXTURE",t[t.RENDER_PASS=4]="RENDER_PASS",t[t.FRAMEBUFFER=5]="FRAMEBUFFER",t[t.SAMPLER=6]="SAMPLER",t[t.SHADER=7]="SHADER",t[t.DESCRIPTOR_SET_LAYOUT=8]="DESCRIPTOR_SET_LAYOUT",t[t.PIPELINE_LAYOUT=9]="PIPELINE_LAYOUT",t[t.PIPELINE_STATE=10]="PIPELINE_STATE",t[t.DESCRIPTOR_SET=11]="DESCRIPTOR_SET",t[t.INPUT_ASSEMBLER=12]="INPUT_ASSEMBLER",t[t.COMMAND_BUFFER=13]="COMMAND_BUFFER",t[t.QUEUE=14]="QUEUE",t[t.QUERY_POOL=15]="QUERY_POOL",t[t.GLOBAL_BARRIER=16]="GLOBAL_BARRIER",t[t.TEXTURE_BARRIER=17]="TEXTURE_BARRIER",t[t.BUFFER_BARRIER=18]="BUFFER_BARRIER",t[t.COUNT=19]="COUNT"}(n||t("aT",n={})),t("b8",r),function(t){t[t.UNREADY=0]="UNREADY",t[t.FAILED=1]="FAILED",t[t.SUCCESS=2]="SUCCESS"}(r||t("b8",r={})),t("r",o),function(t){t[t.UNKNOWN=0]="UNKNOWN",t[t.GLES2=1]="GLES2",t[t.GLES3=2]="GLES3",t[t.METAL=3]="METAL",t[t.VULKAN=4]="VULKAN",t[t.NVN=5]="NVN",t[t.WEBGL=6]="WEBGL",t[t.WEBGL2=7]="WEBGL2",t[t.WEBGPU=8]="WEBGPU"}(o||t("r",o={})),t("bc",u),function(t){t[t.IDENTITY=0]="IDENTITY",t[t.ROTATE_90=1]="ROTATE_90",t[t.ROTATE_180=2]="ROTATE_180",t[t.ROTATE_270=3]="ROTATE_270"}(u||t("bc",u={})),t("a7",h),function(t){t[t.ELEMENT_INDEX_UINT=0]="ELEMENT_INDEX_UINT",t[t.INSTANCED_ARRAYS=1]="INSTANCED_ARRAYS",t[t.MULTIPLE_RENDER_TARGETS=2]="MULTIPLE_RENDER_TARGETS",t[t.BLEND_MINMAX=3]="BLEND_MINMAX",t[t.COMPUTE_SHADER=4]="COMPUTE_SHADER",t[t.INPUT_ATTACHMENT_BENEFIT=5]="INPUT_ATTACHMENT_BENEFIT",t[t.SUBPASS_COLOR_INPUT=6]="SUBPASS_COLOR_INPUT",t[t.SUBPASS_DEPTH_STENCIL_INPUT=7]="SUBPASS_DEPTH_STENCIL_INPUT",t[t.RASTERIZATION_ORDER_NOCOHERENT=8]="RASTERIZATION_ORDER_NOCOHERENT",t[t.MULTI_SAMPLE_RESOLVE_DEPTH_STENCIL=9]="MULTI_SAMPLE_RESOLVE_DEPTH_STENCIL",t[t.COUNT=10]="COUNT"}(h||t("a7",h={})),t("F",a),function(t){t[t.UNKNOWN=0]="UNKNOWN",t[t.A8=1]="A8",t[t.L8=2]="L8",t[t.LA8=3]="LA8",t[t.R8=4]="R8",t[t.R8SN=5]="R8SN",t[t.R8UI=6]="R8UI",t[t.R8I=7]="R8I",t[t.R16F=8]="R16F",t[t.R16UI=9]="R16UI",t[t.R16I=10]="R16I",t[t.R32F=11]="R32F",t[t.R32UI=12]="R32UI",t[t.R32I=13]="R32I",t[t.RG8=14]="RG8",t[t.RG8SN=15]="RG8SN",t[t.RG8UI=16]="RG8UI",t[t.RG8I=17]="RG8I",t[t.RG16F=18]="RG16F",t[t.RG16UI=19]="RG16UI",t[t.RG16I=20]="RG16I",t[t.RG32F=21]="RG32F",t[t.RG32UI=22]="RG32UI",t[t.RG32I=23]="RG32I",t[t.RGB8=24]="RGB8",t[t.SRGB8=25]="SRGB8",t[t.RGB8SN=26]="RGB8SN",t[t.RGB8UI=27]="RGB8UI",t[t.RGB8I=28]="RGB8I",t[t.RGB16F=29]="RGB16F",t[t.RGB16UI=30]="RGB16UI",t[t.RGB16I=31]="RGB16I",t[t.RGB32F=32]="RGB32F",t[t.RGB32UI=33]="RGB32UI",t[t.RGB32I=34]="RGB32I",t[t.RGBA8=35]="RGBA8",t[t.BGRA8=36]="BGRA8",t[t.SRGB8_A8=37]="SRGB8_A8",t[t.RGBA8SN=38]="RGBA8SN",t[t.RGBA8UI=39]="RGBA8UI",t[t.RGBA8I=40]="RGBA8I",t[t.RGBA16F=41]="RGBA16F",t[t.RGBA16UI=42]="RGBA16UI",t[t.RGBA16I=43]="RGBA16I",t[t.RGBA32F=44]="RGBA32F",t[t.RGBA32UI=45]="RGBA32UI",t[t.RGBA32I=46]="RGBA32I",t[t.R5G6B5=47]="R5G6B5",t[t.R11G11B10F=48]="R11G11B10F",t[t.RGB5A1=49]="RGB5A1",t[t.RGBA4=50]="RGBA4",t[t.RGB10A2=51]="RGB10A2",t[t.RGB10A2UI=52]="RGB10A2UI",t[t.RGB9E5=53]="RGB9E5",t[t.DEPTH=54]="DEPTH",t[t.DEPTH_STENCIL=55]="DEPTH_STENCIL",t[t.BC1=56]="BC1",t[t.BC1_ALPHA=57]="BC1_ALPHA",t[t.BC1_SRGB=58]="BC1_SRGB",t[t.BC1_SRGB_ALPHA=59]="BC1_SRGB_ALPHA",t[t.BC2=60]="BC2",t[t.BC2_SRGB=61]="BC2_SRGB",t[t.BC3=62]="BC3",t[t.BC3_SRGB=63]="BC3_SRGB",t[t.BC4=64]="BC4",t[t.BC4_SNORM=65]="BC4_SNORM",t[t.BC5=66]="BC5",t[t.BC5_SNORM=67]="BC5_SNORM",t[t.BC6H_UF16=68]="BC6H_UF16",t[t.BC6H_SF16=69]="BC6H_SF16",t[t.BC7=70]="BC7",t[t.BC7_SRGB=71]="BC7_SRGB",t[t.ETC_RGB8=72]="ETC_RGB8",t[t.ETC2_RGB8=73]="ETC2_RGB8",t[t.ETC2_SRGB8=74]="ETC2_SRGB8",t[t.ETC2_RGB8_A1=75]="ETC2_RGB8_A1",t[t.ETC2_SRGB8_A1=76]="ETC2_SRGB8_A1",t[t.ETC2_RGBA8=77]="ETC2_RGBA8",t[t.ETC2_SRGB8_A8=78]="ETC2_SRGB8_A8",t[t.EAC_R11=79]="EAC_R11",t[t.EAC_R11SN=80]="EAC_R11SN",t[t.EAC_RG11=81]="EAC_RG11",t[t.EAC_RG11SN=82]="EAC_RG11SN",t[t.PVRTC_RGB2=83]="PVRTC_RGB2",t[t.PVRTC_RGBA2=84]="PVRTC_RGBA2",t[t.PVRTC_RGB4=85]="PVRTC_RGB4",t[t.PVRTC_RGBA4=86]="PVRTC_RGBA4",t[t.PVRTC2_2BPP=87]="PVRTC2_2BPP",t[t.PVRTC2_4BPP=88]="PVRTC2_4BPP",t[t.ASTC_RGBA_4X4=89]="ASTC_RGBA_4X4",t[t.ASTC_RGBA_5X4=90]="ASTC_RGBA_5X4",t[t.ASTC_RGBA_5X5=91]="ASTC_RGBA_5X5",t[t.ASTC_RGBA_6X5=92]="ASTC_RGBA_6X5",t[t.ASTC_RGBA_6X6=93]="ASTC_RGBA_6X6",t[t.ASTC_RGBA_8X5=94]="ASTC_RGBA_8X5",t[t.ASTC_RGBA_8X6=95]="ASTC_RGBA_8X6",t[t.ASTC_RGBA_8X8=96]="ASTC_RGBA_8X8",t[t.ASTC_RGBA_10X5=97]="ASTC_RGBA_10X5",t[t.ASTC_RGBA_10X6=98]="ASTC_RGBA_10X6",t[t.ASTC_RGBA_10X8=99]="ASTC_RGBA_10X8",t[t.ASTC_RGBA_10X10=100]="ASTC_RGBA_10X10",t[t.ASTC_RGBA_12X10=101]="ASTC_RGBA_12X10",t[t.ASTC_RGBA_12X12=102]="ASTC_RGBA_12X12",t[t.ASTC_SRGBA_4X4=103]="ASTC_SRGBA_4X4",t[t.ASTC_SRGBA_5X4=104]="ASTC_SRGBA_5X4",t[t.ASTC_SRGBA_5X5=105]="ASTC_SRGBA_5X5",t[t.ASTC_SRGBA_6X5=106]="ASTC_SRGBA_6X5",t[t.ASTC_SRGBA_6X6=107]="ASTC_SRGBA_6X6",t[t.ASTC_SRGBA_8X5=108]="ASTC_SRGBA_8X5",t[t.ASTC_SRGBA_8X6=109]="ASTC_SRGBA_8X6",t[t.ASTC_SRGBA_8X8=110]="ASTC_SRGBA_8X8",t[t.ASTC_SRGBA_10X5=111]="ASTC_SRGBA_10X5",t[t.ASTC_SRGBA_10X6=112]="ASTC_SRGBA_10X6",t[t.ASTC_SRGBA_10X8=113]="ASTC_SRGBA_10X8",t[t.ASTC_SRGBA_10X10=114]="ASTC_SRGBA_10X10",t[t.ASTC_SRGBA_12X10=115]="ASTC_SRGBA_12X10",t[t.ASTC_SRGBA_12X12=116]="ASTC_SRGBA_12X12",t[t.COUNT=117]="COUNT"}(a||t("F",a={})),t("aa",c),function(t){t[t.NONE=0]="NONE",t[t.UNORM=1]="UNORM",t[t.SNORM=2]="SNORM",t[t.UINT=3]="UINT",t[t.INT=4]="INT",t[t.UFLOAT=5]="UFLOAT",t[t.FLOAT=6]="FLOAT"}(c||t("aa",c={})),t("b3",R),function(t){t[t.FLOAT=0]="FLOAT",t[t.UNFILTERABLE_FLOAT=1]="UNFILTERABLE_FLOAT",t[t.SINT=2]="SINT",t[t.UINT=3]="UINT"}(R||t("b3",R={})),t("j",_),function(t){t[t.UNKNOWN=0]="UNKNOWN",t[t.BOOL=1]="BOOL",t[t.BOOL2=2]="BOOL2",t[t.BOOL3=3]="BOOL3",t[t.BOOL4=4]="BOOL4",t[t.INT=5]="INT",t[t.INT2=6]="INT2",t[t.INT3=7]="INT3",t[t.INT4=8]="INT4",t[t.UINT=9]="UINT",t[t.UINT2=10]="UINT2",t[t.UINT3=11]="UINT3",t[t.UINT4=12]="UINT4",t[t.FLOAT=13]="FLOAT",t[t.FLOAT2=14]="FLOAT2",t[t.FLOAT3=15]="FLOAT3",t[t.FLOAT4=16]="FLOAT4",t[t.MAT2=17]="MAT2",t[t.MAT2X3=18]="MAT2X3",t[t.MAT2X4=19]="MAT2X4",t[t.MAT3X2=20]="MAT3X2",t[t.MAT3=21]="MAT3",t[t.MAT3X4=22]="MAT3X4",t[t.MAT4X2=23]="MAT4X2",t[t.MAT4X3=24]="MAT4X3",t[t.MAT4=25]="MAT4",t[t.SAMPLER1D=26]="SAMPLER1D",t[t.SAMPLER1D_ARRAY=27]="SAMPLER1D_ARRAY",t[t.SAMPLER2D=28]="SAMPLER2D",t[t.SAMPLER2D_ARRAY=29]="SAMPLER2D_ARRAY",t[t.SAMPLER3D=30]="SAMPLER3D",t[t.SAMPLER_CUBE=31]="SAMPLER_CUBE",t[t.SAMPLER=32]="SAMPLER",t[t.TEXTURE1D=33]="TEXTURE1D",t[t.TEXTURE1D_ARRAY=34]="TEXTURE1D_ARRAY",t[t.TEXTURE2D=35]="TEXTURE2D",t[t.TEXTURE2D_ARRAY=36]="TEXTURE2D_ARRAY",t[t.TEXTURE3D=37]="TEXTURE3D",t[t.TEXTURE_CUBE=38]="TEXTURE_CUBE",t[t.IMAGE1D=39]="IMAGE1D",t[t.IMAGE1D_ARRAY=40]="IMAGE1D_ARRAY",t[t.IMAGE2D=41]="IMAGE2D",t[t.IMAGE2D_ARRAY=42]="IMAGE2D_ARRAY",t[t.IMAGE3D=43]="IMAGE3D",t[t.IMAGE_CUBE=44]="IMAGE_CUBE",t[t.SUBPASS_INPUT=45]="SUBPASS_INPUT",t[t.COUNT=46]="COUNT"}(_||t("j",_={})),t("b",A),function(t){t[t.NONE=0]="NONE",t[t.TRANSFER_SRC=1]="TRANSFER_SRC",t[t.TRANSFER_DST=2]="TRANSFER_DST",t[t.INDEX=4]="INDEX",t[t.VERTEX=8]="VERTEX",t[t.UNIFORM=16]="UNIFORM",t[t.STORAGE=32]="STORAGE",t[t.INDIRECT=64]="INDIRECT"}(A||t("b",A={})),t("an",T),function(t){t[t.NONE=0]="NONE",t[t.ENABLE_STAGING_WRITE=1]="ENABLE_STAGING_WRITE"}(T||t("an",T={})),t("l",E),function(t){t[t.NONE=0]="NONE",t[t.READ_ONLY=1]="READ_ONLY",t[t.WRITE_ONLY=2]="WRITE_ONLY",t[t.READ_WRITE=3]="READ_WRITE"}(E||t("l",E={})),t("M",d),function(t){t[t.NONE=0]="NONE",t[t.DEVICE=1]="DEVICE",t[t.HOST=2]="HOST"}(d||t("M",d={})),t("d",f),function(t){t[t.TEX1D=0]="TEX1D",t[t.TEX2D=1]="TEX2D",t[t.TEX3D=2]="TEX3D",t[t.CUBE=3]="CUBE",t[t.TEX1D_ARRAY=4]="TEX1D_ARRAY",t[t.TEX2D_ARRAY=5]="TEX2D_ARRAY"}(f||t("d",f={})),t("V",S),function(t){t[t.UNKNOWN=0]="UNKNOWN",t[t.BUFFER=1]="BUFFER",t[t.TEX1D=2]="TEX1D",t[t.TEX1D_ARRAY=3]="TEX1D_ARRAY",t[t.TEX2D=4]="TEX2D",t[t.TEX2D_ARRAY=5]="TEX2D_ARRAY",t[t.TEX2DMS=6]="TEX2DMS",t[t.TEX2DMS_ARRAY=7]="TEX2DMS_ARRAY",t[t.TEX3D=8]="TEX3D",t[t.TEXCUBE=9]="TEXCUBE",t[t.TEXCUBE_ARRAY=10]="TEXCUBE_ARRAY",t[t.RAYTRACING_ACCELERATION_STRUCTURE=11]="RAYTRACING_ACCELERATION_STRUCTURE"}(S||t("V",S={})),t("e",p),function(t){t[t.NONE=0]="NONE",t[t.TRANSFER_SRC=1]="TRANSFER_SRC",t[t.TRANSFER_DST=2]="TRANSFER_DST",t[t.SAMPLED=4]="SAMPLED",t[t.STORAGE=8]="STORAGE",t[t.COLOR_ATTACHMENT=16]="COLOR_ATTACHMENT",t[t.DEPTH_STENCIL_ATTACHMENT=32]="DEPTH_STENCIL_ATTACHMENT",t[t.INPUT_ATTACHMENT=64]="INPUT_ATTACHMENT",t[t.SHADING_RATE=128]="SHADING_RATE"}(p||t("e",p={})),t("p",C),function(t){t[t.NONE=0]="NONE",t[t.GEN_MIPMAP=1]="GEN_MIPMAP",t[t.GENERAL_LAYOUT=2]="GENERAL_LAYOUT",t[t.EXTERNAL_OES=4]="EXTERNAL_OES",t[t.EXTERNAL_NORMAL=8]="EXTERNAL_NORMAL",t[t.LAZILY_ALLOCATED=16]="LAZILY_ALLOCATED",t[t.MUTABLE_VIEW_FORMAT=64]="MUTABLE_VIEW_FORMAT",t[t.MUTABLE_STORAGE=128]="MUTABLE_STORAGE"}(C||t("p",C={})),t("o",N),function(t){t[t.NONE=0]="NONE",t[t.RENDER_TARGET=1]="RENDER_TARGET",t[t.SAMPLED_TEXTURE=2]="SAMPLED_TEXTURE",t[t.LINEAR_FILTER=4]="LINEAR_FILTER",t[t.STORAGE_TEXTURE=8]="STORAGE_TEXTURE",t[t.VERTEX_ATTRIBUTE=16]="VERTEX_ATTRIBUTE",t[t.SHADING_RATE=32]="SHADING_RATE"}(N||t("o",N={})),t("q",l),function(t){t[t.X1=1]="X1",t[t.X2=2]="X2",t[t.X4=4]="X4",t[t.X8=8]="X8",t[t.X16=16]="X16",t[t.X32=32]="X32",t[t.X64=64]="X64"}(l||t("q",l={})),t("bp",B),function(t){t[t.OFF=0]="OFF",t[t.ON=1]="ON",t[t.RELAXED=2]="RELAXED",t[t.MAILBOX=3]="MAILBOX",t[t.HALF=4]="HALF"}(B||t("bp",B={})),t("v",I),function(t){t[t.NONE=0]="NONE",t[t.POINT=1]="POINT",t[t.LINEAR=2]="LINEAR",t[t.ANISOTROPIC=3]="ANISOTROPIC"}(I||t("v",I={})),t("ae",O),function(t){t[t.WRAP=0]="WRAP",t[t.MIRROR=1]="MIRROR",t[t.CLAMP=2]="CLAMP",t[t.BORDER=3]="BORDER"}(O||t("ae",O={})),t("ag",v),function(t){t[t.NEVER=0]="NEVER",t[t.LESS=1]="LESS",t[t.EQUAL=2]="EQUAL",t[t.LESS_EQUAL=3]="LESS_EQUAL",t[t.GREATER=4]="GREATER",t[t.NOT_EQUAL=5]="NOT_EQUAL",t[t.GREATER_EQUAL=6]="GREATER_EQUAL",t[t.ALWAYS=7]="ALWAYS"}(v||t("ag",v={})),t("ah",y),function(t){t[t.ZERO=0]="ZERO",t[t.KEEP=1]="KEEP",t[t.REPLACE=2]="REPLACE",t[t.INCR=3]="INCR",t[t.DECR=4]="DECR",t[t.INVERT=5]="INVERT",t[t.INCR_WRAP=6]="INCR_WRAP",t[t.DECR_WRAP=7]="DECR_WRAP"}(y||t("ah",y={})),t("af",G),function(t){t[t.ZERO=0]="ZERO",t[t.ONE=1]="ONE",t[t.SRC_ALPHA=2]="SRC_ALPHA",t[t.DST_ALPHA=3]="DST_ALPHA",t[t.ONE_MINUS_SRC_ALPHA=4]="ONE_MINUS_SRC_ALPHA",t[t.ONE_MINUS_DST_ALPHA=5]="ONE_MINUS_DST_ALPHA",t[t.SRC_COLOR=6]="SRC_COLOR",t[t.DST_COLOR=7]="DST_COLOR",t[t.ONE_MINUS_SRC_COLOR=8]="ONE_MINUS_SRC_COLOR",t[t.ONE_MINUS_DST_COLOR=9]="ONE_MINUS_DST_COLOR",t[t.SRC_ALPHA_SATURATE=10]="SRC_ALPHA_SATURATE",t[t.CONSTANT_COLOR=11]="CONSTANT_COLOR",t[t.ONE_MINUS_CONSTANT_COLOR=12]="ONE_MINUS_CONSTANT_COLOR",t[t.CONSTANT_ALPHA=13]="CONSTANT_ALPHA",t[t.ONE_MINUS_CONSTANT_ALPHA=14]="ONE_MINUS_CONSTANT_ALPHA"}(G||t("af",G={})),t("ai",m),function(t){t[t.ADD=0]="ADD",t[t.SUB=1]="SUB",t[t.REV_SUB=2]="REV_SUB",t[t.MIN=3]="MIN",t[t.MAX=4]="MAX"}(m||t("ai",m={})),t("aj",L),function(t){t[t.NONE=0]="NONE",t[t.R=1]="R",t[t.G=2]="G",t[t.B=4]="B",t[t.A=8]="A",t[t.ALL=15]="ALL"}(L||t("aj",L={})),t("S",U),function(t){t[t.NONE=0]="NONE",t[t.VERTEX=1]="VERTEX",t[t.CONTROL=2]="CONTROL",t[t.EVALUATION=4]="EVALUATION",t[t.GEOMETRY=8]="GEOMETRY",t[t.FRAGMENT=16]="FRAGMENT",t[t.COMPUTE=32]="COMPUTE",t[t.ALL=63]="ALL"}(U||t("S",U={})),t("aQ",g),function(t){t[t.LOAD=0]="LOAD",t[t.CLEAR=1]="CLEAR",t[t.DISCARD=2]="DISCARD"}(g||t("aQ",g={})),t("y",x),function(t){t[t.STORE=0]="STORE",t[t.DISCARD=1]="DISCARD"}(x||t("y",x={})),t("ak",D),function(t){t[t.NONE=0]="NONE",t[t.INDIRECT_BUFFER=1]="INDIRECT_BUFFER",t[t.INDEX_BUFFER=2]="INDEX_BUFFER",t[t.VERTEX_BUFFER=4]="VERTEX_BUFFER",t[t.VERTEX_SHADER_READ_UNIFORM_BUFFER=8]="VERTEX_SHADER_READ_UNIFORM_BUFFER",t[t.VERTEX_SHADER_READ_TEXTURE=16]="VERTEX_SHADER_READ_TEXTURE",t[t.VERTEX_SHADER_READ_OTHER=32]="VERTEX_SHADER_READ_OTHER",t[t.FRAGMENT_SHADER_READ_UNIFORM_BUFFER=64]="FRAGMENT_SHADER_READ_UNIFORM_BUFFER",t[t.FRAGMENT_SHADER_READ_TEXTURE=128]="FRAGMENT_SHADER_READ_TEXTURE",t[t.FRAGMENT_SHADER_READ_COLOR_INPUT_ATTACHMENT=256]="FRAGMENT_SHADER_READ_COLOR_INPUT_ATTACHMENT",t[t.FRAGMENT_SHADER_READ_DEPTH_STENCIL_INPUT_ATTACHMENT=512]="FRAGMENT_SHADER_READ_DEPTH_STENCIL_INPUT_ATTACHMENT",t[t.FRAGMENT_SHADER_READ_OTHER=1024]="FRAGMENT_SHADER_READ_OTHER",t[t.COLOR_ATTACHMENT_READ=2048]="COLOR_ATTACHMENT_READ",t[t.DEPTH_STENCIL_ATTACHMENT_READ=4096]="DEPTH_STENCIL_ATTACHMENT_READ",t[t.COMPUTE_SHADER_READ_UNIFORM_BUFFER=8192]="COMPUTE_SHADER_READ_UNIFORM_BUFFER",t[t.COMPUTE_SHADER_READ_TEXTURE=16384]="COMPUTE_SHADER_READ_TEXTURE",t[t.COMPUTE_SHADER_READ_OTHER=32768]="COMPUTE_SHADER_READ_OTHER",t[t.TRANSFER_READ=65536]="TRANSFER_READ",t[t.HOST_READ=131072]="HOST_READ",t[t.PRESENT=262144]="PRESENT",t[t.VERTEX_SHADER_WRITE=524288]="VERTEX_SHADER_WRITE",t[t.FRAGMENT_SHADER_WRITE=1048576]="FRAGMENT_SHADER_WRITE",t[t.COLOR_ATTACHMENT_WRITE=2097152]="COLOR_ATTACHMENT_WRITE",t[t.DEPTH_STENCIL_ATTACHMENT_WRITE=4194304]="DEPTH_STENCIL_ATTACHMENT_WRITE",t[t.COMPUTE_SHADER_WRITE=8388608]="COMPUTE_SHADER_WRITE",t[t.TRANSFER_WRITE=16777216]="TRANSFER_WRITE",t[t.HOST_PREINITIALIZED=33554432]="HOST_PREINITIALIZED",t[t.HOST_WRITE=67108864]="HOST_WRITE",t[t.SHADING_RATE=134217728]="SHADING_RATE"}(D||t("ak",D={})),t("b1",M),function(t){t[t.NONE=0]="NONE",t[t.SAMPLE_ZERO=1]="SAMPLE_ZERO",t[t.AVERAGE=2]="AVERAGE",t[t.MIN=3]="MIN",t[t.MAX=4]="MAX"}(M||t("b1",M={})),t("aW",P),function(t){t[t.GRAPHICS=0]="GRAPHICS",t[t.COMPUTE=1]="COMPUTE",t[t.RAY_TRACING=2]="RAY_TRACING"}(P||t("aW",P={})),t("P",F),function(t){t[t.POINT_LIST=0]="POINT_LIST",t[t.LINE_LIST=1]="LINE_LIST",t[t.LINE_STRIP=2]="LINE_STRIP",t[t.LINE_LOOP=3]="LINE_LOOP",t[t.LINE_LIST_ADJACENCY=4]="LINE_LIST_ADJACENCY",t[t.LINE_STRIP_ADJACENCY=5]="LINE_STRIP_ADJACENCY",t[t.ISO_LINE_LIST=6]="ISO_LINE_LIST",t[t.TRIANGLE_LIST=7]="TRIANGLE_LIST",t[t.TRIANGLE_STRIP=8]="TRIANGLE_STRIP",t[t.TRIANGLE_FAN=9]="TRIANGLE_FAN",t[t.TRIANGLE_LIST_ADJACENCY=10]="TRIANGLE_LIST_ADJACENCY",t[t.TRIANGLE_STRIP_ADJACENCY=11]="TRIANGLE_STRIP_ADJACENCY",t[t.TRIANGLE_PATCH_ADJACENCY=12]="TRIANGLE_PATCH_ADJACENCY",t[t.QUAD_PATCH_LIST=13]="QUAD_PATCH_LIST"}(F||t("P",F={})),t("aY",b),function(t){t[t.FILL=0]="FILL",t[t.POINT=1]="POINT",t[t.LINE=2]="LINE"}(b||t("aY",b={})),t("b4",X),function(t){t[t.GOURAND=0]="GOURAND",t[t.FLAT=1]="FLAT"}(X||t("b4",X={})),t("ar",w),function(t){t[t.NONE=0]="NONE",t[t.FRONT=1]="FRONT",t[t.BACK=2]="BACK"}(w||t("ar",w={})),t("aD",H),function(t){t[t.NONE=0]="NONE",t[t.LINE_WIDTH=1]="LINE_WIDTH",t[t.DEPTH_BIAS=2]="DEPTH_BIAS",t[t.BLEND_CONSTANTS=4]="BLEND_CONSTANTS",t[t.DEPTH_BOUNDS=8]="DEPTH_BOUNDS",t[t.STENCIL_WRITE_MASK=16]="STENCIL_WRITE_MASK",t[t.STENCIL_COMPARE_MASK=32]="STENCIL_COMPARE_MASK"}(H||t("aD",H={})),t("b9",k),function(t){t[t.FRONT=1]="FRONT",t[t.BACK=2]="BACK",t[t.ALL=3]="ALL"}(k||t("b9",k={})),t("h",W),function(t){t[t.UNKNOWN=0]="UNKNOWN",t[t.UNIFORM_BUFFER=1]="UNIFORM_BUFFER",t[t.DYNAMIC_UNIFORM_BUFFER=2]="DYNAMIC_UNIFORM_BUFFER",t[t.STORAGE_BUFFER=4]="STORAGE_BUFFER",t[t.DYNAMIC_STORAGE_BUFFER=8]="DYNAMIC_STORAGE_BUFFER",t[t.SAMPLER_TEXTURE=16]="SAMPLER_TEXTURE",t[t.SAMPLER=32]="SAMPLER",t[t.TEXTURE=64]="TEXTURE",t[t.STORAGE_IMAGE=128]="STORAGE_IMAGE",t[t.INPUT_ATTACHMENT=256]="INPUT_ATTACHMENT"}(W||t("h",W={})),t("b0",V),function(t){t[t.GRAPHICS=0]="GRAPHICS",t[t.COMPUTE=1]="COMPUTE",t[t.TRANSFER=2]="TRANSFER"}(V||t("b0",V={})),t("a_",Y),function(t){t[t.OCCLUSION=0]="OCCLUSION",t[t.PIPELINE_STATISTICS=1]="PIPELINE_STATISTICS",t[t.TIMESTAMP=2]="TIMESTAMP"}(Y||t("a_",Y={})),t("aq",z),function(t){t[t.PRIMARY=0]="PRIMARY",t[t.SECONDARY=1]="SECONDARY"}(z||t("aq",z={})),t("C",K),function(t){t[t.NONE=0]="NONE",t[t.COLOR=1]="COLOR",t[t.DEPTH=2]="DEPTH",t[t.STENCIL=4]="STENCIL",t[t.DEPTH_STENCIL=6]="DEPTH_STENCIL",t[t.ALL=7]="ALL"}(K||t("C",K={})),t("al",Q),function(t){t[t.FULL=0]="FULL",t[t.SPLIT_BEGIN=1]="SPLIT_BEGIN",t[t.SPLIT_END=2]="SPLIT_END"}(Q||t("al",Q={})),t("aV",Z),function(t){t[t.RASTER=0]="RASTER",t[t.COMPUTE=1]="COMPUTE",t[t.COPY=2]="COPY",t[t.MOVE=3]="MOVE",t[t.RAYTRACE=4]="RAYTRACE",t[t.PRESENT=5]="PRESENT"}(Z||t("aV",Z={}));var q,J=t("b7",function(){function t(t,e,i){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=0),this.x=t,this.y=e,this.z=i}return t.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this.z=t.z,this},t}()),$=t("aA",function(){function t(t,e,i,s,n,r,o,u,h,a,c,R,_,A,T,E,d,f,S,p,C,N,l,B,I,O){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=0),void 0===s&&(s=0),void 0===n&&(n=0),void 0===r&&(r=0),void 0===o&&(o=0),void 0===u&&(u=0),void 0===h&&(h=0),void 0===a&&(a=0),void 0===c&&(c=0),void 0===R&&(R=0),void 0===_&&(_=0),void 0===A&&(A=0),void 0===T&&(T=0),void 0===E&&(E=1),void 0===d&&(d=0),void 0===f&&(f=0),void 0===S&&(S=new J),void 0===p&&(p=new J),void 0===C&&(C=!1),void 0===N&&(N=!1),void 0===l&&(l=!1),void 0===B&&(B=-1),void 0===I&&(I=1),void 0===O&&(O=1),this.maxVertexAttributes=t,this.maxVertexUniformVectors=e,this.maxFragmentUniformVectors=i,this.maxTextureUnits=s,this.maxImageUnits=n,this.maxVertexTextureUnits=r,this.maxColorRenderTargets=o,this.maxShaderStorageBufferBindings=u,this.maxShaderStorageBlockSize=h,this.maxUniformBufferBindings=a,this.maxUniformBlockSize=c,this.maxTextureSize=R,this.maxCubeMapTextureSize=_,this.maxArrayTextureLayers=A,this.max3DTextureSize=T,this.uboOffsetAlignment=E,this.maxComputeSharedMemorySize=d,this.maxComputeWorkGroupInvocations=f,this.maxComputeWorkGroupSize=S,this.maxComputeWorkGroupCount=p,this.supportQuery=C,this.supportVariableRateShading=N,this.supportSubPassShading=l,this.clipSpaceMinZ=B,this.screenSpaceSignY=I,this.clipSpaceSignY=O}return t.prototype.copy=function(t){return this.maxVertexAttributes=t.maxVertexAttributes,this.maxVertexUniformVectors=t.maxVertexUniformVectors,this.maxFragmentUniformVectors=t.maxFragmentUniformVectors,this.maxTextureUnits=t.maxTextureUnits,this.maxImageUnits=t.maxImageUnits,this.maxVertexTextureUnits=t.maxVertexTextureUnits,this.maxColorRenderTargets=t.maxColorRenderTargets,this.maxShaderStorageBufferBindings=t.maxShaderStorageBufferBindings,this.maxShaderStorageBlockSize=t.maxShaderStorageBlockSize,this.maxUniformBufferBindings=t.maxUniformBufferBindings,this.maxUniformBlockSize=t.maxUniformBlockSize,this.maxTextureSize=t.maxTextureSize,this.maxCubeMapTextureSize=t.maxCubeMapTextureSize,this.maxArrayTextureLayers=t.maxArrayTextureLayers,this.max3DTextureSize=t.max3DTextureSize,this.uboOffsetAlignment=t.uboOffsetAlignment,this.maxComputeSharedMemorySize=t.maxComputeSharedMemorySize,this.maxComputeWorkGroupInvocations=t.maxComputeWorkGroupInvocations,this.maxComputeWorkGroupSize.copy(t.maxComputeWorkGroupSize),this.maxComputeWorkGroupCount.copy(t.maxComputeWorkGroupCount),this.supportQuery=t.supportQuery,this.supportVariableRateShading=t.supportVariableRateShading,this.supportSubPassShading=t.supportSubPassShading,this.clipSpaceMinZ=t.clipSpaceMinZ,this.screenSpaceSignY=t.screenSpaceSignY,this.clipSpaceSignY=t.clipSpaceSignY,this},t}()),tt=t("aB",function(){function t(t){void 0===t&&(t=!0),this.enableBarrierDeduce=t}return t.prototype.copy=function(t){return this.enableBarrierDeduce=t.enableBarrierDeduce,this},t}()),et=t("aU",function(){function t(t,e,i){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=0),this.x=t,this.y=e,this.z=i}return t.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this.z=t.z,this},t}()),it=t("ac",function(){function t(t,e,i,s){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=0),void 0===s&&(s=0),this.x=t,this.y=e,this.width=i,this.height=s}return t.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height,this},t}()),st=t("aG",function(){function t(t,e,i){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=1),this.width=t,this.height=e,this.depth=i}return t.prototype.copy=function(t){return this.width=t.width,this.height=t.height,this.depth=t.depth,this},t}()),nt=t("bg",function(){function t(t,e,i){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=1),this.mipLevel=t,this.baseArrayLayer=e,this.layerCount=i}return t.prototype.copy=function(t){return this.mipLevel=t.mipLevel,this.baseArrayLayer=t.baseArrayLayer,this.layerCount=t.layerCount,this},t}()),rt=t("bh",function(){function t(t,e,i,s){void 0===t&&(t=0),void 0===e&&(e=1),void 0===i&&(i=0),void 0===s&&(s=1),this.baseMipLevel=t,this.levelCount=e,this.baseArrayLayer=i,this.layerCount=s}return t.prototype.copy=function(t){return this.baseMipLevel=t.baseMipLevel,this.levelCount=t.levelCount,this.baseArrayLayer=t.baseArrayLayer,this.layerCount=t.layerCount,this},t}()),ot=t("bf",function(){function t(t,e,i,s,n){void 0===t&&(t=new nt),void 0===e&&(e=new et),void 0===i&&(i=new nt),void 0===s&&(s=new et),void 0===n&&(n=new st),this.srcSubres=t,this.srcOffset=e,this.dstSubres=i,this.dstOffset=s,this.extent=n}return t.prototype.copy=function(t){return this.srcSubres.copy(t.srcSubres),this.srcOffset.copy(t.srcOffset),this.dstSubres.copy(t.dstSubres),this.dstOffset.copy(t.dstOffset),this.extent.copy(t.extent),this},t}()),ut=t("be",function(){function t(t,e,i,s,n,r){void 0===t&&(t=new nt),void 0===e&&(e=new et),void 0===i&&(i=new st),void 0===s&&(s=new nt),void 0===n&&(n=new et),void 0===r&&(r=new st),this.srcSubres=t,this.srcOffset=e,this.srcExtent=i,this.dstSubres=s,this.dstOffset=n,this.dstExtent=r}return t.prototype.copy=function(t){return this.srcSubres.copy(t.srcSubres),this.srcOffset.copy(t.srcOffset),this.srcExtent.copy(t.srcExtent),this.dstSubres.copy(t.dstSubres),this.dstOffset.copy(t.dstOffset),this.dstExtent.copy(t.dstExtent),this},t}()),ht=t("f",function(){function t(t,e,i,s,n,r){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=0),void 0===s&&(s=new et),void 0===n&&(n=new st),void 0===r&&(r=new nt),this.buffOffset=t,this.buffStride=e,this.buffTexHeight=i,this.texOffset=s,this.texExtent=n,this.texSubres=r}return t.prototype.copy=function(t){return this.buffOffset=t.buffOffset,this.buffStride=t.buffStride,this.buffTexHeight=t.buffTexHeight,this.texOffset.copy(t.texOffset),this.texExtent.copy(t.texExtent),this.texSubres.copy(t.texSubres),this},t}()),at=t("bo",function(){function t(t,e,i,s,n,r){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=0),void 0===s&&(s=0),void 0===n&&(n=0),void 0===r&&(r=1),this.left=t,this.top=e,this.width=i,this.height=s,this.minDepth=n,this.maxDepth=r}var e=t.prototype;return e.copy=function(t){return this.left=t.left,this.top=t.top,this.width=t.width,this.height=t.height,this.minDepth=t.minDepth,this.maxDepth=t.maxDepth,this},e.reset=function(){this.left=0,this.top=0,this.width=0,this.height=0,this.minDepth=0,this.maxDepth=1},t}()),ct=t("ab",function(){function t(t,e,i,s){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=0),void 0===s&&(s=0),this.x=t,this.y=e,this.z=i,this.w=s}var e=t.prototype;return e.copy=function(t){return this.x=t.x,this.y=t.y,this.z=t.z,this.w=t.w,this},e.set=function(t,e,i,s){return this.x=t,this.y=e,this.z=i,this.w=s,this},e.reset=function(){this.x=0,this.y=0,this.z=0,this.w=0},t}()),Rt=t("aR",function(){function t(t,e){void 0===t&&(t=""),void 0===e&&(e=new ct),this.name=t,this.color=e}return t.prototype.copy=function(t){return this.name=t.name,this.color.copy(t.color),this},t}()),_t=t("n",function(){function t(t,e,i,s,n,r,o,u){void 0===t&&(t=[0]),void 0===e&&(e=[0]),void 0===i&&(i=[0]),void 0===s&&(s=[0]),void 0===n&&(n=[0]),void 0===r&&(r=[0]),void 0===o&&(o=[0]),void 0===u&&(u=[0]),this.maxBlockCounts=t,this.maxSamplerTextureCounts=e,this.maxSamplerCounts=i,this.maxTextureCounts=s,this.maxBufferCounts=n,this.maxImageCounts=r,this.maxSubpassInputCounts=o,this.setIndices=u}return t.prototype.copy=function(t){return this.maxBlockCounts=t.maxBlockCounts.slice(),this.maxSamplerTextureCounts=t.maxSamplerTextureCounts.slice(),this.maxSamplerCounts=t.maxSamplerCounts.slice(),this.maxTextureCounts=t.maxTextureCounts.slice(),this.maxBufferCounts=t.maxBufferCounts.slice(),this.maxImageCounts=t.maxImageCounts.slice(),this.maxSubpassInputCounts=t.maxSubpassInputCounts.slice(),this.setIndices=t.setIndices.slice(),this},t}()),At=t("a8",function(){function t(t,e,i,s,n){void 0===t&&(t=0),void 0===e&&(e=null),void 0===i&&(i=B.ON),void 0===s&&(s=0),void 0===n&&(n=0),this.windowId=t,this.windowHandle=e,this.vsyncMode=i,this.width=s,this.height=n}return t.prototype.copy=function(t){return this.windowId=t.windowId,this.windowHandle=t.windowHandle,this.vsyncMode=t.vsyncMode,this.width=t.width,this.height=t.height,this},t}()),Tt=t("a9",function(){function t(t){void 0===t&&(t=new _t),this.bindingMappingInfo=t}return t.prototype.copy=function(t){return this.bindingMappingInfo.copy(t.bindingMappingInfo),this},t}()),Et=t("B",function(){function t(t,e,i,s,n){void 0===t&&(t=A.NONE),void 0===e&&(e=d.NONE),void 0===i&&(i=0),void 0===s&&(s=1),void 0===n&&(n=T.NONE),this.usage=t,this.memUsage=e,this.size=i,this.stride=s,this.flags=n}return t.prototype.copy=function(t){return this.usage=t.usage,this.memUsage=t.memUsage,this.size=t.size,this.stride=t.stride,this.flags=t.flags,this},t}()),dt=t("ao",function(){function t(t,e,i){void 0===t&&(t=null),void 0===e&&(e=0),void 0===i&&(i=0),this.buffer=t,this.offset=e,this.range=i}return t.prototype.copy=function(t){return this.buffer=t.buffer,this.offset=t.offset,this.range=t.range,this},t}()),ft=t("D",function(){function t(t,e,i,s,n,r,o){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=0),void 0===s&&(s=0),void 0===n&&(n=0),void 0===r&&(r=0),void 0===o&&(o=0),this.vertexCount=t,this.firstVertex=e,this.indexCount=i,this.firstIndex=s,this.vertexOffset=n,this.instanceCount=r,this.firstInstance=o}return t.prototype.copy=function(t){return this.vertexCount=t.vertexCount,this.firstVertex=t.firstVertex,this.indexCount=t.indexCount,this.firstIndex=t.firstIndex,this.vertexOffset=t.vertexOffset,this.instanceCount=t.instanceCount,this.firstInstance=t.firstInstance,this},t}()),St=t("aC",function(){function t(t,e,i,s,n){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=0),void 0===s&&(s=null),void 0===n&&(n=0),this.groupCountX=t,this.groupCountY=e,this.groupCountZ=i,this.indirectBuffer=s,this.indirectOffset=n}return t.prototype.copy=function(t){return this.groupCountX=t.groupCountX,this.groupCountY=t.groupCountY,this.groupCountZ=t.groupCountZ,this.indirectBuffer=t.indirectBuffer,this.indirectOffset=t.indirectOffset,this},t}()),pt=t("aO",function(){function t(t){void 0===t&&(t=[]),this.drawInfos=t}return t.prototype.copy=function(t){return j(this.drawInfos,t.drawInfos,ft),this},t}()),Ct=t("T",function(){function t(t,e,i,s,n,r,o,u,h,c,R){void 0===t&&(t=f.TEX2D),void 0===e&&(e=p.NONE),void 0===i&&(i=a.UNKNOWN),void 0===s&&(s=0),void 0===n&&(n=0),void 0===r&&(r=C.NONE),void 0===o&&(o=1),void 0===u&&(u=1),void 0===h&&(h=l.X1),void 0===c&&(c=1),void 0===R&&(R=0),this.type=t,this.usage=e,this.format=i,this.width=s,this.height=n,this.flags=r,this.layerCount=o,this.levelCount=u,this.samples=h,this.depth=c,this.externalRes=R}return t.prototype.copy=function(t){return this.type=t.type,this.usage=t.usage,this.format=t.format,this.width=t.width,this.height=t.height,this.flags=t.flags,this.layerCount=t.layerCount,this.levelCount=t.levelCount,this.samples=t.samples,this.depth=t.depth,this.externalRes=t.externalRes,this},t}()),Nt=t("bi",function(){function t(t,e,i,s,n,r,o,u,h){void 0===t&&(t=null),void 0===e&&(e=f.TEX2D),void 0===i&&(i=a.UNKNOWN),void 0===s&&(s=0),void 0===n&&(n=1),void 0===r&&(r=0),void 0===o&&(o=1),void 0===u&&(u=0),void 0===h&&(h=1),this.texture=t,this.type=e,this.format=i,this.baseLevel=s,this.levelCount=n,this.baseLayer=r,this.layerCount=o,this.basePlane=u,this.planeCount=h}return t.prototype.copy=function(t){return this.texture=t.texture,this.type=t.type,this.format=t.format,this.baseLevel=t.baseLevel,this.levelCount=t.levelCount,this.baseLayer=t.baseLayer,this.layerCount=t.layerCount,this.basePlane=t.basePlane,this.planeCount=t.planeCount,this},t}()),lt=t("ad",function(){function t(t,e,i,s,n,r,o,u){void 0===t&&(t=I.LINEAR),void 0===e&&(e=I.LINEAR),void 0===i&&(i=I.NONE),void 0===s&&(s=O.WRAP),void 0===n&&(n=O.WRAP),void 0===r&&(r=O.WRAP),void 0===o&&(o=0),void 0===u&&(u=v.ALWAYS),this.minFilter=t,this.magFilter=e,this.mipFilter=i,this.addressU=s,this.addressV=n,this.addressW=r,this.maxAnisotropy=o,this.cmpFunc=u}return t.prototype.copy=function(t){return this.minFilter=t.minFilter,this.magFilter=t.magFilter,this.mipFilter=t.mipFilter,this.addressU=t.addressU,this.addressV=t.addressV,this.addressW=t.addressW,this.maxAnisotropy=t.maxAnisotropy,this.cmpFunc=t.cmpFunc,this},t}()),Bt=t("i",function(){function t(t,e,i){void 0===t&&(t=""),void 0===e&&(e=_.UNKNOWN),void 0===i&&(i=0),this.name=t,this.type=e,this.count=i}return t.prototype.copy=function(t){return this.name=t.name,this.type=t.type,this.count=t.count,this},t}()),It=t("U",function(){function t(t,e,i,s,n,r){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=""),void 0===s&&(s=[]),void 0===n&&(n=0),void 0===r&&(r=0),this.set=t,this.binding=e,this.name=i,this.members=s,this.count=n,this.flattened=r}return t.prototype.copy=function(t){return this.set=t.set,this.binding=t.binding,this.name=t.name,j(this.members,t.members,Bt),this.count=t.count,this.flattened=t.flattened,this},t}()),Ot=t("k",function(){function t(t,e,i,s,n,r){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=""),void 0===s&&(s=_.UNKNOWN),void 0===n&&(n=0),void 0===r&&(r=0),this.set=t,this.binding=e,this.name=i,this.type=s,this.count=n,this.flattened=r}return t.prototype.copy=function(t){return this.set=t.set,this.binding=t.binding,this.name=t.name,this.type=t.type,this.count=t.count,this.flattened=t.flattened,this},t}()),vt=t("bk",function(){function t(t,e,i,s,n){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=""),void 0===s&&(s=0),void 0===n&&(n=0),this.set=t,this.binding=e,this.name=i,this.count=s,this.flattened=n}return t.prototype.copy=function(t){return this.set=t.set,this.binding=t.binding,this.name=t.name,this.count=t.count,this.flattened=t.flattened,this},t}()),yt=t("bn",function(){function t(t,e,i,s,n,r){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=""),void 0===s&&(s=_.UNKNOWN),void 0===n&&(n=0),void 0===r&&(r=0),this.set=t,this.binding=e,this.name=i,this.type=s,this.count=n,this.flattened=r}return t.prototype.copy=function(t){return this.set=t.set,this.binding=t.binding,this.name=t.name,this.type=t.type,this.count=t.count,this.flattened=t.flattened,this},t}()),Gt=t("bm",function(){function t(t,e,i,s,n,r,o){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=""),void 0===s&&(s=_.UNKNOWN),void 0===n&&(n=0),void 0===r&&(r=E.READ_WRITE),void 0===o&&(o=0),this.set=t,this.binding=e,this.name=i,this.type=s,this.count=n,this.memoryAccess=r,this.flattened=o}return t.prototype.copy=function(t){return this.set=t.set,this.binding=t.binding,this.name=t.name,this.type=t.type,this.count=t.count,this.memoryAccess=t.memoryAccess,this.flattened=t.flattened,this},t}()),mt=t("bl",function(){function t(t,e,i,s,n,r){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=""),void 0===s&&(s=0),void 0===n&&(n=E.READ_WRITE),void 0===r&&(r=0),this.set=t,this.binding=e,this.name=i,this.count=s,this.memoryAccess=n,this.flattened=r}return t.prototype.copy=function(t){return this.set=t.set,this.binding=t.binding,this.name=t.name,this.count=t.count,this.memoryAccess=t.memoryAccess,this.flattened=t.flattened,this},t}()),Lt=t("bj",function(){function t(t,e,i,s,n){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=""),void 0===s&&(s=0),void 0===n&&(n=0),this.set=t,this.binding=e,this.name=i,this.count=s,this.flattened=n}return t.prototype.copy=function(t){return this.set=t.set,this.binding=t.binding,this.name=t.name,this.count=t.count,this.flattened=t.flattened,this},t}()),Ut=t("b6",function(){function t(t,e){void 0===t&&(t=U.NONE),void 0===e&&(e=""),this.stage=t,this.source=e}return t.prototype.copy=function(t){return this.stage=t.stage,this.source=t.source,this},t}()),gt=t("A",function(){function t(t,e,i,s,n,r){void 0===t&&(t=""),void 0===e&&(e=a.UNKNOWN),void 0===i&&(i=!1),void 0===s&&(s=0),void 0===n&&(n=!1),void 0===r&&(r=0),this.name=t,this.format=e,this.isNormalized=i,this.stream=s,this.isInstanced=n,this.location=r}return t.prototype.copy=function(t){return this.name=t.name,this.format=t.format,this.isNormalized=t.isNormalized,this.stream=t.stream,this.isInstanced=t.isInstanced,this.location=t.location,this},t}()),xt=t("b5",function(){function t(t,e,i,s,n,r,o,u,h,a,c){void 0===t&&(t=""),void 0===e&&(e=[]),void 0===i&&(i=[]),void 0===s&&(s=[]),void 0===n&&(n=[]),void 0===r&&(r=[]),void 0===o&&(o=[]),void 0===u&&(u=[]),void 0===h&&(h=[]),void 0===a&&(a=[]),void 0===c&&(c=4294967295),this.name=t,this.stages=e,this.attributes=i,this.blocks=s,this.buffers=n,this.samplerTextures=r,this.samplers=o,this.textures=u,this.images=h,this.subpassInputs=a,this.hash=c}return t.prototype.copy=function(t){return this.name=t.name,j(this.stages,t.stages,Ut),j(this.attributes,t.attributes,gt),j(this.blocks,t.blocks,It),j(this.buffers,t.buffers,mt),j(this.samplerTextures,t.samplerTextures,Ot),j(this.samplers,t.samplers,vt),j(this.textures,t.textures,yt),j(this.images,t.images,Gt),j(this.subpassInputs,t.subpassInputs,Lt),this.hash=t.hash,this},t}()),Dt=t("I",function(){function t(t,e,i,s){void 0===t&&(t=[]),void 0===e&&(e=[]),void 0===i&&(i=null),void 0===s&&(s=null),this.attributes=t,this.vertexBuffers=e,this.indexBuffer=i,this.indirectBuffer=s}return t.prototype.copy=function(t){return j(this.attributes,t.attributes,gt),this.vertexBuffers=t.vertexBuffers.slice(),this.indexBuffer=t.indexBuffer,this.indirectBuffer=t.indirectBuffer,this},t}()),Mt=t("w",function(){function t(t,e,i,s,n){void 0===t&&(t=a.UNKNOWN),void 0===e&&(e=l.X1),void 0===i&&(i=g.CLEAR),void 0===s&&(s=x.STORE),void 0===n&&(n=null),this.format=t,this.sampleCount=e,this.loadOp=i,this.storeOp=s,this.barrier=n}return t.prototype.copy=function(t){return this.format=t.format,this.sampleCount=t.sampleCount,this.loadOp=t.loadOp,this.storeOp=t.storeOp,this.barrier=t.barrier,this},t}()),Pt=t("x",function(){function t(t,e,i,s,n,r,o){void 0===t&&(t=a.UNKNOWN),void 0===e&&(e=l.X1),void 0===i&&(i=g.CLEAR),void 0===s&&(s=x.STORE),void 0===n&&(n=g.CLEAR),void 0===r&&(r=x.STORE),void 0===o&&(o=null),this.format=t,this.sampleCount=e,this.depthLoadOp=i,this.depthStoreOp=s,this.stencilLoadOp=n,this.stencilStoreOp=r,this.barrier=o}return t.prototype.copy=function(t){return this.format=t.format,this.sampleCount=t.sampleCount,this.depthLoadOp=t.depthLoadOp,this.depthStoreOp=t.depthStoreOp,this.stencilLoadOp=t.stencilLoadOp,this.stencilStoreOp=t.stencilStoreOp,this.barrier=t.barrier,this},t}()),Ft=t("bb",function(){function t(t,e,i,s,n,r,o,u,h){void 0===t&&(t=[]),void 0===e&&(e=[]),void 0===i&&(i=[]),void 0===s&&(s=[]),void 0===n&&(n=-1),void 0===r&&(r=-1),void 0===o&&(o=-1),void 0===u&&(u=M.NONE),void 0===h&&(h=M.NONE),this.inputs=t,this.colors=e,this.resolves=i,this.preserves=s,this.depthStencil=n,this.depthStencilResolve=r,this.shadingRate=o,this.depthResolveMode=u,this.stencilResolveMode=h}return t.prototype.copy=function(t){return this.inputs=t.inputs.slice(),this.colors=t.colors.slice(),this.resolves=t.resolves.slice(),this.preserves=t.preserves.slice(),this.depthStencil=t.depthStencil,this.depthStencilResolve=t.depthStencilResolve,this.shadingRate=t.shadingRate,this.depthResolveMode=t.depthResolveMode,this.stencilResolveMode=t.stencilResolveMode,this},t}()),bt=t("ba",function(){function t(t,e,i,s,n){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=null),void 0===s&&(s=D.NONE),void 0===n&&(n=D.NONE),this.srcSubpass=t,this.dstSubpass=e,this.generalBarrier=i,this.prevAccesses=s,this.nextAccesses=n}return t.prototype.copy=function(t){return this.srcSubpass=t.srcSubpass,this.dstSubpass=t.dstSubpass,this.generalBarrier=t.generalBarrier,this.prevAccesses=t.prevAccesses,this.nextAccesses=t.nextAccesses,this},t}()),Xt=t("R",function(){function t(t,e,i,s,n){void 0===t&&(t=[]),void 0===e&&(e=new Pt),void 0===i&&(i=new Pt),void 0===s&&(s=[]),void 0===n&&(n=[]),this.colorAttachments=t,this.depthStencilAttachment=e,this.depthStencilResolveAttachment=i,this.subpasses=s,this.dependencies=n}return t.prototype.copy=function(t){return j(this.colorAttachments,t.colorAttachments,Mt),this.depthStencilAttachment.copy(t.depthStencilAttachment),this.depthStencilResolveAttachment.copy(t.depthStencilResolveAttachment),j(this.subpasses,t.subpasses,Ft),j(this.dependencies,t.dependencies,bt),this},t}()),wt=t("b2",function(){function t(t,e,i,s,n,r,o,u,h){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=0),void 0===s&&(s=0),void 0===n&&(n=0),void 0===r&&(r=0),void 0===o&&(o=0),void 0===u&&(u=0),void 0===h&&(h=0),this.width=t,this.height=e,this.depthOrArraySize=i,this.firstSlice=s,this.numSlices=n,this.mipLevel=r,this.levelCount=o,this.basePlane=u,this.planeCount=h}return t.prototype.copy=function(t){return this.width=t.width,this.height=t.height,this.depthOrArraySize=t.depthOrArraySize,this.firstSlice=t.firstSlice,this.numSlices=t.numSlices,this.mipLevel=t.mipLevel,this.levelCount=t.levelCount,this.basePlane=t.basePlane,this.planeCount=t.planeCount,this},t}()),Ht=t("aM",function(){function t(t,e,i){void 0===t&&(t=D.NONE),void 0===e&&(e=D.NONE),void 0===i&&(i=Q.FULL),this.prevAccesses=t,this.nextAccesses=e,this.type=i}return t.prototype.copy=function(t){return this.prevAccesses=t.prevAccesses,this.nextAccesses=t.nextAccesses,this.type=t.type,this},t}()),kt=t("bd",function(){function t(t,e,i,s,n,r,o){void 0===t&&(t=D.NONE),void 0===e&&(e=D.NONE),void 0===i&&(i=Q.FULL),void 0===s&&(s=new wt),void 0===n&&(n=!1),void 0===r&&(r=null),void 0===o&&(o=null),this.prevAccesses=t,this.nextAccesses=e,this.type=i,this.range=s,this.discardContents=n,this.srcQueue=r,this.dstQueue=o}return t.prototype.copy=function(t){return this.prevAccesses=t.prevAccesses,this.nextAccesses=t.nextAccesses,this.type=t.type,this.range.copy(t.range),this.discardContents=t.discardContents,this.srcQueue=t.srcQueue,this.dstQueue=t.dstQueue,this},t}()),Wt=t("am",function(){function t(t,e,i,s,n,r,o,u){void 0===t&&(t=D.NONE),void 0===e&&(e=D.NONE),void 0===i&&(i=Q.FULL),void 0===s&&(s=0),void 0===n&&(n=0),void 0===r&&(r=!1),void 0===o&&(o=null),void 0===u&&(u=null),this.prevAccesses=t,this.nextAccesses=e,this.type=i,this.offset=s,this.size=n,this.discardContents=r,this.srcQueue=o,this.dstQueue=u}return t.prototype.copy=function(t){return this.prevAccesses=t.prevAccesses,this.nextAccesses=t.nextAccesses,this.type=t.type,this.offset=t.offset,this.size=t.size,this.discardContents=t.discardContents,this.srcQueue=t.srcQueue,this.dstQueue=t.dstQueue,this},t}()),Vt=t("aK",function(){function t(t,e,i,s){void 0===t&&(t=null),void 0===e&&(e=[]),void 0===i&&(i=null),void 0===s&&(s=null),this.renderPass=t,this.colorTextures=e,this.depthStencilTexture=i,this.depthStencilResolveTexture=s}return t.prototype.copy=function(t){return this.renderPass=t.renderPass,this.colorTextures=t.colorTextures.slice(),this.depthStencilTexture=t.depthStencilTexture,this.depthStencilResolveTexture=t.depthStencilResolveTexture,this},t}()),Yt=t("g",function(){function t(t,e,i,s,n,r,o,u,h){void 0===t&&(t=-1),void 0===e&&(e=W.UNKNOWN),void 0===i&&(i=0),void 0===s&&(s=U.NONE),void 0===n&&(n=E.READ_ONLY),void 0===r&&(r=S.UNKNOWN),void 0===o&&(o=R.FLOAT),void 0===u&&(u=a.UNKNOWN),void 0===h&&(h=[]),this.binding=t,this.descriptorType=e,this.count=i,this.stageFlags=s,this.access=n,this.viewDimension=r,this.sampleType=o,this.format=u,this.immutableSamplers=h}return t.prototype.copy=function(t){return this.binding=t.binding,this.descriptorType=t.descriptorType,this.count=t.count,this.stageFlags=t.stageFlags,this.access=t.access,this.viewDimension=t.viewDimension,this.sampleType=t.sampleType,this.format=t.format,this.immutableSamplers=t.immutableSamplers.slice(),this},t}()),zt=t("az",function(){function t(t){void 0===t&&(t=[]),this.bindings=t}return t.prototype.copy=function(t){return j(this.bindings,t.bindings,Yt),this},t}()),Kt=t("ay",function(){function t(t){void 0===t&&(t=null),this.layout=t}return t.prototype.copy=function(t){return this.layout=t.layout,this},t}()),Qt=t("aX",function(){function t(t){void 0===t&&(t=[]),this.setLayouts=t}return t.prototype.copy=function(t){return this.setLayouts=t.setLayouts.slice(),this},t}()),Zt=t("s",function(){function t(t){void 0===t&&(t=[]),this.attributes=t}return t.prototype.copy=function(t){return j(this.attributes,t.attributes,gt),this},t}()),jt=t("ap",function(){function t(t,e){void 0===t&&(t=null),void 0===e&&(e=z.PRIMARY),this.queue=t,this.type=e}return t.prototype.copy=function(t){return this.queue=t.queue,this.type=t.type,this},t}()),qt=t("a$",function(){function t(t){void 0===t&&(t=V.GRAPHICS),this.type=t}return t.prototype.copy=function(t){return this.type=t.type,this},t}()),Jt=t("aZ",function(){function t(t,e,i){void 0===t&&(t=Y.OCCLUSION),void 0===e&&(e=32767),void 0===i&&(i=!0),this.type=t,this.maxQueryObjects=e,this.forceWait=i}return t.prototype.copy=function(t){return this.type=t.type,this.maxQueryObjects=t.maxQueryObjects,this.forceWait=t.forceWait,this},t}()),$t=t("aH",function(){function t(t,e,i,s,n,r,o,u){void 0===t&&(t=""),void 0===e&&(e=0),void 0===i&&(i=0),void 0===s&&(s=c.NONE),void 0===n&&(n=!1),void 0===r&&(r=!1),void 0===o&&(o=!1),void 0===u&&(u=!1),this.name=t,this.size=e,this.count=i,this.type=s,this.hasAlpha=n,this.hasDepth=r,this.hasStencil=o,this.isCompressed=u}return t.prototype.copy=function(t){return this.name=t.name,this.size=t.size,this.count=t.count,this.type=t.type,this.hasAlpha=t.hasAlpha,this.hasDepth=t.hasDepth,this.hasStencil=t.hasStencil,this.isCompressed=t.isCompressed,this},t}()),te=t("aS",function(){function t(t,e){void 0===t&&(t=0),void 0===e&&(e=0),this.bufferSize=t,this.textureSize=e}return t.prototype.copy=function(t){return this.bufferSize=t.bufferSize,this.textureSize=t.textureSize,this},t}()),ee=t("aF",function(){function t(t,e,i){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=0),this.writeMask=t,this.compareMask=e,this.reference=i}return t.prototype.copy=function(t){return this.writeMask=t.writeMask,this.compareMask=t.compareMask,this.reference=t.reference,this},t}()),ie=t("aE",function(){function t(t,e,i,s,n,r,o,u,h,a,c){void 0===t&&(t=new at),void 0===e&&(e=new it),void 0===i&&(i=new ct),void 0===s&&(s=1),void 0===n&&(n=0),void 0===r&&(r=0),void 0===o&&(o=0),void 0===u&&(u=0),void 0===h&&(h=0),void 0===a&&(a=new ee),void 0===c&&(c=new ee),this.viewport=t,this.scissor=e,this.blendConstant=i,this.lineWidth=s,this.depthBiasConstant=n,this.depthBiasClamp=r,this.depthBiasSlope=o,this.depthMinBounds=u,this.depthMaxBounds=h,this.stencilStatesFront=a,this.stencilStatesBack=c}return t.prototype.copy=function(t){return this.viewport.copy(t.viewport),this.scissor.copy(t.scissor),this.blendConstant.copy(t.blendConstant),this.lineWidth=t.lineWidth,this.depthBiasConstant=t.depthBiasConstant,this.depthBiasClamp=t.depthBiasClamp,this.depthBiasSlope=t.depthBiasSlope,this.depthMinBounds=t.depthMinBounds,this.depthMaxBounds=t.depthMaxBounds,this.stencilStatesFront.copy(t.stencilStatesFront),this.stencilStatesBack.copy(t.stencilStatesBack),this},t}()),se=t("aL",function(t){function s(e){var i;return(i=t.call(this)||this)._objectType=n.UNKNOWN,i._objectID=0,i._typedID=0,i._objectType=e,i._objectID=s._idTable[n.UNKNOWN]++,i._typedID=s._idTable[e]++,i}return e(s,t),i(s,[{key:"objectType",get:function(){return this._objectType}},{key:"objectID",get:function(){return this._objectID}},{key:"typedID",get:function(){return this._typedID}}]),s}(s));function ne(t,e,i,s,n,r,o,u){return new $t(t,e,i,s,n,r,o,u)}function re(t){return new $t("ASTC_SRGBA_"+t,1,4,c.UNORM,!0,!1,!1,!0)}function oe(t){return new $t("ASTC_RGBA_"+t,1,4,c.UNORM,!0,!1,!1,!0)}se._idTable=Array(n.COUNT).fill(65536),t("a",q),function(t){t.ATTR_POSITION="a_position",t.ATTR_NORMAL="a_normal",t.ATTR_TANGENT="a_tangent",t.ATTR_BITANGENT="a_bitangent",t.ATTR_WEIGHTS="a_weights",t.ATTR_JOINTS="a_joints",t.ATTR_COLOR="a_color",t.ATTR_COLOR1="a_color1",t.ATTR_COLOR2="a_color2",t.ATTR_TEX_COORD="a_texCoord",t.ATTR_TEX_COORD1="a_texCoord1",t.ATTR_TEX_COORD2="a_texCoord2",t.ATTR_TEX_COORD3="a_texCoord3",t.ATTR_TEX_COORD4="a_texCoord4",t.ATTR_TEX_COORD5="a_texCoord5",t.ATTR_TEX_COORD6="a_texCoord6",t.ATTR_TEX_COORD7="a_texCoord7",t.ATTR_TEX_COORD8="a_texCoord8",t.ATTR_BATCH_ID="a_batch_id",t.ATTR_BATCH_UV="a_batch_uv"}(q||t("a",q={}));var ue=t("c",Object.freeze([ne("UNKNOWN"),ne("A8",1,1,c.UNORM,!0),ne("L8",1,1,c.UNORM),ne("LA8",1,2,c.UNORM,!0),ne("R8",1,1,c.UNORM),ne("R8SN",1,1,c.SNORM),ne("R8UI",1,1,c.UINT),ne("R8I",1,1,c.INT),ne("R16F",2,1,c.FLOAT),ne("R16UI",2,1,c.UINT),ne("R16I",2,1,c.INT),ne("R32F",4,1,c.FLOAT),ne("R32UI",4,1,c.UINT),ne("R32I",4,1,c.INT),ne("RG8",2,2,c.UNORM),ne("RG8SN",2,2,c.SNORM),ne("RG8UI",2,2,c.UINT),ne("RG8I",2,2,c.INT),ne("RG16F",4,2,c.FLOAT),ne("RG16UI",4,2,c.UINT),ne("RG16I",4,2,c.INT),ne("RG32F",8,2,c.FLOAT),ne("RG32UI",8,2,c.UINT),ne("RG32I",8,2,c.INT),ne("RGB8",3,3,c.UNORM),ne("SRGB8",3,3,c.UNORM),ne("RGB8SN",3,3,c.SNORM),ne("RGB8UI",3,3,c.UINT),ne("RGB8I",3,3,c.INT),ne("RGB16F",6,3,c.FLOAT),ne("RGB16UI",6,3,c.UINT),ne("RGB16I",6,3,c.INT),ne("RGB32F",12,3,c.FLOAT),ne("RGB32UI",12,3,c.UINT),ne("RGB32I",12,3,c.INT),ne("RGBA8",4,4,c.UNORM,!0),ne("BGRA8",4,4,c.UNORM,!0),ne("SRGB8_A8",4,4,c.UNORM,!0),ne("RGBA8SN",4,4,c.SNORM,!0),ne("RGBA8UI",4,4,c.UINT,!0),ne("RGBA8I",4,4,c.INT,!0),ne("RGBA16F",8,4,c.FLOAT,!0),ne("RGBA16UI",8,4,c.UINT,!0),ne("RGBA16I",8,4,c.INT,!0),ne("RGBA32F",16,4,c.FLOAT,!0),ne("RGBA32UI",16,4,c.UINT,!0),ne("RGBA32I",16,4,c.INT,!0),ne("R5G6B5",2,3,c.UNORM),ne("R11G11B10F",4,3,c.FLOAT),ne("RGB5A1",2,4,c.UNORM,!0),ne("RGBA4",2,4,c.UNORM,!0),ne("RGB10A2",2,4,c.UNORM,!0),ne("RGB10A2UI",2,4,c.UINT,!0),ne("RGB9E5",2,4,c.FLOAT,!0),ne("DEPTH",4,1,c.FLOAT,!1,!0),ne("DEPTH_STENCIL",5,2,c.FLOAT,!1,!0,!0),ne("BC1",1,3,c.UNORM,!1,!1,!1,!0),ne("BC1_ALPHA",1,4,c.UNORM,!0,!1,!1,!0),ne("BC1_SRGB",1,3,c.UNORM,!1,!1,!1,!0),ne("BC1_SRGB_ALPHA",1,4,c.UNORM,!0,!1,!1,!0),ne("BC2",1,4,c.UNORM,!0,!1,!1,!0),ne("BC2_SRGB",1,4,c.UNORM,!0,!1,!1,!0),ne("BC3",1,4,c.UNORM,!0,!1,!1,!0),ne("BC3_SRGB",1,4,c.UNORM,!0,!1,!1,!0),ne("BC4",1,1,c.UNORM,!1,!1,!1,!0),ne("BC4_SNORM",1,1,c.SNORM,!1,!1,!1,!0),ne("BC5",1,2,c.UNORM,!1,!1,!1,!0),ne("BC5_SNORM",1,2,c.SNORM,!1,!1,!1,!0),ne("BC6H_UF16",1,3,c.UFLOAT,!1,!1,!1,!0),ne("BC6H_SF16",1,3,c.FLOAT,!1,!1,!1,!0),ne("BC7",1,4,c.UNORM,!0,!1,!1,!0),ne("BC7_SRGB",1,4,c.UNORM,!0,!1,!1,!0),ne("ETC_RGB8",1,3,c.UNORM,!1,!1,!1,!0),ne("ETC2_RGB8",1,3,c.UNORM,!1,!1,!1,!0),ne("ETC2_SRGB8",1,3,c.UNORM,!1,!1,!1,!0),ne("ETC2_RGB8_A1",1,4,c.UNORM,!0,!1,!1,!0),ne("ETC2_SRGB8_A1",1,4,c.UNORM,!0,!1,!1,!0),ne("ETC2_RGBA8",2,4,c.UNORM,!0,!1,!1,!0),ne("ETC2_SRGB8_A8",2,4,c.UNORM,!0,!1,!1,!0),ne("EAC_R11",1,1,c.UNORM,!1,!1,!1,!0),ne("EAC_R11SN",1,1,c.SNORM,!1,!1,!1,!0),ne("EAC_RG11",2,2,c.UNORM,!1,!1,!1,!0),ne("EAC_RG11SN",2,2,c.SNORM,!1,!1,!1,!0),ne("PVRTC_RGB2",2,3,c.UNORM,!1,!1,!1,!0),ne("PVRTC_RGBA2",2,4,c.UNORM,!0,!1,!1,!0),ne("PVRTC_RGB4",2,3,c.UNORM,!1,!1,!1,!0),ne("PVRTC_RGBA4",2,4,c.UNORM,!0,!1,!1,!0),ne("PVRTC2_2BPP",2,4,c.UNORM,!0,!1,!1,!0),ne("PVRTC2_4BPP",2,4,c.UNORM,!0,!1,!1,!0),oe("4x4"),oe("5x4"),oe("5x5"),oe("6x5"),oe("6x6"),oe("8x5"),oe("8x6"),oe("8x8"),oe("10x5"),oe("10x6"),oe("10x8"),oe("10x10"),oe("12x10"),oe("12x12"),re("4x4"),re("5x4"),re("5x5"),re("6x5"),re("6x6"),re("8x5"),re("8x6"),re("8x8"),re("10x5"),re("10x6"),re("10x8"),re("10x10"),re("12x10"),re("12x12")])),he=t("as",W.UNIFORM_BUFFER|W.DYNAMIC_UNIFORM_BUFFER|W.STORAGE_BUFFER|W.DYNAMIC_STORAGE_BUFFER),ae=t("au",W.SAMPLER_TEXTURE|W.SAMPLER|W.TEXTURE|W.STORAGE_IMAGE|W.INPUT_ATTACHMENT),ce=t("at",W.DYNAMIC_STORAGE_BUFFER|W.DYNAMIC_UNIFORM_BUFFER),Re=t("av",W.STORAGE_BUFFER|W.DYNAMIC_STORAGE_BUFFER),_e=t("aw",28);function Ae(t){return t>0&&!(t&t-1)}var Te=Math.ceil;function Ee(t,e,i,s){if(!ue[t].isCompressed)return e*i*s*ue[t].size;switch(t){case a.BC1:case a.BC1_ALPHA:case a.BC1_SRGB:case a.BC1_SRGB_ALPHA:return Te(e/4)*Te(i/4)*8*s;case a.BC2:case a.BC2_SRGB:case a.BC3:case a.BC3_SRGB:case a.BC4:case a.BC4_SNORM:case a.BC6H_SF16:case a.BC6H_UF16:case a.BC7:case a.BC7_SRGB:return Te(e/4)*Te(i/4)*16*s;case a.BC5:case a.BC5_SNORM:return Te(e/4)*Te(i/4)*32*s;case a.ETC_RGB8:case a.ETC2_RGB8:case a.ETC2_SRGB8:case a.ETC2_RGB8_A1:case a.EAC_R11:case a.EAC_R11SN:return Te(e/4)*Te(i/4)*8*s;case a.ETC2_RGBA8:case a.ETC2_SRGB8_A1:case a.EAC_RG11:case a.EAC_RG11SN:return Te(e/4)*Te(i/4)*16*s;case a.PVRTC_RGB2:case a.PVRTC_RGBA2:case a.PVRTC2_2BPP:return Te(e/8)*Te(i/4)*8*s;case a.PVRTC_RGB4:case a.PVRTC_RGBA4:case a.PVRTC2_4BPP:return Te(e/4)*Te(i/4)*8*s;case a.ASTC_RGBA_4X4:case a.ASTC_SRGBA_4X4:return Te(e/4)*Te(i/4)*16*s;case a.ASTC_RGBA_5X4:case a.ASTC_SRGBA_5X4:return Te(e/5)*Te(i/4)*16*s;case a.ASTC_RGBA_5X5:case a.ASTC_SRGBA_5X5:return Te(e/5)*Te(i/5)*16*s;case a.ASTC_RGBA_6X5:case a.ASTC_SRGBA_6X5:return Te(e/6)*Te(i/5)*16*s;case a.ASTC_RGBA_6X6:case a.ASTC_SRGBA_6X6:return Te(e/6)*Te(i/6)*16*s;case a.ASTC_RGBA_8X5:case a.ASTC_SRGBA_8X5:return Te(e/8)*Te(i/5)*16*s;case a.ASTC_RGBA_8X6:case a.ASTC_SRGBA_8X6:return Te(e/8)*Te(i/6)*16*s;case a.ASTC_RGBA_8X8:case a.ASTC_SRGBA_8X8:return Te(e/8)*Te(i/8)*16*s;case a.ASTC_RGBA_10X5:case a.ASTC_SRGBA_10X5:return Te(e/10)*Te(i/5)*16*s;case a.ASTC_RGBA_10X6:case a.ASTC_SRGBA_10X6:return Te(e/10)*Te(i/6)*16*s;case a.ASTC_RGBA_10X8:case a.ASTC_SRGBA_10X8:return Te(e/10)*Te(i/8)*16*s;case a.ASTC_RGBA_10X10:case a.ASTC_SRGBA_10X10:return Te(e/10)*Te(i/10)*16*s;case a.ASTC_RGBA_12X10:case a.ASTC_SRGBA_12X10:return Te(e/12)*Te(i/10)*16*s;case a.ASTC_RGBA_12X12:case a.ASTC_SRGBA_12X12:return Te(e/12)*Te(i/12)*16*s;default:return 0}}function de(t,e,i,s,n){for(var r=0,o=0;o<n;++o)r+=Ee(t,e,i,s),e=Math.max(e>>1,1),i=Math.max(i>>1,1);return r}var fe=[0,4,8,12,16,4,8,12,16,4,8,12,16,4,8,12,16,16,24,32,24,36,48,32,48,64,4,4,4,4,4,4];function Se(t){return fe[t]||0}function pe(t){if(t.isCompressed)return Uint8Array;var e=t.size/t.count;switch(t.type){case c.UNORM:case c.UINT:switch(e){case 1:default:return Uint8Array;case 2:return Uint16Array;case 4:return Uint32Array}case c.SNORM:case c.INT:switch(e){case 1:default:return Int8Array;case 2:return Int16Array;case 4:return Int32Array}case c.FLOAT:return 2===e?Uint16Array:Float32Array}return Float32Array}function Ce(t){switch(t){case a.BC1:case a.BC1_ALPHA:case a.BC1_SRGB:case a.BC1_SRGB_ALPHA:case a.BC2:case a.BC2_SRGB:case a.BC3:case a.BC3_SRGB:case a.BC4:case a.BC4_SNORM:case a.BC6H_SF16:case a.BC6H_UF16:case a.BC7:case a.BC7_SRGB:case a.BC5:case a.BC5_SNORM:case a.ETC_RGB8:case a.ETC2_RGB8:case a.ETC2_SRGB8:case a.ETC2_RGB8_A1:case a.EAC_R11:case a.EAC_R11SN:case a.ETC2_RGBA8:case a.ETC2_SRGB8_A1:case a.EAC_RG11:case a.EAC_RG11SN:return{width:4,height:4};case a.PVRTC_RGB2:case a.PVRTC_RGBA2:case a.PVRTC2_2BPP:return{width:8,height:4};case a.PVRTC_RGB4:case a.PVRTC_RGBA4:case a.PVRTC2_4BPP:case a.ASTC_RGBA_4X4:case a.ASTC_SRGBA_4X4:return{width:4,height:4};case a.ASTC_RGBA_5X4:case a.ASTC_SRGBA_5X4:return{width:5,height:4};case a.ASTC_RGBA_5X5:case a.ASTC_SRGBA_5X5:return{width:5,height:5};case a.ASTC_RGBA_6X5:case a.ASTC_SRGBA_6X5:return{width:6,height:5};case a.ASTC_RGBA_6X6:case a.ASTC_SRGBA_6X6:return{width:6,height:6};case a.ASTC_RGBA_8X5:case a.ASTC_SRGBA_8X5:return{width:8,height:5};case a.ASTC_RGBA_8X6:case a.ASTC_SRGBA_8X6:return{width:8,height:6};case a.ASTC_RGBA_8X8:case a.ASTC_SRGBA_8X8:return{width:8,height:8};case a.ASTC_RGBA_10X5:case a.ASTC_SRGBA_10X5:return{width:10,height:5};case a.ASTC_RGBA_10X6:case a.ASTC_SRGBA_10X6:return{width:10,height:6};case a.ASTC_RGBA_10X8:case a.ASTC_SRGBA_10X8:return{width:10,height:8};case a.ASTC_RGBA_10X10:case a.ASTC_SRGBA_10X10:return{width:10,height:10};case a.ASTC_RGBA_12X10:case a.ASTC_SRGBA_12X10:return{width:12,height:10};case a.ASTC_RGBA_12X12:case a.ASTC_SRGBA_12X12:return{width:12,height:12};default:return{width:1,height:1}}}function Ne(t,e){return Te(t/e)*e}t("z",Object.freeze({__proto__:null,get API(){return o},get AccessFlagBit(){return D},get Address(){return O},Attribute:gt,get AttributeName(){return q},get BarrierType(){return Q},BindingMappingInfo:_t,get BlendFactor(){return G},get BlendOp(){return m},BufferBarrierInfo:Wt,get BufferFlagBit(){return T},BufferInfo:Et,BufferTextureCopy:ht,get BufferUsageBit(){return A},BufferViewInfo:dt,get ClearFlagBit(){return K},Color:ct,ColorAttachment:Mt,get ColorMask(){return L},CommandBufferInfo:jt,get CommandBufferType(){return z},get ComparisonFunc(){return v},get CullMode(){return w},DESCRIPTOR_BUFFER_TYPE:he,DESCRIPTOR_DYNAMIC_TYPE:ce,DESCRIPTOR_SAMPLER_TYPE:ae,DESCRIPTOR_STORAGE_BUFFER_TYPE:Re,DRAW_INFO_SIZE:_e,DepthStencilAttachment:Pt,DescriptorSetInfo:Kt,DescriptorSetLayoutBinding:Yt,DescriptorSetLayoutInfo:zt,get DescriptorType(){return W},DeviceCaps:$,DeviceInfo:Tt,DeviceOptions:tt,DispatchInfo:St,DrawInfo:ft,get DynamicStateFlagBit(){return H},DynamicStates:ie,DynamicStencilStates:ee,Extent:st,get Feature(){return h},get Filter(){return I},get Format(){return a},get FormatFeatureBit(){return N},FormatInfo:$t,FormatInfos:ue,FormatSize:Ee,FormatSurfaceSize:de,get FormatType(){return c},FramebufferInfo:Vt,GFXObject:se,GeneralBarrierInfo:Ht,GetTypeSize:Se,IndirectBuffer:pt,InputAssemblerInfo:Dt,InputState:Zt,IsPowerOf2:Ae,get LoadOp(){return g},MarkerInfo:Rt,get MemoryAccessBit(){return E},MemoryStatus:te,get MemoryUsageBit(){return d},get ObjectType(){return n},Offset:et,get PassType(){return Z},get PipelineBindPoint(){return P},PipelineLayoutInfo:Qt,get PolygonMode(){return b},get PrimitiveMode(){return F},QueryPoolInfo:Jt,get QueryType(){return Y},QueueInfo:qt,get QueueType(){return V},Rect:it,RenderPassInfo:Xt,get ResolveMode(){return M},ResourceRange:wt,get SampleCount(){return l},get SampleType(){return R},SamplerInfo:lt,get ShadeModel(){return X},ShaderInfo:xt,ShaderStage:Ut,get ShaderStageFlagBit(){return U},Size:J,get Status(){return r},get StencilFace(){return k},get StencilOp(){return y},get StoreOp(){return x},SubpassDependency:bt,SubpassInfo:Ft,get SurfaceTransform(){return u},SwapchainInfo:At,TextureBarrierInfo:kt,TextureBlit:ut,TextureCopy:ot,get TextureFlagBit(){return C},TextureInfo:Ct,TextureSubresLayers:nt,TextureSubresRange:rt,get TextureType(){return f},get TextureUsageBit(){return p},TextureViewInfo:Nt,get Type(){return _},Uniform:Bt,UniformBlock:It,UniformInputAttachment:Lt,UniformSampler:vt,UniformSamplerTexture:Ot,UniformStorageBuffer:mt,UniformStorageImage:Gt,UniformTexture:yt,get ViewDimension(){return S},Viewport:at,get VsyncMode(){return B},alignTo:Ne,formatAlignment:Ce,getTypedArrayConstructor:pe})),t("H",function(t){function s(){var e;return(e=t.call(this,n.BUFFER)||this)._usage=A.NONE,e._memUsage=d.NONE,e._size=0,e._stride=1,e._count=0,e._flags=T.NONE,e._isBufferView=!1,e}return e(s,t),i(s,[{key:"usage",get:function(){return this._usage}},{key:"memUsage",get:function(){return this._memUsage}},{key:"size",get:function(){return this._size}},{key:"stride",get:function(){return this._stride}},{key:"count",get:function(){return this._count}},{key:"flags",get:function(){return this._flags}}]),s}(se)),t("_",function(t){function s(){var e;return(e=t.call(this,n.COMMAND_BUFFER)||this)._queue=null,e._type=z.PRIMARY,e._numDrawCalls=0,e._numInstances=0,e._numTris=0,e}return e(s,t),i(s,[{key:"type",get:function(){return this._type}},{key:"queue",get:function(){return this._queue}},{key:"numDrawCalls",get:function(){return this._numDrawCalls}},{key:"numInstances",get:function(){return this._numInstances}},{key:"numTris",get:function(){return this._numTris}}]),s}(se)),t("E",function(){function t(){this._gfxAPI=o.UNKNOWN,this._renderer="",this._vendor="",this._features=new Array(h.COUNT),this._formatFeatures=new Array(a.COUNT),this._queue=null,this._cmdBuff=null,this._numDrawCalls=0,this._numInstances=0,this._numTris=0,this._memoryStatus=new te,this._caps=new $,this._bindingMappingInfo=new _t,this._samplers=new Map,this._generalBarrierss=new Map,this._textureBarriers=new Map,this._bufferBarriers=new Map,this._swapchainFormat=a.RGBA8}var e=t.prototype;return e.hasFeature=function(t){return this._features[t]},e.getFormatFeatures=function(t){return this._formatFeatures[t]},e.enableAutoBarrier=function(){},e.getMaxSampleCount=function(){return l.X1},i(t,[{key:"gfxAPI",get:function(){return this._gfxAPI}},{key:"queue",get:function(){return this._queue}},{key:"commandBuffer",get:function(){return this._cmdBuff}},{key:"swapchainFormat",get:function(){return this._swapchainFormat}},{key:"renderer",get:function(){return this._renderer}},{key:"vendor",get:function(){return this._vendor}},{key:"numDrawCalls",get:function(){return this._numDrawCalls}},{key:"numInstances",get:function(){return this._numInstances}},{key:"numTris",get:function(){return this._numTris}},{key:"memoryStatus",get:function(){return this._memoryStatus}},{key:"capabilities",get:function(){return this._caps}},{key:"bindingMappingInfo",get:function(){return this._bindingMappingInfo}}]),t}()).canvas=void 0,t("ax",function(){function t(t){this._texture2D=null,this._texture3D=null,this._textureCube=null,this._texture2DArray=null;var e=t.capabilities,i=new Uint8Array(64);if(i.fill(255),e.maxTextureSize>=2){this._texture2D=t.createTexture(new Ct(f.TEX2D,p.STORAGE|p.SAMPLED,a.RGBA8,2,2,C.NONE));var s=new ht(0,0,0,new et(0,0,0),new st(2,2,1));t.copyBuffersToTexture([i],this._texture2D,[s])}if(e.maxTextureSize>=2){this._textureCube=t.createTexture(new Ct(f.CUBE,p.STORAGE|p.SAMPLED,a.RGBA8,2,2,C.NONE,6));var n=new ht(0,0,0,new et(0,0,0),new st(2,2,1));t.copyBuffersToTexture([i],this._textureCube,[n]),n.texSubres.baseArrayLayer=1,t.copyBuffersToTexture([i],this._textureCube,[n]),n.texSubres.baseArrayLayer=2,t.copyBuffersToTexture([i],this._textureCube,[n]),n.texSubres.baseArrayLayer=3,t.copyBuffersToTexture([i],this._textureCube,[n]),n.texSubres.baseArrayLayer=4,t.copyBuffersToTexture([i],this._textureCube,[n]),n.texSubres.baseArrayLayer=5,t.copyBuffersToTexture([i],this._textureCube,[n])}if(e.max3DTextureSize>=2){this._texture3D=t.createTexture(new Ct(f.TEX3D,p.STORAGE|p.SAMPLED,a.RGBA8,2,2,C.NONE,1,1,l.X1,2));var r=new ht(0,0,0,new et(0,0,0),new st(2,2,2),new nt(0,0,1));t.copyBuffersToTexture([i],this._texture3D,[r])}if(e.maxArrayTextureLayers>=2){this._texture2DArray=t.createTexture(new Ct(f.TEX2D_ARRAY,p.STORAGE|p.SAMPLED,a.RGBA8,2,2,C.NONE,2));var o=new ht(0,0,0,new et(0,0,0),new st(2,2,1),new nt(0,0,1));t.copyBuffersToTexture([i],this._texture2DArray,[o]),o.texSubres.baseArrayLayer=1,t.copyBuffersToTexture([i],this._texture2DArray,[o])}}return t.prototype.getTexture=function(t){switch(t){case f.TEX2D:return this._texture2D;case f.TEX3D:return this._texture3D;case f.CUBE:return this._textureCube;case f.TEX2D_ARRAY:return this._texture2DArray;default:return null}},t}()),t("G",function(t){function s(){var e;return(e=t.call(this,n.SWAPCHAIN)||this)._transform=u.IDENTITY,e._colorTexture=null,e._depthStencilTexture=null,e}return e(s,t),i(s,[{key:"colorTexture",get:function(){return this._colorTexture}},{key:"depthStencilTexture",get:function(){return this._depthStencilTexture}},{key:"surfaceTransform",get:function(){return this._transform}},{key:"width",get:function(){return this._colorTexture.width}},{key:"height",get:function(){return this._colorTexture.height}}]),s}(se)),t("Q",function(t){function s(){var e;return(e=t.call(this,n.FRAMEBUFFER)||this)._renderPass=null,e._colorTextures=[],e._depthStencilTexture=null,e._width=0,e._height=0,e}return e(s,t),i(s,[{key:"renderPass",get:function(){return this._renderPass}},{key:"colorTextures",get:function(){return this._colorTextures}},{key:"depthStencilTexture",get:function(){return this._depthStencilTexture}},{key:"width",get:function(){var t,e;return this.colorTextures.length>0?null!==(t=null==(e=this.colorTextures[0])?void 0:e.width)&&void 0!==t?t:this._width:this.depthStencilTexture?this.depthStencilTexture.width:this._width}},{key:"height",get:function(){var t,e;return this.colorTextures.length>0?null!==(t=null==(e=this.colorTextures[0])?void 0:e.height)&&void 0!==t?t:this._height:this.depthStencilTexture?this.depthStencilTexture.height:this._height}},{key:"needRebuild",get:function(){return!1}}]),s}(se));var le=String.prototype.charCodeAt;function Be(t){return this[t]}function Ie(t,e){for(var i=t.length,s=e^i,n=0,r="string"==typeof t?le:Be;i>=4;){var o=255&r.call(t,n)|(255&r.call(t,++n))<<8|(255&r.call(t,++n))<<16|(255&r.call(t,++n))<<24;o=1540483477*(65535&o)+((1540483477*(o>>>16)&65535)<<16),s=1540483477*(65535&s)+((1540483477*(s>>>16)&65535)<<16)^(o=1540483477*(65535&(o^=o>>>24))+((1540483477*(o>>>16)&65535)<<16)),i-=4,++n}switch(i){case 3:s^=(255&r.call(t,n+2))<<16;case 2:s^=(255&r.call(t,n+1))<<8;case 1:s=1540483477*(65535&(s^=255&r.call(t,n)))+((1540483477*(s>>>16)&65535)<<16)}return s=1540483477*(65535&(s^=s>>>13))+((1540483477*(s>>>16)&65535)<<16),(s^=s>>>15)>>>0}t("N",function(t){function s(){var e;return(e=t.call(this,n.INPUT_ASSEMBLER)||this)._attributes=[],e._attributesHash=0,e._vertexBuffers=[],e._indexBuffer=null,e._indirectBuffer=null,e._drawInfo=new ft,e}e(s,t);var r=s.prototype;return r.getVertexBuffer=function(t){return void 0===t&&(t=0),t<this._vertexBuffers.length?this._vertexBuffers[t]:null},r.computeAttributesHash=function(){for(var t="attrs",e=0;e<this.attributes.length;++e){var i=this.attributes[e];t+=","+i.name+","+i.format+","+i.isNormalized+","+i.stream+","+i.isInstanced+","+i.location}return Ie(t,666)},i(s,[{key:"attributes",get:function(){return this._attributes}},{key:"vertexBuffers",get:function(){return this._vertexBuffers}},{key:"indexBuffer",get:function(){return this._indexBuffer}},{key:"indirectBuffer",get:function(){return this._indirectBuffer}},{key:"attributesHash",get:function(){return this._attributesHash}},{key:"vertexCount",get:function(){return this._drawInfo.vertexCount},set:function(t){this._drawInfo.vertexCount=t}},{key:"firstVertex",get:function(){return this._drawInfo.firstVertex},set:function(t){this._drawInfo.firstVertex=t}},{key:"indexCount",get:function(){return this._drawInfo.indexCount},set:function(t){this._drawInfo.indexCount=t}},{key:"firstIndex",get:function(){return this._drawInfo.firstIndex},set:function(t){this._drawInfo.firstIndex=t}},{key:"vertexOffset",get:function(){return this._drawInfo.vertexOffset},set:function(t){this._drawInfo.vertexOffset=t}},{key:"instanceCount",get:function(){return this._drawInfo.instanceCount},set:function(t){this._drawInfo.instanceCount=t}},{key:"firstInstance",get:function(){return this._drawInfo.firstInstance},set:function(t){this._drawInfo.firstInstance=t}},{key:"drawInfo",get:function(){return this._drawInfo},set:function(t){this._drawInfo=t}}]),s}(se)),t("W",function(t){function s(){var e;return(e=t.call(this,n.DESCRIPTOR_SET)||this)._layout=null,e._buffers=[],e._textures=[],e._samplers=[],e._isDirty=!1,e}e(s,t);var r=s.prototype;return r.bindBuffer=function(t,e,i){void 0===i&&(i=0);var s=this._layout.bindingIndices[t],n=this._layout.bindings[s];if(n&&n.descriptorType&he){var r=this._layout.descriptorIndices[t];this._buffers[r+i]!==e&&(this._buffers[r+i]=e,this._isDirty=!0)}},r.bindSampler=function(t,e,i){void 0===i&&(i=0);var s=this._layout.bindingIndices[t],n=this._layout.bindings[s];if(n&&n.descriptorType&ae){var r=this._layout.descriptorIndices[t];this._samplers[r+i]!==e&&(this._samplers[r+i]=e,this._isDirty=!0)}},r.bindTexture=function(t,e,i,s){void 0===i&&(i=0),void 0===s&&(s=D.NONE);var n=this._layout.bindingIndices[t],r=this._layout.bindings[n];if(r&&r.descriptorType&ae){var o=this._layout.descriptorIndices[t];this._textures[o+i]!==e&&(this._textures[o+i]=e,this._isDirty=!0)}},r.getBuffer=function(t,e){void 0===e&&(e=0);var i=this._layout.descriptorIndices[t];return this._buffers[i+e]},r.getSampler=function(t,e){void 0===e&&(e=0);var i=this._layout.descriptorIndices[t];return this._samplers[i+e]},r.getTexture=function(t,e){void 0===e&&(e=0);var i=this._layout.descriptorIndices[t];return this._textures[i+e]},i(s,[{key:"layout",get:function(){return this._layout}}]),s}(se)),t("X",function(t){function s(){var e;return(e=t.call(this,n.DESCRIPTOR_SET_LAYOUT)||this)._bindings=[],e._bindingIndices=[],e._descriptorIndices=[],e}return e(s,t),i(s,[{key:"bindings",get:function(){return this._bindings}},{key:"bindingIndices",get:function(){return this._bindingIndices}},{key:"descriptorIndices",get:function(){return this._descriptorIndices}}]),s}(se)),t("Y",function(t){function s(){var e;return(e=t.call(this,n.PIPELINE_LAYOUT)||this)._setLayouts=[],e}return e(s,t),i(s,[{key:"setLayouts",get:function(){return this._setLayouts}}]),s}(se));var Oe=t("a3",function(){function t(t,e,i,s,n,r,o,u,h,a,c,R){void 0===t&&(t=!1),void 0===e&&(e=b.FILL),void 0===i&&(i=X.GOURAND),void 0===s&&(s=w.BACK),void 0===n&&(n=!0),void 0===r&&(r=!1),void 0===o&&(o=0),void 0===u&&(u=0),void 0===h&&(h=0),void 0===a&&(a=!0),void 0===c&&(c=!1),void 0===R&&(R=1),this.isDiscard=t,this.polygonMode=e,this.shadeModel=i,this.cullMode=s,this.isFrontFaceCCW=n,this.depthBiasEnabled=r,this.depthBias=o,this.depthBiasClamp=u,this.depthBiasSlop=h,this.isDepthClip=a,this.isMultisample=c,this.lineWidth=R}var e=t.prototype;return e.reset=function(){this.isDiscard=!1,this.polygonMode=b.FILL,this.shadeModel=X.GOURAND,this.cullMode=w.BACK,this.isFrontFaceCCW=!0,this.depthBiasEnabled=!1,this.depthBias=0,this.depthBiasClamp=0,this.depthBiasSlop=0,this.isDepthClip=!0,this.isMultisample=!1,this.lineWidth=1},e.assign=function(t){Object.assign(this,t)},e.destroy=function(){},i(t,[{key:"native",get:function(){return this}}]),t}()),ve=t("a6",function(){function t(t,e,i,s,n,r,o,u,h,a,c,R,_,A,T,E,d,f,S){void 0===t&&(t=!0),void 0===e&&(e=!0),void 0===i&&(i=v.LESS),void 0===s&&(s=!1),void 0===n&&(n=v.ALWAYS),void 0===r&&(r=65535),void 0===o&&(o=65535),void 0===u&&(u=y.KEEP),void 0===h&&(h=y.KEEP),void 0===a&&(a=y.KEEP),void 0===c&&(c=1),void 0===R&&(R=!1),void 0===_&&(_=v.ALWAYS),void 0===A&&(A=65535),void 0===T&&(T=65535),void 0===E&&(E=y.KEEP),void 0===d&&(d=y.KEEP),void 0===f&&(f=y.KEEP),void 0===S&&(S=1),this.depthTest=t,this.depthWrite=e,this.depthFunc=i,this.stencilTestFront=s,this.stencilFuncFront=n,this.stencilReadMaskFront=r,this.stencilWriteMaskFront=o,this.stencilFailOpFront=u,this.stencilZFailOpFront=h,this.stencilPassOpFront=a,this.stencilRefFront=c,this.stencilTestBack=R,this.stencilFuncBack=_,this.stencilReadMaskBack=A,this.stencilWriteMaskBack=T,this.stencilFailOpBack=E,this.stencilZFailOpBack=d,this.stencilPassOpBack=f,this.stencilRefBack=S}var e=t.prototype;return e.reset=function(){this.depthTest=!0,this.depthWrite=!0,this.depthFunc=v.LESS,this.stencilTestFront=!1,this.stencilFuncFront=v.ALWAYS,this.stencilReadMaskFront=65535,this.stencilWriteMaskFront=65535,this.stencilFailOpFront=y.KEEP,this.stencilZFailOpFront=y.KEEP,this.stencilPassOpFront=y.KEEP,this.stencilRefFront=1,this.stencilTestBack=!1,this.stencilFuncBack=v.ALWAYS,this.stencilReadMaskBack=65535,this.stencilWriteMaskBack=65535,this.stencilFailOpBack=y.KEEP,this.stencilZFailOpBack=y.KEEP,this.stencilPassOpBack=y.KEEP,this.stencilRefBack=1},e.assign=function(t){Object.assign(this,t)},e.destroy=function(){},i(t,[{key:"native",get:function(){return this}}]),t}()),ye=t("a5",function(){function t(t,e,i,s,n,r,o,u){void 0===t&&(t=!1),void 0===e&&(e=G.ONE),void 0===i&&(i=G.ZERO),void 0===s&&(s=m.ADD),void 0===n&&(n=G.ONE),void 0===r&&(r=G.ZERO),void 0===o&&(o=m.ADD),void 0===u&&(u=L.ALL),this.blend=t,this.blendSrc=e,this.blendDst=i,this.blendEq=s,this.blendSrcAlpha=n,this.blendDstAlpha=r,this.blendAlphaEq=o,this.blendColorMask=u}var e=t.prototype;return e.reset=function(){this.blend=!1,this.blendSrc=G.ONE,this.blendDst=G.ZERO,this.blendEq=m.ADD,this.blendSrcAlpha=G.ONE,this.blendDstAlpha=G.ZERO,this.blendAlphaEq=m.ADD,this.blendColorMask=L.ALL},e.assign=function(t){Object.assign(this,t)},e.destroy=function(){},t}()),Ge=t("a4",function(){function t(t,e,i,s){void 0===t&&(t=!1),void 0===e&&(e=!1),void 0===i&&(i=new ct),void 0===s&&(s=[new ye]),this.isA2C=t,this.isIndepend=e,this.blendColor=i,this.targets=s}var e=t.prototype;return e.setTarget=function(t,e){var i=this.targets[t];i||(i=this.targets[t]=new ye),Object.assign(i,e)},e.reset=function(){this.isA2C=!1,this.isIndepend=!1,this.blendColor.x=0,this.blendColor.y=0,this.blendColor.z=0,this.blendColor.w=0,this.targets.length=1,this.targets[0].reset()},e.destroy=function(){},i(t,[{key:"native",get:function(){return this}}]),t}());t("t",(function(t,e,i,s,n,r,o,u,h,a){void 0===t&&(t=null),void 0===e&&(e=null),void 0===i&&(i=null),void 0===s&&(s=new Zt),void 0===n&&(n=new Oe),void 0===r&&(r=new ve),void 0===o&&(o=new Ge),void 0===u&&(u=F.TRIANGLE_LIST),void 0===h&&(h=H.NONE),void 0===a&&(a=P.GRAPHICS),this.shader=t,this.pipelineLayout=e,this.renderPass=i,this.inputState=s,this.rasterizerState=n,this.depthStencilState=r,this.blendState=o,this.primitive=u,this.dynamicStates=h,this.bindPoint=a})),t("Z",function(t){function s(){var e;return(e=t.call(this,n.PIPELINE_STATE)||this)._shader=null,e._pipelineLayout=null,e._primitive=F.TRIANGLE_LIST,e._is=null,e._rs=new Oe,e._dss=new ve,e._bs=new Ge,e._dynamicStates=H.NONE,e._renderPass=null,e}return e(s,t),i(s,[{key:"shader",get:function(){return this._shader}},{key:"pipelineLayout",get:function(){return this._pipelineLayout}},{key:"primitive",get:function(){return this._primitive}},{key:"rasterizerState",get:function(){return this._rs}},{key:"depthStencilState",get:function(){return this._dss}},{key:"blendState",get:function(){return this._bs}},{key:"inputState",get:function(){return this._is}},{key:"dynamicStates",get:function(){return this._dynamicStates}},{key:"renderPass",get:function(){return this._renderPass}}]),s}(se)),t("$",function(t){function s(){var e;return(e=t.call(this,n.QUEUE)||this)._type=V.GRAPHICS,e}return e(s,t),i(s,[{key:"type",get:function(){return this._type}}]),s}(se)),t("O",function(t){function s(){var e;return(e=t.call(this,n.RENDER_PASS)||this)._colorInfos=[],e._depthStencilInfo=null,e._subpasses=[],e._hash=0,e}return e(s,t),s.prototype.computeHash=function(){var t="";if(this._subpasses.length)for(var e=0;e<this._subpasses.length;++e){var i=this._subpasses[e];if(i.inputs.length){t+="ia";for(var s=0;s<i.inputs.length;++s){var n=this._colorInfos[i.inputs[s]];t+=","+n.format+","+n.sampleCount}}if(i.colors.length){t+="ca";for(var r=0;r<i.colors.length;++r){var o=this._colorInfos[i.colors[r]];t+=","+o.format+","+o.sampleCount}}if(i.depthStencil>=0){var u=this._colorInfos[i.depthStencil];t+="ds,"+u.format+","+u.sampleCount}}else{t+="ca";for(var h=0;h<this._colorInfos.length;++h){var a=this._colorInfos[h];t+=","+a.format+","+a.sampleCount}var c=this._depthStencilInfo;c&&(t+="ds,"+c.format+","+c.sampleCount)}return Ie(t,666)},i(s,[{key:"colorAttachments",get:function(){return this._colorInfos}},{key:"depthStencilAttachment",get:function(){return this._depthStencilInfo}},{key:"subPasses",get:function(){return this._subpasses}},{key:"hash",get:function(){return this._hash}}]),s}(se)),t("K",function(t){function s(e,i){var s;return(s=t.call(this,n.SAMPLER)||this)._info=new lt,s._hash=0,s._info.copy(e),s._hash=i,s}return e(s,t),s.computeHash=function(t){var e=t.minFilter;return e|=t.magFilter<<2,e|=t.mipFilter<<4,e|=t.addressU<<6,e|=t.addressV<<8,e|=t.addressW<<10,(e|=Math.min(t.maxAnisotropy,16)<<12)|t.cmpFunc<<17},s.unpackFromHash=function(t){var e=new lt;return e.minFilter=3&t,e.magFilter=t>>2&3,e.mipFilter=t>>4&3,e.addressU=t>>6&3,e.addressV=t>>8&3,e.addressW=t>>10&3,e.maxAnisotropy=t>>12&31,e.cmpFunc=t>>17&7,e},i(s,[{key:"info",get:function(){return this._info}},{key:"hash",get:function(){return this._hash}}]),s}(se)),t("L",function(t){function s(){var e;return(e=t.call(this,n.SHADER)||this)._name="",e._stages=[],e._attributes=[],e._blocks=[],e._samplers=[],e}return e(s,t),i(s,[{key:"name",get:function(){return this._name}},{key:"attributes",get:function(){return this._attributes}},{key:"blocks",get:function(){return this._blocks}},{key:"samplers",get:function(){return this._samplers}},{key:"stages",get:function(){return this._stages}}]),s}(se)),t("J",function(t){function s(){var e;return(e=t.call(this,n.TEXTURE)||this)._info=new Ct,e._viewInfo=new Nt,e._isPowerOf2=!1,e._isTextureView=!1,e._size=0,e}return e(s,t),s.getLevelCount=function(t,e){return Math.floor(Math.log2(Math.max(t,e)))},i(s,[{key:"type",get:function(){return this._info.type}},{key:"usage",get:function(){return this._info.usage}},{key:"format",get:function(){return this._info.format}},{key:"width",get:function(){return this._info.width}},{key:"height",get:function(){return this._info.height}},{key:"depth",get:function(){return this._info.depth}},{key:"layerCount",get:function(){return this._info.layerCount}},{key:"levelCount",get:function(){return this._info.levelCount}},{key:"samples",get:function(){return this._info.samples}},{key:"flags",get:function(){return this._info.flags}},{key:"size",get:function(){return this._size}},{key:"info",get:function(){return this._info}},{key:"viewInfo",get:function(){return this._viewInfo}},{key:"isTextureView",get:function(){return this._isTextureView}}]),s}(se)),t("a0",function(t){function s(e,i){var s;return(s=t.call(this,n.GLOBAL_BARRIER)||this)._info=new Ht,s._hash=0,s._info.copy(e),s._hash=i,s}return e(s,t),s.computeHash=function(t){return Ie(t.prevAccesses+" "+t.nextAccesses+" "+t.type,666)},i(s,[{key:"info",get:function(){return this._info}},{key:"hash",get:function(){return this._hash}}]),s}(se)),t("a1",function(t){function s(e,i){var s;return(s=t.call(this,n.TEXTURE_BARRIER)||this)._info=new kt,s._hash=0,s._info.copy(e),s._hash=i,s}return e(s,t),s.computeHash=function(t){var e=t.prevAccesses+" "+t.nextAccesses;return e+=t.type,e+=t.range.mipLevel,e+=t.range.levelCount,e+=t.range.firstSlice,e+=t.range.numSlices,e+=t.range.basePlane,e+=t.range.planeCount,e+=t.discardContents,e+=t.srcQueue?t.srcQueue.type:0,Ie(e+=t.dstQueue?t.dstQueue.type:0,666)},i(s,[{key:"info",get:function(){return this._info}},{key:"hash",get:function(){return this._hash}}]),s}(se)),t("a2",function(t){function s(e,i){var s;return(s=t.call(this,n.BUFFER_BARRIER)||this)._info=new Wt,s._hash=0,s._info.copy(e),s._hash=i,s}return e(s,t),s.computeHash=function(t){var e=t.prevAccesses+" "+t.nextAccesses;return e+=t.type,e+=t.offset,e+=t.size,e+=t.discardContents,e+=t.srcQueue?t.srcQueue.type:0,Ie(e+=t.dstQueue?t.dstQueue.type:0,666)},i(s,[{key:"info",get:function(){return this._info}},{key:"hash",get:function(){return this._hash}}]),s}(se))}}}));
