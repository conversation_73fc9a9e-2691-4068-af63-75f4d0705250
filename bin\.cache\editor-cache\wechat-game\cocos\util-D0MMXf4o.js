System.register(["./index-C5lmLqDW.js","./mesh-C8knhDLk.js","./device-manager-BvjvoelW.js","./buffer-barrier-CuX_5NUR.js","./capsule-sT4rQfGi.js"],(function(e){"use strict";var r,n,t,l,o,i;return{setters:[function(e){r=e.r,n=e.h,t=e.b},function(e){l=e.M},null,function(e){o=e.m},function(e){i=e.c}],execute:function(){function s(e,r){e.__cc_wrapper__=r}function u(e){return e.__cc_wrapper__}function a(e){return Math.max(e.x,Math.max(e.y,e.z))}e({a:m,b:g,g:u,m:a,s:s}),r(l.prototype,"Mesh.prototype",[{name:"renderingMesh",newName:"renderingSubMeshes"}]),n(l.prototype,"Mesh.prototype",[{name:"hasFlatBuffers"},{name:"destroyFlatBuffers"}]);var c=e("V",new t),p=e("T",{type:"onTriggerEnter",selfCollider:null,otherCollider:null,impl:null}),f=e("c",{type:"onControllerTriggerEnter",collider:null,characterController:null,impl:null}),h=e("C",{type:"onCollisionEnter",selfCollider:null,otherCollider:null,contacts:[],impl:null});function g(e){var r=[],n={};if(e.length>=3){r[0]=e[0],r[1]=e[1],r[2]=e[2];for(var t=e.length,l=3;l<t;l+=3){var i=e[l],s=e[l+1],u=e[l+2],a=String(i)+String(s)+String(u),c=o(a,666);n[c]!==a&&(n[c]=a,r.push(i),r.push(s),r.push(u))}}return r}function m(e){return e.x=Math.abs(e.x),e.y=Math.abs(e.y),e.z=Math.abs(e.z),e}e("u",Object.freeze({__proto__:null,CharacterTriggerEventObject:f,CollisionEventObject:h,TriggerEventObject:p,VEC3_0:c,absolute:m,cylinder:i,getWrap:u,maxComponent:a,setWrap:s,shrinkPositions:g}))}}}));
