#include <common/common-define>
//VS->PS
CC_SURFACES_VARING_MODIFIER highp vec3 v_worldPos;
CC_SURFACES_VARING_MODIFIER vec4 v_normal;
CC_SURFACES_VARING_MODIFIER vec2 v_uv;

#if CC_SURFACES_USE_VERTEX_COLOR
  CC_SURFACES_VARING_MODIFIER lowp vec4 v_color;
#endif

#if CC_SURFACES_USE_TANGENT_SPACE
  CC_SURFACES_VARING_MODIFIER mediump vec4 v_tangent;
#endif

#if CC_SURFACES_USE_SECOND_UV
  CC_SURFACES_VARING_MODIFIER mediump vec2 v_uv1;
#endif

#if CC_USE_LIGHTMAP && !CC_FORWARD_ADD
  CC_SURFACES_VARING_MODIFIER mediump vec3 v_luv;
#endif

#if CC_RECEIVE_SHADOW || CC_USE_REFLECTION_PROBE
  CC_SURFACES_VARING_MODIFIER mediump vec4 v_shadowBiasAndProbeId;
#endif

#if CC_USE_REFLECTION_PROBE && USE_INSTANCING
  CC_SURFACES_VARING_MODIFIER mediump vec4 v_reflectionProbeData;
#endif

#if CC_USE_FOG != CC_FOG_NONE && !CC_USE_ACCURATE_FOG
  CC_SURFACES_VARING_MODIFIER mediump float v_fogFactor;
#endif

#if CC_SURFACES_TRANSFER_LOCAL_POS
  CC_SURFACES_VARING_MODIFIER highp vec4 v_localPos;
#endif

#if CC_SURFACES_TRANSFER_CLIP_POS
  CC_SURFACES_VARING_MODIFIER highp vec4 v_clipPos;
#endif

#if CC_USE_LIGHT_PROBE
  #if USE_INSTANCING
    CC_SURFACES_VARING_MODIFIER mediump vec4 v_sh_linear_const_r;
    CC_SURFACES_VARING_MODIFIER mediump vec4 v_sh_linear_const_g;
    CC_SURFACES_VARING_MODIFIER mediump vec4 v_sh_linear_const_b;
  #endif
#endif
