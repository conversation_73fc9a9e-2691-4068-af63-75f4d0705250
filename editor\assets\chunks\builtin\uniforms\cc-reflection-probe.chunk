// Copyright (c) 2017-2020 Xiamen Yaji Software Co., Ltd.

#pragma builtin(local)
layout(set = 2, binding = 13) uniform samplerCube cc_reflectionProbeCubemap;

#pragma builtin(local)
layout(set = 2, binding = 14) uniform sampler2D cc_reflectionProbePlanarMap;

#pragma builtin(local)
layout(set = 2, binding = 15) uniform sampler2D cc_reflectionProbeDataMap;

// Disable proble blend for WebGPU
// #pragma builtin(local)
// layout(set = 2, binding = 16) uniform samplerCube cc_reflectionProbeBlendCubemap;
