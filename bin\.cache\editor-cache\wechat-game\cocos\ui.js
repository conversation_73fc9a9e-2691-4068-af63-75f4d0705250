System.register(["./gc-object-CKHc4SnS.js","./index-C5lmLqDW.js","./global-exports-CR3GRnjt.js","./sprite-frame-C6JiNTOk.js","./sprite-BN1AMlyy.js","./label-ChzmWuX_.js","./prefab-DH0xadMc.js","./scene-7MDSMR3j.js","./pipeline-state-manager-Cdpe3is6.js","./node-event-DTNosVQv.js","./component-BaGvu7EF.js","./sprite-renderer-zme-rDJ_.js","./ui-renderer-DuhVjkfF.js","./xr.js","./director-DIlqD2Nd.js","./deprecated-T2yM9nsJ.js","./touch-DB0AR-Sc.js","./camera-component-Df61RNZm.js","./factory-D9_8ZCqM.js","./rendering-sub-mesh-CowWLfXC.js","./debug-view-CKetkq9d.js","./device-manager-BvjvoelW.js","./buffer-barrier-CuX_5NUR.js","./create-mesh-hkbGggH3.js","./mesh-C8knhDLk.js","./wasm-minigame-DoCiKH-Y.js","./zlib.min-CyXMsivM.js","./deprecated-Ca3AjUwj.js","./deprecated-C8l6Kwy8.js","./model-renderer-BcRDUYby.js","./renderer-9hfAnqUF.js"],(function(t){"use strict";var e,i,n,o,r,s,a,h,c,_,l,u,d,p,g,f,y,T,v,C,E,S,m,O,L,A,I,D,b,R,N,w,B,H,P,x,M,k,z,V,U,F,G,W,j,Z,X,Y,K,q,Q,J,$,tt,et,it,nt,ot,rt,st,at,ht,ct,_t,lt,ut,dt,pt,gt,ft,yt,Tt;return{setters:[function(t){e=t.t,i=t._,n=t.a,o=t.b,r=t.E,s=t.w,a=t.R,h=t.h,c=t.as,_=t.j,l=t.U,u=t.d,d=t.k},function(t){p=t.C,g=t.c,f=t.t,y=t.Z,T=t.b,v=t.a,C=t.i,E=t.K,S=t.u,m=t.s,O=t.M,L=t.B,A=t.V,I=t.a0,D=t.v,b=t.a1,R=t.O,N=t.b3,w=t.I,B=t.J,H=t.N,P=t.m,x=t.$,M=t.h,k=t.o},function(t){z=t.l,V=t.a,U=t.c,F=t.E},function(t){G=t.S},function(t){W=t.S,j=t.a},function(t){Z=t.L,X=t.V},function(t){Y=t.i},function(t){K=t.T,q=t.N,Q=t.m,J=t.k},null,function(t){$=t.N},function(t){tt=t.E,et=t.C},function(e){it=e.V,nt=e.v,t({ResolutionPolicy:e.R,View:e.V,view:e.v})},function(t){ot=t.U,rt=t.c},function(t){st=t.XrUIPressEventType,at=t.XrKeyboardEventType,ht=t.DeviceType},function(t){ct=t.d,_t=t.D},null,function(t){lt=t.I,ut=t.d,dt=t.S,pt=t.E,gt=t.e,ft=t.f},function(t){yt=t.C},function(t){Tt=t.I},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){var vt,Ct,Et,St,mt,Ot,Lt,At,It,Dt,bt,Rt,Nt,wt,Bt,Ht,Pt,xt,Mt,kt,zt,Vt,Ut,Ft,Gt,Wt,jt,Zt,Xt,Yt,Kt=new p;!function(t){t[t.NONE=0]="NONE",t[t.COLOR=1]="COLOR",t[t.SPRITE=2]="SPRITE",t[t.SCALE=3]="SCALE"}(Zt||(Zt={})),e(Zt),function(t){t[t.NORMAL=0]="NORMAL",t[t.HOVER=1]="HOVER",t[t.PRESSED=2]="PRESSED",t[t.DISABLED=3]="DISABLED"}(Xt||(Xt={})),function(t){t.CLICK="click"}(Yt||(Yt={}));var qt,Qt,Jt,$t=(vt=g("cc.Button"),Ct=C(110),Et=E(rt),St=f(q),mt=f(Zt),Ot=f(G),Lt=f(G),At=f(G),It=f(G),Dt=f([tt]),vt(bt=Ct(bt=Et((jt=function(t){function e(){var e;return(e=t.call(this)||this).clickEvents=Nt&&Nt(),e._interactable=wt&&wt(),e._transition=Bt&&Bt(),e._normalColor=Ht&&Ht(),e._hoverColor=Pt&&Pt(),e._pressedColor=xt&&xt(),e._disabledColor=Mt&&Mt(),e._normalSprite=kt&&kt(),e._hoverSprite=zt&&zt(),e._pressedSprite=Vt&&Vt(),e._disabledSprite=Ut&&Ut(),e._duration=Ft&&Ft(),e._zoomScale=Gt&&Gt(),e._target=Wt&&Wt(),e._pressed=!1,e._hovered=!1,e._fromColor=new p,e._toColor=new p,e._time=0,e._transitionFinished=!0,e._fromScale=S(),e._toScale=S(),e._originalScale=null,e._sprite=null,e._targetScale=S(),e}i(e,t);var o=e.prototype;return o.__preload=function(){this.target||(this.target=this.node),this._applyTarget(),this._resetState()},o.onEnable=function(){this._registerNodeEvent()},o.onDisable=function(){this._resetState(),this._unregisterNodeEvent()},o.onDestroy=function(){this.target.isValid&&this._unregisterTargetEvent(this.target)},o.update=function(t){var e=this.target;if(!this._transitionFinished&&e&&(this._transition===Zt.COLOR||this._transition===Zt.SCALE)){this._time+=t;var i=1;if(this._duration>0&&(i=this._time/this._duration),i>=1&&(i=1),this._transition===Zt.COLOR){var n=e._uiProps.uiComp;p.lerp(Kt,this._fromColor,this._toColor,i),n&&(n.color=Kt)}else this.transition===Zt.SCALE&&(e.getScale(this._targetScale),this._targetScale.x=y(this._fromScale.x,this._toScale.x,i),this._targetScale.y=y(this._fromScale.y,this._toScale.y,i),e.setScale(this._targetScale));1===i&&(this._transitionFinished=!0)}},o._resizeNodeToTargetNode=function(){this.target&&this.target._getUITransformComp()},o._resetState=function(){this._pressed=!1,this._hovered=!1;var t=this.target;if(t){var e=this._transition;if(e===Zt.COLOR&&this._interactable){var i=t.getComponent(ot);i&&(i.color=this._normalColor)}else e===Zt.SCALE&&this._originalScale&&t.setScale(this._originalScale);this._transitionFinished=!0}},o._registerNodeEvent=function(){var t=this,e=t.node;e.on($.TOUCH_START,t._onTouchBegan,t),e.on($.TOUCH_MOVE,t._onTouchMove,t),e.on($.TOUCH_END,t._onTouchEnded,t),e.on($.TOUCH_CANCEL,t._onTouchCancel,t),e.on($.MOUSE_ENTER,t._onMouseMoveIn,t),e.on($.MOUSE_LEAVE,t._onMouseMoveOut,t),e.on(st.XRUI_HOVER_ENTERED,t._xrHoverEnter,t),e.on(st.XRUI_HOVER_EXITED,t._xrHoverExit,t),e.on(st.XRUI_CLICK,t._xrClick,t),e.on(st.XRUI_UNCLICK,t._xrUnClick,t)},o._registerTargetEvent=function(t){t.on($.TRANSFORM_CHANGED,this._onTargetTransformChanged,this)},o._unregisterNodeEvent=function(){var t=this,e=t.node;e.off($.TOUCH_START,t._onTouchBegan,t),e.off($.TOUCH_MOVE,t._onTouchMove,t),e.off($.TOUCH_END,t._onTouchEnded,t),e.off($.TOUCH_CANCEL,t._onTouchCancel,t),e.off($.MOUSE_ENTER,t._onMouseMoveIn,t),e.off($.MOUSE_LEAVE,t._onMouseMoveOut,t),e.off(st.XRUI_HOVER_ENTERED,t._xrHoverEnter,t),e.off(st.XRUI_HOVER_EXITED,t._xrHoverExit,t),e.off(st.XRUI_CLICK,t._xrClick,t),e.off(st.XRUI_UNCLICK,t._xrUnClick,t)},o._unregisterTargetEvent=function(t){t.off($.TRANSFORM_CHANGED)},o._getTargetSprite=function(t){var e=null;return t&&(e=t.getComponent(W)),e},o._applyTarget=function(){this.target&&(this._sprite=this._getTargetSprite(this.target),this._originalScale||(this._originalScale=new T),T.copy(this._originalScale,this.target.scale),this._registerTargetEvent(this.target))},o._onTargetSpriteFrameChanged=function(t){this._transition===Zt.SPRITE&&this._setCurrentStateSpriteFrame(t.spriteFrame)},o._setCurrentStateSpriteFrame=function(t){if(t)switch(this._getButtonState()){case Xt.NORMAL:this._normalSprite=t;break;case Xt.HOVER:this._hoverSprite=t;break;case Xt.PRESSED:this._pressedSprite=t;break;case Xt.DISABLED:this._disabledSprite=t}},o._onTargetColorChanged=function(t){this._transition===Zt.COLOR&&this._setCurrentStateColor(t)},o._setCurrentStateColor=function(t){switch(this._getButtonState()){case Xt.NORMAL:this._normalColor=t;break;case Xt.HOVER:this._hoverColor=t;break;case Xt.PRESSED:this._pressedColor=t;break;case Xt.DISABLED:this._disabledColor=t}},o._onTargetTransformChanged=function(t){t&K.SCALE&&this._originalScale&&this._transition===Zt.SCALE&&this._transitionFinished&&T.copy(this._originalScale,this.target.scale)},o._onTouchBegan=function(t){this._interactable&&this.enabledInHierarchy&&(this._pressed=!0,this._updateState(),t&&(t.propagationStopped=!0))},o._onTouchMove=function(t){if(this._interactable&&this.enabledInHierarchy&&this._pressed&&t){var e=t.touch;if(e){var i,n=this.node._getUITransformComp().hitTest(e.getLocation(),t.windowId);this._transition===Zt.SCALE&&this.target&&this._originalScale?n?(T.copy(this._fromScale,this._originalScale),T.multiplyScalar(this._toScale,this._originalScale,this._zoomScale),this._transitionFinished=!1):(this._time=0,this._transitionFinished=!0,this.target.setScale(this._originalScale)):(i=n?Xt.PRESSED:Xt.NORMAL,this._applyTransition(i)),t&&(t.propagationStopped=!0)}}},o._onTouchEnded=function(t){this._interactable&&this.enabledInHierarchy&&(this._pressed&&(tt.emitEvents(this.clickEvents,t),this.node.emit(Yt.CLICK,this)),this._pressed=!1,this._updateState(),t&&(t.propagationStopped=!0))},o._onTouchCancel=function(){this._interactable&&this.enabledInHierarchy&&(this._pressed=!1,this._updateState())},o._onMouseMoveIn=function(){!this._pressed&&this.interactable&&this.enabledInHierarchy&&(this._transition!==Zt.SPRITE||this._hoverSprite)&&(this._hovered||(this._hovered=!0,this._updateState()))},o._onMouseMoveOut=function(){this._hovered&&(this._hovered=!1,this._updateState())},o._updateState=function(){var t=this._getButtonState();this._applyTransition(t)},o._getButtonState=function(){var t=Xt.NORMAL;return this._interactable?this._pressed?t=Xt.PRESSED:this._hovered&&(t=Xt.HOVER):t=Xt.DISABLED,t},o._updateColorTransition=function(t){var e,i=this._getColorByState(t),n=null==(e=this.target)?void 0:e.getComponent(ot);n&&(t===Xt.DISABLED?(n.color=i,this._transitionFinished=!0):(this._fromColor=n.color.clone(),this._toColor=i,this._time=0,this._transitionFinished=!1))},o._updateSpriteTransition=function(t){var e=this._getSpriteFrameByState(t);this._sprite&&e&&(this._sprite.spriteFrame=e)},o._updateScaleTransition=function(t){this._interactable&&(t===Xt.PRESSED?this._zoomUp():this._zoomBack())},o._zoomUp=function(){this._originalScale&&(T.copy(this._fromScale,this._originalScale),T.multiplyScalar(this._toScale,this._originalScale,this._zoomScale),this._time=0,this._transitionFinished=!1)},o._zoomBack=function(){this.target&&this._originalScale&&(T.copy(this._fromScale,this.target.scale),T.copy(this._toScale,this._originalScale),this._time=0,this._transitionFinished=!1)},o._applyTransition=function(t){var e=this._transition;e===Zt.COLOR?this._updateColorTransition(t):e===Zt.SPRITE?this._updateSpriteTransition(t):e===Zt.SCALE&&this._updateScaleTransition(t)},o._getSpriteFrameByState=function(t){switch(t){case Xt.NORMAL:return this._normalSprite;case Xt.DISABLED:return this._disabledSprite;case Xt.HOVER:return this.hoverSprite;case Xt.PRESSED:return this._pressedSprite;default:return null}},o._getColorByState=function(t){switch(t){case Xt.NORMAL:return this._normalColor;case Xt.DISABLED:return this._disabledColor;case Xt.HOVER:return this._hoverColor;case Xt.PRESSED:return this._pressedColor;default:return new p}},o._xrHoverEnter=function(){this._onMouseMoveIn(),this._updateState()},o._xrHoverExit=function(){this._onMouseMoveOut(),this._pressed&&(this._pressed=!1,this._updateState())},o._xrClick=function(){this._interactable&&this.enabledInHierarchy&&(this._pressed=!0,this._updateState())},o._xrUnClick=function(){this._interactable&&this.enabledInHierarchy&&(this._pressed&&(tt.emitEvents(this.clickEvents,this),this.node.emit(Yt.CLICK,this)),this._pressed=!1,this._updateState())},n(e,[{key:"target",get:function(){return this._target||this.node},set:function(t){this._target!==t&&(this._target&&this._unregisterTargetEvent(this._target),this._target=t,this._applyTarget())}},{key:"interactable",get:function(){return this._interactable},set:function(t){this._interactable!==t&&(this._interactable=t,this._updateState(),this._interactable||this._resetState())}},{key:"_resizeToTarget",set:function(t){t&&this._resizeNodeToTargetNode()}},{key:"transition",get:function(){return this._transition},set:function(t){this._transition!==t&&(this._transition===Zt.COLOR?this._updateColorTransition(Xt.NORMAL):this._transition===Zt.SPRITE&&this._updateSpriteTransition(Xt.NORMAL),this._transition=t,this._updateState())}},{key:"normalColor",get:function(){return this._normalColor},set:function(t){this._normalColor!==t&&(this._normalColor.set(t),this._updateState())}},{key:"pressedColor",get:function(){return this._pressedColor},set:function(t){this._pressedColor!==t&&this._pressedColor.set(t)}},{key:"hoverColor",get:function(){return this._hoverColor},set:function(t){this._hoverColor!==t&&this._hoverColor.set(t)}},{key:"disabledColor",get:function(){return this._disabledColor},set:function(t){this._disabledColor!==t&&(this._disabledColor.set(t),this._updateState())}},{key:"duration",get:function(){return this._duration},set:function(t){this._duration!==t&&(this._duration=t)}},{key:"zoomScale",get:function(){return this._zoomScale},set:function(t){this._zoomScale!==t&&(this._zoomScale=t)}},{key:"normalSprite",get:function(){return this._normalSprite},set:function(t){if(this._normalSprite!==t){this._normalSprite=t;var e=this.node.getComponent(W);e&&(e.spriteFrame=t),this._updateState()}}},{key:"pressedSprite",get:function(){return this._pressedSprite},set:function(t){this._pressedSprite!==t&&(this._pressedSprite=t,this._updateState())}},{key:"hoverSprite",get:function(){return this._hoverSprite},set:function(t){this._hoverSprite!==t&&(this._hoverSprite=t,this._updateState())}},{key:"disabledSprite",get:function(){return this._disabledSprite},set:function(t){this._disabledSprite!==t&&(this._disabledSprite=t,this._updateState())}}]),e}(et),jt.Transition=Zt,jt.EventType=Yt,o((Rt=jt).prototype,"target",[St],Object.getOwnPropertyDescriptor(Rt.prototype,"target"),Rt.prototype),o(Rt.prototype,"transition",[mt],Object.getOwnPropertyDescriptor(Rt.prototype,"transition"),Rt.prototype),o(Rt.prototype,"normalSprite",[Ot],Object.getOwnPropertyDescriptor(Rt.prototype,"normalSprite"),Rt.prototype),o(Rt.prototype,"pressedSprite",[Lt],Object.getOwnPropertyDescriptor(Rt.prototype,"pressedSprite"),Rt.prototype),o(Rt.prototype,"hoverSprite",[At],Object.getOwnPropertyDescriptor(Rt.prototype,"hoverSprite"),Rt.prototype),o(Rt.prototype,"disabledSprite",[It],Object.getOwnPropertyDescriptor(Rt.prototype,"disabledSprite"),Rt.prototype),Nt=v(Rt.prototype,"clickEvents",[Dt,m],(function(){return[]})),wt=v(Rt.prototype,"_interactable",[m],(function(){return!0})),Bt=v(Rt.prototype,"_transition",[m],(function(){return Zt.NONE})),Ht=v(Rt.prototype,"_normalColor",[m],(function(){return p.WHITE.clone()})),Pt=v(Rt.prototype,"_hoverColor",[m],(function(){return new p(211,211,211,255)})),xt=v(Rt.prototype,"_pressedColor",[m],(function(){return p.WHITE.clone()})),Mt=v(Rt.prototype,"_disabledColor",[m],(function(){return new p(124,124,124,255)})),kt=v(Rt.prototype,"_normalSprite",[m],(function(){return null})),zt=v(Rt.prototype,"_hoverSprite",[m],(function(){return null})),Vt=v(Rt.prototype,"_pressedSprite",[m],(function(){return null})),Ut=v(Rt.prototype,"_disabledSprite",[m],(function(){return null})),Ft=v(Rt.prototype,"_duration",[m],(function(){return.1})),Gt=v(Rt.prototype,"_zoomScale",[m],(function(){return 1.2})),Wt=v(Rt.prototype,"_target",[m],(function(){return null})),bt=Rt))||bt)||bt)||bt);t({Button:$t,ButtonComponent:$t}),z.Button=$t,function(t){t[t.DEFAULT=0]="DEFAULT",t[t.DONE=1]="DONE",t[t.SEND=2]="SEND",t[t.SEARCH=3]="SEARCH",t[t.GO=4]="GO",t[t.NEXT=5]="NEXT"}(qt||(qt={})),r(qt),function(t){t[t.ANY=0]="ANY",t[t.EMAIL_ADDR=1]="EMAIL_ADDR",t[t.NUMERIC=2]="NUMERIC",t[t.PHONE_NUMBER=3]="PHONE_NUMBER",t[t.URL=4]="URL",t[t.DECIMAL=5]="DECIMAL",t[t.SINGLE_LINE=6]="SINGLE_LINE"}(Qt||(Qt={})),r(Qt),function(t){t[t.PASSWORD=0]="PASSWORD",t[t.SENSITIVE=1]="SENSITIVE",t[t.INITIAL_CAPS_WORD=2]="INITIAL_CAPS_WORD",t[t.INITIAL_CAPS_SENTENCE=3]="INITIAL_CAPS_SENTENCE",t[t.INITIAL_CAPS_ALL_CHARACTERS=4]="INITIAL_CAPS_ALL_CHARACTERS",t[t.DEFAULT=5]="DEFAULT"}(Jt||(Jt={})),r(Jt);var te=function(){function t(){this._editing=!1,this._delegate=null}var e=t.prototype;return e.init=function(){},e.onEnable=function(){},e.beforeDraw=function(){},e.onDisable=function(){this._editing&&this.endEditing()},e.clear=function(){this._delegate=null},e.setTabIndex=function(){},e.setSize=function(){},e.setFocus=function(t){t?this.beginEditing():this.endEditing()},e.isFocused=function(){return this._editing},e.beginEditing=function(){},e.endEditing=function(){},t}();V.document,new O,new O,new T;var ee,ie,ne,oe,re,se,ae,he,ce,_e,le,ue,de,pe,ge,fe,ye,Te,ve,Ce,Ee,Se,me,Oe,Le,Ae,Ie,De,be,Re,Ne=0;!function(t){function e(){var e;return(e=t.call(this)||this)._delegate=null,e._inputMode=-1,e._inputFlag=-1,e._returnType=-1,e.__eventListeners={},e.__autoResize=!1,e.__orientationChanged=void 0,e._edTxt=null,e._isTextArea=!1,e._textLabelFont=null,e._textLabelFontSize=null,e._textLabelFontColor=null,e._textLabelAlign=null,e._placeholderLabelFont=null,e._placeholderLabelFontSize=null,e._placeholderLabelFontColor=null,e._placeholderLabelAlign=null,e._placeholderLineHeight=null,e._placeholderStyleSheet=null,e._domId="EditBoxId_"+ ++Ne,e._forceUpdate=!1,e}i(e,t);var n=e.prototype;n.init=function(){},n.clear=function(){},n._resize=function(){this._forceUpdate=!0},n.beforeDraw=function(){},n.setTabIndex=function(){},n.setSize=function(){},n.beginEditing=function(){},n.endEditing=function(){},n._createInput=function(){},n._createTextArea=function(){},n._addDomToGameContainer=function(){},n._removeDomFromGameContainer=function(){},n._showDom=function(){},n._hideDom=function(){},n._showDomOnMobile=function(){},n._hideDomOnMobile=function(){},n._isElementInViewport=function(){return!1},n._adjustWindowScroll=function(){},n._scrollBackWindow=function(){},n._updateMatrix=function(){},n._updateInputType=function(){},n._updateMaxLength=function(){},n._initStyleSheet=function(){},n._updateStyleSheet=function(){},n._updateTextLabel=function(){},n._updatePlaceholderLabel=function(){},n._registerEventListeners=function(){},n._removeEventListeners=function(){}}(te),function(t){t.EDITING_DID_BEGAN="editing-did-began",t.EDITING_DID_ENDED="editing-did-ended",t.TEXT_CHANGED="text-changed",t.EDITING_RETURN="editing-return",t.XR_EDITING_DID_BEGAN="xr-editing-did-began",t.XR_EDITING_DID_ENDED="xr-editing-did-ended"}(Re||(Re={}));var we,Be,He,Pe,xe,Me,ke,ze,Ve,Ue,Fe,Ge,We,je,Ze,Xe,Ye,Ke,qe,Qe,Je,$e,ti,ei,ii,ni,oi,ri,si,ai,hi,ci,_i,li,ui=(ee=g("cc.EditBox"),ie=C(110),ne=E(rt),oe=f(Z),re=f(Z),se=f(G),ae=f(Jt),he=f(Qt),ce=f(qt),_e=f([tt]),le=f([tt]),ue=f([tt]),de=f([tt]),ee(pe=ie(pe=ne((be=function(t){function e(){var e;return(e=t.call(this)||this).editingDidBegan=fe&&fe(),e.textChanged=ye&&ye(),e.editingDidEnded=Te&&Te(),e.editingReturn=ve&&ve(),e._impl=null,e._background=null,e._textLabel=Ce&&Ce(),e._placeholderLabel=Ee&&Ee(),e._returnType=Se&&Se(),e._string=me&&me(),e._tabIndex=Oe&&Oe(),e._backgroundImage=Le&&Le(),e._inputFlag=Ae&&Ae(),e._inputMode=Ie&&Ie(),e._maxLength=De&&De(),e._isLabelVisible=!1,e}i(e,t);var o=e.prototype;return o.__preload=function(){this._init()},o.onEnable=function(){this._registerEvent(),this._ensureBackgroundSprite(),this._impl&&this._impl.onEnable()},o._beforeDraw=function(){this._impl&&this._impl.beforeDraw()},o.onDisable=function(){this._unregisterEvent(),this._unregisterBackgroundEvent(),this._impl&&this._impl.onDisable()},o.onDestroy=function(){ct.off(_t.BEFORE_DRAW,this._beforeDraw,this),this._impl&&this._impl.clear()},o.setFocus=function(){this._impl&&this._impl.setFocus(!0)},o.focus=function(){this._impl&&this._impl.setFocus(!0)},o.blur=function(){this._impl&&this._impl.setFocus(!1)},o.isFocused=function(){return!!this._impl&&this._impl.isFocused()},o._editBoxEditingDidBegan=function(){tt.emitEvents(this.editingDidBegan,this),this.node.emit(Re.EDITING_DID_BEGAN,this)},o._editBoxEditingDidEnded=function(t){tt.emitEvents(this.editingDidEnded,this),this.node.emit(Re.EDITING_DID_ENDED,this,t)},o._editBoxTextChanged=function(t){t=this._updateLabelStringStyle(t,!0),this.string=t,tt.emitEvents(this.textChanged,t,this),this.node.emit(Re.TEXT_CHANGED,this)},o._editBoxEditingReturn=function(t){tt.emitEvents(this.editingReturn,this),this.node.emit(Re.EDITING_RETURN,this,t)},o._showLabels=function(){this._isLabelVisible=!0,this._updateLabels()},o._hideLabels=function(){this._isLabelVisible=!1,this._textLabel&&(this._textLabel.node.active=!1),this._placeholderLabel&&(this._placeholderLabel.node.active=!1)},o._onTouchBegan=function(t){t.propagationStopped=!0},o._onTouchCancel=function(t){t.propagationStopped=!0},o._onTouchEnded=function(t){this._impl&&this._impl.beginEditing(),t.propagationStopped=!0},o._init=function(){this._updatePlaceholderLabel(),this._updateTextLabel(),this._isLabelVisible=!0,this.node.on($.SIZE_CHANGED,this._resizeChildNodes,this),ct.on(_t.BEFORE_DRAW,this._beforeDraw,this),(this._impl=new e._EditBoxImpl).init(this),this._updateString(this._string),this._syncSize()},o._ensureBackgroundSprite=function(){if(!this._background){var t=this.node.getComponent(W);t||(t=this.node.addComponent(W)),t!==this._background&&(t.type=W.Type.SLICED,t.spriteFrame=this._backgroundImage,this._background=t,this._registerBackgroundEvent())}},o._updateTextLabel=function(){var t=this._textLabel;if(!t){var e=this.node.getChildByName("TEXT_LABEL");e||((e=new q("TEXT_LABEL")).layer=this.node.layer),(t=e.getComponent(Z))||(t=e.addComponent(Z)),e.parent=this.node,this._textLabel=t}this._inputMode===Qt.ANY?(t.verticalAlign=X.TOP,t.enableWrapText=!0):t.enableWrapText=!1,t.string=this._updateLabelStringStyle(this._string)},o._updatePlaceholderLabel=function(){var t=this._placeholderLabel;if(!t){var e=this.node.getChildByName("PLACEHOLDER_LABEL");e||((e=new q("PLACEHOLDER_LABEL")).layer=this.node.layer),(t=e.getComponent(Z))||(t=e.addComponent(Z)),e.parent=this.node,this._placeholderLabel=t}this._inputMode===Qt.ANY?t.enableWrapText=!0:t.enableWrapText=!1,t.string=this.placeholder},o._syncSize=function(){var t=this.node._getUITransformComp(),e=t.contentSize;if(this._background){var i=this._background.node._getUITransformComp();i.anchorPoint=t.anchorPoint,i.setContentSize(e)}this._updateLabelPosition(e),this._impl&&this._impl.setSize(e.width,e.height)},o._updateLabels=function(){if(this._isLabelVisible){var t=this._string;this._textLabel&&(this._textLabel.node.active=""!==t),this._placeholderLabel&&(this._placeholderLabel.node.active=""===t)}},o._updateString=function(t){var e=this._textLabel;if(e){var i=t;i&&(i=this._updateLabelStringStyle(i)),e.string=i,this._updateLabels()}},o._updateLabelStringStyle=function(t,e){void 0===e&&(e=!1);var i,n=this._inputFlag;if(e||n!==Jt.PASSWORD)n===Jt.INITIAL_CAPS_ALL_CHARACTERS?t=t.toUpperCase():n===Jt.INITIAL_CAPS_WORD?t=t.replace(/(?:^|\s)\S/g,(function(t){return t.toUpperCase()})):n===Jt.INITIAL_CAPS_SENTENCE&&(t=(i=t).charAt(0).toUpperCase()+i.slice(1));else{for(var o="",r=t.length,s=0;s<r;++s)o+="●";t=o}return t},o._registerEvent=function(){var t=this,e=t.node;e.on($.TOUCH_START,t._onTouchBegan,t),e.on($.TOUCH_END,t._onTouchEnded,t),e.on(st.XRUI_UNCLICK,t._xrUnClick,t),e.on(at.XR_KEYBOARD_INPUT,t._xrKeyBoardInput,t)},o._unregisterEvent=function(){var t=this,e=t.node;e.off($.TOUCH_START,t._onTouchBegan,t),e.off($.TOUCH_END,t._onTouchEnded,t),e.off(st.XRUI_UNCLICK,t._xrUnClick,t),e.off(at.XR_KEYBOARD_INPUT,t._xrKeyBoardInput,t)},o._onBackgroundSpriteFrameChanged=function(){this._background&&(this.backgroundImage=this._background.spriteFrame)},o._registerBackgroundEvent=function(){var t=this._background&&this._background.node;null==t||t.on(j.SPRITE_FRAME_CHANGED,this._onBackgroundSpriteFrameChanged,this)},o._unregisterBackgroundEvent=function(){var t=this._background&&this._background.node;null==t||t.off(j.SPRITE_FRAME_CHANGED,this._onBackgroundSpriteFrameChanged,this)},o._updateLabelPosition=function(t){var e=this.node._getUITransformComp(),i=-e.anchorX*e.width,n=-e.anchorY*e.height,o=this._placeholderLabel,r=this._textLabel;r&&(r.node._getUITransformComp().setContentSize(t.width-2,t.height),r.node.setPosition(i+2,n+t.height,r.node.position.z),this._inputMode===Qt.ANY&&(r.verticalAlign=X.TOP),r.enableWrapText=this._inputMode===Qt.ANY),o&&(o.node._getUITransformComp().setContentSize(t.width-2,t.height),o.node.setPosition(i+2,n+t.height,o.node.position.z),o.enableWrapText=this._inputMode===Qt.ANY)},o._resizeChildNodes=function(){var t=this.node._getUITransformComp(),e=this._textLabel&&this._textLabel.node;e&&(e.setPosition(-t.width/2,t.height/2,e.position.z),e._getUITransformComp().setContentSize(t.contentSize));var i=this._placeholderLabel&&this._placeholderLabel.node;i&&(i.setPosition(-t.width/2,t.height/2,i.position.z),i._getUITransformComp().setContentSize(t.contentSize));var n=this._background&&this._background.node;n&&n._getUITransformComp().setContentSize(t.contentSize),this._syncSize()},o._xrUnClick=function(){this.node.emit(Re.XR_EDITING_DID_BEGAN,this._maxLength,this.string)},o._xrKeyBoardInput=function(t){this.string=t},n(e,[{key:"string",get:function(){return this._string},set:function(t){this._maxLength>=0&&t.length>=this._maxLength&&(t=t.slice(0,this._maxLength)),this._string!==t&&(this._string=t,this._updateString(t))}},{key:"placeholder",get:function(){return this._placeholderLabel?this._placeholderLabel.string:""},set:function(t){this._placeholderLabel&&(this._placeholderLabel.string=t)}},{key:"textLabel",get:function(){return this._textLabel},set:function(t){this._textLabel!==t&&(this._textLabel=t,this._textLabel&&(this._updateTextLabel(),this._updateLabels()))}},{key:"placeholderLabel",get:function(){return this._placeholderLabel},set:function(t){this._placeholderLabel!==t&&(this._placeholderLabel=t,this._placeholderLabel&&(this._updatePlaceholderLabel(),this._updateLabels()))}},{key:"backgroundImage",get:function(){return this._backgroundImage},set:function(t){this._backgroundImage!==t&&(this._backgroundImage=t,this._ensureBackgroundSprite(),this._background.spriteFrame=t)}},{key:"inputFlag",get:function(){return this._inputFlag},set:function(t){this._inputFlag!==t&&(this._inputFlag=t,this._updateString(this._string))}},{key:"inputMode",get:function(){return this._inputMode},set:function(t){this._inputMode!==t&&(this._inputMode=t,this._updateTextLabel(),this._updatePlaceholderLabel())}},{key:"returnType",get:function(){return this._returnType},set:function(t){this._returnType=t}},{key:"maxLength",get:function(){return this._maxLength},set:function(t){this._maxLength=t}},{key:"tabIndex",get:function(){return this._tabIndex},set:function(t){this._tabIndex!==t&&(this._tabIndex=t,this._impl&&this._impl.setTabIndex(t))}}]),e}(et),be._EditBoxImpl=te,be.KeyboardReturnType=qt,be.InputFlag=Jt,be.InputMode=Qt,be.EventType=Re,o((ge=be).prototype,"textLabel",[oe],Object.getOwnPropertyDescriptor(ge.prototype,"textLabel"),ge.prototype),o(ge.prototype,"placeholderLabel",[re],Object.getOwnPropertyDescriptor(ge.prototype,"placeholderLabel"),ge.prototype),o(ge.prototype,"backgroundImage",[se],Object.getOwnPropertyDescriptor(ge.prototype,"backgroundImage"),ge.prototype),o(ge.prototype,"inputFlag",[ae],Object.getOwnPropertyDescriptor(ge.prototype,"inputFlag"),ge.prototype),o(ge.prototype,"inputMode",[he],Object.getOwnPropertyDescriptor(ge.prototype,"inputMode"),ge.prototype),o(ge.prototype,"returnType",[ce],Object.getOwnPropertyDescriptor(ge.prototype,"returnType"),ge.prototype),fe=v(ge.prototype,"editingDidBegan",[_e,m],(function(){return[]})),ye=v(ge.prototype,"textChanged",[le,m],(function(){return[]})),Te=v(ge.prototype,"editingDidEnded",[ue,m],(function(){return[]})),ve=v(ge.prototype,"editingReturn",[de,m],(function(){return[]})),Ce=v(ge.prototype,"_textLabel",[m],(function(){return null})),Ee=v(ge.prototype,"_placeholderLabel",[m],(function(){return null})),Se=v(ge.prototype,"_returnType",[m],(function(){return qt.DEFAULT})),me=v(ge.prototype,"_string",[m],(function(){return""})),Oe=v(ge.prototype,"_tabIndex",[m],(function(){return 0})),Le=v(ge.prototype,"_backgroundImage",[m],(function(){return null})),Ae=v(ge.prototype,"_inputFlag",[m],(function(){return Jt.DEFAULT})),Ie=v(ge.prototype,"_inputMode",[m],(function(){return Qt.ANY})),De=v(ge.prototype,"_maxLength",[m],(function(){return 20})),pe=ge))||pe)||pe)||pe);t({EditBox:ui,EditBoxComponent:ui}),z.internal.EditBox=ui,function(t){t[t.NONE=0]="NONE",t[t.HORIZONTAL=1]="HORIZONTAL",t[t.VERTICAL=2]="VERTICAL",t[t.GRID=3]="GRID"}(si||(si={})),e(si),function(t){t[t.NONE=0]="NONE",t[t.CONTAINER=1]="CONTAINER",t[t.CHILDREN=2]="CHILDREN"}(ai||(ai={})),e(ai),function(t){t[t.HORIZONTAL=0]="HORIZONTAL",t[t.VERTICAL=1]="VERTICAL"}(hi||(hi={})),e(hi),function(t){t[t.BOTTOM_TO_TOP=0]="BOTTOM_TO_TOP",t[t.TOP_TO_BOTTOM=1]="TOP_TO_BOTTOM"}(ci||(ci={})),e(ci),function(t){t[t.LEFT_TO_RIGHT=0]="LEFT_TO_RIGHT",t[t.RIGHT_TO_LEFT=1]="RIGHT_TO_LEFT"}(_i||(_i={})),e(_i),function(t){t[t.NONE=0]="NONE",t[t.FIXED_ROW=1]="FIXED_ROW",t[t.FIXED_COL=2]="FIXED_COL"}(li||(li={})),e(li);var di,pi,gi,fi,yi,Ti,vi,Ci,Ei,Si,mi,Oi,Li,Ai,Ii=new T,Di=(we=g("cc.Layout"),Be=C(110),He=E(rt),Pe=f(si),xe=f(ai),Me=f(hi),ke=f(ci),ze=f(_i),Ve=f(li),we(Ue=Be(Ue=He((ri=function(t){function e(){var e;return(e=t.call(this)||this)._resizeMode=Ge&&Ge(),e._layoutType=We&&We(),e._cellSize=je&&je(),e._startAxis=Ze&&Ze(),e._paddingLeft=Xe&&Xe(),e._paddingRight=Ye&&Ye(),e._paddingTop=Ke&&Ke(),e._paddingBottom=qe&&qe(),e._spacingX=Qe&&Qe(),e._spacingY=Je&&Je(),e._verticalDirection=$e&&$e(),e._horizontalDirection=ti&&ti(),e._constraint=ei&&ei(),e._constraintNum=ii&&ii(),e._affectedByScale=ni&&ni(),e._isAlign=oi&&oi(),e._layoutSize=new L(300,200),e._layoutDirty=!0,e._childrenDirty=!1,e._usefulLayoutObj=[],e._init=!1,e}i(e,t);var o=e.prototype;return o.updateLayout=function(t){void 0===t&&(t=!1),(this._layoutDirty||t)&&(this._doLayout(),this._layoutDirty=!1)},o.onEnable=function(){this._addEventListeners();var t=this.node._getUITransformComp();t.contentSize.equals(L.ZERO)&&t.setContentSize(this._layoutSize),this._childrenChanged()},o.onDisable=function(){this._usefulLayoutObj.length=0,this._removeEventListeners()},o._checkUsefulObj=function(){this._usefulLayoutObj.length=0;for(var t=this.node.children,e=0;e<t.length;++e){var i=t[e],n=i._getUITransformComp();i.activeInHierarchy&&n&&this._usefulLayoutObj.push(n)}},o._addEventListeners=function(){ct.on(_t.AFTER_UPDATE,this.updateLayout,this),this.node.on($.SIZE_CHANGED,this._resized,this),this.node.on($.ANCHOR_CHANGED,this._doLayoutDirty,this),this.node.on($.CHILD_ADDED,this._childAdded,this),this.node.on($.CHILD_REMOVED,this._childRemoved,this),this.node.on($.CHILDREN_ORDER_CHANGED,this._childrenChanged,this),this.node.on("childrenSiblingOrderChanged",this.updateLayout,this),this._addChildrenEventListeners()},o._removeEventListeners=function(){ct.off(_t.AFTER_UPDATE,this.updateLayout,this),this.node.off($.SIZE_CHANGED,this._resized,this),this.node.off($.ANCHOR_CHANGED,this._doLayoutDirty,this),this.node.off($.CHILD_ADDED,this._childAdded,this),this.node.off($.CHILD_REMOVED,this._childRemoved,this),this.node.off($.CHILDREN_ORDER_CHANGED,this._childrenChanged,this),this.node.off("childrenSiblingOrderChanged",this.updateLayout,this),this._removeChildrenEventListeners()},o._addChildrenEventListeners=function(){for(var t=this.node.children,e=0;e<t.length;++e){var i=t[e];i.on($.SIZE_CHANGED,this._doLayoutDirty,this),i.on($.TRANSFORM_CHANGED,this._transformDirty,this),i.on($.ANCHOR_CHANGED,this._doLayoutDirty,this),i.on($.ACTIVE_IN_HIERARCHY_CHANGED,this._childrenChanged,this)}},o._removeChildrenEventListeners=function(){for(var t=this.node.children,e=0;e<t.length;++e){var i=t[e];i.off($.SIZE_CHANGED,this._doLayoutDirty,this),i.off($.TRANSFORM_CHANGED,this._transformDirty,this),i.off($.ANCHOR_CHANGED,this._doLayoutDirty,this),i.off($.ACTIVE_IN_HIERARCHY_CHANGED,this._childrenChanged,this)}},o._childAdded=function(t){t.on($.SIZE_CHANGED,this._doLayoutDirty,this),t.on($.TRANSFORM_CHANGED,this._transformDirty,this),t.on($.ANCHOR_CHANGED,this._doLayoutDirty,this),t.on($.ACTIVE_IN_HIERARCHY_CHANGED,this._childrenChanged,this),this._childrenChanged()},o._childRemoved=function(t){t.off($.SIZE_CHANGED,this._doLayoutDirty,this),t.off($.TRANSFORM_CHANGED,this._transformDirty,this),t.off($.ANCHOR_CHANGED,this._doLayoutDirty,this),t.off($.ACTIVE_IN_HIERARCHY_CHANGED,this._childrenChanged,this),this._childrenChanged()},o._resized=function(){this._layoutSize.set(this.node._getUITransformComp().contentSize),this._doLayoutDirty()},o._doLayoutHorizontally=function(t,e,i,n){var o=this.node._getUITransformComp().anchorPoint,r=this._getFixedBreakingNum(),s=1,a=this._paddingLeft;this._horizontalDirection===_i.RIGHT_TO_LEFT&&(s=-1,a=this._paddingRight);var h=(this._horizontalDirection-o.x)*t+s*a,c=h-s*this._spacingX,_=0,l=0,u=0,d=0,p=!1,g=this._usefulLayoutObj.length,f=this._cellSize.width,y=this._getPaddingH();this._layoutType!==si.GRID&&this._resizeMode===ai.CHILDREN&&(f=(t-y-(g-1)*this._spacingX)/g);for(var T=this._usefulLayoutObj,v=0;v<T.length;++v){var C=T[v],E=C.node,S=E.scale,m=this._getUsedScaleValue(S.x),O=this._getUsedScaleValue(S.y);this._resizeMode===ai.CHILDREN&&(C.width=f/m,this._layoutType===si.GRID&&(C.height=this._cellSize.height/O));var L=Math.abs(this._horizontalDirection-C.anchorX),A=C.width*m,I=C.height*O;I>u&&(d=Math.max(u,d),l=u||I,u=I),c+=s*(L*A+this._spacingX);var D=s*(1-L)*A;if(e){if(r>0)(p=v/r>0&&v%r==0)&&(l=u>I?u:l);else if(A>t-y)c>h+s*L*A&&(p=!0);else{var b=(1-this._horizontalDirection-o.x)*t,R=c+D+s*(s>0?this._paddingRight:this._paddingLeft);p=Math.abs(R)>Math.abs(b)}p&&(c=h+s*L*A,I!==u&&(l=u),_+=l+this._spacingY,l=u=I)}var N=i(E,C,_);n&&E.setPosition(c,N),c+=D}return l=Math.max(l,u),Math.max(d,_+l)+this._getPaddingV()},o._doLayoutVertically=function(t,e,i,n){var o=this.node._getUITransformComp().anchorPoint,r=this._getFixedBreakingNum(),s=1,a=this._paddingBottom;this._verticalDirection===ci.TOP_TO_BOTTOM&&(s=-1,a=this._paddingTop);var h=(this._verticalDirection-o.y)*t+s*a,c=h-s*this._spacingY,_=0,l=0,u=0,d=0,p=!1,g=this._usefulLayoutObj.length,f=this._cellSize.height,y=this._getPaddingV();this._layoutType!==si.GRID&&this._resizeMode===ai.CHILDREN&&(f=(t-y-(g-1)*this._spacingY)/g);for(var T=this._usefulLayoutObj,v=0;v<T.length;++v){var C=T[v],E=C.node,S=E.scale,m=this._getUsedScaleValue(S.x),O=this._getUsedScaleValue(S.y);this._resizeMode===ai.CHILDREN&&(C.height=f/O,this._layoutType===si.GRID&&(C.width=this._cellSize.width/m));var L=Math.abs(this._verticalDirection-C.anchorY),A=C.width*m,I=C.height*O;A>_&&(l=Math.max(_,l),u=_||A,_=A),c+=s*(L*I+this._spacingY);var D=s*(1-L)*I;if(e){if(r>0)(p=v/r>0&&v%r==0)&&(u=_>I?_:u);else if(I>t-y)c>h+s*L*I&&(p=!0);else{var b=(1-this._verticalDirection-o.y)*t,R=c+D+s*(s>0?this._paddingTop:this._paddingBottom);p=Math.abs(R)>Math.abs(b)}p&&(c=h+s*L*I,A!==_&&(u=_),d+=u+this._spacingX,u=_=A)}var N=i(E,C,d);n&&(E.getPosition(Ii),E.setPosition(N,c,Ii.z)),c+=D}return u=Math.max(u,_),Math.max(l,d+u)+this._getPaddingH()},o._doLayoutGridAxisHorizontal=function(t,e){var i=this,n=e.width,o=1,r=-t.y*e.height,s=this._paddingBottom;this._verticalDirection===ci.TOP_TO_BOTTOM&&(o=-1,r=(1-t.y)*e.height,s=this._paddingTop);var a=function(t,e,n){return r+o*(n+(1-e.anchorY)*e.height*i._getUsedScaleValue(t.scale.y)+s)},h=0;this._resizeMode===ai.CONTAINER&&(h=this._doLayoutHorizontally(n,!0,a,!1),r=-t.y*h,this._verticalDirection===ci.TOP_TO_BOTTOM&&(o=-1,r=(1-t.y)*h)),this._doLayoutHorizontally(n,!0,a,!0),this._resizeMode===ai.CONTAINER&&this.node._getUITransformComp().setContentSize(n,h)},o._doLayoutGridAxisVertical=function(t,e){var i=this,n=e.height,o=1,r=-t.x*e.width,s=this._paddingLeft;this._horizontalDirection===_i.RIGHT_TO_LEFT&&(o=-1,r=(1-t.x)*e.width,s=this._paddingRight);var a=function(t,e,n){return r+o*(n+(1-e.anchorX)*e.width*i._getUsedScaleValue(t.scale.x)+s)},h=0;this._resizeMode===ai.CONTAINER&&(h=this._doLayoutVertically(n,!0,a,!1),r=-t.x*h,this._horizontalDirection===_i.RIGHT_TO_LEFT&&(o=-1,r=(1-t.x)*h)),this._doLayoutVertically(n,!0,a,!0),this._resizeMode===ai.CONTAINER&&this.node._getUITransformComp().setContentSize(h,n)},o._doLayoutGrid=function(){var t=this.node._getUITransformComp(),e=t.anchorPoint,i=t.contentSize;this.startAxis===hi.HORIZONTAL?this._doLayoutGridAxisHorizontal(e,i):this.startAxis===hi.VERTICAL&&this._doLayoutGridAxisVertical(e,i)},o._getHorizontalBaseWidth=function(){var t=this._usefulLayoutObj,e=0,i=t.length;if(this._resizeMode===ai.CONTAINER){for(var n=0;n<t.length;++n){var o=t[n],r=o.node.scale;e+=o.width*this._getUsedScaleValue(r.x)}e+=(i-1)*this._spacingX+this._getPaddingH()}else e=this.node._getUITransformComp().width;return e},o._getVerticalBaseHeight=function(){var t=this._usefulLayoutObj,e=0,i=t.length;if(this._resizeMode===ai.CONTAINER){for(var n=0;n<t.length;++n){var o=t[n],r=o.node.scale;e+=o.height*this._getUsedScaleValue(r.y)}e+=(i-1)*this._spacingY+this._getPaddingV()}else e=this.node._getUITransformComp().height;return e},o._doLayout=function(){var t=this;if(this._init&&!this._childrenDirty||(this._checkUsefulObj(),this._init=!0,this._childrenDirty=!1),this._layoutType===si.HORIZONTAL){var e=this._getHorizontalBaseWidth();this._doLayoutHorizontally(e,!1,(function(e){return(t._isAlign?T.ZERO:e.position).y}),!0),this.node._getUITransformComp().width=e}else if(this._layoutType===si.VERTICAL){var i=this._getVerticalBaseHeight();this._doLayoutVertically(i,!1,(function(e){return(t._isAlign?T.ZERO:e.position).x}),!0),this.node._getUITransformComp().height=i}else this._layoutType===si.GRID&&this._doLayoutGrid()},o._getUsedScaleValue=function(t){return this._affectedByScale?Math.abs(t):1},o._transformDirty=function(t){t&K.SCALE&&t&K.POSITION&&this._affectedByScale&&this._doLayoutDirty()},o._doLayoutDirty=function(){this._layoutDirty=!0},o._childrenChanged=function(){this._childrenDirty=!0,this._doLayoutDirty()},o._getPaddingH=function(){return this._paddingLeft+this._paddingRight},o._getPaddingV=function(){return this._paddingTop+this._paddingBottom},o._getFixedBreakingNum=function(){if(this._layoutType!==si.GRID||this._constraint===li.NONE||this._constraintNum<=0)return 0;var t=this._constraint===li.FIXED_ROW?Math.ceil(this._usefulLayoutObj.length/this._constraintNum):this._constraintNum;return this._startAxis===hi.VERTICAL&&(t=this._constraint===li.FIXED_COL?Math.ceil(this._usefulLayoutObj.length/this._constraintNum):this._constraintNum),t},n(e,[{key:"alignHorizontal",get:function(){return this._isAlign},set:function(t){this._layoutType===si.HORIZONTAL&&(this._isAlign=t,this._doLayoutDirty())}},{key:"alignVertical",get:function(){return this._isAlign},set:function(t){this._layoutType===si.VERTICAL&&(this._isAlign=t,this._doLayoutDirty())}},{key:"type",get:function(){return this._layoutType},set:function(t){this._layoutType=t,this._doLayoutDirty()}},{key:"resizeMode",get:function(){return this._resizeMode},set:function(t){this._layoutType!==si.NONE&&(this._resizeMode=t,this._doLayoutDirty())}},{key:"cellSize",get:function(){return this._cellSize},set:function(t){this._cellSize!==t&&(this._cellSize.set(t),this._doLayoutDirty())}},{key:"startAxis",get:function(){return this._startAxis},set:function(t){this._startAxis!==t&&(this._startAxis=t,this._doLayoutDirty())}},{key:"paddingLeft",get:function(){return this._paddingLeft},set:function(t){this._paddingLeft!==t&&(this._paddingLeft=t,this._doLayoutDirty())}},{key:"paddingRight",get:function(){return this._paddingRight},set:function(t){this._paddingRight!==t&&(this._paddingRight=t,this._doLayoutDirty())}},{key:"paddingTop",get:function(){return this._paddingTop},set:function(t){this._paddingTop!==t&&(this._paddingTop=t,this._doLayoutDirty())}},{key:"paddingBottom",get:function(){return this._paddingBottom},set:function(t){this._paddingBottom!==t&&(this._paddingBottom=t,this._doLayoutDirty())}},{key:"spacingX",get:function(){return this._spacingX},set:function(t){this._spacingX!==t&&(this._spacingX=t,this._doLayoutDirty())}},{key:"spacingY",get:function(){return this._spacingY},set:function(t){this._spacingY!==t&&(this._spacingY=t,this._doLayoutDirty())}},{key:"verticalDirection",get:function(){return this._verticalDirection},set:function(t){this._verticalDirection!==t&&(this._verticalDirection=t,this._doLayoutDirty())}},{key:"horizontalDirection",get:function(){return this._horizontalDirection},set:function(t){this._horizontalDirection!==t&&(this._horizontalDirection=t,this._doLayoutDirty())}},{key:"padding",get:function(){return this._paddingLeft},set:function(t){this.paddingLeft===t&&this._paddingRight===t&&this._paddingTop===t&&this._paddingBottom===t||(this._paddingLeft=this._paddingRight=this._paddingTop=this._paddingBottom=t,this._doLayoutDirty())}},{key:"constraint",get:function(){return this._constraint},set:function(t){this._layoutType!==si.NONE&&this._constraint!==t&&(this._constraint=t,this._doLayoutDirty())}},{key:"constraintNum",get:function(){return this._constraintNum},set:function(t){this._constraint!==li.NONE&&this._constraintNum!==t&&(t<=0&&s(16400),this._constraintNum=t,this._doLayoutDirty())}},{key:"affectedByScale",get:function(){return this._affectedByScale},set:function(t){this._affectedByScale=t,this._doLayoutDirty()}}]),e}(et),ri.Type=si,ri.VerticalDirection=ci,ri.HorizontalDirection=_i,ri.ResizeMode=ai,ri.AxisDirection=hi,ri.Constraint=li,o((Fe=ri).prototype,"type",[Pe],Object.getOwnPropertyDescriptor(Fe.prototype,"type"),Fe.prototype),o(Fe.prototype,"resizeMode",[xe],Object.getOwnPropertyDescriptor(Fe.prototype,"resizeMode"),Fe.prototype),o(Fe.prototype,"startAxis",[Me],Object.getOwnPropertyDescriptor(Fe.prototype,"startAxis"),Fe.prototype),o(Fe.prototype,"verticalDirection",[ke],Object.getOwnPropertyDescriptor(Fe.prototype,"verticalDirection"),Fe.prototype),o(Fe.prototype,"horizontalDirection",[ze],Object.getOwnPropertyDescriptor(Fe.prototype,"horizontalDirection"),Fe.prototype),o(Fe.prototype,"constraint",[Ve],Object.getOwnPropertyDescriptor(Fe.prototype,"constraint"),Fe.prototype),Ge=v(Fe.prototype,"_resizeMode",[m],(function(){return ai.NONE})),We=v(Fe.prototype,"_layoutType",[m],(function(){return si.NONE})),je=v(Fe.prototype,"_cellSize",[m],(function(){return new L(40,40)})),Ze=v(Fe.prototype,"_startAxis",[m],(function(){return hi.HORIZONTAL})),Xe=v(Fe.prototype,"_paddingLeft",[m],(function(){return 0})),Ye=v(Fe.prototype,"_paddingRight",[m],(function(){return 0})),Ke=v(Fe.prototype,"_paddingTop",[m],(function(){return 0})),qe=v(Fe.prototype,"_paddingBottom",[m],(function(){return 0})),Qe=v(Fe.prototype,"_spacingX",[m],(function(){return 0})),Je=v(Fe.prototype,"_spacingY",[m],(function(){return 0})),$e=v(Fe.prototype,"_verticalDirection",[m],(function(){return ci.TOP_TO_BOTTOM})),ti=v(Fe.prototype,"_horizontalDirection",[m],(function(){return _i.LEFT_TO_RIGHT})),ei=v(Fe.prototype,"_constraint",[m],(function(){return li.NONE})),ii=v(Fe.prototype,"_constraintNum",[m],(function(){return 2})),ni=v(Fe.prototype,"_affectedByScale",[m],(function(){return!1})),oi=v(Fe.prototype,"_isAlign",[m],(function(){return!1})),Ue=Fe))||Ue)||Ue)||Ue);t({Layout:Di,LayoutComponent:Di}),z.Layout=Di,function(t){t[t.HORIZONTAL=0]="HORIZONTAL",t[t.VERTICAL=1]="VERTICAL",t[t.FILLED=2]="FILLED"}(Ai||(Ai={})),r(Ai);var bi,Ri,Ni,wi,Bi,Hi,Pi,xi,Mi,ki,zi,Vi,Ui,Fi=(di=g("cc.ProgressBar"),pi=C(110),gi=E(rt),fi=f(W),yi=f(Ai),di(Ti=pi(Ti=gi((Li=function(t){function e(){var e;return(e=t.call(this)||this)._barSprite=Ci&&Ci(),e._mode=Ei&&Ei(),e._totalLength=Si&&Si(),e._progress=mi&&mi(),e._reverse=Oi&&Oi(),e}i(e,t);var o=e.prototype;return o.onLoad=function(){this._updateBarStatus()},o._initBarSprite=function(){if(this._barSprite){var t=this._barSprite.node;if(!t)return;var e=this.node._getUITransformComp(),i=e.contentSize,n=e.anchorPoint,o=t._getUITransformComp().contentSize;if(this._barSprite.fillType===W.FillType.RADIAL&&(this._mode=Ai.FILLED),this._mode===Ai.HORIZONTAL?this.totalLength=o.width:this._mode===Ai.VERTICAL?this.totalLength=o.height:this.totalLength=this._barSprite.fillRange,t.parent===this.node){var r=-i.width*n.x;t.setPosition(r,0,0)}}},o._updateBarStatus=function(){if(this._barSprite){var t=this._barSprite.node;if(!t)return;var e=t._getUITransformComp(),i=e.anchorPoint,n=e.contentSize,o=new A(0,.5),r=I(this._progress),a=this._totalLength*r,h=n,c=0,_=0;switch(this._mode){case Ai.HORIZONTAL:this._reverse&&(o=new A(1,.5)),h=new L(a,n.height),c=this._totalLength,_=n.height;break;case Ai.VERTICAL:o=this._reverse?new A(.5,1):new A(.5,0),h=new L(n.width,a),c=n.width,_=this._totalLength}if(this._mode===Ai.FILLED)this._barSprite.type!==W.Type.FILLED?s(16397):(this._reverse&&(a*=-1),this._barSprite.fillRange=a);else if(this._barSprite.type!==W.Type.FILLED){var l=o.x-i.x,u=o.y-i.y,d=new T(t.position);d.add3f(c*l,_*u,0),t.setPosition(d),e.setAnchorPoint(o),e.setContentSize(h)}else s(16398)}},n(e,[{key:"barSprite",get:function(){return this._barSprite},set:function(t){this._barSprite!==t&&(this._barSprite=t,this._initBarSprite())}},{key:"mode",get:function(){return this._mode},set:function(t){if(this._mode!==t&&(this._mode=t,this._barSprite)){var e=this._barSprite.node;if(!e)return;var i=e._getUITransformComp().contentSize;this._mode===Ai.HORIZONTAL?this.totalLength=i.width:this._mode===Ai.VERTICAL?this.totalLength=i.height:this._mode===Ai.FILLED&&(this.totalLength=this._barSprite.fillRange)}}},{key:"totalLength",get:function(){return this._totalLength},set:function(t){this._mode===Ai.FILLED&&(t=I(t)),this._totalLength!==t&&(this._totalLength=t,this._updateBarStatus())}},{key:"progress",get:function(){return this._progress},set:function(t){this._progress!==t&&(this._progress=t,this._updateBarStatus())}},{key:"reverse",get:function(){return this._reverse},set:function(t){this._reverse!==t&&(this._reverse=t,this._barSprite&&(this._barSprite.fillStart=1-this._barSprite.fillStart),this._updateBarStatus())}}]),e}(et),Li.Mode=Ai,o((vi=Li).prototype,"barSprite",[fi],Object.getOwnPropertyDescriptor(vi.prototype,"barSprite"),vi.prototype),o(vi.prototype,"mode",[yi],Object.getOwnPropertyDescriptor(vi.prototype,"mode"),vi.prototype),Ci=v(vi.prototype,"_barSprite",[m],(function(){return null})),Ei=v(vi.prototype,"_mode",[m],(function(){return Ai.HORIZONTAL})),Si=v(vi.prototype,"_totalLength",[m],(function(){return 1})),mi=v(vi.prototype,"_progress",[m],(function(){return.1})),Oi=v(vi.prototype,"_reverse",[m],(function(){return!1})),Ti=vi))||Ti)||Ti)||Ti);t({ProgressBar:Fi,ProgressBarComponent:Fi}),z.ProgressBar=Fi;var Gi,Wi=new T,ji=new T,Zi=new T,Xi=new A,Yi=new p,Ki=new A;!function(t){t[t.HORIZONTAL=0]="HORIZONTAL",t[t.VERTICAL=1]="VERTICAL"}(Gi||(Gi={})),e(Gi);var qi,Qi=(bi=g("cc.ScrollBar"),Ri=C(110),Ni=E(rt),wi=f(W),Bi=f(Gi),bi(Hi=Ri(Hi=Ni((Ui=function(t){function e(){var e;return(e=t.call(this)||this)._scrollView=xi&&xi(),e._handle=Mi&&Mi(),e._direction=ki&&ki(),e._enableAutoHide=zi&&zi(),e._autoHideTime=Vi&&Vi(),e._touching=!1,e._opacity=255,e._autoHideRemainingTime=0,e}i(e,t);var o=e.prototype;return o.hide=function(){this._autoHideRemainingTime=0,this._setOpacity(0)},o.show=function(){this._autoHideRemainingTime=this._autoHideTime,this._opacity=255,this._setOpacity(this._opacity)},o.onScroll=function(t){if(this._scrollView){var e=this._scrollView.content;if(e){var i=e._getUITransformComp().contentSize,n=this._scrollView.node._getUITransformComp().contentSize,o=this.node._getUITransformComp().contentSize;if(!this._conditionalDisableScrollBar(i,n)){this._enableAutoHide&&(this._autoHideRemainingTime=this._autoHideTime,this._setOpacity(this._opacity));var r=0,s=0,a=0,h=0,c=0,_=Ki;_.set(0,0),this._direction===Gi.HORIZONTAL?(r=i.width,s=n.width,c=o.width,a=t.x,this._convertToScrollViewSpace(_,e),h=-_.x):this._direction===Gi.VERTICAL&&(r=i.height,s=n.height,c=o.height,a=t.y,this._convertToScrollViewSpace(_,e),h=-_.y);var l=this._calculateLength(r,s,c,a),u=Ki;this._calculatePosition(u,r,s,c,h,a,l),this._updateLength(l),this._updateHandlerPosition(u)}}}},o.setScrollView=function(t){this._scrollView=t},o.onTouchBegan=function(){this._enableAutoHide&&(this._touching=!0)},o.onTouchEnded=function(){if(this._enableAutoHide&&(this._touching=!1,!(this._autoHideTime<=0))){if(this._scrollView){var t=this._scrollView.content;if(t){var e=t._getUITransformComp().contentSize,i=this._scrollView.node._getUITransformComp().contentSize;if(this._conditionalDisableScrollBar(e,i))return}}this._autoHideRemainingTime=this._autoHideTime}},o.onEnable=function(){var t=this.node.getComponent(W);t&&(this._opacity=t.color.a)},o.start=function(){this._enableAutoHide&&this._setOpacity(0)},o.update=function(t){this._processAutoHide(t)},o._convertToScrollViewSpace=function(t,e){var i=this._scrollView&&this._scrollView.node._getUITransformComp(),n=e._getUITransformComp();if(i&&n){Wi.set(-n.anchorX*n.width,-n.anchorY*n.height,0),n.convertToWorldSpaceAR(Wi,ji);var o=i.convertToNodeSpaceAR(ji);o.x+=i.anchorX*i.width,o.y+=i.anchorY*i.height,t.set(o.x,o.y)}else t.set(A.ZERO)},o._setOpacity=function(t){if(this._handle){var e=this.node.getComponent(W);e&&(Yi.set(e.color),Yi.a=t,e.color=Yi),(e=this._handle.getComponent(W))&&(Yi.set(e.color),Yi.a=t,e.color=Yi)}},o._updateHandlerPosition=function(t){if(this._handle){var e=Zi;this._fixupHandlerPosition(e),this._handle.node.setPosition(t.x+e.x,t.y+e.y,e.z)}},o._fixupHandlerPosition=function(t){var e=this.node._getUITransformComp(),i=e.contentSize,n=e.anchorPoint,o=this.handle.node._getUITransformComp().contentSize,r=this.handle.node.parent;T.set(Wi,-i.width*n.x,-i.height*n.y,0);var s=this.node._getUITransformComp().convertToWorldSpaceAR(Wi,ji),a=t;a.set(0,0,0),r._getUITransformComp().convertToNodeSpaceAR(s,a),this.direction===Gi.HORIZONTAL?a.set(a.x,a.y+(i.height-o.height)/2,a.z):this.direction===Gi.VERTICAL&&a.set(a.x+(i.width-o.width)/2,a.y,a.z),this.handle.node.setPosition(a)},o._conditionalDisableScrollBar=function(t,e){return t.width<=e.width&&this._direction===Gi.HORIZONTAL||t.height<=e.height&&this._direction===Gi.VERTICAL},o._calculateLength=function(t,e,i,n){var o=t;return n&&(o+=20*(n>0?n:-n)),i*(e/o)},o._calculatePosition=function(t,e,i,n,o,r,s){var a=e-i;r&&(a+=Math.abs(r));var h=0;a&&(h=I(h=o/a));var c=(n-s)*h;this._direction===Gi.VERTICAL?t.set(0,c):t.set(c,0)},o._updateLength=function(t){if(this._handle){var e=this._handle.node._getUITransformComp(),i=e.contentSize,n=e.anchorPoint;n.x===Xi.x&&n.y===Xi.y||e.setAnchorPoint(Xi),this._direction===Gi.HORIZONTAL?e.setContentSize(t,i.height):e.setContentSize(i.width,t)}},o._processAutoHide=function(t){if(this._enableAutoHide&&!(this._autoHideRemainingTime<=0)&&!this._touching&&(this._autoHideRemainingTime-=t,this._autoHideRemainingTime<=this._autoHideTime)){this._autoHideRemainingTime=Math.max(0,this._autoHideRemainingTime);var e=this._opacity*(this._autoHideRemainingTime/this._autoHideTime);this._setOpacity(e)}},n(e,[{key:"handle",get:function(){return this._handle},set:function(t){this._handle!==t&&(this._handle=t,this.onScroll(A.ZERO))}},{key:"direction",get:function(){return this._direction},set:function(t){this._direction!==t&&(this._direction=t,this.onScroll(A.ZERO))}},{key:"enableAutoHide",get:function(){return this._enableAutoHide},set:function(t){this._enableAutoHide!==t&&(this._enableAutoHide=t,this._enableAutoHide&&this._setOpacity(0))}},{key:"autoHideTime",get:function(){return this._autoHideTime},set:function(t){this._autoHideTime!==t&&(this._autoHideTime=t)}}]),e}(et),Ui.Direction=Gi,o((Pi=Ui).prototype,"handle",[wi],Object.getOwnPropertyDescriptor(Pi.prototype,"handle"),Pi.prototype),o(Pi.prototype,"direction",[Bi],Object.getOwnPropertyDescriptor(Pi.prototype,"direction"),Pi.prototype),xi=v(Pi.prototype,"_scrollView",[m],(function(){return null})),Mi=v(Pi.prototype,"_handle",[m],(function(){return null})),ki=v(Pi.prototype,"_direction",[m],(function(){return Gi.HORIZONTAL})),zi=v(Pi.prototype,"_enableAutoHide",[m],(function(){return!1})),Vi=v(Pi.prototype,"_autoHideTime",[m],(function(){return 1})),Hi=Pi))||Hi)||Hi)||Hi);t({ScrollBar:Qi,ScrollBarComponent:Qi}),z.ScrollBar=Qi;var Ji,$i,tn,en,nn,on,rn,sn,an,hn,cn,_n,ln,un,dn,pn,gn,fn,yn,Tn,vn,Cn=t("ViewGroup",g("cc.ViewGroup")(qi=C(110)(qi=function(t){function e(){return t.apply(this,arguments)||this}return i(e,t),e}(et))||qi)||qi);z.ViewGroup=Cn;var En,Sn,mn=1e-4,On=S(),Ln=S(),An=D(),In=D(),Dn=function(){return(new Date).getMilliseconds()},bn={"scroll-to-top":0,"scroll-to-bottom":1,"scroll-to-left":2,"scroll-to-right":3,scrolling:4,"bounce-bottom":6,"bounce-left":7,"bounce-right":8,"bounce-top":5,"scroll-ended":9,"touch-up":10,"scroll-ended-with-threshold":11,"scroll-began":12},Rn={anchor:D(),applyToHorizontal:!1,applyToVertical:!1},Nn=function(t,e,i,n){Rn.anchor.set(t,e),Rn.applyToHorizontal=i,Rn.applyToVertical=n};!function(t){t.NONE="",t.SCROLL_TO_TOP="scroll-to-top",t.SCROLL_TO_BOTTOM="scroll-to-bottom",t.SCROLL_TO_LEFT="scroll-to-left",t.SCROLL_TO_RIGHT="scroll-to-right",t.SCROLL_BEGAN="scroll-began",t.SCROLL_ENDED="scroll-ended",t.BOUNCE_TOP="bounce-top",t.BOUNCE_BOTTOM="bounce-bottom",t.BOUNCE_LEFT="bounce-left",t.BOUNCE_RIGHT="bounce-right",t.SCROLLING="scrolling",t.SCROLL_ENG_WITH_THRESHOLD="scroll-ended-with-threshold",t.TOUCH_UP="touch-up"}(En||(En={})),function(t){t[t.NONE=0]="NONE",t[t.LEFT=1]="LEFT",t[t.RIGHT=2]="RIGHT"}(Sn||(Sn={}));var wn,Bn,Hn,Pn,xn,Mn,kn,zn,Vn,Un,Fn,Gn,Wn,jn=(Ji=g("cc.ScrollView"),$i=C(110),tn=E(rt),en=f(q),nn=f(Qi),on=f(Qi),rn=f([tt]),Ji(sn=$i(sn=tn((vn=function(t){function e(){var e;return(e=t.call(this)||this).bounceDuration=hn&&hn(),e.brake=cn&&cn(),e.elastic=_n&&_n(),e.inertia=ln&&ln(),e.horizontal=un&&un(),e.vertical=dn&&dn(),e.cancelInnerEvents=pn&&pn(),e.scrollEvents=gn&&gn(),e._autoScrolling=!1,e._scrolling=!1,e._content=fn&&fn(),e._horizontalScrollBar=yn&&yn(),e._verticalScrollBar=Tn&&Tn(),e._topBoundary=0,e._bottomBoundary=0,e._leftBoundary=0,e._rightBoundary=0,e._touchMoveDisplacements=[],e._touchMoveTimeDeltas=[],e._touchMovePreviousTimestamp=0,e._touchMoved=!1,e._autoScrollAttenuate=!1,e._autoScrollStartPosition=new T,e._autoScrollTargetDelta=new T,e._autoScrollTotalTime=0,e._autoScrollAccumulatedTime=0,e._autoScrollCurrentlyOutOfBoundary=!1,e._autoScrollBraking=!1,e._autoScrollBrakingStartPosition=new T,e._outOfBoundaryAmount=new T,e._outOfBoundaryAmountDirty=!0,e._stopMouseWheel=!1,e._mouseWheelEventElapsedTime=0,e._isScrollEndedWithThresholdEventFired=!1,e._scrollEventEmitMask=0,e._isBouncing=!1,e._contentPos=new T,e._deltaPos=new T,e._deltaAmount=new T,e._hoverIn=Sn.NONE,e}i(e,t);var o=e.prototype;return o.scrollToBottom=function(t,e){void 0===e&&(e=!0),this._doScroll(0,0,!1,!0,t,e)},o.scrollToTop=function(t,e){void 0===e&&(e=!0),this._doScroll(0,1,!1,!0,t,e)},o.scrollToLeft=function(t,e){void 0===e&&(e=!0),this._doScroll(0,0,!0,!1,t,e)},o.scrollToRight=function(t,e){void 0===e&&(e=!0),this._doScroll(1,0,!0,!1,t,e)},o.scrollToTopLeft=function(t,e){void 0===e&&(e=!0),this._doScroll(0,1,!0,!0,t,e)},o.scrollToTopRight=function(t,e){void 0===e&&(e=!0),this._doScroll(1,1,!0,!0,t,e)},o.scrollToBottomLeft=function(t,e){void 0===e&&(e=!0),this._doScroll(0,0,!0,!0,t,e)},o.scrollToBottomRight=function(t,e){void 0===e&&(e=!0),this._doScroll(1,0,!0,!0,t,e)},o.scrollToOffset=function(t,e,i){void 0===i&&(i=!0);var n=this.getMaxScrollOffset(),o=D();0===n.x?o.x=0:o.x=t.x/n.x,0===n.y?o.y=1:o.y=(n.y-t.y)/n.y,this.scrollTo(o,e,i)},o.getScrollOffset=function(){var t=this._getContentTopBoundary()-this._topBoundary,e=this._getContentLeftBoundary()-this._leftBoundary;return new A(e,t)},o.getMaxScrollOffset=function(){if(!this._content||!this.view)return A.ZERO;var t=this._content._getUITransformComp().contentSize,e=t.width-this.view.width,i=t.height-this.view.height;return new A(e=e>=0?e:0,i=i>=0?i:0)},o.scrollToPercentHorizontal=function(t,e,i){this._doScroll(t,0,!0,!1,e,i)},o.scrollTo=function(t,e,i){this._doScroll(t.x,t.y,!0,!0,e,i)},o.scrollToPercentVertical=function(t,e,i){this._doScroll(0,t,!1,!0,e,i)},o._doScroll=function(t,e,i,n,o,r){void 0===r&&(r=!0),Nn(t,e,i,n);var s=this._calculateMovePercentDelta(Rn);o?this._startAutoScroll(s,o,r):this._moveContent(s)},o.stopAutoScroll=function(){this._autoScrolling=!1,this._autoScrollAccumulatedTime=this._autoScrollTotalTime},o.setContentPosition=function(t){this._setContentPosition(t)},o._setContentPosition=function(t){if(this._content){var e=this._getContentPosition();Math.abs(t.x-e.x)<mn&&Math.abs(t.y-e.y)<mn||(this._content.setPosition(t),this._outOfBoundaryAmountDirty=!0)}},o.getContentPosition=function(){return this._getContentPosition()},o._getContentPosition=function(){return this._content?(this._contentPos.set(this._content.position),this._contentPos):T.ZERO.clone()},o.isScrolling=function(){return this._scrolling},o.isAutoScrolling=function(){return this._autoScrolling},o.getScrollEndedEventTiming=function(){return mn},o.start=function(){this._calculateBoundary(),this._content&&ct.once(_t.BEFORE_DRAW,this._adjustContentOutOfBoundary,this)},o.onEnable=function(){var t=this;t._registerEvent();var e=this._content;if(e){e.on($.SIZE_CHANGED,t._calculateBoundary,t),e.on($.TRANSFORM_CHANGED,t._scaleChanged,t);var i=t.view;i&&(i.node.on($.TRANSFORM_CHANGED,t._scaleChanged,t),i.node.on($.SIZE_CHANGED,t._calculateBoundary,t))}t._calculateBoundary(),t._updateScrollBarState()},o.update=function(t){var e=this._deltaAmount;this._autoScrolling?(this._processAutoScrolling(t),e.x=0,e.y=0):0===e.x&&0===e.y||(this._processDeltaMove(e),e.x=0,e.y=0)},o.onDisable=function(){var t=this;t._unregisterEvent();var e=t.content;if(e){e.off($.SIZE_CHANGED,t._calculateBoundary,t),e.off($.TRANSFORM_CHANGED,t._scaleChanged,t);var i=t.view;i&&(i.node.off($.TRANSFORM_CHANGED,t._scaleChanged,t),i.node.off($.SIZE_CHANGED,t._calculateBoundary,t))}t._deltaAmount.set(0,0),t._hideScrollBar(),t.stopAutoScroll()},o._registerEvent=function(){var t=this,e=t.node;e.on($.TOUCH_START,t._onTouchBegan,t,!0),e.on($.TOUCH_MOVE,t._onTouchMoved,t,!0),e.on($.TOUCH_END,t._onTouchEnded,t,!0),e.on($.TOUCH_CANCEL,t._onTouchCancelled,t,!0),e.on($.MOUSE_WHEEL,t._onMouseWheel,t,!0),e.on(st.XRUI_HOVER_ENTERED,t._xrHoverEnter,t),e.on(st.XRUI_HOVER_EXITED,t._xrHoverExit,t),Y.on(lt.HANDLE_INPUT,t._dispatchEventHandleInput,t),Y.on(lt.GAMEPAD_INPUT,t._dispatchEventHandleInput,t)},o._unregisterEvent=function(){var t=this,e=t.node;e.off($.TOUCH_START,t._onTouchBegan,t,!0),e.off($.TOUCH_MOVE,t._onTouchMoved,t,!0),e.off($.TOUCH_END,t._onTouchEnded,t,!0),e.off($.TOUCH_CANCEL,t._onTouchCancelled,t,!0),e.off($.MOUSE_WHEEL,t._onMouseWheel,t,!0),e.off(st.XRUI_HOVER_ENTERED,t._xrHoverEnter,t),e.off(st.XRUI_HOVER_EXITED,t._xrHoverExit,t),Y.off(lt.HANDLE_INPUT,t._dispatchEventHandleInput,t),Y.off(lt.GAMEPAD_INPUT,t._dispatchEventHandleInput,t)},o._onMouseWheel=function(t,e){var i=this;if(i.enabledInHierarchy&&!i._hasNestedViewGroup(t,e)){var n=t.getScrollY(),o=On;i.vertical?o.set(0,-.1*n,0):i.horizontal&&o.set(-.1*n,0,0),i._mouseWheelEventElapsedTime=0,i._deltaAmount.add(o),i._stopMouseWheel||(i._handlePressLogic(),i.schedule(this._checkMouseWheel,1/60),i._stopMouseWheel=!0),i._stopPropagationIfTargetIsMe(t)}},o._onTouchBegan=function(t,e){var i=this;i.enabledInHierarchy&&i._content&&(i._hasNestedViewGroup(t,e)||(i._handlePressLogic(),i._touchMoved=!1,i._stopPropagationIfTargetIsMe(t)))},o._onTouchMoved=function(t,e){var i=this;if(i.enabledInHierarchy&&i._content&&!i._hasNestedViewGroup(t,e)){var n=t.touch;if(i._handleMoveLogic(n),i.cancelInnerEvents){var o=n.getUILocation(An);if(o.subtract(n.getUIStartLocation(In)),o.length()>7&&!i._touchMoved&&t.target!==i.node){var r=new ut(t.getTouches(),t.bubbles,dt.TOUCH_CANCEL);r.touch=t.touch,r.simulate=!0,t.target.dispatchEvent(r),i._touchMoved=!0}i._stopPropagationIfTargetIsMe(t)}}},o._onTouchEnded=function(t,e){var i=this;if(i.enabledInHierarchy&&i._content&&t&&!i._hasNestedViewGroup(t,e)){i._dispatchEvent(En.TOUCH_UP);var n=t.touch;i._handleReleaseLogic(n),i._touchMoved?t.propagationStopped=!0:i._stopPropagationIfTargetIsMe(t)}},o._onTouchCancelled=function(t,e){var i=this;i.enabledInHierarchy&&i._content&&(i._hasNestedViewGroup(t,e)||(t&&!t.simulate&&i._handleReleaseLogic(t.touch),i._stopPropagationIfTargetIsMe(t)))},o._calculateBoundary=function(){var t=this;if(t._content&&t.view){var e=t._content.getComponent(Di);e&&e.enabledInHierarchy&&e.updateLayout();var i=t.view,n=i.width*i.anchorX,o=i.height*i.anchorY;t._leftBoundary=-n,t._bottomBoundary=-o,t._rightBoundary=t._leftBoundary+i.width,t._topBoundary=t._bottomBoundary+i.height,t._moveContentToTopLeft(i.contentSize)}},o._hasNestedViewGroup=function(t,e){if(!t||t.eventPhase!==pt.CAPTURING_PHASE)return!1;if(e)for(var i=0;i<e.length;i++){var n=e[i];if(this.node===n)return!(!t.target||!t.target.getComponent(Cn));if(n.getComponent(Cn))return!0}return!1},o._startInertiaScroll=function(t){On.set(t),On.multiplyScalar(.7),this._startAttenuatingAutoScroll(On,t)},o._calculateAttenuatedFactor=function(t){return this.brake<=0?1-this.brake:(1-this.brake)*(1/(1+14e-6*t+t*t*8e-9))},o._startAttenuatingAutoScroll=function(t,e){var i=t.clone();if(i.normalize(),this._content&&this.view){var n=this._content._getUITransformComp().contentSize,o=this.view.contentSize,r=n.width-o.width,s=n.height-o.height,a=this._calculateAttenuatedFactor(r),h=this._calculateAttenuatedFactor(s);i.x=i.x*r*(1-this.brake)*a,i.y=i.y*s*h*(1-this.brake),i.z=0}var c=t.length(),_=i.length()/c;this.brake>0&&_>7?(_=Math.sqrt(_),i.set(t),i.multiplyScalar(_+1)):i.add(t);var l=this._calculateAutoScrollTimeByInitialSpeed(e.length());this.brake>0&&_>3&&(l*=_=3),0===this.brake&&_>1&&(l*=_),this._startAutoScroll(i,l,!0)},o._calculateAutoScrollTimeByInitialSpeed=function(t){return Math.sqrt(Math.sqrt(t/5))},o._startAutoScroll=function(t,e,i){void 0===i&&(i=!1);var n=this,o=n._flattenVectorByDirection(t);n._autoScrolling=!0,n._autoScrollTargetDelta=o,n._autoScrollAttenuate=i,T.copy(n._autoScrollStartPosition,n._getContentPosition()),n._autoScrollTotalTime=e,n._autoScrollAccumulatedTime=0,n._autoScrollBraking=!1,n._isScrollEndedWithThresholdEventFired=!1,n._autoScrollBrakingStartPosition.set(0,0,0),n._getHowMuchOutOfBoundary().equals(T.ZERO,mn)||(this._autoScrollCurrentlyOutOfBoundary=!0)},o._calculateTouchMoveVelocity=function(){var t=new T,e=0;if((e=this._touchMoveTimeDeltas.reduce((function(t,e){return t+e}),e))<=0||e>=.5)t.set(T.ZERO);else{var i=On;i.set(0,0,0),i=this._touchMoveDisplacements.reduce((function(t,e){return t.add(e),t}),i),t.set(i.x*(1-this.brake)/e,i.y*(1-this.brake)/e,i.z)}return t},o._flattenVectorByDirection=function(t){return this.horizontal||(t.x=0),this.vertical||(t.y=0),t},o._moveContent=function(t,e){var i=this._flattenVectorByDirection(t);On.set(this._getContentPosition()),On.add(i),On.set(Math.round(1e4*On.x)*mn,Math.round(1e4*On.y)*mn,On.z),this._setContentPosition(On);var n=this._getHowMuchOutOfBoundary();An.set(n.x,n.y),this._updateScrollBar(An),this.elastic&&e&&this._startBounceBackIfNeeded()},o._getContentLeftBoundary=function(){if(!this._content)return-1;var t=this._getContentPosition(),e=this._content._getUITransformComp();return t.x-e.anchorX*e.width},o._getContentRightBoundary=function(){if(!this._content)return-1;var t=this._content._getUITransformComp();return this._getContentLeftBoundary()+t.width},o._getContentTopBoundary=function(){if(!this._content)return-1;var t=this._content._getUITransformComp();return this._getContentBottomBoundary()+t.height},o._getContentBottomBoundary=function(){if(!this._content)return-1;var t=this._getContentPosition(),e=this._content._getUITransformComp();return t.y-e.anchorY*e.height},o._getHowMuchOutOfBoundary=function(t){if(t||(t=T.ZERO),t.equals(T.ZERO,mn)&&!this._outOfBoundaryAmountDirty)return this._outOfBoundaryAmount;var e=new T,i=this._getContentLeftBoundary(),n=this._getContentRightBoundary();i+t.x>this._leftBoundary?e.x=this._leftBoundary-(i+t.x):n+t.x<this._rightBoundary&&(e.x=this._rightBoundary-(n+t.x));var o=this._getContentTopBoundary(),r=this._getContentBottomBoundary();return o+t.y<this._topBoundary?e.y=this._topBoundary-(o+t.y):r+t.y>this._bottomBoundary&&(e.y=this._bottomBoundary-(r+t.y)),t.equals(T.ZERO,mn)&&(this._outOfBoundaryAmount=e,this._outOfBoundaryAmountDirty=!1),this._clampDelta(e),e},o._updateScrollBar=function(t){this._horizontalScrollBar&&this._horizontalScrollBar.isValid&&this._horizontalScrollBar.onScroll(t),this._verticalScrollBar&&this._verticalScrollBar.isValid&&this._verticalScrollBar.onScroll(t)},o._onScrollBarTouchBegan=function(){this._horizontalScrollBar&&this._horizontalScrollBar.isValid&&this._horizontalScrollBar.onTouchBegan(),this._verticalScrollBar&&this._verticalScrollBar.isValid&&this._verticalScrollBar.onTouchBegan()},o._onScrollBarTouchEnded=function(){this._horizontalScrollBar&&this._horizontalScrollBar.isValid&&this._horizontalScrollBar.onTouchEnded(),this._verticalScrollBar&&this._verticalScrollBar.isValid&&this._verticalScrollBar.onTouchEnded()},o._dispatchEvent=function(t){if(t===En.SCROLL_ENDED)this._scrollEventEmitMask=0;else if(t===En.SCROLL_TO_TOP||t===En.SCROLL_TO_BOTTOM||t===En.SCROLL_TO_LEFT||t===En.SCROLL_TO_RIGHT){var e=1<<bn[t];if(this._scrollEventEmitMask&e)return;this._scrollEventEmitMask|=e}tt.emitEvents(this.scrollEvents,this,bn[t]),this.node.emit(t,this)},o._adjustContentOutOfBoundary=function(){if(this._content){this._outOfBoundaryAmountDirty=!0;var t=this._getHowMuchOutOfBoundary();!t.equals(T.ZERO,mn)&&(On.set(this._getContentPosition()),On.add(t),this._setContentPosition(On),this._updateScrollBar(A.ZERO))}},o._hideScrollBar=function(){this._horizontalScrollBar&&this._horizontalScrollBar.isValid&&this._horizontalScrollBar.hide(),this._verticalScrollBar&&this._verticalScrollBar.isValid&&this._verticalScrollBar.hide()},o._updateScrollBarState=function(){var t=this;if(t._content&&t.view){var e=t.view,i=t._content._getUITransformComp(),n=t._verticalScrollBar;n&&n.isValid&&(i.height<e.height||b(i.height,e.height)?n.hide():n.show());var o=t._horizontalScrollBar;o&&o.isValid&&(i.width<e.width||b(i.width,e.width)?o.hide():o.show())}},o._stopPropagationIfTargetIsMe=function(t){t.eventPhase===pt.AT_TARGET&&t.target===this.node&&(t.propagationStopped=!0)},o._processDeltaMove=function(t){this._scrollChildren(t),this._gatherTouchMove(t)},o._handleMoveLogic=function(t){this._getLocalAxisAlignDelta(this._deltaPos,t),this._deltaAmount.add(this._deltaPos)},o._handleReleaseLogic=function(t){var e=this;e._getLocalAxisAlignDelta(e._deltaPos,t),e._gatherTouchMove(e._deltaPos),e._processInertiaScroll(),e._scrolling&&(e._scrolling=!1,e._autoScrolling||e._dispatchEvent(En.SCROLL_ENDED))},o._getLocalAxisAlignDelta=function(t,e){var i=this.node._getUITransformComp();i&&(e.getUILocation(An),e.getUIPreviousLocation(In),On.set(An.x,An.y,0),Ln.set(In.x,In.y,0),i.convertToNodeSpaceAR(On,On),i.convertToNodeSpaceAR(Ln,Ln),T.subtract(t,On,Ln))},o._scrollChildren=function(t){var e=this;e._clampDelta(t);var i,n=t;e.elastic&&(i=e._getHowMuchOutOfBoundary(),n.x*=0===i.x?1:.5,n.y*=0===i.y?1:.5),e.elastic||(i=e._getHowMuchOutOfBoundary(n),n.add(i));var o=En.NONE,r=En.NONE;if(e._content){var s=e._content._getUITransformComp(),a=s.anchorX,h=s.anchorY,c=s.width,_=s.height,l=e._content.position||T.ZERO;e.vertical&&(n.y>0?l.y-h*_+n.y>=e._bottomBoundary&&(o=En.SCROLL_TO_BOTTOM):n.y<0&&l.y-h*_+_+n.y<=e._topBoundary&&(o=En.SCROLL_TO_TOP)),e.horizontal&&(n.x<0?l.x-a*c+c+n.x<=e._rightBoundary&&(r=En.SCROLL_TO_RIGHT):n.x>0&&l.x-a*c+n.x>=e._leftBoundary&&(r=En.SCROLL_TO_LEFT))}e._moveContent(n,!1),(e.horizontal&&0!==n.x||e.vertical&&0!==n.y)&&(e._scrolling||(e._scrolling=!0,e._dispatchEvent(En.SCROLL_BEGAN)),e._dispatchEvent(En.SCROLLING)),o!==En.NONE&&e._dispatchEvent(o),r!==En.NONE&&e._dispatchEvent(r)},o._handlePressLogic=function(){var t=this;t._autoScrolling&&t._dispatchEvent(En.SCROLL_ENDED),t._autoScrolling=!1,t._isBouncing=!1,t._touchMovePreviousTimestamp=Dn(),t._touchMoveDisplacements.length=0,t._touchMoveTimeDeltas.length=0,t._onScrollBarTouchBegan()},o._clampDelta=function(t){if(this._content&&this.view){var e=this.view.contentSize,i=this._content._getUITransformComp();i.width<e.width&&(t.x=0),i.height<e.height&&(t.y=0)}},o._gatherTouchMove=function(t){var e=this,i=t.clone();for(e._clampDelta(i);e._touchMoveDisplacements.length>=5;)e._touchMoveDisplacements.shift(),e._touchMoveTimeDeltas.shift();e._touchMoveDisplacements.push(i);var n=Dn();e._touchMoveTimeDeltas.push((n-e._touchMovePreviousTimestamp)/1e3),e._touchMovePreviousTimestamp=n},o._startBounceBackIfNeeded=function(){var t=this;if(!t.elastic)return!1;var e=t._getHowMuchOutOfBoundary();if(t._clampDelta(e),e.equals(T.ZERO,mn))return!1;var i=Math.max(t.bounceDuration,0);return t._startAutoScroll(e,i,!0),t._isBouncing||(e.y>0&&t._dispatchEvent(En.BOUNCE_TOP),e.y<0&&t._dispatchEvent(En.BOUNCE_BOTTOM),e.x>0&&t._dispatchEvent(En.BOUNCE_RIGHT),e.x<0&&t._dispatchEvent(En.BOUNCE_LEFT),t._isBouncing=!0),!0},o._processInertiaScroll=function(){if(!this._startBounceBackIfNeeded()&&this.inertia){var t=this._calculateTouchMoveVelocity();!t.equals(T.ZERO,mn)&&this.brake<1&&this._startInertiaScroll(t)}this._onScrollBarTouchEnded()},o._isOutOfBoundary=function(){return!this._getHowMuchOutOfBoundary().equals(T.ZERO,mn)},o._isNecessaryAutoScrollBrake=function(){var t=this;if(t._autoScrollBraking)return!0;if(t._isOutOfBoundary()){if(!t._autoScrollCurrentlyOutOfBoundary)return t._autoScrollCurrentlyOutOfBoundary=!0,t._autoScrollBraking=!0,T.copy(t._autoScrollBrakingStartPosition,t._getContentPosition()),!0}else t._autoScrollCurrentlyOutOfBoundary=!1;return!1},o._processAutoScrolling=function(t){var e=this,i=e._isNecessaryAutoScrollBrake(),n=i?.05:1;e._autoScrollAccumulatedTime+=t*(1/n);var o,r=Math.min(1,e._autoScrollAccumulatedTime/e._autoScrollTotalTime);e._autoScrollAttenuate&&(o=r,r=(o-=1)*o*o*o*o+1);var s=e._autoScrollTargetDelta.clone();s.multiplyScalar(r);var a=e._autoScrollStartPosition.clone();a.add(s);var h=Math.abs(r-1)<=mn;if(Math.abs(r-1)<=e.getScrollEndedEventTiming()&&!e._isScrollEndedWithThresholdEventFired&&(e._dispatchEvent(En.SCROLL_ENG_WITH_THRESHOLD),e._isScrollEndedWithThresholdEventFired=!0),e.elastic){var c=a.clone();c.subtract(e._autoScrollBrakingStartPosition),i&&c.multiplyScalar(n),a.set(e._autoScrollBrakingStartPosition),a.add(c)}else{var _=a.clone();_.subtract(e.getContentPosition());var l=e._getHowMuchOutOfBoundary(_);l.equals(T.ZERO,mn)||(a.add(l),h=!0)}h&&(e._autoScrolling=!1);var u=a.clone();u.subtract(e._getContentPosition()),e._clampDelta(u),e._moveContent(u,h),e._dispatchEvent(En.SCROLLING),e._autoScrolling||(e._isBouncing=!1,e._scrolling=!1,e._dispatchEvent(En.SCROLL_ENDED))},o._checkMouseWheel=function(t){var e=this;if(!e._getHowMuchOutOfBoundary().equals(T.ZERO,mn))return e._processInertiaScroll(),e._scrolling&&(e._scrolling=!1,e._autoScrolling||e._dispatchEvent(En.SCROLL_ENDED)),e.unschedule(e._checkMouseWheel),void(e._stopMouseWheel=!1);e._mouseWheelEventElapsedTime+=t,e._mouseWheelEventElapsedTime>.1&&(e._onScrollBarTouchEnded(),e._scrolling&&(e._scrolling=!1,e._autoScrolling||e._dispatchEvent(En.SCROLL_ENDED)),e.unschedule(e._checkMouseWheel),e._stopMouseWheel=!1)},o._calculateMovePercentDelta=function(t){var e=t.anchor,i=t.applyToHorizontal,n=t.applyToVertical,o=this;o._calculateBoundary(),e.clampf(A.ZERO,A.ONE);var r=o._getContentBottomBoundary()-o._bottomBoundary;r=-r;var s=o._getContentLeftBoundary()-o._leftBoundary;s=-s;var a=new T;if(o._content&&o.view){var h=0,c=o._content._getUITransformComp().contentSize,_=o.view.contentSize;i&&(h=c.width-_.width,a.x=s-h*e.x),n&&(h=c.height-_.height,a.y=r-h*e.y)}return a},o._moveContentToTopLeft=function(t){var e=this,i=e._getContentBottomBoundary()-e._bottomBoundary;i=-i;var n=new T,o=0,r=e._getContentLeftBoundary()-e._leftBoundary;if(r=-r,e._content){var s=e._content._getUITransformComp().contentSize;s.height<t.height&&(o=s.height-t.height,n.y=i-o),s.width<t.width&&(o=s.width-t.width,n.x=r)}e._updateScrollBarState(),e._moveContent(n),e._adjustContentOutOfBoundary()},o._scaleChanged=function(t){t===K.SCALE&&this._calculateBoundary()},o._xrHoverEnter=function(t){t.deviceType===ht.Left?this._hoverIn=Sn.LEFT:t.deviceType===ht.Right&&(this._hoverIn=Sn.RIGHT)},o._xrHoverExit=function(){this._hoverIn=Sn.NONE},o._dispatchEventHandleInput=function(t){var e,i;t instanceof gt?e=t.gamepad:t instanceof ft&&(e=t.handleInputDevice),this.enabledInHierarchy&&this._hoverIn!==Sn.NONE&&(this._hoverIn===Sn.LEFT?(i=e.leftStick.getValue()).equals(A.ZERO)||this._xrThumbStickMove(i):this._hoverIn===Sn.RIGHT&&((i=e.rightStick.getValue()).equals(A.ZERO)||this._xrThumbStickMove(i)))},o._xrThumbStickMove=function(t){var e=this;if(e.enabledInHierarchy){var i=t.y,n=On;e.vertical?n.set(0,-62.5*i,0):e.horizontal&&n.set(-62.5*i,0,0),e._mouseWheelEventElapsedTime=0,e._deltaAmount.add(n),e._stopMouseWheel||(e._handlePressLogic(),e.schedule(e._checkMouseWheel,1/60,NaN,0),e._stopMouseWheel=!0)}},n(e,[{key:"content",get:function(){return this._content},set:function(t){if(this._content!==t){var e=t&&t.parent&&t.parent._getUITransformComp();!t||t&&e?(this._content=t,this._calculateBoundary()):a(4302)}}},{key:"horizontalScrollBar",get:function(){var t=this._horizontalScrollBar;return t&&!t.isValid&&h(4303,"horizontal",this.node.name),t},set:function(t){this._horizontalScrollBar!==t&&(this._horizontalScrollBar=t,this._horizontalScrollBar&&(this._horizontalScrollBar.setScrollView(this),this._updateScrollBar(A.ZERO)))}},{key:"verticalScrollBar",get:function(){var t=this._verticalScrollBar;return t&&!t.isValid&&h(4303,"vertical",this.node.name),t},set:function(t){this._verticalScrollBar!==t&&(this._verticalScrollBar=t,this._verticalScrollBar&&(this._verticalScrollBar.setScrollView(this),this._updateScrollBar(A.ZERO)))}},{key:"view",get:function(){var t=this._content&&this._content.parent;return t?t._getUITransformComp():null}}]),e}(Cn),vn.EventType=En,hn=v((an=vn).prototype,"bounceDuration",[m],(function(){return 1})),cn=v(an.prototype,"brake",[m],(function(){return.5})),_n=v(an.prototype,"elastic",[m],(function(){return!0})),ln=v(an.prototype,"inertia",[m],(function(){return!0})),o(an.prototype,"content",[en],Object.getOwnPropertyDescriptor(an.prototype,"content"),an.prototype),un=v(an.prototype,"horizontal",[m],(function(){return!0})),o(an.prototype,"horizontalScrollBar",[nn],Object.getOwnPropertyDescriptor(an.prototype,"horizontalScrollBar"),an.prototype),dn=v(an.prototype,"vertical",[m],(function(){return!0})),o(an.prototype,"verticalScrollBar",[on],Object.getOwnPropertyDescriptor(an.prototype,"verticalScrollBar"),an.prototype),pn=v(an.prototype,"cancelInnerEvents",[m],(function(){return!0})),gn=v(an.prototype,"scrollEvents",[rn,m],(function(){return[]})),fn=v(an.prototype,"_content",[m],(function(){return null})),yn=v(an.prototype,"_horizontalScrollBar",[m],(function(){return null})),Tn=v(an.prototype,"_verticalScrollBar",[m],(function(){return null})),sn=an))||sn)||sn)||sn);t({ScrollView:jn,ScrollViewComponent:jn}),z.ScrollView=jn;var Zn,Xn=new T;!function(t){t[t.Horizontal=0]="Horizontal",t[t.Vertical=1]="Vertical"}(Zn||(Zn={})),e(Zn);var Yn,Kn,qn,Qn,Jn,$n,to,eo,io,no,oo,ro,so=(wn=g("cc.Slider"),Bn=C(110),Hn=E(rt),Pn=f(W),xn=f(Zn),Mn=f([tt]),wn(kn=Bn(kn=Hn((Wn=function(t){function e(){var e;return(e=t.call(this)||this).slideEvents=Vn&&Vn(),e._handle=Un&&Un(),e._direction=Fn&&Fn(),e._progress=Gn&&Gn(),e._offset=new T,e._dragging=!1,e._touchHandle=!1,e._handleLocalPos=new T,e._touchPos=new T,e}i(e,t);var o=e.prototype;return o.__preload=function(){this._updateHandlePosition()},o.onEnable=function(){var t=this,e=t.node,i=t._handle;if(t._updateHandlePosition(),e.on($.TOUCH_START,t._onTouchBegan,t),e.on($.TOUCH_MOVE,t._onTouchMoved,t),e.on($.TOUCH_END,t._onTouchEnded,t),e.on($.TOUCH_CANCEL,t._onTouchCancelled,t),e.on(st.XRUI_HOVER_STAY,t._xrHoverStay,t),e.on(st.XRUI_CLICK,t._xrClick,t),e.on(st.XRUI_UNCLICK,t._xrUnClick,t),i&&i.isValid){var n=i.node;n.on($.TOUCH_START,t._onHandleDragStart,t),n.on($.TOUCH_MOVE,t._onTouchMoved,t),n.on($.TOUCH_END,t._onTouchEnded,t)}},o.onDisable=function(){var t=this,e=t.node,i=t._handle;if(e.off($.TOUCH_START,t._onTouchBegan,t),e.off($.TOUCH_MOVE,t._onTouchMoved,t),e.off($.TOUCH_END,t._onTouchEnded,t),e.off($.TOUCH_CANCEL,t._onTouchCancelled,t),e.off(st.XRUI_HOVER_STAY,t._xrHoverStay,t),e.off(st.XRUI_CLICK,t._xrClick,t),e.off(st.XRUI_UNCLICK,t._xrUnClick,t),i&&i.isValid){var n=i.node;n.off($.TOUCH_START,t._onHandleDragStart,t),n.off($.TOUCH_MOVE,t._onTouchMoved,t),n.off($.TOUCH_END,t._onTouchEnded,t)}},o._onHandleDragStart=function(t){if(t&&this._handle&&this._handle.node._getUITransformComp()){this._dragging=!0,this._touchHandle=!0;var e=t.touch.getUILocation();T.set(this._touchPos,e.x,e.y,0),this._handle.node._getUITransformComp().convertToNodeSpaceAR(this._touchPos,this._offset),t.propagationStopped=!0}},o._onTouchBegan=function(t){this._handle&&t&&(this._dragging=!0,this._touchHandle||this._handleSliderLogic(t.touch),t.propagationStopped=!0)},o._onTouchMoved=function(t){this._dragging&&t&&(this._handleSliderLogic(t.touch),t.propagationStopped=!0)},o._onTouchEnded=function(t){this._dragging=!1,this._touchHandle=!1,this._offset=new T,t&&(t.propagationStopped=!0)},o._onTouchCancelled=function(t){this._dragging=!1,t&&(t.propagationStopped=!0)},o._handleSliderLogic=function(t){this._updateProgress(t),this._emitSlideEvent()},o._emitSlideEvent=function(){tt.emitEvents(this.slideEvents,this),this.node.emit("slide",this)},o._updateProgress=function(t){if(this._handle&&t){var e=t.getUILocation();T.set(this._touchPos,e.x,e.y,0);var i=this.node._getUITransformComp(),n=i.convertToNodeSpaceAR(this._touchPos,Xn);this.direction===Zn.Horizontal?this.progress=I(.5+(n.x-this._offset.x)/i.width):this.progress=I(.5+(n.y-this._offset.y)/i.height)}},o._updateHandlePosition=function(){if(this._handle){this._handleLocalPos.set(this._handle.node.position);var t=this.node._getUITransformComp();this._direction===Zn.Horizontal?this._handleLocalPos.x=-t.width*t.anchorX+this.progress*t.width:this._handleLocalPos.y=-t.height*t.anchorY+this.progress*t.height,this._handle.node.setPosition(this._handleLocalPos)}},o._changeLayout=function(){var t=this.node._getUITransformComp(),e=t.contentSize;if(t.setContentSize(e.height,e.width),this._handle){var i=this._handle.node.position;this._direction===Zn.Horizontal?this._handle.node.setPosition(i.x,0,i.z):this._handle.node.setPosition(0,i.y,i.z),this._updateHandlePosition()}},o._xrHandleProgress=function(t){if(!this._touchHandle){var e=this.node._getUITransformComp();e.convertToNodeSpaceAR(t,Xn),this.direction===Zn.Horizontal?this.progress=I(.5+(Xn.x-this.node.position.x)/e.width):this.progress=I(.5+(Xn.y-this.node.position.y)/e.height)}},o._xrClick=function(t){this._handle&&(this._dragging=!0,this._xrHandleProgress(t.hitPoint),this._emitSlideEvent())},o._xrUnClick=function(){this._dragging=!1,this._touchHandle=!1},o._xrHoverStay=function(t){this._dragging&&(this._xrHandleProgress(t.hitPoint),this._emitSlideEvent())},n(e,[{key:"handle",get:function(){return this._handle},set:function(t){this._handle!==t&&(this._handle=t)}},{key:"direction",get:function(){return this._direction},set:function(t){this._direction!==t&&(this._direction=t,this._changeLayout())}},{key:"progress",get:function(){return this._progress},set:function(t){this._progress!==t&&(this._progress=t,this._updateHandlePosition())}}]),e}(et),Wn.Direction=Zn,o((zn=Wn).prototype,"handle",[Pn],Object.getOwnPropertyDescriptor(zn.prototype,"handle"),zn.prototype),o(zn.prototype,"direction",[xn],Object.getOwnPropertyDescriptor(zn.prototype,"direction"),zn.prototype),Vn=v(zn.prototype,"slideEvents",[Mn,m],(function(){return[]})),Un=v(zn.prototype,"_handle",[m],(function(){return null})),Fn=v(zn.prototype,"_direction",[m],(function(){return Zn.Horizontal})),Gn=v(zn.prototype,"_progress",[m],(function(){return.1})),kn=zn))||kn)||kn)||kn);function ao(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return Object.assign.apply(Object,[{}].concat(e))}t({Slider:so,SliderComponent:so}),z.Slider=so,function(t){t.TOGGLE="toggle"}(ro||(ro={}));var ho,co,_o,lo,uo,po,go,fo=(Yn=g("cc.Toggle"),Kn=C(110),qn=E(rt),Qn=f(W),Jn=f([tt]),Yn($n=Kn($n=qn((oo=function(t){function e(){var e;return(e=t.call(this)||this).checkEvents=eo&&eo(),e._isChecked=io&&io(),e._checkMark=no&&no(),e}i(e,t);var o=e.prototype;return o._internalToggle=function(){this.isChecked=!this.isChecked},o._set=function(t,e){if(void 0===e&&(e=!0),this._isChecked!=t){this._isChecked=t;var i=this._toggleContainer;i&&i.enabled&&this.enabled&&(t||!i.anyTogglesChecked()&&!i.allowSwitchOff)&&(this._isChecked=!0,i.notifyToggleCheck(this,e)),this.playEffect(),e&&this._emitToggleEvents()}},o.playEffect=function(){this._checkMark&&(this._checkMark.node.active=this._isChecked)},o.setIsCheckedWithoutNotify=function(t){this._set(t,!1)},o.onEnable=function(){t.prototype.onEnable.call(this),this.playEffect(),this.node.on(e.EventType.CLICK,this._internalToggle,this)},o.onDisable=function(){t.prototype.onDisable.call(this),this.node.off(e.EventType.CLICK,this._internalToggle,this)},o._emitToggleEvents=function(){this.node.emit(e.EventType.TOGGLE,this),this.checkEvents&&tt.emitEvents(this.checkEvents,this)},n(e,[{key:"isChecked",get:function(){return this._isChecked},set:function(t){this._set(t)}},{key:"checkMark",get:function(){return this._checkMark},set:function(t){this._checkMark!==t&&(this._checkMark=t)}},{key:"_resizeToTarget",set:function(t){t&&this._resizeNodeToTargetNode()}},{key:"_toggleContainer",get:function(){var t=this.node.parent;return z.Node.isNode(t)?t.getComponent("cc.ToggleContainer"):null}}]),e}($t),oo.EventType=ao(ro,Yt),o((to=oo).prototype,"checkMark",[Qn],Object.getOwnPropertyDescriptor(to.prototype,"checkMark"),to.prototype),eo=v(to.prototype,"checkEvents",[Jn,m],(function(){return[]})),io=v(to.prototype,"_isChecked",[m],(function(){return!0})),no=v(to.prototype,"_checkMark",[m],(function(){return null})),$n=to))||$n)||$n)||$n);t({Toggle:fo,ToggleComponent:fo}),z.Toggle=fo;var yo,To,vo,Co,Eo,So,mo,Oo,Lo,Ao,Io,Do,bo,Ro,No,wo,Bo,Ho,Po,xo,Mo,ko,zo,Vo,Uo,Fo,Go=(ho=g("cc.ToggleContainer"),co=C(110),_o=f([tt]),ho(lo=co((uo=function(t){function e(){var e;return(e=t.call(this)||this)._allowSwitchOff=po&&po(),e.checkEvents=go&&go(),e}i(e,t);var o=e.prototype;return o.onEnable=function(){this.ensureValidState(),this.node.on($.CHILD_ADDED,this.ensureValidState,this),this.node.on($.CHILD_REMOVED,this.ensureValidState,this)},o.onDisable=function(){this.node.off($.CHILD_ADDED,this.ensureValidState,this),this.node.off($.CHILD_REMOVED,this.ensureValidState,this)},o.activeToggles=function(){return this.toggleItems.filter((function(t){return t.isChecked}))},o.anyTogglesChecked=function(){return!!this.toggleItems.find((function(t){return t.isChecked}))},o.notifyToggleCheck=function(t,e){if(void 0===e&&(e=!0),this.enabledInHierarchy){for(var i=0;i<this.toggleItems.length;i++){var n=this.toggleItems[i];n!==t&&(e?n.isChecked=!1:n.setIsCheckedWithoutNotify(!1))}this.checkEvents&&z.Component.EventHandler.emitEvents(this.checkEvents,t)}},o.ensureValidState=function(){var t=this.toggleItems;if(!this._allowSwitchOff&&!this.anyTogglesChecked()&&0!==t.length){var e=t[0];e.isChecked=!0,this.notifyToggleCheck(e)}var i=this.activeToggles();if(i.length>1)for(var n=i[0],o=0;o<i.length;++o){var r=i[o];r!==n&&(r.isChecked=!1)}},n(e,[{key:"allowSwitchOff",get:function(){return this._allowSwitchOff},set:function(t){this._allowSwitchOff=t}},{key:"toggleItems",get:function(){return this.node.children.map((function(t){var e=t.getComponent("cc.Toggle");return e&&e.enabled?e:null})).filter(Boolean)}}]),e}(et),po=v(uo.prototype,"_allowSwitchOff",[m],(function(){return!1})),go=v(uo.prototype,"checkEvents",[_o,m],(function(){return[]})),lo=uo))||lo)||lo);t({ToggleContainer:Go,ToggleContainerComponent:Go}),z.ToggleContainer=Go;var Wo,jo,Zo=new A;function Xo(t){var e=t._getUITransformComp();return t instanceof Q?R:e?e.contentSize:L.ZERO}function Yo(t,e,i,n){t.parent?Zo.set(t.parent.scale.x,t.parent.scale.y):Zo.set(0,0);for(var o=Zo.x,r=Zo.y,s=0,a=0,h=t.parent;;){if(!h)return i.x=i.y=0,void(n.x=n.y=1);var c=h.position;if(s+=c.x,a+=c.y,(h=h.parent)===e)break;h?Zo.set(h.scale.x,h.scale.y):Zo.set(0,0);var _=Zo.x,l=Zo.y;s*=_,a*=l,o*=_,r*=l}n.x=0!==o?1/o:1,n.y=0!==r?1/r:1,i.x=-s,i.y=-a}!function(t){t[t.ONCE=0]="ONCE",t[t.ALWAYS=1]="ALWAYS",t[t.ON_WINDOW_RESIZE=2]="ON_WINDOW_RESIZE"}(Wo||(Wo={})),e(Wo),function(t){t[t.TOP=1]="TOP",t[t.MID=2]="MID",t[t.BOT=4]="BOT",t[t.LEFT=8]="LEFT",t[t.CENTER=16]="CENTER",t[t.RIGHT=32]="RIGHT",t[t.HORIZONTAL=56]="HORIZONTAL",t[t.VERTICAL=7]="VERTICAL"}(jo||(jo={}));var Ko,qo,Qo,Jo,$o,tr,er,ir,nr,or,rr,sr,ar=jo.TOP|jo.BOT,hr=jo.LEFT|jo.RIGHT,cr=(yo=g("cc.Widget"),To=C(110),vo=E(rt),Co=f(q),Eo=f(Wo),yo(So=To(So=vo((Fo=function(t){function e(){var e;return(e=t.call(this)||this)._lastPos=new T,e._lastSize=new L,e._dirty=!0,e._hadAlignOnce=!1,e._alignFlags=Oo&&Oo(),e._target=Lo&&Lo(),e._left=Ao&&Ao(),e._right=Io&&Io(),e._top=Do&&Do(),e._bottom=bo&&bo(),e._horizontalCenter=Ro&&Ro(),e._verticalCenter=No&&No(),e._isAbsLeft=wo&&wo(),e._isAbsRight=Bo&&Bo(),e._isAbsTop=Ho&&Ho(),e._isAbsBottom=Po&&Po(),e._isAbsHorizontalCenter=xo&&xo(),e._isAbsVerticalCenter=Mo&&Mo(),e._originalWidth=ko&&ko(),e._originalHeight=zo&&zo(),e._alignMode=Vo&&Vo(),e._lockFlags=Uo&&Uo(),e}i(e,t);var o=e.prototype;return o.updateAlignment=function(){U._widgetManager.updateAlignment(this.node)},o._validateTargetInDEV=function(){},o.setDirty=function(){this._recursiveDirty()},o.onEnable=function(){this.node.getPosition(this._lastPos),this._lastSize.set(this.node._getUITransformComp().contentSize),U._widgetManager.add(this),this._hadAlignOnce=!1,this._registerEvent(),this._registerTargetEvents()},o.onDisable=function(){U._widgetManager.remove(this),this._unregisterEvent(),this._unregisterTargetEvents()},o.onDestroy=function(){this._removeParentEvent()},o._adjustWidgetToAllowMovingInEditor=function(){},o._adjustWidgetToAllowResizingInEditor=function(){},o._adjustWidgetToAnchorChanged=function(){this.setDirty()},o._adjustTargetToParentChanged=function(t){t&&this._unregisterOldParentEvents(t),this.node.getParent()&&this._registerTargetEvents(),this._setDirtyByMode()},o._registerEvent=function(){this.node.on($.TRANSFORM_CHANGED,this._setDirtyByMode,this),this.node.on($.SIZE_CHANGED,this._setDirtyByMode,this),this.node.on($.ANCHOR_CHANGED,this._adjustWidgetToAnchorChanged,this),this.node.on($.PARENT_CHANGED,this._adjustTargetToParentChanged,this)},o._unregisterEvent=function(){this.node.off($.TRANSFORM_CHANGED,this._setDirtyByMode,this),this.node.off($.SIZE_CHANGED,this._setDirtyByMode,this),this.node.off($.ANCHOR_CHANGED,this._adjustWidgetToAnchorChanged,this)},o._removeParentEvent=function(){this.node.off($.PARENT_CHANGED,this._adjustTargetToParentChanged,this)},o._autoChangedValue=function(t,e){if((this._alignFlags&t)>0){var i=this.node.parent&&this.node.parent._uiProps,n=i&&i.uiTransformComp,o=n?n.contentSize:R;this.isAlignLeft&&t===jo.LEFT?this._left=e?this._left*o.width:this._left/o.width:this.isAlignRight&&t===jo.RIGHT?this._right=e?this._right*o.width:this._right/o.width:this.isAlignHorizontalCenter&&t===jo.CENTER?this._horizontalCenter=e?this._horizontalCenter*o.width:this._horizontalCenter/o.width:this.isAlignTop&&t===jo.TOP?this._top=e?this._top*o.height:this._top/o.height:this.isAlignBottom&&t===jo.BOT?this._bottom=e?this._bottom*o.height:this._bottom/o.height:this.isAbsoluteVerticalCenter&&t===jo.MID&&(this._verticalCenter=this._verticalCenter/o.height),this._recursiveDirty()}},o._registerTargetEvents=function(){var t=this._target||this.node.parent;t&&t.getComponent(rt)&&(t.on($.TRANSFORM_CHANGED,this._setDirtyByMode,this),t.on($.SIZE_CHANGED,this._setDirtyByMode,this),t.on($.ANCHOR_CHANGED,this._setDirtyByMode,this))},o._unregisterTargetEvents=function(){var t=this._target||this.node.parent;t&&(t.off($.TRANSFORM_CHANGED,this._setDirtyByMode,this),t.off($.SIZE_CHANGED,this._setDirtyByMode,this),t.off($.ANCHOR_CHANGED,this._setDirtyByMode,this))},o._unregisterOldParentEvents=function(t){var e=this._target||t;e&&(e.off($.TRANSFORM_CHANGED,this._setDirtyByMode,this),e.off($.SIZE_CHANGED,this._setDirtyByMode,this))},o._setDirtyByMode=function(){(this.alignMode===Wo.ALWAYS||F)&&this._recursiveDirty()},o._setAlign=function(t,e){if(e!==(this._alignFlags&t)>0){var i=(t&hr)>0,n=this.node._getUITransformComp();e?(this._alignFlags|=t,i?(this.isAlignHorizontalCenter=!1,this.isStretchWidth&&(this._originalWidth=n.width)):(this.isAlignVerticalCenter=!1,this.isStretchHeight&&(this._originalHeight=n.height))):(i?this.isStretchWidth&&(n.width=this._originalWidth):this.isStretchHeight&&(n.height=this._originalHeight),this._alignFlags&=~t)}},o._recursiveDirty=function(){this._dirty||(this._dirty=!0)},n(e,[{key:"target",get:function(){return this._target},set:function(t){this._target!==t&&(this._unregisterTargetEvents(),this._target=t,this._registerTargetEvents(),this._validateTargetInDEV(),this._recursiveDirty())}},{key:"isAlignTop",get:function(){return(this._alignFlags&jo.TOP)>0},set:function(t){this._setAlign(jo.TOP,t),this._recursiveDirty()}},{key:"isAlignBottom",get:function(){return(this._alignFlags&jo.BOT)>0},set:function(t){this._setAlign(jo.BOT,t),this._recursiveDirty()}},{key:"isAlignLeft",get:function(){return(this._alignFlags&jo.LEFT)>0},set:function(t){this._setAlign(jo.LEFT,t),this._recursiveDirty()}},{key:"isAlignRight",get:function(){return(this._alignFlags&jo.RIGHT)>0},set:function(t){this._setAlign(jo.RIGHT,t),this._recursiveDirty()}},{key:"isAlignVerticalCenter",get:function(){return(this._alignFlags&jo.MID)>0},set:function(t){t?(this.isAlignTop=!1,this.isAlignBottom=!1,this._alignFlags|=jo.MID):this._alignFlags&=~jo.MID,this._recursiveDirty()}},{key:"isAlignHorizontalCenter",get:function(){return(this._alignFlags&jo.CENTER)>0},set:function(t){t?(this.isAlignLeft=!1,this.isAlignRight=!1,this._alignFlags|=jo.CENTER):this._alignFlags&=~jo.CENTER,this._recursiveDirty()}},{key:"isStretchWidth",get:function(){return(this._alignFlags&hr)===hr}},{key:"isStretchHeight",get:function(){return(this._alignFlags&ar)===ar}},{key:"top",get:function(){return this._top},set:function(t){this._top=t,this._recursiveDirty()}},{key:"editorTop",get:function(){return this._isAbsTop?this._top:100*this._top},set:function(t){this._top=this._isAbsTop?t:t/100,this._recursiveDirty()}},{key:"bottom",get:function(){return this._bottom},set:function(t){this._bottom=t,this._recursiveDirty()}},{key:"editorBottom",get:function(){return this._isAbsBottom?this._bottom:100*this._bottom},set:function(t){this._bottom=this._isAbsBottom?t:t/100,this._recursiveDirty()}},{key:"left",get:function(){return this._left},set:function(t){this._left=t,this._recursiveDirty()}},{key:"editorLeft",get:function(){return this._isAbsLeft?this._left:100*this._left},set:function(t){this._left=this._isAbsLeft?t:t/100,this._recursiveDirty()}},{key:"right",get:function(){return this._right},set:function(t){this._right=t,this._recursiveDirty()}},{key:"editorRight",get:function(){return this._isAbsRight?this._right:100*this._right},set:function(t){this._right=this._isAbsRight?t:t/100,this._recursiveDirty()}},{key:"horizontalCenter",get:function(){return this._horizontalCenter},set:function(t){this._horizontalCenter=t,this._recursiveDirty()}},{key:"editorHorizontalCenter",get:function(){return this._isAbsHorizontalCenter?this._horizontalCenter:100*this._horizontalCenter},set:function(t){this._horizontalCenter=this._isAbsHorizontalCenter?t:t/100,this._recursiveDirty()}},{key:"verticalCenter",get:function(){return this._verticalCenter},set:function(t){this._verticalCenter=t,this._recursiveDirty()}},{key:"editorVerticalCenter",get:function(){return this._isAbsVerticalCenter?this._verticalCenter:100*this._verticalCenter},set:function(t){this._verticalCenter=this._isAbsVerticalCenter?t:t/100,this._recursiveDirty()}},{key:"isAbsoluteTop",get:function(){return this._isAbsTop},set:function(t){this._isAbsTop!==t&&(this._isAbsTop=t,this._autoChangedValue(jo.TOP,this._isAbsTop))}},{key:"isAbsoluteBottom",get:function(){return this._isAbsBottom},set:function(t){this._isAbsBottom!==t&&(this._isAbsBottom=t,this._autoChangedValue(jo.BOT,this._isAbsBottom))}},{key:"isAbsoluteLeft",get:function(){return this._isAbsLeft},set:function(t){this._isAbsLeft!==t&&(this._isAbsLeft=t,this._autoChangedValue(jo.LEFT,this._isAbsLeft))}},{key:"isAbsoluteRight",get:function(){return this._isAbsRight},set:function(t){this._isAbsRight!==t&&(this._isAbsRight=t,this._autoChangedValue(jo.RIGHT,this._isAbsRight))}},{key:"isAbsoluteHorizontalCenter",get:function(){return this._isAbsHorizontalCenter},set:function(t){this._isAbsHorizontalCenter!==t&&(this._isAbsHorizontalCenter=t,this._autoChangedValue(jo.CENTER,this._isAbsHorizontalCenter))}},{key:"isAbsoluteVerticalCenter",get:function(){return this._isAbsVerticalCenter},set:function(t){this._isAbsVerticalCenter!==t&&(this._isAbsVerticalCenter=t,this._autoChangedValue(jo.MID,this._isAbsVerticalCenter))}},{key:"alignMode",get:function(){return this._alignMode},set:function(t){this._alignMode=t,this._recursiveDirty()}},{key:"alignFlags",get:function(){return this._alignFlags},set:function(t){this._alignFlags!==t&&(this._alignFlags=t,this._recursiveDirty())}}]),e}(et),Fo.AlignMode=Wo,o((mo=Fo).prototype,"target",[Co],Object.getOwnPropertyDescriptor(mo.prototype,"target"),mo.prototype),o(mo.prototype,"alignMode",[Eo],Object.getOwnPropertyDescriptor(mo.prototype,"alignMode"),mo.prototype),Oo=v(mo.prototype,"_alignFlags",[m],(function(){return 0})),Lo=v(mo.prototype,"_target",[m],(function(){return null})),Ao=v(mo.prototype,"_left",[m],(function(){return 0})),Io=v(mo.prototype,"_right",[m],(function(){return 0})),Do=v(mo.prototype,"_top",[m],(function(){return 0})),bo=v(mo.prototype,"_bottom",[m],(function(){return 0})),Ro=v(mo.prototype,"_horizontalCenter",[m],(function(){return 0})),No=v(mo.prototype,"_verticalCenter",[m],(function(){return 0})),wo=v(mo.prototype,"_isAbsLeft",[m],(function(){return!0})),Bo=v(mo.prototype,"_isAbsRight",[m],(function(){return!0})),Ho=v(mo.prototype,"_isAbsTop",[m],(function(){return!0})),Po=v(mo.prototype,"_isAbsBottom",[m],(function(){return!0})),xo=v(mo.prototype,"_isAbsHorizontalCenter",[m],(function(){return!0})),Mo=v(mo.prototype,"_isAbsVerticalCenter",[m],(function(){return!0})),ko=v(mo.prototype,"_originalWidth",[m],(function(){return 0})),zo=v(mo.prototype,"_originalHeight",[m],(function(){return 0})),Vo=v(mo.prototype,"_alignMode",[m],(function(){return Wo.ON_WINDOW_RESIZE})),Uo=v(mo.prototype,"_lockFlags",[m,N],(function(){return 0})),So=mo))||So)||So)||So);t({Widget:cr,WidgetComponent:cr}),U.internal.computeInverseTransForTarget=Yo,U.internal.getReadonlyNodeSize=Xo,U.Widget=cr;var _r,lr=new p;!function(t){t[t.HORIZONTAL=0]="HORIZONTAL",t[t.VERTICAL=1]="VERTICAL"}(_r||(_r={})),e(_r);var ur,dr,pr,gr,fr,yr,Tr,vr,Cr,Er,Sr,mr,Or,Lr,Ar,Ir,Dr,br,Rr,Nr,wr,Br,Hr,Pr,xr=(Ko=g("cc.PageViewIndicator"),qo=C(110),Qo=f(G),Jo=f(_r),$o=f(L),Ko(tr=qo((sr=function(t){function e(){var e;return(e=t.call(this)||this).spacing=ir&&ir(),e._spriteFrame=nr&&nr(),e._direction=or&&or(),e._cellSize=rr&&rr(),e._layout=null,e._pageView=null,e._indicators=[],e}i(e,t);var o=e.prototype;return o.onLoad=function(){this._updateLayout()},o.setPageView=function(t){this._pageView=t,this._refresh()},o._updateLayout=function(){this._layout=this.getComponent(Di),this._layout||(this._layout=this.addComponent(Di));var t=this._layout;this.direction===_r.HORIZONTAL?(t.type=si.HORIZONTAL,t.spacingX=this.spacing):this.direction===_r.VERTICAL&&(t.type=si.VERTICAL,t.spacingY=this.spacing),t.resizeMode=ai.CONTAINER},o._createIndicator=function(){var t=new q;t.layer=this.node.layer;var e=t.addComponent(W);return e.spriteFrame=this.spriteFrame,e.sizeMode=W.SizeMode.CUSTOM,t.parent=this.node,t._getUITransformComp().setContentSize(this._cellSize),t},o._changedState=function(){var t=this._indicators;if(0!==t.length&&this._pageView){var e=this._pageView.curPageIdx;if(!(e>=t.length)){for(var i=0;i<t.length;++i){var n=t[i];if(n._uiProps.uiComp){var o=n._uiProps.uiComp;lr.set(o.color),lr.a=127.5,o.color=lr}}if(t[e]._uiProps.uiComp){var r=t[e]._uiProps.uiComp;lr.set(r.color),lr.a=255,r.color=lr}}}},o._refresh=function(){if(this._pageView){var t=this._indicators,e=this._pageView.getPages();if(e.length!==t.length){var i=0;if(e.length>t.length)for(i=0;i<e.length;++i)t[i]||(t[i]=this._createIndicator());else for(i=t.length-e.length;i>0;--i){var n=t[i-1];this.node.removeChild(n),t.splice(i-1,1)}this._layout&&this._layout.enabledInHierarchy&&this._layout.updateLayout(),this._changedState()}}},n(e,[{key:"spriteFrame",get:function(){return this._spriteFrame},set:function(t){this._spriteFrame!==t&&(this._spriteFrame=t)}},{key:"direction",get:function(){return this._direction},set:function(t){this._direction!==t&&(this._direction=t)}},{key:"cellSize",get:function(){return this._cellSize},set:function(t){this._cellSize!==t&&(this._cellSize=t)}}]),e}(et),sr.Direction=_r,o((er=sr).prototype,"spriteFrame",[Qo],Object.getOwnPropertyDescriptor(er.prototype,"spriteFrame"),er.prototype),o(er.prototype,"direction",[Jo],Object.getOwnPropertyDescriptor(er.prototype,"direction"),er.prototype),o(er.prototype,"cellSize",[$o],Object.getOwnPropertyDescriptor(er.prototype,"cellSize"),er.prototype),ir=v(er.prototype,"spacing",[m],(function(){return 0})),nr=v(er.prototype,"_spriteFrame",[m],(function(){return null})),or=v(er.prototype,"_direction",[m],(function(){return _r.HORIZONTAL})),rr=v(er.prototype,"_cellSize",[m],(function(){return new L(20,20)})),tr=er))||tr)||tr);t({PageViewIndicator:xr,PageViewIndicatorComponent:xr}),z.PageViewIndicator=xr;var Mr,kr,zr,Vr=new A;!function(t){t[t.Unified=0]="Unified",t[t.Free=1]="Free"}(Mr||(Mr={})),e(Mr),function(t){t[t.HORIZONTAL=0]="HORIZONTAL",t[t.VERTICAL=1]="VERTICAL"}(kr||(kr={})),e(kr),function(t){t.PAGE_TURNING="page-turning"}(zr||(zr={}));var Ur=(ur=g("cc.PageView"),dr=C(110),pr=f(Mr),gr=f(kr),fr=f(xr),yr=f(Qi),Tr=f(Qi),vr=f([tt]),Cr=f([tt]),ur(Er=dr((Pr=function(t){function e(){var e;return(e=t.call(this)||this).autoPageTurningThreshold=mr&&mr(),e.horizontal=Or&&Or(),e.vertical=Lr&&Lr(),e.cancelInnerEvents=Ar&&Ar(),e.scrollEvents=Ir&&Ir(),e.pageTurningSpeed=Dr&&Dr(),e.pageEvents=br&&br(),e._sizeMode=Rr&&Rr(),e._direction=Nr&&Nr(),e._scrollThreshold=wr&&wr(),e._pageTurningEventTiming=Br&&Br(),e._indicator=Hr&&Hr(),e._curPageIdx=0,e._lastPageIdx=0,e._pages=[],e._initContentPos=S(),e._scrollCenterOffsetX=[],e._scrollCenterOffsetY=[],e._touchBeganPosition=D(),e._touchEndPosition=D(),e}i(e,t);var o=e.prototype;return o.onEnable=function(){t.prototype.onEnable.call(this),this.node.on($.SIZE_CHANGED,this._updateAllPagesSize,this),this.node.on(e.EventType.SCROLL_ENG_WITH_THRESHOLD,this._dispatchPageTurningEvent,this)},o.onDisable=function(){t.prototype.onDisable.call(this),this.node.off($.SIZE_CHANGED,this._updateAllPagesSize,this),this.node.off(e.EventType.SCROLL_ENG_WITH_THRESHOLD,this._dispatchPageTurningEvent,this)},o.onLoad=function(){this._initPages(),this.indicator&&this.indicator.setPageView(this)},o.getCurrentPageIndex=function(){return this._curPageIdx},o.setCurrentPageIndex=function(t){this.scrollToPage(t,1)},o.getPages=function(){return this._pages},o.addPage=function(t){t&&-1===this._pages.indexOf(t)&&this.content&&(t._getUITransformComp()?(this.content.addChild(t),this._pages.push(t),this._updatePageView()):a(4301))},o.insertPage=function(t,e){if(!(e<0)&&t&&-1===this._pages.indexOf(t)&&this.content)if(e>=this._pages.length)this.addPage(t);else{if(!t._getUITransformComp())return void a(4301);this._pages.splice(e,0,t),this.content.insertChild(t,e),this._updatePageView()}},o.removePage=function(t){if(t&&this.content){var e=this._pages.indexOf(t);-1!==e?this.removePageAtIndex(e):s(4300,t.name)}},o.removePageAtIndex=function(t){var e=this._pages;if(!(t<0||t>=e.length)){var i=e[t];i&&this.content&&(this.content.removeChild(i),e.splice(t,1),this._updatePageView())}},o.removeAllPages=function(){if(this.content){for(var t=this._pages,e=0,i=t.length;e<i;e++)this.content.removeChild(t[e]);this._pages.length=0,this._updatePageView()}},o.scrollToPage=function(t,e){void 0===e&&(e=.3),t<0||t>=this._pages.length||(this._curPageIdx=t,this.scrollToOffset(this._moveOffsetValue(t),e,!0),this.indicator&&this.indicator._changedState())},o.getScrollEndedEventTiming=function(){return this.pageTurningEventTiming},o._updatePageView=function(){if(this.content){var t=this.content.getComponent(Di);t&&t.enabled&&t.updateLayout();var e=this._pages.length;this._curPageIdx>=e&&(this._curPageIdx=0===e?0:e-1,this._lastPageIdx=this._curPageIdx);for(var i=this._initContentPos,n=0;n<e;++n){var o=this._pages[n].position;this.direction===kr.HORIZONTAL?this._scrollCenterOffsetX[n]=Math.abs(i.x+o.x):this._scrollCenterOffsetY[n]=Math.abs(i.y+o.y)}this.indicator&&this.indicator._refresh()}},o._updateAllPagesSize=function(){var t=this.view;if(this.content&&t&&this._sizeMode===Mr.Unified)for(var e=this._pages,i=t.contentSize,n=0,o=e.length;n<o;n++)e[n]._getUITransformComp().setContentSize(i)},o._handleReleaseLogic=function(){this._autoScrollToPage(),this._scrolling&&(this._scrolling=!1,this._autoScrolling||this._dispatchEvent(e.EventType.SCROLL_ENDED))},o._onTouchBegan=function(e,i){e.touch.getUILocation(Vr),A.set(this._touchBeganPosition,Vr.x,Vr.y),t.prototype._onTouchBegan.call(this,e,i)},o._onTouchMoved=function(e,i){t.prototype._onTouchMoved.call(this,e,i)},o._onTouchEnded=function(e,i){e.touch.getUILocation(Vr),A.set(this._touchEndPosition,Vr.x,Vr.y),t.prototype._onTouchEnded.call(this,e,i)},o._onTouchCancelled=function(e,i){e.touch.getUILocation(Vr),A.set(this._touchEndPosition,Vr.x,Vr.y),t.prototype._onTouchCancelled.call(this,e,i)},o._onMouseWheel=function(){},o._syncScrollDirection=function(){this.horizontal=this.direction===kr.HORIZONTAL,this.vertical=this.direction===kr.VERTICAL},o._syncSizeMode=function(){var t=this.view;if(this.content&&t){var e=this.content.getComponent(Di);if(e){if(this._sizeMode===Mr.Free&&this._pages.length>0){var i=this._pages[0]._getUITransformComp(),n=this._pages[this._pages.length-1]._getUITransformComp();this.direction===kr.HORIZONTAL?(e.paddingLeft=(t.width-i.width)/2,e.paddingRight=(t.width-n.width)/2):this.direction===kr.VERTICAL&&(e.paddingTop=(t.height-i.height)/2,e.paddingBottom=(t.height-n.height)/2)}e.updateLayout()}}},o._initPages=function(){if(this.content){this._initContentPos=this.content.position;for(var t=this.content.children,e=0;e<t.length;++e){var i=t[e];this._pages.indexOf(i)>=0||this._pages.push(i)}this._syncScrollDirection(),this._syncSizeMode(),this._updatePageView()}},o._dispatchPageTurningEvent=function(){this._lastPageIdx!==this._curPageIdx&&(this._lastPageIdx=this._curPageIdx,tt.emitEvents(this.pageEvents,this,zr.PAGE_TURNING),this.node.emit(zr.PAGE_TURNING,this))},o._isQuicklyScrollable=function(t){if(this.direction===kr.HORIZONTAL){if(Math.abs(t.x)>this.autoPageTurningThreshold)return!0}else if(this.direction===kr.VERTICAL&&Math.abs(t.y)>this.autoPageTurningThreshold)return!0;return!1},o._moveOffsetValue=function(t){var e=new A;if(this._sizeMode===Mr.Free)this.direction===kr.HORIZONTAL?e.x=this._scrollCenterOffsetX[t]:this.direction===kr.VERTICAL&&(e.y=this._scrollCenterOffsetY[t]);else{var i=this.view;if(!i)return e;this.direction===kr.HORIZONTAL?e.x=t*i.width:this.direction===kr.VERTICAL&&(e.y=t*i.height)}return e},o._getDragDirection=function(t){return this._direction===kr.HORIZONTAL?0===t.x?0:t.x>0?1:-1:0===t.y?0:t.y<0?1:-1},o._isScrollable=function(t,e,i){if(this._sizeMode===Mr.Free){var n=0,o=0;if(this.direction===kr.HORIZONTAL)return n=this._scrollCenterOffsetX[e],o=this._scrollCenterOffsetX[i],Math.abs(t.x)>=Math.abs(n-o)*this.scrollThreshold;if(this.direction===kr.VERTICAL)return n=this._scrollCenterOffsetY[e],o=this._scrollCenterOffsetY[i],Math.abs(t.y)>=Math.abs(n-o)*this.scrollThreshold}else{var r=this.view;if(!r)return!1;if(this.direction===kr.HORIZONTAL)return Math.abs(t.x)>=r.width*this.scrollThreshold;if(this.direction===kr.VERTICAL)return Math.abs(t.y)>=r.height*this.scrollThreshold}return!1},o._autoScrollToPage=function(){if(this._startBounceBackIfNeeded()){var t=this._getHowMuchOutOfBoundary();this._clampDelta(t),(t.x>0||t.y<0)&&(this._curPageIdx=0===this._pages.length?0:this._pages.length-1),(t.x<0||t.y>0)&&(this._curPageIdx=0),this.indicator&&this.indicator._changedState()}else{var e=new A;A.subtract(e,this._touchBeganPosition,this._touchEndPosition);var i=this._curPageIdx,n=i+this._getDragDirection(e),o=this.pageTurningSpeed*Math.abs(i-n);if(n<this._pages.length){if(this._isScrollable(e,i,n))return void this.scrollToPage(n,o);var r=this._calculateTouchMoveVelocity();if(this._isQuicklyScrollable(r))return void this.scrollToPage(n,o)}this.scrollToPage(i,o)}},n(e,[{key:"sizeMode",get:function(){return this._sizeMode},set:function(t){this._sizeMode!==t&&(this._sizeMode=t,this._syncSizeMode())}},{key:"direction",get:function(){return this._direction},set:function(t){this._direction!==t&&(this._direction=t,this._syncScrollDirection())}},{key:"scrollThreshold",get:function(){return this._scrollThreshold},set:function(t){this._scrollThreshold!==t&&(this._scrollThreshold=t)}},{key:"pageTurningEventTiming",get:function(){return this._pageTurningEventTiming},set:function(t){this._pageTurningEventTiming!==t&&(this._pageTurningEventTiming=t)}},{key:"indicator",get:function(){return this._indicator},set:function(t){this._indicator!==t&&(this._indicator=t,this.indicator&&this.indicator.setPageView(this))}},{key:"curPageIdx",get:function(){return this._curPageIdx}},{key:"verticalScrollBar",get:function(){return t.prototype.verticalScrollBar},set:function(t){this.verticalScrollBar=t}},{key:"horizontalScrollBar",get:function(){return t.prototype.horizontalScrollBar},set:function(t){this.horizontalScrollBar=t}}]),e}(jn),Pr.SizeMode=Mr,Pr.Direction=kr,Pr.EventType=ao(zr,En),o((Sr=Pr).prototype,"sizeMode",[pr],Object.getOwnPropertyDescriptor(Sr.prototype,"sizeMode"),Sr.prototype),o(Sr.prototype,"direction",[gr],Object.getOwnPropertyDescriptor(Sr.prototype,"direction"),Sr.prototype),o(Sr.prototype,"indicator",[fr],Object.getOwnPropertyDescriptor(Sr.prototype,"indicator"),Sr.prototype),mr=v(Sr.prototype,"autoPageTurningThreshold",[m],(function(){return 100})),o(Sr.prototype,"verticalScrollBar",[yr,w],Object.getOwnPropertyDescriptor(Sr.prototype,"verticalScrollBar"),Sr.prototype),o(Sr.prototype,"horizontalScrollBar",[Tr,w],Object.getOwnPropertyDescriptor(Sr.prototype,"horizontalScrollBar"),Sr.prototype),Or=v(Sr.prototype,"horizontal",[w,m],(function(){return!0})),Lr=v(Sr.prototype,"vertical",[w,m],(function(){return!0})),Ar=v(Sr.prototype,"cancelInnerEvents",[w,m],(function(){return!0})),Ir=v(Sr.prototype,"scrollEvents",[vr,m,w],(function(){return[]})),Dr=v(Sr.prototype,"pageTurningSpeed",[m],(function(){return.3})),br=v(Sr.prototype,"pageEvents",[Cr,m],(function(){return[]})),Rr=v(Sr.prototype,"_sizeMode",[m],(function(){return Mr.Unified})),Nr=v(Sr.prototype,"_direction",[m],(function(){return kr.HORIZONTAL})),wr=v(Sr.prototype,"_scrollThreshold",[m],(function(){return.5})),Br=v(Sr.prototype,"_pageTurningEventTiming",[m],(function(){return.1})),Hr=v(Sr.prototype,"_indicator",[m],(function(){return null})),Er=Sr))||Er)||Er);t({PageView:Ur,PageViewComponent:Ur}),z.PageView=Ur;var Fr=new T,Gr=new A,Wr=new A,jr=new A(1,1),Zr=new A,Xr=new A;function Yr(t,e){if(!e._hadAlignOnce){e.alignMode===Wo.ONCE&&(e._hadAlignOnce=!0);var i,n=e.target,o=Wr,r=jr;n?Yo(t,i=n,o,r):i=t.parent;var s=Xo(i),a=i instanceof Q||!i.getComponent(rt),h=a?Gr:i.getComponent(rt).anchorPoint,c=a;t.getPosition(Fr);var _=t._getUITransformComp(),l=Fr.x,u=Fr.y,d=_.anchorPoint,p=t.scale;if(e.alignFlags&jo.HORIZONTAL){var g=0,f=0,y=s.width;c?(g=R.left.x,f=R.right.x):f=(g=-h.x*y)+y,g+=e.isAbsoluteLeft?e.left:e.left*y,f-=e.isAbsoluteRight?e.right:e.right*y,n&&(g+=o.x,g*=r.x,f+=o.x,f*=r.x);var v=0,C=d.x,E=p.x;if(E<0&&(C=1-C,E=-E),e.isStretchWidth)v=f-g,0!==E&&(_.width=v/E),l=g+C*v;else{if(v=_.width*E,e.isAlignHorizontalCenter){var S=e.isAbsoluteHorizontalCenter?e.horizontalCenter:e.horizontalCenter*y,m=(.5-h.x)*s.width;n&&(S*=r.x,m+=o.x,m*=r.x),l=m+(C-.5)*v+S}else l=e.isAlignLeft?g+C*v:f+(C-1)*v;b(E,0,H)?v=_.width:v/=E}e._lastSize.width=v}if(e.alignFlags&jo.VERTICAL){var O=0,L=0,A=s.height;c?(L=R.bottom.y,O=R.top.y):O=(L=-h.y*A)+A,L+=e.isAbsoluteBottom?e.bottom:e.bottom*A,O-=e.isAbsoluteTop?e.top:e.top*A,n&&(L+=o.y,L*=r.y,O+=o.y,O*=r.y);var I=0,D=d.y,N=p.y;if(N<0&&(D=1-D,N=-N),e.isStretchHeight)I=O-L,0!==N&&(_.height=I/N),u=L+D*I;else{if(I=_.height*N,e.isAlignVerticalCenter){var w=e.isAbsoluteVerticalCenter?e.verticalCenter:e.verticalCenter*A,B=(.5-h.y)*s.height;n&&(w*=r.y,B+=o.y,B*=r.y),u=B+(D-.5)*I+w}else u=e.isAlignBottom?L+D*I:O+(D-1)*I;b(N,0,H)?I=_.height:I/=N}e._lastSize.height=I}t.setPosition(l,u,Fr.z),T.set(e._lastPos,l,u,Fr.z)}}function Kr(t){var e=t.getComponent(cr);if(e&&e.enabled){if(!U.isValid(t,!0))return;ts.push(e)}for(var i,n=t.children,o=_(n);!(i=o()).done;){var r=i.value;r.active&&Kr(r)}}function qr(){var t=ct.getScene();if(t){es.isAligning=!0,es._nodesOrderDirty&&(ts.length=0,Kr(t),es._nodesOrderDirty=!1);var e=null,i=es._activeWidgetsIterator;for(i.i=0;i.i<ts.length;++i.i)(e=ts[i.i])._dirty&&(Yr(e.node,e),e._dirty=!1);es.isAligning=!1}}var Qr,Jr,$r,ts=[],es=t("widgetManager",U._widgetManager={isAligning:!1,_nodesOrderDirty:!1,_activeWidgetsIterator:new c(ts),animationState:null,init:function(){ct.on(_t.AFTER_SCENE_LAUNCH,qr),ct.on(_t.AFTER_UPDATE,qr),it.instance.on("design-resolution-changed",this.onResized,this);var t=this.onResized.bind(this);it.instance.on("canvas-resize",t),B.on("window-resize",t)},add:function(){this._nodesOrderDirty=!0},remove:function(t){this._activeWidgetsIterator.remove(t)},onResized:function(){var t=ct.getScene();t&&this.refreshWidgetOnResized(t)},refreshWidgetOnResized:function(t){var e=q.isNode(t)&&t.getComponent(cr);e&&e.enabled&&(e.alignMode===Wo.ON_WINDOW_RESIZE||e.alignMode===Wo.ALWAYS)&&e.setDirty();for(var i,n=t.children,o=_(n);!(i=o()).done;){var r=i.value;this.refreshWidgetOnResized(r)}},updateOffsetsToStayPut:function(t,e){function i(t,e){return Math.abs(t-e)>1e-10?e:t}var n=t.node,o=n.parent;if(o){var r=Zr;r.set(0,0);var s=Xr;if(s.set(1,1),t.target&&Yo(n,o=t.target,r,s),!e)return;var a=o._uiProps&&o._getUITransformComp(),h=a?a.anchorPoint:Gr,c=n._getUITransformComp(),_=Xo(o),l=c.anchorPoint,u=n.position,d=jo,p=n.scale,g=0;if(e&d.LEFT){var f=-h.x*_.width;f+=r.x,f*=s.x,g=u.x-l.x*c.width*Math.abs(p.x)-f,t.isAbsoluteLeft||(g/=_.width),g/=s.x,t.left=i(t.left,g)}if(e&d.RIGHT){var y=(1-h.x)*_.width;y+=r.x,g=(y*=s.x)-(u.x+(1-l.x)*c.width*Math.abs(p.x)),t.isAbsoluteRight||(g/=_.width),g/=s.x,t.right=i(t.right,g)}if(e&d.TOP){var T=(1-h.y)*_.height;T+=r.y,g=(T*=s.y)-(u.y+(1-l.y)*c.height*Math.abs(p.y)),t.isAbsoluteTop||(g/=_.height),g/=s.y,t.top=i(t.top,g)}if(e&d.BOT){var v=-h.y*_.height;v+=r.y,v*=s.y,g=u.y-l.y*c.height*Math.abs(p.y)-v,t.isAbsoluteBottom||(g/=_.height),g/=s.y,t.bottom=i(t.bottom,g)}}},updateAlignment:function t(e){var i=e.parent;i&&q.isNode(i)&&t(i);var n=e.getComponent(cr);n&&i&&Yr(e,n)},AlignMode:Wo,AlignFlags:jo});ct.on(_t.INIT,(function(){es.init()}));var is,ns,os,rs,ss,as,hs,cs,_s,ls,us,ds,ps=g("cc.SafeArea")(Qr=C(110)(Qr=E(cr)((Jr=function(t){function e(){var e;return(e=t.call(this)||this)._symmetric=$r&&$r(),e}i(e,t);var o=e.prototype;return o.onEnable=function(){this.updateArea(),B.on("window-resize",this.updateArea,this),B.on("orientation-change",this.updateArea,this)},o.onDisable=function(){B.off("window-resize",this.updateArea,this),B.off("orientation-change",this.updateArea,this)},o.updateArea=function(){var t=this.node.getComponent(cr),e=this.node.getComponent(rt);if(t&&e){t.updateAlignment();var i=this.node.position.clone(),n=e.anchorPoint.clone();t.isAlignTop=t.isAlignBottom=t.isAlignLeft=t.isAlignRight=!0;var o=nt.getVisibleSize(),r=o.width,s=o.height,a=P.getSafeAreaRect(this._symmetric);t.top=s-a.y-a.height,t.bottom=a.y,t.left=a.x,t.right=r-a.x-a.width,t.updateAlignment();var h=this.node.position.clone(),c=n.x-(h.x-i.x)/e.width,_=n.y-(h.y-i.y)/e.height;e.setAnchorPoint(c,_),es.add(t)}},n(e,[{key:"symmetric",get:function(){return this._symmetric},set:function(t){this._symmetric=t}}]),e}(et),$r=v(Jr.prototype,"_symmetric",[m],(function(){return!0})),Qr=Jr))||Qr)||Qr)||Qr;t({SafeArea:ps,SafeAreaComponent:ps}),z.SafeArea=ps;var gs,fs=(is=g("cc.UICoordinateTracker"),ns=C(110),os=f(q),rs=f(yt),ss=f([tt]),is(as=ns((hs=function(t){function e(){var e;return(e=t.call(this)||this).syncEvents=cs&&cs(),e._target=_s&&_s(),e._camera=ls&&ls(),e._useScale=us&&us(),e._distance=ds&&ds(),e._transformPos=S(),e._viewPos=S(),e._canMove=!0,e._lastWPos=S(),e._lastCameraPos=S(),e}i(e,t);var o=e.prototype;return o.onEnable=function(){this._checkCanMove()},o.update=function(){var t=this.node.worldPosition,e=this._camera;if(this._canMove&&e&&e.camera&&(!this._lastWPos.equals(t)||!this._lastCameraPos.equals(e.node.worldPosition))&&(this._lastWPos.set(t),this._lastCameraPos.set(e.node.worldPosition),e.camera.update(),e.convertToUINode(t,this._target,this._transformPos),this._useScale&&T.transformMat4(this._viewPos,this.node.worldPosition,e.camera.matView),this.syncEvents.length>0)){var i=this._distance/Math.abs(this._viewPos.z);tt.emitEvents(this.syncEvents,this._transformPos,i)}},o._checkCanMove=function(){this._canMove=!(!this._camera||!this._target)},n(e,[{key:"target",get:function(){return this._target},set:function(t){this._target!==t&&(this._target=t,this._checkCanMove())}},{key:"camera",get:function(){return this._camera},set:function(t){this._camera!==t&&(this._camera=t,this._checkCanMove())}},{key:"useScale",get:function(){return this._useScale},set:function(t){this._useScale!==t&&(this._useScale=t)}},{key:"distance",get:function(){return this._distance},set:function(t){this._distance!==t&&(this._distance=t)}}]),e}(et),o(hs.prototype,"target",[os],Object.getOwnPropertyDescriptor(hs.prototype,"target"),hs.prototype),o(hs.prototype,"camera",[rs],Object.getOwnPropertyDescriptor(hs.prototype,"camera"),hs.prototype),cs=v(hs.prototype,"syncEvents",[ss,m],(function(){return[]})),_s=v(hs.prototype,"_target",[m],(function(){return null})),ls=v(hs.prototype,"_camera",[m],(function(){return null})),us=v(hs.prototype,"_useScale",[m],(function(){return!0})),ds=v(hs.prototype,"_distance",[m],(function(){return 1})),as=hs))||as)||as);t({UICoordinateTracker:fs,UICoordinateTrackerComponent:fs});var ys=[$.TOUCH_START,$.TOUCH_END,$.TOUCH_MOVE,$.MOUSE_DOWN,$.MOUSE_MOVE,$.MOUSE_UP,$.MOUSE_ENTER,$.MOUSE_LEAVE,$.MOUSE_WHEEL];function Ts(t){t.propagationStopped=!0}var vs,Cs,Es,Ss,ms=g("cc.BlockInputEvents")(gs=function(t){function e(){return t.apply(this,arguments)||this}i(e,t);var n=e.prototype;return n.onEnable=function(){for(var t=0;t<ys.length;t++)this.node.on(ys[t],Ts,this)},n.onDisable=function(){for(var t=0;t<ys.length;t++)this.node.off(ys[t],Ts,this)},e}(et))||gs;t({BlockInputEvents:ms,BlockInputEventsComponent:ms});var Os,Ls=t("SubContextView",g("cc.SubContextView")(vs=C(110)(vs=E(rt)((Cs=function(t){function e(){var e;return(e=t.call(this)||this)._fps=Es&&Es(),e._sprite=null,e._imageAsset=new Tt,e._texture=new J,e._updatedTime=0,e._updateInterval=0,e._openDataContext=null,e._content=new q("content"),e._designResolutionSize=Ss&&Ss(),e._content.hideFlags|=u.DontSave|u.HideInHierarchy,e._updatedTime=performance.now(),e}i(e,t);var o=e.prototype;return o.onLoad=function(){l.getOpenDataContext?(this._updateInterval=1e3/this._fps,this._openDataContext=l.getOpenDataContext(),this._initSharedCanvas(),this._initContentNode(),this._updateSubContextView(),this._updateContentLayer()):this.enabled=!1},o.onEnable=function(){this._registerNodeEvent()},o.onDisable=function(){this._unregisterNodeEvent()},o._initSharedCanvas=function(){if(this._openDataContext){var t=this._openDataContext.canvas,e=this._designResolutionSize.width,i=this._designResolutionSize.height,n=513;if(e<=n&&i<=n){var o=n/e,r=n/i,s=o<r?o:r;e*=s,i*=s}t.width=e,t.height=i}},o._initContentNode=function(){if(this._openDataContext){var t=this._openDataContext.canvas,e=this._imageAsset;if(e.reset(t),this._texture.image=e,this._texture.create(t.width,t.height),this._sprite=this._content.getComponent(W),this._sprite||(this._sprite=this._content.addComponent(W)),this._sprite.spriteFrame)this._sprite.spriteFrame.texture=this._texture;else{var i=new G;i.texture=this._texture,this._sprite.spriteFrame=i}this._content.parent=this.node}},o._updateSubContextView=function(){if(this._openDataContext){var t=this.node.getComponent(rt),e=this._content.getComponent(rt),i=t.width/e.width,n=t.height/e.height,o=i>n?n:i;e.width*=o,e.height*=o;var r=nt.getViewportRect(),s=e.getBoundingBoxToWorld(),a=nt.getVisibleSize(),h=B.devicePixelRatio,c=(r.width*(s.x/a.width)+r.x)/h,_=(r.height*(s.y/a.height)+r.y)/h,l=r.width*(s.width/a.width)/h,u=r.height*(s.height/a.height)/h;this._openDataContext.postMessage({fromEngine:!0,type:"engine",event:"viewport",x:c,y:_,width:l,height:u})}},o._updateSubContextTexture=function(){var t=this._imageAsset;if(t&&this._openDataContext&&!(t.width<=0||t.height<=0)){var e=this._openDataContext.canvas;t.reset(e),(e.width>t.width||e.height>t.height)&&this._texture.create(e.width,e.height),this._texture.uploadData(e)}},o._registerNodeEvent=function(){this.node.on($.TRANSFORM_CHANGED,this._updateSubContextView,this),this.node.on($.SIZE_CHANGED,this._updateSubContextView,this),this.node.on($.LAYER_CHANGED,this._updateContentLayer,this)},o._unregisterNodeEvent=function(){this.node.off($.TRANSFORM_CHANGED,this._updateSubContextView,this),this.node.off($.SIZE_CHANGED,this._updateSubContextView,this),this.node.off($.LAYER_CHANGED,this._updateContentLayer,this)},o._updateContentLayer=function(){this._content.layer=this.node.layer},o.update=function(t){void 0===t?this._updateSubContextTexture():performance.now()-this._updatedTime>=this._updateInterval&&(this._updatedTime+=this._updateInterval,this._updateSubContextTexture())},o.onDestroy=function(){this._content.destroy(),this._texture.destroy(),this._sprite&&this._sprite.destroy(),this._imageAsset.destroy(),this._openDataContext=null},n(e,[{key:"designResolutionSize",get:function(){return this._designResolutionSize},set:function(){}},{key:"fps",get:function(){return this._fps},set:function(t){this._fps!==t&&(this._fps=t,this._updateInterval=1e3/t)}}]),e}(et),Es=v(Cs.prototype,"_fps",[m],(function(){return 60})),Ss=v(Cs.prototype,"_designResolutionSize",[m],(function(){return new L(640,960)})),vs=Cs))||vs)||vs)||vs);z.SubContextView=Ls,x({ButtonComponent:{newName:"Button",since:"1.2.0",removed:!1},EditBoxComponent:{newName:"EditBox",since:"1.2.0",removed:!1},LayoutComponent:{newName:"Layout",since:"1.2.0",removed:!1},ProgressBarComponent:{newName:"ProgressBar",since:"1.2.0",removed:!1},ScrollViewComponent:{newName:"ScrollView",since:"1.2.0",removed:!1},ScrollBarComponent:{newName:"ScrollBar",since:"1.2.0",removed:!1},SliderComponent:{newName:"Slider",since:"1.2.0",removed:!1},ToggleComponent:{newName:"Toggle",since:"1.2.0",removed:!1},ToggleContainerComponent:{newName:"ToggleContainer",since:"1.2.0",removed:!1},WidgetComponent:{newName:"Widget",since:"1.2.0",removed:!1},PageViewComponent:{newName:"PageView",since:"1.2.0",removed:!1},PageViewIndicatorComponent:{newName:"PageViewIndicator",since:"1.2.0",removed:!1},SafeAreaComponent:{newName:"SafeArea",since:"1.2.0",removed:!1},UICoordinateTrackerComponent:{newName:"UICoordinateTracker",since:"1.2.0",removed:!1},BlockInputEventsComponent:{newName:"BlockInputEvents",since:"1.2.0",removed:!1}});var As=t("UIReorderComponent",g("cc.UIReorderComponent")(Os=function(){s(1408,"UIReorderComponent")})||Os);z.UIReorderComponent=As,z.ButtonComponent=$t,d($t,"cc.ButtonComponent"),z.EditBoxComponent=ui,d(ui,"cc.EditBoxComponent"),z.LayoutComponent=Di,d(Di,"cc.LayoutComponent"),z.ProgressBarComponent=Fi,d(Fi,"cc.ProgressBarComponent"),z.ScrollViewComponent=jn,d(jn,"cc.ScrollViewComponent"),z.ScrollBarComponent=Qi,d(Qi,"cc.ScrollBarComponent"),z.SliderComponent=so,d(so,"cc.SliderComponent"),z.ToggleComponent=fo,d(fo,"cc.ToggleComponent"),z.ToggleContainerComponent=Go,d(Go,"cc.ToggleContainerComponent"),z.WidgetComponent=cr,d(cr,"cc.WidgetComponent"),z.PageViewComponent=Ur,d(Ur,"cc.PageViewComponent"),z.PageViewIndicatorComponent=xr,d(xr,"cc.PageViewIndicatorComponent"),z.SafeAreaComponent=ps,d(ps,"cc.SafeAreaComponent"),d(fs,"cc.UICoordinateTrackerComponent"),z.BlockInputEventsComponent=ms,d(ms,"cc.BlockInputEventsComponent"),M(it.prototype,"View.prototype",[{name:"isAntiAliasEnabled",suggest:"The API of Texture2d have been largely modified, no alternative"},{name:"enableAntiAlias",suggest:"The API of Texture2d have been largely modified, no alternative"}]),k(it.prototype,"View.prototype",[{name:"adjustViewportMeta"},{name:"enableAutoFullScreen",suggest:"use screen.requestFullScreen() instead."},{name:"isAutoFullScreenEnabled"},{name:"setCanvasSize",suggest:"setting size in CSS pixels is not recommended, please use screen.windowSize instead."},{name:"getCanvasSize",suggest:"please use screen.windowSize instead."},{name:"getFrameSize",suggest:"getting size in CSS pixels is not recommended, please use screen.windowSize instead."},{name:"setFrameSize",suggest:"setting size in CSS pixels is not recommended, please use screen.windowSize instead."},{name:"getDevicePixelRatio",suggest:"use screen.devicePixelRatio instead."},{name:"convertToLocationInView"},{name:"enableRetina"},{name:"isRetinaEnabled"},{name:"setRealPixelResolution"}])}}}));
