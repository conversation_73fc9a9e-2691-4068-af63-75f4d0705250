System.register(["./gc-object-D18ulfCO.js","./index-Y4La_nfG.js","./global-exports-CLZKKIY2.js","./animation-component-DS1Wt_yW.js","./scene-ArUG4OfI.js","./component-CsuvAQKv.js","./factory-BOc5khhM.js","./deprecated-D5UVm7fE.js","./device-manager-B8lyUDPi.js","./buffer-barrier-q_79u36H.js","./instantiate-BgGIVvKs.js","./move-Bit2D-fl.js","./3d.js","./prefab-BQYc0LyR.js","./create-mesh-o_2FMF_K.js","./debug-view-BP17WHcy.js","./pipeline-state-manager-DQyhxoC_.js","./node-event-DTNosVQv.js","./director-8iUu7HD2.js","./deprecated-CuuzltLj.js","./touch-B157r-vS.js","./mesh-renderer-CTCVb-Pf.js","./mesh-Ba1cTOGw.js","./wasm-minigame-DBi57dFz.js","./deprecated-Bf8XgTPJ.js","./zlib.min-CyXMsivM.js","./deprecated-6ty78xzL.js","./model-renderer-D7qfPDfZ.js","./renderer-CZheciPr.js","./util-K-7Ucy5K.js","./capsule-DyZvzyLy.js","./skeleton-BrWwQslM.js","./camera-component-X7pwLmnP.js"],(function(t){"use strict";var e,n,r,i,o,a,s,u,l,c,h,f,p,d,v,_,y,g,m,E,T,A,C,b,S,w,P,O,N,I,x,R,k,L,M,D,B,U,F,V,G,W,z,j,H,Q,Y,q,Z,X,J,K,$,tt,et,nt,rt,it,ot,at,st,ut,lt,ct,ht,ft,pt,dt,vt,_t,yt,gt,mt,Et,Tt,At,Ct,bt,St,wt,Pt,Ot,Nt,It,xt,Rt,kt,Lt,Mt,Dt,Bt,Ut,Ft,Vt,Gt,Wt,zt,jt,Ht,Qt;return{setters:[function(t){e=t._,n=t.a,r=t.h,i=t.F,o=t.aL,a=t.w,s=t.b,u=t.o,l=t.aR,c=t.k,h=t.ay,f=t.aw,p=t.az,d=t.K,v=t.aS,_=t.aB,y=t.r,g=t.aO,m=t.R,E=t.at,T=t.O,A=t.aF,C=t.P,b=t.t,S=t.a6,w=t.as},function(t){P=t.c,O=t.a,N=t.aD,I=t.s,x=t.aE,R=t.b9,k=t.b,L=t.Q,M=t.az,D=t.M,B=t.L,U=t.ab,F=t.ba,V=t.g,G=t.b5,W=t.t,z=t.V,j=t.$,H=t.b4,Q=t.a$,Y=t.O,q=t.j,Z=t.a2,X=t.S},function(t){J=t.c},function(e){K=e.C,$=e.A,tt=e.c,et=e.b,nt=e.T,rt=e.i,it=e.e,ot=e.U,at=e.d,st=e.t,ut=e.n,lt=e.f,ct=e.h,ht=e.W,ft=e.j,pt=e.k,dt=e.l,vt=e.m,_t=e.o,yt=e.p,gt=e.q,mt=e.H,Et=e.O,Tt=e.Q,At=e.R,Ct=e.S,bt=e.r,St=e.V,wt=e.s,Pt=e.u,t({AnimCurve:e.w,Animation:e.a,AnimationClip:e.h,AnimationComponent:e.a,AnimationState:e.A,EventInfo:e.E,RatioSampler:e.v,computeRatioByType:e.y,sampleAnimationCurve:e.x})},function(t){Ot=t.b,Nt=t.I,It=t.D,xt=t.d,Rt=t.f,kt=t.J,Lt=t.y,Mt=t.N},function(t){Dt=t.A,Bt=t.C},null,null,function(t){Ut=t.d},function(t){Ft=t.j},function(t){Vt=t.i},function(t){Gt=t.s},null,null,null,null,null,null,function(t){Wt=t.d,zt=t.D},function(e){jt=e.c,Ht=e.d,Qt=e.e,t({getPathFromRoot:e.f,getWorldTransformUntilRoot:e.g})},null,null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){var Yt,qt,Zt,Xt,Jt;P(K+"EmbeddedPlayer")((Yt=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).begin=qt&&qt(),e.end=Zt&&Zt(),e.reconciledSpeed=Xt&&Xt(),e.playable=Jt&&Jt(),e}return e(n,t),n}(N),qt=O(Yt.prototype,"begin",[I],(function(){return 0})),Zt=O(Yt.prototype,"end",[I],(function(){return 0})),Xt=O(Yt.prototype,"reconciledSpeed",[I],(function(){return!1})),Jt=O(Yt.prototype,"playable",[I],(function(){return null})),Yt));var Kt,$t,te,ee=function(){},ne=function(){function t(t){this._randomAccess=t}return t.prototype.setTime=function(){},n(t,[{key:"randomAccess",get:function(){return this._randomAccess}}]),t}();P(K+"EmbeddedAnimationClipPlayable")((Kt=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).path=$t&&$t(),e.clip=te&&te(),e}return e(n,t),n.prototype.instantiate=function(t){var e=this.clip,n=this.path;if(!e)return null;var i=t.getChildByPath(n);if(!i)return r(3938,n,t.getPathInHierarchy(),e.name),null;var o=new $(e);return o.initialize(i),new oe(o)},n}(ee),$t=O(Kt.prototype,"path",[I],(function(){return""})),te=O(Kt.prototype,"clip",[I],(function(){return null})),Kt));var re,ie,oe=function(t){function n(e){var n;return(n=t.call(this,!0)||this)._animationState=void 0,n._animationState=e,n}e(n,t);var r=n.prototype;return r.destroy=function(){this._animationState.destroy()},r.play=function(){this._animationState.play()},r.pause=function(){this._animationState.pause()},r.stop=function(){this._animationState.stop()},r.setSpeed=function(t){this._animationState.speed=t},r.setTime=function(t){this._animationState.time=t},n}(ne);P(K+"EmbeddedParticleSystemPlayable")((re=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).path=ie&&ie(),e}return e(n,t),n.prototype.instantiate=function(t){var e=t.getChildByPath(this.path);if(!e)return i("Hierarchy path "+this.path+" does not exists."),null;var n=o("cc.ParticleSystem");if(!n)return i("Particle system is required for embedded particle system player."),null;var r=e.getComponent(n);return r?new ue(r):(i(this.path+" does not includes a particle system component."),null)},n}(ee),ie=O(re.prototype,"path",[I],(function(){return""})),re));var ae,se,ue=function(t){function n(e){var n;return(n=t.call(this,!1)||this)._particleSystem=void 0,n._particleSystem=e,n}e(n,t);var r=n.prototype;return r.destroy=function(){},r.play=function(){this._particleSystem.play()},r.pause=function(){this._particleSystem.stopEmitting()},r.stop=function(){this._particleSystem.stopEmitting()},r.setSpeed=function(t){this._particleSystem.simulationSpeed=t},n}(ne),le=I;P(K+"RealArrayTrack")((ae=function(t){function r(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this)._channels=se&&se(),e}e(r,t);var i=r.prototype;return i.channels=function(){return this._channels},i[tt]=function(){return new Ce(this._channels.map((function(t){return t.curve})))},n(r,[{key:"elementCount",get:function(){return this._channels.length},set:function(t){var e=this._channels.length;if(t<e)this._channels.splice(t);else if(t>e){var n;(n=this._channels).push.apply(n,Array.from({length:t-e},(function(){return new et(new x)})))}}}]),r}(nt),se=O(ae.prototype,"_channels",[le],(function(){return[]})),ae));var ce,he,fe,pe,de,ve,_e,ye,ge,me,Ee,Te,Ae,Ce=function(){function t(t){this._curves=t,this._result=new Array(t.length).fill(0)}return t.prototype.evaluate=function(t){for(var e=this._result,n=e.length,r=0;r<n;++r)e[r]=this._curves[r].evaluate(t);return this._result},n(t,[{key:"requiresDefault",get:function(){return!1}}]),t}(),be=P("cc.animation.UniformProxyFactory")((he=function(){function t(t,e){this.passIndex=fe&&fe(),this.uniformName=pe&&pe(),l(this,"channelIndex",de,this),this.passIndex=e||0,this.uniformName=t||""}return t.prototype.forTarget=function(t){if(t instanceof Ot){var e=this.passIndex,n=this.uniformName,r=this.channelIndex;if(e<0||e>=t.passes.length)a(3941,t.name,e);else{var o=t.passes[e],s=o.getHandle(n);if(s){if(kt(s)<Ft.SAMPLER1D){var u=void 0===r?s:o.getHandle(n,r,Ft.FLOAT);return u?Se(o,n)?{set:function(t){o.setUniformArray(u,t)}}:{set:function(t){o.setUniform(u,t)}}:void a(3943,t.name,e,n,r)}var l=Lt(s),c=o.properties[n],h=c&&c.value?""+c.value+Nt(c.type):It(c.type),f=xt.get(h);return f||(i("Illegal texture default value: "+h+"."),f=xt.get("default-texture")),{set:function(t){t||(t=f);var e=t.getGFXTexture();e&&e.width&&e.height&&(o.bindTexture(l,e),t instanceof Rt&&o.bindSampler(l,Ut.gfxDevice.getSampler(t.getSamplerInfo())))}}}a(3942,t.name,e,n)}}else a(3940,t)},t}(),fe=O(he.prototype,"passIndex",[I],(function(){return 0})),pe=O(he.prototype,"uniformName",[I],(function(){return""})),de=s(he.prototype,"channelIndex",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){}}),ce=he))||ce;function Se(t,e){for(var n,r=u(t.shaderInfo.blocks);!(n=r()).done;)for(var i,o=n.value,a=u(o.members);!(i=a()).done;){var s=i.value;if(s.name===e)return s.count>1}return!1}var we,Pe=P("cc.animation.MorphWeightValueProxy")((_e=function(){function t(){this.subMeshIndex=ye&&ye(),this.shapeIndex=ge&&ge()}return t.prototype.forTarget=function(t){var e=this;return{set:function(n){t.setWeight(n,e.subMeshIndex,e.shapeIndex)}}},t}(),ye=O(_e.prototype,"subMeshIndex",[I],(function(){return 0})),ge=O(_e.prototype,"shapeIndex",[I],(function(){return 0})),ve=_e))||ve,Oe=P("cc.animation.MorphWeightsValueProxy")((Ee=function(){function t(){this.subMeshIndex=Te&&Te()}return t.prototype.forTarget=function(t){var e=this;return{set:function(n){t.setWeights(n,e.subMeshIndex)}}},t}(),Te=O(Ee.prototype,"subMeshIndex",[I],(function(){return 0})),me=Ee))||me,Ne=P("cc.animation.MorphWeightsAllValueProxy")(Ae=function(){function t(){}return t.prototype.forTarget=function(t){return{set:function(e){for(var n,r,i=null!==(n=null==(r=t.mesh)?void 0:r.struct.primitives.length)&&void 0!==n?n:0,o=0;o<i;++o)t.setWeights(e,o)}}},t}())||Ae,Ie=Symbol("[[Owner]]");function xe(t,e){c(t[Ie]===e)}!function(t){t[t.FLOAT=0]="FLOAT",t[t.BOOLEAN=1]="BOOLEAN",t[t.TRIGGER=2]="TRIGGER",t[t.INTEGER=3]="INTEGER",t[t.VEC3_experimental=4]="VEC3_experimental",t[t.QUAT_experimental=5]="QUAT_experimental"}(we||(we={}));var Re,ke,Le,Me,De,Be,Ue,Fe,Ve=Symbol("CreateInstance"),Ge=function(){function t(t){this._refs=[],this.type=t}return t.prototype.bind=function(t,e){for(var n=arguments.length,r=new Array(n>2?n-2:0),i=2;i<n;i++)r[i-2]=arguments[i];return this._refs.push({fn:t,thisArg:e,args:r}),this.getValue()},n(t,[{key:"value",get:function(){return this.getValue()},set:function(t){this.setValue(t);for(var e,n=u(this._refs);!(e=n()).done;){var r=e.value,i=r.fn,o=r.thisArg,a=r.args;i.call.apply(i,[o,t].concat(a))}}}]),t}(),We=P("cc.animation.PlainVariable")((ke=function(){function t(t){if(this._type=Le&&Le(),this._value=Me&&Me(),void 0!==t)switch(this._type=t,t){default:break;case we.FLOAT:case we.INTEGER:this._value=0;break;case we.BOOLEAN:this._value=!1}}return t.prototype[Ve]=function(){return new ze(this._type,this._value)},n(t,[{key:"type",get:function(){return this._type}},{key:"value",get:function(){return this._value},set:function(t){this._value=t}}]),t}(),Le=O(ke.prototype,"_type",[I],(function(){return we.FLOAT})),Me=O(ke.prototype,"_value",[I],(function(){return 0})),Re=ke))||Re,ze=function(t){function n(e,n){var r;return(r=t.call(this,e)||this)._value=void 0,r._value=n,r}e(n,t);var r=n.prototype;return r.getValue=function(){return this._value},r.setValue=function(t){this._value=t},n}(Ge);!function(t){t[t.AFTER_CONSUMED=0]="AFTER_CONSUMED",t[t.NEXT_FRAME_OR_AFTER_CONSUMED=1]="NEXT_FRAME_OR_AFTER_CONSUMED"}(Fe||(Fe={})),c(!(Fe.AFTER_CONSUMED<<1));var je,He,Qe,Ye,qe,Ze,Xe=P("cc.animation.TriggerVariable")((Be=function(){function t(){this._flags=Ue&&Ue()}return t.prototype[Ve]=function(){return new Je(this.value,this.resetMode)},n(t,[{key:"type",get:function(){return we.TRIGGER}},{key:"value",get:function(){return!!(1&this._flags)},set:function(t){t?this._flags|=1:this._flags&=-2}},{key:"resetMode",get:function(){return(6&this._flags)>>1},set:function(t){this._flags&=-7,this._flags|=t<<1}}]),t}(),Ue=O(Be.prototype,"_flags",[I],(function(){return 0})),De=Be))||De,Je=function(t){function n(e,n){var r;return(r=t.call(this,we.TRIGGER)||this).resetMode=Fe.AFTER_CONSUMED,r._value=void 0,r.resetMode=n,r._value=e,r}e(n,t);var r=n.prototype;return r.getValue=function(){return this._value},r.setValue=function(t){this._value=t},n}(Ge),Ke=P("cc.animation.Vec3Variable")((He=function(){function t(){this._value=Qe&&Qe()}return t.prototype[Ve]=function(){return new $e(this.value)},n(t,[{key:"type",get:function(){return we.VEC3_experimental}},{key:"value",get:function(){return this._value},set:function(t){k.copy(this._value,t)}}]),t}(),Qe=O(He.prototype,"_value",[I],(function(){return new k})),je=He))||je,$e=function(t){function n(e){var n;return(n=t.call(this,we.VEC3_experimental)||this)._value=new k,k.copy(n._value,e),n}e(n,t);var r=n.prototype;return r.getValue=function(){return this._value},r.setValue=function(t){k.copy(this._value,t)},n}(Ge),tn=P("cc.animation.QuatVariable")((qe=function(){function t(){this._value=Ze&&Ze()}return t.prototype[Ve]=function(){return new en(this._value)},n(t,[{key:"type",get:function(){return we.QUAT_experimental}},{key:"value",get:function(){return this._value},set:function(t){L.copy(this._value,t)}}]),t}(),Ze=O(qe.prototype,"_value",[I],(function(){return new L})),Ye=qe))||Ye,en=function(t){function n(e){var n;return(n=t.call(this,we.QUAT_experimental)||this)._value=new L,L.copy(n._value,e),n}e(n,t);var r=n.prototype;return r.getValue=function(){return this._value},r.setValue=function(t){L.copy(this._value,t)},n}(Ge);function nn(t,e){var n;switch(t){case we.FLOAT:case we.INTEGER:case we.BOOLEAN:n=new We(t);break;case we.TRIGGER:n=new Xe;break;case we.VEC3_experimental:n=new Ke;break;case we.QUAT_experimental:n=new tn;break;default:throw new Error("Unknown variable type "+t)}return void 0!==e&&(n.value=e),n}var rn,on,an,sn,un,ln,cn=function(t){function n(e){var n;return(n=t.call(this,e+" transition is invalid")||this).name="TransitionRejectError",n}return e(n,t),n}(h(Error)),hn=function(t){function n(e){return t.call(this,"Graph variable "+e+" is not defined")||this}return e(n,t),n}(h(Error)),fn=function(t){function n(e,n,r){return t.call(this,"Expect graph variable "+e+" to have type '"+n+"' instead of received '"+(null!=r?r:typeof r)+"'")||this}return e(n,t),n}(h(Error));function pn(t){var e=t[M];if("object"==typeof e&&e){var n=e;return null==n.clone?void 0:n.clone(t)}}var dn,vn,_n,yn,gn,mn,En,Tn,An,Cn,bn,Sn,wn=Symbol("[[Outgoing transitions]]"),Pn=Symbol("[[Incoming transitions]]"),On=P,Nn=I,In=On("cc.animation.State")((on=function(t){function n(){var e;return(e=t.call(this)||this).name=an&&an(),e[wn]=[],e[Pn]=[],e}return e(n,t),n.prototype.copyTo=function(t){t.name=this.name,t[M]=pn(this)},n}(N),an=O(on.prototype,"name",[Nn],(function(){return""})),rn=on))||rn,xn=On(K+"InteractiveState")((un=function(t){function r(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this)._components=ln&&ln(),e}e(r,t);var i=r.prototype;return i.addComponent=function(t){var e=new t;return this._components.push(e),e},i.removeComponent=function(t){f(this._components,t)},i.instantiateComponents=function(){return this._components.map((function(t){return Vt(t)}))},i.copyTo=function(e){t.prototype.copyTo.call(this,e),e._components=this.instantiateComponents()},n(r,[{key:"components",get:function(){return this._components}}]),r}(In),ln=O(un.prototype,"_components",[Nn],(function(){return[]})),sn=un))||sn,Rn=P(K+"AnimationGraphEventBinding")((vn=function(){function t(){this.methodName=_n&&_n()}var e=t.prototype;return e.emit=function(t){this.methodName&&rt(t,this.methodName,[])},e.copyTo=function(t){return t.methodName=this.methodName,this},n(t,[{key:"isBound",get:function(){return!!this.methodName}}]),t}(),_n=O(vn.prototype,"methodName",[I],(function(){return""})),dn=vn))||dn,kn=P("cc.animation.Motion")((gn=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).motion=mn&&mn(),e.speed=En&&En(),e.speedMultiplier=Tn&&Tn(),e.speedMultiplierEnabled=An&&An(),e.transitionInEventBinding=Cn&&Cn(),e.transitionOutEventBinding=bn&&bn(),e}e(n,t);var r=n.prototype;return r.__callOnAfterDeserializeRecursive=function(){var t;null==(t=this.motion)||t.__callOnAfterDeserializeRecursive()},r.copyTo=function(e){var n,r;return t.prototype.copyTo.call(this,e),e.motion=null!==(n=null==(r=this.motion)?void 0:r.clone())&&void 0!==n?n:null,e.speed=this.speed,e.speedMultiplier=this.speedMultiplier,e.speedMultiplierEnabled=this.speedMultiplierEnabled,this.transitionInEventBinding.copyTo(e.transitionInEventBinding),this.transitionOutEventBinding.copyTo(e.transitionOutEventBinding),this},n}(xn),mn=O(gn.prototype,"motion",[I],(function(){return null})),En=O(gn.prototype,"speed",[I],(function(){return 1})),Tn=O(gn.prototype,"speedMultiplier",[I],(function(){return""})),An=O(gn.prototype,"speedMultiplierEnabled",[I],(function(){return!1})),Cn=O(gn.prototype,"transitionInEventBinding",[I],(function(){return new Rn})),bn=O(gn.prototype,"transitionOutEventBinding",[I],(function(){return new Rn})),yn=gn))||yn,Ln=Symbol("[[OnAfterDeserialized]]"),Mn=P(K+"AnimationGraphLike")(Sn=function(t){function n(){return t.apply(this,arguments)||this}return e(n,t),n}(Dt))||Sn;function Dn(t,e,n){var r=Object.prototype.propertyIsEnumerable;if(!r.call(t,e))return t;if(n in t)return t;var i={};return"symbol"==typeof e?(Object.entries(t).forEach((function(t){var e=t[0],n=t[1];i[e]=n})),Object.getOwnPropertySymbols(t).forEach((function(o){r.call(t,o)&&(i[o===e?n:o]=t[o])}))):(Object.entries(t).forEach((function(t){var r=t[0],o=t[1];i[r===e?n:r]=o})),Object.getOwnPropertySymbols(t).forEach((function(e){r.call(t,e)&&(i[e]=t[e])}))),i}var Bn,Un,Fn,Vn,Gn,Wn,zn,jn,Hn=function(){var t=!1;try{t=[]instanceof function(){function t(){}return t[Symbol.hasInstance]=function(t){return Array.isArray(t)},t}()}catch(e){t=!1}return t?function(t){function e(){throw new Error("This function can not be called as a constructor.")}return Object.defineProperty(e,Symbol.hasInstance,{value:function(e){return e instanceof t}}),e}:function(t){return t}}(),Qn=P(K+"PoseGraphNodeShell")((Un=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this)._bindings=Fn&&Fn(),e}e(n,t);var r=n.prototype;return r.getBindings=function(){return this._bindings},r.addBinding=function(t,e,n){this._emplaceBinding(new Zn(t,e,n))},r.deleteBinding=function(t){var e=this._findBindingIndex(t);e>=0&&this._bindings.splice(e,1)},r.moveArrayElementBindingForward=function(t,e,n){for(var r=this._bindings,i=[],o=0;o<r.length;++o){var a=r[o],s=a.inputPath,u=s[0],l=s[1];u===t&&(void 0===l?-1:l)>=e&&(i.push(a),r.splice(o,1))}for(var c=0,h=i;c<h.length;c++){var f=h[c],p=f.inputPath,d=p[0],v=p[1],_=void 0===v?-1:v;this.addBinding([d,_+(n?-1:1)],f.producer,f.outputIndex)}},r.deleteBindingTo=function(t){for(var e=this._bindings,n=0;n<e.length;++n)e[n].producer===t&&e.splice(n,1)},r.findBinding=function(t){var e=this._findBindingIndex(t);return e>=0?this._bindings[e]:void 0},r._findBindingIndex=function(t){return this._bindings.findIndex((function(e){return Yn(e.inputPath,t)}))},r._emplaceBinding=function(t){var e=this._bindings.findIndex((function(e){return Yn(e.inputPath,t.inputPath)}));e>=0?this._bindings[e]=t:this._bindings.push(t)},n}(N),Fn=O(Un.prototype,"_bindings",[I],(function(){return[]})),Bn=Un))||Bn;function Yn(t,e){var n=t[0],r=t[1],i=e[0],o=e[1];return n===i&&r===o}var qn,Zn=P(K+"PoseGraphNodeInputBinding")((Gn=function(){function t(t,e,n){this._inputPath=Wn&&Wn(),this._producer=zn&&zn(),this._outputIndex=jn&&jn(),this._inputPath=t,this._producer=e,void 0!==n&&(this._outputIndex=n)}return n(t,[{key:"inputPath",get:function(){return this._inputPath}},{key:"producer",get:function(){return this._producer}},{key:"outputIndex",get:function(){return this._outputIndex}}]),t}(),Wn=O(Gn.prototype,"_inputPath",[I],null),zn=O(Gn.prototype,"_producer",[I],null),jn=O(Gn.prototype,"_outputIndex",[I],(function(){return 0})),Vn=Gn))||Vn,Xn=function(t){function n(e){return t.call(this,"Can not add the specified "+e.toString()+" since it has already been added into another graph.")||this}return e(n,t),n}(h(Error)),Jn=function(t){function n(e){return t.call(this,"Can not perform specified operation on "+e.toString()+" since it has not been added in to graph.")||this}return e(n,t),n}(h(Error));!function(t){t[t.FLOAT=0]="FLOAT",t[t.INTEGER=1]="INTEGER",t[t.BOOLEAN=2]="BOOLEAN",t[t.VEC3=3]="VEC3",t[t.QUAT=4]="QUAT",t[t.POSE=5]="POSE"}(qn||(qn={}));var Kn=function(t){function n(){return t.apply(this,arguments)||this}return e(n,t),n}(N),$n=new WeakMap;function tr(t){var e=$n.get(t);if(e)return e;var n={};return $n.set(t,n),n}function er(t){return function(e){if(p(e,Kn)||(d("This kind of decorator should only be applied to pose graph node classes."),0)){var n=tr(e);t(n)}}}var nr=function(t){return er((function(e){e.category=t}))},rr=function(t){return er((function(e){e.factory=t}))},ir=function(t){return void 0===t&&(t=!0),er((function(e){e.hide=t}))},or=function(t){return er((function(e){var n;Object.assign(null!==(n=e.appearance)&&void 0!==n?n:e.appearance={},t)}))};var ar=["arraySyncGroup"],sr=function(){function t(){this._classInputMap=new WeakMap}var e=t.prototype;return e.setPropertyNodeInputRecord=function(t,e,n){var r=this._classInputMap.get(t);r||(r={properties:{}},this._classInputMap.set(t,r)),n.arraySyncGroup;var i=v(n,ar),o=n.arraySyncGroup;if(o){var a,s;r.arraySyncGroups||(r.arraySyncGroups={});var u=null!==(s=(a=r.arraySyncGroups)[o])&&void 0!==s?s:a[o]={members:[]};u.members.includes(e)||u.members.push(e),i.arraySyncGroup=u}r.properties[e]=Object.freeze(i)},e.getInputKeys=function(t){var e=this,n=[];return function r(i){if(i){r(_(i));var o=e._classInputMap.get(i);if(o)for(var a=function(){var e=u[s][0];if(n.findIndex((function(t){var n=t[0];return e===n}))>=0)return 1;var r=t[e];if(Array.isArray(r))for(var i=0;i<r.length;++i)n.push([e,i]);else n.push([e])},s=0,u=Object.entries(o.properties);s<u.length;s++)a()}}(t.constructor),n},e.isPoseInput=function(t,e){var n=e[0],r=this._getPropertyNodeInputRecord(t.constructor,n);return!!r&&r.type===qn.POSE},e.getInputMetadata=function(t,e){var n=e[0],r=e[1],i=void 0===r?-1:r,o=this._getPropertyNodeInputRecord(t.constructor,n);if(o){var a=t[n];if(Array.isArray(a)){if(i<0||i>=a.length)return;var s,u,l=null!==(s=null==(u=o.getArrayElementDisplayName)?void 0:u.call(t,i))&&void 0!==s?s:o.displayName;return{type:o.type,displayName:l,deletable:!(o.arraySyncGroup&&o.arraySyncGroupFollower),insertPoint:!0}}return{type:o.type,displayName:o.displayName}}},e.hasInput=function(t,e){var n=e[0],r=e[1],i=void 0===r?-1:r;if(!this._getPropertyNodeInputRecord(t.constructor,n))return!1;var o=t[n];return!Array.isArray(o)||!(i<0||i>=o.length)},e.getInputInsertInfos=function(t){for(var e={},n=t.constructor;n;n=_(n)){var r=this._classInputMap.get(n);if(r)for(var i in r.properties){var o=r.properties[i],a=t[i];if(Array.isArray(a)){if(o.arraySyncGroup&&o.arraySyncGroupFollower)continue;e[i]={displayName:i}}}}return e},e.deleteInput=function(t,e,n){var r=n[0],i=n[1],o=void 0===i?-1:i,a=this._getPropertyNodeInputRecord(e.constructor,r);if(a){var s=e[r];if(Array.isArray(s)&&!(o<0||o>=s.length)){var u=a.arraySyncGroup;u?this._deleteInputInArraySyncGroup(t,e,u,s.length,o):lr(t,e,n)}}},e.insertInput=function(t,e,n){var r=n,i=this._getPropertyNodeInputRecord(e.constructor,r);if(i){var o=e[r];if(Array.isArray(o)){var a=o.length,s=i.arraySyncGroup;s?this._insertInputInArraySyncGroup(t,e,s,o.length,a):ur(t,e,[r,a],cr(i.type))}}},e._getPropertyNodeInputRecord=function(t,e){if(t){var n=this._classInputMap.get(t);if(n){var r=n.properties[e];if(r)return r}return this._getPropertyNodeInputRecord(_(t),e)}},e._insertInputInArraySyncGroup=function(t,e,n,r,i){for(var o=0;o<n.members.length;++o){var a=n.members[o],s=this._getPropertyNodeInputRecord(e.constructor,a),u=e[a];Array.isArray(u)&&u.length===r&&ur(t,e,[a,i],cr(s.type))}},e._deleteInputInArraySyncGroup=function(t,e,n,r,i){for(var o=0;o<n.members.length;++o){var a=n.members[o];this._getPropertyNodeInputRecord(e.constructor,a);var s=e[a];Array.isArray(s)&&s.length===r&&lr(t,e,[a,i])}},t}();function ur(t,e,n,r){var i=t.getShell(e);if(!i)throw new Jn(e);var o=n[0],a=n[1],s=void 0===a?-1:a,u=e[o];Array.isArray(u)&&(u.splice(s,0,r),i.moveArrayElementBindingForward(o,s+1,!1))}function lr(t,e,n){var r=t.getShell(e);if(!r)throw new Jn(e);var i=n[0],o=n[1],a=void 0===o?-1:o,s=e[i];Array.isArray(s)&&(a<0||a>=s.length||(r.deleteBinding(n),s.splice(a,1),r.moveArrayElementBindingForward(i,a+1,!0)))}function cr(t){switch(t){default:case qn.FLOAT:case qn.INTEGER:return 0;case qn.BOOLEAN:return!1;case qn.POSE:return null;case qn.VEC3:return new k;case qn.QUAT:return new L}}var hr,fr=new sr,pr=new k,dr=new k,vr=new L;new L;var _r,yr,gr,mr=function(){function t(){this._position=new k,this._rotation=new L,this._scale=k.clone(k.ONE)}return t.clone=function(e){var n=new t;return t.copy(n,e),n},t.setIdentity=function(t){return k.copy(t._position,k.ZERO),L.copy(t._rotation,L.IDENTITY),k.copy(t._scale,k.ONE),t},t.copy=function(t,e){return k.copy(t._position,e._position),L.copy(t._rotation,e._rotation),k.copy(t._scale,e._scale),t},t.equals=function(t,e,n){return k.equals(t._position,e._position,n)&&L.equals(t._rotation,e._rotation,n)&&k.equals(t._scale,e._scale,n)},t.strictEquals=function(t,e){return k.strictEquals(t._position,e._position)&&L.strictEquals(t._rotation,e._rotation)&&k.strictEquals(t._scale,e._scale)},t.lerp=function(e,n,r,i){return 0===i?t.copy(e,n):1===i?t.copy(e,r):(k.lerp(e._position,n._position,r._position,i),L.slerp(e._rotation,n._rotation,r._rotation,i),k.lerp(e._scale,n._scale,r._scale,i),e)},t.multiply=function(t,e,n){var r=L.multiply(vr,e._rotation,n._rotation),i=k.multiply(pr,n._scale,e._scale),o=k.multiply(dr,n._position,e._scale);return k.transformQuat(o,o,e._rotation),k.add(o,o,e._position),k.copy(t._position,o),L.copy(t._rotation,r),k.copy(t._scale,i),t},t.invert=function(t,e){var n=t._rotation,r=t._scale,i=t._position;return L.invert(n,e._rotation),Er(r,e._scale,B),k.negate(i,e._position),k.multiply(i,i,r),k.transformQuat(i,i,n),t},t.fromMatrix=function(t,e){return D.toSRT(e,t._rotation,t._position,t._scale),t},t.toMatrix=function(t,e){return D.fromSRT(t,e._rotation,e._position,e._scale)},n(t,[{key:"position",get:function(){return this._position},set:function(t){k.copy(this._position,t)}},{key:"rotation",get:function(){return this._rotation},set:function(t){L.copy(this._rotation,t)}},{key:"scale",get:function(){return this._scale},set:function(t){k.copy(this._scale,t)}}]),t}();function Er(t,e,n){var r=e.x,i=e.y,o=e.z;return k.set(t,Math.abs(r)<=n?0:1/r,Math.abs(i)<=n?0:1/i,Math.abs(o)<=n?0:1/o)}function Tr(t,e,n){return k.subtract(t.position,e.position,n.position),wr(t.rotation,n.rotation,e.rotation),k.subtract(t.scale,e.scale,n.scale),t}hr=mr,mr.IDENTITY=Object.freeze(new hr),mr.ZERO=Object.freeze((_r=new hr,k.copy(_r._position,k.ZERO),L.set(_r._rotation,0,0,0,0),k.copy(_r._scale,k.ZERO),_r)),mr.calculateRelative=(yr=new L,gr=new k,function(t,e,n){var r=L.invert(yr,n._rotation),i=Er(gr,n._scale,B),o=k.subtract(dr,e._position,n._position);return k.transformQuat(o,o,r),k.multiply(o,o,i),k.copy(t._position,o),L.multiply(t._rotation,r,e._rotation),k.multiply(t._scale,e._scale,i),t});var Ar,Cr,br,Sr=(Ar=new L,function(t,e,n,r){k.scaleAndAdd(t.position,e.position,n.position,r);var i=L.slerp(Ar,L.IDENTITY,n.rotation,r);return L.multiply(t.rotation,i,e.rotation),k.scaleAndAdd(t.scale,e.scale,n.scale,r),t}),wr=(Cr=new L,function(t,e,n){var r=L.invert(Cr,e);return L.multiply(t,n,r)}),Pr=Object.freeze(function(){var t=new mr;return t.position=k.ZERO,t.rotation=L.IDENTITY,t.scale=k.ZERO,t}()),Or=function(){function t(t,e){this.transforms=void 0,this.auxiliaryCurves=void 0,this._poseTransformSpace=br.LOCAL,this.transforms=t,this.auxiliaryCurves=e}return t._create=function(e,n){return new t(e,n)},t}();!function(t){t[t.LOCAL=0]="LOCAL",t[t.COMPONENT=1]="COMPONENT"}(br||(br={}));var Nr=function(){function t(t){this._involvedTransforms=new Uint16Array(t)}return n(t,[{key:"involvedTransforms",get:function(){return this._involvedTransforms}}]),t}();function Ir(t,e,n,r){void 0===r&&(r=void 0),xr(t.transforms,e.transforms,n,r),Dr(t.auxiliaryCurves,e.auxiliaryCurves,n)}function xr(t,e,n,r){void 0===r&&(r=void 0);var i=t.length;if(c(i===t.length),0!==n)if(1!==n)if(r)for(var o=0;o<r.involvedTransforms.length;++o){var a=r.involvedTransforms[o];Mr(t,e,n,a)}else for(var s=0;s<i;++s)Mr(t,e,n,s);else r?Rr(t,e,r):t.set(e)}function Rr(t,e,n){var r=t.length;c(r===t.length);for(var i=0;i<n.involvedTransforms.length;++i){var o=n.involvedTransforms[i];t.copyRange(o,e,o,1)}}var kr,Lr,Mr=(kr=new mr,Lr=new mr,function(t,e,n,r){var i=t.getTransform(r,Lr),o=e.getTransform(r,kr);mr.lerp(i,i,o,n),t.setTransform(r,i)});function Dr(t,e,n){var r=e.length;c(r===t.length);for(var i=0;i<r;++i)t[i]=U(t[i],e[i],n)}var Br=function(){var t=new mr,e=new mr;return function(n,r,i){var o=r.getTransform(i,t),a=n.getTransform(i,e);Tr(a,a,o),n.setTransform(i,a)}}();function Ur(t,e){var n=t.length;c(n===e.length);for(var r=0;r<n;++r)Br(t,e,r)}function Fr(t,e){var n=t.length;c(n===e.length);for(var r=0;r<t.length;++r)t[r]-=e[r]}function Vr(t,e,n,r){void 0===r&&(r=void 0),jr(t.transforms,e.transforms,n,r),Hr(t.auxiliaryCurves,e.auxiliaryCurves,n)}var Gr,Wr,zr=function(){var t=new mr,e=new mr;return function(n,r,i,o){var a=r.getTransform(o,t),s=n.getTransform(o,e);Sr(s,s,a,i),n.setTransform(o,s)}}();function jr(t,e,n,r){void 0===r&&(r=void 0);var i=t.length;if(c(i===e.length),r)for(var o=0;o<r.involvedTransforms.length;++o){var a=r.involvedTransforms[o];zr(t,e,n,a)}else for(var s=0;s<i;++s)zr(t,e,n,s)}function Hr(t,e,n){var r=t.length;c(r===e.length);for(var i=0;i<t.length;++i)t[i]+=e[i]*n}!function(t){t[t.NO=0]="NO",t[t.LOCAL=1]="LOCAL",t[t.COMPONENT=2]="COMPONENT"}(Wr||(Wr={})),y(Wr);var Qr,Yr,qr,Zr,Xr,Jr,Kr=P(K+"PoseNode")(Gr=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this)._dependencyEvaluation=void 0,e}e(n,t);var r=n.prototype;return r.update=function(t){var e;null==(e=this._dependencyEvaluation)||e.evaluate(),this.doUpdate(t)},r.evaluate=function(t,e){var n=this.doEvaluate(t),r=n._poseTransformSpace;switch(e){default:case Wr.NO:break;case Wr.LOCAL:r===br.COMPONENT&&t._poseTransformsSpaceComponentToLocal(n),c(n._poseTransformSpace===br.LOCAL);break;case Wr.COMPONENT:r===br.LOCAL&&t._poseTransformsSpaceLocalToComponent(n),c(n._poseTransformSpace===br.COMPONENT)}return n},n.evaluateDefaultPose=function(t,e){switch(e){default:case Wr.NO:case Wr.LOCAL:return t.pushDefaultedPose();case Wr.COMPONENT:return t.pushDefaultedPoseInComponentSpace()}},r._setDependencyEvaluation=function(t){this._dependencyEvaluation=t},r._forceEvaluateEvaluation=function(){var t;null==(t=this._dependencyEvaluation)||t.evaluate()},n}(Kn))||Gr,$r=function(t){function r(e){var n;return(n=t.call(this)||this)._outputTypes=[],n._outputTypes=e,n}e(r,t);var i=r.prototype;return i.getOutputType=function(t){return this._outputTypes[t]},i.link=function(){},n(r,[{key:"outputCount",get:function(){return this._outputTypes.length}}]),r}(Kn),ti=function(t){function n(e){return t.call(this,[e])||this}return e(n,t),n.prototype.selfEvaluate=function(t){t[0]=this.selfEvaluateDefaultOutput()},n}($r);function ei(t){return function(e,n){var r=e.constructor;t.type!==qn.POSE||p(r,Kr)?p(r,Kr)||p(r,$r)?ni(t)(e,n):d("@input can be only applied to fields of subclasses of PoseNode or PureValueNode."):d("@input specifying pose input can be only applied to fields of subclasses of PoseNode.")}}function ni(t){return function(e,n){if("string"==typeof n){var r=e.constructor;fr.setPropertyNodeInputRecord(r,n,t),F(e,n).__internalFlags|=g.STANDALONE|g.IMPLICIT_VISIBLE}else d("@input can be only applied to string-named fields.")}}var ri,ii,oi,ai,si,ui,li,ci,hi,fi,pi,di,vi,_i,yi,gi,mi,Ei,Ti,Ai,Ci,bi,Si,wi,Pi,Oi,Ni,Ii,xi,Ri,ki,Li,Mi,Di,Bi,Ui,Fi,Vi,Gi,Wi,zi,ji,Hi,Qi,Yi,qi,Zi,Xi,Ji,Ki,$i,to,eo,no,ro,io,oo,ao,so,uo,lo=(Qr=P(K+"PoseGraphOutputNode"),Yr=or({themeColor:"#CD3A58",inline:!0}),qr=ni({type:qn.POSE}),Qr(Zr=Yr((Xr=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return e=t.call.apply(t,[this].concat(r))||this,l(e,"pose",Jr,m(e)),e}return e(n,t),n}(Kn),Jr=s(Xr.prototype,"pose",[I,qr],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Zr=Xr))||Zr)||Zr),co=P(K+"PoseGraph")((ii=function(t){function r(){var e;return(e=t.call(this)||this)._outputNode=oi&&oi(),e._nodes=ai&&ai(),e._shells=si&&si(),e._shellMap=ui&&ui(),e.addNode(e._outputNode),e}e(r,t);var i=r.prototype;return i.__callOnAfterDeserializeRecursive=function(){c(this._nodes.length===this._shells.length);for(var t=0;t<this._nodes.length;++t){var e=this._nodes[t],n=this._shells[t];this._shellMap.set(e,n),null==e.__callOnAfterDeserializeRecursive||e.__callOnAfterDeserializeRecursive()}},i.nodes=function(){return this._nodes.values()},i.addNode=function(t){if(this._shellMap.has(t))throw new Xn(t);var e=new Qn;return this._shells.push(e),this._nodes.push(t),this._shellMap.set(t,e),t},i.removeNode=function(t){if(t!==this._outputNode){var e=this._nodes.indexOf(t);if(!(e<0)){c(this._shellMap.has(t));for(var n,r=u(this._shells);!(n=r()).done;)n.value.deleteBindingTo(t);E(this._shells,e),E(this._nodes,e),this._shellMap.delete(t)}}else d("Can not remove the output node.")},i.getShell=function(t){return this._shellMap.get(t)},n(r,[{key:"outputNode",get:function(){return this._outputNode}}]),r}(N),oi=O(ii.prototype,"_outputNode",[I],(function(){return new lo})),ai=O(ii.prototype,"_nodes",[I],(function(){return[]})),si=O(ii.prototype,"_shells",[I],(function(){return[]})),ui=O(ii.prototype,"_shellMap",[I],(function(){return new Map})),ri=ii))||ri,ho=P(K+"Transition")((ci=function(t){function n(e,n,r){var i;return(i=t.call(this)||this).from=hi&&hi(),i.to=fi&&fi(),i.conditions=pi&&pi(),i[Ie]=void 0,i.from=e,i.to=n,r&&(i.conditions=r),i}return e(n,t),n.prototype.copyTo=function(t){t.conditions=this.conditions.map((function(t){return t.clone()}))},n}(N),hi=O(ci.prototype,"from",[I],null),fi=O(ci.prototype,"to",[I],null),pi=O(ci.prototype,"conditions",[I],(function(){return[]})),li=ci))||li,fo=P(K+"DurationalTransition")((vi=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).destinationStart=_i&&_i(),e.relativeDestinationStart=yi&&yi(),e.startEventBinding=gi&&gi(),e.endEventBinding=mi&&mi(),e[Ie]=void 0,e}return e(n,t),n.prototype.copyTo=function(e){t.prototype.copyTo.call(this,e),e.destinationStart=this.destinationStart,e.relativeDestinationStart=this.relativeDestinationStart,this.startEventBinding.copyTo(e.startEventBinding),this.endEventBinding.copyTo(e.endEventBinding)},n}(ho),_i=O(vi.prototype,"destinationStart",[I],(function(){return 0})),yi=O(vi.prototype,"relativeDestinationStart",[I],(function(){return!1})),gi=O(vi.prototype,"startEventBinding",[I],(function(){return new Rn})),mi=O(vi.prototype,"endEventBinding",[I],(function(){return new Rn})),di=vi))||di,po=P(K+"AnimationTransition")((Ti=function(t){function r(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).duration=Ai&&Ai(),e.relativeDuration=Ci&&Ci(),e.exitConditionEnabled=bi&&bi(),e._exitCondition=Si&&Si(),e}return e(r,t),r.prototype.copyTo=function(e){t.prototype.copyTo.call(this,e),e.duration=this.duration,e.relativeDuration=this.relativeDuration,e.exitConditionEnabled=this.exitConditionEnabled,e.exitCondition=this.exitCondition},n(r,[{key:"exitCondition",get:function(){return this._exitCondition},set:function(t){this._exitCondition=t}}]),r}(fo),Ai=O(Ti.prototype,"duration",[I],(function(){return.3})),Ci=O(Ti.prototype,"relativeDuration",[I],(function(){return!1})),bi=O(Ti.prototype,"exitConditionEnabled",[I],(function(){return!0})),Si=O(Ti.prototype,"_exitCondition",[I],(function(){return 1})),Ei=Ti))||Ei;function vo(t){return t instanceof po}var _o,yo=P(K+"EmptyState")(wi=function(t){function n(){return t.apply(this,arguments)||this}return e(n,t),n}(In))||wi,go=P(K+"EmptyStateTransition")((Oi=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).duration=Ni&&Ni(),e}return e(n,t),n.prototype.copyTo=function(e){t.prototype.copyTo.call(this,e),e.duration=this.duration},n}(fo),Ni=O(Oi.prototype,"duration",[I],(function(){return.3})),Pi=Oi))||Pi,mo=P(K+"ProceduralPoseState")((xi=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).graph=Ri&&Ri(),e.transitionInEventBinding=ki&&ki(),e.transitionOutEventBinding=Li&&Li(),e}e(n,t);var r=n.prototype;return r.__callOnAfterDeserializeRecursive=function(){this.graph.__callOnAfterDeserializeRecursive()},r.copyTo=function(e){return t.prototype.copyTo.call(this,e),this.transitionInEventBinding.copyTo(e.transitionInEventBinding),this.transitionOutEventBinding.copyTo(e.transitionOutEventBinding),this},n}(In),Ri=O(xi.prototype,"graph",[I],(function(){return new co})),ki=O(xi.prototype,"transitionInEventBinding",[I],(function(){return new Rn})),Li=O(xi.prototype,"transitionOutEventBinding",[I],(function(){return new Rn})),Ii=xi))||Ii,Eo=Hn(mo),To=P(K+"ProceduralPoseTransition")((Di=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).duration=Bi&&Bi(),e}return e(n,t),n.prototype.copyTo=function(e){t.prototype.copyTo.call(this,e),e.duration=this.duration},n}(fo),Bi=O(Di.prototype,"duration",[I],(function(){return.3})),Mi=Di))||Mi,Ao=Hn(To),Co=P("cc.animation.StateMachine")((Fi=function(t){e(i,t);var r=i.prototype;function i(e){var n;return(n=t.call(this)||this)._states=Vi&&Vi(),n._transitions=Gi&&Gi(),n._entryState=Wi&&Wi(),n._exitState=zi&&zi(),n._anyState=ji&&ji(),n._allowEmptyStates=!0,n._allowEmptyStates=null!=e&&e,n._entryState=n._addState(new In),n._entryState.name="Entry",n._exitState=n._addState(new In),n._exitState.name="Exit",n._anyState=n._addState(new In),n._anyState.name="Any",n}return r.__callOnAfterDeserializeRecursive=function(){this[Ln]();for(var t=this._states.length,e=0;e<t;++e){var n=this._states[e];n instanceof bo?n.stateMachine.__callOnAfterDeserializeRecursive():(n instanceof mo||n instanceof kn)&&n.__callOnAfterDeserializeRecursive()}},r[Ln]=function(){this._states.forEach((function(){})),this._transitions.forEach((function(t){t.from[wn].push(t),t.to[Pn].push(t)}))},r.states=function(){return this._states},r.transitions=function(){return this._transitions},r.getTransitionsBetween=function(t,e){return xe(t,this),xe(e,this),t[wn].filter((function(t){return t.to===e}))},r.getOutgoings=function(t){return xe(t,this),t[wn]},r.getIncomings=function(t){return xe(t,this),t[Pn]},r.addMotion=function(){return this._addState(new kn)},r.addSubStateMachine=function(){return this._addState(new bo(this._allowEmptyStates))},r.addEmpty=function(){if(!this._allowEmptyStates)throw new Error("Empty states are now allowed in this state machine.");return this._addState(new yo)},r.addProceduralPoseState=function(){return this._addState(new mo)},r.remove=function(t){xe(t,this),t!==this.entryState&&t!==this.exitState&&t!==this.anyState&&(this.eraseTransitionsIncludes(t),f(this._states,t))},r.connect=function(t,e,n){if(xe(t,this),xe(e,this),e===this.entryState)throw new cn("to-entry");if(e===this.anyState)throw new cn("to-any");if(t===this.exitState)throw new cn("from-exit");var r=t instanceof kn||t===this._anyState?new po(t,e,n):t instanceof yo?new go(t,e,n):t instanceof mo?new To(t,e,n):new ho(t,e,n);return this._transitions.push(r),t[wn].push(r),e[Pn].push(r),r},r.disconnect=function(t,e){xe(t,this),xe(e,this);for(var n=t[wn],r=e[Pn],i=this._transitions,o=n.filter((function(t){return t.to===e})),a=o.length,s=function(){var t=o[u];f(n,t),c(f(i,t)),T(A(r,(function(e){return e===t})))},u=0;u<a;++u)s()},r.removeTransition=function(t){c(f(this._transitions,t)),T(A(t.from[wn],(function(e){return e===t}))),T(A(t.to[Pn],(function(e){return e===t})))},r.eraseOutgoings=function(t){var e=this;xe(t,this);for(var n=t[wn],r=function(){var t=n[i],r=t.to;c(f(e._transitions,t)),T(A(r[Pn],(function(e){return e===t})))},i=0;i<n.length;++i)r();n.length=0},r.eraseIncomings=function(t){var e=this;xe(t,this);for(var n=t[Pn],r=function(){var t=n[i],r=t.from;c(f(e._transitions,t)),T(A(r[wn],(function(e){return e===t})))},i=0;i<n.length;++i)r();n.length=0},r.eraseTransitionsIncludes=function(t){this.eraseIncomings(t),this.eraseOutgoings(t)},r.adjustTransitionPriority=function(t,e){var n=t.from;if(0!==e){var r=n[wn],i=r.indexOf(t),o=V(i+e,0,r.length-1),a=this._transitions,s=a.indexOf(t);if(o>i)for(var u=i+1;u<=o;++u){var l=r[u],c=a.indexOf(l);a[s]=l,s=c}else if(i>o)for(var h=i-1;h>=o;--h){var f=r[h],p=a.indexOf(f);a[s]=f,s=p}a[s]=t,Gt(r,i,o)}},r.copyTo=function(t){for(var e,n=t._states.filter((function(e){switch(e){case t._entryState:case t._exitState:case t._anyState:return!0;default:return!1}})),r=u(n);!(e=r()).done;){var i=e.value;t.remove(i)}for(var o,a=new Map,s=u(this._states);!(o=s()).done;){var l=o.value;switch(l){case this._entryState:a.set(l,t._entryState);break;case this._exitState:a.set(l,t._exitState);break;case this._anyState:a.set(l,t._anyState);break;default:if(l instanceof kn||l instanceof bo||l instanceof yo||l instanceof mo){if(l instanceof yo&&!t._allowEmptyStates)continue;var c=Vt(l);t._addState(c),a.set(l,c)}}}for(var h,f=u(this._transitions);!(h=f()).done;){var p=h.value;if(t._allowEmptyStates||!(p.from instanceof yo||p.to instanceof yo)){var d=a.get(p.from),v=a.get(p.to),_=t.connect(d,v);_.conditions=p.conditions.map((function(t){return t.clone()})),p.copyTo(_)}}},r.clone=function(){var t=new i(this._allowEmptyStates);return this.copyTo(t),t},r._addState=function(t){return this._states.push(t),t},n(i,[{key:"allowEmptyStates",get:function(){return this._allowEmptyStates}},{key:"entryState",get:function(){return this._entryState}},{key:"exitState",get:function(){return this._exitState}},{key:"anyState",get:function(){return this._anyState}}]),i}(N),Vi=O(Fi.prototype,"_states",[I],(function(){return[]})),Gi=O(Fi.prototype,"_transitions",[I],(function(){return[]})),Wi=O(Fi.prototype,"_entryState",[I],null),zi=O(Fi.prototype,"_exitState",[I],null),ji=O(Fi.prototype,"_anyState",[I],null),Ui=Fi))||Ui,bo=P("cc.animation.SubStateMachine")((Qi=function(t){function r(e){var n;return(n=t.call(this)||this)._stateMachine=Yi&&Yi(),n._stateMachine=new Co(e),n}return e(r,t),r.prototype.copyTo=function(e){t.prototype.copyTo.call(this,e),this._stateMachine.copyTo(e._stateMachine)},n(r,[{key:"stateMachine",get:function(){return this._stateMachine}}]),r}(xn),Yi=O(Qi.prototype,"_stateMachine",[I],null),Hi=Qi))||Hi,So=P(K+"PoseGraphStash")((Zi=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).graph=Xi&&Xi(),e}return e(n,t),n}(N),Xi=O(Zi.prototype,"graph",[I],(function(){return new co})),qi=Zi))||qi,wo=P("cc.animation.Layer")((Ki=function(){var t=e.prototype;function e(){this[Ie]=void 0,this._stateMachine=$i&&$i(),this.name=to&&to(),this.weight=eo&&eo(),this.mask=no&&no(),this.additive=ro&&ro(),this._stashes=io&&io(),this._stateMachine=new Co(!0)}return t.__callOnAfterDeserializeRecursive=function(){for(var t in this.stateMachine._allowEmptyStates=!0,this.stateMachine.__callOnAfterDeserializeRecursive(),this._stashes)this._stashes[t].graph.__callOnAfterDeserializeRecursive()},t.stashes=function(){return Object.entries(this._stashes)},t.getStash=function(t){return this._stashes[t]},t.addStash=function(t){return this._stashes[t]=new So},t.removeStash=function(t){delete this._stashes[t]},t.renameStash=function(t,e){this._stashes=Dn(this._stashes,t,e)},n(e,[{key:"stateMachine",get:function(){return this._stateMachine}}]),e}(),$i=O(Ki.prototype,"_stateMachine",[I],null),to=O(Ki.prototype,"name",[I],(function(){return""})),eo=O(Ki.prototype,"weight",[I],(function(){return 1})),no=O(Ki.prototype,"mask",[I],(function(){return null})),ro=O(Ki.prototype,"additive",[I],(function(){return!1})),io=O(Ki.prototype,"_stashes",[I],(function(){return{}})),Ji=Ki))||Ji;!function(t){t[t.override=0]="override",t[t.additive=1]="additive"}(_o||(_o={}));var Po,Oo=P("cc.animation.AnimationGraph")((ao=function(t){function r(){var e;return(e=t.call(this)||this)._layers=so&&so(),e._variables=uo&&uo(),e}e(r,t);var i=r.prototype;return i.onLoaded=function(){for(var t=this._layers,e=t.length,n=0;n<e;++n)t[n].__callOnAfterDeserializeRecursive()},i.addLayer=function(){var t=new wo;return this._layers.push(t),t},i.removeLayer=function(t){E(this._layers,t)},i.moveLayer=function(t,e){Gt(this._layers,t,e)},i.addVariable=function(t,e,n){var r=nn(e,n);return this._variables[t]=r,r},i.removeVariable=function(t){delete this._variables[t]},i.getVariable=function(t){return this._variables[t]},i.renameVariable=function(t,e){this._variables=Dn(this._variables,t,e)},n(r,[{key:"layers",get:function(){return this._layers}},{key:"variables",get:function(){return Object.entries(this._variables)}}]),r}(Mn),so=O(ao.prototype,"_layers",[I],(function(){return[]})),uo=O(ao.prototype,"_variables",[I],(function(){return{}})),oo=ao))||oo,No=Symbol("[[createEval]]"),Io=P(K+"MotionBase")(Po=function(t){function n(){return t.apply(this,arguments)||this}return e(n,t),n.prototype.__callOnAfterDeserializeRecursive=function(){},n}(N))||Po;function xo(t,e,n,r,i,o){if(0===e)return o.time=0,o.ratio=0,o.direction=1,o.stopped=!!Number.isFinite(r),o.iterations=0,o;var a=!1,s=t>0?t/e:-t/e;if(s>=r){s=r,a=!0;var u=r-(0|r);0===u&&(u=1),t=u*e*(t>0?1:-1)}if(t>e){var l=t%e;t=0===l?e:l}else t<0&&0!=(t%=e)&&(t+=e);var c=!1,h=n&G.ShouldWrap;h&&(c=Ro(n,s));var f=c?-1:1;return h&&c&&(t=e-t),o.time=t,o.ratio=o.time/e,o.direction=f,o.stopped=a,o.iterations=s,o}function Ro(t,e){var n=!1;return(t&G.PingPong)===G.PingPong&&(e-(0|e)==0&&e>0&&(e-=1),1&e&&(n=!n)),(t&G.Reverse)===G.Reverse&&(n=!n),n}var ko=new k,Lo=new L,Mo=function(){function t(t){this._transformHandle=t}return t.prototype.destroy=function(){this._transformHandle.destroy()},t}(),Do=function(t){function n(){return t.apply(this,arguments)||this}e(n,t);var r=n.prototype;return r.setValue=function(t,e){e.transforms.setPosition(this._transformHandle.index,t)},r.getValue=function(t){return t.transforms.getPosition(this._transformHandle.index,ko)},n}(Mo),Bo=function(t){function n(){return t.apply(this,arguments)||this}e(n,t);var r=n.prototype;return r.setValue=function(t,e){e.transforms.setRotation(this._transformHandle.index,t)},r.getValue=function(t){return t.transforms.getRotation(this._transformHandle.index,Lo)},n}(Mo),Uo=function(t){function n(){return t.apply(this,arguments)||this}e(n,t);var r=n.prototype;return r.setValue=function(t,e){var r=L.fromEuler(n._EULER_TO_QUAT_CACHE,t.x,t.y,t.z);e.transforms.setRotation(this._transformHandle.index,r)},r.getValue=function(t){var e=t.transforms.getRotation(this._transformHandle.index,Lo);return L.toEuler(ko,e)},n}(Mo);Uo._EULER_TO_QUAT_CACHE=new L;var Fo=function(t){function n(){return t.apply(this,arguments)||this}e(n,t);var r=n.prototype;return r.setValue=function(t,e){e.transforms.setScale(this._transformHandle.index,t)},r.getValue=function(t){return t.transforms.getScale(this._transformHandle.index,ko)},n}(Mo),Vo=function(){function t(t){this._handle=t}var e=t.prototype;return e.destroy=function(){this._handle.destroy()},e.setValue=function(t,e){e.auxiliaryCurves[this._handle.index]=t},e.getValue=function(t){return t.auxiliaryCurves[this._handle.index]},t}();function Go(t,e){switch(e){case"position":return new Do(t);case"rotation":return new Bo(t);case"eulerAngles":return new Uo(t);case"scale":return new Fo(t)}}var Wo=function(){function t(t){this.binding=t}var e=t.prototype;return e.destroy=function(){},e.setValue=function(t){this.binding.setValue(t)},e.getValue=function(){var t,e,n;return null!==(t=null==(e=(n=this.binding).getValue)?void 0:e.call(n))&&void 0!==t?t:void 0},t}(),zo=function(){function t(t,e){this._binding=void 0,this._trackSampler=void 0,this._binding=t,this._trackSampler=e}var e=t.prototype;return e.destroy=function(){this._binding.destroy()},e.evaluate=function(t,e){var n=this._trackSampler,r=this._binding,i=n.requiresDefault?r.getValue(e):void 0,o=n.evaluate(t,i);r.setValue(o,e)},t}();function jo(t,e,n){var r=Ho(e[st],n);return null!=r?r:void 0}function Ho(t,e){var n=e.origin,r=t.path,i=t.proxy,o=r.length,a=o-1;if(0!==o&&(r.isPropertyAt(a)||r.isElementAt(a))&&!i){var s=r.isPropertyAt(a)?r.parsePropertyAt(a):r.parseElementAt(a),u=r[ut](n,0,o-1);if(null===u)return null;if(u instanceof Mt&&lt(s)){var l=function(){for(var t=[],e=u;e&&e!==n;e=e.parent)t.unshift(e.name);return e===n?t.join("/"):void 0}();if("string"==typeof l){var c=e.bindTransform(l);if(!c)return;return Go(c,s)}}}var h=t.createRuntimeBinding(e.origin,void 0,!1);return h?new Wo(h):null}var Qo=function(){function t(t,e){this._binding=t,this._curve=e}return t.prototype.evaluate=function(t,e){var n=this._curve,r=this._binding,i=e,o=n.evaluate(t);r.setValue(o,i)},t}();function Yo(t,e){return t.isAdditive_experimental?new Aa(t,e):new Ta(t,e)}var qo,Zo,Xo,Jo,Ko,$o,ta,ea,na,ra,ia,oa,aa,sa,ua,la,ca,ha,fa,pa,da,va,_a,ya,ga,ma,Ea,Ta=function(){function t(t,e){this._trackEvaluations=[],this._exoticAnimationEvaluation=void 0,this._auxiliaryCurveEvaluations=[],t._trySyncLegacyData();for(var n,r,i=[],o=[],a=t.tracks,s=t[it],l=u(a);!(r=l()).done;){var c=r.value;if(!(c instanceof ot||Array.from(c.channels()).every((function(t){return 0===t.curve.keyFramesCount})))){var h=jo(0,c,e);if(h){var f=c[tt](),p=new zo(h,f);i.push(p)}}}s&&(n=s.createEvaluatorForAnimationGraph(e));for(var d=t.getAuxiliaryCurveNames_experimental(),v=d.length,_=0;_<v;++_){var y=d[_],g=t.getAuxiliaryCurve_experimental(y),m=e.bindAuxiliaryCurve(y),E=new Vo(m);o.push(new Qo(E,g))}this._trackEvaluations=i,this._exoticAnimationEvaluation=n,this._auxiliaryCurveEvaluations=o}var e=t.prototype;return e.destroy=function(){var t;null==(t=this._exoticAnimationEvaluation)||t.destroy();for(var e=this._trackEvaluations,n=e.length,r=0;r<n;++r)e[r].destroy()},e.evaluate=function(t,e){for(var n=this._trackEvaluations,r=this._exoticAnimationEvaluation,i=this._auxiliaryCurveEvaluations,o=e.pushDefaultedPose(),a=n.length,s=0;s<a;++s)n[s].evaluate(t,o);r&&r.evaluate(t,o);for(var u=i.length,l=0;l<u;++l)i[l].evaluate(t,o);return o},t}(),Aa=function(){function t(t,e){this._clipEval=void 0,this._refClipEval=void 0,this._clipEval=new Ta(t,e);var n=t[at].refClip;n&&n!==t&&(this._refClipEval=new Ta(n,e))}var e=t.prototype;return e.destroy=function(){var t;this._clipEval.destroy(),null==(t=this._refClipEval)||t.destroy()},e.evaluate=function(t,e){var n,r,i=this._clipEval.evaluate(t,e);return r=this._refClipEval?this._refClipEval.evaluate(0,e):this._clipEval.evaluate(0,e),Ur((n=i).transforms,r.transforms),Fr(n.auxiliaryCurves,r.auxiliaryCurves),e.popPose(),i},t}(),Ca=W,ba=(qo=P("cc.animation.ClipMotion"),Zo=Ca(ct),qo((Jo=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).clip=Ko&&Ko(),e}e(n,t);var r=n.prototype;return r[No]=function(t,e){return this.clip?new wa(t,this.clip,e):null},r.clone=function(){var t=new n;return t.clip=this.clip,t[M]=pn(this),t},n}(Io),Ko=O(Jo.prototype,"clip",[Zo,I],(function(){return null})),Xo=Jo))||Xo),Sa=Symbol("EvaluatePort"),wa=function(){function t(t,e,n){var r,i;this._clipEmbeddedPlayerEval=null,this._frameEventEval=null,this._wrapInfo=new ht,this._duration=0,this._ignoreEmbeddedPlayers=void 0,this._originalClip=e,this._ignoreEmbeddedPlayers=n;var o=null!==(r=null==(i=t.clipOverrides)?void 0:i.get(e))&&void 0!==r?r:e;this._setClip(o,t)}var e=t.prototype;return e.createPort=function(){return new Pa(this)},e.getClipStatuses=function(t){var e=this,n=!1;return{next:function(){return n?{done:!0,value:void 0}:(n=!0,{done:!1,value:{__DEBUG_ID__:e.__DEBUG__ID__,clip:e._clip,weight:t}})}}},e[Sa]=function(t,e){var n,r,i=this._duration,o=this._clip.duration,a=this._clipEval,s=i*t,u=this._clip.wrapMode,l=xo(s,i,u,(u&G.Loop)===G.Loop?1/0:1,0,this._wrapInfo),c=l.ratio*o,h=a.evaluate(c,e);return null==(n=this._frameEventEval)||n.sample(l.ratio,l.direction,l.iterations),null==(r=this._clipEmbeddedPlayerEval)||r.evaluate(c,Math.trunc(l.iterations)),h},e.overrideClips=function(t){var e,n=this._originalClip,r=null==(e=t.clipOverrides)?void 0:e.get(n);r&&this._setClip(r,t)},e.reenter=function(){var t;null==(t=this._frameEventEval)||t.reset()},e._setClip=function(t,e){var n;null==(n=this._clipEval)||n.destroy(),this._frameEventEval=null,this._clipEmbeddedPlayerEval&&(this._clipEmbeddedPlayerEval.destroy(),this._clipEmbeddedPlayerEval=null),this._clip=t,this._duration=0===t.speed?0:t.duration/t.speed,this._clipEval=Yo(t,e),this._frameEventEval=t.createEventEvaluator(e.origin),!this._ignoreEmbeddedPlayers&&t.containsAnyEmbeddedPlayer()&&(this._clipEmbeddedPlayerEval=t.createEmbeddedPlayerEvaluator(e.origin))},n(t,[{key:"duration",get:function(){return this._duration}}]),t}(),Pa=function(){function t(t){this._eval=void 0,this._eval=t}var e=t.prototype;return e.evaluate=function(t,e){return this._eval[Sa](t,e)},e.reenter=function(){this._eval.reenter()},t}(),Oa=P,Na=I,Ia=Oa(K+"AnimationBlendItem")((ta=function(){function t(){this.motion=ea&&ea()}var e=t.prototype;return e.clone=function(){var e=new t;return this._copyTo(e),e},e._copyTo=function(t){var e,n;return t.motion=null!==(e=null==(n=this.motion)?void 0:n.clone())&&void 0!==e?e:null,t},t}(),ea=O(ta.prototype,"motion",[Na],(function(){return null})),$o=ta))||$o,xa=Oa(K+"AnimationBlend")((ra=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).name=ia&&ia(),e}return e(n,t),n.prototype.copyTo=function(t){t.name=this.name,t[M]=pn(this)},n}(Io),ia=O(ra.prototype,"name",[Na],(function(){return""})),na=ra))||na,Ra=function(){function t(t,e,n,r,i){this._childEvaluators=r.map((function(n){var r,i;return null!==(r=null==(i=n.motion)?void 0:i[No](t,e))&&void 0!==r?r:null})),this._weights=new Array(this._childEvaluators.length).fill(0),this._inputs=[].concat(i)}var e=t.prototype;return e.createPort=function(){return new ka(this,this._childEvaluators.map((function(t){var e;return null!==(e=null==t?void 0:t.createPort())&&void 0!==e?e:null})))},e.getChildWeight=function(t){return this._weights[t]},e.getChildMotionEval=function(t){return this._childEvaluators[t]},e.getClipStatuses=function(t){var e,n=this._childEvaluators,r=this._weights,i=n.length,o=0;return{next:function(){for(;;){if(e){var a=e.next();if(!a.done)return a}if(o>=i)return{done:!0,value:void 0};var s=n[o];e=null==s?void 0:s.getClipStatuses(t*r[o]),++o}}}},e.__evaluatePort=function(t,e,n){for(var r=this._childEvaluators.length,i=0,o=null,a=0;a<r;++a){var s,u=this._weights[a];if(u){var l=null==(s=t.childPorts[a])?void 0:s.evaluate(e,n);l&&(i+=u,o?(i&&Ir(o,l,u/i),n.popPose()):o=l)}}return o||n.pushDefaultedPose()},e.overrideClips=function(t){for(var e=0;e<this._childEvaluators.length;++e){var n;null==(n=this._childEvaluators[e])||n.overrideClips(t)}},e.setInput=function(t,e){this._inputs[e]=t,this.doEval()},e.doEval=function(){this.eval(this._weights,this._inputs)},n(t,[{key:"childCount",get:function(){return this._weights.length}},{key:"duration",get:function(){for(var t=0,e=0;e<this._childEvaluators.length;++e){var n,r;t+=(null!==(n=null==(r=this._childEvaluators[e])?void 0:r.duration)&&void 0!==n?n:0)*this._weights[e]}return t}}]),t}(),ka=function(){function t(t,e){this.childPorts=[],this._host=void 0,this._host=t,this.childPorts=e}var e=t.prototype;return e.evaluate=function(t,e){return this._host.__evaluatePort(this,t,e)},e.reenter=function(){for(var t=this.childPorts,e=t.length,n=0;n<e;++n){var r;null==(r=t[n])||r.reenter()}},t}(),La=P,Ma=I,Da=La(K+"BindableNumber")((aa=function(){function t(t){void 0===t&&(t=0),this.variable=sa&&sa(),this.value=ua&&ua(),this.value=t}return t.prototype.clone=function(){var e=new t;return e.value=this.value,e.variable=this.variable,e},t}(),sa=O(aa.prototype,"variable",[Ma],(function(){return""})),ua=O(aa.prototype,"value",[Ma],(function(){return 0})),oa=aa))||oa,Ba=La(K+"BindableBoolean")((ca=function(){function t(t){void 0===t&&(t=!1),this.variable=ha&&ha(),this.value=fa&&fa(),this.value=t}return t.prototype.clone=function(){var e=new t;return e.value=this.value,e.variable=this.variable,e},t}(),ha=O(ca.prototype,"variable",[Ma],(function(){return""})),fa=O(ca.prototype,"value",[Ma],(function(){return!1})),la=ca))||la;function Ua(t,e,n,r,i){var o=e.variable,a=e.value;if(!o)return a;var s=t.getVar(o);if(!Fa(s,o))return a;if(s.type!==n)throw new fn(o,"number");for(var u=arguments.length,l=new Array(u>5?u-5:0),c=5;c<u;c++)l[c-5]=arguments[c];return s.bind.apply(s,[r,i].concat(l))}function Fa(t,e){if(t)return!0;throw new hn(e)}function Va(t,e,n){if(t!==e)throw new fn(n,"number")}function Ga(t,e){if(t!==we.TRIGGER)throw new fn(e,"trigger")}function Wa(t,e,n){if(t.fill(0),0===e.length);else if(n<=e[0])t[0]=1;else if(n>=e[e.length-1])t[t.length-1]=1;else{for(var r=0,i=1;i<e.length;++i)if(e[i]>n){r=i;break}var o=e[r-1],a=e[r],s=a-o;t[r-1]=(a-n)/s,t[r]=(n-o)/s}}var za,ja,Ha=P,Qa=I,Ya=Ha(K+"AnimationBlend1DItem")((da=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).threshold=va&&va(),e}e(n,t);var r=n.prototype;return r.clone=function(){var t=new n;return this._copyTo(t),t},r._copyTo=function(e){return t.prototype._copyTo.call(this,e),e.threshold=this.threshold,e},n}(Ia),va=O(da.prototype,"threshold",[Qa],(function(){return 0})),pa=da))||pa,qa=Ha("cc.animation.AnimationBlend1D")((Ea=function(t){function r(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this)._items=ga&&ga(),e.param=ma&&ma(),e}e(r,t);var i=r.prototype;return i.clone=function(){var t=new r;return this.copyTo(t),t._items=this._items.map((function(t){return t.clone()})),t.param=this.param.clone(),t},i[No]=function(t,e){var n=new Za(t,e,this,this._items,this._items.map((function(t){return t.threshold})),0),r=Ua(t,this.param,we.FLOAT,n.setInput,n,0);return n.setInput(r,0),n},n(r,[{key:"items",get:function(){return this._items},set:function(t){this._items=Array.from(t).sort((function(t,e){return t.threshold-e.threshold}))}}]),r}(xa),Ea.Item=Ya,ga=O((ya=Ea).prototype,"_items",[Qa],(function(){return[]})),ma=O(ya.prototype,"param",[Qa],(function(){return new Da})),_a=ya))||_a,Za=function(t){function n(e,n,r,i,o,a){var s;return(s=t.call(this,e,n,r,i,[a])||this)._thresholds=o,s.doEval(),s}return e(n,t),n.prototype.eval=function(t,e){var n=e[0];Wa(t,this._thresholds,n)},n}(Ra),Xa=(za=new z,ja={wA:0,wB:0},function(t,e,n){if(c(t.length===e.length),0!==e.length)if(1!==e.length)if(z.strictEquals(n,z.ZERO)){var r=e.findIndex((function(t){return z.strictEquals(t,z.ZERO)}));r>=0?t[r]=1:t.fill(1/e.length)}else{for(var i=-1,o=-1,a=-1,s=Number.NEGATIVE_INFINITY,u=Number.NEGATIVE_INFINITY,l=n.x,h=n.y,f=0;f<e.length;++f){var p=e[f];if(z.equals(p,z.ZERO))a=f;else{var d=z.normalize(za,p),v=z.dot(d,n);d.x*h-d.y*l>0?v>=u&&(u=v,i=f):v>=s&&(s=v,o=f)}}var _=0;if(i<0||o<0)_=1;else{var y=(S=e[i],w=e[o],P=n,O=ja,(N=z.cross(S,w))?(O.wA=z.cross(P,w)/N,O.wB=z.cross(P,S)/-N):(O.wA=0,O.wB=0),O),g=y.wA,m=y.wB,E=0,T=0,A=g+m;A>1?(E=g/A,T=m/A):A<0?(E=0,T=0,_=1):(E=g,T=m,_=1-A),t[i]=E,t[o]=T}if(_>0)if(a>=0)t[a]=_;else for(var C=_/t.length,b=0;b<t.length;++b)t[b]+=C}else t[0]=1;var S,w,P,O,N});function Ja(t,e,n){Ka(t,e,n,ls)}function Ka(t,e,n,r){t.fill(0);for(var i=new z(0,0),o=new z(0,0),a=0,s=e.length,u=0;u<s;++u){for(var l=Number.MAX_VALUE,c=!1,h=0;h<s;++h)if(u!==h){r(e[u],e[h],n,i,o);var f=1-z.dot(i,o)/z.lengthSqr(o);if(f<0){c=!0;break}l=Math.min(l,f)}c||(t[u]=l,a+=l)}a>0&&t.forEach((function(e,n){return t[n]=e/a}))}var $a,ts,es,ns,rs,is,os,as,ss,us,ls=function(t,e,n,r,i){z.subtract(r,n,t),z.subtract(i,e,t)},cs=function(){function t(e){for(var n=t._ANGLE_MULTIPLIER,r=e.length,i=this._exampleMagnitudes=new Array(r).fill(0),o=this._exampleDirections=e.map((function(t,e){var n=z.copy(new z,t),r=z.len(n);return i[e]=r,j(r,0,1e-5)||z.multiplyScalar(n,n,1/r),n})),a=this._precomputedVIJs=new Float32Array(3*r*r),s=0;s<r;++s)for(var u=i[s],l=o[s],c=0;c<r;++c)if(s!==c){var h=i[c],f=o[c],p=(u+h)/2,d=3*(r*s+c);a[d+0]=(h-u)/p,a[d+1]=hs(l,f)*n,a[d+2]=p}this._cacheVIXAngles=new Float32Array(r)}return t.prototype.interpolate=function(e,n){var r=this._exampleDirections,i=this._exampleMagnitudes,o=this._precomputedVIJs,a=this._cacheVIXAngles,s=t._CACHE_INPUT_DIRECTION,u=t._CACHE_VIJ,l=t._CACHE_VIX,h=t._ANGLE_MULTIPLIER,f=r.length;if(c(e.length===f),0!==f)if(1!==f){var p=n,d=z.len(p),v=a;if(z.equals(p,z.ZERO))for(var _=0;_<f;++_)v[_]=0;else for(var y=z.multiplyScalar(s,p,1/d),g=0;g<f;++g){var m=r[g];z.equals(m,z.ZERO)?v[g]=0:v[g]=hs(m,y)*h}for(var E=0,T=0;T<f;++T){for(var A=i[T],C=r[T],b=Number.POSITIVE_INFINITY,S=0;S<f;++S)if(T!==S){var w=r[S],P=3*(f*T+S),O=o[P+0],N=o[P+1],I=o[P+2],x=N,R=v[T];z.equals(C,z.ZERO)?x=v[S]:z.equals(w,z.ZERO)?x=v[T]:z.equals(p,z.ZERO)&&(R=x);var k=z.set(u,O,x),L=z.set(l,(d-A)/I,R),M=1-z.dot(L,k)/z.lengthSqr(k);if(M<=0){b=0;break}b=Math.min(b,M)}e[T]=b,E+=b}if(E>0)for(var D=0;D<f;++D)e[D]/=E;else for(var B=1/f,U=0;U<f;++U)e[U]=B}else e[0]=1},t}();function hs(t,e){var n=z.angle(t,e);return t.x*e.y-t.y*e.x<0?-n:n}cs._CACHE_INPUT_DIRECTION=new z,cs._CACHE_VIJ=new z,cs._CACHE_VIX=new z,cs._ANGLE_MULTIPLIER=1;var fs,ps=P,ds=I;!function(t){t[t.SIMPLE_DIRECTIONAL=0]="SIMPLE_DIRECTIONAL",t[t.FREEFORM_CARTESIAN=1]="FREEFORM_CARTESIAN",t[t.FREEFORM_DIRECTIONAL=2]="FREEFORM_DIRECTIONAL"}(fs||(fs={})),y(fs);var vs,_s,ys,gs,ms,Es,Ts=ps(K+"AnimationBlend2DItem")((ts=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).threshold=es&&es(),e}e(n,t);var r=n.prototype;return r.clone=function(){var t=new n;return this._copyTo(t),t},r._copyTo=function(e){return t.prototype._copyTo.call(this,e),z.copy(e.threshold,this.threshold),e},n}(Ia),es=O(ts.prototype,"threshold",[ds],(function(){return new z})),$a=ts))||$a,As=ps("cc.animation.AnimationBlend2D")((us=function(t){function r(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this)._items=is&&is(),e.paramX=os&&os(),e.paramY=as&&as(),e._algorithm=ss&&ss(),e._polarSpaceGBI=void 0,e}e(r,t);var i=r.prototype;return i.__callOnAfterDeserializeRecursive=function(){this._tryReconstructPolarSpaceInterpolator()},i.clone=function(){var t=new r;return this.copyTo(t),t._items=this._items.map((function(t){var e;return null!==(e=null==t?void 0:t.clone())&&void 0!==e?e:null})),t.paramX=this.paramX.clone(),t.paramY=this.paramY.clone(),t.algorithm=this._algorithm,t},i[No]=function(t,e){var n,r=this.algorithm;switch(r){case fs.FREEFORM_DIRECTIONAL:c(this._polarSpaceGBI),n=new bs(t,e,this,this._items,this._polarSpaceGBI,[0,0]);break;default:case fs.SIMPLE_DIRECTIONAL:case fs.FREEFORM_CARTESIAN:n=new Cs(t,e,this,this._items,this._items.map((function(t){return t.threshold})),r,[0,0])}var i=Ua(t,this.paramX,we.FLOAT,n.setInput,n,0),o=Ua(t,this.paramY,we.FLOAT,n.setInput,n,1);return n.setInput(i,0),n.setInput(o,1),n},i._tryReconstructPolarSpaceInterpolator=function(){this._algorithm===fs.FREEFORM_DIRECTIONAL?this._polarSpaceGBI=new cs(this._items.map((function(t){return t.threshold}))):this._polarSpaceGBI=void 0},n(r,[{key:"algorithm",get:function(){return this._algorithm},set:function(t){t!==this._algorithm&&(this._algorithm=t,this._tryReconstructPolarSpaceInterpolator())}},{key:"items",get:function(){return this._items},set:function(t){this._items=Array.from(t),this._tryReconstructPolarSpaceInterpolator()}}]),r}(xa),us.Algorithm=fs,us.Item=Ts,is=O((rs=us).prototype,"_items",[ds],(function(){return[]})),os=O(rs.prototype,"paramX",[ds],(function(){return new Da})),as=O(rs.prototype,"paramY",[ds],(function(){return new Da})),ss=O(rs.prototype,"_algorithm",[ds],(function(){return fs.SIMPLE_DIRECTIONAL})),ns=rs))||ns,Cs=function(t){function n(e,n,r,i,o,a,s){var u;return(u=t.call(this,e,n,r,i,s)||this)._thresholds=void 0,u._algorithm=void 0,u._value=new z,u._thresholds=o,u._algorithm=a,u.doEval(),u}return e(n,t),n.prototype.eval=function(t,e){var n=e[0],r=e[1];switch(z.set(this._value,n,r),t.fill(0),this._algorithm){case fs.SIMPLE_DIRECTIONAL:Xa(t,this._thresholds,this._value);break;case fs.FREEFORM_CARTESIAN:Ja(t,this._thresholds,this._value)}},n}(Ra),bs=function(t){function n(e,n,r,i,o,a){var s;return(s=t.call(this,e,n,r,i,a)||this)._interpolator=void 0,s._value=new z,s._interpolator=o,s.doEval(),s}return e(n,t),n.prototype.eval=function(t,e){var n=e[0],r=e[1];z.set(this._value,n,r),t.fill(0),this._interpolator.interpolate(t,this._value)},n}(Ra),Ss=P,ws=I,Ps=Ss(K+"AnimationBlendDirectItem")((_s=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).weight=ys&&ys(),e}e(n,t);var r=n.prototype;return r.clone=function(){var t=new n;return this._copyTo(t),t},r._copyTo=function(e){return t.prototype._copyTo.call(this,e),e.weight=this.weight,e},n}(Ia),ys=O(_s.prototype,"weight",[ws],(function(){return new Da(0)})),vs=_s))||vs;Ss("cc.animation.AnimationBlendDirect")((Es=function(t){function r(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this)._items=ms&&ms(),e}e(r,t);var i=r.prototype;return i.clone=function(){var t=new r;return this.copyTo(t),t._items=this._items.map((function(t){var e;return null!==(e=null==t?void 0:t.clone())&&void 0!==e?e:null})),t},i[No]=function(t,e){for(var n=new ou(t,e,this,this._items,new Array(this._items.length).fill(0)),r=0;r<this._items.length;++r){var i=Ua(t,this._items[r].weight,we.FLOAT,n.setInput,n,r);n.setInput(i,r)}return n},n(r,[{key:"items",get:function(){return this._items},set:function(t){this._items=Array.from(t)}}]),r}(xa),Es.Item=Ps,ms=O((gs=Es).prototype,"_items",[ws],(function(){return[]})),gs));var Os,Ns,Is,xs,Rs,ks,Ls,Ms,Ds,Bs,Us,Fs,Vs,Gs,Ws,zs,js,Hs,Qs,Ys,qs,Zs,Xs,Js,Ks,$s,tu,eu,nu,ru,iu,ou=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).doEval(),e}return e(n,t),n.prototype.eval=function(t,e){for(var n=t.length,r=0;r<n;++r)t[r]=e[r]},n}(Ra),au=P("cc.JointMask")((Ns=function(){this.path=Is&&Is(),this.enabled=xs&&xs()},Is=O(Ns.prototype,"path",[I],(function(){return""})),xs=O(Ns.prototype,"enabled",[I],(function(){return!0})),Os=Ns))||Os,su=(Rs=P(K+"AnimationMask"),ks=W(au),Rs((Ms=function(t){function r(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this)._jointMasks=Ds&&Ds(),e}e(r,t);var i=r.prototype;return i.addJoint=function(t,e){this.removeJoint(t);var n=new au;n.path=t,n.enabled=e,this._jointMasks.push(n)},i.removeJoint=function(t){A(this._jointMasks,(function(e){return e.path===t}))},i.clear=function(){this._jointMasks.length=0},i.filterDisabledNodes=function(t){for(var e=this._jointMasks,n=e.length,r=new Set,i=0;i<n;++i){var o=e[i],a=o.path;if(!o.enabled){var s=t.getChildByPath(a);s&&r.add(s)}}return r},i.isExcluded=function(t){var e,n;return!(null===(e=null==(n=this._jointMasks.find((function(e){return e.path===t})))?void 0:n.enabled)||void 0===e||e)},n(r,[{key:"joints",get:function(){return this._jointMasks},set:function(t){this.clear();for(var e,n=u(t);!(e=n()).done;){var r=e.value;this.addJoint(r.path,r.enabled)}}}]),r}(Dt),Ds=O(Ms.prototype,"_jointMasks",[I],(function(){return[]})),s(Ms.prototype,"joints",[ks],Object.getOwnPropertyDescriptor(Ms.prototype,"joints"),Ms.prototype),Ls=Ms))||Ls),uu=P(K+"ClipOverrideEntry")((Fs=function(){this.original=Vs&&Vs(),this.substitution=Gs&&Gs()},Vs=O(Fs.prototype,"original",[I],(function(){return null})),Gs=O(Fs.prototype,"substitution",[I],(function(){return null})),Us=Fs))||Us,lu=(Ws=P(K+"AnimationGraphVariant"),zs=W(Oo),Ws((Hs=function(t){function r(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this)._graph=Qs&&Qs(),e._clipOverrides=Ys&&Ys(),e}return e(r,t),n(r,[{key:"original",get:function(){return this._graph},set:function(t){this._graph=t}},{key:"clipOverrides",get:function(){return this._clipOverrides}}]),r}(Mn),s(Hs.prototype,"original",[zs],Object.getOwnPropertyDescriptor(Hs.prototype,"original"),Hs.prototype),Qs=O(Hs.prototype,"_graph",[I],(function(){return null})),Ys=O(Hs.prototype,"_clipOverrides",[I],(function(){return new cu})),js=Hs))||js),cu=P(K+"ClipOverrideMap")((Bs=Symbol.iterator,Zs=function(){function t(){this._entries=Xs&&Xs()}var e=t.prototype;return e[Bs]=function(){return this._entries[Symbol.iterator]()},e.has=function(t){return!!this._entries.find((function(e){return e.original===t}))},e.get=function(t){var e=this._entries.find((function(e){return e.original===t}));return null==e?void 0:e.substitution},e.set=function(t,e){var n=this._entries.find((function(e){return e.original===t}));if(n)n.substitution=e;else{var r=new uu;r.original=t,r.substitution=e,this._entries.push(r)}},e.delete=function(t){A(this._entries,(function(e){return e.original===t}))},e.clear=function(){this._entries.length=0},n(t,[{key:"size",get:function(){return this._entries.length}}]),t}(),Xs=O(Zs.prototype,"_entries",[I],(function(){return[]})),qs=Zs))||qs,hu="i18n:ENGINE.animation_graph.pose_graph_node_sub_categories.pose_nodes/",fu=hu+"/i18n:ENGINE.animation_graph.pose_graph_node_sub_categories.pose_nodes_blend/",pu={listEntries:function(t){return[].concat(t.animationGraph.layers[t.layerIndex].stashes()).map((function(t){var e=t[0];return{arg:e,menu:e}}))},create:function(t){var e=new du;return e.stashName=t,e}},du=P(K+"PoseNodeUseStashedPose")(Js=nr(hu)(Js=rr(pu)(Js=or({inline:!0})((Ks=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).stashName=$s&&$s(),e._runtimeStash=void 0,e}e(n,t);var r=n.prototype;return r.bind=function(t){var e=this.stashName;if(e){var n=t.stashView.bindStash(e);this._runtimeStash=n}},r.settle=function(){},r.reenter=function(){var t;null==(t=this._runtimeStash)||t.reenter()},r.doUpdate=function(t){var e;null==(e=this._runtimeStash)||e.requestUpdate(t)},r.doEvaluate=function(t){var e,n;return null!==(e=null==(n=this._runtimeStash)?void 0:n.evaluate(t))&&void 0!==e?e:t.pushDefaultedPose()},n}(Kr),$s=O(Ks.prototype,"stashName",[I],(function(){return""})),Js=Ks))||Js)||Js)||Js)||Js,vu=P,_u=I;!function(t){t[t.TRUTHY=0]="TRUTHY",t[t.FALSY=1]="FALSY"}(iu||(iu={})),vu(K+"UnaryCondition")(((ru=function(){function t(){this.operator=eu&&eu(),this.operand=nu&&nu()}var e=t.prototype;return e.clone=function(){var e=new t;return e.operator=this.operator,e.operand=this.operand.clone(),e},e[No]=function(t){var e=this.operator,n=this.operand,r=new gu(e,!1),i=Ua(t,n,we.BOOLEAN,r.setOperand,r);return r.reset(i),r},t}()).Operator=iu,eu=O((tu=ru).prototype,"operator",[_u],(function(){return iu.TRUTHY})),nu=O(tu.prototype,"operand",[_u],(function(){return new Ba})),tu));var yu,gu=function(){function t(t,e){this._operator=t,this._operand=e,this._eval()}var e=t.prototype;return e.reset=function(t){this.setOperand(t)},e.setOperand=function(t){this._operand=t,this._eval()},e.eval=function(){return this._result},e._eval=function(){var t=this._operand;switch(this._operator){default:case iu.TRUTHY:this._result=!!t;break;case iu.FALSY:this._result=!t}},t}();!function(t){t[t.FLOAT=0]="FLOAT",t[t.INTEGER=3]="INTEGER"}(yu||(yu={}));var mu,Eu=function(){},Tu=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return function(){}};!function(t){t[t.MOTION=1]="MOTION",t[t.POSE=2]="POSE",t[t.EMPTY=4]="EMPTY",t[t.WEIGHTED=7]="WEIGHTED"}(mu||(mu={}));var Au,Cu,bu,Su,wu,Pu,Ou,Nu=I,Iu=P(K+"TCVariableBinding")(Au=Tu(yu.FLOAT,yu.INTEGER)((Cu=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).type=bu&&bu(),e.variableName=Su&&Su(),e}e(n,t);var r=n.prototype;return r.getValueType=function(){return this.type},r.bind=function(t){var e=t.getVar(this.variableName);if(e)return new xu(e)},n}(Eu),bu=O(Cu.prototype,"type",[Nu,H],(function(){return yu.FLOAT})),Su=O(Cu.prototype,"variableName",[Nu],(function(){return""})),Au=Cu))||Au)||Au,xu=function(){function t(t){this._varInstance=t}return t.prototype.evaluate=function(){return this._varInstance.value},t}(),Ru=I;P(K+"TCAuxiliaryCurveBinding")(wu=Tu(yu.FLOAT)((Pu=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).curveName=Ou&&Ou(),e}e(n,t);var r=n.prototype;return r.getValueType=function(){return yu.FLOAT},r.bind=function(t){var e=t.getEvaluationTimeAuxiliaryCurveView();return new Du(e,this.curveName)},n}(Eu),Ou=O(Pu.prototype,"curveName",[Ru],(function(){return""})),wu=Pu))||wu);var ku,Lu,Mu,Du=function(){function t(t,e){this._view=t,this._curveName=e}return t.prototype.evaluate=function(){return this._view.get(this._curveName)},t}();ku=P(K+"TCStateWeightBinding"),Lu=Tu(yu.FLOAT),mu.WEIGHTED,ku(Mu=Lu(Mu=void(Mu=function(t){function n(){return t.apply(this,arguments)||this}e(n,t);var r=n.prototype;return r.getValueType=function(){return yu.FLOAT},r.bind=function(){return new Vu},n}(Eu))||Mu)||Mu);var Bu,Uu,Fu,Vu=function(){function t(){}return t.prototype.evaluate=function(t){return t.sourceStateWeight},t}();Bu=P(K+"TCStateMotionTimeBinding"),Uu=Tu(yu.FLOAT),mu.POSE,Bu(Fu=Uu(Fu=void(Fu=function(t){function n(){return t.apply(this,arguments)||this}e(n,t);var r=n.prototype;return r.getValueType=function(){return yu.FLOAT},r.bind=function(){return new qu},n}(Eu))||Fu)||Fu);var Gu,Wu,zu,ju,Hu,Qu,Yu,qu=function(){function t(){}return t.prototype.evaluate=function(t){return t.sourceStateMotionTimeNormalized},t}(),Zu=P,Xu=I;!function(t){t[t.EQUAL_TO=0]="EQUAL_TO",t[t.NOT_EQUAL_TO=1]="NOT_EQUAL_TO",t[t.LESS_THAN=2]="LESS_THAN",t[t.LESS_THAN_OR_EQUAL_TO=3]="LESS_THAN_OR_EQUAL_TO",t[t.GREATER_THAN=4]="GREATER_THAN",t[t.GREATER_THAN_OR_EQUAL_TO=5]="GREATER_THAN_OR_EQUAL_TO"}(Yu||(Yu={})),Zu(K+"BinaryCondition")(((Qu=function(){function t(){this.operator=Wu&&Wu(),this.lhs=zu&&zu(),this.lhsBinding=ju&&ju(),this.rhs=Hu&&Hu()}var e=t.prototype;return e.clone=function(){var e=new t;return e.operator=this.operator,e.lhs=this.lhs,e.lhsBinding=Vt(this.lhsBinding),e.rhs=this.rhs,e},e[No]=function(t){var e,n=null==(e=this.lhsBinding)?void 0:e.bind(t);return new el(this.operator,this.lhs,this.rhs,n)},t}()).Operator=Yu,Wu=O((Gu=Qu).prototype,"operator",[Xu],(function(){return Yu.EQUAL_TO})),zu=O(Gu.prototype,"lhs",[Xu],(function(){return 0})),ju=O(Gu.prototype,"lhsBinding",[Xu],(function(){return new Iu})),Hu=O(Gu.prototype,"rhs",[Xu],(function(){return 0})),Gu));var Ju,Ku,$u,tl,el=function(){function t(t,e,n,r){this._operator=t,this._lhsBindingEvaluation=r,this._lhsValue=e,this._rhsValue=n}return t.prototype.eval=function(t){var e,n,r=null!==(e=null==(n=this._lhsBindingEvaluation)?void 0:n.evaluate(t))&&void 0!==e?e:this._lhsValue,i=this._rhsValue;switch(this._operator){default:case Yu.EQUAL_TO:return r===i;case Yu.NOT_EQUAL_TO:return r!==i;case Yu.LESS_THAN:return r<i;case Yu.LESS_THAN_OR_EQUAL_TO:return r<=i;case Yu.GREATER_THAN:return r>i;case Yu.GREATER_THAN_OR_EQUAL_TO:return r>=i}},t}(),nl=I,rl=P(K+"TriggerCondition")((Ku=function(){function t(){this.trigger=$u&&$u()}var e=t.prototype;return e.clone=function(){var e=new t;return e.trigger=this.trigger,e},e[No]=function(t){var e=new il(!1),n=t.getVar(this.trigger);return Fa(n,this.trigger)&&(Ga(n.type,this.trigger),e.setTrigger(n.bind(e.setTrigger,e))),e},t}(),$u=O(Ku.prototype,"trigger",[nl],(function(){return""})),Ju=Ku))||Ju,il=function(){function t(t){this._triggered=!1,this._triggered=t}var e=t.prototype;return e.setTrigger=function(t){this._triggered=t},e.eval=function(){return this._triggered},t}(),ol=P(K+"StateMachineComponent")(tl=function(){function t(){}var e=t.prototype;return e.onMotionStateEnter=function(){},e.onMotionStateExit=function(){},e.onMotionStateUpdate=function(){},e.onStateMachineEnter=function(){},e.onStateMachineExit=function(){},t}())||tl,al=10,sl=Float64Array.BYTES_PER_ELEMENT*al,ul=k.fromArray,ll=k.toArray,cl=L.fromArray,hl=L.toArray,fl=function(){function t(t,e,n){this._data=void 0,this._data=void 0===t?new Float64Array:"number"==typeof t?new Float64Array(al*t):new Float64Array(t,e,"number"==typeof n?al*n:void 0)}var e=t.prototype;return e.getTransform=function(t,e){var n=this._data,r=e.position,i=e.rotation,o=e.scale,a=al*t;return ul(r,n,a),cl(i,n,a+3),ul(o,n,a+7),e},e.getPosition=function(t,e){var n=this._data;return ul(e,n,al*t),e},e.getRotation=function(t,e){var n=this._data;return cl(e,n,al*t+3),e},e.getScale=function(t,e){var n=this._data;return ul(e,n,al*t+7),e},e.setTransform=function(t,e){var n=this._data,r=e.position,i=e.rotation,o=e.scale,a=al*t;ll(n,r,a),hl(n,i,a+3),ll(n,o,a+7)},e.setPosition=function(t,e){var n=this._data;ll(n,e,al*t)},e.setRotation=function(t,e){var n=this._data;hl(n,e,al*t+3)},e.setScale=function(t,e){var n=this._data;ll(n,e,al*t+7)},e.copyWithin=function(t,e,n){this._data.copyWithin(t*al,e*al,"number"==typeof n?n*al:void 0)},e.fill=function(t,e,n){var r,i,o=this.length;if(null!==(r=e)&&void 0!==r||(e=0),null!==(i=n)&&void 0!==i||(n=o),!(e>=o)){this.setTransform(e,t);for(var a=e+1;a<n;++a)this.copyWithin(a,e,e+1)}},e.fillZero=function(t,e){this._data.fill(0,"number"==typeof t?t*al:void 0,"number"==typeof e?e*al:void 0)},e.set=function(t,e){this._data.set(t._data,"number"==typeof e?e*al:void 0)},e.slice=function(e,n){var r=this._data.slice("number"==typeof e?e*al:void 0,"number"==typeof n?n*al:void 0);return new t(r.buffer,r.byteOffset,r.length/al)},e.copyRange=function(t,e,n,r){for(var i=al*r,o=this._data,a=al*t,s=e._data,u=al*n,l=0;l<i;++l)o[a+l]=s[u+l]},n(t,[{key:"buffer",get:function(){return this._data.buffer}},{key:"byteLength",get:function(){return this._data.byteLength}},{key:"byteOffset",get:function(){return this._data.byteOffset}},{key:"length",get:function(){return this._data.length/al}}],[{key:"BYTES_PER_ELEMENT",get:function(){return sl}}]),t}(),pl=Symbol(""),dl=Symbol(""),vl=function(t){this.buffer=void 0,this.useCount=0,this.buffer=new ArrayBuffer(t)},_l=function(){function t(t,e){this._locking=!1,this._pages=[],this._allocatorCount=0,this._manager=t,this._pageSize=e}var e=t.prototype;return e.debugLock=function(){c(!this._locking),this._locking=!0},e.debugUnlock=function(){c(this._locking),this._locking=!1},e.getPageMemory=function(t){return c(t>=0&&t<this._pages.length),this._pages[t].buffer},e.pushPage=function(t){var e=t[pl];c(e<=this._pages.length),e===this._pages.length&&this._pushNewPage(),c(e<this._pages.length);var n=this._pages[e];return++n.useCount,++t[pl],n},e.popPage=function(t){var e=t[pl]-1,n=this._pages[e];c(n.useCount>0),--n.useCount,--t[pl],0===n.useCount&&(c(e===this._pages.length-1),this._pages.pop())},e.createAllocator=function(t){var e=new gl(this,t);return++this._allocatorCount,e},e.destroyAllocator=function(t){for(var e=t[pl],n=0;n<e;++n){var r=this._pages[n];c(r.useCount>0),--r.useCount}c(this._allocatorCount>0),--this._allocatorCount,0===this._allocatorCount&&this._manager[dl](this)},e._pushNewPage=function(){var t=new vl(this._pageSize);this._pages.push(t)},n(t,[{key:"pageSize",get:function(){return this._pageSize}},{key:"debugLocking",get:function(){return this._locking}},{key:"allocatorCount",get:function(){return this._allocatorCount}}]),t}(),yl=function(t,e){this.buffer=t,this.byteOffset=e},gl=function(){function t(t,e){this[pl]=0,this._slicesPerPage=0,this._slices=[],this._resource=t,this._sliceSize=e;var n=Math.floor(this._resource.pageSize/e);this._slicesPerPage=n}var e=t.prototype;return e.destroy=function(){c(0===this._slices.length),c(!this._resource.debugLocking),this._resource.destroyAllocator(this)},e.debugLock=function(){this._resource.debugLock()},e.debugUnlock=function(){this._resource.debugUnlock()},e.push=function(){var t=this._sliceSize,e=this._slices,n=this._slicesPerPage,r=e.length,i=0,o=0;0===t?(0===this[pl]&&this._resource.pushPage(this),c(1===this[pl])):(r===n*this[pl]&&(this._resource.pushPage(this),c(r<n*this[pl])),o=(r-(i=r%n))/n,c(this[pl]*n>=r));var a=this._resource.getPageMemory(o),s=new yl(a,t*i);return this._slices.push(s),s},e.pop=function(){var t=this._slices,e=this._slicesPerPage,n=t.length-1;0===this._sliceSize?(c(1===this[pl]),0===n&&this._resource.popPage(this)):0==n%e&&this._resource.popPage(this),this._slices.pop()},n(t,[{key:"isEmpty",get:function(){return 0===this._slices.length}}]),t}(),ml=function(){function t(t){this._stacks=new Map,this._thresholds=t,c(t.every((function(t,e,n){return 0===e||t>n[e-1]})))}var e=t.prototype;return e.createAllocator=function(t){var e=t,n=this._selectStackPageSize(e),r=this._stacks.get(n);return r||(r=new _l(this,n),this._stacks.set(n,r)),r.createAllocator(e)},e[dl]=function(t){for(var e,n=u(this._stacks);!(e=n()).done;){var r=e.value,i=r[0];if(r[1]===t){this._stacks.delete(i);break}}},e._selectStackPageSize=function(t){var e=Q(this._thresholds,t),n=t;return e>=0?n=this._thresholds[e]:(e=~e)===this._thresholds.length||(c(e>=0&&e<this._thresholds.length),n=this._thresholds[e]),n},n(t,[{key:"isEmpty",get:function(){return 0===this._stacks.size}}]),t}(),El=function(){function t(t,e){this._poses=[],this._allocatedCount=0,this._memoryAllocator=void 0,this._transformCount=t,this._auxiliaryCurveCount=e;var n=Tl(t,e,1);this._memoryAllocator=Cl.createAllocator(n)}var e=t.prototype;return e.destroy=function(){c(0===this._allocatedCount);for(var t=0;t<this._poses.length;++t)this._memoryAllocator.pop();return this._poses.length=0,this._memoryAllocator.destroy()},e.push=function(){0===this._allocatedCount&&this._memoryAllocator.debugLock(),this._allocatedCount===this._poses.length&&(this._allocateNewPose(),c(this._allocatedCount<this._poses.length));var t=this._poses[this._allocatedCount];return++this._allocatedCount,t},e.pop=function(){c(this._allocatedCount>0),--this._allocatedCount,0===this._allocatedCount&&this._memoryAllocator.debugUnlock()},e._allocateNewPose=function(){var t=this._memoryAllocator.push(),e=fl.BYTES_PER_ELEMENT*this._transformCount,n=t.byteOffset,r=new fl(t.buffer,n,this._transformCount),i=new Float64Array(t.buffer,n+e,this._auxiliaryCurveCount),o=Or._create(r,i);this._poses.push(o)},n(t,[{key:"allocatedCount",get:function(){return this._allocatedCount}},{key:"top",get:function(){return c(this._allocatedCount>0),this._poses[this._allocatedCount-1]}}]),t}();function Tl(t,e,n){return(fl.BYTES_PER_ELEMENT*t+Float64Array.BYTES_PER_ELEMENT*e)*n}var Al,Cl=new ml([Tl(128,10,4)]);function bl(t,e){for(var n=t.length,r=0;r<n&&e(t[r],r,t);++r);if(r===n)return n;for(var i=r,o=r+1;o<n;++o){var a=t[o];if(e(a,o,t)){var s=a;t[o]=t[i],t[i]=s,++i}}return i}!function(t){t[t.WORLD=0]="WORLD",t[t.COMPONENT=1]="COMPONENT",t[t.PARENT=2]="PARENT",t[t.LOCAL=3]="LOCAL"}(Al||(Al={})),y(Al);var Sl,wl,Pl,Ol,Nl,Il,xl,Rl,kl=Symbol("PoseHeapAllocator"),Ll=function(){function t(t,e){this._transformCount=0,this._metaValueCount=0,this._pages=[],this._allocatedCount=0,this._foremostPossibleFreePage=0,this._transformCount=t,this._metaValueCount=e}var e=t.prototype;return e.allocatePose=function(){++this._allocatedCount;for(var t=this._pages,e=t.length,n=this._foremostPossibleFreePage;n<e;++n){var r=t[n],i=r.tryAllocate();if(i)return i[kl].pageIndex=n,0===r.freeCount&&++this._foremostPossibleFreePage,i}var o=this._allocatePoseInNewPage();return this._foremostPossibleFreePage=o[kl].pageIndex,o},e.destroyPose=function(t){var e=this._pages;e.length;var n=t[kl].pageIndex,r=e[n];r.deallocate(t),--this._allocatedCount,n<this._foremostPossibleFreePage&&(c(r.freeCount>0),this._foremostPossibleFreePage=n)},e._allocatePoseInNewPage=function(){var t=new Ul(this._transformCount,this._metaValueCount,4),e=this._pages.length;this._pages.push(t);var n=t.tryAllocate();return n[kl].pageIndex=e,n},n(t,[{key:"allocatedCount",get:function(){return this._allocatedCount}}]),t}(),Ml=function(){function t(){this._id=-1}return n(t,[{key:"pageIndex",get:function(){return this._id>>Bl},set:function(t){this._id&=Dl,this._id|=t<<Bl}},{key:"poseIndex",get:function(){return this._id&Dl},set:function(t){this._id&=-8,this._id|=t}}]),t}(),Dl=7,Bl=3,Ul=function(){function t(t,e,n){this._buffer=void 0,this._idleFlags=15,this._poses=void 0,this._freeCount=0,this._transformCount=t,this._metaValueCount=e,this._capacity=n;var r=(fl.BYTES_PER_ELEMENT*t+Float64Array.BYTES_PER_ELEMENT*e)*n;this._buffer=new ArrayBuffer(r),this._poses=new Array(n).fill(null),this._freeCount=n}var e=t.prototype;return e.tryAllocate=function(){var t,e,n=this._poses,r=this._idleFlags,i=this._capacity,o=0===(e=r)?1/0:Math.log2(e&-e);if(o>=i)return null;c(o>=0&&o<n.length);var a=null!==(t=n[o])&&void 0!==t?t:n[o]=this._createPose(o);return a[kl].poseIndex=o,this._idleFlags&=~(1<<o),c(this._freeCount>0),--this._freeCount,a},e.deallocate=function(t){var e=this._poses,n=t[kl].poseIndex;c(n>=0&&n<e.length),c(e[n]===t),this._idleFlags|=1<<n,c(this._freeCount<this._capacity),++this._freeCount},e._createPose=function(t){var e=fl.BYTES_PER_ELEMENT*this._transformCount,n=(e+Float64Array.BYTES_PER_ELEMENT*this._metaValueCount)*t,r=new fl(this._buffer,n,this._transformCount),i=new Float64Array(this._buffer,n+e,this._metaValueCount),o=Or._create(r,i);return o[kl]=new Ml,o},n(t,[{key:"capacity",get:function(){return this._capacity}},{key:"freeCount",get:function(){return this._freeCount}}]),t}();function Fl(t,e){if(t.name===e)return t;for(var n=t.children.length,r=0;r<n;++r){var i=Fl(t.children[r],e);if(i)return i}return null}var Vl,Gl=function(){function t(t,e,n,r){var i=this;this._origin=void 0,this._layoutMaintainer=void 0,this._varRegistry=void 0,this._additiveFlagStack=[],this._triggerResetter=function(t){return i._resetTrigger(t)},this._isLayerWideContextPropertiesSet=!1,this._stashView=void 0,this._motionSyncManager=void 0,this._clipOverrides=void 0,this._controller=r,this._origin=t,this._layoutMaintainer=e,this._varRegistry=n,this._additiveFlagStack=[!1]}var e=t.prototype;return e.bindTransform=function(t){var e=this._origin.getChildByPath(t);return e?this._layoutMaintainer.getOrCreateTransformBinding(e):null},e.bindTransformByName=function(t){var e=Fl(this._origin,t);return e?this._layoutMaintainer.getOrCreateTransformBinding(e):null},e.getBoneChildren=function(t){var e=Fl(this._origin,t);return e?e.children.map((function(t){return t.name})):[]},e.getParentBoneNameByName=function(t){var e,n=Fl(this._origin,t);return n?n===this._origin?"":null==(e=n.parent)?void 0:e.name:null},e.bindAuxiliaryCurve=function(t){return this._layoutMaintainer.getOrCreateAuxiliaryCurveBinding(t)},e.getEvaluationTimeAuxiliaryCurveView=function(){return this._layoutMaintainer.auxiliaryCurveRegistry},e.getVar=function(t){return this._varRegistry[t]},e._pushAdditiveFlag=function(t){this._additiveFlagStack.push(t)},e._popAdditiveFlag=function(){c(this._additiveFlagStack.length>1),this._additiveFlagStack.pop()},e._integrityCheck=function(){return 1===this._additiveFlagStack.length},e._setLayerWideContextProperties=function(t,e){c(!this._isLayerWideContextPropertiesSet),this._isLayerWideContextPropertiesSet=!0,this._stashView=t,this._motionSyncManager=e},e._unsetLayerWideContextProperties=function(){c(this._isLayerWideContextPropertiesSet),this._isLayerWideContextPropertiesSet=!1,this._stashView=void 0,this._motionSyncManager=void 0},e._setClipOverrides=function(t){this._clipOverrides=t},e._resetTrigger=function(t){var e=this._varRegistry[t];e&&(e.value=!1)},n(t,[{key:"origin",get:function(){return this._origin}},{key:"controller",get:function(){return this._controller}},{key:"triggerResetter",get:function(){return this._triggerResetter}},{key:"clipOverrides",get:function(){return this._clipOverrides}},{key:"additive",get:function(){var t=this._additiveFlagStack;return t[t.length-1]}},{key:"stashView",get:function(){return c(this._stashView),this._stashView}},{key:"motionSyncManager",get:function(){return c(this._motionSyncManager),this._motionSyncManager}}]),t}(),Wl=new mr,zl=function(){function t(){this._namedCurves=new Map}var e=t.prototype;return e.names=function(){return this._namedCurves.keys()},e.has=function(t){return this._namedCurves.has(t)},e.get=function(t){var e;return null!==(e=this._namedCurves.get(t))&&void 0!==e?e:0},e.set=function(t,e){this._namedCurves.set(t,e)},t}();!function(t){t[t.TRANSFORM_COUNT=1]="TRANSFORM_COUNT",t[t.TRANSFORM_ORDER=2]="TRANSFORM_ORDER",t[t.AUXILIARY_CURVE_COUNT=4]="AUXILIARY_CURVE_COUNT"}(Vl||(Vl={}));var jl=(Sl=function(){},wl=function(){},Pl=function(){},Ol=function(){},Nl=function(){},Il=function(){},xl=function(){},Rl=function(){function t(t,e){this._origin=void 0,this._auxiliaryCurveRegistry=void 0,this._auxiliaryCurveRecords=[],this._transformRecords=[],this._parentTable=[],this._bindStarted=!1,this._transformCountBeforeBind=-1,this._auxiliaryCurveCountBeforeBind=-1,this._origin=t,this._auxiliaryCurveRegistry=e}var e=t.prototype;return e.getOrCreateTransformBinding=function(t){for(var e=this._origin,n=!1,r=t;r;r=r.parent)if(r===e){n=!0;break}if(!n)return null;var i=this._getOrCreateTransformBinding(t);if(t!==e)for(var o=t.parent;o!==e;o=o.parent)this._getOrCreateTransformBinding(o);return i},e._getOrCreateTransformBinding=function(t){var e=this._transformRecords,n=e.findIndex((function(e){return e.node===t}));if(n>=0){var r=e[n];return++r.refCount,r.handle}for(var i=0,o=function(t){var n=e.findIndex((function(e){return e.node===t}));if(n>=0)return i=n+1,1},a=t.parent;a&&!o(a);a=a.parent);for(var s=i;s<e.length;++s)++e[s].handle.index;var u=new Hl(new dc(this,i),t);return e.splice(i,0,u),u.handle},e.getOrCreateAuxiliaryCurveBinding=function(t){var e=this._auxiliaryCurveRecords,n=e.findIndex((function(e){return e.name===t}));if(n>=0){var r=e[n];return++r.refCount,r.handle}var i=e.length,o=new Ql(new vc(this,i),t);return e.push(o),o.handle},e.createEvaluationContext=function(){return c(!this._bindStarted),new pc(this.transformCount,this.auxiliaryCurveCount,this._parentTable.slice(),this._origin)},e.resetPoseStashAllocator=function(t){c(!this._bindStarted),t._reset(this.transformCount,this.auxiliaryCurveCount)},e.createTransformFilter=function(t){for(var e,n=this._origin,r=[],i=u(this._transformRecords);!(e=i()).done;){var o=e.value,a=o.node,s=o.handle,l=c(n,a);if(void 0===l)d(a.getPathInHierarchy()+" is not a child of "+n.getPathInHierarchy());else if(t.isExcluded(l))continue;r.push(s.index)}return r.sort(),new Nr(r);function c(t,e){for(var n=[],r=e;r;r=r.parent){if(r===t)return n.join("/");n.unshift(r.name)}}},e.fetchDefaultTransforms=function(t){var e=this._transformRecords.length;c(t.length===e);for(var n=0;n<e;++n){var r=this._transformRecords[n].defaultTransform;t.setTransform(n,r)}},e.apply=function(t){var e=t.transforms,n=t.auxiliaryCurves,r=this._transformRecords.length;c(e.length===r);for(var i=0;i<r;++i){var o=e.getTransform(i,Wl);this._transformRecords[i].node.setRTS(o.rotation,o.position,o.scale)}for(var a=this._auxiliaryCurveRecords.length,s=0;s<a;++s){var u=this._auxiliaryCurveRecords[s].name,l=n[s];this._auxiliaryCurveRegistry.set(u,l)}},e._destroyTransformHandle=function(t){c(t>=0&&t<this._transformRecords.length);var e=this._transformRecords[t];c(e.refCount>0),--e.refCount},e._destroyAuxiliaryCurveHandle=function(t){c(t>=0&&t<this._auxiliaryCurveRecords.length);var e=this._auxiliaryCurveRecords[t];c(e.refCount>0),--e.refCount},e.startBind=function(){this._bindStarted=!0,this._transformCountBeforeBind=this._transformRecords.length,this._auxiliaryCurveCountBeforeBind=this._auxiliaryCurveRecords.length},e.endBind=function(){var t=this._transformRecords,e=this._auxiliaryCurveRecords,n=0;if(Yl(t),t.length!==this._transformCountBeforeBind){n|=Vl.TRANSFORM_COUNT;for(var r=t.length,i=0;i<r;++i)t[i].order=i}else{for(var o=t.length,a=!1,s=0;s<o;++s){var u=t[s];u.order!==s&&(a=!0,u.order=s)}a&&(n|=Vl.TRANSFORM_ORDER)}Yl(e),e.length!==this._auxiliaryCurveCountBeforeBind&&(n|=Vl.AUXILIARY_CURVE_COUNT);var l=this._parentTable,c=this._origin;l.length=t.length;for(var h=function(){var e=t[f].node;if(e===c)return l[f]=-1,1;var n=e.parent;if(n===c){var r=t.findIndex((function(t){return t.node===n}));l[f]=r>=0?r:-1}else{var i=t.findIndex((function(t){return t.node===n}));l[f]=i}},f=0;f<t.length;++f)h();return this._bindStarted=!1,n},n(t,[{key:"transformCount",get:function(){return this._transformRecords.length}},{key:"auxiliaryCurveCount",get:function(){return this._auxiliaryCurveRecords.length}},{key:"auxiliaryCurveRegistry",get:function(t){function e(){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}((function(){return this._auxiliaryCurveRegistry}))}]),t}(),s(Rl.prototype,"getOrCreateTransformBinding",[Sl],Object.getOwnPropertyDescriptor(Rl.prototype,"getOrCreateTransformBinding"),Rl.prototype),s(Rl.prototype,"_getOrCreateTransformBinding",[wl],Object.getOwnPropertyDescriptor(Rl.prototype,"_getOrCreateTransformBinding"),Rl.prototype),s(Rl.prototype,"getOrCreateAuxiliaryCurveBinding",[Pl],Object.getOwnPropertyDescriptor(Rl.prototype,"getOrCreateAuxiliaryCurveBinding"),Rl.prototype),s(Rl.prototype,"_destroyTransformHandle",[Ol],Object.getOwnPropertyDescriptor(Rl.prototype,"_destroyTransformHandle"),Rl.prototype),s(Rl.prototype,"_destroyAuxiliaryCurveHandle",[Nl],Object.getOwnPropertyDescriptor(Rl.prototype,"_destroyAuxiliaryCurveHandle"),Rl.prototype),s(Rl.prototype,"startBind",[Il],Object.getOwnPropertyDescriptor(Rl.prototype,"startBind"),Rl.prototype),s(Rl.prototype,"endBind",[xl],Object.getOwnPropertyDescriptor(Rl.prototype,"endBind"),Rl.prototype),Rl),Hl=function(t,e){this.order=-1,this.refCount=1,this.handle=void 0,this.node=void 0,this.defaultTransform=void 0,this.handle=t,this.node=e;var n=new mr;n.position=e.position,n.rotation=e.rotation,n.scale=e.scale,this.defaultTransform=n},Ql=function(t,e){this.refCount=1,this.handle=void 0,this.name=void 0,this.handle=t,this.name=e};function Yl(t){var e=bl(t,(function(t){return c(t.refCount>=0),t.refCount>0}));if(c(e<=t.length),e!==t.length){for(var n=0;n<e;++n)t[n].handle.index=n;t.splice(e,t.length-e)}}var ql,Zl,Xl,Jl,Kl,$l,tc,ec,nc,rc,ic,oc,ac,sc,uc,lc=Symbol("[[DefaultTransforms]]"),cc=function(){function t(t){this._layoutMaintainer=t}return t.prototype.createTransformFilter=function(t){return this._layoutMaintainer.createTransformFilter(t)},n(t,[{key:"transformCount",get:function(){return this._layoutMaintainer.transformCount}}]),t}(),hc=new mr,fc=new mr,pc=function(){function t(t,e,n,r){this[lc]=void 0,this._poseAllocator=void 0,this._parentTable=void 0,this._componentNode=void 0,this._cacheComponentToWorldTransform=new mr,this._poseAllocator=new El(t,e),this._parentTable=n,this._componentNode=r,this[lc]=new fl(t)}var e=t.prototype;return e.destroy=function(){this._poseAllocator.destroy()},e.pushDefaultedPose=function(){var t=this._poseAllocator.push();return t.transforms.set(this[lc]),t._poseTransformSpace=br.LOCAL,t.auxiliaryCurves.fill(0),t},e.pushDefaultedPoseInComponentSpace=function(){var t=this.pushDefaultedPose();return this._poseTransformsSpaceLocalToComponent(t),t},e.pushZeroDeltaPose=function(){var t=this._poseAllocator.push();return t.transforms.fill(Pr),t._poseTransformSpace=br.LOCAL,t.auxiliaryCurves.fill(0),t},e.pushDuplicatedPose=function(t){var e=this._poseAllocator.push();return e.transforms.set(t.transforms),e._poseTransformSpace=t._poseTransformSpace,e.auxiliaryCurves.set(t.auxiliaryCurves),e},e.popPose=function(){this._poseAllocator.pop()},e._isStackTopPose_debugging=function(t){return t===this._poseAllocator.top},e._poseTransformsSpaceLocalToComponent=function(t){for(var e=t.transforms,n=e.length,r=0;r<n;++r){var i=this._parentTable[r];if(!(i<0)){var o=e.getTransform(r,hc),a=e.getTransform(i,fc);mr.multiply(o,a,o),e.setTransform(r,o)}}t._poseTransformSpace=br.COMPONENT},e._poseTransformsSpaceComponentToLocal=function(t){for(var e=t.transforms,n=e.length-1;n>=0;--n){var r=this._parentTable[n];if(!(r<0)){var i=e.getTransform(n,hc),o=e.getTransform(r,fc);mr.calculateRelative(i,i,o),e.setTransform(n,i)}}t._poseTransformSpace=br.LOCAL},e._convertPoseSpaceTransformToTargetSpace=function(t,e,n,r){var i=n._poseTransformSpace;switch(e){default:break;case Al.WORLD:i===br.COMPONENT?mr.multiply(t,this._getComponentToWorldTransform(),t):(c(i===br.LOCAL),mr.multiply(t,this._getLocalToWorldTransform(fc,n,r),t));break;case Al.COMPONENT:i===br.COMPONENT||(c(i===br.LOCAL),mr.multiply(t,this._getLocalToComponentTransform(fc,n,r),t));break;case Al.PARENT:if(i===br.COMPONENT){var o=this._parentTable[r];if(o>=0){var a=n.transforms.getTransform(o,fc),s=mr.invert(a,a);mr.multiply(t,s,t)}}else c(i===br.LOCAL);break;case Al.LOCAL:c(i===br.COMPONENT||i===br.LOCAL);var u=n.transforms.getTransform(r,fc),l=mr.invert(u,u);mr.multiply(t,l,t)}return t},e._convertTransformToPoseTransformSpace=function(t,e,n,r){var i=n._poseTransformSpace;switch(e){default:break;case Al.WORLD:if(i===br.COMPONENT){var o=mr.invert(fc,this._getComponentToWorldTransform());mr.multiply(t,o,t)}else{c(i===br.LOCAL);var a=this._getLocalToWorldTransform(fc,n,r),s=mr.invert(a,a);mr.multiply(t,s,t)}break;case Al.COMPONENT:if(i===br.COMPONENT);else{c(i===br.LOCAL);var u=this._getLocalToComponentTransform(fc,n,r),l=mr.invert(u,u);mr.multiply(t,l,t)}break;case Al.PARENT:if(i===br.COMPONENT){var h=this._parentTable[r];if(h>=0){var f=n.transforms.getTransform(h,fc);mr.multiply(t,f,t)}}break;case Al.LOCAL:c(i===br.COMPONENT||i===br.LOCAL);var p=n.transforms.getTransform(r,fc);mr.multiply(t,p,t)}return t},e._getComponentToWorldTransform=function(){var t=this._cacheComponentToWorldTransform,e=this._componentNode;return t.position=e.worldPosition,t.rotation=e.worldRotation,t.scale=e.worldScale,t},e._getLocalToComponentTransform=function(t,e,n){var r=this._parentTable;mr.setIdentity(t);for(var i=r[n];i>=0;i=r[i]){var o=e.transforms.getTransform(i,hc);mr.multiply(t,o,t)}return t},e._getLocalToWorldTransform=function(t,e,n){return this._getLocalToComponentTransform(t,e,n),mr.multiply(t,this._getComponentToWorldTransform(),t),t},n(t,[{key:"allocatedPoseCount",get:function(){return this._poseAllocator.allocatedCount}},{key:"parentTable",get:function(){return this._parentTable}},{key:"_stackSize_debugging",get:function(){return this._poseAllocator.allocatedCount}}]),t}(),dc=function(){function t(t,e){this.index=-1,this._host=void 0,this._host=t,this.index=e}return t.prototype.destroy=function(){this._host._destroyTransformHandle(this.index)},t}(),vc=function(){function t(t,e){this.index=-1,this._host=void 0,this._host=t,this.index=e}return t.prototype.destroy=function(){this._host._destroyAuxiliaryCurveHandle(this.index)},t}(),_c=function(){function t(){this._context={deltaTime:0,indicativeWeight:0}}var e=t.prototype;return e.generate=function(t,e){return this._context.deltaTime=t,this._context.indicativeWeight=e,this._context},e.forkSubWeight=function(t,e){this._context.deltaTime=t.deltaTime,this._context.indicativeWeight=t.indicativeWeight*e},t}(),yc=function(){function t(){this._allocator=null}var e=t.prototype;return e._reset=function(t,e){this._allocator=new Ll(t,e)},e.allocatePose=function(){return c(this._allocator),this._allocator.allocatePose()},e.destroyPose=function(t){return c(this._allocator),this._allocator.destroyPose(t)},n(t,[{key:"allocatedPoseCount",get:function(){return c(this._allocator),this._allocator.allocatedCount}}]),t}(),gc=P(K+"MotionSyncInfo")((Zl=function(){this.group=Xl&&Xl()},Xl=O(Zl.prototype,"group",[I],(function(){return""})),ql=Zl))||ql;function mc(t){return{listEntries:function(){return[{arg:{type:"clip-motion"},menu:"i18n:ENGINE.animation_graph.pose_graph_node_sub_menus.play_or_sample_clip_motion"},{arg:{type:"animation-blend-1d"},menu:"i18n:ENGINE.animation_graph.pose_graph_node_sub_menus.play_or_sample_animation_blend_1d"},{arg:{type:"animation-blend-2d"},menu:"i18n:ENGINE.animation_graph.pose_graph_node_sub_menus.play_or_sample_animation_blend_2d"}]},create:function(e){var n=null;switch(e.type){case"clip-motion":n=new ba;break;case"animation-blend-1d":n=new qa;break;case"animation-blend-2d":n=new As}return t(n)}}}var Ec=1e-5,Tc=(Jl=P(K+"PoseNodePlayMotion"),Kl=nr(hu),$l=rr(mc((function(t){var e=new Tc;return e.motion=t,e}))),tc=or({themeColor:"#227F9B"}),ec=ei({type:qn.FLOAT}),nc=ei({type:qn.FLOAT}),Jl(rc=Kl(rc=$l(rc=tc((ic=function(t){function r(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).motion=oc&&oc(),e.syncInfo=ac&&ac(),l(e,"startTime",sc,m(e)),l(e,"speedMultiplier",uc,m(e)),e._workspace=null,e._runtimeSyncRecord=void 0,e}e(r,t);var i=r.prototype;return i.bind=function(t){var e=this.motion;if(e){var n=e[No](t,!1);n&&(this._workspace=new Ac(n,n.createPort()),this.syncInfo.group&&(this._runtimeSyncRecord=t.motionSyncManager.register(this.syncInfo)))}},i.settle=function(){},i.reenter=function(){if(this._workspace){var t=this._runtimeSyncRecord,e=this._workspace.motionEval.duration;this._forceEvaluateEvaluation();var n=e<Ec?0:Y(this.startTime/e);t?t.notifyRenter(n):this._workspace.normalizedTime=n,this._workspace.lastIndicativeWeight=0}},i.doUpdate=function(t){if(this._workspace){var e=t.deltaTime,n=this._runtimeSyncRecord,r=this._workspace.motionEval.duration,i=0;r>Ec&&(i=e*this.speedMultiplier/r),n?n.notifyUpdate(i,t.indicativeWeight):this._workspace.normalizedTime+=i,this._workspace.lastIndicativeWeight=t.indicativeWeight}},i.doEvaluate=function(t){if(this._workspace){var e=this._runtimeSyncRecord?this._runtimeSyncRecord.getSyncedEnterTime():this._workspace.normalizedTime;return this._workspace.motionEvalPort.evaluate(e,t)}return t.pushDefaultedPose()},n(r,[{key:"lastIndicativeWeight",get:function(){var t,e;return null!==(t=null==(e=this._workspace)?void 0:e.lastIndicativeWeight)&&void 0!==t?t:0}},{key:"elapsedMotionTime",get:function(){var t,e;return null!==(t=null==(e=this._workspace)?void 0:e.normalizedTime)&&void 0!==t?t:0}}]),r}(Kr),oc=O(ic.prototype,"motion",[I],(function(){return new ba})),ac=O(ic.prototype,"syncInfo",[I],(function(){return new gc})),sc=s(ic.prototype,"startTime",[I,ec],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 0}}),uc=s(ic.prototype,"speedMultiplier",[I,nc],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 1}}),rc=ic))||rc)||rc)||rc)||rc),Ac=function(t,e){this.normalizedTime=0,this.lastIndicativeWeight=0,this.motionEval=t,this.motionEvalPort=e};function Cc(t){return t instanceof Kr||t instanceof $r}var bc=function(){function t(t,e){this._rootPoseNode=t,this._countingPlayMotionNodes=e}var e=t.prototype;return e.bind=function(t){var e;null==(e=this._rootPoseNode)||e.bind(t)},e.settle=function(t){var e;null==(e=this._rootPoseNode)||e.settle(t)},e.reenter=function(){var t;null==(t=this._rootPoseNode)||t.reenter()},e.update=function(t){var e;null==(e=this._rootPoseNode)||e.update(t)},e.evaluate=function(t){var e,n;return null!==(e=null==(n=this._rootPoseNode)?void 0:n.evaluate(t,Wr.LOCAL))&&void 0!==e?e:null},e.countMotionTime=function(){var t=this._countingPlayMotionNodes;if(!t)return 0;for(var e=0,n=Number.NEGATIVE_INFINITY,r=0;r<t.length;++r){var i=t[r],o=i.elapsedMotionTime,a=i.lastIndicativeWeight;a>n&&(n=a,e=o)}return e},t}();function Sc(t,e,n){void 0===n&&(n=!1);var r=t.outputNode,i=t.getShell(r).getBindings();if(c(i.length<2),0===i.length)return new bc(void 0,n?[]:void 0);var o=i[0];c(0===o.outputIndex),c(o.producer instanceof Kr);var a=new Map,s=wc(t,o.producer,a,e);return new bc(s,n?Array.from(a.values()).filter((function(t){return t instanceof Tc})):void 0)}function wc(t,e,n,r){var o=t.getShell(e),a=n.get(e);if(a)return a;var s=Vt(e);"__callOnAfterDeserializeRecursive"in s&&s.__callOnAfterDeserializeRecursive(),s instanceof $r&&s.link(r);for(var l,c=s,h=[],f=u(o.getBindings());!(l=f()).done;){var p=l.value,d=p.producer,v=p.outputIndex,_=p.inputPath;if(Cc(d)){var y=wc(t,d,n,r);if(y instanceof Kr)Nc(c,_,y,v);else{var g=kc(c,_,y,v);g&&h.push(g)}}else i("There's a input bound to a node with unrecognized type.")}var m,E=new Pc(h);return c instanceof Kr?(c._setDependencyEvaluation(E),m=c):m=new Oc(c,E),n.set(e,m),m}var Pc=function(){function t(t){this._bindingEvaluations=void 0,this._bindingEvaluations=t}return t.prototype.evaluate=function(){for(var t,e=this._bindingEvaluations,n=u(e);!(t=n()).done;)t.value.evaluate()},t}(),Oc=function(){function t(t,e){this._outputs=void 0,this._node=t,this._dependency=e,this._outputs=new Array(t.outputCount)}var e=t.prototype;return e.getDefaultOutput=function(){return this.getOutput(0)},e.getOutput=function(t){return this._outputs[t]},e.evaluate=function(){var t=this._node;this._dependency.evaluate(),t.selfEvaluate(this._outputs)},n(t,[{key:"node",get:function(){return this._node}},{key:"outputCount",get:function(){return this._outputs.length}}]),t}();function Nc(t,e,n,r){var o=e[0],a=e[1],s=void 0===a?-1:a;if(o in t)if(0===r){var u=t[o];if(s<0)return null!==u?void i("Invalid binding: consumer node's input "+o+" should be leaved as evaluation before evaluation."):void(t[o]=n);Array.isArray(u)?s>=u.length?i("Invalid binding: consumer node's input "+o+" have length "+u.length+" but the binding specified "+s):null===u[s]?u[s]=n:i("Invalid binding: consumer node's input "+o+"["+s+"] should be leaved as null before evaluation"):i("Invalid binding: consumer node's input "+o+" should be an array.")}else i("Node "+n.toString()+" does not have specified output "+r+".");else i("Invalid binding: consumer node has no property "+o)}var Ic,xc=function(){function t(t,e,n,r){this._consumerNode=t,this._consumerPropertyKey=e,this._producerRecord=n,this._producerOutputIndex=r}return t.prototype.evaluate=function(){this._producerRecord.evaluate(),this._consumerNode[this._consumerPropertyKey]=this._producerRecord.getOutput(this._producerOutputIndex)},t}(),Rc=function(){function t(t,e,n,r,i){this._consumerNode=t,this._consumerPropertyKey=e,this._consumerElementIndex=n,this._producerRecord=r,this._producerOutputIndex=i}return t.prototype.evaluate=function(){this._producerRecord.evaluate(),this._consumerNode[this._consumerPropertyKey][this._consumerElementIndex]=this._producerRecord.getOutput(this._producerOutputIndex)},t}();function kc(t,e,n,r){var o=e[0],a=e[1],s=void 0===a?-1:a;if(o in t){var u=t[o];if(s<0)return new xc(t,o,n,r);if(Array.isArray(u)){if(!(s>=u.length))return new Rc(t,o,s,n,r);i("Invalid binding: consumer node's input "+o+" have length "+u.length+" but the binding specified "+s)}else i("Invalid binding: consumer node's input "+o+" should be an array.")}else i("Invalid binding: consumer node has no property "+o)}var Lc=function(){function t(t,e,n){this.passthroughWeight=1,this._motionStates=[],this._proceduralPoseStates=[],this._topLevelEntry=void 0,this._topLevelExit=void 0,this._currentNode=void 0,this._pendingTransitionPath=[],this._activatedTransitions=[],this._activatedTransitionPool=hh.createPool(4),this._updateContextGenerator=new _c,this._conditionEvaluationContext=new ch,this._additive=!1,this._additive=n.additive,this.name=e,this._controller=n.controller;var r=this._addStateMachine(t,null,n,e),i=r.entry,o=r.exit;this._topLevelEntry=i,this._topLevelExit=o,this._currentNode=i,i.increaseActiveReference(),this._resetTrigger=n.triggerResetter}var e=t.prototype;return e.settle=function(t){for(var e=this._proceduralPoseStates,n=e.length,r=0;r<n;++r)e[r].settle(t)},e.reenter=function(){for(var t,e=u(this._activatedTransitions);!(t=e()).done;){var n=t.value;n.destination.decreaseActiveReference(),this._activatedTransitionPool.free(n)}this._activatedTransitions.length=0,this._topLevelEntry.increaseActiveReference(),this._currentNode.decreaseActiveReference(),this._currentNode=this._topLevelEntry},e.update=function(t){c(!this.exited),this._loopMatchTransitions(),this._resetStateTickFlagsAndWeights(),this._updateActivatedTransitions(t.deltaTime),this._commitStateUpdates(t)},e.evaluate=function(t){return this._sample(t)||this._pushNullishPose(t)},e.getCurrentStateStatus=function(){var t=this._currentNode;return t.kind===Dc.animation||t.kind===Dc.procedural?t.getStatus():null},e.getCurrentClipStatuses=function(){var t=this._currentNode;return t.kind===Dc.animation?t.getClipStatuses(t.absoluteWeight):Uc},e.getCurrentTransition=function(t){var e=this._activatedTransitions;if(0===e.length)return!1;var n=e[e.length-1],r=1===e.length?this._currentNode:e[e.length-2].destination,i=n.getAbsoluteDuration(r);return t.duration=i,t.time=n.normalizedElapsedTime*i,!0},e.getNextStateStatus=function(){var t=this._activatedTransitions;if(0===t.length)return null;var e=t[t.length-1].destination;switch(e.kind){default:break;case Dc.procedural:case Dc.animation:return e.getStatus()}return null},e.getNextClipStatuses=function(){var t,e=this._activatedTransitions;if(0===e.length)return Uc;var n=e[e.length-1],r=n.destination;return r.kind===Dc.animation&&null!==(t=r.getClipStatuses(n.destination.absoluteWeight))&&void 0!==t?t:Uc},e.overrideClips=function(t){for(var e=this._motionStates,n=e.length,r=0;r<n;++r)e[r].overrideClips(t)},e._addStateMachine=function(t,e,n,r){for(var i,o,a,s=this,l=Array.from(t.states()),c=l.map((function(e){if(e instanceof kn){var r=new zc(e,n);return s._motionStates.push(r),r}if(e===t.entryState)return i=new sh(e,Dc.entry,e.name);if(e===t.exitState)return a=new sh(e,Dc.exit,e.name);if(e===t.anyState)return o=new sh(e,Dc.any,e.name);if(e instanceof yo)return new uh(e);if(e instanceof Eo){var u=new lh(e,n);return s._proceduralPoseStates.push(u),u}return null})),h={components:null,parent:e,entry:i,exit:a,any:o},f=0;f<l.length;++f){var p=c[f];p&&(p.stateMachine=h)}for(var d=l.map((function(t){if(t instanceof bo){var e=s._addStateMachine(t.stateMachine,h,n,r+"/"+t.name);return e.components=new Wc(t),e}return null})),v=0;v<l.length;++v){var _=l[v],y=t.getOutgoings(_),g=void 0;if(_ instanceof bo){var m=d[v];g=m.exit}else{var E=c[v];g=E}for(var T,A=function(){var t,e=T.value,r=e.to,i=l.findIndex((function(t){return t===e.to}));if(r instanceof bo)t=d[i].entry;else{var o=c[i];t=o instanceof zc?o.entry:o}var a={conditions:e.conditions.map((function(t){return t[No](n)})),to:t,triggers:void 0,duration:0,normalizedDuration:!1,destinationStart:0,relativeDestinationStart:!1,exitCondition:0,exitConditionEnabled:!1,activated:!1,startEventBinding:void 0,endEventBinding:void 0};(vo(e)||e instanceof go||e instanceof Ao)&&(a.duration=e.duration,a.destinationStart=e.destinationStart,a.relativeDestinationStart=e.relativeDestinationStart,e.startEventBinding.isBound&&(a.startEventBinding=e.startEventBinding),e.endEventBinding.isBound&&(a.endEventBinding=e.endEventBinding),vo(e)&&(a.normalizedDuration=e.relativeDuration,a.exitConditionEnabled=e.exitConditionEnabled,a.exitCondition=e.exitCondition)),a.conditions.forEach((function(t,n){var r,i=e.conditions[n];i instanceof rl&&i.trigger&&(null!==(r=a.triggers)&&void 0!==r?r:a.triggers=[]).push(i.trigger)})),g.addTransition(a)},C=u(y);!(T=C()).done;)A()}return h},e._loopMatchTransitions=function(){var t=this._pendingTransitionPath,e=this._activatedTransitions;c(0===t.length);for(var n=0===e.length?this._currentNode:e[e.length-1].destination,r=0;;++r){if(r>=16){a(14e3,16,"");break}var i=this._matchNextTransition(n);if(!i)break;var o=i.to,s=n;if(n=o,Mc(o)){if(o===s)break;this._activateTransition(t,i),t.length=0}else t.push(i)}t.length=0},e._resetStateTickFlagsAndWeights=function(){var t=this._currentNode,e=this._activatedTransitions;t.resetTickFlagsAndWeight();for(var n=0;n<e.length;++n)e[n].destination.resetTickFlagsAndWeight()},e._commitStateUpdates=function(t){var e=this._currentNode,n=this._activatedTransitions;this._updateContextGenerator,this._commitStateUpdate(e,t);for(var r=0;r<n.length;++r){var i=n[r].destination;this._commitStateUpdate(i,t)}},e._commitStateUpdate=function(t,e){var n=this._updateContextGenerator;if(!t.testTickFlag(Fc.UPDATED))if(t.setTickFlag(Fc.UPDATED),t.kind===Dc.animation)t.update(e.deltaTime,this._controller);else if(t.kind===Dc.procedural){var r=n.generate(e.deltaTime,e.indicativeWeight*t.absoluteWeight);t.update(r)}},e._sample=function(t){var e,n=this._currentNode,r=this._activatedTransitions,i=1,o=null,a=0;if(n.kind===Dc.animation)o=null!==(e=n.evaluate(t))&&void 0!==e?e:this._pushNullishPose(t);else if(n.kind===Dc.procedural){var s;o=null!==(s=n.evaluate(t))&&void 0!==s?s:this._pushNullishPose(t)}else i-=n.absoluteWeight,o=null;o&&(a=n.absoluteWeight),n.setTickFlag(Fc.EVALUATED);for(var u=0;u<r.length;++u){var l=r[u].destination;if(!l.testTickFlag(Fc.EVALUATED)){l.setTickFlag(Fc.EVALUATED);var c,h=l.absoluteWeight,f=void 0;l.kind===Dc.empty?(i-=h,f=null):f=null!==(c=l.evaluate(t))&&void 0!==c?c:this._pushNullishPose(t),f&&(o&&(a+=h)?(Ir(o,f,h/a),t.popPose()):o=f)}}return this.passthroughWeight=i,o},e._pushNullishPose=function(t){return this._additive?t.pushZeroDeltaPose():t.pushDefaultedPose()},e._matchNextTransition=function(t){var e=this._matchTransition(t,t);if(e)return e;if(t.kind===Dc.animation||t.kind===Dc.procedural){var n=this._matchAnyScoped(t);if(n)return n}return null},e._matchAnyScoped=function(t){for(var e=t.stateMachine;null!==e;e=e.parent){var n=this._matchTransition(e.any,t);if(n)return n}return null},e._matchTransition=function(t,e){c(t===e||t.kind===Dc.any),this._conditionEvaluationContext.set(e);for(var n=t.outgoingTransitions,r=n.length,i=0;i<r;++i){var o=n[i];if(!o.activated){var a=o.conditions,s=a.length;if(0===s){if(t.kind===Dc.entry||t.kind===Dc.exit)return o;if(!o.exitConditionEnabled)continue}if(e.kind===Dc.animation&&o.exitConditionEnabled){var u=e.duration*o.exitCondition;if(e.time<u)break}for(var l=!0,h=0;h<s;++h)if(!a[h].eval(this._conditionEvaluationContext)){l=!1;break}if(l)return o}}return null},e._activateTransition=function(t,e){var n=e.to;c(Mc(n));var r=this._activatedTransitionPool.alloc();r.reset(t,e),this._activatedTransitions.push(r);for(var i=r.path.length,o=0;o<i;++o){var a=r.path[o];this._resetTriggersOnTransition(a)}for(var s=0;s<r.path.length;++s){var u=r.path[s];this._callEnterMethods(u.to)}c(this._activatedTransitions.length>0);var l=1===this._activatedTransitions.length?this._currentNode:this._activatedTransitions[this._activatedTransitions.length-2].destination;l instanceof Gc&&l.transitionOutEventBinding&&this._emit(l.transitionOutEventBinding),e.startEventBinding&&this._emit(e.startEventBinding),n instanceof Gc&&n.transitionInEventBinding&&this._emit(n.transitionInEventBinding)},e._updateActivatedTransitions=function(t){for(var e=this._activatedTransitions,n=e.length-1,r=1,i=n;n>=0;--n){var o=e[n],a=0===n?this._currentNode:e[n-1].destination;if(o.update(t,a),o.done){this._dropActivatedTransitions(i);break}var s=o.normalizedElapsedTime*r;o.destination.increaseAbsoluteWeight(s),r*=1-o.normalizedElapsedTime,i=n-1}this._currentNode.increaseAbsoluteWeight(r)},e._dropActivatedTransitions=function(t){var e=this._activatedTransitions,n=this._activatedTransitionPool;c(t>=0&&t<e.length);var r=t+1,i=e[t],o=i.destination;c(0!==i.path.length);var a=i.path[i.path.length-1];a.endEventBinding&&this._emit(a.endEventBinding),this._callExitMethods(this._currentNode);for(var s=0;s<=t;++s){var u=e[s];s!==t&&u.destination.decreaseActiveReference();for(var l=s===t?u.path.length-1:u.path.length,h=0;h<l;++h){var f=u.path[h];this._callExitMethods(f.to)}n.free(u)}if(t===e.length-1)e.length=0;else{for(var p=t+1;p<e.length;++p)e[p-r]=e[p];e.length-=r}this._currentNode.decreaseActiveReference(),this._currentNode=o},e._resetTriggersOnTransition=function(t){var e=t.triggers;if(e)for(var n=e.length,r=0;r<n;++r){var i=e[r];this._resetTrigger(i)}},e._resetTrigger=function(t){(0,this._triggerReset)(t)},e._callEnterMethods=function(t){var e,n=this._controller;switch(t.kind){default:break;case Dc.animation:t.components.callMotionStateEnterMethods(n,t.getStatus());break;case Dc.entry:null==(e=t.stateMachine.components)||e.callStateMachineEnterMethods(n)}},e._callExitMethods=function(t){var e,n=this._controller;switch(t.kind){default:break;case Dc.animation:t.components.callMotionStateExitMethods(n,t.getStatus());break;case Dc.exit:null==(e=t.stateMachine.components)||e.callStateMachineExitMethods(n)}},e._emit=function(t){t.emit(this._controller.node)},n(t,[{key:"exited",get:function(){return this._currentNode===this._topLevelExit}}]),t}();function Mc(t){return t.kind===Dc.animation||t.kind===Dc.empty||t.kind===Dc.procedural}var Dc,Bc={next:function(){return{done:!0,value:void 0}}},Uc=((Ic={})[Symbol.iterator]=function(){return Bc},Ic);!function(t){t[t.entry=0]="entry",t[t.exit=1]="exit",t[t.any=2]="any",t[t.animation=3]="animation",t[t.empty=4]="empty",t[t.procedural=5]="procedural"}(Dc||(Dc={}));var Fc,Vc=function(){function t(t){this.name=void 0,this.outgoingTransitions=[],this._activeReferenceCount=0,this._tickFlags=0,this._absoluteWeight=0,this.name=t.name}var e=t.prototype;return e.setPrefix_debug=function(t){this.__DEBUG_ID__=""+t+this.name},e.addTransition=function(t){this.outgoingTransitions.push(t)},e.increaseActiveReference=function(){0===this._activeReferenceCount&&(this._absoluteWeight=0,this._tickFlags=0),++this._activeReferenceCount},e.decreaseActiveReference=function(){--this._activeReferenceCount},e.resetTickFlagsAndWeight=function(){this._checkActivated(),this._absoluteWeight=0,this._tickFlags=0},e.increaseAbsoluteWeight=function(t){this._absoluteWeight+=t},e.testTickFlag=function(t){return!!(this._tickFlags&t)},e.setTickFlag=function(t){c(!this.testTickFlag(t),"Can not set "+Fc[t]+" since it has been set!"),this._tickFlags|=t},e._checkActivated=function(){c(this._activeReferenceCount>0)},n(t,[{key:"absoluteWeight",get:function(){return this._absoluteWeight}},{key:"activeReferenceCount",get:function(){return this._activeReferenceCount}}]),t}(),Gc=function(t){function n(e){var n;return(n=t.call(this,e)||this).transitionInEventBinding=void 0,n.transitionOutEventBinding=void 0,e.transitionInEventBinding.isBound&&(n.transitionInEventBinding=e.transitionInEventBinding),e.transitionOutEventBinding.isBound&&(n.transitionOutEventBinding=e.transitionOutEventBinding),n}return e(n,t),n}(Vc);!function(t){t[t.UPDATED=1]="UPDATED",t[t.EVALUATED=2]="EVALUATED"}(Fc||(Fc={}));var Wc=function(){function t(t){this._components=t.instantiateComponents()}var e=t.prototype;return e.callMotionStateEnterMethods=function(t,e){this._callMotionStateCallbackIfNonDefault("onMotionStateEnter",t,e)},e.callMotionStateUpdateMethods=function(t,e){this._callMotionStateCallbackIfNonDefault("onMotionStateUpdate",t,e)},e.callMotionStateExitMethods=function(t,e){this._callMotionStateCallbackIfNonDefault("onMotionStateExit",t,e)},e.callStateMachineEnterMethods=function(t){this._callStateMachineCallbackIfNonDefault("onStateMachineEnter",t)},e.callStateMachineExitMethods=function(t){this._callStateMachineCallbackIfNonDefault("onStateMachineExit",t)},e._callMotionStateCallbackIfNonDefault=function(t,e,n){for(var r=this._components,i=r.length,o=0;o<i;++o){var a=r[o];a[t]!==ol.prototype[t]&&a[t](e,n)}},e._callStateMachineCallbackIfNonDefault=function(t,e){for(var n=this._components,r=n.length,i=0;i<r;++i){var o=n[i];o[t]!==ol.prototype[t]&&o[t](e)}},t}(),zc=function(){function t(t,e){var n,r;this._source=null,this._baseSpeed=1,this._speed=1,this._publicState=void 0,this._privateState=void 0;var i=t.name;if(this._baseSpeed=t.speed,this._setSpeedMultiplier(1),t.speedMultiplierEnabled&&t.speedMultiplier){var o=t.speedMultiplier,a=e.getVar(o);if(Fa(a,o)){Va(a.type,we.FLOAT,o),a.bind(this._setSpeedMultiplier,this);var s=a.value;this._setSpeedMultiplier(s)}}var u=null!==(n=null==(r=t.motion)?void 0:r[No](e,!1))&&void 0!==n?n:null;u&&Object.defineProperty(u,"__DEBUG_ID__",{value:i}),this._source=u,this._publicState=new jc(this,t,null==u?void 0:u.createPort()),this._privateState=new jc(this,t,null==u?void 0:u.createPort()),this.components=new Wc(t)}var e=t.prototype;return e.setPrefix_debug=function(t){this._publicState.setPrefix_debug(t),this._privateState.setPrefix_debug(t)},e.addTransition=function(t){t.to===this._publicState?this._publicState.addTransition(b({},t,{to:this._privateState})):this._publicState.addTransition(t),this._privateState.addTransition(t)},e.getClipStatuses=function(t){var e,n=this._source;return n?((e={})[Symbol.iterator]=function(){return n.getClipStatuses(t)},e):Uc},e.overrideClips=function(t){var e;null==(e=this._source)||e.overrideClips(t)},e._setSpeedMultiplier=function(t){this._speed=this._baseSpeed*t},n(t,[{key:"duration",get:function(){var t,e;return null!==(t=null==(e=this._source)?void 0:e.duration)&&void 0!==t?t:0}},{key:"speed",get:function(){return this._speed}},{key:"entry",get:function(){return this._publicState}},{key:"stateMachine",get:function(){return this._stateMachine},set:function(t){this._stateMachine=t,this._publicState.stateMachine=t,this._privateState.stateMachine=t}}]),t}(),jc=function(t){function r(e,n,r){var i;return(i=t.call(this,n)||this).kind=Dc.animation,i._container=void 0,i._progress=0,i._port=void 0,i._statusCache={progress:0},i._container=e,i._port=r,i}e(r,t);var i=r.prototype;return i.reenter=function(t){var e;this._progress=t,null==(e=this._port)||e.reenter()},i.getStatus=function(){var t=this._statusCache;return t.progress=Qc(this._progress),t},i.getClipStatuses=function(t){return this._container.getClipStatuses(t)},i.update=function(t,e){this._progress=Hc(this._progress,this.duration,t*this._container.speed),this._container.components.callMotionStateUpdateMethods(e,this.getStatus())},i.evaluate=function(t){var e,n;return null!==(e=null==(n=this._port)?void 0:n.evaluate(this._progress,t))&&void 0!==e?e:null},n(r,[{key:"duration",get:function(){return this._container.duration}},{key:"components",get:function(){return this._container.components}},{key:"normalizedTime",get:function(){return this._progress}},{key:"time",get:function(){return this._progress*this._container.duration}}]),r}(Gc);function Hc(t,e,n){return 0===e?0:t+n/e}function Qc(t){var e=t-Math.trunc(t);return e>=0?e:1+e}var Yc,qc,Zc,Xc,Jc,Kc,$c,th,eh,nh,rh,ih,oh,ah,sh=function(t){function n(e,n){var r;return(r=t.call(this,e)||this).kind=void 0,r.kind=n,r}return e(n,t),n}(Vc),uh=function(t){function n(e){var n;return(n=t.call(this,e)||this).kind=Dc.empty,n}return e(n,t),n}(Vc),lh=function(t){function n(e,n){var r;(r=t.call(this,e)||this).kind=Dc.procedural,r.elapsedTime=0,r.statusCache={progress:0},r._instantiatedPoseGraph=void 0,r._statusCache={progress:0},r._elapsedTime=0;var i=Sc(e.graph,n,!0);return i.bind(n),r._instantiatedPoseGraph=i,r._statusCache.progress=0,r}e(n,t);var r=n.prototype;return r.settle=function(t){this._instantiatedPoseGraph.settle(t)},r.reenter=function(){this._statusCache.progress=0,this._instantiatedPoseGraph.reenter()},r.update=function(t){this._elapsedTime+=t.deltaTime,this._instantiatedPoseGraph.update(t)},r.evaluate=function(t){var e;return null!==(e=this._instantiatedPoseGraph.evaluate(t))&&void 0!==e?e:null},r.getStatus=function(){return this._statusCache.progress=Qc(this._elapsedTime),this._statusCache},r.countMotionTime=function(){return this._instantiatedPoseGraph.countMotionTime()},n}(Gc),ch=function(){function t(){this.sourceStateWeight=0,this._sourceState=void 0}var e=t.prototype;return e.set=function(t){this._sourceState=t,Mc(t)?(c(t.activeReferenceCount),this.sourceStateWeight=t.absoluteWeight):this.sourceStateWeight=0},e.unset=function(){this._sourceState=void 0,this.sourceStateWeight=0},n(t,[{key:"sourceStateMotionTimeNormalized",get:function(){var t=this._sourceState;switch(c(t&&(t.kind===Dc.animation||t.kind===Dc.procedural)&&t.activeReferenceCount),t.kind){case Dc.animation:return t.normalizedTime;case Dc.procedural:return t.countMotionTime();default:return 0}}}]),t}(),hh=function(){function t(){this.normalizedElapsedTime=0,this.path=[],this._durationMultiplier=1}var e=t.prototype;return e.getAbsoluteDuration=function(t){return this._getAbsoluteDurationUnscaled(t)*this._durationMultiplier},e.update=function(t,e){if(Mc(e)){var n=this.getAbsoluteDuration(e),r=0;if(n<=0)r=0,this.normalizedElapsedTime=1;else{var i=this.normalizedElapsedTime*n,o=n-i;r=Math.min(o,t);var a=Y((i+r)/n);this.normalizedElapsedTime=a}}else this.normalizedElapsedTime=1},t.createPool=function(e){return new C((function(){return new t}),e,void 0)},e.reset=function(t,e){var n=e.to;c(Mc(n)),this.normalizedElapsedTime=0,this.destination=n,this.path=[].concat(t,[e]);var r=n.activeReferenceCount;if(n.increaseActiveReference(),0===r)if(n.kind===Dc.animation){var i=this.path[0],o=i.destinationStart,a=i.relativeDestinationStart?o:0===n.duration?0:o/n.duration;n.reenter(a)}else n.kind===Dc.procedural&&n.reenter();c(n.activeReferenceCount>0),this._durationMultiplier=1-n.absoluteWeight},e._getAbsoluteDurationUnscaled=function(t){c(0!==this.path.length);var e=this.path[0],n=e.duration;return e.normalizedDuration?(t.kind===Dc.animation?t.duration:1)*n:n},n(t,[{key:"done",get:function(){return j(this.normalizedElapsedTime,1,1e-6)}}]),t}();P(K+"PoseNodeStateMachine")(Yc=nr(hu)(Yc=or({themeColor:"#CCCCCC",inline:!0})((qc=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).name=Zc&&Zc(),e.stateMachine=Xc&&Xc(),e._stateMachineEval=void 0,e}e(n,t);var r=n.prototype;return r.__callOnAfterDeserializeRecursive=function(){this.stateMachine._allowEmptyStates=!1,this.stateMachine.__callOnAfterDeserializeRecursive()},r.bind=function(t){c(!this._stateMachineEval),this._stateMachineEval=new Lc(this.stateMachine,"",t)},r.settle=function(t){var e;null==(e=this._stateMachineEval)||e.settle(t)},r.reenter=function(){var t;null==(t=this._stateMachineEval)||t.reenter()},r.doUpdate=function(t){var e=this._stateMachineEval;e.update(t),c(e.passthroughWeight>.99999)},r.doEvaluate=function(t){var e=this._stateMachineEval,n=e.evaluate(t);return c(e.passthroughWeight>.99999),n},n}(Kr),Zc=O(qc.prototype,"name",[I],(function(){return""})),Xc=O(qc.prototype,"stateMachine",[I],(function(){return new Co(!1)})),Yc=qc))||Yc)||Yc);var fh,ph,dh,vh,_h,yh,gh,mh,Eh,Th,Ah,Ch,bh,Sh,wh,Ph,Oh,Nh,Ih,xh,Rh,kh,Lh,Mh,Dh,Bh,Uh,Fh,Vh,Gh,Wh=(Jc=P(K+"PoseNodeSampleMotion"),Kc=nr(hu),$c=rr(mc((function(t){var e=new Wh;return e.motion=t,e}))),th=or({themeColor:"#D97721"}),eh=ei({type:qn.FLOAT}),Jc(nh=Kc(nh=$c(nh=th((rh=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).motion=ih&&ih(),l(e,"time",oh,m(e)),e.useNormalizedTime=ah&&ah(),e._workspace=null,e}e(n,t);var r=n.prototype;return r.bind=function(t){var e=this.motion;if(e){var n=e[No](t,!0);if(n){var r=new zh(n,n.createPort());this._workspace=r}}},r.settle=function(){},r.reenter=function(){},r.doUpdate=function(){},r.doEvaluate=function(t){var e=this._workspace;if(!e)return t.pushDefaultedPose();var n=this.time,r=this.useNormalizedTime?n:n/e.motionEval.duration;return e.motionEvalPort.evaluate(Y(r),t)},n}(Kr),ih=O(rh.prototype,"motion",[I],(function(){return new ba})),oh=s(rh.prototype,"time",[I,eh],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 0}}),ah=O(rh.prototype,"useNormalizedTime",[I],(function(){return!1})),nh=rh))||nh)||nh)||nh)||nh),zh=function(t,e){this.motionEval=t,this.motionEvalPort=e};function jh(t){return t<1e-5}fh=P(K+"PoseNodeAdditivelyBlend"),ph=nr(fu),dh=or({themeColor:"#72A869"}),vh=ei({type:qn.POSE}),_h=ei({type:qn.POSE}),yh=ei({type:qn.FLOAT}),fh(gh=ph(gh=dh((mh=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return e=t.call.apply(t,[this].concat(r))||this,l(e,"basePose",Eh,m(e)),l(e,"additivePose",Th,m(e)),l(e,"ratio",Ah,m(e)),e}e(n,t);var r=n.prototype;return r.bind=function(t){var e,n;null==(e=this.basePose)||e.bind(t),t._pushAdditiveFlag(!0),null==(n=this.additivePose)||n.bind(t),t._popAdditiveFlag()},r.settle=function(t){var e,n;null==(e=this.basePose)||e.settle(t),null==(n=this.additivePose)||n.settle(t)},r.reenter=function(){var t,e;null==(t=this.basePose)||t.reenter(),null==(e=this.additivePose)||e.reenter()},r.doUpdate=function(t){var e,n;null==(e=this.basePose)||e.update(t),null==(n=this.additivePose)||n.update(t)},r.doEvaluate=function(t){var e,n,r=null!==(e=null==(n=this.basePose)?void 0:n.evaluate(t,Wr.LOCAL))&&void 0!==e?e:t.pushDefaultedPose();return this.additivePose?(Vr(r,this.additivePose.evaluate(t,Wr.LOCAL),this.ratio),t.popPose(),r):r},n}(Kr),Eh=s(mh.prototype,"basePose",[I,vh],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Th=s(mh.prototype,"additivePose",[I,_h],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Ah=s(mh.prototype,"ratio",[I,yh],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 1}}),gh=mh))||gh)||gh),Ch=P(K+"PoseNodeBlendInProportion"),bh=nr(fu),Sh=or({themeColor:"#72A869"}),wh=ei({type:qn.POSE,arraySyncGroup:"blend-item"}),Ph=ei({type:qn.FLOAT,arraySyncGroup:"blend-item",arraySyncGroupFollower:!0}),Ch(Oh=bh(Oh=Sh((Nh=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return e=t.call.apply(t,[this].concat(r))||this,l(e,"poses",Ih,m(e)),l(e,"proportions",xh,m(e)),e._updateContextGenerator=new _c,e}e(n,t);var r=n.prototype;return r.bind=function(t){for(var e,n=u(this.poses);!(e=n()).done;){var r=e.value;null==r||r.bind(t)}},r.settle=function(t){for(var e,n=u(this.poses);!(e=n()).done;){var r=e.value;null==r||r.settle(t)}},r.reenter=function(){for(var t,e=u(this.poses);!(t=e()).done;){var n=t.value;null==n||n.reenter()}},r.doUpdate=function(t){for(var e=this._updateContextGenerator,n=this.poses.length,r=0;r<n;++r){var i,o=this.proportions[r];if(!jh(o)){var a=e.generate(t.deltaTime,t.indicativeWeight*o);null==(i=this.poses[r])||i.update(a)}}},r.doEvaluate=function(t){for(var e=this.poses.length,n=0,r=null,i=0;i<e;++i){var o,a=this.proportions[i];if(!jh(a)){var s=null==(o=this.poses[i])?void 0:o.evaluate(t,Wr.LOCAL);s&&(n+=a,r?(n&&Ir(r,s,a/n),t.popPose()):r=s)}}return r||t.pushDefaultedPose()},n}(Kr),Ih=s(Nh.prototype,"poses",[I,wh],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),xh=s(Nh.prototype,"proportions",[I,Ph],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),Oh=Nh))||Oh)||Oh);var Hh,Qh,Yh,qh,Zh,Xh,Jh,Kh,$h=(Rh=P(K+"PoseNodeBlendTwoPoseBase"),kh=ir(!0),Lh=ei({type:qn.POSE}),Mh=ei({type:qn.POSE}),Dh=ei({type:qn.FLOAT}),Rh(Bh=kh((Uh=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return e=t.call.apply(t,[this].concat(r))||this,l(e,"pose0",Fh,m(e)),l(e,"pose1",Vh,m(e)),l(e,"ratio",Gh,m(e)),e._updateContextGenerator=new _c,e}e(n,t);var r=n.prototype;return r.bind=function(t){var e,n;null==(e=this.pose0)||e.bind(t),null==(n=this.pose1)||n.bind(t)},r.settle=function(t){var e,n;null==(e=this.pose0)||e.settle(t),null==(n=this.pose1)||n.settle(t)},r.reenter=function(){var t,e;null==(t=this.pose0)||t.reenter(),null==(e=this.pose1)||e.reenter()},r.doUpdate=function(t){var e=this.pose0,n=this.pose1,r=this._updateContextGenerator,i=this.ratio,o=r.generate(t.deltaTime,t.indicativeWeight*(1-i));null==e||e.update(o);var a=r.generate(t.deltaTime,t.indicativeWeight*i);null==n||n.update(a)},r.doEvaluate=function(t){var e,r,i,o,a=Wr.LOCAL;if(!this.pose0||!this.pose1)return n.evaluateDefaultPose(t,a);var s=null!==(e=null==(r=this.pose0)?void 0:r.evaluate(t,a))&&void 0!==e?e:n.evaluateDefaultPose(t,a),u=null!==(i=null==(o=this.pose1)?void 0:o.evaluate(t,a))&&void 0!==i?i:n.evaluateDefaultPose(t,a);return this.doBlend(s,u,this.ratio),t.popPose(),s},n}(Kr),Fh=s(Uh.prototype,"pose0",[I,Lh],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Vh=s(Uh.prototype,"pose1",[I,Mh],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Gh=s(Uh.prototype,"ratio",[I,Dh],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 1}}),Bh=Uh))||Bh)||Bh);P(K+"PoseNodeBlendTwoPose")(Hh=nr(fu)(Hh=or({themeColor:"#72A869"})(Hh=function(t){function n(){return t.apply(this,arguments)||this}return e(n,t),n.prototype.doBlend=function(t,e,n){return Ir(t,e,n)},n}($h))||Hh)||Hh),Qh=P(K+"PoseNodeFilteringBlend"),Yh=nr(fu),qh=or({themeColor:"#72A869"}),Zh=W(su),Qh(Xh=Yh(Xh=qh((Jh=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).mask=Kh&&Kh(),e._transformFilter=void 0,e}e(n,t);var r=n.prototype;return r.settle=function(e){if(t.prototype.settle.call(this,e),this.mask){var n=e.createTransformFilter(this.mask);this._transformFilter=n}},r.doBlend=function(t,e,n){Ir(t,e,n,this._transformFilter)},n}($h),Kh=O(Jh.prototype,"mask",[I,Zh],(function(){return null})),Xh=Jh))||Xh)||Xh);var tf,ef,nf,rf,of,af,sf,uf,lf,cf,hf,ff,pf,df,vf,_f,yf,gf,mf,Ef,Tf,Af,Cf,bf,Sf,wf,Pf,Of,Nf,If,xf,Rf,kf=hu+"/i18n:ENGINE.animation_graph.pose_graph_node_sub_categories.pose_nodes_choose/",Lf=1e-5,Mf=P(K+"PoseNodeChoosePoseBase")(tf=ir()((ef=function(t){function n(e){var n;return void 0===e&&(e=0),(n=t.call(this)||this)._poses=nf&&nf(),n._fadeInDurations=rf&&rf(),n._updateContextGenerator=new _c,n._evaluationRecord=void 0,n._poses.length=e,n._poses.fill(null),n._fadeInDurations.length=e,n._fadeInDurations.fill(0),n}e(n,t);var r=n.prototype;return r.bind=function(t){for(var e,n=u(this._poses);!(e=n()).done;){var r=e.value;null==r||r.bind(t)}var i=new Df(this._poses.length,this.getChosenIndex());this._evaluationRecord=i},r.settle=function(t){for(var e,n=u(this._poses);!(e=n()).done;){var r=e.value;null==r||r.settle(t)}},r.reenter=function(){for(var t,e=u(this._poses);!(t=e()).done;){var n=t.value;null==n||n.reenter()}},r.doUpdate=function(t){var e=this._poses,n=this._evaluationRecord;if(n.update(t.deltaTime,this.getChosenIndex(),this._fadeInDurations),!n.allWeightsAreZero()){var r=e.length,i=n.items;c(i.length===r);for(var o=0;o<r;++o){var a=i[o].weight;if(!jh(a)){var s=e[o],u=this._updateContextGenerator.generate(t.deltaTime,t.indicativeWeight*a);null==s||s.update(u)}}}},r.doEvaluate=function(t){var e=this._poses,r=this._evaluationRecord,i=Wr.LOCAL,o=e.length,a=r.items;c(a.length===e.length);var s=null;if(!r.allWeightsAreZero())for(var u=0,l=0;l<o;++l){var h,f=r.items[l].weight;if(!jh(f)){var p=null==(h=e[l])?void 0:h.evaluate(t,Wr.LOCAL);p&&(u+=f,s?(u&&Ir(s,p,f/u),t.popPose()):s=p)}}return s||n.evaluateDefaultPose(t,i)},r.getChosenIndex=function(){return 0},n}(Kr),nf=O(ef.prototype,"_poses",[I],(function(){return[]})),rf=O(ef.prototype,"_fadeInDurations",[I],(function(){return[]})),tf=ef))||tf)||tf,Df=function(){function t(t,e){this._items=void 0,this._chosenPoseIndex=-1,this._elapsedTransitionTime=0,this._blendingDuration=0;var n=Array.from({length:t},(function(){return new Bf}));e>=0&&e<t&&(n[e].selfSourceWeight=1,n[e].selfTargetWeight=1,n[e].weight=1),this._items=n}var e=t.prototype;return e.allWeightsAreZero=function(){return this._chosenPoseIndex<0},e.update=function(t,e,n){if(this._checkAlternation(e,n),!(this._chosenPoseIndex<0)){var r=this._elapsedTransitionTime,i=this._blendingDuration,o=this._items;if(!(r>=i)){var a=o.length,s=0,u=0;t>i-r?(this._elapsedTransitionTime=i,u=1):(this._elapsedTransitionTime+=t,u=this._elapsedTransitionTime/i);for(var l=0;l<a;++l){var c=o[l],h=U(c.selfSourceWeight,c.selfTargetWeight,u);s+=h,c.weight=h}if(!jh(s))for(var f=0;f<a;++f)o[f].weight/=s}}},e._checkAlternation=function(t,e){var n=this._items,r=this._chosenPoseIndex,i=n.length;if(i&&t!==r&&!(t<0||t>=i)){var o=Math.max(e[t],0);if(o<Lf)for(var a=0;a<i;++a){var s=n[a];a===t?(s.selfSourceWeight=1,s.selfTargetWeight=1,s.weight=1):(s.selfSourceWeight=0,s.selfTargetWeight=0,s.weight=0)}else for(var u=this._blendingDuration<Lf?1:this._elapsedTransitionTime/this._blendingDuration,l=0;l<i;++l){var c=n[l];c.selfSourceWeight=U(c.selfSourceWeight,c.selfTargetWeight,u),c.selfTargetWeight=l===t?1:0}this._chosenPoseIndex=t,this._elapsedTransitionTime=0,this._blendingDuration=o}},n(t,[{key:"items",get:function(){return this._items}}]),t}(),Bf=function(){this.selfSourceWeight=0,this.selfTargetWeight=0,this.weight=0};of=P(K+"PoseNodeChoosePoseByBoolean"),af=nr(kf),sf=or({themeColor:"#D07979"}),uf=ei({type:qn.POSE}),lf=ei({type:qn.POSE}),cf=ei({type:qn.FLOAT}),hf=ei({type:qn.FLOAT}),ff=ei({type:qn.BOOLEAN}),of(pf=af(pf=sf((df=function(t){function r(){var e;return e=t.call(this,2)||this,l(e,"choice",vf,m(e)),e}return e(r,t),r.prototype.getChosenIndex=function(){return this.choice?0:1},n(r,[{key:"truePose",get:function(){return this._poses[0]},set:function(t){this._poses[0]=t}},{key:"falsePose",get:function(){return this._poses[1]},set:function(t){this._poses[1]=t}},{key:"trueFadeInDuration",get:function(){return this._fadeInDurations[0]},set:function(t){this._fadeInDurations[0]=t}},{key:"falseFadeInDuration",get:function(){return this._fadeInDurations[1]},set:function(t){this._fadeInDurations[1]=t}}]),r}(Mf),s(df.prototype,"truePose",[uf],Object.getOwnPropertyDescriptor(df.prototype,"truePose"),df.prototype),s(df.prototype,"falsePose",[lf],Object.getOwnPropertyDescriptor(df.prototype,"falsePose"),df.prototype),s(df.prototype,"trueFadeInDuration",[cf],Object.getOwnPropertyDescriptor(df.prototype,"trueFadeInDuration"),df.prototype),s(df.prototype,"falseFadeInDuration",[hf],Object.getOwnPropertyDescriptor(df.prototype,"falseFadeInDuration"),df.prototype),vf=s(df.prototype,"choice",[I,ff],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!0}}),pf=df))||pf)||pf),_f=P(K+"PoseNodeChoosePoseByIndex"),yf=nr(kf),gf=or({themeColor:"#D07979"}),mf=ei({type:qn.POSE,arraySyncGroup:"choose-item"}),Ef=ei({type:qn.FLOAT,arraySyncGroup:"choose-item",arraySyncGroupFollower:!0}),Tf=ei({type:qn.INTEGER}),_f(Af=yf(Af=gf((Cf=function(t){function r(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return e=t.call.apply(t,[this].concat(r))||this,l(e,"choice",bf,m(e)),e}return e(r,t),r.prototype.getChosenIndex=function(){return this.choice},n(r,[{key:"poses",get:function(){return this._poses},set:function(t){this._poses=t}},{key:"fadeInDurations",get:function(){return this._fadeInDurations},set:function(t){this._fadeInDurations=t}}]),r}(Mf),s(Cf.prototype,"poses",[mf],Object.getOwnPropertyDescriptor(Cf.prototype,"poses"),Cf.prototype),s(Cf.prototype,"fadeInDurations",[Ef],Object.getOwnPropertyDescriptor(Cf.prototype,"fadeInDurations"),Cf.prototype),bf=s(Cf.prototype,"choice",[I,Tf],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 0}}),Af=Cf))||Af)||Af),function(t){t[t.VALUE=0]="VALUE",t[t.AUXILIARY_CURVE=1]="AUXILIARY_CURVE"}(Rf||(Rf={})),y(Rf);var Uf,Ff,Vf,Gf,Wf,zf,jf=(Sf=P(K+"IntensitySpecification"),wf=W(Rf),Sf((Of=function(){function t(){this.type=Nf&&Nf(),this.value=If&&If(),this.auxiliaryCurveName=xf&&xf(),this._handle=void 0}var e=t.prototype;return e.bind=function(t){if(this.type===Rf.AUXILIARY_CURVE&&this.auxiliaryCurveName){var e=t.bindAuxiliaryCurve(this.auxiliaryCurveName);this._handle=e}},e.evaluate=function(t){return this.type===Rf.AUXILIARY_CURVE&&this._handle?t.auxiliaryCurves[this._handle.index]:this.value},t}(),Nf=O(Of.prototype,"type",[wf,I],(function(){return Rf.VALUE})),If=O(Of.prototype,"value",[I],(function(){return 1})),xf=O(Of.prototype,"auxiliaryCurveName",[I],(function(){return""})),Pf=Of))||Pf),Hf=function(){this.transformIndex=-1,this.transform=new mr},Qf=function(){function t(){this._pool=new C((function(){return new Hf}),3),this._array=new S(3),this._debugLastTransformIndex=-1}var e=t.prototype;return e.push=function(t,e){var n=this._pool.alloc();n.transformIndex=t,mr.copy(n.transform,e),this._array.push(n)},e.clear=function(){for(var t=this._array.length,e=0;e<t;++e){var n=this._array.get(e);this._pool.free(n)}this._array.clear()},n(t,[{key:"length",get:function(){return this._array.length}},{key:"array",get:function(){return this._array.array}}]),t}(),Yf=function(){function t(t){this._transformFlags=[],this._transformFlags=new Array(t)}var e=t.prototype;return e.clear=function(){this._transformFlags.fill(!1)},e.test=function(t){return this._transformFlags[t]},e.set=function(t){this._transformFlags[t]=!0},e.unset=function(t){this._transformFlags[t]=!1},t}(),qf=new mr,Zf=new mr;function Xf(t,e,n,r){var i=n.length;if(0!==i)if(e._poseTransformSpace!==br.LOCAL){c(e._poseTransformSpace===br.COMPONENT),r.clear();for(var o=n.array[0].transformIndex,a=o,s=0;s<i;++s){var u=n.array[s].transformIndex;r.set(u),a=u}for(var l=o;l<e.transforms.length;++l){var h=t.parentTable[l];h<0||r.test(h)&&(r.set(l),a=l)}for(var f=a;f>=o;--f)if(r.test(f)){var p=t.parentTable[f];if(p>=0){var d=e.transforms.getTransform(f,qf),v=e.transforms.getTransform(p,Zf);mr.calculateRelative(d,d,v),e.transforms.setTransform(f,d)}}for(var _=0;_<i;++_){var y=n.array[_],g=y.transformIndex,m=y.transform;e.transforms.setTransform(g,m),r.unset(g)}for(var E=o;E<=a;++E)if(r.test(E)){var T=t.parentTable[E],A=e.transforms.getTransform(E,qf),C=e.transforms.getTransform(T,Zf);mr.multiply(A,C,A),e.transforms.setTransform(E,A)}}else for(var b=0;b<i;++b){var S=n.array[b],w=S.transformIndex,P=S.transform;e.transforms.setTransform(w,P)}}var Jf,Kf,$f,tp,ep,np,rp,ip,op,ap,sp,up,lp,cp,hp,fp,pp,dp,vp,_p=(Uf=P(K+"PoseNodeModifyPoseBase"),Ff=ir(),Vf=ei({type:qn.POSE}),Uf(Gf=Ff((Wf=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return e=t.call.apply(t,[this].concat(r))||this,l(e,"pose",zf,m(e)),e._modificationQueue=new Qf,e._spaceFlagTable=new Yf(0),e}e(n,t);var r=n.prototype;return r.settle=function(t){var e;null==(e=this.pose)||e.settle(t),this._spaceFlagTable=new Yf(t.transformCount)},r.reenter=function(){var t;null==(t=this.pose)||t.reenter()},r.bind=function(t){var e;null==(e=this.pose)||e.bind(t)},r.doUpdate=function(t){var e;null==(e=this.pose)||e.update(t)},r.doEvaluate=function(t){var e,n,r=this.getPoseTransformSpaceRequirement(),i=null!==(e=null==(n=this.pose)?void 0:n.evaluate(t,r))&&void 0!==e?e:Kr.evaluateDefaultPose(t,r),o=this._modificationQueue;return c(0===o.length),this.modifyPose(t,i,o),Xf(t,i,o,this._spaceFlagTable),o.clear(),i},n}(Kr),zf=s(Wf.prototype,"pose",[I,Vf],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Gf=Wf))||Gf)||Gf);!function(t){t[t.LEAVE_UNCHANGED=0]="LEAVE_UNCHANGED",t[t.REPLACE=1]="REPLACE",t[t.ADD=2]="ADD"}(vp||(vp={})),y(vp);var yp=1e-5,gp=new mr;Jf=P(K+"PoseNodeApplyTransform"),Kf=nr(hu),$f=or({themeColor:"#72A869"}),tp=W(vp),ep=ei({type:qn.VEC3}),np=W(vp),rp=ei({type:qn.QUAT}),ip=W(Al),op=ei({type:qn.FLOAT}),Jf(ap=Kf(ap=$f((sp=function(t){function r(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).node=up&&up(),e.positionOperation=lp&&lp(),l(e,"position",cp,m(e)),e.rotationOperation=hp&&hp(),l(e,"rotation",fp,m(e)),e.intensity=pp&&pp(),e.transformSpace=dp&&dp(),e._transformHandle=null,e}e(r,t);var i=r.prototype;return i.bind=function(e){var n=this.node;if(t.prototype.bind.call(this,e),n){var r=e.bindTransformByName(n);r?(this._transformHandle=r,this.intensity.bind(e)):d("Failed to bind transform "+n)}},i.getPoseTransformSpaceRequirement=function(){return Wr.NO},i.modifyPose=function(t,e,n){var r=this._transformHandle;if(!r)return e;var i=this.intensity.evaluate(e);if(i<yp)return e;var o=j(i,1,yp),a=r.index,s=e.transforms.getTransform(a,gp),u=this.rotationOperation;if(u!==vp.LEAVE_UNCHANGED){var l=this.rotation,c=this.transformSpace;switch(t._convertPoseSpaceTransformToTargetSpace(s,c,e,a),u){default:case vp.REPLACE:Mp(s,l,i,o);break;case vp.ADD:Dp(s,l,i,o)}t._convertTransformToPoseTransformSpace(s,c,e,a)}var h=this.positionOperation;if(h!==vp.LEAVE_UNCHANGED){var f=this.position,p=this.transformSpace;switch(t._convertPoseSpaceTransformToTargetSpace(s,p,e,a),h){default:case vp.REPLACE:Rp(s,f,i,o);break;case vp.ADD:kp(s,f,i,o)}t._convertTransformToPoseTransformSpace(s,p,e,a)}return n.push(a,s),e},n(r,[{key:"intensityValue",get:function(){return this.intensity.value},set:function(t){this.intensity.value=t}}]),r}(_p),up=O(sp.prototype,"node",[I],(function(){return""})),lp=O(sp.prototype,"positionOperation",[I,tp],(function(){return vp.LEAVE_UNCHANGED})),cp=s(sp.prototype,"position",[I,ep],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return new k}}),hp=O(sp.prototype,"rotationOperation",[I,np],(function(){return vp.LEAVE_UNCHANGED})),fp=s(sp.prototype,"rotation",[I,rp],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return new L}}),pp=O(sp.prototype,"intensity",[I],(function(){return new jf})),dp=O(sp.prototype,"transformSpace",[I,ip],(function(){return Al.WORLD})),s(sp.prototype,"intensityValue",[op],Object.getOwnPropertyDescriptor(sp.prototype,"intensityValue"),sp.prototype),ap=sp))||ap)||ap);var mp,Ep,Tp,Ap,Cp,bp,Sp,wp,Pp,Op,Np,Ip,xp=(mp=new k,Ep=new k,{replace:function(t,e,n,r){if(r)t.position=e;else{var i=k.copy(mp,t.position);k.lerp(i,i,e,n),t.position=i}},add:function(t,e,n,r){var i=Ep;r?k.copy(i,e):k.slerp(i,k.ZERO,e,n),k.add(i,t.position,i),t.position=i}}),Rp=xp.replace,kp=xp.add,Lp=function(){var t=new L,e=new L;return{replace:function(e,n,r,i){if(i)e.rotation=n;else{var o=L.copy(t,e.rotation);L.slerp(o,o,n,r),e.rotation=o}},add:function(n,r,i,o){var a=L.copy(t,n.rotation),s=e;o?L.copy(s,r):L.slerp(s,L.IDENTITY,r,i),L.multiply(s,s,a),n.rotation=s}}}(),Mp=Lp.replace,Dp=Lp.add,Bp=new mr;!function(t){t[t.LOCAL=0]="LOCAL",t[t.COMPONENT=1]="COMPONENT"}(Ip||(Ip={})),y(Ip),Tp=P(K+"PoseNodeCopyTransform"),Ap=nr(hu),Cp=or({themeColor:"#72A869"}),bp=W(Ip),Tp(Sp=Ap(Sp=Cp((wp=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).sourceNodeName=Pp&&Pp(),e.targetNodeName=Op&&Op(),e.space=Np&&Np(),e._workspace=void 0,e}e(n,t);var r=n.prototype;return r.bind=function(e){t.prototype.bind.call(this,e);var n=e.bindTransformByName(this.sourceNodeName),r=e.bindTransformByName(this.targetNodeName);if(!n||!r)return null==n||n.destroy(),void(null==r||r.destroy());this._workspace=new qp(n,r)},r.modifyPose=function(t,e){var n=this._workspace;if(n){var r=n.hSource.index,i=n.hTarget.index,o=e.transforms.getTransform(r,Bp);e.transforms.setTransform(i,o)}},r.getPoseTransformSpaceRequirement=function(){return this.space===Ip.COMPONENT?Wr.COMPONENT:Wr.LOCAL},n}(_p),Pp=O(wp.prototype,"sourceNodeName",[I],(function(){return""})),Op=O(wp.prototype,"targetNodeName",[I],(function(){return""})),Np=O(wp.prototype,"space",[I,bp],(function(){return Ip.COMPONENT})),Sp=wp))||Sp)||Sp);var Up,Fp,Vp,Gp,Wp,zp,jp,Hp,Qp,Yp,qp=function(t,e){this.hSource=t,this.hTarget=e};!function(t){t[t.LEAVE_UNCHANGED=0]="LEAVE_UNCHANGED",t[t.REPLACE=1]="REPLACE",t[t.ADD=2]="ADD"}(Yp||(Yp={})),y(Yp),Up=P(K+"PoseNodeSetAuxiliaryCurve"),Fp=nr(hu),Vp=ei({type:qn.FLOAT}),Gp=W(Yp),Up(Wp=Fp((zp=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).curveName=jp&&jp(),l(e,"curveValue",Hp,m(e)),e.flag=Qp&&Qp(),e._handle=void 0,e}e(n,t);var r=n.prototype;return r.bind=function(e){t.prototype.bind.call(this,e),this.curveName&&(this._handle=e.bindAuxiliaryCurve(this.curveName))},r.getPoseTransformSpaceRequirement=function(){return Wr.NO},r.modifyPose=function(t,e){var n=this._handle;if(n)switch(this.flag){case Yp.REPLACE:e.auxiliaryCurves[n.index]=this.curveValue;break;case Yp.ADD:e.auxiliaryCurves[n.index]+=this.curveValue;case Yp.LEAVE_UNCHANGED:}},n}(_p),jp=O(zp.prototype,"curveName",[I],(function(){return""})),Hp=s(zp.prototype,"curveValue",[I,Vp],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 0}}),Qp=O(zp.prototype,"flag",[I,Gp],(function(){return Yp.REPLACE})),Wp=zp))||Wp);var Zp,Xp,Jp,Kp,$p,td,ed,nd,rd,id,od,ad,sd,ud,ld,cd,hd,fd,pd,dd,vd,_d,yd,gd=function(){var t,e,n=new L,r=new k,i=new k,o=new k,a=(t=new k,e=new k,function(n,r,i,o,a){return L.rotationTo(n,k.subtract(t,i,r).normalize(),k.subtract(e,a,o).normalize())});return function(t,e,s,u,l){var c=k.copy(r,null!=l?l:e.position),h=t.position,f=e.position,p=s.position;s.rotation;var d=i,v=o;md(h,f,p,u,c,d,v);var _=a(n,h,f,h,d);L.multiply(_,_,t.rotation),t.rotation=_;var y=a(n,f,p,d,v);L.multiply(y,y,e.rotation),e.rotation=y,e.position=d,s.position=v}}(),md=(Zp=new k,Xp=new k,Jp=new k,function(t,e,n,r,i,o,a){var s=k.distance(t,e),u=k.distance(e,n),l=k.distance(t,r),c=k.subtract(Zp,r,t);c.normalize();var h=s+u;if(l>=h)return k.scaleAndAdd(o,t,c,s),void k.scaleAndAdd(a,t,c,h);k.copy(a,r);var f=V((s*s+l*l-u*u)/(2*s*l),-1,1),p=k.subtract(Xp,i,t),d=k.projectOnPlane(Jp,p,c);d.normalize();var v=s*f,_=s*s-v*v,y=Math.sqrt(_);k.scaleAndAdd(o,t,c,v),k.scaleAndAdd(o,o,d,y)}),Ed=hu+"/i18n:ENGINE.animation_graph.pose_graph_node_sub_categories.pose_nodes_ik/",Td=new mr,Ad=new mr,Cd=new mr,bd=new k,Sd=new k,wd=new mr;!function(t){t[t.NONE=0]="NONE",t[t.VALUE=1]="VALUE",t[t.BONE=2]="BONE"}(yd||(yd={})),y(yd);var Pd=(Kp=P(K+"PoseNodeTwoBoneIKSolver.TargetSpecification"),$p=W(yd),td=W(Al),Kp((nd=function(){function t(t){this.type=rd&&rd(),this.targetPosition=id&&id(),this.targetPositionSpace=od&&od(),this.targetBone=ad&&ad(),this._sourceBoneHandle=void 0,this._targetBoneHandle=void 0,void 0!==t&&(this.type=t)}var e=t.prototype;return e.bind=function(t,e){var n;this._sourceBoneHandle=e,this.type===yd.BONE&&this.targetBone&&(this._targetBoneHandle=null!==(n=t.bindTransformByName(this.targetBone))&&void 0!==n?n:void 0)},e.evaluate=function(t,e,n){if(c(this._sourceBoneHandle),this._targetBoneHandle)e.transforms.getPosition(this._targetBoneHandle.index,t);else if(this.type===yd.NONE)e.transforms.getPosition(this._sourceBoneHandle.index,t);else{var r=mr.setIdentity(wd);r.position=this.targetPosition,n._convertTransformToPoseTransformSpace(r,this.targetPositionSpace,e,this._sourceBoneHandle.index),k.copy(t,r.position)}return t},t}(),rd=O(nd.prototype,"type",[I,$p],(function(){return yd.VALUE})),id=O(nd.prototype,"targetPosition",[I],(function(){return new k})),od=O(nd.prototype,"targetPositionSpace",[I,td],(function(){return Al.WORLD})),ad=O(nd.prototype,"targetBone",[I],(function(){return""})),ed=nd))||ed);sd=P(K+"PoseNodeTwoBoneIKSolver"),ud=nr(Ed),ld=ei({type:qn.VEC3}),cd=ei({type:qn.VEC3}),sd(hd=ud((fd=function(t){function r(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).debug=pd&&pd(),e.endEffectorBoneName=dd&&dd(),e.endEffectorTarget=vd&&vd(),e.poleTarget=_d&&_d(),e._workspace=void 0,e}e(r,t);var i=r.prototype;return i.bind=function(e){if(t.prototype.bind.call(this,e),this.endEffectorBoneName){var n=e.getParentBoneNameByName(this.endEffectorBoneName),r=n?e.getParentBoneNameByName(n):"";if(n&&r){var i=e.bindTransformByName(this.endEffectorBoneName),o=e.bindTransformByName(n),a=e.bindTransformByName(r);i&&o&&a?(this.endEffectorTarget.bind(e,i),this.poleTarget.bind(e,o),this._workspace=new Bd(i,o,a)):(null==i||i.destroy(),null==o||o.destroy(),null==a||a.destroy())}}},i.getPoseTransformSpaceRequirement=function(){return Wr.COMPONENT},i.modifyPose=function(t,e,n){var r=this._workspace;if(r){var i=r.hRoot.index,o=r.hMiddle.index,a=r.hEndEffector.index,s=e.transforms.getTransform(i,Td),u=e.transforms.getTransform(o,Ad),l=e.transforms.getTransform(a,Cd),c=this.endEffectorTarget.evaluate(bd,e,t),h=this.poleTarget.evaluate(Sd,e,t);gd(s,u,l,c,h,this.debug?this:void 0),n.push(i,s),n.push(o,u),n.push(a,l)}},n(r,[{key:"endEffectorTargetPosition",get:function(){return this.endEffectorTarget.targetPosition},set:function(t){k.copy(this.endEffectorTarget.targetPosition,t)}},{key:"poleTargetPosition",get:function(){return this.poleTarget.targetPosition},set:function(t){k.copy(this.poleTarget.targetPosition,t)}}]),r}(_p),pd=O(fd.prototype,"debug",[I],(function(){return!1})),dd=O(fd.prototype,"endEffectorBoneName",[I],(function(){return""})),vd=O(fd.prototype,"endEffectorTarget",[I],(function(){return new Pd(yd.VALUE)})),s(fd.prototype,"endEffectorTargetPosition",[ld],Object.getOwnPropertyDescriptor(fd.prototype,"endEffectorTargetPosition"),fd.prototype),_d=O(fd.prototype,"poleTarget",[I],(function(){return new Pd(yd.NONE)})),s(fd.prototype,"poleTargetPosition",[cd],Object.getOwnPropertyDescriptor(fd.prototype,"poleTargetPosition"),fd.prototype),hd=fd))||hd);var Od,Nd,Id,xd,Rd,kd,Ld,Md,Dd,Bd=function(t,e,n){this.hEndEffector=t,this.hMiddle=e,this.hRoot=n},Ud={listEntries:function(t){for(var e,n=[],r=u(t.animationGraph.variables);!(e=r()).done;){var i=e.value,o=i[0],a=i[1].type;if(a!==we.TRIGGER){var s=void 0;switch(a){default:break;case we.FLOAT:s=qn.FLOAT;break;case we.INTEGER:s=qn.INTEGER;break;case we.BOOLEAN:s=qn.BOOLEAN;break;case we.VEC3_experimental:s=qn.VEC3;break;case we.QUAT_experimental:s=qn.QUAT}void 0!==s&&n.push({arg:{name:o,type:s},menu:o})}}return n},create:function(t){var e;switch(t.type){default:throw new Error("Bad create node arg: "+qn[t.type]);case qn.FLOAT:e=new Vd;break;case qn.INTEGER:e=new Gd;break;case qn.BOOLEAN:e=new Wd;break;case qn.VEC3:e=new zd;break;case qn.QUAT:e=new jd}return e.variableName=t.name,e}},Fd=P(K+"PVNodeGetVariableBase")(Od=rr(Ud)((Nd=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).variableName=Id&&Id(),e._varInstance=void 0,e}return e(n,t),n.prototype.link=function(t){this._varInstance=t.getVar(this.variableName)},n}(ti),Id=O(Nd.prototype,"variableName",[I],(function(){return""})),Od=Nd))||Od)||Od,Vd=P(K+"PVNodeGetVariableFloat")(xd=ir()(xd=or({inline:!0,themeColor:"#8471CF"})(xd=function(t){function n(){return t.call(this,qn.FLOAT)||this}return e(n,t),n.prototype.selfEvaluateDefaultOutput=function(){var t;return null==(t=this._varInstance)?void 0:t.value},n}(Fd))||xd)||xd)||xd,Gd=P(K+"PVNodeGetVariableInteger")(Rd=ir()(Rd=or({inline:!0,themeColor:"#2A90DC"})(Rd=function(t){function n(){return t.call(this,qn.INTEGER)||this}return e(n,t),n.prototype.selfEvaluateDefaultOutput=function(){var t;return null==(t=this._varInstance)?void 0:t.value},n}(Fd))||Rd)||Rd)||Rd,Wd=P(K+"PVNodeGetVariableBoolean")(kd=ir()(kd=or({inline:!0,themeColor:"#D07979"})(kd=function(t){function n(){return t.call(this,qn.BOOLEAN)||this}return e(n,t),n.prototype.selfEvaluateDefaultOutput=function(){var t;return null==(t=this._varInstance)?void 0:t.value},n}(Fd))||kd)||kd)||kd,zd=P(K+"PVNodeGetVariableVec3")(Ld=ir()(Ld=or({inline:!0,themeColor:"#D97721"})(Ld=function(t){function n(){return t.call(this,qn.VEC3)||this}return e(n,t),n.prototype.selfEvaluateDefaultOutput=function(){var t;return null==(t=this._varInstance)?void 0:t.value},n}(Fd))||Ld)||Ld)||Ld,jd=P(K+"PVNodeGetVariableQuat")(Md=ir()(Md=or({inline:!0,themeColor:"#B169C4"})(Md=function(t){function n(){return t.call(this,qn.QUAT)||this}return e(n,t),n.prototype.selfEvaluateDefaultOutput=function(){var t;return null==(t=this._varInstance)?void 0:t.value},n}(Fd))||Md)||Md)||Md,Hd=function(){function t(){this._groups=[]}var e=t.prototype;return e.register=function(t){var e=t.group,n=this._groups.find((function(t){return t.name===e}));return n||(n=new Qd(e),this._groups.push(n)),n.addMember()},e.sync=function(){for(var t,e=u(this._groups);!(t=e()).done;)t.value.sync()},t}(),Qd=function(){function t(t){this._lastLeader=void 0,this._records=[],this.name=t}var e=t.prototype;return e.addMember=function(){var t=new Yd;return this._records.push(t),t},e.sync=function(){var t=this._records,e=t.length,n=this._lastLeader;if(this._lastLeader=void 0,!t.every((function(t){return!t.active}))){t.sort((function(t,e){var n=t.active?t.weight:-1;return(e.active?e.weight:-1)-n}));var r=0,i=t[0].weight;if(t[r]!==n)for(var o=0;o<e;++o){var a=t[o];if(!a.active||!j(a.weight,i,1e-6))break;if(a===n){r=o;break}}c(t[r].active),this._lastLeader=t[r];for(var s=t[r].normalizedTime,u=0;u<e;++u){var l=t[u];if(!l.active)break;l.normalizedTime=s,l.reset()}}},t}(),Yd=function(){function t(){this.normalizedTime=0,this.weight=0,this.active=!1}var e=t.prototype;return e.notifyRenter=function(t){this.reset(),this.normalizedTime=t},e.notifyUpdate=function(t,e){this.normalizedTime+=t,this.active?this.weight+=e:(this.active=!0,this.weight=e)},e.reset=function(){this.active=!1,this.weight=0},e.getSyncedEnterTime=function(){return this.normalizedTime},t}();!function(t){t[t.UNINITIALIZED=0]="UNINITIALIZED",t[t.UNSETTLED=1]="UNSETTLED",t[t.SETTLED=2]="SETTLED",t[t.UP_TO_DATE=3]="UP_TO_DATE",t[t.OUTDATED=4]="OUTDATED",t[t.UPDATING=5]="UPDATING",t[t.UPDATED=6]="UPDATED",t[t.EVALUATING=7]="EVALUATING",t[t.EVALUATED=8]="EVALUATED"}(Dd||(Dd={}));var qd,Zd,Xd,Jd,Kd,$d,tv=function(){function t(t){this._state=Dd.UNINITIALIZED,this._instantiatedPoseGraph=void 0,this._maxRequestedUpdateTime=0,this._evaluationCache=null,this._updateContextGenerator=new _c,this._allocator=t}var e=t.prototype;return e.set=function(t,e){c(this._state===Dd.UNINITIALIZED);var n=Sc(t.graph,e);n.bind(e),this._instantiatedPoseGraph=n,this._state=Dd.UNSETTLED},e.settle=function(t){c(this._state===Dd.UNSETTLED||this._state===Dd.SETTLED),c(this._instantiatedPoseGraph),this._instantiatedPoseGraph.settle(t),this._state=Dd.SETTLED},e.reset=function(){switch(this._state){case Dd.SETTLED:case Dd.OUTDATED:break;case Dd.UP_TO_DATE:this._state=Dd.OUTDATED;break;case Dd.UPDATED:case Dd.EVALUATED:this._evaluationCache&&(this._allocator.destroyPose(this._evaluationCache),this._evaluationCache=null),this._maxRequestedUpdateTime=0,this._state=Dd.UP_TO_DATE;case Dd.UNINITIALIZED:}},e.reenter=function(){switch(this._state){default:c(!1,"Unexpected stash state "+this._state+" when reenter().");break;case Dd.UP_TO_DATE:case Dd.UPDATED:break;case Dd.SETTLED:case Dd.OUTDATED:this._state=Dd.UP_TO_DATE,c(this._instantiatedPoseGraph),this._instantiatedPoseGraph.reenter()}},e.requestUpdate=function(t){var e=t.deltaTime;if(c(this._state===Dd.OUTDATED||this._state===Dd.UP_TO_DATE||this._state===Dd.UPDATING||this._state===Dd.UPDATED),c(this._instantiatedPoseGraph),this._state!==Dd.UPDATING){var n=Math.max(0,e-this._maxRequestedUpdateTime);if(this._state!==Dd.UPDATED||!j(n,0,1e-8)){this._state=Dd.UPDATING,this._maxRequestedUpdateTime=Math.max(e,this._maxRequestedUpdateTime);var r=this._updateContextGenerator.generate(n,t.indicativeWeight);this._instantiatedPoseGraph.update(r),this._state=Dd.UPDATED}}},e.evaluate=function(t){switch(this._state){default:c(!1,"Unexpected stash state "+this._state+" when evaluate().");break;case Dd.EVALUATING:this._state=Dd.EVALUATED;break;case Dd.EVALUATED:break;case Dd.UPDATED:var e;c(!this._evaluationCache),this._state=Dd.EVALUATING;var n=null==(e=this._instantiatedPoseGraph)?void 0:e.evaluate(t);if(this._state=Dd.EVALUATED,n){var r=this._allocator.allocatePose();r.transforms.set(n.transforms),r.auxiliaryCurves.set(n.auxiliaryCurves),this._evaluationCache=r,t.popPose()}this._state=Dd.EVALUATED}return c(this._state===Dd.EVALUATED),c(this._instantiatedPoseGraph),this._evaluationCache?t.pushDuplicatedPose(this._evaluationCache):null},t}(),ev=function(){function t(t){this._allocator=void 0,this._stashEvaluations={},this._allocator=t}var e=t.prototype;return e.bindStash=function(t){return this._stashEvaluations[t]},e.getStash=function(t){return this._stashEvaluations[t]},e.addStash=function(t){this._stashEvaluations[t]=new tv(this._allocator)},e.setStash=function(t,e,n){c(t in this._stashEvaluations),this._stashEvaluations[t].set(e,n)},e.reset=function(){for(var t in this._stashEvaluations)this._stashEvaluations[t].reset()},e.settle=function(t){for(var e in this._stashEvaluations)this._stashEvaluations[e].settle(t)},t}(),nv=function(t){function r(e,n,r){var i;(i=t.call(this)||this)._layerRecords=void 0;var o=e.layers.map((function(t){return new rv(t,n,r)}));return i._layerRecords=o,i}e(r,t);var i=r.prototype;return i.reenter=function(){},i.bind=function(){},i.settle=function(t){for(var e=this._layerRecords,n=e.length,r=0;r<n;++r)e[r].settle(t)},i.getLayerWeight=function(t){return c(t>=0&&t<this._layerRecords.length),this._layerRecords[t].weight},i.setLayerWeight=function(t,e){c(t>=0&&t<this._layerRecords.length),this._layerRecords[t].weight=e},i.getLayerTopLevelStateMachineEvaluation=function(t){return this._layerRecords[t].stateMachineEvaluation},i.overrideClips=function(t){for(var e=this._layerRecords,n=e.length,r=0;r<n;++r){var i=e[r];t._pushAdditiveFlag(i.additive),i.stateMachineEvaluation.overrideClips(t),t._popAdditiveFlag()}},i.doUpdate=function(t){for(var e=this._layerRecords,n=e.length,r=0;r<n;++r)e[r].update(t)},i.doEvaluate=function(t){for(var e=t.pushDefaultedPose(),n=this._layerRecords,r=n.length,i=0;i<r;++i){var o=n[i],a=o.stateMachineEvaluation.evaluate(t),s=o.weight*o.stateMachineEvaluation.passthroughWeight,u=o.transformFilter;o.additive?Vr(e,a,s,u):Ir(e,a,s,u),t.popPose(),o.postEvaluate()}return e},n(r,[{key:"layerCount",get:function(){return this._layerRecords.length}}]),r}(Kr),rv=function(){function t(t,e,n){var r;this.additive=!1,this.weight=0,this._topLevelStateMachineEval=void 0,this._stashManager=void 0,this._motionSyncManager=void 0,this._mask=void 0,this.transformFilter=void 0;for(var i,o=new ev(n),a=u(t.stashes());!(i=a()).done;){var s=i.value,l=s[0];s[1],o.addStash(l)}this._stashManager=o;var c=new Hd;this._motionSyncManager=c,e._setLayerWideContextProperties(o,c);for(var h,f=u(t.stashes());!(h=f()).done;){var p=h.value,d=p[0],v=p[1];o.setStash(d,v,e)}this.weight=t.weight;var _=this.additive=t.additive;this._mask=null!==(r=t.mask)&&void 0!==r?r:void 0,e._pushAdditiveFlag(_),this._topLevelStateMachineEval=new Lc(t.stateMachine,t.name,e),e._popAdditiveFlag(),e._unsetLayerWideContextProperties()}var e=t.prototype;return e.settle=function(t){this._mask&&(this.transformFilter=t.createTransformFilter(this._mask)),this._stashManager.settle(t),this._topLevelStateMachineEval.settle(t)},e.update=function(t){this.stateMachineEvaluation.update(t),this._motionSyncManager.sync()},e.postEvaluate=function(){this._stashManager.reset()},n(t,[{key:"stateMachineEvaluation",get:function(){return this._topLevelStateMachineEval}}]),t}(),iv=function(){function t(t,e,n,r){this._currentTransitionCache={duration:0,time:0},this._rootPoseNode=void 0,this._varInstances={},this._hasAutoTrigger=!1,this._auxiliaryCurveRegistry=new zl,this._poseLayoutMaintainer=void 0,this._bindingContext=void 0,this._settleContext=void 0,this._rootUpdateContextGenerator=new _c;for(var i,o=u(t.variables);!(i=o()).done;){var a=i.value,s=a[0],l=a[1][Ve]();this._varInstances[s]=l,l instanceof Je&&l.resetMode===Fe.NEXT_FRAME_OR_AFTER_CONSUMED&&(this._hasAutoTrigger=!0)}var c=new jl(e,this._auxiliaryCurveRegistry);this._poseLayoutMaintainer=c;var h=new Gl(e,c,this._varInstances,n);h._setClipOverrides(null!=r?r:void 0),this._bindingContext=h;var f=new cc(c);this._settleContext=f,c.startBind();var p=new yc;this._poseStashAllocator=p,this._rootPoseNode=new nv(t,h,p),this._root=e,this._initializeContexts()}var e=t.prototype;return e.destroy=function(){this._evaluationContext.destroy()},e._destroyAfterException_debugging=function(){var t=this._evaluationContext._stackSize_debugging;if(0!==t)for(var e=0;e<t;++e)this._evaluationContext.popPose();this._evaluationContext.destroy()},e.update=function(t){var e=this._evaluationContext,n=this._poseLayoutMaintainer,r=this._rootUpdateContextGenerator,i=this._rootPoseNode,o=r.generate(t,1);i.update(o);var a=i.evaluate(e,Wr.LOCAL);if(this._hasAutoTrigger){var s=this._varInstances;for(var u in s){var l=s[u];l instanceof Je&&l.resetMode===Fe.NEXT_FRAME_OR_AFTER_CONSUMED&&(l.value=!1)}}n.apply(a),e.popPose()},e.getVariables=function(){return Object.entries(this._varInstances)},e.getCurrentStateStatus=function(t){return this._rootPoseNode.getLayerTopLevelStateMachineEvaluation(t).getCurrentStateStatus()},e.getCurrentClipStatuses=function(t){return this._rootPoseNode.getLayerTopLevelStateMachineEvaluation(t).getCurrentClipStatuses()},e.getCurrentTransition=function(t){var e=this._currentTransitionCache;return this._rootPoseNode.getLayerTopLevelStateMachineEvaluation(t).getCurrentTransition(e)?e:null},e.getNextStateStatus=function(t){return this._rootPoseNode.getLayerTopLevelStateMachineEvaluation(t).getNextStateStatus()},e.getNextClipStatuses=function(t){return this._rootPoseNode.getLayerTopLevelStateMachineEvaluation(t).getNextClipStatuses()},e.getValue=function(t){var e=this._varInstances[t];return e?e.value:void 0},e.setValue=function(t,e){var n=this._varInstances[t];n&&(n.value=e)},e.getLayerWeight=function(t){return this._rootPoseNode.getLayerWeight(t)},e.setLayerWeight=function(t,e){this._rootPoseNode.setLayerWeight(t,e)},e.overrideClips=function(t){this._poseLayoutMaintainer.startBind(),this._bindingContext._setClipOverrides(t),this._rootPoseNode.overrideClips(this._bindingContext),this._updateAfterPossiblePoseLayoutChange()},e.getAuxiliaryCurveValue=function(t){return this._auxiliaryCurveRegistry.get(t)},e._initializeContexts=function(){var t=this._poseLayoutMaintainer;t.endBind(),this._createOrUpdateTransformFilters();var e=t.createEvaluationContext();this._evaluationContext=e,t.fetchDefaultTransforms(e[lc]),t.resetPoseStashAllocator(this._poseStashAllocator)},e._updateAfterPossiblePoseLayoutChange=function(){var t=this._poseLayoutMaintainer,e=t.endBind();if(0!==e){(e&Vl.TRANSFORM_COUNT||e&Vl.TRANSFORM_ORDER)&&this._createOrUpdateTransformFilters();var n=!1;if(e&Vl.TRANSFORM_COUNT||e&Vl.AUXILIARY_CURVE_COUNT){var r=t.createEvaluationContext();this._evaluationContext.destroy(),this._evaluationContext=r,n=!0,t.resetPoseStashAllocator(this._poseStashAllocator)}(n||e&Vl.TRANSFORM_COUNT||e&Vl.TRANSFORM_ORDER)&&t.fetchDefaultTransforms(this._evaluationContext[lc])}},e._createOrUpdateTransformFilters=function(){this._rootPoseNode.settle(this._settleContext)},n(t,[{key:"layerCount",get:function(){return this._rootPoseNode.layerCount}}]),t}(),ov=W,av=I,sv=q,uv=(qd=P("cc.animation.AnimationController"),Zd=ov(Mn),Xd=sv("graph"),qd((Kd=function(t){function r(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this)._graph=$d&&$d(),e._graphEval=null,e}e(r,t);var i=r.prototype;return i.__preload=function(){var t=this.graph;if(t){var e,n=null;if(t instanceof lu){if(!t.original)return;e=t.original,n=t.clipOverrides}else e=t;var r=new iv(e,this.node,this,n);this._graphEval=r}},i.onDestroy=function(){var t;null==(t=this._graphEval)||t.destroy()},i.update=function(t){var e;null==(e=this._graphEval)||e.update(t)},i.getVariables=function(){return this._graphEval.getVariables()},i.setValue=function(t,e){return this.setValue_experimental(t,e)},i.setValue_experimental=function(t,e){this._graphEval.setValue(t,e)},i.getValue=function(t){var e=this.getValue_experimental(t);return"object"==typeof e?void 0:e},i.getValue_experimental=function(t){return this._graphEval.getValue(t)},i.getCurrentStateStatus=function(t){return this._graphEval.getCurrentStateStatus(t)},i.getCurrentClipStatuses=function(t){return this._graphEval.getCurrentClipStatuses(t)},i.getCurrentTransition=function(t){return this._graphEval.getCurrentTransition(t)},i.getNextStateStatus=function(t){return this._graphEval.getNextStateStatus(t)},i.getNextClipStatuses=function(t){return this._graphEval.getNextClipStatuses(t)},i.getLayerWeight=function(t){return this._graphEval.getLayerWeight(t)},i.setLayerWeight=function(t,e){return this._graphEval.setLayerWeight(t,e)},i.overrideClips_experimental=function(t){this._graphEval.overrideClips(t)},i.getAuxiliaryCurveValue_experimental=function(t){var e=this._graphEval;return e?e.getAuxiliaryCurveValue(t):0},n(r,[{key:"graph",get:function(){return this._graph},set:function(t){this._graph=t}},{key:"layerCount",get:function(){var t,e;return null!==(t=null==(e=this._graphEval)?void 0:e.layerCount)&&void 0!==t?t:0}}]),r}(Bt),s(Kd.prototype,"graph",[Zd],Object.getOwnPropertyDescriptor(Kd.prototype,"graph"),Kd.prototype),$d=O(Kd.prototype,"_graph",[av,Xd],(function(){return null})),Jd=Kd))||Jd);t("animation",Object.freeze({__proto__:null,AnimationController:uv,ColorTrack:ft,ComponentPath:pt,CubicSplineNumberValue:dt,CubicSplineQuatValue:vt,CubicSplineVec2Value:_t,CubicSplineVec3Value:yt,CubicSplineVec4Value:gt,HierarchyPath:mt,MorphWeightValueProxy:Pe,MorphWeightsAllValueProxy:Ne,MorphWeightsValueProxy:Oe,ObjectTrack:Et,QuatTrack:Tt,RealTrack:At,SizeTrack:Ct,StateMachineComponent:ol,Track:nt,TrackPath:bt,UniformProxyFactory:be,get VariableType(){return we},VectorTrack:St,isCustomPath:wt,isPropertyPath:Pt}));var lv,cv=function(){function t(){this._nodeBlendStates=new Map}var e=t.prototype;return e.createWriter=function(t,e,n,r){var i=this.ref(t,e);return new hv(t,e,i,n,r)},e.destroyWriter=function(t){var e=t;this.deRef(e.node,e.property)},e.ref=function(t,e){var n=this._nodeBlendStates.get(t);return n||(n=this.createNodeBlendState(),this._nodeBlendStates.set(t,n)),n.refProperty(t,e)},e.deRef=function(t,e){var n=this._nodeBlendStates.get(t);n&&(n.deRefProperty(e),n.empty&&this._nodeBlendStates.delete(t))},e.apply=function(){this._nodeBlendStates.forEach((function(t,e){t.apply(e)}))},t}(),hv=function(){function t(t,e,n,r,i){this._node=t,this._property=e,this._propertyBlendState=n,this._host=r,this._constants=i}var e=t.prototype;return e.getValue=function(){return this._node[this._property]},e.setValue=function(t){var e=this._propertyBlendState,n=this._host.weight;e.blend(t,n)},n(t,[{key:"node",get:function(){return this._node}},{key:"property",get:function(){return this._property}}]),t}();!function(t){t[t.POSITION=1]="POSITION",t[t.ROTATION=2]="ROTATION",t[t.SCALE=4]="SCALE",t[t.EULER_ANGLES=8]="EULER_ANGLES"}(lv||(lv={})),lv.POSITION,lv.ROTATION,lv.SCALE,lv.EULER_ANGLES;var fv,pv,dv=function(){function t(){this.refCount=0,this.accumulatedWeight=0,this.result=new k}var e=t.prototype;return e.blend=function(t,e){this.accumulatedWeight=gv(this.result,this.result,this.accumulatedWeight,t,e)},e.reset=function(){this.accumulatedWeight=0,k.zero(this.result)},t}(),vv=function(){function t(){this.refCount=0,this.accumulatedWeight=0,this.result=new L}var e=t.prototype;return e.blend=function(t,e){this.accumulatedWeight=mv(this.result,this.result,this.accumulatedWeight,t,e)},e.reset=function(){this.accumulatedWeight=0,L.identity(this.result)},t}(),_v=function(t){function n(){return t.apply(this,arguments)||this}e(n,t);var r=n.prototype;return r.apply=function(e){var n=this._properties,r=n.position,i=n.scale,o=n.rotation,a=n.eulerAngles;r&&r.accumulatedWeight&&(this._transformApplyFlags|=lv.POSITION,r.accumulatedWeight<1&&r.blend(e.position,1-r.accumulatedWeight)),i&&i.accumulatedWeight&&(this._transformApplyFlags|=lv.SCALE,i.accumulatedWeight<1&&i.blend(e.scale,1-i.accumulatedWeight)),a&&a.accumulatedWeight&&(this._transformApplyFlags|=lv.EULER_ANGLES,a.accumulatedWeight<1&&a.blend(e.eulerAngles,1-a.accumulatedWeight)),o&&o.accumulatedWeight&&(this._transformApplyFlags|=lv.ROTATION,o.accumulatedWeight<1&&o.blend(e.rotation,1-o.accumulatedWeight)),t.prototype.apply.call(this,e),null==r||r.reset(),null==i||i.reset(),null==o||o.reset(),null==a||a.reset()},r._createVec3BlendState=function(){return new dv},r._createQuatBlendState=function(){return new vv},n}(function(){function t(){this._transformApplyFlags=0,this._properties={}}var e=t.prototype;return e.refProperty=function(t,e){var n,r,i,o=this._properties;switch(e){default:case"position":case"scale":case"eulerAngles":i=null!==(n=o[e])&&void 0!==n?n:o[e]=this._createVec3BlendState(t[e]);break;case"rotation":i=null!==(r=o[e])&&void 0!==r?r:o[e]=this._createQuatBlendState(t.rotation)}return++i.refCount,i},e.deRefProperty=function(t){var e=this._properties,n=e[t];n&&(--n.refCount,n.refCount>0||delete e[t])},e.apply=function(t){var e,n,r,i=this._transformApplyFlags,o=this._properties,a=o.position,s=o.scale,u=o.rotation,l=o.eulerAngles;i&&(a&&i&lv.POSITION&&(e=a.result),s&&i&lv.SCALE&&(n=s.result),l&&i&lv.EULER_ANGLES&&(r=l.result),u&&i&lv.ROTATION&&(r=u.result),(r||e||n)&&t.setRTS(r,e,n),this._transformApplyFlags=0)},n(t,[{key:"empty",get:function(){var t=this._properties;return!(t.position||t.rotation||t.eulerAngles||t.scale)}}]),t}()),yv=function(t){function n(){return t.apply(this,arguments)||this}return e(n,t),n.prototype.createNodeBlendState=function(){return new _v},n}(cv);function gv(t,e,n,r,i){var o=n+i;if(1!==i||n){if(o){var a=i/o;k.lerp(t,t,r,a)}}else k.copy(t,r);return o}function mv(t,e,n,r,i){var o=n+i;if(1!==i||n){if(o){var a=i/o;L.slerp(t,e,r,a)}}else L.copy(t,r);return o}var Ev=t("AnimationManager",P((pv=function(t){function i(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this)._anims=new w([]),e._crossFades=new w([]),e._delayEvents=[],e._blendStateBuffer=new yv,e._sockets=[],e}e(i,t);var o=i.prototype;return o.addCrossFade=function(t){-1===this._crossFades.array.indexOf(t)&&this._crossFades.push(t)},o.removeCrossFade=function(t){var e=this._crossFades.array.indexOf(t);e>=0?this._crossFades.fastRemoveAt(e):r(3907)},o.update=function(t){var e=this._delayEvents,n=this._crossFades,r=this._sockets,i=n.array;for(n.i=0;n.i<i.length;++n.i)i[n.i].update(t);var o=this._anims,a=o.array;for(o.i=0;o.i<a.length;++o.i){var s=a[o.i];s.isMotionless||s.update(t)}this._blendStateBuffer.apply();for(var u=Wt.getTotalFrames(),l=0,c=r.length;l<c;l++){var h=r[l],f=h.target,p=h.transform;f.matrix=jt(p,u)}for(var d=0,v=e.length;d<v;d++){var _=e[d];_.fn.apply(_.thisArg,_.args)}e.length=0},o.destruct=function(){},o.addAnimation=function(t){-1===this._anims.array.indexOf(t)&&this._anims.push(t)},o.removeAnimation=function(t){var e=this._anims.array.indexOf(t);e>=0?this._anims.fastRemoveAt(e):r(3907)},o.pushDelayEvent=function(t,e,n){this._delayEvents.push({fn:t,thisArg:e,args:n})},o.addSockets=function(t,e){for(var n=this,r=function(){var r=e[i];if(n._sockets.find((function(t){return t.target===r.target})))return 1;var o=t.getChildByPath(r.path),a=r.target&&o&&Qt(o,t);a&&n._sockets.push({target:r.target,transform:a})},i=0;i<e.length;++i)r()},o.removeSockets=function(t,e){for(var n=0;n<e.length;++n)for(var r=e[n],i=0;i<this._sockets.length;++i){var o=this._sockets[i];if(o.target===r.target){Ht(o.transform.node),this._sockets[i]=this._sockets[this._sockets.length-1],this._sockets.length--;break}}},n(i,[{key:"blendState",get:function(){return this._blendStateBuffer}}]),i}(X),pv.ID="animation",fv=pv))||fv);Wt.on(zt.INIT,(function(){var t=new Ev;Wt.registerSystem(Ev.ID,t,Z.HIGH)})),J.AnimationManager=Ev}}}));
