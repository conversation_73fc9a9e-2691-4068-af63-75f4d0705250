System.register([],(function(t){"use strict";return{execute:function(){t("A",function(){function t(){this.matrix=[]}var i=t.prototype;return i.get=function(t,i){if(i>t){var n=i;i=t,t=n}return this.matrix[(t*(t+1)>>1)+i-1]},i.set=function(t,i,n){if(i>t){var e=i;i=t,t=e}this.matrix[(t*(t+1)>>1)+i-1]=n?1:0},i.reset=function(){this.matrix.length=0},i.setNumObjects=function(t){this.matrix.length=t*(t-1)>>1},t}())}}}));
