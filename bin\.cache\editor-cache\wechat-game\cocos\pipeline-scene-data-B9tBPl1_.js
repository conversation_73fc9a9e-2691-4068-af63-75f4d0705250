System.register(["./gc-object-CKHc4SnS.js","./scene-7MDSMR3j.js","./debug-view-CKetkq9d.js","./device-manager-BvjvoelW.js","./buffer-barrier-CuX_5NUR.js","./rendering-sub-mesh-CowWLfXC.js","./component-BaGvu7EF.js","./factory-D9_8ZCqM.js","./index-C5lmLqDW.js","./global-exports-CR3GRnjt.js"],(function(e){"use strict";var t,i,s,r,a,n,o,u,c,l,h,d,_,m,y,w,f,p,g,S,v,F,M,V,L,b;return{setters:[function(e){t=e.a,i=e._,s=e.p},function(e){r=e.C,a=e.f,n=e.b,o=e.F,u=e.A,c=e.S,l=e.h,h=e.P},function(e){d=e.O,_=e.S},null,function(e){m=e.B,y=e.b,w=e.M,f=e.A,p=e.F,g=e.I},null,null,null,function(e){S=e.M,v=e.b,F=e.V,M=e.F,V=e.A,L=e.e},function(e){b=e.c}],execute:function(){var k=new S,B=new S,x=new S,R=new S,j=new S,P=new S,C=new S,Q=new v(0,0,0),E=new v,O=new F,A=new v,D=new v,I=new v(1e7,1e7,1e7),N=new v(-1e7,-1e7,-1e7),z=new v,T=0,Y=0,q=function(){function e(e){this._shadowObjects=[],this._shadowCameraFar=0,this._matShadowView=new S,this._matShadowProj=new S,this._matShadowViewProj=new S,this._validFrustum=new M,this._splitFrustum=new M,this._lightViewFrustum=new M,this._castLightViewBoundingBox=new V,this._level=e,this._validFrustum.accurate=!0,this._splitFrustum.accurate=!0,this._lightViewFrustum.accurate=!0}var i=e.prototype;return i.copyToValidFrustum=function(e){M.copy(this._validFrustum,e)},i.calculateValidFrustumOrtho=function(e,t,i,s,r){M.createOrtho(this._validFrustum,e,t,i,s,r)},i.calculateSplitFrustum=function(e,t,i,s){this._splitFrustum.split(i,s,e.aspect,e.fov,t)},i.destroy=function(){this._shadowObjects.length=0},i.createMatrix=function(e,t,i){var s=b.director.root.device,a=e.shadowInvisibleOcclusionRange;M.copy(this._lightViewFrustum,this._splitFrustum),S.fromRT(B,e.node.rotation,Q),S.invert(x,B);var n,o,u=x.clone();this._lightViewFrustum.transform(x),V.fromPoints(this._castLightViewBoundingBox,I,N),this._castLightViewBoundingBox.mergeFrustum(this._lightViewFrustum),e.csmOptimizationMode===r.DisableRotationFix?(n=2*this._castLightViewBoundingBox.halfExtents.x,o=2*this._castLightViewBoundingBox.halfExtents.y):n=o=v.distance(this._lightViewFrustum.vertices[0],this._lightViewFrustum.vertices[6]);var c=b.director.root.pipeline.pipelineSceneData.csmSupported?e.csmLevel:1;if(c>1&&e.csmOptimizationMode===r.RemoveDuplicates)if(this._level>=c-1)Y=this._castLightViewBoundingBox.halfExtents.z,T=this._castLightViewBoundingBox.center.z;else{var l=Math.abs(this._castLightViewBoundingBox.center.z-T)+Y;this._castLightViewBoundingBox.halfExtents.z=Math.max(this._castLightViewBoundingBox.center.z,l)}var h=this._castLightViewBoundingBox.halfExtents.z;this._shadowCameraFar=2*h+a;var d=this._castLightViewBoundingBox.center;if(z.set(d.x,d.y,d.z+h+a),v.transformMat4(z,z,B),S.fromRT(B,e.node.rotation,z),S.invert(x,B),!i){var _=.5*n,m=.5*o;S.ortho(R,-_,_,-m,m,.1,this._shadowCameraFar,s.capabilities.clipSpaceMinZ,s.capabilities.clipSpaceSignY),S.multiply(P,R,u),v.transformMat4(E,z,P);var y=2/t;O.set(y,y);var w=E.x%O.x,f=E.y%O.y;A.set(E.x-w,E.y-f,E.z),S.invert(C,P),v.transformMat4(D,A,C),S.fromRT(B,e.node.rotation,D),S.invert(x,B),S.multiply(j,R,x),S.copy(this._matShadowView,x),S.copy(this._matShadowProj,R),S.copy(this._matShadowViewProj,j)}M.createOrtho(this._validFrustum,n,o,.1,this._shadowCameraFar,B)},t(e,[{key:"level",get:function(){return this._level}},{key:"shadowObjects",get:function(){return this._shadowObjects}},{key:"shadowCameraFar",get:function(){return this._shadowCameraFar},set:function(e){this._shadowCameraFar=e}},{key:"matShadowView",get:function(){return this._matShadowView},set:function(e){this._matShadowView=e}},{key:"matShadowProj",get:function(){return this._matShadowProj},set:function(e){this._matShadowProj=e}},{key:"matShadowViewProj",get:function(){return this._matShadowViewProj},set:function(e){this._matShadowViewProj=e}},{key:"validFrustum",get:function(){return this._validFrustum}},{key:"splitFrustum",get:function(){return this._splitFrustum}},{key:"lightViewFrustum",get:function(){return this._lightViewFrustum}},{key:"castLightViewBoundingBox",get:function(){return this._castLightViewBoundingBox}}]),e}(),G=function(e){function s(t){var i;return(i=e.call(this,t)||this)._splitCameraNear=0,i._splitCameraFar=0,i._csmAtlas=new L,i._calculateAtlas(t),i}i(s,e);var r=s.prototype;return r.destroy=function(){e.prototype.destroy.call(this)},r._calculateAtlas=function(e){var t=b.director.root.device.capabilities.clipSpaceSignY,i=e%2-.5,s=(.5-Math.floor(e/2))*t;this._csmAtlas.set(.5,.5,i,s)},t(s,[{key:"splitCameraNear",get:function(){return this._splitCameraNear},set:function(e){this._splitCameraNear=e}},{key:"splitCameraFar",get:function(){return this._splitCameraFar},set:function(e){this._splitCameraFar=e}},{key:"csmAtlas",get:function(){return this._csmAtlas},set:function(e){this._csmAtlas=e}}]),s}(q),H=function(){function e(){this._castShadowObjects=[],this._layerObjects=new s(64),this._layers=[],this._levelCount=0,this._specialLayer=new q(1),this._shadowDistance=0;for(var e=0;e<a.LEVEL_4;e++)this._layers[e]=new G(e)}var i=e.prototype;return i.update=function(e,t){var i=t.scene.mainLight;if(null!==i){var s=e.shadows,r=b.director.root.pipeline.pipelineSceneData.csmSupported?i.csmLevel:1,a=i.shadowDistance;s.enabled&&i.shadowEnabled&&(i.shadowFixedArea?this._updateFixedArea(i):((i.csmNeedUpdate||this._levelCount!==r||this._shadowDistance!==a)&&(this._splitFrustumLevels(i),this._levelCount=r,this._shadowDistance=a),this._calculateCSM(t,i,s)))}},i.destroy=function(){this._castShadowObjects.length=0;for(var e=0;e<this._layers.length;e++)this._layers[e].destroy();this._layers.length=0},i._updateFixedArea=function(e){var t=b.director.root.device,i=e.shadowOrthoSize,s=e.shadowOrthoSize,r=e.shadowNear,a=e.shadowFar;S.fromRT(B,e.node.worldRotation,e.node.worldPosition),S.invert(x,B),S.ortho(R,-i,i,-s,s,r,a,t.capabilities.clipSpaceMinZ,t.capabilities.clipSpaceSignY),S.multiply(j,R,x),this._specialLayer.matShadowView=x,this._specialLayer.matShadowProj=R,this._specialLayer.matShadowViewProj=j,this._specialLayer.calculateValidFrustumOrtho(2*i,2*s,r,a,B)},i._splitFrustumLevels=function(e){var t=.1,i=e.shadowDistance,s=i/t,r=b.director.root.pipeline.pipelineSceneData.csmSupported?e.csmLevel:1,a=e.csmLayerLambda;this._layers[0].splitCameraNear=t;for(var n=1;n<r;n++){var o=n/r,u=a*t*Math.pow(s,o)+(1-a)*(t+(i-t)*o),c=1.005*u;this._layers[n].splitCameraNear=u,this._layers[n-1].splitCameraFar=c}this._layers[r-1].splitCameraFar=i,e.csmNeedUpdate=!1},i._calculateCSM=function(e,t,i){var s=b.director.root.pipeline.pipelineSceneData.csmSupported?t.csmLevel:1,r=s>1?.5*i.size.x:i.size.x;if(!(r<0)){this._getCameraWorldMatrix(k,e);for(var n=s-1;n>=0;n--){var o=this._layers[n],u=o.splitCameraNear,c=o.splitCameraFar;o.calculateSplitFrustum(e,k,u,c),o.createMatrix(t,r,!1)}s===a.LEVEL_1?(this._specialLayer.shadowCameraFar=this._layers[0].shadowCameraFar,S.copy(this._specialLayer.matShadowView,this._layers[0].matShadowView),S.copy(this._specialLayer.matShadowProj,this._layers[0].matShadowProj),S.copy(this._specialLayer.matShadowViewProj,this._layers[0].matShadowViewProj),this._specialLayer.copyToValidFrustum(this._layers[0].validFrustum)):(this._specialLayer.calculateSplitFrustum(e,k,.1,t.shadowDistance),this._specialLayer.createMatrix(t,r,!0))}},i._getCameraWorldMatrix=function(e,t){if(t.node){var i=t.node,s=i.worldPosition,r=i.worldRotation;S.fromRT(e,r,s)}},t(e,[{key:"castShadowObjects",get:function(){return this._castShadowObjects}},{key:"layerObjects",get:function(){return this._layerObjects}},{key:"layers",get:function(){return this._layers}},{key:"specialLayer",get:function(){return this._specialLayer}}]),e}();e("P",function(){function e(){this.fog=new o,this.ambient=new u,this.skybox=new c,this.shadows=new l,this.csmLayers=new H,this.octree=new d,this.skin=new _,this.postSettings=new h,this.lightProbes=b.internal.LightProbes?new b.internal.LightProbes:null,this.validPunctualLights=[],this.renderObjects=[],this.shadowFrameBufferMap=new Map,this._geometryRendererMaterials=[],this._geometryRendererPasses=[],this._geometryRendererShaders=[],this._occlusionQueryVertexBuffer=null,this._occlusionQueryIndicesBuffer=null,this._occlusionQueryInputAssembler=null,this._occlusionQueryMaterial=null,this._occlusionQueryShader=null,this._isHDR=!0,this._shadingScale=1,this._csmSupported=!0,this._standardSkinMeshRenderer=null,this._standardSkinModel=null,this._skinMaterialModel=null,this._shadingScale=1}var i=e.prototype;return i.activate=function(e){return this._device=e,this.initGeometryRendererMaterials(),this.initOcclusionQuery(),!0},i.initGeometryRendererMaterials=function(){for(var e=0,t=this._geometryRendererMaterials,i=0;i<6;i++){t[i]=new n,t[i]._uuid="geometry-renderer-material-"+i,t[i].initialize({effectName:"internal/builtin-geometry-renderer",technique:i});for(var s=t[i].passes,r=0;r<s.length;++r)this._geometryRendererPasses[e]=s[r],this._geometryRendererShaders[e]=s[r].getShaderVariant(),e++}},i.initOcclusionQuery=function(){if(this._occlusionQueryInputAssembler||(this._occlusionQueryInputAssembler=this._createOcclusionQueryIA()),!this._occlusionQueryMaterial){var e=new n;e._uuid="default-occlusion-query-material",e.initialize({effectName:"internal/builtin-occlusion-query"}),this._occlusionQueryMaterial=e,e.passes.length>0&&(this._occlusionQueryShader=e.passes[0].getShaderVariant())}},i.getOcclusionQueryPass=function(){return this._occlusionQueryMaterial&&this._occlusionQueryMaterial.passes.length>0?this._occlusionQueryMaterial.passes[0]:null},i.updatePipelineSceneData=function(){},i.destroy=function(){var e,t,i;this.shadows.destroy(),this.csmLayers.destroy(),this.validPunctualLights.length=0,null==(e=this._occlusionQueryInputAssembler)||e.destroy(),this._occlusionQueryInputAssembler=null,null==(t=this._occlusionQueryVertexBuffer)||t.destroy(),this._occlusionQueryVertexBuffer=null,null==(i=this._occlusionQueryIndicesBuffer)||i.destroy(),this._occlusionQueryIndicesBuffer=null,this._standardSkinMeshRenderer=null,this._standardSkinModel=null,this._skinMaterialModel=null},i._createOcclusionQueryIA=function(){var e=this._device,t=new Float32Array([-1,-1,-1,1,-1,-1,-1,1,-1,1,1,-1,-1,-1,1,1,-1,1,-1,1,1,1,1,1]),i=3*Float32Array.BYTES_PER_ELEMENT,s=8*i;this._occlusionQueryVertexBuffer=e.createBuffer(new m(y.VERTEX|y.TRANSFER_DST,w.DEVICE,s,i)),this._occlusionQueryVertexBuffer.update(t);var r=new Uint16Array([0,2,1,1,2,3,4,5,6,5,7,6,1,3,7,1,7,5,0,4,6,0,6,2,0,1,5,0,5,4,2,6,7,2,7,3]),a=Uint16Array.BYTES_PER_ELEMENT,n=36*a;this._occlusionQueryIndicesBuffer=e.createBuffer(new m(y.INDEX|y.TRANSFER_DST,w.DEVICE,n,a)),this._occlusionQueryIndicesBuffer.update(r);var o=[new f("a_position",p.RGB32F)],u=new g(o,[this._occlusionQueryVertexBuffer],this._occlusionQueryIndicesBuffer);return e.createInputAssembler(u)},t(e,[{key:"isHDR",get:function(){return this._isHDR},set:function(e){this._isHDR=e}},{key:"shadingScale",get:function(){return this._shadingScale},set:function(e){this._shadingScale=e}},{key:"csmSupported",get:function(){return this._csmSupported},set:function(e){this._csmSupported=e}},{key:"standardSkinModel",get:function(){return this._standardSkinModel},set:function(e){this._standardSkinModel=e}},{key:"standardSkinMeshRenderer",get:function(){return this._standardSkinMeshRenderer},set:function(e){this._standardSkinMeshRenderer&&this._standardSkinMeshRenderer!==e&&this._standardSkinMeshRenderer.clearGlobalStandardSkinObjectFlag(),this._standardSkinMeshRenderer=e,this.standardSkinModel=e?e.model:null}},{key:"skinMaterialModel",get:function(){return this._skinMaterialModel},set:function(e){this._skinMaterialModel=e}},{key:"geometryRendererPasses",get:function(){return this._geometryRendererPasses}},{key:"geometryRendererShaders",get:function(){return this._geometryRendererShaders}}]),e}())}}}));
