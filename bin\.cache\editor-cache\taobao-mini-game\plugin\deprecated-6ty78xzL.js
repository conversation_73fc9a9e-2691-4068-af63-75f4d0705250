System.register(["./device-manager-B8lyUDPi.js","./buffer-barrier-q_79u36H.js","./index-Y4La_nfG.js","./gc-object-D18ulfCO.js","./deprecated-D5UVm7fE.js","./pipeline-state-manager-DQyhxoC_.js","./global-exports-CLZKKIY2.js","./scene-ArUG4OfI.js","./debug-view-BP17WHcy.js"],(function(t){"use strict";var e,n,a,i,s,r,h,o,u,m,c,l,g,_,d,f,p,x,y;return{setters:[null,function(t){e=t.c,n=t.u,a=t.T,i=t.d,s=t.e,r=t.F,h=t.f},function(t){o=t.h,u=t.r},function(t){m=t.x},function(t){c=t.a,l=t.M,g=t.S,_=t.b},function(t){d=t.L},function(t){f=t.l},function(t){p=t.P,x=t.S},function(t){y=t.f}],execute:function(){var S,M;function w(t){return--t,t|=t>>16,t|=t>>8,t|=t>>4,t|=t>>2,t|=t>>1,++t}function E(t,e){return Math.ceil(t/e)*e}t("n",w),t("R",S),function(t){t[t.OPAQUE=0]="OPAQUE",t[t.TRANSPARENT=1]="TRANSPARENT",t[t.OVERLAY=2]="OVERLAY"}(S||t("R",S={})),t("P",M),function(t){t[t.DEFAULT=1]="DEFAULT",t[t.FORWARD=2]="FORWARD",t[t.SHADOWCAST=4]="SHADOWCAST"}(M||t("P",M={})),t("T",function(){function t(t){this._format=r.UNKNOWN,this._formatSize=0,this._chunks=[],this._chunkCount=0,this._handles=[],this._region0=new h,this._region1=new h,this._region2=new h,this._roundUpFn=null,this._bufferViewCtor=Uint8Array,this._channels=4,this._alignment=1,this._device=t}var o=t.prototype;return o.initialize=function(t){var a=e[t.format];this._format=t.format,this._formatSize=a.size,this._channels=a.count,this._bufferViewCtor=n(a),this._roundUpFn=t.roundUpFn||null,this._alignment=t.alignment||1,t.inOrderFree&&(this.alloc=this._McDonaldAlloc)},o.destroy=function(){for(var t=0;t<this._chunkCount;++t)this._chunks[t].texture.destroy();this._chunks.length=0,this._handles.length=0},o.alloc=function(t,e){t=E(t,this._alignment);var n=-1,a=-1;if(void 0!==e&&(n=e,a=this._findAvailableSpace(t,n)),a<0)for(var i=0;i<this._chunkCount&&(n=i,!((a=this._findAvailableSpace(t,n))>=0));++i);if(a>=0){var s=this._chunks[n];s.start+=t;var r={chunkIdx:n,start:a,end:a+t,texture:s.texture};return this._handles.push(r),r}var h=Math.sqrt(t/this._formatSize),o=this._roundUpFn&&this._roundUpFn(h,this._formatSize)||Math.max(1024,w(h)),u=this._chunks[this.createChunk(o)];u.start+=t;var m={chunkIdx:this._chunkCount-1,start:0,end:t,texture:u.texture};return this._handles.push(m),m},o.free=function(t){for(var e=0;e<this._handles.length;++e)if(this._handles[e]===t)return this._chunks[t.chunkIdx].end=t.end,void this._handles.splice(e,1)},o.createChunk=function(t){var e=t*t*this._formatSize;m("TextureBufferPool: Allocate chunk "+this._chunkCount+", size: "+e+", format: "+this._format);var n={texture:this._device.createTexture(new a(i.TEX2D,s.SAMPLED|s.TRANSFER_DST,this._format,t,t)),size:e,start:0,end:e};return this._chunks[this._chunkCount]=n,this._chunkCount++},o.update=function(t,e){var n=[],a=[],i=t.start/this._formatSize,s=e.byteLength/this._formatSize,r=i%t.texture.width,h=Math.floor(i/t.texture.width),o=Math.min(t.texture.width-r,s),u=0;r>0&&(this._region0.texOffset.x=r,this._region0.texOffset.y=h,this._region0.texExtent.width=o,this._region0.texExtent.height=1,n.push(new this._bufferViewCtor(e,u*this._formatSize,o*this._channels)),a.push(this._region0),r=0,h+=1,s-=o,u+=o),s>0&&(this._region1.texOffset.x=r,this._region1.texOffset.y=h,s>t.texture.width?(this._region1.texExtent.width=t.texture.width,this._region1.texExtent.height=Math.floor(s/t.texture.width),o=this._region1.texExtent.width*this._region1.texExtent.height):(o=s,this._region1.texExtent.width=o,this._region1.texExtent.height=1),n.push(new this._bufferViewCtor(e,u*this._formatSize,o*this._channels)),a.push(this._region1),r=0,h+=this._region1.texExtent.height,s-=o,u+=o),s>0&&(this._region2.texOffset.x=r,this._region2.texOffset.y=h,this._region2.texExtent.width=s,this._region2.texExtent.height=1,n.push(new this._bufferViewCtor(e,u*this._formatSize,s*this._channels)),a.push(this._region2)),this._device.copyBuffersToTexture(n,t.texture,a)},o._findAvailableSpace=function(t,e){var n=this._chunks[e],a=!1,i=n.start;if(i+t<=n.size)a=!0;else{i=0;for(var s=this._handles.filter((function(t){return t.chunkIdx===e})).sort((function(t,e){return t.start-e.start})),r=0;r<s.length;r++){var h=s[r];if(i+t<=h.start){a=!0;break}i=h.end}!a&&i+t<=n.size&&(a=!0)}return a?i:-1},o._McDonaldAlloc=function(t){t=E(t,this._alignment);for(var e=0;e<this._chunkCount;++e){var n=this._chunks[e],a=!1,i=n.start;if(i+t<=n.end?a=!0:i>n.end?i+t<=n.size?a=!0:t<=n.end&&(n.start=i=0,a=!0):i===n.end&&(n.start=i=0,n.end=n.size,t<=n.end&&(a=!0)),a){n.start+=t;var s={chunkIdx:e,start:i,end:i+t,texture:n.texture};return this._handles.push(s),s}}var r=Math.sqrt(t/this._formatSize),h=this._roundUpFn&&this._roundUpFn(r,this._formatSize)||Math.max(1024,w(r)),o=this._chunks[this.createChunk(h)];o.start+=t;var u={chunkIdx:this._chunkCount,start:0,end:t,texture:o.texture};return this._handles.push(u),u},t}()),o(c.prototype,"RenderScene.prototype",[{name:"raycastUI2DNode"},{name:"raycastUINode"}]),o(c.prototype,"RenderScene.prototype",[{name:"raycastAll",suggest:"using intersect.rayModel in geometry"},{name:"raycastAllModels",suggest:"using intersect.rayModel in geometry"},{name:"raycastSingleModel",suggest:"using intersect.rayModel in geometry"},{name:"raycastAllCanvas",suggest:"using intersect.rayAABB in geometry"},{name:"rayResultCanvas"},{name:"rayResultModels"},{name:"rayResultAll"},{name:"rayResultSingleModel"}]),o(l.prototype,"Model.prototype",[{name:"isInstancingEnabled"},{name:"instancedAttributes"}]);var I=t("C",{});o(I,"CameraVisFlags",[{name:"GENERAL"}]),u(I,"CameraVisFlags",[{name:"PROFILER",newName:"PROFILER",target:d.BitMask,targetName:"PROFILER"},{name:"GIZMOS",newName:"GIZMOS",target:d.BitMask,targetName:"GIZMOS"},{name:"EDITOR",newName:"EDITOR",target:d.BitMask,targetName:"EDITOR"},{name:"UI",newName:"UI",target:d.BitMask,targetName:"UI_3D"},{name:"UI2D",newName:"UI2D",target:d.BitMask,targetName:"UI_2D"}]),f.CameraVisFlags=I;var v=t("V",{});o(v,"VisibilityFlags",[{name:"GENERAL"}]),u(v,"VisibilityFlags",[{name:"ALWALS",newName:"ALWALS",target:d.Enum,targetName:"ALWALS"},{name:"PROFILER",newName:"PROFILER",target:d.Enum,targetName:"PROFILER"},{name:"GIZMOS",newName:"GIZMOS",target:d.Enum,targetName:"GIZMOS"},{name:"EDITOR",newName:"EDITOR",target:d.Enum,targetName:"EDITOR"},{name:"UI",newName:"UI",target:d.Enum,targetName:"UI_3D"},{name:"UI2D",newName:"UI2D",target:d.Enum,targetName:"UI_2D"}]),f.VisibilityFlags=v,u(p.prototype,"Pass.prototype",[{name:"getBindingTypeFromHandle",newName:"getDescriptorTypeFromHandle"}]),o(y.prototype,"Camera.prototype",[{name:"getSplitFrustum"},{name:"setMatView"},{name:"setMatViewInv"},{name:"setMatProjInv"},{name:"setMatViewProjInv"},{name:"setMatProj"},{name:"setMatViewProj"},{name:"getMatViewInv"}]),o(x.prototype,"Shadows.prototype",[{name:"aspect"},{name:"selfShadow"},{name:"linear"},{name:"packing"},{name:"autoAdapt"},{name:"fixedArea"},{name:"pcf"},{name:"bias"},{name:"normalBias"},{name:"near"},{name:"far"},{name:"shadowDistance"},{name:"invisibleOcclusionRange"},{name:"orthoSize"},{name:"saturation"}]),o(g.prototype,"SpotLight.prototype",[{name:"aspect"}]),u(_.prototype,"SubModel.prototype",[{name:"subMeshData",newName:"subMesh"}]),o(_.prototype,"SubModel.prototype",[{name:"getSubModel",suggest:"Use `subModels[i]` instead"},{name:"subModelNum",suggest:"Use `subModels.length` instead"}])}}}));
