System.register(["./gc-object-CKHc4SnS.js","./index-C5lmLqDW.js","./global-exports-CR3GRnjt.js","./pipeline-state-manager-Cdpe3is6.js","./component-BaGvu7EF.js","./node-event-DTNosVQv.js","./touch-DB0AR-Sc.js","./device-manager-BvjvoelW.js","./buffer-barrier-CuX_5NUR.js","./factory-D9_8ZCqM.js"],(function(t){"use strict";var e,i,n,r,s,o,a,u,h,l,p,c,f,_,d,g,m,v,y,b,I,T,E,D,R,S,M,A,w,k,C,x,L,P,O,N,H,B,F,U,G,V,z,j,W,X,q,K,Y,Q,J,Z,$,tt,et,it,nt,rt,st,ot,at,ut,ht,lt,pt,ct,ft,_t,dt,gt,mt,vt,yt,bt,It,Tt,Et,Dt,Rt,St,Mt,At,wt,kt,Ct,xt,Lt,Pt,Ot,Nt,Ht,Bt,Ft,Ut,Gt,Vt,zt,jt,Wt,Xt,qt,Kt,Yt,Qt,Jt,Zt,$t,te,ee,ie,ne,re,se,oe,ae,ue,he,le,pe,ce,fe,_e,de,ge,me,ve,ye,be,Ie,Te,Ee,De,Re,Se,Me,Ae,we,ke,Ce,xe,Le,Pe,Oe,Ne,He,Be,Fe,Ue,Ge,Ve,ze,je,We,Xe,qe,Ke,Ye,Qe,Je,Ze,$e,ti,ei,ii,ni,ri,si,oi,ai,ui,hi,li,pi,ci,fi,_i,di,gi,mi,vi,yi;return{setters:[function(t){e=t.t,i=t._,n=t.h,r=t.a,s=t.ao,o=t.A,a=t.I,u=t.J,h=t.r,l=t.a2,p=t.q,c=t.ap,f=t.l,_=t.L,d=t.w,g=t.G,m=t.g,v=t.j,y=t.aK,b=t.s,I=t.S,T=t.n,E=t.u,D=t.y,R=t.E,S=t.T,M=t.d,A=t.P,w=t.al,k=t.H,C=t.aL,x=t.az,L=t.K,P=t.b,O=t.o,N=t.m,H=t.f,B=t.V},function(t){F=t.c,U=t.a,G=t.s,V=t.t,z=t.m,j=t.V,W=t.b,X=t.e,q=t.L,K=t.M,Y=t.C,Q=t.Q,J=t.b3,Z=t.X,$=t.w,tt=t.u,et=t._,it=t.aB,nt=t.a1,rt=t.p,st=t.N,ot=t.j,at=t.az},function(t){ut=t.c,ht=t.l,lt=t.V,pt=t.O,ct=t.a,ft=t.P,_t=t.b},function(t){dt=t.n,gt=t.A,mt=t.S,vt=t.H,yt=t.N,bt=t.v,It=t.w,Tt=t.x,Et=t.R,Dt=t.$,Rt=t.i,St=t.P,Mt=t.a0,At=t.a1,wt=t.L},function(t){kt=t.A,Ct=t.h,xt=t.o,Lt=t.t,Pt=t.u,Ot=t.v,Nt=t.f,Ht=t.n,Bt=t.r,Ft=t.P,Ut=t.w,Gt=t.R,Vt=t.x,zt=t.y,jt=t.z,Wt=t.k,Xt=t.e,qt=t.l,Kt=t.b,Yt=t.B,Qt=t.D,Jt=t.F,Zt=t.C},function(t){$t=t.N},function(t){te=t.d,ee=t.I},function(t){ie=t.d},function(t){ne=t.F,re=t.ak,se=t.m,oe=t.p,ae=t.e,ue=t.f,he=t.r,le=t.T,pe=t.d,ce=t.bi,fe=t.j,_e=t.aN,de=t.aA,ge=t.b6,me=t.g,ve=t.h,ye=t.U,be=t.i,Ie=t.k,Te=t.bk,Ee=t.bn,De=t.bl,Re=t.bm,Se=t.bj,Me=t.A,Ae=t.b7,we=t.S,ke=t.aX,Ce=t.at,xe=t.av,Le=t.B,Pe=t.b,Oe=t.M,Ne=t.I,He=t.a5,Be=t.a7,Fe=t.a4,Ue=t.P,Ge=t.aE,Ve=t.L,ze=t.a8,je=t.ap,We=t.az,Xe=t.bo,qe=t.b1,Ke=t.K},function(t){Ye=t.P,Qe=t.W,Je=t.T,Ze=t.e,$e=t.I,ti=t.u,ei=t.g,ii=t.d,ni=t.h,ri=t.i,si=t.j,oi=t.k,ai=t.l,ui=t.m,hi=t.o,li=t.n,pi=t.q,ci=t.t,fi=t.v,_i=t.f,di=t.r,gi=t.w,mi=t.x,vi=t.y,yi=t.z}],execute:function(){var bi,Ii,Ti,Ei,Di,Ri,Si,Mi,Ai,wi,ki;t({D:Wn,G:pr,I:Xn,K:qn,ab:xl,ac:kl,ae:function(t){for(var e=t.length-1;e>=0;--e){var i=t[e];if(i.window.swapchain)return void(Vr=i)}Vr=null},af:$n,ag:Yn,ah:Zn,ai:Qn,aj:nr,ak:or,al:sr,am:ir,an:function(t,e,i,n,r){if(!Rt()&&n&&n.enabled&&r===Vr){var s=n.subModels[0],o=s.inputAssembler,a=s.passes,u=s.shaders,h=s.descriptorSet;kr.width=Cr.width=r.window.width,kr.height=Cr.height=r.window.height;var l=St.getOrCreatePipelineState(t,a[0],u[0],e,o);i.setViewport(kr),i.setScissor(Cr),i.bindPipelineState(l),i.bindDescriptorSet(mt.MATERIAL,a[0].descriptorSet),i.bindDescriptorSet(mt.LOCAL,h),i.bindInputAssembler(o),i.draw(o)}},ao:xr,e:Yr}),e(ne);var Ci,xi=new s("Tex"),Li=t("i",F("cc.TextureBase")((ki=function(t){function e(e){var i;return(i=t.call(this,e)||this)._format=Ti&&Ti(),i._minFilter=Ei&&Ei(),i._magFilter=Di&&Di(),i._mipFilter=Ri&&Ri(),i._wrapS=Si&&Si(),i._wrapT=Mi&&Mi(),i._wrapR=Ai&&Ai(),i._anisotropy=wi&&wi(),i._width=1,i._height=1,i._samplerInfo=new re,i._gfxSampler=null,i._gfxDevice=null,i._textureHash=0,i._id=xi.getNewId(),i._gfxDevice=i._getGFXDevice(),i._textureHash=se(i._id,666),i}i(e,t);var s=e.prototype;return s.getId=function(){return this._id},s.getPixelFormat=function(){return this._format},s.getAnisotropy=function(){return this._anisotropy},s.setWrapMode=function(t,e,i){void 0===i&&(i=t),this._wrapS=t,this._samplerInfo.addressU=t,this._wrapT=e,this._samplerInfo.addressV=e,this._wrapR=i,this._samplerInfo.addressW=i,this._gfxDevice&&(this._gfxSampler=this._gfxDevice.getSampler(this._samplerInfo))},s.setFilters=function(t,e){this._minFilter=t,this._samplerInfo.minFilter=t,this._magFilter=e,this._samplerInfo.magFilter=e,this._gfxDevice&&(this._gfxSampler=this._gfxDevice.getSampler(this._samplerInfo))},s.setMipFilter=function(t){this._mipFilter=t,this._samplerInfo.mipFilter=t,this._gfxDevice&&(this._gfxSampler=this._gfxDevice.getSampler(this._samplerInfo))},s.setAnisotropy=function(t){t=Math.min(t,16),this._anisotropy=t,this._samplerInfo.maxAnisotropy=t,this._gfxDevice&&(this._gfxSampler=this._gfxDevice.getSampler(this._samplerInfo))},s.destroy=function(){var e,i=t.prototype.destroy.call(this);return i&&null!=(e=ut.director.root)&&e.batcher2D&&ut.director.root.batcher2D._releaseDescriptorSetCache(this._textureHash),i},s.getHash=function(){return this._textureHash},s.getGFXTexture=function(){return null},s.getSamplerInfo=function(){return this._samplerInfo},s.getGFXSampler=function(){return this._gfxSampler||(this._gfxDevice?this._gfxSampler=this._gfxDevice.getSampler(this._samplerInfo):n(9302)),this._gfxSampler},s._serialize=function(){return""},s._deserialize=function(t){var e=t.split(",");e.unshift(""),e.length>=5&&(this.setFilters(parseInt(e[1]),parseInt(e[2])),this.setWrapMode(parseInt(e[3]),parseInt(e[4]))),e.length>=7&&(this.setMipFilter(parseInt(e[5])),this.setAnisotropy(parseInt(e[6])))},s._getGFXDevice=function(){return ie.gfxDevice},s._getGFXFormat=function(){return this._getGFXPixelFormat(this._format)},s._setGFXFormat=function(t){this._format=void 0===t?Ye.RGBA8888:t},s._getGFXPixelFormat=function(t){return t===Ye.RGBA_ETC1?t=Ye.RGB_ETC1:t===Ye.RGB_A_PVRTC_4BPPV1?t=Ye.RGB_PVRTC_4BPPV1:t===Ye.RGB_A_PVRTC_2BPPV1&&(t=Ye.RGB_PVRTC_2BPPV1),t},r(e,[{key:"isCompressed",get:function(){return this._format>=Ye.RGB_ETC1&&this._format<=Ye.RGBA_ASTC_12x12||this._format>=Ye.RGB_A_PVRTC_2BPPV1&&this._format<=Ye.RGBA_ETC1}},{key:"width",get:function(){return this._width}},{key:"height",get:function(){return this._height}}]),e}(kt),ki.PixelFormat=Ye,ki.WrapMode=Qe,ki.Filter=Je,Ti=U((Ii=ki).prototype,"_format",[G],(function(){return Ye.RGBA8888})),Ei=U(Ii.prototype,"_minFilter",[G],(function(){return Je.LINEAR})),Di=U(Ii.prototype,"_magFilter",[G],(function(){return Je.LINEAR})),Ri=U(Ii.prototype,"_mipFilter",[G],(function(){return Je.NONE})),Si=U(Ii.prototype,"_wrapS",[G],(function(){return Qe.REPEAT})),Mi=U(Ii.prototype,"_wrapT",[G],(function(){return Qe.REPEAT})),Ai=U(Ii.prototype,"_wrapR",[G],(function(){return Qe.REPEAT})),wi=U(Ii.prototype,"_anisotropy",[G],(function(){return 0})),bi=Ii))||bi);ut.TextureBase=Li;var Pi=[new ue];function Oi(t,e){for(var i=Math.max(t,e),n=0;i;)i>>=1,n++;return n}function Ni(t){return t&&!(t&t-1)}function Hi(t,e,i){return!(t.gfxAPI===he.WEBGL)||Ni(e)&&Ni(i)}var Bi,Fi,Ui,Gi,Vi,zi=F("cc.SimpleTexture")(Ci=function(t){function e(e){var i;return(i=t.call(this,e)||this)._gfxTexture=null,i._gfxTextureView=null,i._mipmapLevel=1,i._textureWidth=0,i._textureHeight=0,i._baseLevel=0,i._maxLevel=1e3,i}i(e,t);var n=e.prototype;return n.getGFXTexture=function(){return this._gfxTextureView},n.destroy=function(){return this._tryDestroyTextureView(),this._tryDestroyTexture(),t.prototype.destroy.call(this)},n.updateImage=function(){this.updateMipmaps(0)},n.updateMipmaps=function(){},n.uploadData=function(t,e,i){if(void 0===e&&(e=0),void 0===i&&(i=0),this._gfxTexture&&!(this._mipmapLevel<=e)){var n=this._getGFXDevice();if(n){var r=Pi[0];r.texExtent.width=this._textureWidth>>e,r.texExtent.height=this._textureHeight>>e,r.texSubres.mipLevel=e,r.texSubres.baseArrayLayer=i,ArrayBuffer.isView(t)?n.copyBuffersToTexture([t],this._gfxTexture,Pi):n.copyTexImagesToTexture([t],this._gfxTexture,Pi)}}},n._assignImage=function(t,e,i){var n=t.data;if(n&&(this.uploadData(n,e,i),this._checkTextureLoaded(),o.CLEANUP_IMAGE_CACHE)){var r=Ze.getDeps(this._uuid),s=r.indexOf(t._uuid);-1!==s&&(a(r,s),t.decRef())}},n._checkTextureLoaded=function(){this._textureReady()},n._textureReady=function(){this.loaded=!0,this.emit("load")},n._setMipmapLevel=function(t){this._mipmapLevel=t<1?1:t},n._setMipRange=function(t,e){this._baseLevel=t<1?0:t,this._maxLevel=e<1?0:e},n.setMipRange=function(t,e){u(t<=e,3124),this._setMipRange(t,e);var i=this._getGFXDevice();if(i){var n=this._createTextureView(i);this._tryDestroyTextureView(),this._gfxTextureView=n}},n._getGfxTextureCreateInfo=function(){return null},n._getGfxTextureViewCreateInfo=function(){return null},n._tryReset=function(){if(this._tryDestroyTextureView(),this._tryDestroyTexture(),0!==this._mipmapLevel){var t=this._getGFXDevice();t&&(this._createTexture(t),this._gfxTextureView=this._createTextureView(t))}},n.isUsingOfflineMipmaps=function(){return!1},n._createTexture=function(t){if(0!==this._width&&0!==this._height){var e=oe.NONE;this._mipFilter!==Je.NONE&&Hi(t,this._width,this._height)&&(this._mipmapLevel=Oi(this._width,this._height),this.isUsingOfflineMipmaps()||this.isCompressed||(e=oe.GEN_MIPMAP));var i=this._getGfxTextureCreateInfo({usage:ae.SAMPLED|ae.TRANSFER_DST|ae.COLOR_ATTACHMENT,format:this._getGFXFormat(),levelCount:this._mipmapLevel,flags:e});if(i){var n=t.createTexture(i);this._textureWidth=i.width,this._textureHeight=i.height,this._gfxTexture=n}}},n._createTextureView=function(t){if(!this._gfxTexture)return null;var e=this._maxLevel<this._mipmapLevel?this._maxLevel:this._mipmapLevel-1,i=this._getGfxTextureViewCreateInfo({texture:this._gfxTexture,format:this._getGFXFormat(),baseLevel:this._baseLevel,levelCount:e-this._baseLevel+1});return i?t.createTexture(i):null},n._tryDestroyTexture=function(){this._gfxTexture&&(this._gfxTexture.destroy(),this._gfxTexture=null)},n._tryDestroyTextureView=function(){this._gfxTextureView&&(this._gfxTextureView.destroy(),this._gfxTextureView=null)},r(e,[{key:"mipmapLevel",get:function(){return this._mipmapLevel}}]),e}(Li))||Ci;ut.SimpleTexture=zi;var ji,Wi,Xi,qi,Ki,Yi,Qi,Ji,Zi,$i=t("k",(Bi=F("cc.Texture2D"),Fi=V([$e]),Bi((Gi=function(t){function e(e){var i;return(i=t.call(this,e)||this)._mipmaps=Vi&&Vi(),i._generatedMipmaps=[],i}i(e,t);var n=e.prototype;return n._setMipmapParams=function(t){var e=this;if(this._generatedMipmaps=t,this._setMipmapLevel(this._generatedMipmaps.length),this._generatedMipmaps.length>0){var i=this._generatedMipmaps[0];this.reset({width:i.width,height:i.height,format:i.format,mipmapLevel:this._generatedMipmaps.length,baseLevel:this._baseLevel,maxLevel:this._maxLevel}),this._generatedMipmaps.forEach((function(t,i){e._assignImage(t,i)}))}else this.reset({width:0,height:0,mipmapLevel:this._generatedMipmaps.length,baseLevel:this._baseLevel,maxLevel:this._maxLevel})},n.initialize=function(){this.mipmaps=this._mipmaps},n.onLoaded=function(){this.initialize()},n.reset=function(t){this._width=t.width,this._height=t.height,this._setGFXFormat(t.format);var e=void 0===t.mipmapLevel?1:t.mipmapLevel;this._setMipmapLevel(e);var i=void 0===t.baseLevel?0:t.baseLevel,n=void 0===t.maxLevel?1e3:t.maxLevel;this._setMipRange(i,n),this._tryReset()},n.create=function(t,e,i,n,r,s){void 0===i&&(i=Ye.RGBA8888),void 0===n&&(n=1),void 0===r&&(r=0),void 0===s&&(s=1e3),this.reset({width:t,height:e,format:i,mipmapLevel:n,baseLevel:r,maxLevel:s})},n.toString=function(){return 0!==this._mipmaps.length?this._mipmaps[0].url:""},n.updateMipmaps=function(t,e){if(void 0===t&&(t=0),void 0===e&&(e=void 0),!(t>=this._generatedMipmaps.length))for(var i=Math.min(void 0===e?this._generatedMipmaps.length:e,this._generatedMipmaps.length-t),n=0;n<i;++n){var r=t+n;this._assignImage(this._generatedMipmaps[r],r)}},n.getHtmlElementObj=function(){return this._mipmaps[0]&&this._mipmaps[0].data instanceof HTMLElement?this._mipmaps[0].data:null},n.destroy=function(){return this._mipmaps=[],this._generatedMipmaps=[],t.prototype.destroy.call(this)},n.description=function(){return"<cc.Texture2D | Name = "+(this._mipmaps[0]?this._mipmaps[0].url:"")+" | Dimension = "+this.width+" x "+this.height+">"},n.releaseTexture=function(){this.destroy()},n._serialize=function(){return null},n._deserialize=function(e,i){var n=e;t.prototype._deserialize.call(this,n.base,i),this._mipmaps=new Array(n.mipmaps.length);for(var r=0;r<n.mipmaps.length;++r)if(this._mipmaps[r]=new $e,n.mipmaps[r]){var s=n.mipmaps[r];i.result.push(this._mipmaps,""+r,s,h($e))}},n._getGfxTextureCreateInfo=function(t){var e=new le(pe.TEX2D);return e.width=this._width,e.height=this._height,Object.assign(e,t),e},n._getGfxTextureViewCreateInfo=function(t){var e=new ce;return e.type=pe.TEX2D,Object.assign(e,t),e},n.initDefault=function(e){t.prototype.initDefault.call(this,e);var i=new $e;i.initDefault(),this.image=i},n.validate=function(){return this.mipmaps&&0!==this.mipmaps.length},r(e,[{key:"mipmaps",get:function(){return this._mipmaps},set:function(t){this._mipmaps=t;var e=[];if(1===t.length){var i=t[0];e.push.apply(e,i.extractMipmaps())}else if(t.length>1)for(var n=0;n<t.length;++n){var r=t[n];e.push(r.extractMipmap0())}this._setMipmapParams(e)}},{key:"image",get:function(){return 0===this._mipmaps.length?null:this._mipmaps[0]},set:function(t){this.mipmaps=t?[t]:[]}}]),e}(zi),Vi=U(Gi.prototype,"_mipmaps",[Fi],(function(){return[]})),Ui=Gi))||Ui));ut.Texture2D=$i,function(t){t[t.right=0]="right",t[t.left=1]="left",t[t.top=2]="top",t[t.bottom=3]="bottom",t[t.front=4]="front",t[t.back=5]="back"}(Ji||(Ji={})),function(t){t[t.NONE=0]="NONE",t[t.AUTO=1]="AUTO",t[t.BAKED_CONVOLUTION_MAP=2]="BAKED_CONVOLUTION_MAP"}(Zi||(Zi={}));var tn=t("a6",F("cc.TextureCube")((Qi=function(t){function e(e){var i;return(i=t.call(this,e)||this).isRGBE=Xi&&Xi(),i._mipmapAtlas=qi&&qi(),i._mipmapMode=Ki&&Ki(),i._mipmaps=Yi&&Yi(),i._generatedMipmaps=[],i}i(e,t);var s=e.prototype;return s._setMipmapParams=function(t){var e=this;if(this._generatedMipmaps=t,this._setMipmapLevel(this._generatedMipmaps.length),this._generatedMipmaps.length>0){var i=this._generatedMipmaps[0].front;this.reset({width:i.width,height:i.height,format:i.format,mipmapLevel:this._generatedMipmaps.length,baseLevel:this._baseLevel,maxLevel:this._maxLevel}),this._generatedMipmaps.forEach((function(t,i){en(t,(function(t,n){e._assignImage(t,i,n)}))}))}else this.reset({width:0,height:0,mipmapLevel:this._generatedMipmaps.length,baseLevel:this._baseLevel,maxLevel:this._maxLevel})},s.isUsingOfflineMipmaps=function(){return this._mipmapMode===Zi.BAKED_CONVOLUTION_MAP},e.fromTexture2DArray=function(t,i){for(var n=[],r=t.length/6,s=0;s<r;s++){var o=6*s;n.push({front:t[o+Ji.front].image,back:t[o+Ji.back].image,left:t[o+Ji.left].image,right:t[o+Ji.right].image,top:t[o+Ji.top].image,bottom:t[o+Ji.bottom].image})}return(i=i||new e).mipmaps=n,i},s.onLoaded=function(){this._mipmapMode===Zi.BAKED_CONVOLUTION_MAP?this.mipmapAtlas=this._mipmapAtlas:this.mipmaps=this._mipmaps},s.reset=function(t){this._width=t.width,this._height=t.height,this._setGFXFormat(t.format);var e=void 0===t.mipmapLevel?1:t.mipmapLevel;this._setMipmapLevel(e);var i=void 0===t.baseLevel?0:t.baseLevel,n=void 0===t.maxLevel?1e3:t.maxLevel;this._setMipRange(i,n),this._tryReset()},s.updateMipmaps=function(t,e){var i=this;if(void 0===t&&(t=0),void 0===e&&(e=void 0),!(t>=this._generatedMipmaps.length))for(var n=Math.min(void 0===e?this._generatedMipmaps.length:e,this._generatedMipmaps.length-t),r=function(){var e=t+s;en(i._generatedMipmaps[e],(function(t,n){i._assignImage(t,e,n)}))},s=0;s<n;++s)r()},s.destroy=function(){return this._mipmaps=[],this._generatedMipmaps=[],this._mipmapAtlas=null,t.prototype.destroy.call(this)},s.releaseTexture=function(){this.destroy()},s._serialize=function(){return null},s._deserialize=function(e,i){var n=e;if(t.prototype._deserialize.call(this,n.base,i),this.isRGBE=n.rgbe,this._mipmapMode=n.mipmapMode,this._mipmapMode===Zi.BAKED_CONVOLUTION_MAP){var r=n.mipmapAtlas,s=n.mipmapLayout;this._mipmapAtlas={atlas:{},layout:s},this._mipmapAtlas.atlas={front:new $e,back:new $e,left:new $e,right:new $e,top:new $e,bottom:new $e};var o=h($e);i.result.push(this._mipmapAtlas.atlas,"front",r.front,o),i.result.push(this._mipmapAtlas.atlas,"back",r.back,o),i.result.push(this._mipmapAtlas.atlas,"left",r.left,o),i.result.push(this._mipmapAtlas.atlas,"right",r.right,o),i.result.push(this._mipmapAtlas.atlas,"top",r.top,o),i.result.push(this._mipmapAtlas.atlas,"bottom",r.bottom,o)}else{this._mipmaps=new Array(n.mipmaps.length);for(var a=0;a<n.mipmaps.length;++a){this._mipmaps[a]={front:new $e,back:new $e,left:new $e,right:new $e,top:new $e,bottom:new $e};var u=n.mipmaps[a],l=h($e);i.result.push(this._mipmaps[a],"front",u.front,l),i.result.push(this._mipmaps[a],"back",u.back,l),i.result.push(this._mipmaps[a],"left",u.left,l),i.result.push(this._mipmaps[a],"right",u.right,l),i.result.push(this._mipmaps[a],"top",u.top,l),i.result.push(this._mipmaps[a],"bottom",u.bottom,l)}}},s._getGfxTextureCreateInfo=function(t){var e=new le(pe.CUBE);return e.width=this._width,e.height=this._height,e.layerCount=6,Object.assign(e,t),e},s._getGfxTextureViewCreateInfo=function(t){var e=new ce;return e.type=pe.CUBE,e.baseLayer=0,e.layerCount=6,Object.assign(e,t),e},s._uploadAtlas=function(){var t=this,e=this._mipmapAtlas.layout,i=e[0];this.reset({width:i.width,height:i.height,format:this._mipmapAtlas.atlas.front.format,mipmapLevel:e.length}),en(this._mipmapAtlas.atlas,(function(i,n){var r=new $i;r.image=i,r.reset({width:i.width,height:i.height,format:i.format}),r.uploadData(i.data);for(var s=0;s<e.length;s++){var o=e[s],a=r.getGFXTexture().size,u=new Uint8Array(a),h=new ue;h.texOffset.x=o.left,h.texOffset.y=o.top,h.texExtent.width=o.width,h.texExtent.height=o.height,t._getGFXDevice().copyTextureToBuffers(r.getGFXTexture(),[u],[h]);var l=new $e({_data:u,_compressed:i.isCompressed,width:o.width,height:o.height,format:i.format});t._assignImage(l,o.level,n)}}))},s.initDefault=function(e){t.prototype.initDefault.call(this,e);var i=new $e;i.initDefault(),this.mipmaps=[{front:i,back:i,top:i,bottom:i,left:i,right:i}]},s.validate=function(){if(this._mipmapMode===Zi.BAKED_CONVOLUTION_MAP){if(null===this.mipmapAtlas||0===this.mipmapAtlas.layout.length)return!1;var t=this.mipmapAtlas.atlas;return!!(t.top&&t.bottom&&t.front&&t.back&&t.left&&t.right)}return 0!==this._mipmaps.length&&!this._mipmaps.find((function(t){return!(t.top&&t.bottom&&t.front&&t.back&&t.left&&t.right)}))},r(e,[{key:"mipmaps",get:function(){return this._mipmaps},set:function(t){this._mipmaps=t;var e=[];if(1===t.length){var i=t[0],r=i.front.extractMipmaps(),s=i.back.extractMipmaps(),o=i.left.extractMipmaps(),a=i.right.extractMipmaps(),u=i.top.extractMipmaps(),h=i.bottom.extractMipmaps();if(r.length!==s.length||r.length!==o.length||r.length!==a.length||r.length!==u.length||r.length!==h.length)return n(16347),void this._setMipmapParams([]);for(var l=r.length,p=0;p<l;++p){var c={front:r[p],back:s[p],left:o[p],right:a[p],top:u[p],bottom:h[p]};e.push(c)}}else t.length>1&&t.forEach((function(t){var i={front:t.front.extractMipmap0(),back:t.back.extractMipmap0(),left:t.left.extractMipmap0(),right:t.right.extractMipmap0(),top:t.top.extractMipmap0(),bottom:t.bottom.extractMipmap0()};e.push(i)}));this._setMipmapParams(e)}},{key:"mipmapAtlas",get:function(){return this._mipmapAtlas},set:function(t){var e=this;if(this._mipmapAtlas=t,this._mipmapAtlas){var i=this._mipmapAtlas.atlas.front;if(i.data)if(z.os===l.IOS||lt||pt)this._uploadAtlas();else{var n=this._mipmapAtlas.atlas,r=this._mipmapAtlas.layout,s=r[0],o=Object.assign(ct.document.createElement("canvas"),{width:i.width,height:i.height}).getContext("2d");this.reset({width:s.width,height:s.height,format:i.format,mipmapLevel:r.length});for(var a=function(){var t=r[u];en(n,(function(n,r){o.clearRect(0,0,i.width,i.height);var s=n.data;o.drawImage(s,0,0);var a=o.getImageData(t.left,t.top,t.width,t.height),u=new $e({_data:a.data,_compressed:n.isCompressed,width:a.width,height:a.height,format:n.format});e._assignImage(u,t.level,r)}))},u=0;u<r.length;u++)a()}}else this.reset({width:0,height:0,mipmapLevel:0})}},{key:"image",get:function(){return 0===this._mipmaps.length?null:this._mipmaps[0]},set:function(t){this.mipmaps=t?[t]:[]}}]),e}(zi),Qi.FaceIndex=Ji,Xi=U((Wi=Qi).prototype,"isRGBE",[G],(function(){return!1})),qi=U(Wi.prototype,"_mipmapAtlas",[G],(function(){return null})),Ki=U(Wi.prototype,"_mipmapMode",[G],(function(){return Zi.NONE})),Yi=U(Wi.prototype,"_mipmaps",[G],(function(){return[]})),ji=Wi))||ji);function en(t,e){e(t.front,Ji.front),e(t.back,Ji.back),e(t.left,Ji.left),e(t.right,Ji.right),e(t.top,Ji.top),e(t.bottom,Ji.bottom)}ht.TextureCube=tn;var nn=function(){function t(){this._loading=new Ct,this._unpackers={".json":this.unpackJson}}var e=t.prototype;return e.unpackJson=function(t,e,i,r){var s=p(!0),o=null;if(Array.isArray(e)){(e=ti(e)).length!==t.length&&n(4915);for(var a=0;a<t.length;a++)s[t[a]+"@import"]=e[a]}else{var u=h($i),l=h($e);if(e.type===u&&e.data){var c=e.data;c.length!==t.length&&n(4915);for(var f=0;f<t.length;f++)s[t[f]+"@import"]=ei(u,{base:c[f][0],mipmaps:c[f][1]})}else{if(e.type!==l||!e.data)return void r(o=new Error("unmatched type pack!"),null);var _=e.data;_.length!==t.length&&n(4915);for(var d=0;d<t.length;d++)s[t[d]+"@import"]=_[d]}}r(o,s)},e.init=function(){this._loading.clear()},e.register=function(t,e){"object"==typeof t?c(this._unpackers,t):this._unpackers[t]=e},e.unpack=function(t,e,i,n,r){e?(0,this._unpackers[i])(t,e,n,r):r(new Error("package data is wrong!"))},e.load=function(t,e,i){var n=this;if(!t.isNative&&t.info&&t.info.packs)if(xt.has(t.id))i(null,xt.get(t.id));else{var r=t.info.packs,s=r.find((function(t){return n._loading.has(t.uuid)}));if(s)this._loading.get(s.uuid).push({onComplete:i,id:t.id});else{var o=r[0];this._loading.add(o.uuid,[{onComplete:i,id:t.id}]),f(t.config);var a=Lt(o.uuid,{ext:o.ext,bundle:t.config.name});ii.download(o.uuid,a,o.ext,t.options,(function(e,i){xt.remove(o.uuid),e&&_(e.message,e.stack),n.unpack(o.packedUuids,i,o.ext,t.options,(function(t,i){if(!t)for(var r in i)xt.add(r,i[r]);for(var s=n._loading.remove(o.uuid),a=0,u=s.length;a<u;a++){var h=s[a];if(e||t)h.onComplete(e||t);else{var l=i[h.id];l?h.onComplete(null,l):h.onComplete(new Error("can not retrieve data from package"))}}}))}))}}else ii.download(t.id,t.url,t.ext,t.options,i)},t}(),rn=new nn;function sn(t,e){var i=!1;t.progress||(t.progress={finish:0,total:t.input.length,canInvoke:!0},i=!0);var n=t.options,r=t.progress,s=[],o=r.total,a=n.__exclude__=n.__exclude__||Object.create(null);t.output=[],ni(t.input,(function(n,u){if(!n.isNative&&Nt.has(n.uuid)){var h=Nt.get(n.uuid);return n.content=h.addRef(),t.output.push(n),r.canInvoke&&t.dispatch("progress",++r.finish,r.total,n),void u()}rn.load(n,t.options,(function(h,l){h?t.isFinished||(!ut.assetManager.force||i?(_(h.message,h.stack),r.canInvoke=!1,e(h)):(t.output.push(n),r.canInvoke&&t.dispatch("progress",++r.finish,r.total,n))):t.isFinished||(n.file=l,t.output.push(n),n.isNative||(a[n.uuid]=!0,si(n.uuid,l,a,s,n.config),r.total=o+s.length),r.canInvoke&&t.dispatch("progress",++r.finish,r.total,n)),u()}))}),(function(){if(t.isFinished)return ri(t),void t.dispatch("error");if(s.length>0){var o=Pt.create({input:s,progress:r,options:n,onProgress:t.onProgress,onError:Pt.prototype.recycle,onComplete:function(n){var r;n||((r=t.output).push.apply(r,o.output),o.recycle()),i&&on(t),e(n)}});Ot.async(o)}else i&&on(t),e()}))}function on(t){for(var e=t.output,i=0,n=e.length;i<n;i++)e[i].content&&e[i].content.decRef(!1)}var an=function(t){function e(){return t.apply(this,arguments)||this}i(e,t);var n=e.prototype;return n.parse=function(t){var e=this._parseXML(t).documentElement;if("plist"!==e.tagName)return d(5100),{};for(var i=null,n=0,r=e.childNodes.length;n<r&&1!==(i=e.childNodes[n]).nodeType;n++);return this._parseNode(i)},n._parseNode=function(t){var e=null,i=t.tagName;if("dict"===i)e=this._parseDict(t);else if("array"===i)e=this._parseArray(t);else if("string"===i)if(1===t.childNodes.length)e=t.firstChild.nodeValue;else{e="";for(var n=0;n<t.childNodes.length;n++)e+=t.childNodes[n].nodeValue}else"false"===i?e=!1:"true"===i?e=!0:"real"===i?e=parseFloat(t.firstChild.nodeValue):"integer"===i&&(e=parseInt(t.firstChild.nodeValue,10));return e},n._parseArray=function(t){for(var e=[],i=0,n=t.childNodes.length;i<n;i++){var r=t.childNodes[i];1===r.nodeType&&e.push(this._parseNode(r))}return e},n._parseDict=function(t){for(var e={},i="",n=0,r=t.childNodes.length;n<r;n++){var s=t.childNodes[n];1===s.nodeType&&("key"===s.tagName?i=s.firstChild.nodeValue:e[i]=this._parseNode(s))}return e},e}(t("n",function(){function t(){this._parser=null,globalThis.DOMParser&&(this._parser=new DOMParser)}var e=t.prototype;return e.parse=function(t){return this._parseXML(t)},e._parseXML=function(t){if(this._parser)return this._parser.parseFromString(t,"text/xml");throw new Error("Dom parser is not supported in this platform!")},t}())),un=new an,hn=function(){function t(){this._parsing=new Ct,this._parsers={".png":this.parseImage,".jpg":this.parseImage,".bmp":this.parseImage,".jpeg":this.parseImage,".gif":this.parseImage,".ico":this.parseImage,".tiff":this.parseImage,".webp":this.parseImage,".image":this.parseImage,".pvr":this.parsePVRTex,".pkm":this.parsePKMTex,".astc":this.parseASTCTex,".plist":this.parsePlist,import:this.parseImport,".ccon":this.parseImport,".cconb":this.parseImport}}var e=t.prototype;return e.parseImage=function(t,e,i){t instanceof HTMLImageElement?i(null,t):createImageBitmap(t,{premultiplyAlpha:"none"}).then((function(t){i(null,t)}),(function(t){i(t,null)}))},e.parsePVRTex=function(t,e,i){var n=null,r=null;try{r=$e.parseCompressedTextures(t,0)}catch(t){g(n=t)}i(n,r)},e.parsePKMTex=function(t,e,i){var n=null,r=null;try{r=$e.parseCompressedTextures(t,1)}catch(t){g(n=t)}i(n,r)},e.parseASTCTex=function(t,e,i){var n=null,r=null;try{r=$e.parseCompressedTextures(t,2)}catch(t){g(n=t)}i(n,r)},e.parsePlist=function(t,e,i){var n=null,r=un.parse(t);r||(n=new Error("parse failed")),i(n,r)},e.parseImport=function(t,e,i){if(t){var n=null,r=null;try{n=oi(t,e)}catch(t){r=t}i(r,n)}else i(new Error(m(3702,e.__uuid__)))},e.init=function(){this._parsing.clear()},e.register=function(t,e){"object"==typeof t?c(this._parsers,t):this._parsers[t]=e},e.parse=function(t,e,i,n,r){var s=this,o=Ht.get(t);if(o)r(null,o);else{var a=this._parsing.get(t);if(a)a.push(r);else{var u=this._parsers[i];u?(this._parsing.add(t,[r]),u(e,n,(function(e,i){e?xt.remove(t):Bt(i)||Ht.add(t,i);for(var n=s._parsing.remove(t),r=0,o=n.length;r<o;r++)n[r](e,i)}))):r(null,e)}}},r(t,null,[{key:"instance",get:function(){return this._instance||(this._instance=new t),this._instance}}]),t}();hn._instance=void 0;var ln=t("a9",hn.instance);function pn(t,e){var i=!1;t.progress||(t.progress={finish:0,total:t.input.length,canInvoke:!0},i=!0);var n=t.options,r=t.progress;n.__exclude__=n.__exclude__||Object.create(null),t.output=[],ni(t.input,(function(s,o){var a=Pt.create({input:s,onProgress:t.onProgress,options:n,progress:r,onComplete:function(n,u){n&&!t.isFinished&&(!ut.assetManager.force||i?(_(n.message,n.stack),r.canInvoke=!1,e(n)):r.canInvoke&&t.dispatch("progress",++r.finish,r.total,s)),t.output.push(u),a.recycle(),o(null)}});cn.async(a)}),(function(){if(n.__exclude__=null,t.isFinished)return ri(t),void t.dispatch("error");ci(t),ri(t),e()}))}var cn=new Ft("loadOneAsset",[function(t,e){var i=t.output=t.input,n=i.options,r=i.isNative,s=i.uuid,o=i.file,a=n.reloadAsset;o||!a&&!r&&Nt.has(s)?e():rn.load(i,t.options,(function(t,n){i.file=n,e(t)}))},function(t,e){var i=t.output=t.input,n=t.progress,r=t.options.__exclude__,s=i.id,o=i.file,a=i.options;if(i.isNative)ln.parse(s,o,i.ext,a,(function(r,o){r?e(r):(i.content=o,n.canInvoke&&t.dispatch("progress",++n.finish,n.total,i),xt.remove(s),Ht.remove(s),e())}));else{var u=i.uuid;if(u in r){var h=r[u],l=h.finish,p=h.content,c=h.err,f=h.callbacks;n.canInvoke&&t.dispatch("progress",++n.finish,n.total,i),l||ai(u,u,r)?(p&&p.addRef(),i.content=p,e(c)):f.push({done:e,item:i})}else if(!a.reloadAsset&&Nt.has(u)){var _=Nt.get(u);i.content=_.addRef(),n.canInvoke&&t.dispatch("progress",++n.finish,n.total,i),e()}else a.__uuid__=u,ln.parse(s,o,"import",a,(function(i,n){i?e(i):fn(t,n,e)}))}}]);function fn(t,e,i){var r=t.input,s=t.progress,o=r,a=o.uuid,u=o.id,h=o.options,l=o.config,p=h.cacheAsset,c=[];e.addRef&&e.addRef(),si(a,e,Object.create(null),c,l),s.canInvoke&&t.dispatch("progress",++s.finish,s.total+=c.length,r);var f=t.options.__exclude__[a]={content:e,finish:!1,callbacks:[{done:i,item:r}]},_=Pt.create({input:c,options:t.options,onProgress:t.onProgress,onError:Pt.prototype.recycle,progress:s,onComplete:function(t){if(e.decRef&&e.decRef(!1),f.finish=!0,f.err=t,!t){for(var i,r=Array.isArray(_.output)?_.output:[_.output],s=Object.create(null),o=v(r);!(i=o()).done;){var h=i.value;h&&(s[h instanceof kt?h._uuid+"@import":a+"@native"]=h)}ui(a,e,s);try{"function"!=typeof e.onLoaded||hi.has(e)||li.has(e)||(e.onLoaded(),hi.add(e))}catch(t){n(16352,a,t.message,t.stack)}xt.remove(u),Ht.remove(u),pi(a,e,p),_.recycle()}for(var l=f.callbacks,c=0,d=l.length;c<d;c++){var g=l[c];e.addRef&&e.addRef(),g.item.content=e,g.done(t)}l.length=0}});Ut.async(_)}function _n(t,e){var i=t.options,n=Object.create(null),r=Object.create(null);for(var s in i)switch(s){case Gt.PATH:case Gt.UUID:case Gt.DIR:case Gt.SCENE:case Gt.URL:break;case"__requestType__":case"__isNative__":case"ext":case"type":case"__nativeName__":case"audioLoadMode":case"bundle":n[s]=i[s];break;case"__exclude__":case"__outputAsArray__":r[s]=i[s];break;default:n[s]=i[s],r[s]=i[s]}t.options=r;var o=Pt.create({input:t.input,options:n}),a=null;try{t.output=t.source=Vt.sync(o)}catch(t){a=t;for(var u=0,h=o.output.length;u<h;u++)o.output[u].recycle()}o.recycle(),e(a)}var dn=function(){function t(){this.uuid="",this.overrideUuid="",this.url="",this.ext=".json",this.content=null,this.file=null,this.info=null,this.config=null,this.isNative=!1,this.options=Object.create(null),this._id=""}return t.create=function(){return 0!==t._deadPool.length?t._deadPool.pop():new t},t.prototype.recycle=function(){t._deadPool.length!==t.MAX_DEAD_NUM&&(this._id="",this.uuid="",this.overrideUuid="",this.url="",this.ext=".json",this.content=null,this.file=null,this.info=null,this.config=null,this.isNative=!1,this.options=Object.create(null),t._deadPool.push(this))},r(t,[{key:"id",get:function(){return this._id||(this._id=(this.overrideUuid||this.uuid)+"@"+(this.isNative?"native":"import")),this._id}}]),t}();dn.MAX_DEAD_NUM=500,dn._deadPool=[];var gn=[];function mn(t){var e=t.options,i=Array.isArray(t.input)?t.input:[t.input];t.output=[];for(var n=function(){var n=i[r],s=dn.create(),o=null,a=null;if("string"==typeof n&&((n=Object.create(null))[e.__requestType__||Gt.UUID]=i[r]),"object"==typeof n){y(n,e),n.preset&&y(n,zt[n.preset]);var u=function(){var t;switch(h){case Gt.UUID:var e,r=s.uuid=qt(n.uuid);if(!n.bundle){var u=Wt.find((function(t){return!!t.getAssetInfo(r)}));n.bundle=u&&u.name}if(Wt.has(n.bundle)){if(o=Wt.get(n.bundle).config,(a=o.getAssetInfo(r))&&a.redirect){if(!Wt.has(a.redirect))throw new Error("Please load bundle "+a.redirect+" first");o=Wt.get(a.redirect).config,a=o.getAssetInfo(r)}s.config=o,s.info=a}s.ext=n.ext||(null==(e=a)?void 0:e.extension)||".json";break;case"__requestType__":case"ext":case"bundle":case"preset":case"type":break;case Gt.DIR:if(Wt.has(n.bundle)){Wt.get(n.bundle).config.getDirWithPath(n.dir,n.type,gn);for(var l,p=v(gn);!(l=p()).done;){var c=l.value;i.push({uuid:c.uuid,__isNative__:!1,ext:c.extension||".json",bundle:n.bundle})}gn.length=0}s.recycle(),s=null;break;case Gt.PATH:if(Wt.has(n.bundle)){if(o=Wt.get(n.bundle).config,(a=o.getInfoWithPath(n.path,n.type))&&a.redirect){if(!Wt.has(a.redirect))throw new Error("you need to load bundle "+a.redirect+" first");o=Wt.get(a.redirect).config,a=o.getAssetInfo(a.uuid)}if(!a)throw s.recycle(),new Error("Bundle "+n.bundle+" doesn't contain "+n.path);s.config=o,s.uuid=a.uuid,s.info=a}s.ext=n.ext||(null==(t=a)?void 0:t.extension)||".json";break;case Gt.SCENE:if(!n.bundle){var f=Wt.find((function(t){return!!t.getSceneInfo(n.scene)}));n.bundle=f&&f.name}if(Wt.has(n.bundle)){if(o=Wt.get(n.bundle).config,(a=o.getSceneInfo(n.scene))&&a.redirect){if(!Wt.has(a.redirect))throw new Error("you need to load bundle "+a.redirect+" first");o=Wt.get(a.redirect).config,a=o.getAssetInfo(a.uuid)}if(!a)throw s.recycle(),new Error("Bundle "+o.name+" doesn't contain scene "+n.scene);s.config=o,s.uuid=a.uuid,s.info=a}break;case"__isNative__":s.isNative=n.__isNative__;break;case Gt.URL:s.url=n.url,s.uuid=n.uuid||n.url,s.ext=n.ext||Xt(n.url),s.isNative=void 0===n.__isNative__||n.__isNative__;break;default:s.options[h]=n[h]}if(!s)return 1};for(var h in n)if(u())break}if(!s)return 1;if(t.output.push(s),!s.uuid&&!s.url)throw new Error("Can not parse this input:"+JSON.stringify(n))},r=0;r<i.length;r++)n();return null}function vn(t){for(var e=t.output=t.input,i=function(){var t=e[n];if(jt.has(t.uuid)){var i=jt.get(t.uuid),r=Wt.find((function(t){return!!t.getAssetInfo(i)}));if(r){var s;t.overrideUuid=i;var o=r.config,a=o.getAssetInfo(i);if(a&&a.redirect){if(!Wt.has(a.redirect))throw new Error("Please load bundle "+a.redirect+" first");a=(o=Wt.get(a.redirect).config).getAssetInfo(i)}t.config=o,t.info=a,t.ext=t.isNative?t.ext:(null==(s=a)?void 0:s.extension)||".json"}else d(16201,i,t.uuid)}},n=0;n<e.length;n++)i()}function yn(t){for(var e=t.output=t.input,i=0;i<e.length;i++){var n=e[i];if(!n.url){var r,s,o=n.config;s=n.isNative?o&&o.nativeBase?o.base+o.nativeBase:ut.assetManager.generalNativeBase:o&&o.importBase?o.base+o.importBase:ut.assetManager.generalImportBase;var a=n.overrideUuid||n.uuid,u="";n.info&&(u=n.isNative?n.info.nativeVer?"."+n.info.nativeVer:"":n.info.ver?"."+n.info.ver:""),r=".ttf"===n.ext?s+"/"+a.slice(0,2)+"/"+a+u+"/"+n.options.__nativeName__:s+"/"+a.slice(0,2)+"/"+a+u+n.ext,n.url=r}}return null}var bn=b.querySettings.bind(b),In=I.ASSETS,Tn="asset-missing",En=t("a7",function(){function t(){this.pipeline=Ut.append(_n).append(pn),this.fetchPipeline=Ot.append(_n).append(sn),this.transformPipeline=Vt.append(mn).append(vn).append(yn),this.bundles=Wt,this.assets=Nt,this.assetsOverrideMap=jt,this.generalImportBase="",this.generalNativeBase="",this.dependUtil=Ze,this.force=ft,this.allowImageBitmap=!1,this.utils=Qt,this.downloader=ii,this.parser=ln,this.packManager=rn,this.cacheAsset=!0,this.cacheManager=null,this.presets=zt,this.factory=_i,this.preprocessPipe=_n,this.fetchPipe=sn,this.loadPipe=pn,this.references=Jt,this._releaseManager=di,this._files=xt,this._parsed=Ht,this._parsePipeline=null,this._projectBundles=[],this._eventTarget=new T}var e=t.prototype;return e.getReleaseManager=function(){return this._releaseManager},e.onAssetMissing=function(t,e){this._eventTarget.on(Tn,t,e)},e.offAssetMissing=function(t,e){this._eventTarget.off(Tn,t,e)},e.dispatchAssetMissing=function(t,e,i,n){this._eventTarget.emit(Tn,t,e,i,n)},e.init=function(t){void 0===t&&(t={});var e=t.server||bn(In,"server")||"",i=t.bundleVers||bn(In,"bundleVers")||{},n=t.remoteBundles||bn(In,"remoteBundles")||[],r=t.downloadMaxConcurrency||bn(In,"downloadMaxConcurrency");r&&r>0&&(this.downloader.maxConcurrency=r),this._files.clear(),this._parsed.clear(),this._releaseManager.init(),this.assets.clear(),this.bundles.clear(),this.packManager.init(),this.downloader.init(e,i,n),this.parser.init(),this.dependUtil.init();var s=t.importBase||bn(In,"importBase")||"";s&&s.endsWith("/")&&(s=s.substring(0,s.length-1));var o=t.nativeBase||bn(In,"nativeBase")||"";o&&o.endsWith("/")&&(o=o.substring(0,o.length-1)),this.generalImportBase=s,this.generalNativeBase=o,this._projectBundles=bn(In,"projectBundles")||[];var a=bn(In,"assetsOverrides")||{};for(var u in a)this.assetsOverrideMap.set(u,a[u])},e.getBundle=function(t){return Wt.get(t)||null},e.removeBundle=function(t){t._destroy(),Wt.remove(t.name)},e.loadAny=function(t,e,i,n){var r=yi(e,i,n),s=r.options,o=r.onProgress,a=r.onComplete;s.preset=s.preset||"default",t=Array.isArray(t)?t.slice():t;var u=Pt.create({input:t,onProgress:o,onComplete:fi(a),options:s});Ut.async(u)},e.preloadAny=function(t,e,i,n){var r=yi(e,i,n),s=r.options,o=r.onProgress,a=r.onComplete;s.preset=s.preset||"preload",t=Array.isArray(t)?t.slice():t;var u=Pt.create({input:t,onProgress:o,onComplete:fi(a),options:s});Ot.async(u)},e.loadRemote=function(t,e,i){var n=yi(e,void 0,i),r=n.options,s=n.onComplete;r.reloadAsset||!this.assets.has(t)?(r.__isNative__=!0,r.preset=r.preset||"remote",this.loadAny({url:t},r,null,(function(e,i){e?(_(e.message,e.stack),s&&s(e,i)):_i.create(t,i,r.ext||Xt(t),r,(function(t,e){s&&s(t,e)}))}))):fi(s)(null,this.assets.get(t))},e.loadBundle=function(t,e,i){var n=yi(e,void 0,i),r=n.options,s=n.onComplete,o=Kt(t);this.bundles.has(o)?fi(s)(null,this.getBundle(o)):(r.preset=r.preset||"bundle",r.ext="bundle",r.__isNative__=!0,this.loadAny({url:t},r,null,(function(e,i){e?(_(e.message,e.stack),s&&s(e,i)):_i.create(t,i,"bundle",r,(function(t,e){s&&s(t,e)}))})))},e.releaseAsset=function(t){di.tryRelease(t,!0)},e.releaseUnusedAssets=function(){Nt.forEach((function(t){di.tryRelease(t)}))},e.releaseAll=function(){Nt.forEach((function(t){di.tryRelease(t,!0)}))},e.loadWithJson=function(){throw new Error("Only valid in Editor")},r(t,[{key:"files",get:function(){return this._files}},{key:"main",get:function(){return Wt.get(Yt.MAIN)||null}},{key:"resources",get:function(){return Wt.get(Yt.RESOURCES)||null}}],[{key:"instance",get:function(){return this._instance||(this._instance=new t),this._instance}}]),t}());En._instance=void 0,En.Pipeline=Ft,En.Task=Pt,En.Cache=Ct,En.RequestItem=dn,En.Bundle=gi,En.BuiltinBundleName=Yt,En.CacheManager=function(){this.cacheDir=void 0,this.cacheEnabled=void 0,this.autoClear=void 0,this.cacheInterval=void 0,this.deleteInterval=void 0,this.cachedFiles=void 0},En.Downloader=mi,En.Parser=hn,En.DependUtil=vi;var Dn=t("l",ut.assetManager=En.instance);ut.AssetManager=En;var Rn,Sn,Mn,An,wn,kn=t("a8",function(){function t(){this._resources={},this._materialsToBeCompiled=[]}var e=t.prototype;return e.init=function(){for(var t=this._resources,e=new Uint8Array(16),i=new Uint8Array(16),n=new Uint8Array(16),r=new Uint8Array(16),s=new Uint8Array(16),o=0,a=0;a<4;a++)e[o]=0,e[o+1]=0,e[o+2]=0,e[o+3]=255,i[o]=0,i[o+1]=0,i[o+2]=0,i[o+3]=0,n[o]=119,n[o+1]=119,n[o+2]=119,n[o+3]=255,r[o]=255,r[o+1]=255,r[o+2]=255,r[o+3]=255,s[o]=127,s[o+1]=127,s[o+2]=255,s[o+3]=255,o+=4;var u=new Uint8Array(1024);o=0;for(var h=0;h<256;h++)u[o]=221,u[o+1]=221,u[o+2]=221,u[o+3]=255,o+=4;o=0;for(var l=0;l<8;l++){for(var p=0;p<8;p++)u[o]=85,u[o+1]=85,u[o+2]=85,u[o+3]=255,o+=4;o+=32}o+=32;for(var c=0;c<8;c++){for(var f=0;f<8;f++)u[o]=85,u[o+1]=85,u[o+2]=85,u[o+3]=255,o+=4;o+=32}var _={width:2,height:2,_data:e,_compressed:!1,format:Ye.RGBA8888},d={width:2,height:2,_data:i,_compressed:!1,format:Ye.RGBA8888},g={width:2,height:2,_data:n,_compressed:!1,format:Ye.RGBA8888},m={width:2,height:2,_data:r,_compressed:!1,format:Ye.RGBA8888},v={width:2,height:2,_data:s,_compressed:!1,format:Ye.RGBA8888},y={width:16,height:16,_data:u,_compressed:!1,format:Ye.RGBA8888},b=new $e(_),I=new $i;I._uuid="black-texture",I.image=b,t[I._uuid]=I;var T=new $e(d),E=new $i;E._uuid="empty-texture",E.image=T,t[E._uuid]=E;var D=new tn;D._uuid="black-cube-texture",D.setMipFilter(Je.NEAREST),D.image={front:new $e(_),back:new $e(_),left:new $e(_),right:new $e(_),top:new $e(_),bottom:new $e(_)},t[D._uuid]=D;var R=new $e(g),S=new $i;S._uuid="grey-texture",S.image=R,t[S._uuid]=S;var M=new tn;M._uuid="grey-cube-texture",M.setMipFilter(Je.NEAREST),M.image={front:new $e(g),back:new $e(g),left:new $e(g),right:new $e(g),top:new $e(g),bottom:new $e(g)},t[M._uuid]=M;var A=new $e(m),w=new $i;w._uuid="white-texture",w.image=A,t[w._uuid]=w;var k=new tn;k._uuid="white-cube-texture",k.setMipFilter(Je.NEAREST),k.image={front:new $e(m),back:new $e(m),left:new $e(m),right:new $e(m),top:new $e(m),bottom:new $e(m)},t[k._uuid]=k;var C=new $e(v),x=new $i;x._uuid="normal-texture",x.image=C,t[x._uuid]=x;var L=new $e(y),P=new $i;P._uuid="default-texture",P.image=L,t[P._uuid]=P;var O=new tn;if(O.setMipFilter(Je.NEAREST),O._uuid="default-cube-texture",O.image={front:new $e(y),back:new $e(y),left:new $e(y),right:new $e(y),top:new $e(y),bottom:new $e(y)},t[O._uuid]=O,ut.SpriteFrame){var N=new ut.SpriteFrame,H=b,B=new $i;B.image=H,N.texture=B,N._uuid="default-spriteframe",t[N._uuid]=N}},e.addAsset=function(t,e){this._resources[t]=e},e.get=function(t){return this._resources[t]},e.loadBuiltinAssets=function(){var t=this,e=b.querySettings(I.ENGINE,"builtinAssets");if(!e)return Promise.resolve();var i=this._resources;return new Promise((function(n,r){Dn.loadBundle(Yt.INTERNAL,(function(s){s?r(s):Dn.loadAny(e,(function(e,s){e?r(e):(s.forEach((function(e){i[e.name]=e,di.addIgnoredAsset(e),e instanceof ut.Material&&t._materialsToBeCompiled.push(e)})),n())}))}))}))},e.compileBuiltinMaterial=function(){for(var t=0;t<this._materialsToBeCompiled.length;++t)for(var e=this._materialsToBeCompiled[t],i=0;i<e.passes.length;++i)e.passes[i].tryCompile();this._materialsToBeCompiled.length=0},t}()),Cn=t("d",ut.builtinResMgr=new kn),xn=t("g",(Rn=new Map,Sn=0,function(t){return"number"==typeof t?t:(Rn.has(t)||(Rn.set(t,1<<Sn),Sn++),Rn.get(t))})),Ln=4227858432,Pn=66060288,On=1044480,Nn=t("x",(function(t,e,i,n){return void 0===n&&(n=0),e<<26&Ln|t<<20&Pn|i<<12&On|4095&n})),Hn=t("J",(function(t){return(t&Ln)>>>26})),Bn=t("y",(function(t){return(t&Pn)>>>20})),Fn=t("z",(function(t){return(t&On)>>>12})),Un=t("H",(function(t){return 4095&t})),Gn=t("w",(function(t,e){return 67108863&t|e<<26&Ln})),Vn=t("O",((Mn={})[fe.UNKNOWN]=function(t,e,i){return void 0===i&&(i=0),d(12010,i)},Mn[fe.INT]=function(t,e,i){return void 0===i&&(i=0),t[i]},Mn[fe.INT2]=function(t,e,i){return void 0===i&&(i=0),j.fromArray(e,t,i)},Mn[fe.INT3]=function(t,e,i){return void 0===i&&(i=0),W.fromArray(e,t,i)},Mn[fe.INT4]=function(t,e,i){return void 0===i&&(i=0),X.fromArray(e,t,i)},Mn[fe.FLOAT]=function(t,e,i){return void 0===i&&(i=0),t[i]},Mn[fe.FLOAT2]=function(t,e,i){return void 0===i&&(i=0),j.fromArray(e,t,i)},Mn[fe.FLOAT3]=function(t,e,i){return void 0===i&&(i=0),W.fromArray(e,t,i)},Mn[fe.FLOAT4]=function(t,e,i){return void 0===i&&(i=0),X.fromArray(e,t,i)},Mn[fe.MAT3]=function(t,e,i){return void 0===i&&(i=0),q.fromArray(e,t,i)},Mn[fe.MAT4]=function(t,e,i){return void 0===i&&(i=0),K.fromArray(e,t,i)},Mn)),zn=t("R",((An={})[fe.UNKNOWN]=function(t,e,i){return void 0===i&&(i=0),d(12010,i)},An[fe.INT]=function(t,e,i){return void 0===i&&(i=0),t[i]=e},An[fe.INT2]=function(t,e,i){return void 0===i&&(i=0),j.toArray(t,e,i)},An[fe.INT3]=function(t,e,i){return void 0===i&&(i=0),W.toArray(t,e,i)},An[fe.INT4]=function(t,e,i){return void 0===i&&(i=0),X.toArray(t,e,i)},An[fe.FLOAT]=function(t,e,i){return void 0===i&&(i=0),t[i]=e},An[fe.FLOAT2]=function(t,e,i){return void 0===i&&(i=0),j.toArray(t,e,i)},An[fe.FLOAT3]=function(t,e,i){return void 0===i&&(i=0),W.toArray(t,e,i)},An[fe.FLOAT4]=function(t,e,i){return void 0===i&&(i=0),X.toArray(t,e,i)},An[fe.MAT3]=function(t,e,i){return void 0===i&&(i=0),q.toArray(t,e,i)},An[fe.MAT4]=function(t,e,i){return void 0===i&&(i=0),K.toArray(t,e,i)},An)),jn=(t("Q",((wn={})[fe.INT]=function(t){return"number"==typeof t},wn[fe.FLOAT]=function(t){return"number"==typeof t},wn[fe.INT2]=function(t){return!!(t instanceof j)},wn[fe.FLOAT2]=function(t){return!!(t instanceof j)},wn[fe.INT3]=function(t){return!!(t instanceof W)},wn[fe.FLOAT3]=function(t){return!!(t instanceof W)},wn[fe.INT4]=function(t){return!!(t instanceof X)},wn[fe.FLOAT4]=function(t){return!!(t instanceof X||t instanceof Y||t instanceof Q)},wn[fe.MAT3]=function(t){return!!(t instanceof q)},wn[fe.MAT4]=function(t){return!!(t instanceof K)},wn)),[Object.freeze([0]),Object.freeze([0,0]),Object.freeze([0,0,0,0]),Object.freeze([1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1])]);function Wn(t){switch(t){case fe.BOOL:case fe.INT:case fe.UINT:case fe.FLOAT:return jn[0];case fe.BOOL2:case fe.INT2:case fe.UINT2:case fe.FLOAT2:return jn[1];case fe.BOOL4:case fe.INT4:case fe.UINT4:case fe.FLOAT4:return jn[2];case fe.MAT4:return jn[3];case fe.SAMPLER2D:return"default-texture";case fe.SAMPLER_CUBE:return"default-cube-texture";case fe.SAMPLER2D_ARRAY:return"default-array-texture";case fe.SAMPLER3D:return"default-3d-texture"}return jn[0]}function Xn(t){switch(t){case fe.SAMPLER2D:return"-texture";case fe.SAMPLER_CUBE:return"-cube-texture";case fe.SAMPLER2D_ARRAY:return"-array-texture";case fe.SAMPLER3D:return"-3d-texture";default:return"-unknown"}}function qn(t,e){for(var i=Object.entries(e),n=!1,r=0;r<i.length;r++)t[i[r][0]]!==i[r][1]&&(t[i[r][0]]=i[r][1],n=!0);return n}function Kn(t,e){switch(t.type){case"boolean":return"number"==typeof e?e.toString():e?"1":"0";case"string":return void 0!==e?e:t.options[0];case"number":return void 0!==e?e.toString():t.range[0].toString();default:return d(16369),"-1"}}function Yn(t,e){for(var i=[],n=0;n<e.length;n++){var r=e[n],s=r.name,o=t[s],a=Kn(r,o),u=!o||"0"===o;i.push({name:s,value:a,isDefault:u})}return i}function Qn(t,e){return t+e.reduce((function(t,e){return e.isDefault?t:t+"|"+e.name+e.value}),"")}function Jn(t,e){for(var i=0;i<t.length;i++){var n=t[i];if("!"===n[0]){if(e[n.slice(1)])return!1}else if(!e[n])return!1}return!0}function Zn(t,e,i){for(var n=[],r=t.attributes,s=0;s<r.length;s++)Jn(r[s].defines,i)&&n.push(e[s]);return n}function $n(t,e){var i=t.defines;if(t.uber){for(var n="",r=0;r<i.length;r++){var s=i[r],o=e[s.name];if(o&&s._map){var a=s._map(o);n+=""+s._offset+a+"|"}}return""+n+t.hash}for(var u=0,h=0;h<i.length;h++){var l=i[h],p=e[l.name];p&&l._map&&(u|=l._map(p)<<l._offset)}return u.toString(16)+"|"+t.hash}var tr=new Map;function er(t,e){if(e.count)return t+_e(e.type)*e.count;var i=tr.get(e.name);return void 0!==i?t+_e(e.type)*i:(n(16345,e.name),t)}function ir(t){return t.reduce(er,0)}function nr(t){for(var e={},i=0;i<t.blocks.length;i++)for(var n=t.blocks[i],r=n.members,s=0,o=0;o<r.length;o++){var a=r[o];e[a.name]=Nn(n.binding,a.type,a.count,s),s+=(_e(a.type)>>2)*a.count}for(var u=0;u<t.samplerTextures.length;u++){var h=t.samplerTextures[u];e[h.name]=Nn(h.binding,h.type,h.count)}return e}function rr(t){return Math.ceil(Math.log2(Math.max(t,2)))}function sr(t){for(var e=0,i=function(){var i=t.defines[n],r=1;if("number"===i.type){var s=i.range;r=rr(s[1]-s[0]+1),i._map=function(t){return t-s[0]}}else"string"===i.type?(r=rr(i.options.length),i._map=function(t){return Math.max(0,i.options.findIndex((function(e){return e===t})))}):"boolean"===i.type&&(i._map=function(t){return t?1:0});i._offset=e,e+=r},n=0;n<t.defines.length;n++)i();for(var r in e>31&&(t.uber=!0),t.constantMacros="",t.builtins.statistics)t.constantMacros+="#define "+r+" "+t.builtins.statistics[r]+"\n"}function or(t){return Object.keys(t).reduce((function(e,i){return e.reduce((function(e,n){for(var r=t[i],s=0;s<r.length;++s){var o=E({},n);o[i]=r[s],e.push(o)}return e}),[])}),[{}])}function ar(t){for(var e=0;e<t.techniques.length;e++)for(var i=t.techniques[e],n=0;n<i.passes.length;n++){var r=i.passes[n];void 0!==r.propertyIndex&&void 0===r.properties&&(r.properties=i.passes[r.propertyIndex].properties)}}tr.set("cc_joints",dt.LAYOUT.members[0].count),tr.set("cc_lightPos",gt.LIGHTS_PER_PASS),tr.set("cc_lightColor",gt.LIGHTS_PER_PASS),tr.set("cc_lightSizeRangeAngle",gt.LIGHTS_PER_PASS),tr.set("cc_lightDir",gt.LIGHTS_PER_PASS),tr.set("cc_lightBoundingSizeVS",gt.LIGHTS_PER_PASS);var ur=new de;function hr(t,e,i,n){for(var r=t.builtins[n],s=[],o=function(){var t=r.blocks[a],e=i.layouts[t.name],n=e&&i.bindings.find((function(t){return t.binding===e.binding}));if(!(e&&n&&n.descriptorType&Ce))return d(16348,t.name),1;s.push(e)},a=0;a<r.blocks.length;a++)o();Array.prototype.unshift.apply(e.shaderInfo.blocks,s);for(var u=[],h=function(){var t=r.samplerTextures[l],e=i.layouts[t.name],n=e&&i.bindings.find((function(t){return t.binding===e.binding}));if(!(e&&n&&n.descriptorType&xe))return d(16349,t.name),1;u.push(e)},l=0;l<r.samplerTextures.length;l++)h();Array.prototype.unshift.apply(e.shaderInfo.samplerTextures,u)}var lr=function(){function t(){this._templates={},this._cache={},this._templateInfos={}}var e=t.prototype;return e.register=function(t){for(var e=0;e<t.shaders.length;e++)this.define(t.shaders[e]).effectName=t.name;for(var i=0;i<t.techniques.length;i++)for(var n=t.techniques[i],r=0;r<n.passes.length;r++){var s=n.passes[r];void 0!==s.propertyIndex&&void 0===s.properties&&(s.properties=n.passes[s.propertyIndex].properties)}},e.define=function(t){var e=this._templates[t.name];if(e&&e.hash===t.hash)return e;var i=E({},t);if(sr(i),this._templates[t.name]=i,!this._templateInfos[i.hash]){var n={};n.samplerStartBinding=i.blocks.length,n.shaderInfo=new ge,n.blockSizes=[],n.bindings=[];for(var r=0;r<i.blocks.length;r++){var s=i.blocks[r];n.blockSizes.push(ir(s.members)),n.bindings.push(new me(s.binding,ve.UNIFORM_BUFFER,1,s.stageFlags)),n.shaderInfo.blocks.push(new ye(mt.MATERIAL,s.binding,s.name,s.members.map((function(t){return new be(t.name,t.type,t.count)})),1))}for(var o=0;o<i.samplerTextures.length;o++){var a=i.samplerTextures[o];n.bindings.push(new me(a.binding,ve.SAMPLER_TEXTURE,a.count,a.stageFlags)),n.shaderInfo.samplerTextures.push(new Ie(mt.MATERIAL,a.binding,a.name,a.type,a.count))}for(var u=0;u<i.samplers.length;u++){var h=i.samplers[u];n.bindings.push(new me(h.binding,ve.SAMPLER,h.count,h.stageFlags)),n.shaderInfo.samplers.push(new Te(mt.MATERIAL,h.binding,h.name,h.count))}for(var l=0;l<i.textures.length;l++){var p=i.textures[l];n.bindings.push(new me(p.binding,ve.TEXTURE,p.count,p.stageFlags)),n.shaderInfo.textures.push(new Ee(mt.MATERIAL,p.binding,p.name,p.type,p.count))}for(var c=0;c<i.buffers.length;c++){var f=i.buffers[c];n.bindings.push(new me(f.binding,ve.STORAGE_BUFFER,1,f.stageFlags)),n.shaderInfo.buffers.push(new De(mt.MATERIAL,f.binding,f.name,1,f.memoryAccess))}for(var _=0;_<i.images.length;_++){var d=i.images[_];n.bindings.push(new me(d.binding,ve.STORAGE_IMAGE,d.count,d.stageFlags)),n.shaderInfo.images.push(new Re(mt.MATERIAL,d.binding,d.name,d.type,d.count,d.memoryAccess))}for(var g=0;g<i.subpassInputs.length;g++){var m=i.subpassInputs[g];n.bindings.push(new me(m.binding,ve.INPUT_ATTACHMENT,m.count,m.stageFlags)),n.shaderInfo.subpassInputs.push(new Se(mt.MATERIAL,m.binding,m.name,m.count))}n.gfxAttributes=[];for(var v=0;v<i.attributes.length;v++){var y=i.attributes[v];n.gfxAttributes.push(new Me(y.name,y.format,y.isNormalized,0,y.isInstanced,y.location))}hr(i,n,vt,"locals"),n.shaderInfo.stages.push(new Ae(we.VERTEX,"")),n.shaderInfo.stages.push(new Ae(we.FRAGMENT,"")),n.handleMap=nr(i),n.setLayouts=[],this._templateInfos[i.hash]=n}return i},e.getTemplate=function(t){return this._templates[t]},e.getTemplateInfo=function(t){var e=this._templates[t].hash;return this._templateInfos[e]},e.getDescriptorSetLayout=function(t,e,i){void 0===i&&(i=!1);var n=this._templates[e],r=this._templateInfos[n.hash];return r.setLayouts.length||(ur.bindings=r.bindings,r.setLayouts[mt.MATERIAL]=t.createDescriptorSetLayout(ur),ur.bindings=vt.bindings,r.setLayouts[mt.LOCAL]=t.createDescriptorSetLayout(ur)),r.setLayouts[i?mt.LOCAL:mt.MATERIAL]},e.hasProgram=function(t){return void 0!==this._templates[t]},e.getKey=function(t,e){return $n(this._templates[t],e)},e.destroyShaderByDefines=function(t){var e=this,i=Object.keys(t);if(i.length)for(var n=i.map((function(e){var i=t[e];return"boolean"==typeof i&&(i=i?"1":"0"),new RegExp(""+e+i)})),r=Object.keys(this._cache).filter((function(t){return n.every((function(i){return i.test(e._cache[t].name)}))})),s=0;s<r.length;s++){var o=r[s],a=this._cache[o];D("destroyed shader "+a.name),a.destroy(),delete this._cache[o]}},e.getGFXShader=function(t,e,i,r,s){Object.assign(i,r.macros),s||(s=this.getKey(e,i));var o=this._cache[s];if(o)return o;var a=this._templates[e],u=this._templateInfos[a.hash];u.pipelineLayout||(this.getDescriptorSetLayout(t,e),hr(a,u,yt,"globals"),u.setLayouts[mt.GLOBAL]=r.descriptorSetLayout,u.pipelineLayout=t.createPipelineLayout(new ke(u.setLayouts)));var h=Yn(i,a.defines),l=r.constantMacros+a.constantMacros+h.reduce((function(t,e){return t+"#define "+e.name+" "+e.value+"\n"}),""),p=a.glsl3,c=pr(t);c?p=a[c]:n(16346),u.shaderInfo.stages[0].source=l+p.vert,u.shaderInfo.stages[1].source=l+p.frag,u.shaderInfo.attributes=Zn(a,u.gfxAttributes,i),u.shaderInfo.name=Qn(e,h);var f=u.shaderInfo;return this._cache[s]=t.createShader(f)},t}();function pr(t){switch(t.gfxAPI){case he.GLES2:case he.WEBGL:return"glsl1";case he.GLES3:case he.WEBGL2:return"glsl3";default:return"glsl4"}}var cr=t("L",new lr);ut.programLib=cr;var fr,_r=t("U",function(){function t(t){this.instances=[],this.hasPendingModels=!1,this.dynamicOffsets=[],this._device=t.device,this.pass=t}var e=t.prototype;return e.destroy=function(){this.instances.forEach((function(t){t.vb.destroy(),t.ia.destroy()})),this.instances.length=0},e.merge=function(t,e,i){void 0===i&&(i=null);var n=t.instancedAttributeBlock,r=n.buffer.length;if(r){var s=t.inputAssembler,o=t.descriptorSet,a=o.getTexture(bt),u=o.getTexture(It),h=o.getTexture(Tt),l=t.useReflectionProbeType,p=i;p||(p=t.shaders[e]);for(var c=t.descriptorSet,f=0;f<this.instances.length;++f){var _,d,g=this.instances[f];if(!((null==(_=g.ia.indexBuffer)?void 0:_.objectID)!==(null==(d=s.indexBuffer)?void 0:d.objectID)||g.count>=1024)&&g.lightingMap.objectID===a.objectID&&g.useReflectionProbeType===l&&g.reflectionProbeCubemap.objectID===u.objectID&&g.reflectionProbePlanarMap.objectID===h.objectID&&g.stride===r){if(g.count>=g.capacity){g.capacity<<=1;var m=g.stride*g.capacity,v=g.data;g.data=new Uint8Array(m),g.data.set(v),g.vb.resize(m)}return g.shader=p,g.descriptorSet=c,g.data.set(n.buffer,g.stride*g.count++),void(this.hasPendingModels=!0)}}for(var y=this._device.createBuffer(new Le(Pe.VERTEX|Pe.TRANSFER_DST,Oe.HOST|Oe.DEVICE,32*r,r)),b=new Uint8Array(32*r),I=s.vertexBuffers.slice(),T=s.attributes.slice(),E=s.indexBuffer,D=0;D<n.attributes.length;D++){var R=n.attributes[D],S=new Me(R.name,R.format,R.isNormalized,I.length,!0);T.push(S)}b.set(n.buffer),I.push(y);var M=new Ne(T,I,E),A=this._device.createInputAssembler(M);this.instances.push({count:1,capacity:32,vb:y,data:b,ia:A,stride:r,shader:p,descriptorSet:c,lightingMap:a,reflectionProbeCubemap:u,reflectionProbePlanarMap:h,useReflectionProbeType:l,reflectionProbeBlendCubemap:null}),this.hasPendingModels=!0}},e.uploadBuffers=function(t){for(var e=0;e<this.instances.length;++e){var i=this.instances[e];i.count&&(i.ia.instanceCount=i.count,t.updateBuffer(i.vb,i.data))}},e.clear=function(){this.instances.forEach((function(t){t.count=0})),this.hasPendingModels=!1},t}()),dr=new Le(Pe.UNIFORM|Pe.TRANSFER_DST,Oe.DEVICE),gr=new je(null),mr=new We(null);t("B",fr),function(t){t[t.NONE=0]="NONE",t[t.INSTANCING=1]="INSTANCING"}(fr||t("B",fr={}));var vr,yr,br,Ir,Tr,Er,Dr,Rr=t("j",function(){function t(t){this._rootBuffer=null,this._buffers=[],this._descriptorSet=null,this._pipelineLayout=null,this._passIndex=0,this._propertyIndex=0,this._programName="",this._dynamics={},this._propertyHandleMap={},this._rootBlock=null,this._blocksInt=[],this._blocks=[],this._shaderInfo=null,this._defines={},this._properties={},this._shader=null,this._bs=new He,this._dss=new Be,this._rs=new Fe,this._priority=Et.DEFAULT,this._stage=Dt.DEFAULT,this._phase=xn("default"),this._passID=4294967295,this._subpassID=4294967295,this._phaseID=4294967295,this._primitive=Ue.TRIANGLE_LIST,this._batchingScheme=fr.NONE,this._dynamicStates=Ge.NONE,this._instancedBuffers={},this._hash=0,this._rootBufferDirty=!1,this._root=t,this._device=ie.gfxDevice}t.fillPipelineInfo=function(t,e){void 0!==e.priority&&(t._priority=e.priority),void 0!==e.primitive&&(t._primitive=e.primitive),void 0!==e.stage&&(t._stage=e.stage),void 0!==e.dynamicStates&&(t._dynamicStates=e.dynamicStates),void 0!==e.phase&&(t._phase=xn(e.phase));var i=t._bs;if(e.blendState){var n=e.blendState,r=n.targets;r&&r.forEach((function(t,e){i.setTarget(e,t)})),void 0!==n.isA2C&&(i.isA2C=n.isA2C),void 0!==n.isIndepend&&(i.isIndepend=n.isIndepend),void 0!==n.blendColor&&(i.blendColor=n.blendColor)}t._rs.assign(e.rasterizerState),t._dss.assign(e.depthStencilState)},t.getPassHash=function(t){var e="";if(ut.rendering&&ut.rendering.enableEffectImport){var i=ut.rendering.programLib.getKey(t._phaseID,t.program,t.defines);e=t._phaseID.toString()+","+i}else e=cr.getKey(t.program,t.defines);var n,r=e+","+t._primitive+","+t._dynamicStates;return r+=Sr(t._bs),r+=Mr(t._dss),r+=",rs,"+(n=t._rs).cullMode+","+n.depthBias+","+n.isFrontFaceCCW,se(r,666)};var e=t.prototype;return e.initialize=function(t){this._doInit(t),this.resetUBOs(),this.resetTextures(),this.tryCompile()},e.getHandle=function(t,e,i){void 0===e&&(e=0),void 0===i&&(i=fe.UNKNOWN);var n=this._propertyHandleMap[t];return n?(i?n=Gn(n,i):e&&(n=Gn(n,Hn(n)-e)),n+e):0},e.getBinding=function(t){var e=this.getHandle(t);return e?Bn(e):-1},e.setUniform=function(t,e){var i=Bn(t),n=Hn(t),r=Un(t),s=this._getBlockView(n,i);zn[n](s,e,r),this._rootBufferDirty=!0},e.getUniform=function(t,e){var i=Bn(t),n=Hn(t),r=Un(t),s=this._getBlockView(n,i);return Vn[n](s,e,r)},e.setUniformArray=function(t,e){for(var i=Bn(t),n=Hn(t),r=_e(n)>>2,s=this._getBlockView(n,i),o=Un(t),a=0;a<e.length;a++,o+=r)null!==e[a]&&zn[n](s,e[a],o);this._rootBufferDirty=!0},e.bindTexture=function(t,e,i){this._descriptorSet.bindTexture(t,e,i||0)},e.bindSampler=function(t,e,i){this._descriptorSet.bindSampler(t,e,i||0)},e.setDynamicState=function(t,e){var i=this._dynamics[t];i&&i.value===e||(i.value=e,i.dirty=!0)},e.overridePipelineStates=function(){d(12102)},e.update=function(){this._descriptorSet?(this._rootBuffer&&this._rootBufferDirty&&(this._rootBuffer.update(this._rootBlock),this._rootBufferDirty=!1),this._descriptorSet.update()):n(12006)},e.getInstancedBuffer=function(t){return void 0===t&&(t=0),this._instancedBuffers[t]||(this._instancedBuffers[t]=new _r(this))},e.destroy=function(){for(var t=0;t<this._shaderInfo.blocks.length;t++){var e=this._shaderInfo.blocks[t];this._buffers[e.binding].destroy()}for(var i in this._buffers=[],this._rootBuffer&&(this._rootBuffer.destroy(),this._rootBuffer=null),this._instancedBuffers)this._instancedBuffers[i].destroy();this._descriptorSet.destroy(),this._rs.destroy(),this._dss.destroy(),this._bs.destroy()},e.resetUniform=function(t){var e=this.getHandle(t);if(e){for(var i=Hn(e),n=Bn(e),r=Un(e),s=Fn(e),o=this._getBlockView(i,n),a=this._properties[t],u=a&&a.value||Wn(i),h=(_e(i)>>2)*s,l=0;l+u.length<=h;l+=u.length)o.set(u,r+l);this._rootBufferDirty=!0}},e.resetTexture=function(t,e){var i=this.getHandle(t);if(i){var n,r=Hn(i),s=Bn(i),o=this._properties[t],a=o&&o.value,u=(n="string"==typeof a?Cn.get(""+a+Xn(r)):a||Cn.get(Wn(r)))&&n.getGFXTexture(),h=o&&void 0!==o.samplerHash?Ve.unpackFromHash(o.samplerHash):n&&n.getSamplerInfo(),l=this._device.getSampler(h);this._descriptorSet.bindSampler(s,l,e||0),this._descriptorSet.bindTexture(s,u,e||0)}},e.resetUBOs=function(){for(var t=0;t<this._shaderInfo.blocks.length;t++)for(var e=this._shaderInfo.blocks[t],i=0,n=0;n<e.members.length;n++){for(var r=e.members[n],s=this._getBlockView(r.type,e.binding),o=this._properties[r.name],a=o&&o.value||Wn(r.type),u=(_e(r.type)>>2)*r.count,h=0;h+a.length<=u;h+=a.length)s.set(a,i+h);i+=u}this._rootBufferDirty=!0},e.resetTextures=function(){var t=this;if(ut.rendering)this._shaderInfo.descriptors[mt.MATERIAL].samplerTextures.forEach((function(e){for(var i=0;i<e.count;++i)t.resetTexture(e.name,i)}));else for(var e=0;e<this._shaderInfo.samplerTextures.length;e++)for(var i=this._shaderInfo.samplerTextures[e],n=0;n<i.count;n++)this.resetTexture(i.name,n)},e.tryCompile=function(){var e=this._root.pipeline;if(!e)return!1;if(this._syncBatchingScheme(),ut.rendering&&ut.rendering.enableEffectImport){var i=ut.rendering.programLib,n=i.getProgramVariant(this._device,this._phaseID,this._programName,this._defines);if(!n)return d(12103,this._programName),!1;this._shader=n.shader,this._pipelineLayout=i.getPipelineLayout(this.device,this._phaseID,this._programName)}else{var r=cr.getGFXShader(this._device,this._programName,this._defines,e);if(!r)return d(12104,this._programName),!1;this._shader=r,this._pipelineLayout=cr.getTemplateInfo(this._programName).pipelineLayout}return this._hash=t.getPassHash(this),!0},e.getShaderVariant=function(t){if(void 0===t&&(t=null),!this._shader&&!this.tryCompile())return d(12105),null;if(!t)return this._shader;for(var e=this._root.pipeline,i=0;i<t.length;i++){var n=t[i];this._defines[n.name]=n.value}this._isBlend&&(this._defines.CC_IS_TRANSPARENCY_PASS=1);var r=null;if(ut.rendering&&ut.rendering.enableEffectImport){var s=ut.rendering.programLib.getProgramVariant(this._device,this._phaseID,this._programName,this._defines);s&&(r=s.shader)}else r=cr.getGFXShader(this._device,this._programName,this._defines,e);for(var o=0;o<t.length;o++){var a=t[o];delete this._defines[a.name]}return r},e.beginChangeStatesSilently=function(){},e.endChangeStatesSilently=function(){},e._doInit=function(e,i){var r;void 0===i&&(i=!1),this._priority=Et.DEFAULT,this._stage=Dt.DEFAULT;var s=null==(r=ut.rendering)?void 0:r.enableEffectImport;if(s){var o=ut.rendering;if("number"==typeof e.phase?(this._passID=e._passID,this._subpassID=e._subpassID,this._phaseID=e._phaseID):(this._passID=o.getPassID(e.pass),this._passID!==o.INVALID_ID&&(e.subpass?(this._subpassID=o.getSubpassID(this._passID,e.subpass),this._phaseID=o.getPhaseID(this._subpassID,e.phase)):this._phaseID=o.getPhaseID(this._passID,e.phase))),this._passID===o.INVALID_ID)return void n(12107,e.program);if(this._phaseID===o.INVALID_ID)return void n(12108,e.program)}else"number"==typeof e.phase?this._passID=e._passID:e.pass&&"default"!==e.pass&&(u(4294967295===this._passID,12110),this._passID=0);this._phase=xn("default"),this._primitive=Ue.TRIANGLE_LIST,this._passIndex=e.passIndex,this._propertyIndex=void 0!==e.propertyIndex?e.propertyIndex:e.passIndex,this._programName=e.program,this._defines=i?E({},e.defines):e.defines,this._shaderInfo=s?ut.rendering.programLib.getProgramInfo(this._phaseID,this._programName):cr.getTemplate(e.program),this._properties=e.properties||this._properties;var a=this._device;t.fillPipelineInfo(this,e),e.stateOverrides&&t.fillPipelineInfo(this,e.stateOverrides),mr.layout=s?ut.rendering.programLib.getMaterialDescriptorSetLayout(this._device,this._phaseID,e.program):cr.getDescriptorSetLayout(this._device,e.program),this._descriptorSet=this._device.createDescriptorSet(mr);var h,l,p=this._shaderInfo.blocks;if(s){var c=ut.rendering.programLib;h=c.getBlockSizes(this._phaseID,this._programName),l=c.getHandleMap(this._phaseID,this._programName)}else{var f=cr.getTemplateInfo(e.program);h=f.blockSizes,l=f.handleMap}if(s){var _=ut.rendering.programLib.getShaderInfo(this._phaseID,this.program);this._buildMaterialUniformBlocks(a,_.blocks,h)}else this._buildUniformBlocks(a,p,h);var d=this._propertyHandleMap=l,g={};for(var m in this._properties){var v=this._properties[m];v.handleInfo&&(g[m]=this.getHandle.apply(this,v.handleInfo))}Object.assign(d,g)},e._buildUniformBlocks=function(t,e,i){for(var n=t.capabilities.uboOffsetAlignment,r=[],s=0,o=0,a=0;a<e.length;a++){var u=i[a];r.push(o),o+=Math.ceil(u/n)*n,s=u}var h=r[r.length-1]+16*Math.ceil(s/16);h&&(dr.size=16*Math.ceil(h/16),this._rootBuffer=t.createBuffer(dr),this._rootBlock=new ArrayBuffer(h));for(var l=0,p=0;l<e.length;l++){var c=e[l].binding,f=i[l];gr.buffer=this._rootBuffer,gr.offset=r[p++],gr.range=16*Math.ceil(f/16);var _=this._buffers[c]=t.createBuffer(gr);this._blocks[c]=new Float32Array(this._rootBlock,gr.offset,f/Float32Array.BYTES_PER_ELEMENT),this._blocksInt[c]=new Int32Array(this._blocks[c].buffer,this._blocks[c].byteOffset,this._blocks[c].length),this._descriptorSet.bindBuffer(c,_)}},e._buildMaterialUniformBlocks=function(t,e,i){for(var n=t.capabilities.uboOffsetAlignment,r=[],s=0,o=0,a=0;a<e.length;a++)if(1===e[a].set){var u=i[a];r.push(o),o+=Math.ceil(u/n)*n,s=u}if(0!==s){var h=r[r.length-1]+s;h&&(dr.size=16*Math.ceil(h/16),this._rootBuffer=t.createBuffer(dr),this._rootBlock=new ArrayBuffer(h))}for(var l=0,p=0;l<e.length;l++)if(1===e[l].set){var c=e[l].binding,f=i[l];gr.buffer=this._rootBuffer,gr.offset=r[p++],gr.range=16*Math.ceil(f/16);var _=this._buffers[c]=t.createBuffer(gr);this._blocks[c]=new Float32Array(this._rootBlock,gr.offset,f/Float32Array.BYTES_PER_ELEMENT),this._blocksInt[c]=new Int32Array(this._blocks[c].buffer,this._blocks[c].byteOffset,this._blocks[c].length),this._descriptorSet.bindBuffer(c,_)}},e._syncBatchingScheme=function(){this._defines.USE_INSTANCING?this._device.hasFeature(ze.INSTANCED_ARRAYS)?this._batchingScheme=fr.INSTANCING:(this._defines.USE_INSTANCING=!1,this._batchingScheme=fr.NONE):this._batchingScheme=fr.NONE},e._getBlockView=function(t,e){return t<fe.FLOAT?this._blocksInt[e]:this._blocks[e]},e._initPassFromTarget=function(t,e,i){this._priority=t.priority,this._stage=t.stage,this._phase=t.phase,this._phaseID=t._phaseID,this._passID=t._passID,this._batchingScheme=t.batchingScheme,this._primitive=t.primitive,this._dynamicStates=t.dynamicStates,this._bs=t.blendState,this._dss=e,this._descriptorSet=t.descriptorSet,this._rs=t.rasterizerState,this._passIndex=t.passIndex,this._propertyIndex=t.propertyIndex,this._programName=t.program,this._defines=t.defines,this._shaderInfo=t._shaderInfo,this._properties=t._properties,this._blocks=t._blocks,this._blocksInt=t._blocksInt,this._dynamics=t._dynamics,this._shader=t._shader,ut.rendering&&ut.rendering.enableEffectImport?this._pipelineLayout=ut.rendering.programLib.getPipelineLayout(this.device,this._phaseID,this._programName):this._pipelineLayout=cr.getTemplateInfo(this._programName).pipelineLayout,this._hash=t._hash^i},e._updatePassHash=function(){this._hash=t.getPassHash(this)},e.setRootBufferDirty=function(t){this._rootBufferDirty=t},e.setPriority=function(t){this._priority=t},r(t,[{key:"_isBlend",get:function(){return this.blendState.targets.some((function(t){return t.blend}))}},{key:"root",get:function(){return this._root}},{key:"device",get:function(){return this._device}},{key:"shaderInfo",get:function(){return this._shaderInfo}},{key:"localSetLayout",get:function(){return ut.rendering&&ut.rendering.enableEffectImport?ut.rendering.programLib.getLocalDescriptorSetLayout(this._device,this._phaseID,this._programName):cr.getDescriptorSetLayout(this._device,this._programName,!0)}},{key:"program",get:function(){return this._programName}},{key:"properties",get:function(){return this._properties}},{key:"defines",get:function(){return this._defines}},{key:"passIndex",get:function(){return this._passIndex}},{key:"propertyIndex",get:function(){return this._propertyIndex}},{key:"dynamics",get:function(){return this._dynamics}},{key:"blocks",get:function(){return this._blocks}},{key:"blocksInt",get:function(){return this._blocksInt}},{key:"rootBufferDirty",get:function(){return this._rootBufferDirty}},{key:"priority",get:function(){return this._priority}},{key:"primitive",get:function(){return this._primitive}},{key:"stage",get:function(){return this._stage}},{key:"phase",get:function(){return this._phase}},{key:"passID",get:function(){return this._passID}},{key:"phaseID",get:function(){return this._phaseID}},{key:"rasterizerState",get:function(){return this._rs}},{key:"depthStencilState",get:function(){return this._dss}},{key:"blendState",get:function(){return this._bs}},{key:"dynamicStates",get:function(){return this._dynamicStates}},{key:"batchingScheme",get:function(){return this._batchingScheme}},{key:"descriptorSet",get:function(){return this._descriptorSet}},{key:"hash",get:function(){return this._hash}},{key:"pipelineLayout",get:function(){return this._pipelineLayout}}]),t}());function Sr(t){var e=",bs,"+t.isA2C;return t.targets.forEach((function(t){e+=",bt,"+t.blend+","+t.blendEq+","+t.blendAlphaEq+","+t.blendColorMask,e+=","+t.blendSrc+","+t.blendDst+","+t.blendSrcAlpha+","+t.blendDstAlpha})),e}function Mr(t){var e=",dss,"+t.depthTest+","+t.depthWrite+","+t.depthFunc;return e+=","+t.stencilTestFront+","+t.stencilFuncFront+","+t.stencilRefFront+","+t.stencilReadMaskFront,e+=","+t.stencilFailOpFront+","+t.stencilZFailOpFront+","+t.stencilPassOpFront+","+t.stencilWriteMaskFront,(e+=","+t.stencilTestBack+","+t.stencilFuncBack+","+t.stencilRefBack+","+t.stencilReadMaskBack)+","+t.stencilFailOpBack+","+t.stencilZFailOpBack+","+t.stencilPassOpBack+","+t.stencilWriteMaskBack}Rr.getTypeFromHandle=Hn,Rr.getBindingFromHandle=Bn,Rr.getCountFromHandle=Fn,Rr.getOffsetFromHandle=Un;var Ar=["planar-shadow","skybox","deferred-lighting","bloom","hbao","copy-pass","post-process","profiler","splash-screen","unlit","sprite","particle","particle-gpu","particle-trail","billboard","terrain","graphics","clear-stencil","spine","occlusion-query","geometry-renderer","debug-renderer","ssss-blur","float-output-process"],wr=t("E",F("cc.EffectAsset")((Dr=function(t){function e(e){var i;return(i=t.call(this,e)||this).techniques=br&&br(),i.shaders=Ir&&Ir(),i.combinations=Tr&&Tr(),i.hideInEditor=Er&&Er(),i}i(e,t),e.register=function(t){e._effects[t.name]=t,e._layoutValid=!1},e.remove=function(t){if("string"!=typeof t)e._effects[t.name]&&e._effects[t.name]===t&&delete e._effects[t.name];else{if(e._effects[t])return void delete e._effects[t];for(var i in e._effects)if(e._effects[i]._uuid===t)return void delete e._effects[i]}},e.get=function(t){if(e._effects[t])return e._effects[t];for(var i in e._effects)if(e._effects[i]._uuid===t)return e._effects[i];return Ar.includes(t)&&d(16101,t),null},e.getAll=function(){return e._effects},e.isLayoutValid=function(){return e._layoutValid},e.setLayoutValid=function(){e._layoutValid=!0};var n=e.prototype;return n.onLoaded=function(){if(ut.rendering&&ut.rendering.enableEffectImport){ar(this);var t=ut.rendering.programLib;t.addEffect(this),t.init(ie.gfxDevice)}else cr.register(this);e.register(this),ut.game.once(ut.Game.EVENT_RENDERER_INITED,this._precompile,this)},n._precompile=function(){var t=this;if(ut.rendering&&ut.rendering.enableEffectImport)ut.rendering.programLib.precompileEffect(ie.gfxDevice,this);else for(var e=ut.director.root,i=function(){var i=t.shaders[n],r=t.combinations[n];if(!r)return 1;or(r).forEach((function(t){return cr.getGFXShader(ie.gfxDevice,i.name,t,e.pipeline)}))},n=0;n<this.shaders.length;n++)i()},n.destroy=function(){return e.remove(this),t.prototype.destroy.call(this)},n.initDefault=function(i){t.prototype.initDefault.call(this,i);var n=e.get("builtin-unlit");this.name="builtin-unlit",this.shaders=n.shaders,this.combinations=n.combinations,this.techniques=n.techniques},n.validate=function(){return this.techniques.length>0&&this.shaders.length>0},e}(kt),Dr._effects={},Dr._layoutValid=!0,br=U((yr=Dr).prototype,"techniques",[G],(function(){return[]})),Ir=U(yr.prototype,"shaders",[G],(function(){return[]})),Tr=U(yr.prototype,"combinations",[G],(function(){return[]})),Er=U(yr.prototype,"hideInEditor",[G,J],(function(){return!1})),vr=yr))||vr);ut.EffectAsset=wr;var kr=new Xe,Cr=new qe;function xr(t,e){t.x=e.x*e.x,t.y=e.y*e.y,t.z=e.z*e.z}var Lr,Pr,Or,Nr,Hr,Br,Fr,Ur,Gr,Vr=null,zr=new X,jr=t("b",(Lr=F("cc.Material"),Pr=V(wr),Lr((Nr=function(t){function e(e){var i;return(i=t.call(this,e)||this)._effectAsset=Hr&&Hr(),i._techIdx=Br&&Br(),i._defines=Fr&&Fr(),i._states=Ur&&Ur(),i._props=Gr&&Gr(),i._passes=[],i._hash=0,i}i(e,t),e.getHash=function(t){for(var e,i=0,n=v(t.passes);!(e=n()).done;)i^=e.value.hash;return i};var n=e.prototype;return n.initialize=function(t){this._passes.length?d(12005):(this._defines||(this._defines=[]),this._states||(this._states=[]),this._props||(this._props=[]),this._fillInfo(t),this._update())},n.reset=function(t){this.initialize(t)},n.destroy=function(){return this._doDestroy(),t.prototype.destroy.call(this)},n.recompileShaders=function(){d(16370,this.name)},n.overridePipelineStates=function(){d(16371,this.name)},n.onLoaded=function(){this._update()},n.resetUniforms=function(t){void 0===t&&(t=!0),this._props.length=this._passes.length;for(var e=0;e<this._props.length;e++)this._props[e]={};if(t)for(var i,n=v(this._passes);!(i=n()).done;){var r=i.value;r.resetUBOs(),r.resetTextures()}},n.setProperty=function(t,e,i){var n=!1;if(void 0===i)for(var r=this._passes,s=r.length,o=0;o<s;o++){var a=r[o];this._uploadProperty(a,t,e)&&(this._props[a.propertyIndex][t]=e,n=!0)}else{i>=this._passes.length&&d(16372,i);var u=this._passes[i];this._uploadProperty(u,t,e)&&(this._props[u.propertyIndex][t]=e,n=!0)}n||d(16373,t)},n.getProperty=function(t,e){if(void 0===e)for(var i=this._props,n=i.length,r=0;r<n;r++){var s=i[r];if(t in s)return s[t]}else{if(e>=this._passes.length)return d(16372,e),null;var o=this._props[this._passes[e].propertyIndex];if(t in o)return o[t]}return null},n.copy=function(t,e){this._techIdx=t._techIdx,this._props.length=t._props.length;for(var i=0;i<t._props.length;i++)this._props[i]=E({},t._props[i]);this._defines.length=t._defines.length;for(var n=0;n<t._defines.length;n++)this._defines[n]=E({},t._defines[n]);this._states.length=t._states.length;for(var r=0;r<t._states.length;r++)this._states[r]=E({},t._states[r]);this._effectAsset=t._effectAsset,e&&this._fillInfo(e),this._update()},n._fillInfo=function(t){void 0!==t.technique&&(this._techIdx=t.technique),t.effectAsset?this._effectAsset=t.effectAsset:t.effectName&&(this._effectAsset=wr.get(t.effectName)),t.defines&&this._prepareInfo(t.defines,this._defines),t.states&&this._prepareInfo(t.states,this._states)},n._prepareInfo=function(t,e){var i=t;if(!Array.isArray(i)){var n=this._effectAsset?this._effectAsset.techniques[this._techIdx].passes.length:1;i=Array(n).fill(i)}for(var r=0;r<i.length;++r)Object.assign(e[r]||(e[r]={}),i[r])},n._createPasses=function(){var t=this._effectAsset.techniques[this._techIdx||0];if(!t)return[];for(var e=t.passes.length,i=[],n=0;n<e;++n){var r=t.passes[n],s=r.passIndex=n,o=r.defines=this._defines[s]||(this._defines[s]={});if(r.stateOverrides=this._states[s]||(this._states[s]={}),void 0!==r.propertyIndex&&Object.assign(o,this._defines[r.propertyIndex]),void 0!==r.embeddedMacros&&Object.assign(o,r.embeddedMacros),!r.switch||o[r.switch]){var a=new Rr(ut.director.root);a.initialize(r),i.push(a)}}return i},n._update=function(t){var i=this;if(void 0===t&&(t=!0),this._effectAsset){this._passes=this._createPasses();var n=this._effectAsset.techniques[this._techIdx].passes.length;if(this._props.length=n,t)this._passes.forEach((function(t,e){var n=i._props[e];for(var r in n||(n=i._props[e]={}),void 0!==t.propertyIndex&&Object.assign(n,i._props[t.propertyIndex]),n)i._uploadProperty(t,r,n[r])}));else for(var r=0;r<this._props.length;r++)this._props[r]={}}this._hash=e.getHash(this)},n._uploadProperty=function(t,e,i){var n=this,r=t.getHandle(e);if(!r)return!1;if(Hn(r)<fe.SAMPLER1D)if(Array.isArray(i))t.setUniformArray(r,i);else if(null!==i){var s;if(null!=(s=t.properties[e])&&s.linear){var o=i;xr(zr,o),zr.w=o.w,i=zr}t.setUniform(r,i)}else t.resetUniform(e);else Array.isArray(i)?i.forEach((function(e,i){n._bindTexture(t,r,e,i)})):i?this._bindTexture(t,r,i):t.resetTexture(e);return!0},n._bindTexture=function(t,e,i,n){var r=Rr.getBindingFromHandle(e);if(i instanceof Ke)t.bindTexture(r,i,n);else if(i instanceof Li){var s=i.getGFXTexture();if(!s||!s.width||!s.height)return;t.bindTexture(r,s,n),t.bindSampler(r,i.getGFXSampler(),n)}},n._doDestroy=function(){if(this._passes&&this._passes.length)for(var t,e=v(this._passes);!(t=e()).done;)t.value.destroy();this._passes.length=0},n.initDefault=function(e){t.prototype.initDefault.call(this,e),this.initialize({effectName:"builtin-unlit",defines:{USE_COLOR:!0},technique:0}),this.setProperty("mainColor",new Y("#ff00ff"))},n.validate=function(){return!!this._effectAsset&&!this._effectAsset.isDefault&&this.passes.length>0},r(e,[{key:"effectAsset",get:function(){return this._effectAsset}},{key:"effectName",get:function(){return this._effectAsset?this._effectAsset.name:""}},{key:"technique",get:function(){return this._techIdx}},{key:"passes",get:function(){return this._passes}},{key:"hash",get:function(){return this._hash}},{key:"parent",get:function(){return null}},{key:"owner",get:function(){return null}}]),e}(kt),Hr=U(Nr.prototype,"_effectAsset",[Pr],(function(){return null})),Br=U(Nr.prototype,"_techIdx",[G],(function(){return 0})),Fr=U(Nr.prototype,"_defines",[G],(function(){return[]})),Ur=U(Nr.prototype,"_states",[G],(function(){return[]})),Gr=U(Nr.prototype,"_props",[G],(function(){return[]})),Or=Nr))||Or));ut.Material=jr;var Wr=t("s",R({Low_256x256:256,Medium_512x512:512,High_1024x1024:1024,Ultra_2048x2048:2048})),Xr=t("t",R({Planar:0,ShadowMap:1})),qr=(t("r",R({HARD:0,SOFT:1,SOFT_2X:2,SOFT_4X:3})),t("f",R({LEVEL_1:1,LEVEL_2:2,LEVEL_3:3,LEVEL_4:4})),t("C",R({NONE:1,RemoveDuplicates:2,DisableRotationFix:3})),Xr.ShadowMap+1),Kr=t("h",function(){function t(){this.fixedSphere=new Z(0,0,0,.01),this.maxReceived=4,this._matLight=new K,this._material=null,this._instancingMaterial=null,this._enabled=!1,this._type=qr,this._distance=0,this._planeBias=1,this._normal=new W(0,1,0),this._shadowColor=new Y(0,0,0,76),this._size=new j(1024,1024),this._shadowMapDirty=!1}var e=t.prototype;return e.getPlanarShader=function(t){this._material||(this._material=new jr,this._material.initialize({effectName:"pipeline/planar-shadow"}));var e=this._material.passes;return e.length>0?e[0].getShaderVariant(t):null},e.initialize=function(t){this._enabled=t.enabled,this._type=this.enabled?t.type:qr,this.normal=t.planeDirection,this.distance=t.planeHeight,this.planeBias=t.planeBias,this.shadowColor=t.shadowColor,this.maxReceived=t.maxReceived,t.shadowMapSize!==this._size.x&&(this.size.set(t.shadowMapSize,t.shadowMapSize),this._shadowMapDirty=!0)},e.activate=function(){if(this._enabled)if(this.type===Xr.Planar)this._updatePlanarInfo();else{var t=ut.director.root;t.pipeline.macros.CC_SHADOW_TYPE=2,t.onGlobalPipelineStateChanged()}else{var e=ut.director.root;e.pipeline.macros.CC_SHADOW_TYPE=0,e.onGlobalPipelineStateChanged()}},e._updatePlanarInfo=function(){this._material||(this._material=new jr,this._material.initialize({effectName:"pipeline/planar-shadow"}));var t=ut.director.root;t.pipeline.macros.CC_SHADOW_TYPE=1,t.onGlobalPipelineStateChanged()},e.destroy=function(){this._material&&this._material.destroy(),this._instancingMaterial&&this._instancingMaterial.destroy(),this.fixedSphere.destroy()},r(t,[{key:"enabled",get:function(){return this._enabled},set:function(t){this._enabled=t,this.activate()}},{key:"type",get:function(){return this._type},set:function(t){this._type=this.enabled?t:qr,this.activate()}},{key:"normal",get:function(){return this._normal},set:function(t){W.copy(this._normal,t)}},{key:"distance",get:function(){return this._distance},set:function(t){this._distance=t}},{key:"planeBias",get:function(){return this._planeBias},set:function(t){this._planeBias=t}},{key:"shadowColor",get:function(){return this._shadowColor},set:function(t){this._shadowColor=t}},{key:"size",get:function(){return this._size},set:function(t){this._size.set(t)}},{key:"shadowMapDirty",get:function(){return this._shadowMapDirty},set:function(t){this._shadowMapDirty=t}},{key:"matLight",get:function(){return this._matLight}},{key:"material",get:function(){return this._material}},{key:"instancingMaterial",get:function(){return this._instancingMaterial}}]),t}());function Yr(){return ut.director.root.pipeline.pipelineSceneData}Kr.MAX_FAR=2e3,Kr.COEFFICIENT_OF_EXPANSION=2*Math.sqrt(3),ut.Shadows=Kr;var Qr=t("A",function(){function t(){this._groundAlbedoHDR=new X(.2,.2,.2,1),this._skyColorHDR=new X(.2,.5,.8,1),this._skyIllumHDR=0,this._groundAlbedoLDR=new X(.2,.2,.2,1),this._skyColorLDR=new X(.2,.5,.8,1),this._skyIllumLDR=0,this._mipmapCount=1,this._enabled=!1}return t.prototype.initialize=function(t){this._skyColorHDR=t.skyColorHDR,this._groundAlbedoHDR.set(t.groundAlbedoHDR),this._skyIllumHDR=t.skyIllumHDR,this._skyColorLDR=t.skyColorLDR,this._groundAlbedoLDR.set(t.groundAlbedoLDR),this._skyIllumLDR=t.skyIllumLDR},r(t,[{key:"enabled",get:function(){return this._enabled},set:function(t){this._enabled=t}},{key:"skyColor",get:function(){return Yr().isHDR?this._skyColorHDR:this._skyColorLDR},set:function(t){Yr().isHDR?this._skyColorHDR.set(t):this._skyColorLDR.set(t)}},{key:"skyIllum",get:function(){return Yr().isHDR?this._skyIllumHDR:this._skyIllumLDR},set:function(t){Yr().isHDR?this._skyIllumHDR=t:this._skyIllumLDR=t}},{key:"groundAlbedo",get:function(){return Yr().isHDR?this._groundAlbedoHDR:this._groundAlbedoLDR},set:function(t){Yr().isHDR?this._groundAlbedoHDR.set(t):this._groundAlbedoLDR.set(t)}}]),t}());Qr.SUN_ILLUM=65e3,Qr.SKY_ILLUM=2e4,ut.Ambient=Qr;var Jr,Zr=t("v",function(t){function e(e,i){var n;(n=t.call(this,e.root)||this)._dontNotify=!1,n._parent=e,n._owner=i,n._doInit(n._parent,!0),n._shaderInfo.blocks.forEach((function(t){var e=n._blocks[t.binding],i=n._parent.blocks[t.binding];e.set(i)})),n._rootBufferDirty=!0;var r=n._parent,s=n._descriptorSet;return n._shaderInfo.samplerTextures.forEach((function(t){for(var e=0;e<t.count;e++){var i=r._descriptorSet,n=t.binding,o=i.getSampler(n,e),a=i.getTexture(n,e);s.bindSampler(n,o,e),s.bindTexture(n,a,e)}})),t.prototype.tryCompile.call(S(n)),n}i(e,t);var n=e.prototype;return n.overridePipelineStates=function(t,e){this._bs.reset(),this._rs.reset(),this._dss.reset(),Rr.fillPipelineInfo(this,t),Rr.fillPipelineInfo(this,e),this._onStateChange()},n.tryCompile=function(e){if(e&&!qn(this._defines,e))return!1;var i=t.prototype.tryCompile.call(this);return this._onStateChange(),i},n.beginChangeStatesSilently=function(){this._dontNotify=!0},n.endChangeStatesSilently=function(){this._dontNotify=!1},n._syncBatchingScheme=function(){this._defines.USE_INSTANCING=!1,this._batchingScheme=fr.NONE},n._onStateChange=function(){this._hash=Rr.getPassHash(this),this._owner.onPassStateChange(this._dontNotify)},r(e,[{key:"parent",get:function(){return this._parent}}]),e}(Rr)),$r=t("M",function(t){function e(e){var i;return(i=t.call(this)||this)._passes=[],i._subModelIdx=0,i._parent=e.parent,i._owner=e.owner||null,i._subModelIdx=e.subModelIdx||0,i.copy(i._parent),i}i(e,t);var n=e.prototype;return n.recompileShaders=function(t,e){this._passes&&this.effectAsset&&(void 0===e?this._passes.forEach((function(e){e.tryCompile(t)})):this._passes[e].tryCompile(t))},n.overridePipelineStates=function(t,e){if(this._passes&&this.effectAsset){var i=this.effectAsset.techniques[this.technique].passes;if(void 0===e)for(var n=0;n<this._passes.length;n++){var r=this._passes[n],s=this._states[n]||(this._states[n]={});for(var o in t)s[o]=t[o];r.overridePipelineStates(i[r.passIndex],s)}else{var a=this._states[e]||(this._states[e]={});for(var u in t)a[u]=t[u];this._passes[e].overridePipelineStates(i[e],a)}}},n.destroy=function(){return this._doDestroy(),!0},n.onPassStateChange=function(t){this._hash=jr.getHash(this),!t&&this._owner&&this._owner._onRebuildPSO(this._subModelIdx,this)},n._createPasses=function(){var t=[],e=this._parent.passes;if(!e)return t;for(var i=0;i<e.length;++i)t.push(new Zr(e[i],this));return t},r(e,[{key:"parent",get:function(){return this._parent}},{key:"owner",get:function(){return this._owner}}]),e}(jr)),ts=null,es=null;t("o",Jr),function(t){t[t.HEMISPHERE_DIFFUSE=0]="HEMISPHERE_DIFFUSE",t[t.AUTOGEN_HEMISPHERE_DIFFUSE_WITH_REFLECTION=1]="AUTOGEN_HEMISPHERE_DIFFUSE_WITH_REFLECTION",t[t.DIFFUSEMAP_WITH_REFLECTION=2]="DIFFUSEMAP_WITH_REFLECTION"}(Jr||t("o",Jr={})),R(Jr);var is=t("S",function(){function t(){this._envmapLDR=null,this._envmapHDR=null,this._diffuseMapLDR=null,this._diffuseMapHDR=null,this._globalDSManager=null,this._model=null,this._default=null,this._enabled=!1,this._useIBL=!1,this._useHDR=!0,this._useDiffuseMap=!1,this._editableMaterial=null,this._activated=!1,this._reflectionHDR=null,this._reflectionLDR=null,this._rotationAngle=0}var e=t.prototype;return e.initialize=function(t){this._activated=!1,this._enabled=t.enabled,this._useIBL=t.useIBL,this._useDiffuseMap=t.applyDiffuseMap,this._useHDR=t.useHDR},e.setEnvMaps=function(t,e){this._envmapHDR=t,this._envmapLDR=e,this._updateGlobalBinding(),this._updatePipeline()},e.setDiffuseMaps=function(t,e){this._diffuseMapHDR=t,this._diffuseMapLDR=e,this._updateGlobalBinding(),this._updatePipeline()},e.setSkyboxMaterial=function(t){t?(this._editableMaterial=new $r({parent:t}),this._editableMaterial.recompileShaders({USE_RGBE_CUBEMAP:this.isRGBE})):this._editableMaterial=null,this._updatePipeline()},e.setReflectionMaps=function(t,e){this._reflectionHDR=t,this._reflectionLDR=e,this._updateGlobalBinding(),this._updatePipeline()},e.setRotationAngle=function(t){this._rotationAngle=t},e.getRotationAngle=function(){return this._rotationAngle},e.updateMaterialRenderInfo=function(){this._updateGlobalBinding(),this._updatePipeline()},e.activate=function(){var t=ut.director.root.pipeline;this._globalDSManager=t.globalDSManager,this._default=Cn.get("default-cube-texture"),this._model||(this._model=ut.director.root.createModel(ut.renderer.scene.Model));var e=this._default.isRGBE;if(this._default.isUsingOfflineMipmaps(),this.envmap&&(e=this.envmap.isRGBE,this.envmap.isUsingOfflineMipmaps()),!es){var i=new jr;i.initialize({effectName:"pipeline/skybox",defines:{USE_RGBE_CUBEMAP:e}}),es=new $r({parent:i})}this.enabled&&(ts||(ts=ut.utils.createMesh(ut.primitives.box({width:2,height:2,length:2}))),this._editableMaterial?this._model.initSubModel(0,ts.renderingSubMeshes[0],this._editableMaterial):this._model.initSubModel(0,ts.renderingSubMeshes[0],es)),this.envmap||(this.envmap=this._default),this.diffuseMap||(this.diffuseMap=this._default),this._updateGlobalBinding(),this._updatePipeline(),this._activated=!0},e._updatePipeline=function(){var t=ut.director.root,e=t.pipeline,i=this.useIBL?this.isRGBE?2:1:0,n=this.useIBL&&this.useDiffuseMap&&this.diffuseMap&&this.diffuseMap!==this._default?this.isRGBE?2:1:0,r=this.useHDR,s=this.useConvolutionMap;if(e.macros.CC_USE_IBL===i&&e.macros.CC_USE_DIFFUSEMAP===n&&e.macros.CC_USE_HDR===r&&e.macros.CC_IBL_CONVOLUTED===s||(e.macros.CC_USE_IBL=i,e.macros.CC_USE_DIFFUSEMAP=n,e.macros.CC_USE_HDR=r,e.macros.CC_IBL_CONVOLUTED=s,this._activated&&t.onGlobalPipelineStateChanged()),this.enabled){var o=this.envmap?this.envmap:this._default,a=this._editableMaterial?this._editableMaterial:es;a&&(a.setProperty("environmentMap",o),a.recompileShaders({USE_RGBE_CUBEMAP:this.isRGBE})),this._model&&(this._model.setSubModelMaterial(0,a),this._updateSubModes())}},e._updateGlobalBinding=function(){if(!ut.rendering&&this._globalDSManager){var t=ie.gfxDevice;if(this.reflectionMap){var e=this.reflectionMap.getGFXTexture(),i=t.getSampler(this.reflectionMap.getSamplerInfo());this._globalDSManager.bindSampler(Mt,i),this._globalDSManager.bindTexture(Mt,e)}else{var n=this.envmap?this.envmap:this._default;if(n){var r=n.getGFXTexture(),s=t.getSampler(n.getSamplerInfo());this._globalDSManager.bindSampler(Mt,s),this._globalDSManager.bindTexture(Mt,r)}}var o=this.diffuseMap?this.diffuseMap:this._default;if(o){var a=o.getGFXTexture(),u=t.getSampler(o.getSamplerInfo());this._globalDSManager.bindSampler(At,u),this._globalDSManager.bindTexture(At,a)}this._globalDSManager.update()}},e._updateSubModes=function(){if(this._model)for(var t=this._model.subModels,e=0;e<t.length;e++)t[e].update()},r(t,[{key:"model",get:function(){return this._model}},{key:"enabled",get:function(){return this._enabled},set:function(t){this._enabled=t,t?this.activate():this._updatePipeline()}},{key:"useHDR",get:function(){return this._useHDR},set:function(t){this._useHDR=t,this.setEnvMaps(this._envmapHDR,this._envmapLDR)}},{key:"useIBL",get:function(){return this._useIBL},set:function(t){this._useIBL=t,this._updatePipeline()}},{key:"useDiffuseMap",get:function(){return this._useDiffuseMap},set:function(t){this._useDiffuseMap=t,this._updatePipeline()}},{key:"isRGBE",get:function(){return!!this.envmap&&this.envmap.isRGBE}},{key:"useConvolutionMap",get:function(){return this.reflectionMap?this.reflectionMap.isUsingOfflineMipmaps():!!this.envmap&&this.envmap.isUsingOfflineMipmaps()}},{key:"envmap",get:function(){return Yr().isHDR?this._envmapHDR:this._envmapLDR},set:function(t){Yr().isHDR?this.setEnvMaps(t,this._envmapLDR):this.setEnvMaps(this._envmapHDR,t)}},{key:"diffuseMap",get:function(){return Yr().isHDR?this._diffuseMapHDR:this._diffuseMapLDR},set:function(t){Yr().isHDR?this.setDiffuseMaps(t,this._diffuseMapLDR):this.setDiffuseMaps(this._diffuseMapHDR,t)}},{key:"reflectionMap",get:function(){return Yr().isHDR?this._reflectionHDR:this._reflectionLDR}},{key:"editableMaterial",get:function(){return this._editableMaterial}}]),t}());ut.Skybox=is;var ns,rs,ss=new X,os=t("q",R({LINEAR:0,EXP:1,EXP_SQUARED:2,LAYERED:3})),as=t("p",os.LAYERED+1),us=t("F",function(){function t(){this._fogColor=new Y("#C8C8C8"),this._colorArray=new X(.2,.2,.2,1),this._enabled=!1,this._accurate=!1,this._type=0,this._fogDensity=.3,this._fogStart=.5,this._fogEnd=300,this._fogAtten=5,this._fogTop=1.5,this._fogRange=1.2,this._activated=!1}var e=t.prototype;return e.initialize=function(t){this._activated=!1,this.fogColor=t.fogColor,this._enabled=t.enabled,this._type=this.enabled?t.type:as,this._accurate=t.accurate,this.fogDensity=t.fogDensity,this.fogStart=t.fogStart,this.fogEnd=t.fogEnd,this.fogAtten=t.fogAtten,this.fogTop=t.fogTop,this.fogRange=t.fogRange},e.activate=function(){this._updatePipeline(),this._activated=!0},e._updatePipeline=function(){var t=ut.director.root,e=this.enabled?this.type:as,i=this.accurate?1:0,n=t.pipeline;n.macros.CC_USE_FOG===e&&n.macros.CC_USE_ACCURATE_FOG===i||(n.macros.CC_USE_FOG=e,n.macros.CC_USE_ACCURATE_FOG=i,this._activated&&t.onGlobalPipelineStateChanged())},r(t,[{key:"enabled",get:function(){return this._enabled},set:function(t){this._enabled=t,t?this.activate():(this._type=as,this._updatePipeline())}},{key:"accurate",get:function(){return this._accurate},set:function(t){this._accurate=t,this._updatePipeline()}},{key:"fogColor",get:function(){return this._fogColor},set:function(t){this._fogColor.set(t),ss.set(t.x,t.y,t.z,t.w),xr(this._colorArray,ss)}},{key:"type",get:function(){return this._type},set:function(t){this._type=this.enabled?t:as,this.enabled&&this._updatePipeline()}},{key:"fogDensity",get:function(){return this._fogDensity},set:function(t){this._fogDensity=t}},{key:"fogStart",get:function(){return this._fogStart},set:function(t){this._fogStart=t}},{key:"fogEnd",get:function(){return this._fogEnd},set:function(t){this._fogEnd=t}},{key:"fogAtten",get:function(){return this._fogAtten},set:function(t){this._fogAtten=t}},{key:"fogTop",get:function(){return this._fogTop},set:function(t){this._fogTop=t}},{key:"fogRange",get:function(){return this._fogRange},set:function(t){this._fogRange=t}},{key:"colorArray",get:function(){return this._colorArray}}]),t}());ut.Fog=us,t("V",ns),function(t){t[t.LOCAL=0]="LOCAL",t[t.WORLD=1]="WORLD"}(ns||t("V",ns={})),t("T",rs),function(t){t[t.NONE=0]="NONE",t[t.POSITION=1]="POSITION",t[t.ROTATION=2]="ROTATION",t[t.SCALE=4]="SCALE",t[t.SKEW=8]="SKEW",t[t.RS=t.ROTATION|t.SCALE]="RS",t[t.RSS=t.ROTATION|t.SCALE|t.SKEW]="RSS",t[t.TRS=t.POSITION|t.ROTATION|t.SCALE]="TRS",t[t.TRS_MASK=~t.TRS]="TRS_MASK"}(rs||t("T",rs={})),ht.internal.TransformBit=rs;var hs=t("c",R({Static:0,Stationary:1,Movable:2})),ls=t("u",R({DEFAULT:0,LINEAR:1})),ps=(t("P",function(){function t(){this._toneMappingType=ls.DEFAULT,this._activated=!1}var e=t.prototype;return e.initialize=function(t){this._toneMappingType=t.toneMappingType},e.activate=function(){this._updatePipeline(),this._activated=!0},e._updatePipeline=function(){var t=ut.director.root;t.pipeline.macros.CC_TONE_MAPPING_TYPE=this._toneMappingType,this._activated&&t.onGlobalPipelineStateChanged()},r(t,[{key:"toneMappingType",get:function(){return this._toneMappingType},set:function(t){this._toneMappingType=t,this._updatePipeline()}}]),t}()),t("aa",function(){var t=e.prototype;function e(t){this._uiComp=null,this._opacity=1,this._localOpacity=1,this.colorDirty=!0,this._uiTransformComp=null,this._uiSkewComp=null,this._node=t}return t.setOpacity=function(t){this._opacity=t},t.applyOpacity=function(t){this._opacity=this._localOpacity*t},e.markOpacityTree=function(){},r(e,[{key:"uiTransformComp",get:function(){return this._uiTransformComp||(this._uiTransformComp=this._node.getComponent("cc.UITransform")),this._uiTransformComp},set:function(t){this._uiTransformComp=t}},{key:"uiComp",get:function(){return this._uiComp},set:function(t){this._uiComp&&t?d(12002):this._uiComp=t}},{key:"opacity",get:function(){return this._opacity}},{key:"localOpacity",get:function(){return this._localOpacity},set:function(t){this._localOpacity=t,this.colorDirty=!0}}]),e}()));M.Destroying,ht.GAME_VIEW;var cs,fs=new A((function(){return new Array(16)}),3),_s=null,ds=new j,gs=[$t.TOUCH_START,$t.TOUCH_MOVE,$t.TOUCH_END,$t.TOUCH_CANCEL],ms=[$t.MOUSE_DOWN,$t.MOUSE_ENTER,$t.MOUSE_MOVE,$t.MOUSE_LEAVE,$t.MOUSE_UP,$t.MOUSE_WHEEL];t("aq",cs),function(t){t[t.ADD_POINTER_EVENT_PROCESSOR=0]="ADD_POINTER_EVENT_PROCESSOR",t[t.REMOVE_POINTER_EVENT_PROCESSOR=1]="REMOVE_POINTER_EVENT_PROCESSOR",t[t.MARK_LIST_DIRTY=2]="MARK_LIST_DIRTY"}(cs||t("aq",cs={}));var vs=new w,ys=t("ap",function(){function t(t){this.claimedTouchIdList=[],this.maskList=null,this.cachedCameraPriority=0,this.previousMouseIn=!1,this.bubblingTarget=null,this.capturingTarget=null,this.shouldHandleEventMouse=!1,this.shouldHandleEventTouch=!1,this._dispatchingTouch=null,this._isEnabled=!1,this._isMouseLeaveWindow=!1,this._node=t}var e=t.prototype;return e.setEnabled=function(t,e){if(void 0===e&&(e=!1),this._isEnabled!==t){this._isEnabled=t;var i=this.node.children;if(t&&this._attachMask(),vs.emit(cs.MARK_LIST_DIRTY),e&&i.length>0)for(var n=0;n<i.length;++n)i[n].eventProcessor.setEnabled(t,!0)}},e.reattach=function(){this.node.walk((function(e){var i=e.eventProcessor,n=i._searchComponentsInParent(t._maskComp);i.maskList=n}))},e.destroy=function(){if(_s===this._node&&(_s=null),this.capturingTarget&&this.capturingTarget.clear(),this.bubblingTarget&&this.bubblingTarget.clear(),vs.emit(cs.REMOVE_POINTER_EVENT_PROCESSOR,this),this._dispatchingTouch){var t=new te([this._dispatchingTouch],!0,ee.TOUCH_CANCEL);t.touch=this._dispatchingTouch,this.dispatchEvent(t),this._dispatchingTouch=null}},e.on=function(t,e,i,n){var r,s;return this._tryEmittingAddEvent(t),((n=!!n)?null!==(r=this.capturingTarget)&&void 0!==r?r:this.capturingTarget=this._newCallbacksInvoker():null!==(s=this.bubblingTarget)&&void 0!==s?s:this.bubblingTarget=this._newCallbacksInvoker()).on(t,e,i),e},e.once=function(t,e,i,n){var r,s;return this._tryEmittingAddEvent(t),((n=!!n)?null!==(r=this.capturingTarget)&&void 0!==r?r:this.capturingTarget=this._newCallbacksInvoker():null!==(s=this.bubblingTarget)&&void 0!==s?s:this.bubblingTarget=this._newCallbacksInvoker()).on(t,e,i,!0),e},e.off=function(t,e,i,n){var r;null==(r=(n=!!n)?this.capturingTarget:this.bubblingTarget)||r.off(t,e,i)},e.targetOff=function(t){var e,i;null==(e=this.capturingTarget)||e.removeAll(t),null==(i=this.bubblingTarget)||i.removeAll(t),this.shouldHandleEventTouch&&!this._hasTouchListeners()&&(this.shouldHandleEventTouch=!1),this.shouldHandleEventMouse&&!this._hasMouseListeners()&&(this.shouldHandleEventMouse=!1),this._hasPointerListeners()||vs.emit(cs.REMOVE_POINTER_EVENT_PROCESSOR,this)},e.emit=function(t,e,i,n,r,s){var o;null==(o=this.bubblingTarget)||o.emit(t,e,i,n,r,s)},e.dispatchEvent=function(t){var e,i=this.node,n=0;t.target=i;var r=fs.alloc();for(r.length=0,this.getCapturingTargets(t.type,r),t.eventPhase=1,n=r.length-1;n>=0;--n)if((e=r[n]).eventProcessor.capturingTarget&&(t.currentTarget=e,e.eventProcessor.capturingTarget.emit(t.type,t,r),t.propagationStopped))return void fs.free(r);if(t.eventPhase=2,t.currentTarget=i,this.capturingTarget&&this.capturingTarget.emit(t.type,t),!t.propagationImmediateStopped&&this.bubblingTarget&&this.bubblingTarget.emit(t.type,t),!t.propagationStopped&&t.bubbles)for(r.length=0,this.getBubblingTargets(t.type,r),t.eventPhase=3,n=0;n<r.length;++n)if((e=r[n]).eventProcessor.bubblingTarget&&(t.currentTarget=e,e.eventProcessor.bubblingTarget.emit(t.type,t),t.propagationStopped))return void fs.free(r);fs.free(r)},e.hasEventListener=function(t,e,i){var n=!1;return this.bubblingTarget&&(n=this.bubblingTarget.hasEventListener(t,e,i)),!n&&this.capturingTarget&&(n=this.capturingTarget.hasEventListener(t,e,i)),n},e.getCapturingTargets=function(t,e){for(var i=this._node.parent;i;){var n;null!=(n=i.eventProcessor.capturingTarget)&&n.hasEventListener(t)&&e.push(i),i=i.parent}},e.getBubblingTargets=function(t,e){for(var i=this._node.parent;i;){var n;null!=(n=i.eventProcessor.bubblingTarget)&&n.hasEventListener(t)&&e.push(i),i=i.parent}},e.onUpdatingSiblingIndex=function(){vs.emit(cs.MARK_LIST_DIRTY)},e._searchComponentsInParent=function(t){var e=this.node;if(t){for(var i=0,n=[],r=e;r&&ut.Node.isNode(r);r=r.parent,++i){var s=r.getComponent(t);if(s){var o={index:i,comp:s};n?n.push(o):n=[o]}}return n.length>0?n:null}return null},e._attachMask=function(){this.maskList=this._searchComponentsInParent(t._maskComp)},e._isTouchEvent=function(t){return-1!==gs.indexOf(t)},e._isMouseEvent=function(t){return-1!==ms.indexOf(t)},e._hasTouchListeners=function(){for(var t=0;t<gs.length;++t){var e=gs[t];if(this.hasEventListener(e))return!0}return!1},e._hasMouseListeners=function(){for(var t=0;t<ms.length;++t){var e=ms[t];if(this.hasEventListener(e))return!0}return!1},e._hasPointerListeners=function(){return!!this._hasTouchListeners()||this._hasMouseListeners()},e._tryEmittingAddEvent=function(t){var e=this._isTouchEvent(t),i=this._isMouseEvent(t);e?this.shouldHandleEventTouch=!0:i&&(this.shouldHandleEventMouse=!0),!e&&!i||this._hasPointerListeners()||vs.emit(cs.ADD_POINTER_EVENT_PROCESSOR,this)},e._newCallbacksInvoker=function(){var t=this,e=new w;return e._registerOffCallback((function(){t.shouldHandleEventTouch&&!t._hasTouchListeners()&&(t.shouldHandleEventTouch=!1),t.shouldHandleEventMouse&&!t._hasMouseListeners()&&(t.shouldHandleEventMouse=!1),t._hasPointerListeners()||vs.emit(cs.REMOVE_POINTER_EVENT_PROCESSOR,t)})),e},e._handleEventMouse=function(t){switch(t.type){case ee.MOUSE_DOWN:return this._handleMouseDown(t);case ee.MOUSE_MOVE:return this._handleMouseMove(t);case ee.MOUSE_UP:return this._handleMouseUp(t);case ee.MOUSE_WHEEL:return this._handleMouseWheel(t);case ee.MOUSE_LEAVE:return this._handleMouseLeave(t);case ee.MOUSE_ENTER:return this._handleMouseEnter(t);default:return!1}},e._handleMouseDown=function(t){var e=this._node,i=e._getUITransformComp();return!(!e||!i||(t.getLocation(ds),!i.hitTest(ds,t.windowId)||(t.type=$t.MOUSE_DOWN,t.bubbles=!0,e.dispatchEvent(t),t.propagationStopped=!0,0)))},e._handleMouseMove=function(t){var e=this._node,i=e._getUITransformComp();return!(!e||!i||this._isMouseLeaveWindow||(t.getLocation(ds),i.hitTest(ds,t.windowId)?(this.previousMouseIn||(_s&&_s!==e&&(t.type=$t.MOUSE_LEAVE,_s.dispatchEvent(t),_s.eventProcessor.previousMouseIn=!1),_s=e,t.type=$t.MOUSE_ENTER,e.dispatchEvent(t),this.previousMouseIn=!0),t.type=$t.MOUSE_MOVE,t.bubbles=!0,e.dispatchEvent(t),t.propagationStopped=!0,0):(this.previousMouseIn&&(t.type=$t.MOUSE_LEAVE,e.dispatchEvent(t),this.previousMouseIn=!1,_s=null),1)))},e._handleMouseUp=function(t){var e=this._node,i=e._getUITransformComp();return!(!e||!i||(t.getLocation(ds),!i.hitTest(ds,t.windowId)||(t.type=$t.MOUSE_UP,t.bubbles=!0,e.dispatchEvent(t),t.propagationStopped=!0,0)))},e._handleMouseWheel=function(t){var e=this._node,i=e._getUITransformComp();return!(!e||!i||(t.getLocation(ds),!i.hitTest(ds,t.windowId)||(t.type=$t.MOUSE_WHEEL,t.bubbles=!0,e.dispatchEvent(t),t.propagationStopped=!0,0)))},e._handleMouseLeave=function(t){return this._isMouseLeaveWindow=!0,this.previousMouseIn&&(t.type=$t.MOUSE_LEAVE,this._node.dispatchEvent(t),this.previousMouseIn=!1,_s=null),!1},e._handleMouseEnter=function(){return this._isMouseLeaveWindow=!1,!1},e._handleEventTouch=function(t){try{switch(t.type){case ee.TOUCH_START:return this._handleTouchStart(t);case ee.TOUCH_MOVE:return this._handleTouchMove(t);case ee.TOUCH_END:return this._handleTouchEnd(t);case ee.TOUCH_CANCEL:return this._handleTouchCancel(t);default:return!1}}catch(t){throw this.claimedTouchIdList.length=0,t}},e._handleTouchStart=function(t){var e=this.node,i=e._getUITransformComp();return!(!e||!i||(t.getLocation(ds),!i.hitTest(ds,t.windowId)||(t.type=$t.TOUCH_START,t.bubbles=!0,this._dispatchingTouch=t.touch,e.dispatchEvent(t),0)))},e._handleTouchMove=function(t){var e=this.node;return!(!e||!e._getUITransformComp()||(t.type=$t.TOUCH_MOVE,t.bubbles=!0,this._dispatchingTouch=t.touch,e.dispatchEvent(t),0))},e._handleTouchEnd=function(t){var e=this.node,i=e._getUITransformComp();e&&i&&(t.getLocation(ds),i.hitTest(ds,t.windowId)?t.type=$t.TOUCH_END:t.type=$t.TOUCH_CANCEL,t.bubbles=!0,e.dispatchEvent(t),this._dispatchingTouch=null)},e._handleTouchCancel=function(t){var e=this.node;e&&e._getUITransformComp()&&(t.type=$t.TOUCH_CANCEL,t.bubbles=!0,e.dispatchEvent(t),this._dispatchingTouch=null)},r(t,[{key:"isEnabled",get:function(){return this._isEnabled}},{key:"node",get:function(){return this._node}}]),t}());ys._maskComp=null,ys.callbacksInvoker=vs,ut.NodeEventProcessor=ys;var bs,Is,Ts,Es,Ds,Rs,Ss,Ms,As,ws,ks,Cs,xs,Ls,Ps,Os,Ns,Hs=$(),Bs=[],Fs=Math.PI/180;function Us(t,e){if(!t)return!1;Bs.length=0;for(var i=Bs,n=null,r=t;r;r=r.parent)i.push(r),r._uiProps._uiSkewComp&&(n=r);var s=!1;if(n){e.set(n.parent._mat);for(var o=i.indexOf(n);o>=0;--o){var a=i[o];K.fromSRT(Hs,a.rotation,a.position,a.scale),K.multiply(e,e,Hs)}s=!0}else e.set(t._mat);return Bs.length=0,s}function Gs(t,e){if(t.isSkewEnabled()&&(0!==t.x||0!==t.y))if(t.rotational){var i=-t.x*Fs,n=t.y*Fs,r=Math.cos(i),s=Math.sin(i),o=Math.cos(n),a=Math.sin(n),u=e.m00,h=e.m01,l=e.m04,p=e.m05;e.m00=o*u-s*h,e.m01=a*u+r*h,e.m04=o*l-s*p,e.m05=a*l+r*p}else{var c=Math.tan(t.x*Fs),f=Math.tan(t.y*Fs),_=e.m00,d=e.m01,g=e.m04,m=e.m05;e.m00=_+g*f,e.m01=d+m*f,e.m04=g+_*c,e.m05=m+d*c}}var Vs=M.Destroying,zs=M.DontDestroy,js=M.Deactivating,Ws=$t.TRANSFORM_CHANGED,Xs=$t.ACTIVE_CHANGED,qs=t("a",1),Ks=new s("Node");function Ys(t){return t?"string"==typeof t?C(t):t:(n(3804),null)}var Qs,Js,Zs,$s,to,eo,io,no,ro,so,oo,ao,uo,ho,lo,po,co,fo,_o,go,mo,vo,yo,bo,Io,To,Eo,Do,Ro,So,Mo,Ao,wo,ko,Co,xo,Lo,Po,Oo,No,Ho,Bo,Fo,Uo,Go,Vo,zo,jo,Wo,Xo,qo,Ko,Yo,Qo,Jo,Zo,$o,ta,ea,ia,na,ra,sa,oa,aa,ua,ha,la,pa,ca,fa,_a,da,ga,ma,va,ya,ba,Ia,Ta,Ea,Da,Ra,Sa,Ma,Aa,wa,ka,Ca,xa,La,Pa,Oa,Na,Ha,Ba,Fa,Ua,Ga,Va,za,ja,Wa,Xa,qa,Ka,Ya,Qa,Ja,Za,$a,tu,eu,iu,nu,ru,su,ou,au,uu,hu,lu,pu,cu,fu,_u,du,gu,mu,vu,yu=tt(),bu=tt(),Iu=et(),Tu=et(),Eu=et(),Du=new q,Ru=$(),Su=$(),Mu=[],Au=Symbol("ReserveContentsForAllSyncablePrefab"),wu=0,ku=0,Cu=t("N",(bs=F("cc.Node"),Is=V(W),Ts=V(hs),bs((Ns=function(t){i(s,t);var e=s.prototype;function s(e){var i;return void 0===e&&(e="New Node"),(i=t.call(this,e)||this)._parent=Rs&&Rs(),i._children=Ss&&Ss(),i._active=Ms&&Ms(),i._components=As&&As(),i._prefab=ws&&ws(),i._scene=null,i._activeInHierarchy=!1,i._id=Ks.getNewId(),i._eventProcessor=new ys(S(i)),i._eventMask=0,i._siblingIndex=0,i._originalSceneId="",i._uiProps=new ps(S(i)),i._static=!1,i._lpos=ks&&ks(),i._lrot=Cs&&Cs(),i._lscale=xs&&xs(),i._mobility=Ls&&Ls(),i._layer=Ps&&Ps(),i._euler=Os&&Os(),i._transformFlags=rs.TRS|rs.SKEW,i._eulerDirty=!1,i._flagChangeVersion=0,i._hasChangedFlags=0,i._pos=new W,i._rot=new Q,i._scale=new W(1,1,1),i._mat=new K,i}return e._setActiveInHierarchy=function(t){this._activeInHierarchy=t},s._setScene=function(t){t._updateScene()},s._incSkewCompCount=function(){++ku},s._decSkewCompCount=function(){--ku},s._findComponent=function(t,e){var i=e,n=t._components;if(i._sealed)for(var r=0;r<n.length;++r){var s=n[r];if(s.constructor===e)return s}else for(var o=0;o<n.length;++o){var a=n[o];if(a instanceof e)return a}return null},s._findComponents=function(t,e,i){var n=e,r=t._components;if(n._sealed)for(var s=0;s<r.length;++s){var o=r[s];o.constructor===e&&i.push(o)}else for(var a=0;a<r.length;++a){var u=r[a];u instanceof e&&i.push(u)}},s._findChildComponent=function(t,e){for(var i=0;i<t.length;++i){var n=t[i],r=s._findComponent(n,e);if(r)return r;if(n._children.length>0&&(r=s._findChildComponent(n._children,e)))return r}return null},s._findChildComponents=function(t,e,i){for(var n=0;n<t.length;++n){var r=t[n];s._findComponents(r,e,i),r._children.length>0&&s._findChildComponents(r._children,e,i)}},e.getWritableComponents=function(){return this._components},e._updateScene=function(){null==this._parent?n(1640,this.name,this.uuid):this._scene=this._parent._scene},e.attr=function(t){c(this,t)},e.getParent=function(){return this._parent},e.modifyParent=function(t){this._parent=t},e.setParent=function(t,e){if(void 0===e&&(e=!1),e&&this.updateWorldTransform(),this._parent!==t){var i=this._parent,n=t;if(this._parent=n,this._siblingIndex=0,this._onSetParent(i,e),this.emit&&this.emit($t.PARENT_CHANGED,i),i&&!(i._objFlags&Vs)){var r=i._children.indexOf(this);i._children.splice(r,1),i._updateSiblingIndex(),i.emit&&i.emit($t.CHILD_REMOVED,this)}n&&(n._children.push(this),this._siblingIndex=n._children.length-1,n.emit&&n.emit($t.CHILD_ADDED,this)),this._onHierarchyChanged(i)}},e.getChildByUuid=function(t){if(!t)return k("Invalid uuid"),null;for(var e=this._children,i=0,n=e.length;i<n;i++)if(e[i]._id===t)return e[i];return null},e.getChildByName=function(t){if(!t)return k("Invalid name"),null;for(var e=this._children,i=0,n=e.length;i<n;i++)if(e[i]._name===t)return e[i];return null},e.getChildByPath=function(t){for(var e,i=t.split("/"),n=this,r=function(){var t=i[s];if(0===t.length)return 0;var e=n.children.find((function(e){return e.name===t}));if(!e)return{v:null};n=e},s=0;s<i.length;++s)if(0!==(e=r())&&e)return e.v;return n},e.addChild=function(t){t.setParent(this)},e.insertChild=function(t,e){t.setParent(this),t.setSiblingIndex(e)},e.getSiblingIndex=function(){return this._siblingIndex},e.setSiblingIndex=function(t){if(this._parent)if(this._parent._objFlags&js)n(3821);else{var e=this._parent._children;t=t>=0?t:e.length+t;var i=e.indexOf(this);t!==i&&(e.splice(i,1),t<e.length?e.splice(t,0,this):e.push(this),this._parent._updateSiblingIndex(),this._onSiblingIndexChanged&&this._onSiblingIndexChanged(t),this._eventProcessor.onUpdatingSiblingIndex())}},e.walk=function(t,e){var i=1,n=null,r=null,o=0,a=s._stacks[s._stackId];a||(a=[],s._stacks.push(a)),s._stackId++,a.length=0,a[0]=this;for(var u=null,h=!1;i;)if(r=a[--i])if(!h&&t?t(r):h&&e&&e(r),a[i]=null,h){if(u===this._parent)break;if(h=!1,n)if(n[++o])a[i]=n[o],i++;else if(u&&(a[i]=u,i++,h=!0,u._parent?(o=(n=u._parent._children).indexOf(u),u=u._parent):(u=null,n=null),o<0))break}else r._children.length>0?(u=r,n=r._children,o=0,a[i]=n[o],i++):(a[i]=r,i++,h=!0);a.length=0,s._stackId--},e.removeFromParent=function(){this._parent&&this._parent.removeChild(this)},e.removeChild=function(t){this._children.indexOf(t)>-1&&(t.parent=null)},e.removeAllChildren=function(){for(var t=this._children,e=t.length-1;e>=0;e--){var i=t[e];i&&(i.parent=null)}this._children.length=0},e.isChildOf=function(t){var e=this;do{if(e===t)return!0;e=e._parent}while(e);return!1},e.getComponent=function(t){var e=Ys(t);return e?s._findComponent(this,e):null},e.getComponents=function(t){var e=Ys(t),i=[];return e&&s._findComponents(this,e,i),i},e.getComponentInChildren=function(t){var e=Ys(t);return e?s._findChildComponent(this._children,e):null},e.getComponentsInChildren=function(t){var e=Ys(t),i=[];return e&&(s._findComponents(this,e,i),s._findChildComponents(this._children,e,i)),i},e.addComponent=function(t){var e;if("string"==typeof t){if(!(e=C(t)))throw ut._RF.peek()&&n(3808,t),TypeError(m(3807,t))}else{if(!t)throw TypeError(m(3804));e=t}if("function"!=typeof e)throw TypeError(m(3809));if(!x(e,ut.Component))throw TypeError(m(3810));var i=e._requireComponent;if(i)if(Array.isArray(i))for(var r=0;r<i.length;r++){var s=i[r];this.getComponent(s)||this.addComponent(s)}else{var o=i;this.getComponent(o)||this.addComponent(o)}var a=new e;return a.node=this,this._components.push(a),this.emit($t.COMPONENT_ADDED,a),this._activeInHierarchy&&ut.director._nodeActivator.activateComp(a),a},e.removeComponent=function(t){if(t){var e=null;(e=t instanceof Zt?t:this.getComponent(t))&&e.destroy()}else n(3813)},e.on=function(t,e,i,n){switch(void 0===n&&(n=!1),t){case Ws:this._eventMask|=qs;break;case Xs:this._eventMask|=2}this._eventProcessor.on(t,e,i,n)},e.off=function(t,e,i,n){if(void 0===n&&(n=!1),this._eventProcessor.off(t,e,i,n),!this._eventProcessor.hasEventListener(t))switch(t){case Ws:this._eventMask&=-2;break;case Xs:this._eventMask&=-3}},e.once=function(t,e,i,n){this._eventProcessor.once(t,e,i,n)},e.emit=function(t,e,i,n,r,s){this._eventProcessor.emit(t,e,i,n,r,s)},e.dispatchEvent=function(t){this._eventProcessor.dispatchEvent(t)},e.hasEventListener=function(t,e,i){return this._eventProcessor.hasEventListener(t,e,i)},e.targetOff=function(t){this._eventProcessor.targetOff(t),this._eventMask&qs&&!this._eventProcessor.hasEventListener(Ws)&&(this._eventMask&=-2),2&this._eventMask&&!this._eventProcessor.hasEventListener(Xs)&&(this._eventMask&=-3)},e.destroy=function(){return!!t.prototype.destroy.call(this)&&(this.active=!1,!0)},e.destroyAllChildren=function(){for(var t=this._children,e=0;e<t.length;++e)t[e].destroy()},e._removeComponent=function(t){if(t){if(!(this._objFlags&Vs)){var e=this._components.indexOf(t);-1!==e?(this._components.splice(e,1),this.emit($t.COMPONENT_REMOVED,t)):t.node!==this&&n(3815)}}else n(3814)},e._updateSiblingIndex=function(){for(var t=0;t<this._children.length;++t)this._children[t]._siblingIndex=t;this.emit($t.CHILDREN_ORDER_CHANGED)},e._instantiate=function(t,e){return void 0===e&&(e=!1),t||(t=ut.instantiate._clone(this,this)),t._prefab,t._parent=null,t._onBatchCreated(e),t},e._onHierarchyChangedBase=function(){var t=this._parent;!this._persistNode||t instanceof ut.Scene||ut.game.removePersistRootNode(this);var e=this._active&&!(!t||!t._activeInHierarchy);this._activeInHierarchy!==e&&ut.director._nodeActivator.activateNode(this,e)},e._onPreDestroyBase=function(){this._objFlags|=Vs;var t=this._parent,e=!!t&&!!(t._objFlags&Vs);if(this._persistNode&&ut.game.removePersistRootNode(this),!e&&t){this.emit($t.PARENT_CHANGED,this);var i=t._children.indexOf(this);t._children.splice(i,1),this._siblingIndex=0,t._updateSiblingIndex(),t.emit&&t.emit($t.CHILD_REMOVED,this)}this.emit($t.NODE_DESTROYED,this),this._eventProcessor.destroy();for(var n=this._children,r=0;r<n.length;++r)n[r]._destroyImmediate();for(var s=this._components,o=0;o<s.length;++o)s[o]._destroyImmediate();return e},s.isNode=function(t){return t instanceof s&&(t.constructor===s||!(t instanceof ut.Scene))},e._onPreDestroy=function(){return this._onPreDestroyBase()},e[it]=function(t){t.writeThis()},e._onSetParent=function(t,e){void 0===e&&(e=!1);var i=this,n=i._parent;if(n&&(null!=t&&t._scene===n._scene||null==n._scene||i.walk(s._setScene)),e){if(n)if(n.updateWorldTransform(),nt(K.determinant(n._mat),0,st))d(14300),i._transformFlags|=rs.TRS,i.updateWorldTransform();else{var r=n._mat;if(ku>0){if(t){var o=Us(t,Su);K.fromSRT(Ru,i._lrot,i._lpos,i._lscale);var a=o?Su:t._mat;K.multiply(i._mat,a,Ru)}Us(n,Su)&&(r=Su)}K.multiply(Ru,K.invert(Ru,r),i._mat),K.toSRT(Ru,i._lrot,i._lpos,i._lscale)}else W.copy(i._lpos,i._pos),Q.copy(i._lrot,i._rot),W.copy(i._lscale,i._scale);i._eulerDirty=!0}i.invalidateChildren(rs.TRS)},e._onHierarchyChanged=function(t){this.eventProcessor.reattach(),this._onHierarchyChangedBase(t)},e._onBatchCreated=function(t){2&this._eventMask&&(this._activeInHierarchy||this.emit(Xs,this,!1)),this.hasChangedFlags=rs.TRS,this._children.forEach((function(e,i){e._siblingIndex=i,e._onBatchCreated(t)}))},e._onBeforeSerialize=function(){this.eulerAngles},e._onPostActivated=function(t){var e=this;2&e._eventMask&&e.emit(Xs,e,t);var i=this._eventProcessor;if(i.isEnabled===t&&ys.callbacksInvoker.emit(cs.MARK_LIST_DIRTY),i.setEnabled(t),t){e.invalidateChildren(rs.TRS);var n=e._uiProps&&e._uiProps.uiComp;n&&(n.setNodeDirty(),n.setTextureDirty(),n._markForUpdateRenderData())}},e.translate=function(t,e){var i=e||ns.LOCAL;if(i===ns.LOCAL)W.transformQuat(yu,t,this._lrot),this._lpos.x+=yu.x,this._lpos.y+=yu.y,this._lpos.z+=yu.z;else if(i===ns.WORLD)if(this._parent){Q.invert(Iu,this._parent.worldRotation),W.transformQuat(yu,t,Iu);var n=this.worldScale;this._lpos.x+=yu.x/n.x,this._lpos.y+=yu.y/n.y,this._lpos.z+=yu.z/n.z}else this._lpos.x+=t.x,this._lpos.y+=t.y,this._lpos.z+=t.z;this.invalidateChildren(rs.POSITION),this._eventMask&qs&&this.emit(Ws,rs.POSITION)},e.rotate=function(t,e){var i=e||ns.LOCAL;if(Q.normalize(Iu,t),i===ns.LOCAL)Q.multiply(this._lrot,this._lrot,Iu);else if(i===ns.WORLD){var n=this.worldRotation;Q.multiply(Tu,Iu,n),Q.invert(Iu,n),Q.multiply(Tu,Iu,Tu),Q.multiply(this._lrot,this._lrot,Tu)}this._eulerDirty=!0,this.invalidateChildren(rs.ROTATION),this._eventMask&qs&&this.emit(Ws,rs.ROTATION)},e.lookAt=function(t,e){this.getWorldPosition(yu),W.subtract(yu,yu,t),W.normalize(yu,yu),Q.fromViewUp(Iu,yu,e),this.setWorldRotation(Iu)},e.invalidateChildren=function(t){var e,i,n=0,r=0,s=0,o=0,a=t|rs.POSITION;for(Mu[0]=this;n>=0;){if(o=(e=Mu[n--]).hasChangedFlags,e.isValid&&(e._transformFlags&o&t)!==t)for(e._transformFlags|=t,e.hasChangedFlags=o|t,s=(i=e._children).length,r=0;r<s;r++)Mu[++n]=i[r];t=a}},e.updateWorldTransform=function(){if(this._transformFlags){for(var t,e,i,n=this,r=0;n&&n._transformFlags;)Mu[r++]=n,n=n._parent;for(var s=0,o=0,a=0,u=null,h=!1;r;){if(e=(t=Mu[--r])._mat,i=t._pos,o=(s|=t._transformFlags)&rs.POSITION,a=s&rs.RSS,n){if(o&&!a&&(W.transformMat4(i,t._lpos,n._mat),e.m12=i.x,e.m13=i.y,e.m14=i.z),a){var l=e;K.fromSRT(Ru,t._lrot,t._lpos,t._lscale),ku>0&&(h=Us(n,Su),((u=t._uiProps._uiSkewComp)||h)&&(K.multiply(Su,Su,Ru),u&&Gs(u,Ru),l=Su)),K.multiply(e,n._mat,Ru);var p=s&rs.ROTATION?t._rot:null;K.toSRT(l,p,i,t._scale),h&&W.transformMat4(i,t._lpos,n._mat)}}else o&&(W.copy(i,t._lpos),e.m12=i.x,e.m13=i.y,e.m14=i.z),a&&(s&rs.ROTATION&&Q.copy(t._rot,t._lrot),s&rs.SCALE&&W.copy(t._scale,t._lscale),K.fromSRT(e,t._rot,t._pos,t._scale),ku>0&&(u=t._uiProps._uiSkewComp)&&Gs(u,e));t._transformFlags=rs.NONE,n=t}}},e.setPosition=function(t,e,i){var n=this._lpos;void 0===e?W.copy(n,t):(void 0===i&&(i=n.z),W.set(n,t,e,i)),this.invalidateChildren(rs.POSITION),this._eventMask&qs&&this.emit(Ws,rs.POSITION)},e.getPosition=function(t){return t?W.set(t,this._lpos.x,this._lpos.y,this._lpos.z):W.copy(new W,this._lpos)},e.setRotation=function(t,e,i,n){void 0===e?Q.copy(this._lrot,t):Q.set(this._lrot,t,e,i,n),this._eulerDirty=!0,this.invalidateChildren(rs.ROTATION),this._eventMask&qs&&this.emit(Ws,rs.ROTATION)},e.setRotationFromEuler=function(t,e,i){if(void 0===e)W.copy(this._euler,t),Q.fromEuler(this._lrot,t.x,t.y,t.z);else{var n=void 0===i?this._euler.z:i;W.set(this._euler,t,e,n),Q.fromEuler(this._lrot,t,e,n)}this._eulerDirty=!1,this.invalidateChildren(rs.ROTATION),this._eventMask&qs&&this.emit(Ws,rs.ROTATION)},e.getRotation=function(t){return t?Q.set(t,this._lrot.x,this._lrot.y,this._lrot.z,this._lrot.w):Q.copy(new Q,this._lrot)},e.setScale=function(t,e,i){var n=this._lscale;void 0===e?W.copy(n,t):(void 0===i&&(i=n.z),W.set(n,t,e,i)),this.invalidateChildren(rs.SCALE),this._eventMask&qs&&this.emit(Ws,rs.SCALE)},e.getScale=function(t){return t?W.set(t,this._lscale.x,this._lscale.y,this._lscale.z):W.copy(new W,this._lscale)},e.inverseTransformPoint=function(t,e){W.copy(t,e);for(var i=this,n=0;i._parent;)Mu[n++]=i,i=i._parent;for(;n>=0;)W.transformInverseRTS(t,t,i._lrot,i._lpos,i._lscale),i=Mu[--n];return t},e.setWorldPosition=function(t,e,i){var n=this._pos;void 0===e?W.copy(n,t):W.set(n,t,e,i);var r=this._parent,s=this._lpos;r?(r.updateWorldTransform(),W.transformMat4(s,n,K.invert(Ru,r._mat))):W.copy(s,n),this.invalidateChildren(rs.POSITION),this._eventMask&qs&&this.emit(Ws,rs.POSITION)},e.getWorldPosition=function(t){return this.updateWorldTransform(),t?W.copy(t,this._pos):W.copy(new W,this._pos)},e.setWorldRotation=function(t,e,i,n){var r=this._rot;void 0===e?Q.copy(r,t):Q.set(r,t,e,i,n),this._parent?(this._parent.updateWorldTransform(),Q.multiply(this._lrot,Q.conjugate(this._lrot,this._parent._rot),r)):Q.copy(this._lrot,r),this._eulerDirty=!0,this.invalidateChildren(rs.ROTATION),this._eventMask&qs&&this.emit(Ws,rs.ROTATION)},e.setWorldRotationFromEuler=function(t,e,i){Q.fromEuler(Iu,t,e,i),this.setWorldRotation(Iu)},e.getWorldRotation=function(t){return this.updateWorldTransform(),t?Q.copy(t,this._rot):Q.copy(new Q,this._rot)},e.setWorldScale=function(t,e,i){var n=this,r=n._parent;r&&n.updateWorldTransform();var s=n._scale;void 0===e?W.copy(s,t):W.set(s,t,e,i);var o=rs.NONE;if(r){var a=n._mat;n._uiProps._uiSkewComp&&(K.fromSRT(Ru,n._lrot,n._lpos,n._lscale),K.multiply(a,r._mat,Ru));var u=W.set(bu,a.m00,a.m01,a.m02).length(),h=W.set(bu,a.m04,a.m05,a.m06).length(),l=W.set(bu,a.m08,a.m09,a.m10).length();0===u?(yu.x=s.x,a.m00=1,o=rs.ROTATION):yu.x=s.x/u,0===h?(yu.y=s.y,a.m05=1,o=rs.ROTATION):yu.y=s.y/h,0===l?(yu.z=s.z,a.m10=1,o=rs.ROTATION):yu.z=s.z/l,K.scale(Ru,a,yu),K.multiply(Su,K.invert(Su,r._mat),Ru),q.fromQuat(Du,Q.conjugate(Eu,n._lrot)),q.multiplyMat4(Du,Du,Su);var p=n._lscale;p.x=W.set(yu,Du.m00,Du.m01,Du.m02).length(),p.y=W.set(yu,Du.m03,Du.m04,Du.m05).length(),p.z=W.set(yu,Du.m06,Du.m07,Du.m08).length(),0!==p.x&&0!==p.y&&0!==p.z||(o=rs.ROTATION)}else W.copy(n._lscale,s);n.invalidateChildren(rs.SCALE|o),n._eventMask&qs&&n.emit(Ws,rs.SCALE|o)},e.getWorldScale=function(t){return this.updateWorldTransform(),t?W.copy(t,this._scale):W.copy(new W,this._scale)},e.getWorldMatrix=function(t){this.updateWorldTransform();var e=t||new K;return K.copy(e,this._mat)},e.getWorldRS=function(t){this.updateWorldTransform();var e=t||new K;return K.copy(e,this._mat),e.m12=0,e.m13=0,e.m14=0,e},e.getWorldRT=function(t){this.updateWorldTransform();var e=t||new K;return K.fromRT(e,this._rot,this._pos)},e.setRTS=function(t,e,i){var n=0;t&&(n|=rs.ROTATION,void 0!==t.w?(Q.copy(this._lrot,t),this._eulerDirty=!0):(W.copy(this._euler,t),Q.fromEuler(this._lrot,t.x,t.y,t.z),this._eulerDirty=!1)),e&&(W.copy(this._lpos,e),n|=rs.POSITION),i&&(W.copy(this._lscale,i),n|=rs.SCALE),n&&(this.invalidateChildren(n),this._eventMask&qs&&this.emit(Ws,n))},e.isTransformDirty=function(){return this._transformFlags!==rs.NONE},e.pauseSystemEvents=function(t){this._eventProcessor.setEnabled(!1,t)},e.resumeSystemEvents=function(t){this._eventProcessor.setEnabled(!0,t)},s.resetHasChangedFlags=function(){wu+=1},s.clearNodeArray=function(){s.ClearFrame<s.ClearRound?s.ClearFrame++:(s.ClearFrame=0,Mu.length=0)},e.getPathInHierarchy=function(){for(var t=this.name,e=this.parent;e&&!(e instanceof ut.Scene);)t=e.name+"/"+t,e=e.parent;return t},e._getUITransformComp=function(){return this._uiProps.uiTransformComp},r(s,[{key:"components",get:function(){return this._components}},{key:"_persistNode",get:function(){return(this._objFlags&zs)>0},set:function(t){t?this._objFlags|=zs:this._objFlags&=~zs}},{key:"name",get:function(){return this._name},set:function(t){this._name=t}},{key:"uuid",get:function(){return this._id}},{key:"children",get:function(){return this._children}},{key:"active",get:function(){return this._active},set:function(t){if(t=!!t,this._active!==t){this._active=t;var e=this._parent;e&&e._activeInHierarchy&&ut.director._nodeActivator.activateNode(this,t)}}},{key:"activeInHierarchy",get:function(){return this._activeInHierarchy}},{key:"parent",get:function(){return this._parent},set:function(t){this.setParent(t)}},{key:"scene",get:function(){return this._scene}},{key:"eventProcessor",get:function(){return this._eventProcessor}},{key:"prefab",get:function(){return this._prefab}},{key:"id",set:function(t){this._id=t}},{key:"siblingIndex",get:function(){return this._siblingIndex},set:function(t){this._siblingIndex=t}},{key:"position",get:function(){return this._lpos},set:function(t){this.setPosition(t)}},{key:"x",get:function(){return this._lpos.x},set:function(t){this.setPosition(t,this._lpos.y,this._lpos.z)}},{key:"y",get:function(){return this._lpos.y},set:function(t){this.setPosition(this._lpos.x,t,this._lpos.z)}},{key:"z",get:function(){return this._lpos.z},set:function(t){this.setPosition(this._lpos.x,this._lpos.y,t)}},{key:"worldPosition",get:function(){return this.updateWorldTransform(),this._pos},set:function(t){this.setWorldPosition(t)}},{key:"worldPositionX",get:function(){return this.updateWorldTransform(),this._pos.x},set:function(t){this.setWorldPosition(t,this._pos.y,this._pos.z)}},{key:"worldPositionY",get:function(){return this.updateWorldTransform(),this._pos.y},set:function(t){this.setWorldPosition(this._pos.x,t,this._pos.z)}},{key:"worldPositionZ",get:function(){return this.updateWorldTransform(),this._pos.z},set:function(t){this.setWorldPosition(this._pos.x,this._pos.y,t)}},{key:"rotation",get:function(){return this._lrot},set:function(t){this.setRotation(t)}},{key:"eulerAngles",get:function(){return this._eulerDirty&&(Q.toEuler(this._euler,this._lrot),this._eulerDirty=!1),this._euler},set:function(t){this.setRotationFromEuler(t.x,t.y,t.z)}},{key:"angle",get:function(){return this.eulerAngles.z},set:function(t){W.set(this._euler,0,0,t),Q.fromAngleZ(this._lrot,t),this._eulerDirty=!1,this.invalidateChildren(rs.ROTATION),this._eventMask&qs&&this.emit(Ws,rs.ROTATION)}},{key:"worldRotation",get:function(){return this.updateWorldTransform(),this._rot},set:function(t){this.setWorldRotation(t)}},{key:"scale",get:function(){return this._lscale},set:function(t){this.setScale(t)}},{key:"worldScale",get:function(){return this.updateWorldTransform(),this._scale},set:function(t){this.setWorldScale(t)}},{key:"matrix",set:function(t){K.toSRT(t,this._lrot,this._lpos,this._lscale),this.invalidateChildren(rs.TRS),this._eulerDirty=!0,this._eventMask&qs&&this.emit(Ws,rs.TRS)}},{key:"worldMatrix",get:function(){return this.updateWorldTransform(),this._mat}},{key:"forward",get:function(){return W.transformQuat(new W,W.FORWARD,this.worldRotation)},set:function(t){var e=t.length();W.multiplyScalar(yu,t,-1/e),Q.fromViewUp(Iu,yu),this.setWorldRotation(Iu)}},{key:"up",get:function(){return W.transformQuat(new W,W.UP,this.worldRotation)}},{key:"right",get:function(){return W.transformQuat(new W,W.RIGHT,this.worldRotation)}},{key:"mobility",get:function(){return this._mobility},set:function(t){this._mobility!==t&&(this._mobility=t,this.emit($t.MOBILITY_CHANGED))}},{key:"layer",get:function(){return this._layer},set:function(t){var e=this;if(e._layer!==t){e._layer=t;var i=e._uiProps&&e._uiProps.uiComp;i&&(i.setNodeDirty(),i._markForUpdateRenderData()),e.emit($t.LAYER_CHANGED,e._layer)}}},{key:"flagChangedVersion",get:function(){return this._flagChangeVersion}},{key:"hasChangedFlags",get:function(){return this._flagChangeVersion===wu?this._hasChangedFlags:0},set:function(t){this._flagChangeVersion=wu,this._hasChangedFlags=t}}]),s}(L),Ns.idGenerator=Ks,Ns._stacks=[[]],Ns._stackId=0,Ns.EventType=$t,Ns.NodeSpace=ns,Ns.TransformDirtyBit=rs,Ns.TransformBit=rs,Ns.reserveContentsForAllSyncablePrefabTag=Au,Ns.ClearFrame=0,Ns.ClearRound=1e3,P((Ds=Ns).prototype,"_persistNode",[rt],Object.getOwnPropertyDescriptor(Ds.prototype,"_persistNode"),Ds.prototype),Rs=U(Ds.prototype,"_parent",[G],(function(){return null})),Ss=U(Ds.prototype,"_children",[G],(function(){return[]})),Ms=U(Ds.prototype,"_active",[G],(function(){return!0})),As=U(Ds.prototype,"_components",[G],(function(){return[]})),ws=U(Ds.prototype,"_prefab",[G],(function(){return null})),ks=U(Ds.prototype,"_lpos",[G],(function(){return new W})),Cs=U(Ds.prototype,"_lrot",[G],(function(){return new Q})),xs=U(Ds.prototype,"_lscale",[G],(function(){return new W(1,1,1)})),Ls=U(Ds.prototype,"_mobility",[G],(function(){return hs.Static})),Ps=U(Ds.prototype,"_layer",[G],(function(){return wt.Enum.DEFAULT})),Os=U(Ds.prototype,"_euler",[G],(function(){return new W})),P(Ds.prototype,"eulerAngles",[Is],Object.getOwnPropertyDescriptor(Ds.prototype,"eulerAngles"),Ds.prototype),P(Ds.prototype,"mobility",[Ts],Object.getOwnPropertyDescriptor(Ds.prototype,"mobility"),Ds.prototype),Es=Ds))||Es));ut.Node=Cu;var xu=new W(0,1,0),Lu=new W,Pu=new X,Ou=new Y,Nu=new Q,Hu=function(t){var e=1/Math.max(Math.max(Math.max(t.x,t.y),t.z),1e-4);e<1&&(t.x*=e,t.y*=e,t.z*=e)},Bu=t("W",(Qs=F("cc.AmbientInfo"),Js=V(O),Zs=ot("_skyColor"),$s=ot("_skyIllum"),to=ot("_groundAlbedo"),Qs((io=function(){function t(){this._skyColorHDR=no&&no(),this._skyIllumHDR=ro&&ro(),this._groundAlbedoHDR=so&&so(),this._skyColorLDR=oo&&oo(),this._skyIllumLDR=ao&&ao(),this._groundAlbedoLDR=uo&&uo(),this._resource=null}return t.prototype.activate=function(t){this._resource=t,t.initialize(this)},r(t,[{key:"skyColorHDR",get:function(){return this._skyColorHDR}},{key:"groundAlbedoHDR",get:function(){return this._groundAlbedoHDR}},{key:"skyIllumHDR",get:function(){return this._skyIllumHDR}},{key:"skyColorLDR",get:function(){return this._skyColorLDR}},{key:"groundAlbedoLDR",get:function(){return this._groundAlbedoLDR}},{key:"skyIllumLDR",get:function(){return this._skyIllumLDR}},{key:"skyLightingColor",get:function(){var t=Yr().isHDR;return Pu.set(t?this._skyColorHDR:this._skyColorLDR),Hu(Pu),Ou.set(255*Pu.x,255*Pu.y,255*Pu.z,255)},set:function(t){Pu.set(t.x,t.y,t.z,t.w),Yr().isHDR?this._skyColorHDR.set(Pu):this._skyColorLDR.set(Pu),this._resource&&this._resource.skyColor.set(Pu)}},{key:"skyColor",set:function(t){Yr().isHDR?this._skyColorHDR.set(t):this._skyColorLDR.set(t),this._resource&&this._resource.skyColor.set(t)}},{key:"skyIllum",get:function(){return Yr().isHDR?this._skyIllumHDR:this._skyIllumLDR},set:function(t){Yr().isHDR?this._skyIllumHDR=t:this._skyIllumLDR=t,this._resource&&(this._resource.skyIllum=t)}},{key:"groundLightingColor",get:function(){var t=Yr().isHDR;return Pu.set(t?this._groundAlbedoHDR:this._groundAlbedoLDR),Hu(Pu),Ou.set(255*Pu.x,255*Pu.y,255*Pu.z,255)},set:function(t){Pu.set(t.x,t.y,t.z,t.w),Yr().isHDR?this._groundAlbedoHDR.set(Pu):this._groundAlbedoLDR.set(Pu),this._resource&&this._resource.groundAlbedo.set(Pu)}},{key:"groundAlbedo",set:function(t){Yr().isHDR?this._groundAlbedoHDR.set(t):this._groundAlbedoLDR.set(t),this._resource&&this._resource.groundAlbedo.set(t)}}]),t}(),P(io.prototype,"skyIllum",[Js],Object.getOwnPropertyDescriptor(io.prototype,"skyIllum"),io.prototype),no=U(io.prototype,"_skyColorHDR",[G,Zs],(function(){return new X(.2,.5,.8,1)})),ro=U(io.prototype,"_skyIllumHDR",[G,$s],(function(){return Qr.SKY_ILLUM})),so=U(io.prototype,"_groundAlbedoHDR",[G,to],(function(){return new X(.2,.2,.2,1)})),oo=U(io.prototype,"_skyColorLDR",[G],(function(){return new X(.2,.5,.8,1)})),ao=U(io.prototype,"_skyIllumLDR",[G],(function(){return Qr.SKY_ILLUM})),uo=U(io.prototype,"_groundAlbedoLDR",[G],(function(){return new X(.2,.2,.2,1)})),eo=io))||eo));ht.AmbientInfo=Bu;var Fu=t("X",(ho=F("cc.SkyboxInfo"),lo=V(Jr),po=V(tn),co=V(O),fo=V(tn),_o=V(tn),go=V(jr),mo=V(tn),vo=ot("_envmap"),yo=V(tn),bo=V(tn),Io=V(tn),To=V(jr),Eo=V(tn),Do=V(tn),ho((So=function(){function t(){this._envLightingType=Mo&&Mo(),this._envmapHDR=Ao&&Ao(),this._envmapLDR=wo&&wo(),this._diffuseMapHDR=ko&&ko(),this._diffuseMapLDR=Co&&Co(),this._enabled=xo&&xo(),this._useHDR=Lo&&Lo(),this._editableMaterial=Po&&Po(),this._reflectionHDR=Oo&&Oo(),this._reflectionLDR=No&&No(),this._rotationAngle=Ho&&Ho(),this._resource=null}var e=t.prototype;return e.activate=function(t){this.envLightingType=this._envLightingType,this._resource=t,t.initialize(this),t.setEnvMaps(this._envmapHDR,this._envmapLDR),t.setDiffuseMaps(this._diffuseMapHDR,this._diffuseMapLDR),t.setSkyboxMaterial(this._editableMaterial),t.setReflectionMaps(this._reflectionHDR,this._reflectionLDR),t.setRotationAngle(this._rotationAngle),t.activate()},e.updateEnvMap=function(t){t||(this.applyDiffuseMap=!1,this.useIBL=!1,this.envLightingType=Jr.HEMISPHERE_DIFFUSE,d(15001));var e=this._resource;e&&(e.setEnvMaps(this._envmapHDR,this._envmapLDR),e.setDiffuseMaps(this._diffuseMapHDR,this._diffuseMapLDR),e.setReflectionMaps(this._reflectionHDR,this._reflectionLDR),e.useDiffuseMap=this.applyDiffuseMap,e.envmap=t)},e.setMaterialProperty=function(t,e,i){var n=this._resource;if(n){var r=n.editableMaterial;n.enabled&&r&&(r.setProperty(t,e,i),r.passes.forEach((function(t){t.update()})))}},r(t,[{key:"applyDiffuseMap",get:function(){return Jr.DIFFUSEMAP_WITH_REFLECTION===this._envLightingType},set:function(t){this._resource&&(this._resource.useDiffuseMap=t)}},{key:"enabled",get:function(){return this._enabled},set:function(t){this._enabled!==t&&(this._enabled=t,this._resource&&(this._resource.enabled=this._enabled))}},{key:"envLightingType",get:function(){return this._envLightingType},set:function(t){this.envmap||Jr.HEMISPHERE_DIFFUSE===t?(Jr.HEMISPHERE_DIFFUSE===t?(this.useIBL=!1,this.applyDiffuseMap=!1):Jr.AUTOGEN_HEMISPHERE_DIFFUSE_WITH_REFLECTION===t?(this.useIBL=!0,this.applyDiffuseMap=!1):Jr.DIFFUSEMAP_WITH_REFLECTION===t&&(this.useIBL=!0,this.applyDiffuseMap=!0),this._envLightingType=t):(this.useIBL=!1,this.applyDiffuseMap=!1,this._envLightingType=Jr.HEMISPHERE_DIFFUSE,d(15001))}},{key:"useIBL",get:function(){return Jr.HEMISPHERE_DIFFUSE!==this._envLightingType},set:function(t){this._resource&&(this._resource.useIBL=t)}},{key:"useHDR",get:function(){return Yr().isHDR=this._useHDR,this._useHDR},set:function(t){Yr().isHDR=t,this._useHDR=t;var e=this._resource;e&&this.envLightingType===Jr.DIFFUSEMAP_WITH_REFLECTION&&(null===this.diffuseMap?(this.envLightingType=Jr.AUTOGEN_HEMISPHERE_DIFFUSE_WITH_REFLECTION,d(15e3)):this.diffuseMap.isDefault&&d(15002)),e&&(e.useHDR=this._useHDR,e.updateMaterialRenderInfo())}},{key:"envmap",get:function(){return Yr().isHDR?this._envmapHDR:this._envmapLDR},set:function(t){var e=Yr().isHDR;e?(this._envmapHDR=t,this._reflectionHDR=null):(this._envmapLDR=t,this._reflectionLDR=null),t||(e?this._diffuseMapHDR=null:this._diffuseMapLDR=null,this.applyDiffuseMap=!1,this.useIBL=!1,this.envLightingType=Jr.HEMISPHERE_DIFFUSE,d(15001));var i=this._resource;i&&(i.setEnvMaps(this._envmapHDR,this._envmapLDR),i.setDiffuseMaps(this._diffuseMapHDR,this._diffuseMapLDR),i.setReflectionMaps(this._reflectionHDR,this._reflectionLDR),i.useDiffuseMap=this.applyDiffuseMap,i.envmap=t)}},{key:"rotationAngle",get:function(){return this._rotationAngle},set:function(t){this._rotationAngle=t,this._resource&&this._resource.setRotationAngle(this._rotationAngle)}},{key:"diffuseMap",get:function(){return Yr().isHDR?this._diffuseMapHDR:this._diffuseMapLDR},set:function(t){Yr().isHDR?this._diffuseMapHDR=t:this._diffuseMapLDR=t,this._resource&&this._resource.setDiffuseMaps(this._diffuseMapHDR,this._diffuseMapLDR)}},{key:"reflectionMap",get:function(){return Yr().isHDR?this._reflectionHDR:this._reflectionLDR},set:function(t){Yr().isHDR?this._reflectionHDR=t:this._reflectionLDR=t,this._resource&&this._resource.setReflectionMaps(this._reflectionHDR,this._reflectionLDR)}},{key:"skyboxMaterial",get:function(){return this._editableMaterial},set:function(t){this._editableMaterial=t,this._resource&&this._resource.setSkyboxMaterial(this._editableMaterial)}}]),t}(),P(So.prototype,"envLightingType",[lo],Object.getOwnPropertyDescriptor(So.prototype,"envLightingType"),So.prototype),P(So.prototype,"envmap",[po],Object.getOwnPropertyDescriptor(So.prototype,"envmap"),So.prototype),P(So.prototype,"rotationAngle",[co],Object.getOwnPropertyDescriptor(So.prototype,"rotationAngle"),So.prototype),P(So.prototype,"diffuseMap",[fo],Object.getOwnPropertyDescriptor(So.prototype,"diffuseMap"),So.prototype),P(So.prototype,"reflectionMap",[_o],Object.getOwnPropertyDescriptor(So.prototype,"reflectionMap"),So.prototype),P(So.prototype,"skyboxMaterial",[go],Object.getOwnPropertyDescriptor(So.prototype,"skyboxMaterial"),So.prototype),Mo=U(So.prototype,"_envLightingType",[G],(function(){return Jr.HEMISPHERE_DIFFUSE})),Ao=U(So.prototype,"_envmapHDR",[G,mo,vo],(function(){return null})),wo=U(So.prototype,"_envmapLDR",[G,yo],(function(){return null})),ko=U(So.prototype,"_diffuseMapHDR",[G,bo],(function(){return null})),Co=U(So.prototype,"_diffuseMapLDR",[G,Io],(function(){return null})),xo=U(So.prototype,"_enabled",[G],(function(){return!1})),Lo=U(So.prototype,"_useHDR",[G],(function(){return!0})),Po=U(So.prototype,"_editableMaterial",[G,To],(function(){return null})),Oo=U(So.prototype,"_reflectionHDR",[G,Eo],(function(){return null})),No=U(So.prototype,"_reflectionLDR",[G,Do],(function(){return null})),Ho=U(So.prototype,"_rotationAngle",[G],(function(){return 0})),Ro=So))||Ro));ht.SkyboxInfo=Fu;var Uu=t("Y",(Bo=F("cc.FogInfo"),Fo=V(os),Uo=V(O),Go=V(O),Vo=V(O),zo=V(O),jo=V(O),Wo=V(O),Bo((ra=function(){function t(){this._type=Ko&&Ko(),this._fogColor=Yo&&Yo(),this._enabled=Qo&&Qo(),this._fogDensity=Jo&&Jo(),this._fogStart=Zo&&Zo(),this._fogEnd=$o&&$o(),this._fogAtten=ta&&ta(),this._fogTop=ea&&ea(),this._fogRange=ia&&ia(),this._accurate=na&&na(),this._resource=null}return t.prototype.activate=function(t){this._resource=t,t.initialize(this),t.activate()},r(t,[{key:"enabled",get:function(){return this._enabled},set:function(t){if(this._enabled!==t){this._enabled=t;var e=this._resource;e&&(e.enabled=t,t&&(e.type=this._type))}}},{key:"accurate",get:function(){return this._accurate},set:function(t){if(this._accurate!==t){this._accurate=t;var e=this._resource;e&&(e.accurate=t,t&&(e.type=this._type))}}},{key:"fogColor",get:function(){return this._fogColor},set:function(t){this._fogColor.set(t),this._resource&&(this._resource.fogColor=this._fogColor)}},{key:"type",get:function(){return this._type},set:function(t){this._type=t,this._resource&&(this._resource.type=t)}},{key:"fogDensity",get:function(){return this._fogDensity},set:function(t){this._fogDensity=t,this._resource&&(this._resource.fogDensity=t)}},{key:"fogStart",get:function(){return this._fogStart},set:function(t){this._fogStart=t,this._resource&&(this._resource.fogStart=t)}},{key:"fogEnd",get:function(){return this._fogEnd},set:function(t){this._fogEnd=t,this._resource&&(this._resource.fogEnd=t)}},{key:"fogAtten",get:function(){return this._fogAtten},set:function(t){this._fogAtten=t,this._resource&&(this._resource.fogAtten=t)}},{key:"fogTop",get:function(){return this._fogTop},set:function(t){this._fogTop=t,this._resource&&(this._resource.fogTop=t)}},{key:"fogRange",get:function(){return this._fogRange},set:function(t){this._fogRange=t,this._resource&&(this._resource.fogRange=t)}}]),t}(),ra.FogType=os,P((qo=ra).prototype,"type",[Fo],Object.getOwnPropertyDescriptor(qo.prototype,"type"),qo.prototype),P(qo.prototype,"fogDensity",[Uo],Object.getOwnPropertyDescriptor(qo.prototype,"fogDensity"),qo.prototype),P(qo.prototype,"fogStart",[Go],Object.getOwnPropertyDescriptor(qo.prototype,"fogStart"),qo.prototype),P(qo.prototype,"fogEnd",[Vo],Object.getOwnPropertyDescriptor(qo.prototype,"fogEnd"),qo.prototype),P(qo.prototype,"fogAtten",[zo],Object.getOwnPropertyDescriptor(qo.prototype,"fogAtten"),qo.prototype),P(qo.prototype,"fogTop",[jo],Object.getOwnPropertyDescriptor(qo.prototype,"fogTop"),qo.prototype),P(qo.prototype,"fogRange",[Wo],Object.getOwnPropertyDescriptor(qo.prototype,"fogRange"),qo.prototype),Ko=U(qo.prototype,"_type",[G],(function(){return os.LINEAR})),Yo=U(qo.prototype,"_fogColor",[G],(function(){return new Y("#C8C8C8")})),Qo=U(qo.prototype,"_enabled",[G],(function(){return!1})),Jo=U(qo.prototype,"_fogDensity",[G],(function(){return.3})),Zo=U(qo.prototype,"_fogStart",[G],(function(){return.5})),$o=U(qo.prototype,"_fogEnd",[G],(function(){return 300})),ta=U(qo.prototype,"_fogAtten",[G],(function(){return 5})),ea=U(qo.prototype,"_fogTop",[G],(function(){return 1.5})),ia=U(qo.prototype,"_fogRange",[G],(function(){return 1.2})),na=U(qo.prototype,"_accurate",[G],(function(){return!1})),Xo=qo))||Xo)),Gu=t("Z",(sa=F("cc.ShadowsInfo"),oa=V(Xr),aa=V(O),ua=V(O),ha=V(N),la=V(Wr),sa((ca=function(){function t(){this._enabled=fa&&fa(),this._type=_a&&_a(),this._normal=da&&da(),this._distance=ga&&ga(),this._planeBias=ma&&ma(),this._shadowColor=va&&va(),this._maxReceived=ya&&ya(),this._size=ba&&ba(),this._resource=null}var e=t.prototype;return e.setPlaneFromNode=function(t){t.getWorldRotation(Nu),this.planeDirection=W.transformQuat(Lu,xu,Nu),t.getWorldPosition(Lu),this.planeHeight=W.dot(this._normal,Lu)},e.activate=function(t){this._resource=t,t.initialize(this),t.activate()},r(t,[{key:"enabled",get:function(){return this._enabled},set:function(t){if(this._enabled!==t){this._enabled=t;var e=this._resource;e&&(e.enabled=t,t&&(e.type=this._type))}}},{key:"type",get:function(){return this._type},set:function(t){this._type=t,this._resource&&(this._resource.type=t)}},{key:"shadowColor",get:function(){return this._shadowColor},set:function(t){this._shadowColor.set(t),this._resource&&(this._resource.shadowColor=t)}},{key:"planeDirection",get:function(){return this._normal},set:function(t){W.copy(this._normal,t),this._resource&&(this._resource.normal=t)}},{key:"planeHeight",get:function(){return this._distance},set:function(t){this._distance=t,this._resource&&(this._resource.distance=t)}},{key:"planeBias",get:function(){return this._planeBias},set:function(t){this._planeBias=t,this._resource&&(this._resource.planeBias=t)}},{key:"maxReceived",get:function(){return this._maxReceived},set:function(t){this._maxReceived=t,this._resource&&(this._resource.maxReceived=t)}},{key:"shadowMapSize",get:function(){return this._size.x},set:function(t){var e=this._resource;this._size.set(t,t),e&&(e.size.set(t,t),e.shadowMapDirty=!0)}}]),t}(),P(ca.prototype,"type",[oa],Object.getOwnPropertyDescriptor(ca.prototype,"type"),ca.prototype),P(ca.prototype,"planeHeight",[aa],Object.getOwnPropertyDescriptor(ca.prototype,"planeHeight"),ca.prototype),P(ca.prototype,"planeBias",[ua],Object.getOwnPropertyDescriptor(ca.prototype,"planeBias"),ca.prototype),P(ca.prototype,"maxReceived",[ha],Object.getOwnPropertyDescriptor(ca.prototype,"maxReceived"),ca.prototype),P(ca.prototype,"shadowMapSize",[la],Object.getOwnPropertyDescriptor(ca.prototype,"shadowMapSize"),ca.prototype),fa=U(ca.prototype,"_enabled",[G],(function(){return!1})),_a=U(ca.prototype,"_type",[G],(function(){return Xr.Planar})),da=U(ca.prototype,"_normal",[G],(function(){return new W(0,1,0)})),ga=U(ca.prototype,"_distance",[G],(function(){return 0})),ma=U(ca.prototype,"_planeBias",[G],(function(){return 1})),va=U(ca.prototype,"_shadowColor",[G],(function(){return new Y(0,0,0,76)})),ya=U(ca.prototype,"_maxReceived",[G],(function(){return 4})),ba=U(ca.prototype,"_size",[G],(function(){return new j(1024,1024)})),pa=ca))||pa));ht.ShadowsInfo=Gu;var Vu=t("_",new W(-1024,-1024,-1024)),zu=t("$",new W(1024,1024,1024)),ju=t("a0",8),Wu=t("a1",(Ia=F("cc.OctreeInfo"),Ta=V(N),Ia((Da=function(){function t(){this._enabled=Ra&&Ra(),this._minPos=Sa&&Sa(),this._maxPos=Ma&&Ma(),this._depth=Aa&&Aa(),this._resource=null}return t.prototype.activate=function(t){this._resource=t,t.initialize(this)},r(t,[{key:"enabled",get:function(){return this._enabled},set:function(t){this._enabled!==t&&(this._enabled=t,this._resource&&(this._resource.enabled=t))}},{key:"minPos",get:function(){return this._minPos},set:function(t){this._minPos=t,this._resource&&(this._resource.minPos=t)}},{key:"maxPos",get:function(){return this._maxPos},set:function(t){this._maxPos=t,this._resource&&(this._resource.maxPos=t)}},{key:"depth",get:function(){return this._depth},set:function(t){this._depth=t,this._resource&&(this._resource.depth=t)}}]),t}(),P(Da.prototype,"depth",[Ta],Object.getOwnPropertyDescriptor(Da.prototype,"depth"),Da.prototype),Ra=U(Da.prototype,"_enabled",[G],(function(){return!1})),Sa=U(Da.prototype,"_minPos",[G],(function(){return new W(Vu)})),Ma=U(Da.prototype,"_maxPos",[G],(function(){return new W(zu)})),Aa=U(Da.prototype,"_depth",[G],(function(){return ju})),Ea=Da))||Ea));ht.OctreeInfo=Wu;var Xu=t("a2",(wa=F("cc.SkinInfo"),ka=V(O),Ca=V(O),wa((La=function(){function t(){this._enabled=Pa&&Pa(),this._blurRadius=Oa&&Oa(),this._sssIntensity=Na&&Na(),this._resource=null}return t.prototype.activate=function(t){this._resource=t,t.initialize(this)},r(t,[{key:"enabled",get:function(){return this._enabled},set:function(t){this._enabled!==t&&(this._enabled=t,this._resource&&(this._resource.enabled=t))}},{key:"blurRadius",get:function(){return this._blurRadius},set:function(t){this._blurRadius=t,this._resource&&(this._resource.blurRadius=t)}},{key:"sssIntensity",get:function(){return this._sssIntensity},set:function(t){this._sssIntensity=t,this._resource&&(this._resource.sssIntensity=t)}}]),t}(),P(La.prototype,"blurRadius",[ka],Object.getOwnPropertyDescriptor(La.prototype,"blurRadius"),La.prototype),P(La.prototype,"sssIntensity",[Ca],Object.getOwnPropertyDescriptor(La.prototype,"sssIntensity"),La.prototype),Pa=U(La.prototype,"_enabled",[G],(function(){return!0})),Oa=U(La.prototype,"_blurRadius",[G],(function(){return.01})),Na=U(La.prototype,"_sssIntensity",[G],(function(){return 3})),xa=La))||xa));ht.SkinInfo=Xu;var qu=t("a3",(Ha=F("cc.PostSettingsInfo"),Ba=V(ls),Ha((Ua=function(){function t(){this._toneMappingType=Ga&&Ga(),this._resource=null}return t.prototype.activate=function(t){this._resource=t,t.initialize(this),t.activate()},r(t,[{key:"toneMappingType",get:function(){return this._toneMappingType},set:function(t){this._toneMappingType=t,this._resource&&(this._resource.toneMappingType=t)}}]),t}(),P(Ua.prototype,"toneMappingType",[Ba],Object.getOwnPropertyDescriptor(Ua.prototype,"toneMappingType"),Ua.prototype),Ga=U(Ua.prototype,"_toneMappingType",[G],(function(){return ls.DEFAULT})),Fa=Ua))||Fa));ht.PostSettingsInfo=qu;var Ku,Yu,Qu,Ju,Zu,$u,th,eh,ih,nh,rh,sh,oh,ah,uh,hh,lh,ph,ch,fh,_h,dh,gh,mh,vh,yh,bh,Ih,Th,Eh,Dh,Rh,Sh,Mh,Ah,wh,kh,Ch,xh,Lh,Ph,Oh,Nh,Hh,Bh,Fh,Uh,Gh,Vh,zh,jh,Wh,Xh,qh,Kh,Yh,Qh,Jh,Zh,$h,tl,el,il,nl,rl,sl,ol,al,ul,hl=t("a4",(Va=F("cc.LightProbeInfo"),za=V(O),ja=V(N),Wa=V(N),Xa=V(O),qa=V(O),Va((Ya=function(){function t(){this._giScale=Qa&&Qa(),this._giSamples=Ja&&Ja(),this._bounces=Za&&Za(),this._reduceRinging=$a&&$a(),this._showProbe=tu&&tu(),this._showWireframe=eu&&eu(),this._showConvex=iu&&iu(),this._data=nu&&nu(),this._lightProbeSphereVolume=ru&&ru(),this._nodes=[],this._scene=null,this._resource=null}var e=t.prototype;return e.activate=function(t,e){this._scene=t,this._resource=e,e.initialize(this)},e.onProbeBakeFinished=function(){this.onProbeBakingChanged(this._scene)},e.onProbeBakeCleared=function(){this.clearSHCoefficients(),this.onProbeBakingChanged(this._scene)},e.onProbeBakingChanged=function(t){var e=this;t&&(t.emit($t.LIGHT_PROBE_BAKING_CHANGED),t.children.forEach((function(t){e.onProbeBakingChanged(t)})))},e.clearSHCoefficients=function(){this._data&&(this._data.probes.forEach((function(t){t.coefficients.length=0})),this.clearAllSHUBOs())},e.isUniqueNode=function(){return 1===this._nodes.length},e.addNode=function(t){if(!t)return!1;for(var e=0;e<this._nodes.length;e++)if(this._nodes[e].node===t)return!1;return this._nodes.push({node:t,probes:null}),!0},e.removeNode=function(t){if(!t)return!1;var e=this._nodes.findIndex((function(e){return e.node===t}));return-1!==e&&(this._nodes.splice(e,1),!0)},e.syncData=function(t,e){for(var i=0;i<this._nodes.length;i++)if(this._nodes[i].node===t)return void(this._nodes[i].probes=e)},e.update=function(t){if(void 0===t&&(t=!0),ut.internal.LightProbesData){this._data||(this._data=new ut.internal.LightProbesData,this._resource&&(this._resource.data=this._data));for(var e=[],i=0;i<this._nodes.length;i++){var n=this._nodes[i],r=n.node,s=n.probes,o=r.worldPosition;if(s)for(var a=0;a<s.length;a++){var u=tt();W.add(u,s[a],o),e.push(u)}}if(e.length<4)return this.resetAllTetraIndices(),void this._data.reset();this._data.updateProbes(e),t&&(this.resetAllTetraIndices(),this._data.updateTetrahedrons())}},e.clearAllSHUBOs=function(){if(this._scene){var t=this._scene.renderScene;t&&t.models.forEach((function(t){t.clearSHUBOs()}))}},e.resetAllTetraIndices=function(){if(this._scene){var t=this._scene.renderScene;t&&t.models.forEach((function(t){t.tetrahedronIndex=-1}))}},r(t,[{key:"giScale",get:function(){return this._giScale},set:function(t){this._giScale!==t&&(this._giScale=t,this._resource&&(this._resource.giScale=t))}},{key:"giSamples",get:function(){return this._giSamples},set:function(t){this._giSamples!==t&&(this._giSamples=t,this._resource&&(this._resource.giSamples=t))}},{key:"bounces",get:function(){return this._bounces},set:function(t){this._bounces!==t&&(this._bounces=t,this._resource&&(this._resource.bounces=t))}},{key:"reduceRinging",get:function(){return this._reduceRinging},set:function(t){this._reduceRinging!==t&&(this._reduceRinging=t,this._resource&&(this._resource.reduceRinging=t))}},{key:"showProbe",get:function(){return this._showProbe},set:function(t){this._showProbe!==t&&(this._showProbe=t,this._resource&&(this._resource.showProbe=t))}},{key:"showWireframe",get:function(){return this._showWireframe},set:function(t){this._showWireframe!==t&&(this._showWireframe=t,this._resource&&(this._resource.showWireframe=t))}},{key:"showConvex",get:function(){return this._showConvex},set:function(t){this._showConvex!==t&&(this._showConvex=t,this._resource&&(this._resource.showConvex=t))}},{key:"data",get:function(){return this._data},set:function(t){this._data!==t&&(this._data=t,this._resource&&(this._resource.data=t))}},{key:"lightProbeSphereVolume",get:function(){return this._lightProbeSphereVolume},set:function(t){this._lightProbeSphereVolume!==t&&(this._lightProbeSphereVolume=t,this._resource&&(this._resource.lightProbeSphereVolume=t))}}]),t}(),P(Ya.prototype,"giScale",[za],Object.getOwnPropertyDescriptor(Ya.prototype,"giScale"),Ya.prototype),P(Ya.prototype,"giSamples",[ja],Object.getOwnPropertyDescriptor(Ya.prototype,"giSamples"),Ya.prototype),P(Ya.prototype,"bounces",[Wa],Object.getOwnPropertyDescriptor(Ya.prototype,"bounces"),Ya.prototype),P(Ya.prototype,"reduceRinging",[Xa],Object.getOwnPropertyDescriptor(Ya.prototype,"reduceRinging"),Ya.prototype),P(Ya.prototype,"lightProbeSphereVolume",[qa],Object.getOwnPropertyDescriptor(Ya.prototype,"lightProbeSphereVolume"),Ya.prototype),Qa=U(Ya.prototype,"_giScale",[G],(function(){return 1})),Ja=U(Ya.prototype,"_giSamples",[G],(function(){return 1024})),Za=U(Ya.prototype,"_bounces",[G],(function(){return 2})),$a=U(Ya.prototype,"_reduceRinging",[G],(function(){return 0})),tu=U(Ya.prototype,"_showProbe",[G],(function(){return!0})),eu=U(Ya.prototype,"_showWireframe",[G],(function(){return!0})),iu=U(Ya.prototype,"_showConvex",[G],(function(){return!1})),nu=U(Ya.prototype,"_data",[G],(function(){return null})),ru=U(Ya.prototype,"_lightProbeSphereVolume",[G],(function(){return 1})),Ka=Ya))||Ka)),ll=t("a5",(su=F("cc.SceneGlobals"),ou=V(Fu),su((uu=function(){function t(){this.ambient=hu&&hu(),this.shadows=lu&&lu(),this._skybox=pu&&pu(),this.fog=cu&&cu(),this.octree=fu&&fu(),this.skin=_u&&_u(),this.lightProbeInfo=du&&du(),this.postSettings=gu&&gu(),this.bakedWithStationaryMainLight=mu&&mu(),this.bakedWithHighpLightmap=vu&&vu(),this.disableLightmap=!1}return t.prototype.activate=function(t){var e=ht.director.root.pipeline.pipelineSceneData;this.skybox.activate(e.skybox),this.ambient.activate(e.ambient),this.shadows.activate(e.shadows),this.fog.activate(e.fog),this.octree.activate(e.octree),this.skin.activate(e.skin),this.postSettings.activate(e.postSettings),this.lightProbeInfo&&e.lightProbes&&this.lightProbeInfo.activate(t,e.lightProbes),ht.director.root.onGlobalPipelineStateChanged()},r(t,[{key:"skybox",get:function(){return this._skybox},set:function(t){this._skybox=t}}]),t}(),hu=U(uu.prototype,"ambient",[G],(function(){return new Bu})),lu=U(uu.prototype,"shadows",[G],(function(){return new Gu})),pu=U(uu.prototype,"_skybox",[G],(function(){return new Fu})),cu=U(uu.prototype,"fog",[G],(function(){return new Uu})),P(uu.prototype,"skybox",[ou],Object.getOwnPropertyDescriptor(uu.prototype,"skybox"),uu.prototype),fu=U(uu.prototype,"octree",[G],(function(){return new Wu})),_u=U(uu.prototype,"skin",[G],(function(){return new Xu})),du=U(uu.prototype,"lightProbeInfo",[G],(function(){return new hl})),gu=U(uu.prototype,"postSettings",[G],(function(){return new qu})),mu=U(uu.prototype,"bakedWithStationaryMainLight",[G],(function(){return!1})),vu=U(uu.prototype,"bakedWithHighpLightmap",[G],(function(){return!1})),au=uu))||au));ht.SceneGlobals=ll;var pl,cl,fl,_l,dl=(Ku=F("cc.TargetInfo"),Yu=V([H]),Ku((Ju=function(){this.localID=Zu&&Zu()},Zu=U(Ju.prototype,"localID",[G,Yu],(function(){return[]})),Qu=Ju))||Qu),gl=($u=F("cc.TargetOverrideInfo"),th=V(L),eh=V(dl),ih=V([H]),nh=V(Cu),rh=V(dl),$u((oh=function(){this.source=ah&&ah(),this.sourceInfo=uh&&uh(),this.propertyPath=hh&&hh(),this.target=lh&&lh(),this.targetInfo=ph&&ph()},ah=U(oh.prototype,"source",[G,th],(function(){return null})),uh=U(oh.prototype,"sourceInfo",[G,eh],(function(){return null})),hh=U(oh.prototype,"propertyPath",[G,ih],(function(){return[]})),lh=U(oh.prototype,"target",[G,nh],(function(){return null})),ph=U(oh.prototype,"targetInfo",[G,rh],(function(){return null})),sh=oh))||sh),ml=F("cc.CompPrefabInfo")((fh=function(){this.fileId=_h&&_h()},_h=U(fh.prototype,"fileId",[G],(function(){return""})),ch=fh))||ch,vl=(dh=F("CCPropertyOverrideInfo"),gh=V(dl),mh=V([H]),dh((yh=function(){function t(){this.targetInfo=bh&&bh(),this.propertyPath=Ih&&Ih(),this.value=Th&&Th()}return t.prototype.isTarget=function(){},t}(),bh=U(yh.prototype,"targetInfo",[G,gh],(function(){return null})),Ih=U(yh.prototype,"propertyPath",[G,mh],(function(){return[]})),Th=U(yh.prototype,"value",[G],null),vh=yh))||vh),yl=(Eh=F("cc.MountedChildrenInfo"),Dh=V(dl),Rh=V([Cu]),Eh((Mh=function(){function t(){this.targetInfo=Ah&&Ah(),this.nodes=wh&&wh()}return t.prototype.isTarget=function(){},t}(),Ah=U(Mh.prototype,"targetInfo",[G,Dh],(function(){return null})),wh=U(Mh.prototype,"nodes",[G,Rh],(function(){return[]})),Sh=Mh))||Sh),bl=(kh=F("cc.MountedComponentsInfo"),Ch=V(dl),xh=V([Zt]),kh((Ph=function(){function t(){this.targetInfo=Oh&&Oh(),this.components=Nh&&Nh()}return t.prototype.isTarget=function(){},t}(),Oh=U(Ph.prototype,"targetInfo",[G,Ch],(function(){return null})),Nh=U(Ph.prototype,"components",[G,xh],(function(){return[]})),Lh=Ph))||Lh),Il=(Hh=F("cc.PrefabInstance"),Bh=V(Cu),Fh=V([yl]),Uh=V([bl]),Gh=V([vl]),Vh=V([dl]),Hh((jh=function(){function t(){this.fileId=Wh&&Wh(),this.prefabRootNode=Xh&&Xh(),this.mountedChildren=qh&&qh(),this.mountedComponents=Kh&&Kh(),this.propertyOverrides=Yh&&Yh(),this.removedComponents=Qh&&Qh(),this.targetMap={},this.expanded=!1}var e=t.prototype;return e.findPropertyOverride=function(){},e.removePropertyOverride=function(){},t}(),Wh=U(jh.prototype,"fileId",[G],(function(){return""})),Xh=U(jh.prototype,"prefabRootNode",[G,Bh],null),qh=U(jh.prototype,"mountedChildren",[G,Fh],(function(){return[]})),Kh=U(jh.prototype,"mountedComponents",[G,Uh],(function(){return[]})),Yh=U(jh.prototype,"propertyOverrides",[G,Gh],(function(){return[]})),Qh=U(jh.prototype,"removedComponents",[G,Vh],(function(){return[]})),zh=jh))||zh),Tl=(Jh=F("cc.PrefabInfo"),Zh=V(Cu),$h=V(Il),tl=V([gl]),Jh((il=function(){this.root=nl&&nl(),this.asset=rl&&rl(),this.fileId=sl&&sl(),this.instance=ol&&ol(),this.targetOverrides=al&&al(),this.nestedPrefabInstanceRoots=ul&&ul()},nl=U(il.prototype,"root",[G,Zh],null),rl=U(il.prototype,"asset",[G],null),sl=U(il.prototype,"fileId",[G],(function(){return""})),ol=U(il.prototype,"instance",[G,$h],null),al=U(il.prototype,"targetOverrides",[G,tl],null),ul=U(il.prototype,"nestedPrefabInstanceRoots",[G],null),el=il))||el);function El(t){var e=null==t?void 0:t.prefab;if(e&&e.instance){if(!e.asset)return n(3701,t.name),void(e.instance=void 0);var i=t._objFlags,r=t.getParent(),s=t.uuid;t[at],ut.game._isCloning=!0;var o=e.asset.data;o._iN$t=t,ut.instantiate._clone(o,o),ut.game._isCloning=!1,t._objFlags=i,t.modifyParent(r),t.id=s,t.prefab&&(t.prefab.instance=e.instance)}}function Dl(t,e,i){var n;if(e&&t){var r=e,s=null==(n=t.prefab)?void 0:n.instance;!i&&s&&(e[s.fileId]={},r=e[s.fileId]);var o=t.prefab;o&&(r[o.fileId]=t),t.components.forEach((function(t){t.__prefab&&(r[t.__prefab.fileId]=t)})),t.children.forEach((function(t){Dl(t,r,!1)}))}}function Rl(t,e){if(!t)return null;for(var i=e,n=0;n<t.length;n++){if(!i)return null;i=i[t[n]]}return i}function Sl(t,e,i){if(e)for(var n=0;n<e.length;n++){var r=e[n];if(r&&r.targetInfo){var s=Rl(r.targetInfo.localID,i);if(!s)continue;var o=i,a=r.targetInfo.localID;if(a.length>0)for(var u=0;u<a.length-1;u++)o=o[a[u]];if(r.nodes)for(var h=0;h<r.nodes.length;h++){var l=r.nodes[h];l&&!s.children.includes(l)&&(s.children.push(l),l.modifyParent(s),Dl(l,o,!1),l.siblingIndex=s.children.length-1,Cl(l,!0))}}}}function Ml(t,e,i){if(e)for(var n=0;n<e.length;n++){var r=e[n];if(r&&r.targetInfo){var s=Rl(r.targetInfo.localID,i);if(!s)continue;if(r.components)for(var o=0;o<r.components.length;o++){var a=r.components[o];a&&(a.node=s,s.getWritableComponents().push(a))}}}}function Al(t,e,i){if(e)for(var n=0;n<e.length;n++){var r=e[n];if(r){var s=Rl(r.localID,i);if(!s||!s.node)continue;var o=s.node.components.indexOf(s);o>=0&&s.node.getWritableComponents().splice(o,1)}}}function wl(t,e,i){if(!(e.length<=0))for(var n=null,r=0;r<e.length;r++){var s=e[r];if(s&&s.targetInfo){if(!(n=Rl(s.targetInfo.localID,i)))continue;var o=n,a=s.propertyPath.slice();if(a.length>0){var u=a.pop();if(!u)continue;for(var h=0;h<a.length&&(o=o[a[h]]);h++);if(!o)continue;if(Array.isArray(o))if("length"===u)o[u]=s.value;else{var l=Number.parseInt(u);Number.isInteger(l)&&l<o.length&&(o[u]=s.value)}else o[u]instanceof B?o[u].set(s.value):o[u]=s.value}}}}function kl(t){var e,i=null==(e=t.prefab)?void 0:e.targetOverrides;if(i)for(var n=0;n<i.length;n++){var r,s=i[n],o=s.source,a=s.sourceInfo;if(a){var u,h=s.source,l=null==h||null==(u=h.prefab)?void 0:u.instance;l&&l.targetMap&&(o=Rl(a.localID,l.targetMap))}if(o){var p,c=s.targetInfo;if(c){var f=s.target,_=null==f||null==(r=f.prefab)?void 0:r.instance;if(_&&_.targetMap&&(p=Rl(c.localID,_.targetMap))){var d=s.propertyPath.slice(),g=o;if(d.length>0){var m=d.pop();if(!m)return;for(var v=0;v<d.length&&(g=g[d[v]]);v++);if(!g)continue;g[m]=p}}}}}}function Cl(t,e){var i;void 0===e&&(e=!1);var n=null==t||null==(i=t.prefab)?void 0:i.instance;if(n&&!n.expanded){El(t),e&&t&&t.children&&t.children.forEach((function(t){Cl(t,!0)}));var r={};n.targetMap=r,Dl(t,r,!0),Sl(0,n.mountedChildren,r),Al(0,n.removedComponents,r),Ml(0,n.mountedComponents,r),wl(0,n.propertyOverrides,r),n.expanded=!0}else e&&t&&t.children&&t.children.forEach((function(t){Cl(t,!0)}))}function xl(t){var e=t.prefab;e&&e.nestedPrefabInstanceRoots&&e.nestedPrefabInstanceRoots.forEach((function(t){Cl(t)}))}ut._PrefabInfo=Tl,t("ad",Object.freeze({__proto__:null,CompPrefabInfo:ml,MountedChildrenInfo:yl,MountedComponentsInfo:bl,PrefabInfo:Tl,PrefabInstance:Il,PropertyOverrideInfo:vl,TargetInfo:dl,TargetOverrideInfo:gl,applyMountedChildren:Sl,applyMountedComponents:Ml,applyNodeAndComponentId:function t(e,i){for(var n=e.components,r=e.children,s=0;s<n.length;s++){var o,a,u=n[s],h=null!==(o=null==(a=u.__prefab)?void 0:a.fileId)&&void 0!==o?o:"";u._id=""+i+h}for(var l=0;l<r.length;l++){var p=r[l],c=p.prefab,f=null!=c&&c.instance?c.instance.fileId:null==c?void 0:c.fileId;f&&(p.id=""+i+f,null!=c&&c.instance||t(p,i))}},applyPropertyOverrides:wl,applyRemovedComponents:Al,applyTargetOverrides:kl,createNodeWithPrefab:El,expandNestedPrefabInstanceNode:xl,expandPrefabInstanceNode:Cl,generateTargetMap:Dl,getTarget:Rl}));var Ll=t("m",F("cc.Scene")((cl=function(t){i(n,t);var e=n.prototype;function n(e){var i;return(i=t.call(this,e)||this).autoReleaseAssets=fl&&fl(),i._globals=_l&&_l(),i.dependAssets=null,i._renderScene=null,i._prefabSyncedInLiveReload=!1,i._activeInHierarchy=!1,ht.director&&ht.director.root&&(i._renderScene=ht.director.root.createScene({})),i._inited=!ht.game||!ht.game._isCloning,i}return e._updateScene=function(){this._scene=this},e.destroy=function(){var t=L.prototype.destroy.call(this);if(t)for(var e=this._children,i=0;i<e.length;++i)e[i].active=!1;return this._renderScene&&ht.director.root.destroyScene(this._renderScene),this._active=!1,this._activeInHierarchy=!1,t},e.addComponent=function(){throw new Error(m(3822))},e._onHierarchyChanged=function(){},e._onPostActivated=function(){},e._onBatchCreated=function(t){for(var e=this._children.length,i=0;i<e;++i)this._children[i]._siblingIndex=i,this._children[i]._onBatchCreated(t)},e.updateWorldTransform=function(){},e._instantiate=function(){return null},e._load=function(){this._inited||(xl(this),kl(this),this._onBatchCreated(_t),this._inited=!0),this.walk(Cu._setScene)},e._activate=function(t){void 0===t&&(t=!0),ht.director._nodeActivator.activateNode(this,t),this._globals.activate(this)},r(n,[{key:"renderScene",get:function(){return this._renderScene}},{key:"globals",get:function(){return this._globals}}]),n}(Cu),fl=U(cl.prototype,"autoReleaseAssets",[G],(function(){return!1})),_l=U(cl.prototype,"_globals",[G],(function(){return new ll})),pl=cl))||pl);ht.Scene=Ll}}}));
