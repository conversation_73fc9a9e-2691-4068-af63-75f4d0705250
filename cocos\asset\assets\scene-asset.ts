/*
 Copyright (c) 2013-2016 Chukong Technologies Inc.
 Copyright (c) 2017-2023 Xiamen Yaji Software Co., Ltd.

 http://www.cocos.com

 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and associated documentation files (the "Software"), to deal
 in the Software without restriction, including without limitation the rights to
 use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies
 of the Software, and to permit persons to whom the Software is furnished to do so,
 subject to the following conditions:

 The above copyright notice and this permission notice shall be included in
 all copies or substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 THE SOFTWARE.
*/

import { ccclass, editable, serializable } from 'cc.decorator';
import { Scene } from '../../scene-graph/scene';
import { Asset } from './asset';
import { cclegacy } from '../../core';

/**
 * @en Class for scene loading.
 * @zh 场景资源类。
 *
 */
@ccclass('cc.SceneAsset')
export class SceneAsset extends Asset {
    /**
     * @en The scene node
     * @zh 场景节点。
     */
    @editable
    @serializable
    public scene: Scene | null = null;

    constructor (name?: string) {
        super(name);
    }

    public initDefault (uuid?: string): void {
        super.initDefault(uuid);
        this.scene = new Scene('New Scene');
    }

    public validate (): boolean {
        return !!this.scene;
    }
}

cclegacy.SceneAsset = SceneAsset;
