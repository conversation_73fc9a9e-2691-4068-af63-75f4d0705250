// Copyright (c) 2017-2020 Xiamen Yaji Software Co., Ltd.

// have to specify precisions explicitly
// if shared among stages with different precisions
#pragma builtin(global)
layout(set = 0, binding = 0) uniform CCGlobal {
  highp   vec4 cc_time; // x: global time since started in seconds, y: delta time for current frame, z: total frames since started, w: fractinal part of x, better precious, should be used with uv animation
  mediump vec4 cc_screenSize; // xy: screen size, zw: inverse screen size
  mediump vec4 cc_nativeSize; // xy: shading size, zw: inverse shading size
  mediump vec4 cc_probeInfo; // x: reflection probe count

  mediump vec4 cc_debug_view_mode; // x: single mode, yzw: composite and misc flags combination value
};

#pragma builtin(global)
layout(set = 0, binding = 1) uniform CCCamera {
  highp   mat4 cc_matView; // view matrix
  highp   mat4 cc_matViewInv; // inverse view matrix
  highp   mat4 cc_matProj; // projection matrix
  highp   mat4 cc_matProjInv; // inverse projection matrix
  highp   mat4 cc_matViewProj; // view-projection matrix
  highp   mat4 cc_matViewProjInv; // inverse view-projection matrix
  highp   vec4 cc_cameraPos; // xyz: camera position w: flip NDC sign
  mediump vec4 cc_surfaceTransform; // x: surface transform y: camera usage zw: skybox rotation angle cos/sin value
  mediump vec4 cc_screenScale; // xy: screen scale, zw: inverse screen scale
  mediump vec4 cc_exposure; // x: exposure, y: inverse exposure, z: HDR flag, w: inverse standard exposure value
  mediump vec4 cc_mainLitDir; // xyz: main direcitonal light direction, w: enable shadow
  mediump vec4 cc_mainLitColor; // xyz: main direcitonal light color, w: intensity
  mediump vec4 cc_ambientSky; //xyz: sky illumination color, w: intensity
  mediump vec4 cc_ambientGround; // xyz: ground albedo color, w: envmap LOD
  mediump vec4 cc_fogColor; // xyz: global fog color
  mediump vec4 cc_fogBase; // xyz: fogStart, fogEnd, fogDensity
  mediump vec4 cc_fogAdd; // xyz: fogTop, fogRange, fogAtten
  mediump vec4 cc_nearFar; // xy: frustum near, frustum far z: clipspace minz (vulkan: 0 metal: 0 other: -1)
  mediump vec4 cc_viewPort; // xyzw: camera view port, default(0, 0, 1, 1)
};
