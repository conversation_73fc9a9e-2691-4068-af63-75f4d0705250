System.register(["./device-manager-B8lyUDPi.js","./buffer-barrier-q_79u36H.js","./debug-view-BP17WHcy.js","./gc-object-D18ulfCO.js","./mesh-Ba1cTOGw.js","./index-Y4La_nfG.js"],(function(t){"use strict";var e,n,s,r,a,i,u,o,f,c,l,h,d;return{setters:[null,function(t){e=t.a,n=t.c,s=t.F,r=t.A,a=t.P},function(t){i=t.r,u=t.w},function(t){o=t.o},function(t){f=t.M,c=t.d,l=t.i,h=t.B},function(t){d=t.b}],execute:function(){var m;t({_:b,r:function(t,e){void 0===e&&(e=0);var r={positions:[]},a=new DataView(t.data.buffer,t.data.byteOffset,t.data.byteLength),u=t.struct,o=u.primitives[e];o.vertexBundelIndices.forEach((function(t){var e=u.vertexBundles[t],s=e.view.offset,o=e.view,f=o.length,c=o.stride;e.attributes.forEach((function(t){var e=m[t.name];e&&(r[e]=(r[e]||[]).concat(i(a,t.format,s,f,c))),s+=n[t.format].size}))}));var f=o.indexView;return r.indices=i(a,s["R"+8*f.stride+"UI"],f.offset,f.length),r}}),function(t){t[t.positions=e.ATTR_POSITION]="positions",t[t.normals=e.ATTR_NORMAL]="normals",t[t.uvs=e.ATTR_TEX_COORD]="uvs",t[t.colors=e.ATTR_COLOR]="colors"}(m||(m={}));var v=[new r(e.ATTR_POSITION,s.RGB32F),new r(e.ATTR_NORMAL,s.RGB32F),new r(e.ATTR_TEX_COORD,s.RG32F),new r(e.ATTR_TANGENT,s.RGBA32F),new r(e.ATTR_COLOR,s.RGBA32F)],T=new d;function b(t,r,i){i=i||{};var o,c=[],l=0,m=[],b=0,g=t.positions.slice();if(g.length>0){o=null,t.attributes&&(o=t.attributes.find((function(t){return t.name===e.ATTR_POSITION}))||null),o||(o=v[0]),c.push(o);var A=n[o.format];b=Math.max(b,Math.floor(g.length/A.count)),m.push({offset:l,data:g,attribute:o}),l+=A.size}if(t.normals&&t.normals.length>0){o=null,t.attributes&&(o=t.attributes.find((function(t){return t.name===e.ATTR_NORMAL}))||null),o||(o=v[1]);var R=n[o.format];c.push(o),b=Math.max(b,Math.floor(t.normals.length/R.count)),m.push({offset:l,data:t.normals,attribute:o}),l+=R.size}if(t.uvs&&t.uvs.length>0){o=null,t.attributes&&(o=t.attributes.find((function(t){return t.name===e.ATTR_TEX_COORD}))||null),o||(o=v[2]);var M=n[o.format];c.push(o),b=Math.max(b,Math.floor(t.uvs.length/M.count)),m.push({offset:l,data:t.uvs,attribute:o}),l+=M.size}if(t.tangents&&t.tangents.length>0){o=null,t.attributes&&(o=t.attributes.find((function(t){return t.name===e.ATTR_TANGENT}))||null),o||(o=v[3]);var w=n[o.format];c.push(o),b=Math.max(b,Math.floor(t.tangents.length/w.count)),m.push({offset:l,data:t.tangents,attribute:o}),l+=w.size}if(t.colors&&t.colors.length>0){o=null,t.attributes&&(o=t.attributes.find((function(t){return t.name===e.ATTR_COLOR}))||null),o||(o=v[4]);var x=n[o.format];c.push(o),b=Math.max(b,Math.floor(t.colors.length/x.count)),m.push({offset:l,data:t.colors,attribute:o}),l+=x.size}if(t.customAttributes)for(var p=0;p<t.customAttributes.length;p++){var O=t.customAttributes[p],_=n[O.attr.format];c.push(O.attr),b=Math.max(b,Math.floor(O.values.length/_.count)),m.push({offset:l,data:O.values,attribute:O.attr}),l+=_.size}var B=new h,I=new ArrayBuffer(b*l),S=new DataView(I);m.forEach((function(t){u(S,t.data,t.attribute.format,t.offset,l)})),B.setNextAlignment(0);var L={attributes:c,view:{offset:B.getLength(),length:I.byteLength,count:b,stride:l}};B.addBuffer(I);var N=null,G=0;if(t.indices){var y=t.indices;G=y.length,N=new ArrayBuffer(2*G);var z=new DataView(N);u(z,y,s.R16UI)}var E={primitiveMode:t.primitiveMode||a.TRIANGLE_LIST,vertexBundelIndices:[0]};N&&(B.setNextAlignment(2),E.indexView={offset:B.getLength(),length:N.byteLength,count:G,stride:2},B.addBuffer(N));var F=t.minPos;if(!F&&i.calculateBounds){F=d.set(new d,1/0,1/0,1/0);for(var V=0;V<b;++V)d.set(T,g[3*V+0],g[3*V+1],g[3*V+2]),d.min(F,F,T)}var C=t.maxPos;if(!C&&i.calculateBounds){C=d.set(new d,-1/0,-1/0,-1/0);for(var P=0;P<b;++P)d.set(T,g[3*P+0],g[3*P+1],g[3*P+2]),d.max(C,C,T)}var D={vertexBundles:[L],primitives:[E]};return F&&(D.minPosition=new d(F.x,F.y,F.z)),C&&(D.maxPosition=new d(C.x,C.y,C.z)),r||(r=new f),r.reset({struct:D,data:new Uint8Array(B.getCombined())}),r}function g(t,e){if(e>0){var n=t%e;if(0!==n)return e-n}return 0}function A(t,i,u,c){c=c||{maxSubMeshes:1,maxSubMeshVertices:1024,maxSubMeshIndices:1024};var l=[],h=0;if(i.positions.length>0&&l.push(new r(e.ATTR_POSITION,s.RGB32F,!1,h++,!1,0)),i.normals&&i.normals.length>0&&l.push(new r(e.ATTR_NORMAL,s.RGB32F,!1,h++,!1,0)),i.uvs&&i.uvs.length>0&&l.push(new r(e.ATTR_TEX_COORD,s.RG32F,!1,h++,!1,0)),i.tangents&&i.tangents.length>0&&l.push(new r(e.ATTR_TANGENT,s.RGBA32F,!1,h++,!1,0)),i.colors&&i.colors.length>0&&l.push(new r(e.ATTR_COLOR,s.RGBA32F,!1,h++,!1,0)),i.customAttributes)for(var d=0;d<i.customAttributes.length;d++){var m=i.customAttributes[d],v=new r;v.copy(m.attr),v.stream=h++,l.push(v)}for(var T=[],b=[],A=0,R=0;R<c.maxSubMeshes;R++){for(var M,w={vertexBundelIndices:[],primitiveMode:i.primitiveMode||a.TRIANGLE_LIST},x=o(l);!(M=x()).done;){var p=M.value,O=n[p.format],_=c.maxSubMeshVertices*O.size,B={view:{offset:A,length:_,count:0,stride:O.size},attributes:[p]},I=T.length;w.vertexBundelIndices.push(I),T.push(B),A+=_}var S=0;if(i.indices16&&i.indices16.length>0?S=2:i.indices32&&i.indices32.length>0&&(S=4),S>0){A+=g(A,S);var L=c.maxSubMeshIndices*S,N={offset:A,length:L,count:0,stride:S};w.indexView=N,A+=L}b.push(w)}var G={info:{maxSubMeshes:c.maxSubMeshes,maxSubMeshVertices:c.maxSubMeshVertices,maxSubMeshIndices:c.maxSubMeshIndices},bounds:[]};G.bounds.length=c.maxSubMeshes;var y={struct:{vertexBundles:T,primitives:b,dynamic:G},data:new Uint8Array(A)};return u||(u=new f),u.reset(y),u.initialize(),u.updateSubMesh(t,i),u}t("M",function(){function t(){}return t.createMesh=function(t,e,n){return b(t,e,n)},t.createDynamicMesh=function(t,e,n,s){return A(t,e,n,s)},t.decodeMesh=function(t){return c(t)},t.inflateMesh=function(t){return l(t)},t}())}}}));
