System.register(["./deprecated-sXXwkzPk.js","./index-C5lmLqDW.js","./global-exports-CR3GRnjt.js","./gc-object-CKHc4SnS.js","./animation-component-lOZ0BNSE.js","./scene-7MDSMR3j.js","./skeleton-d3ONjcrt.js","./component-BaGvu7EF.js","./buffer-barrier-CuX_5NUR.js","./mesh-renderer-5-GqFOQS.js","./mesh-C8knhDLk.js","./rendering-sub-mesh-CowWLfXC.js","./debug-view-CKetkq9d.js","./device-manager-BvjvoelW.js","./pipeline-state-manager-Cdpe3is6.js","./factory-D9_8ZCqM.js","./wasm-minigame-DoCiKH-Y.js","./deprecated-T2yM9nsJ.js","./director-DIlqD2Nd.js","./prefab-DH0xadMc.js","./touch-DB0AR-Sc.js","./node-event-DTNosVQv.js","./deprecated-Ca3AjUwj.js","./zlib.min-CyXMsivM.js","./deprecated-D4QUWou_.js","./model-renderer-BcRDUYby.js","./renderer-9hfAnqUF.js"],(function(t){"use strict";var e,n,i,s,o,a,r,l,u,c,h,d,f,p,k,m,_,v,y,g,S,A,B,j;return{setters:[function(a){e=a.J,n=a.a,i=a.S,s=a.b,o=a.g,t("SkelAnimDataHub",a.S)},function(t){a=t.M,r=t.b,l=t.Q,u=t.c,c=t.t,h=t.a,d=t.s,f=t.i},function(t){p=t.c},function(t){k=t._,m=t.k,_=t.G,v=t.l,y=t.a,g=t.b},function(t){S=t.A,A=t.g,B=t.a},function(t){j=t.N},null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){var C=function(){function t(t){this.jointTexturePool=void 0,this.jointAnimationInfo=void 0,this.jointTexturePool=new e(t),this.jointAnimationInfo=new n(t)}var i=t.prototype;return i.releaseSkeleton=function(t){this.jointTexturePool.releaseSkeleton(t)},i.releaseAnimationClip=function(t){this.jointTexturePool.releaseAnimationClip(t)},i.clear=function(){this.jointTexturePool.clear(),this.jointAnimationInfo.clear()},t}();p.internal.DataPoolManager=C;var b,I,w,E,P,x,M,T,D,O,U,R,F,N,z,L=new a,q=new a,J=t("SkeletalAnimationState",function(t){function e(e,n){var i;return void 0===n&&(n=""),(i=t.call(this,e,n)||this)._frames=1,i._bakedDuration=0,i._animInfo=null,i._sockets=[],i._animInfoMgr=void 0,i._parent=null,i._curvesInited=!1,i._animInfoMgr=p.director.root.dataPoolManager.jointAnimationInfo,i}k(e,t);var n=e.prototype;return n.initialize=function(e){if(!this._curveLoaded){this._parent=e.getComponent("cc.SkeletalAnimation");var n=this._parent.useBakedAnimation;this._doNotCreateEval=n,t.prototype.initialize.call(this,e),this._curvesInited=!n;var s=i.getOrExtract(this.clip),o=s.frames,a=s.samples;this._frames=o-1,this._animInfo=this._animInfoMgr.getData(e.uuid),this._bakedDuration=this._frames/a,this.setUseBaked(n)}},n.onPlay=function(){var e=this;t.prototype.onPlay.call(this),this._parent.useBakedAnimation&&(this._animInfoMgr.switchClip(this._animInfo,this.clip),this._parent.getUsers().forEach((function(t){t.uploadAnimation(e.clip)})))},n.setUseBaked=function(e){e?(this._sampleCurves=this._sampleCurvesBaked,this.duration=this._bakedDuration):(this._sampleCurves=t.prototype._sampleCurves,this.duration=this.clip.duration,this._curvesInited||(this._curveLoaded=!1,t.prototype.initialize.call(this,this._targetNode),this._curvesInited=!0))},n.rebuildSocketCurves=function(t){if(this._sockets.length=0,this._targetNode)for(var e=this._targetNode,n=0;n<t.length;++n){var s=t[n],o=e.getChildByPath(s.path);if(s.target){for(var u=i.getOrExtract(this.clip),c=s.path,h=u.joints[c],d=o,f=void 0;!h;){var p=c.lastIndexOf("/");if(c=c.substring(0,p),h=u.joints[c],d&&(f||(f=a.identity(q)),a.fromRTS(L,d.rotation,d.position,d.scale),a.multiply(f,L,f),d=d.parent),p<0)break}for(var k=h&&h.transforms,m=u.frames,_=[],v=0;v<m;v++){var y;y=k&&f?a.multiply(L,k[v],f):k?k[v]:f||new a;var g={pos:new r,rot:new l,scale:new r};a.toSRT(y,g.rot,g.pos,g.scale),_.push(g)}this._sockets.push({target:s.target,frames:_})}}},n._sampleCurvesBaked=function(t){var e=t/this.duration,n=this._animInfo,i=this.clip;n.currentClip!==i&&(this._animInfoMgr.switchClip(this._animInfo,i),this._parent.getUsers().forEach((function(t){t.uploadAnimation(i)})),n.data[0]=-1);var s=e*this._frames+.5|0;if(s!==n.data[0]){n.data[0]=s,n.dirty=!0;for(var o=0;o<this._sockets.length;++o){var a=this._sockets[o],r=a.target,l=a.frames[s],u=l.pos,c=l.rot,h=l.scale;r.setRTS(c,u,h)}}},e}(S)),G=t("Socket",(b=u("cc.SkeletalAnimation.Socket"),I=c(j),b((E=function(t,e){void 0===t&&(t=""),void 0===e&&(e=null),this.path=P&&P(),this.target=x&&x(),this.path=t,this.target=e},P=h(E.prototype,"path",[d],(function(){return""})),x=h(E.prototype,"target",[I],(function(){return null})),w=E))||w));m(G,"cc.SkeletalAnimationComponent.Socket");var H=new a,K=new a;function Q(t,e,n){void 0===e&&(e=""),void 0===n&&(n=[]);for(var i=0;i<t.children.length;i++){var s=t.children[i];if(s){var o=e?e+"/"+s.name:s.name;n.push(o),Q(s,o,n)}}return n}var W=(M=u("cc.SkeletalAnimation"),T=f(99),D=c([G]),O=c([G]),M(U=T((z=function(t){function e(){for(var e,n=arguments.length,i=new Array(n),s=0;s<n;s++)i[s]=arguments[s];return(e=t.call.apply(t,[this].concat(i))||this)._useBakedAnimation=F&&F(),e._sockets=N&&N(),e._users=new Set,e._currentBakedState=null,e}k(e,t);var n=e.prototype;return n.onLoad=function(){t.prototype.onLoad.call(this);for(var e=this.node.getComponentsInChildren(s),n=0;n<e.length;++n){var i=e[n];i.skinningRoot===this.node&&this.notifySkinnedMeshAdded(i)}},n.onDestroy=function(){t.prototype.onDestroy.call(this),p.director.root.dataPoolManager.jointAnimationInfo.destroy(this.node.uuid),A().removeSockets(this.node,this._sockets),this._removeAllUsers()},n.onEnable=function(){var e;t.prototype.onEnable.call(this),null==(e=this._currentBakedState)||e.resume()},n.onDisable=function(){var e;t.prototype.onDisable.call(this),null==(e=this._currentBakedState)||e.pause()},n.start=function(){this.sockets=this._sockets,this._applyBakeFlagChange(),t.prototype.start.call(this)},n.pause=function(){var e;this._useBakedEffectively?null==(e=this._currentBakedState)||e.pause():t.prototype.pause.call(this)},n.resume=function(){var e;this._useBakedEffectively?null==(e=this._currentBakedState)||e.resume():t.prototype.resume.call(this)},n.stop=function(){this._useBakedEffectively?this._currentBakedState&&(this._currentBakedState.stop(),this._currentBakedState=null):t.prototype.stop.call(this)},n.querySockets=function(){var t=this._defaultClip&&Object.keys(i.getOrExtract(this._defaultClip).joints).sort().reduce((function(t,e){return e.startsWith(t[t.length-1]+"/")||t.push(e),t}),[])||[];if(!t.length)return["please specify a valid default animation clip first"];for(var e=[],n=0;n<t.length;n++){var s=t[n],o=this.node.getChildByPath(s);o&&(e.push(s),Q(o,s,e))}return e},n.rebuildSocketAnimations=function(){var t=this;for(var e in this._sockets.forEach((function(e){var n=t.node.getChildByPath(e.path),i=e.target;n&&i&&(i.name=e.path.substring(e.path.lastIndexOf("/")+1)+" Socket",i.parent=t.node,o(n,t.node,H),a.fromRTS(K,i.rotation,i.position,i.scale),a.equals(K,H)||(i.matrix=H))})),this._nameToState)this._nameToState[e].rebuildSocketCurves(this._sockets)},n.createSocket=function(t){var e=this._sockets.find((function(e){return e.path===t}));if(e)return e.target;if(!this.node.getChildByPath(t))return _("illegal socket path"),null;var n=new j;return n.parent=this.node,this._sockets.push(new G(t,n)),this.rebuildSocketAnimations(),n},n.notifySkinnedMeshAdded=function(t){var e=this._useBakedEffectively,n=t.associatedAnimation;if(n&&n._users.delete(t),t.associatedAnimation=this,t.setUseBakedAnimation(e,!0),e){var i=this._currentBakedState;i&&t.uploadAnimation(i.clip)}this._users.add(t)},n.notifySkinnedMeshRemoved=function(t){v(t.associatedAnimation===this||null===t.associatedAnimation),t.setUseBakedAnimation(!1),t.associatedAnimation=null,this._users.delete(t)},n.getUsers=function(){return this._users},n._createState=function(t,e){return new J(t,e)},n._doCreateState=function(e,n){var i=t.prototype._doCreateState.call(this,e,n);return i.rebuildSocketCurves(this._sockets),i},n.doPlayOrCrossFade=function(e,n){if(this._useBakedEffectively){this._currentBakedState&&this._currentBakedState.stop();var i=e;this._currentBakedState=i,i.play()}else t.prototype.doPlayOrCrossFade.call(this,e,n)},n._removeAllUsers=function(){var t=this;Array.from(this._users).forEach((function(e){t.notifySkinnedMeshRemoved(e)}))},n._applyBakeFlagChange=function(){var t=this._useBakedEffectively;for(var e in this._nameToState)this._nameToState[e].setUseBaked(t);this._users.forEach((function(e){e.setUseBakedAnimation(t)})),t?A().removeSockets(this.node,this._sockets):(A().addSockets(this.node,this._sockets),this._currentBakedState=null)},y(e,[{key:"sockets",get:function(){return this._sockets},set:function(t){if(!this._useBakedEffectively){var e=A();e.removeSockets(this.node,this._sockets),e.addSockets(this.node,t)}this._sockets=t,this.rebuildSocketAnimations()}},{key:"useBakedAnimation",get:function(){return this._useBakedAnimation},set:function(t){this._useBakedAnimation=t,this._applyBakeFlagChange()}},{key:"_useBakedEffectively",get:function(){return this._useBakedAnimation}}]),e}(B),z.Socket=G,g((R=z).prototype,"sockets",[D],Object.getOwnPropertyDescriptor(R.prototype,"sockets"),R.prototype),F=h(R.prototype,"_useBakedAnimation",[d],(function(){return!0})),N=h(R.prototype,"_sockets",[O],(function(){return[]})),U=R))||U)||U);t({SkeletalAnimation:W,SkeletalAnimationComponent:W}),p.SkeletalAnimationComponent=W,m(W,"cc.SkeletalAnimationComponent")}}}));
