// Copyright (c) 2017-2022 Xiamen Yaji Software Co., Ltd.

#pragma define-meta USE_INSTANCING editor(elevated: true)
// this macro must be non-const
// use #pragma define to rename a macro with editor flag
//#pragma define CC_SURFACES_USE_INSTANCING USE_INSTANCING



// all material->internal macros
// varying-dependent macros
#ifndef CC_SURFACES_USE_SECOND_UV
  #define CC_SURFACES_USE_SECOND_UV 0
#endif
#ifndef CC_SURFACES_USE_TANGENT_SPACE
  #define CC_SURFACES_USE_TANGENT_SPACE 0
#endif
#ifndef CC_SURFACES_USE_VERTEX_COLOR
  #define CC_SURFACES_USE_VERTEX_COLOR 0
#endif
#ifndef CC_SURFACES_TRANSFER_LOCAL_POS
  #define CC_SURFACES_TRANSFER_LOCAL_POS 0
#endif
#ifndef CC_SURFACES_TRANSFER_CLIP_POS
  #define CC_SURFACES_TRANSFER_CLIP_POS 0
#endif

// force CC_SURFACES_USE_LIGHT_MAP to 0 is valid, force to 1 is invalid
#ifndef CC_SURFACES_USE_LIGHT_MAP
  #ifdef CC_USE_LIGHTMAP
    #define CC_SURFACES_USE_LIGHT_MAP CC_USE_LIGHTMAP
  #else
    #define CC_SURFACES_USE_LIGHT_MAP 0
  #endif
#endif

// varying-undependent macros
#ifndef CC_SURFACES_FLIP_UV
  #define CC_SURFACES_FLIP_UV 0
#endif
#ifndef CC_SURFACES_USE_TWO_SIDED
  #define CC_SURFACES_USE_TWO_SIDED 0
#endif
#ifndef CC_SURFACES_USE_REFLECTION_DENOISE
  #define CC_SURFACES_USE_REFLECTION_DENOISE 0
#endif
#ifndef CC_SURFACES_LIGHTING_ANISOTROPIC
  #define CC_SURFACES_LIGHTING_ANISOTROPIC 0
#endif
#ifndef CC_SURFACES_LIGHTING_ANISOTROPIC_ENVCONVOLUTION_COUNT
  #define CC_SURFACES_LIGHTING_ANISOTROPIC_ENVCONVOLUTION_COUNT 0
#endif
#ifndef CC_SURFACES_USE_LEGACY_COMPATIBLE_LIGHTING
  #define CC_SURFACES_USE_LEGACY_COMPATIBLE_LIGHTING 0
#endif
#ifndef CC_SURFACES_LIGHTING_USE_FRESNEL
  #define CC_SURFACES_LIGHTING_USE_FRESNEL 0
#endif
#ifndef CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR
  #define CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR 0
#endif
#ifndef CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE
  #define CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE 0
#endif
#ifndef CC_SURFACES_LIGHTING_USE_SHADOWMAP_TRANSMIT
  #define CC_SURFACES_LIGHTING_USE_SHADOWMAP_TRANSMIT 0
#endif
#ifndef CC_SURFACES_LIGHTING_TRT
  #define CC_SURFACES_LIGHTING_TRT 0
#endif
#ifndef CC_SURFACES_LIGHTING_DUAL_LOBE_SPECULAR
  #define CC_SURFACES_LIGHTING_DUAL_LOBE_SPECULAR 0
#endif
#ifndef CC_SURFACES_LIGHTING_SHEEN
  #define CC_SURFACES_LIGHTING_SHEEN 0
#endif
#ifndef CC_SURFACES_LIGHTING_CLEAR_COAT
  #define CC_SURFACES_LIGHTING_CLEAR_COAT 0
#endif
#ifndef CC_SURFACES_LIGHTING_TT
  #define CC_SURFACES_LIGHTING_TT 0
#endif
#ifndef CC_SURFACES_LIGHTING_SSS
  #define CC_SURFACES_LIGHTING_SSS 0
#endif

// common 2nd-specular for TRT / Dual-Lobe / Wet / ClearCoat...
#ifndef CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR
  #if CC_SURFACES_LIGHTING_TRT || CC_SURFACES_LIGHTING_DUAL_LOBE_SPECULAR || CC_SURFACES_LIGHTING_SHEEN || CC_SURFACES_LIGHTING_CLEAR_COAT
    #define CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR 1
  #endif
#endif
#ifndef CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR
  #define CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR 0
#endif

// this value should be sub-layers count
#ifndef CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND
  #if CC_SURFACES_LIGHTING_CLEAR_COAT
    #define CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND 1
  #endif
#endif
#ifndef CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND
  #define CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND 0
#endif


// render-to-color-map can disable functionality to avoid error data map
#ifndef CC_SURFACES_ENABLE_DEBUG_VIEW
  #define CC_SURFACES_ENABLE_DEBUG_VIEW 1
#endif

// for ssss skin, only can be defined in pass embedded macros
// CC_SURFACES_LIGHTING_DISABLE_DIFFUSE / CC_SURFACES_LIGHTING_DISABLE_SPECULAR
