System.register(["./index-Y4La_nfG.js","./device-manager-B8lyUDPi.js","./buffer-barrier-q_79u36H.js","./gc-object-D18ulfCO.js","./component-CsuvAQKv.js","./prefab-BQYc0LyR.js","./camera-component-X7pwLmnP.js","./model-renderer-D7qfPDfZ.js","./renderer-CZheciPr.js","./global-exports-CLZKKIY2.js"],(function(e){"use strict";var n,r,t,a,o,i,s,d,u,c,l,m,f,b,v,x,h,g,p,I,M;return{setters:[function(e){n=e.T,r=e.E,t=e.R,a=e.M,o=e.n,i=e.b,s=e.a,d=e.c,u=e.t,c=e.s,l=e.r,m=e.o},null,function(e){f=e.P},function(e){b=e._,v=e.j},function(e){x=e.C},function(e){h=e.P},function(e){g=e.C},function(e){p=e.M},function(e){I=e.R},function(e){M=e.c}],execute:function(){function S(e,n,r){var t=e.o,a=e.d,o=1/a.x,i=1/a.y,s=1/a.z,d=(n.x-t.x)*o,u=(r.x-t.x)*o,c=(n.y-t.y)*i,l=(r.y-t.y)*i,m=(n.z-t.z)*s,f=(r.z-t.z)*s,b=Math.max(Math.max(Math.min(d,u),Math.min(c,l)),Math.min(m,f)),v=Math.min(Math.min(Math.max(d,u),Math.max(c,l)),Math.max(m,f));return v<0||b>v?0:b>0?b:v}var y,C,N,A,T,j,L,E,R,Y,w=(y=n.create(),C={distance:1/0,doubleSided:!1,mode:r.ANY},N=0,A=function(e,n,t,a,o,i){e===r.CLOSEST?(N>n||0===N)&&(N=n,i&&(0===i.length?i.push({distance:n,vertexIndex0:t/3,vertexIndex1:a/3,vertexIndex2:o/3}):(i[0].distance=n,i[0].vertexIndex0=t/3,i[0].vertexIndex1=a/3,i[0].vertexIndex2=o/3))):(N=n,i&&i.push({distance:n,vertexIndex0:t/3,vertexIndex1:a/3,vertexIndex2:o/3}))},T=function(e,n,t,a,s){if(t===f.TRIANGLE_LIST)for(var d=n.length,u=0;u<d;u+=3){var c=3*n[u],l=3*n[u+1],m=3*n[u+2];i.set(y.a,e[c],e[c+1],e[c+2]),i.set(y.b,e[l],e[l+1],e[l+2]),i.set(y.c,e[m],e[m+1],e[m+2]);var b=o.rayTriangle(a,y,s.doubleSided);if(!(0===b||b>s.distance)&&(A(s.mode,b,c,l,m,s.result),s.mode===r.ANY))return b}else if(t===f.TRIANGLE_STRIP)for(var v=n.length-2,x=0,h=0;h<v;h+=1){var g=3*n[h-x],p=3*n[h+x+1],I=3*n[h+2];i.set(y.a,e[g],e[g+1],e[g+2]),i.set(y.b,e[p],e[p+1],e[p+2]),i.set(y.c,e[I],e[I+1],e[I+2]),x=~x;var M=o.rayTriangle(a,y,s.doubleSided);if(!(0===M||M>s.distance)&&(A(s.mode,M,g,p,I,s.result),s.mode===r.ANY))return M}else if(t===f.TRIANGLE_FAN){var S=n.length-1,C=3*n[0];i.set(y.a,e[C],e[C+1],e[C+2]);for(var T=1;T<S;T+=1){var j=3*n[T],L=3*n[T+1];i.set(y.b,e[j],e[j+1],e[j+2]),i.set(y.c,e[L],e[L+1],e[L+2]);var E=o.rayTriangle(a,y,s.doubleSided);if(!(0===E||E>s.distance)&&(A(s.mode,E,C,j,L,s.result),s.mode===r.ANY))return E}}return N},function(e,n,r){if(N=0,0===n.geometricInfo.positions.length)return N;var t=void 0===r?C:r;if(S(e,n.geometricInfo.boundingBox.min,n.geometricInfo.boundingBox.max)){var a=n.primitiveMode,o=n.geometricInfo,i=o.positions,s=o.indices;T(i,s,a,e,t)}return N}),P=function(){var e=0,n={distance:1/0,doubleSided:!1,mode:r.ANY};return function(t,a,o){e=0;var i=void 0===o?n:o,s=a.renderingSubMeshes.length,d=a.struct.minPosition,u=a.struct.maxPosition;if(d&&u&&!S(t,d,u))return e;for(var c=0;c<s;c++){var l=a.renderingSubMeshes[c],m=w(t,l,i);if(m)if(i.mode===r.CLOSEST)(0===e||e>m)&&(e=m,i.subIndices&&(i.subIndices[0]=c));else if(e=m,i.subIndices&&i.subIndices.push(c),i.mode===r.ANY)return m}return e&&i.mode===r.CLOSEST&&(i.result&&(i.result[0].distance=e,i.result.length=1),i.subIndices&&(i.subIndices.length=1)),e}}(),O=function(){var e=0,n={distance:1/0,doubleSided:!1,mode:r.ANY},s=new t,d=new a;return function(u,c,l){e=0;var m=void 0===l?n:l,f=c.worldBounds;if(f&&!o.rayAABB(u,f))return e;t.copy(s,u),c.node&&(a.invert(d,c.node.getWorldMatrix(d)),i.transformMat4(s.o,u.o,d),i.transformMat4Normal(s.d,u.d,d));for(var b=c.subModels,v=0;v<b.length;v++){var x=b[v].subMesh,h=w(s,x,m);if(h)if(m.mode===r.CLOSEST)(0===e||e>h)&&(e=h,m.subIndices&&(m.subIndices[0]=v));else if(e=h,m.subIndices&&m.subIndices.push(v),m.mode===r.ANY)return h}return e&&m.mode===r.CLOSEST&&(m.result&&(m.result[0].distance=e,m.result.length=1),m.subIndices&&(m.subIndices.length=1)),e}}();o.rayModel=O,o.raySubMesh=w,o.rayMesh=P;var z=c,B=u;e("P",(j=d("cc.PrefabLink"),L=B(h),j((R=function(e){function n(){var n;return(n=e.call(this)||this).prefab=Y&&Y(),n}return b(n,e),n}(x),Y=s(R.prototype,"prefab",[L,z],(function(){return null})),E=R))||E)),l(g,"Camera",[{name:"CameraClearFlag",newName:"ClearFlag"}]),l(g.prototype,"Camera.prototype",[{name:"color",newName:"clearColor"},{name:"depth",newName:"clearDepth"},{name:"stencil",newName:"clearStencil"}]),m(I.prototype,"Renderer.prototype",[{name:"getMaterial",suggest:"please use renderer.getSharedMaterial instead."}]),M.CameraComponent=g,v(g,"cc.CameraComponent"),M.RenderableComponent=p,v(p,"cc.RenderableComponent")}}}));
