System.register(["./index-C5lmLqDW.js","./gc-object-CKHc4SnS.js","./global-exports-CR3GRnjt.js","./component-BaGvu7EF.js","./ui-renderer-DuhVjkfF.js","./camera-component-Df61RNZm.js","./director-DIlqD2Nd.js","./scene-7MDSMR3j.js","./factory-D9_8ZCqM.js","./deprecated-Ca3AjUwj.js","./pipeline-state-manager-Cdpe3is6.js","./debug-view-CKetkq9d.js","./sprite-frame-C6JiNTOk.js","./deprecated-C8l6Kwy8.js","./model-renderer-BcRDUYby.js","./renderer-9hfAnqUF.js"],(function(e){"use strict";var t,i,n,o,r,s,a,c,h,l,u,_,p,d,m,f,g,y,v,R,S,C,w,E,b,T,F,z,A,I,O,D,P,M,N,x,L,H,U,X,W,G,j,V,B,k,Y,Q;return{setters:[function(e){t=e.z,i=e.G,n=e.O,o=e.J,r=e.B,s=e.V,a=e.k,c=e.x,h=e.S,l=e.$,u=e.c,_=e.d,p=e.i,d=e.K,m=e.b,f=e.t,g=e.a,y=e.s,v=e.h,R=e.r,S=e.C,C=e.o},function(e){w=e.A,E=e.a4,b=e._,T=e.s,F=e.S,z=e.h,A=e.$,I=e.a5,O=e.a,D=e.b,P=e.T,M=e.k},function(e){N=e.c},function(e){x=e.C},function(e){L=e.c,H=e.S,U=e.U},function(e){X=e.a,W=e.C},function(e){G=e.d},function(e){j=e.T,V=e.d},null,function(e){B=e.M},function(e){k=e.M},null,function(e){Y=e.S},null,function(e){Q=e.M},null],execute:function(){var q,K=t(),J=((q={})[w.ORIENTATION_AUTO]=E.AUTO,q[w.ORIENTATION_LANDSCAPE]=E.LANDSCAPE,q[w.ORIENTATION_PORTRAIT]=E.PORTRAIT,q),$=e("V",function(e){function a(){var i;(i=e.call(this)||this)._designResolutionSize=t(0,0),i._scaleX=1,i._scaleY=1,i._viewportRect=c(),i._visibleRect=c(),i._autoFullScreen=!1,i._retinaEnabled=!1,i._resizeCallback=null;var n=Z,o=ee;return i._rpExactFit=new ce(n.EQUAL_TO_FRAME,o.EXACT_FIT),i._rpShowAll=new ce(n.EQUAL_TO_FRAME,o.SHOW_ALL),i._rpNoBorder=new ce(n.EQUAL_TO_FRAME,o.NO_BORDER),i._rpFixedHeight=new ce(n.EQUAL_TO_FRAME,o.FIXED_HEIGHT),i._rpFixedWidth=new ce(n.EQUAL_TO_FRAME,o.FIXED_WIDTH),i._resolutionPolicy=i._rpShowAll,i}b(a,e);var h=a.prototype;return h.init=function(){var e=i.windowSize,t=e.width,o=e.height;this._designResolutionSize.width=t,this._designResolutionSize.height=o,this._viewportRect.width=t,this._viewportRect.height=o,this._visibleRect.width=t,this._visibleRect.height=o,K.width=this._visibleRect.width,K.height=this._visibleRect.height,n.init(this._visibleRect),this.resizeWithBrowserSize(!0);var r=T.querySettings(F.SCREEN,"designResolution");r&&this.setDesignResolutionSize(Number(r.width),Number(r.height),r.policy||ce.FIXED_HEIGHT),i.on("window-resize",this._updateAdaptResult,this),i.on("fullscreen-change",this._updateAdaptResult,this)},h.resizeWithBrowserSize=function(e){o.handleResizeEvent=e},h.setResizeCallback=function(e){"function"!=typeof e&&null!=e||(this._resizeCallback=e)},h.setOrientation=function(e){o.orientation=J[e]},h.adjustViewportMeta=function(){},h.enableRetina=function(e){this._retinaEnabled=!!e},h.isRetinaEnabled=function(){return this._retinaEnabled},h.enableAutoFullScreen=function(e){e!==this._autoFullScreen&&(this._autoFullScreen=e,e&&i.requestFullScreen().catch((function(){})))},h.isAutoFullScreenEnabled=function(){return this._autoFullScreen},h.setCanvasSize=function(e,t){o.resolutionScale=1;var n=o.devicePixelRatio,s=new r(e*n,t*n);i.windowSize=s},h.getCanvasSize=function(){return i.windowSize},h.getFrameSize=function(){var e=o.devicePixelRatio,t=i.windowSize;return t.width/=e,t.height/=e,t},h.setFrameSize=function(e,t){var n=o.devicePixelRatio;i.windowSize=new r(e*n,t*n)},h.getVisibleSize=function(){return new r(this._visibleRect.width,this._visibleRect.height)},h.getVisibleSizeInPixel=function(){return new r(this._visibleRect.width*this._scaleX,this._visibleRect.height*this._scaleY)},h.getVisibleOrigin=function(){return new s(this._visibleRect.x,this._visibleRect.y)},h.getVisibleOriginInPixel=function(){return new s(this._visibleRect.x*this._scaleX,this._visibleRect.y*this._scaleY)},h.getResolutionPolicy=function(){return this._resolutionPolicy},h._updateResolutionPolicy=function(e){if(e instanceof ce)this._resolutionPolicy=e;else{var t=ce;e===t.EXACT_FIT&&(this._resolutionPolicy=this._rpExactFit),e===t.SHOW_ALL&&(this._resolutionPolicy=this._rpShowAll),e===t.NO_BORDER&&(this._resolutionPolicy=this._rpNoBorder),e===t.FIXED_HEIGHT&&(this._resolutionPolicy=this._rpFixedHeight),e===t.FIXED_WIDTH&&(this._resolutionPolicy=this._rpFixedWidth)}},h.setResolutionPolicy=function(e){this._updateResolutionPolicy(e);var t=le.getDesignResolutionSize();le.setDesignResolutionSize(t.width,t.height,e)},h.setDesignResolutionSize=function(e,t,i){if(e>0&&t>0){this._updateResolutionPolicy(i);var o=this._resolutionPolicy;o&&o.preApply(this),this._designResolutionSize.width=e,this._designResolutionSize.height=t;var r=o.apply(this,this._designResolutionSize);if(r.scale&&2===r.scale.length&&(this._scaleX=r.scale[0],this._scaleY=r.scale[1]),r.viewport){var s=this._viewportRect,a=this._visibleRect,c=r.viewport;s.x=c.x,s.y=c.y,s.width=c.width,s.height=c.height,a.x=0,a.y=0,a.width=c.width/this._scaleX,a.height=c.height/this._scaleY}o.postApply(this),K.width=this._visibleRect.width,K.height=this._visibleRect.height,n.init(this._visibleRect),this.emit("design-resolution-changed")}else z(2200)},h.getDesignResolutionSize=function(){return new r(this._designResolutionSize.width,this._designResolutionSize.height)},h.setRealPixelResolution=function(e,t,i){this.setDesignResolutionSize(e,t,i)},h.getViewportRect=function(){return this._viewportRect},h.getScaleX=function(){return this._scaleX},h.getScaleY=function(){return this._scaleY},h.getDevicePixelRatio=function(){return o.devicePixelRatio},h.convertToLocationInView=function(e,t,n,r){void 0===r&&(r=new s);var a=o.devicePixelRatio*(e-n.left),c=o.devicePixelRatio*(n.top+n.height-t);return o.isFrameRotated?(r.x=i.windowSize.width-c,r.y=a):(r.x=a,r.y=c),r},h._convertToUISpace=function(e){var t=this._viewportRect;e.x=(e.x-t.x)/this._scaleX,e.y=(e.y-t.y)/this._scaleY},h._updateAdaptResult=function(e,t,i){N.director.root.resize(e,t,void 0===i||0===i?1:i);var n=this._designResolutionSize,o=n.width,r=n.height;e>0&&t>0?this.setDesignResolutionSize(o,r,this._resolutionPolicy):A(!1,"_updateAdaptResult Invalid size."),this.emit("canvas-resize"),this._resizeCallback&&this._resizeCallback()},a}(I(h)));$.instance=void 0;var Z=function(){function e(){this.name="ContainerStrategy"}var t=e.prototype;return t.preApply=function(){},t.apply=function(){},t.postApply=function(){},t._setupCanvas=function(){var e=N.game.canvas;if(e){var t=i.windowSize;e.width!==t.width&&(e.width=t.width),e.height!==t.height&&(e.height=t.height)}},e}();Z.EQUAL_TO_FRAME=void 0,Z.PROPORTION_TO_FRAME=void 0;var ee=function(){function e(){this.name="ContentStrategy",this._result={scale:[1,1],viewport:null},this._strategy=ce.UNKNOWN}var t=e.prototype;return t.preApply=function(){},t.apply=function(){return{scale:[1,1]}},t.postApply=function(){},t._buildResult=function(e,t,i,n,o,r){Math.abs(e-i)<2&&(i=e),Math.abs(t-n)<2&&(n=t);var s=new a(Math.round((e-i)/2),Math.round((t-n)/2),i,n),c=this._result;return c.scale=[o,r],c.viewport=s,c},O(e,[{key:"strategy",get:function(){return this._strategy}}]),e}(),te=function(e){function t(){var t;return(t=e.call(this)||this).name="EqualToFrame",t}return b(t,e),t.prototype.apply=function(){o.isProportionalToFrame=!1,this._setupCanvas()},t}(Z),ie=function(e){function t(){var t;return(t=e.call(this)||this).name="ProportionalToFrame",t}return b(t,e),t.prototype.apply=function(){o.isProportionalToFrame=!0,this._setupCanvas()},t}(Z);Z.EQUAL_TO_FRAME=new te,Z.PROPORTION_TO_FRAME=new ie;var ne=function(e){function t(){var t;return(t=e.call(this)||this).name="ExactFit",t._strategy=ce.EXACT_FIT,t}return b(t,e),t.prototype.apply=function(e,t){var n=i.windowSize,o=n.width,r=n.height,s=o/t.width,a=r/t.height;return this._buildResult(o,r,o,r,s,a)},t}(ee),oe=function(e){function t(){var t;return(t=e.call(this)||this).name="ShowAll",t._strategy=ce.SHOW_ALL,t}return b(t,e),t.prototype.apply=function(e,t){var n,o,r=i.windowSize,s=r.width,a=r.height,c=t.width,h=t.height,l=s/c,u=a/h,_=0;return l<u?(n=s,o=h*(_=l)):(n=c*(_=u),o=a),this._buildResult(s,a,n,o,_,_)},t}(ee),re=function(e){function t(){var t;return(t=e.call(this)||this).name="NoBorder",t._strategy=ce.NO_BORDER,t}return b(t,e),t.prototype.apply=function(e,t){var n,o,r,s=i.windowSize,a=s.width,c=s.height,h=t.width,l=t.height,u=a/h,_=c/l;return u<_?(o=h*(n=_),r=c):(o=a,r=l*(n=u)),this._buildResult(a,c,o,r,n,n)},t}(ee),se=function(e){function t(){var t;return(t=e.call(this)||this).name="FixedHeight",t._strategy=ce.FIXED_HEIGHT,t}return b(t,e),t.prototype.apply=function(e,t){var n=i.windowSize,o=n.width,r=n.height,s=r/t.height,a=o,c=r;return this._buildResult(o,r,a,c,s,s)},t}(ee),ae=function(e){function t(){var t;return(t=e.call(this)||this).name="FixedWidth",t._strategy=ce.FIXED_WIDTH,t}return b(t,e),t.prototype.apply=function(e,t){var n=i.windowSize,o=n.width,r=n.height,s=o/t.width,a=o,c=r;return this._buildResult(o,r,a,c,s,s)},t}(ee),ce=e("R",function(){function e(e,t){this.name="ResolutionPolicy",this._containerStrategy=e,this._contentStrategy=t}var t=e.prototype;return t.preApply=function(e){this._contentStrategy.preApply(e)},t.apply=function(e,t){return this._containerStrategy.apply(e,t),this._contentStrategy.apply(e,t)},t.postApply=function(e){this._contentStrategy.postApply(e)},t.setContainerStrategy=function(e){this._containerStrategy=e},t.setContentStrategy=function(e){this._contentStrategy=e},t.getContentStrategy=function(){return this._contentStrategy},O(e,[{key:"canvasSize",get:function(){return i.windowSize}}]),e}());ce.EXACT_FIT=0,ce.NO_BORDER=1,ce.SHOW_ALL=2,ce.FIXED_HEIGHT=3,ce.FIXED_WIDTH=4,ce.UNKNOWN=5,ce.ContainerStrategy=Z,ce.ContentStrategy=ee,N.ResolutionPolicy=ce,ee.EXACT_FIT=new ne,ee.SHOW_ALL=new oe,ee.NO_BORDER=new re,ee.FIXED_HEIGHT=new se,ee.FIXED_WIDTH=new ae;var he,le=e("v",$.instance=N.view=new $);G.registerSystem("view",le,0),N.winSize=K,l({RenderComponent:{newName:"UIRenderer",since:"1.2.0",removed:!0},UITransformComponent:{newName:"UITransform",since:"1.2.0",removed:!1},CanvasComponent:{newName:"Canvas",since:"1.2.0",removed:!1}}),l({UIRenderable:{newName:"UIRenderer",since:"3.0.0",removed:!0}}),l({Renderable2D:{newName:"UIRenderer",since:"3.6.0",removed:!1}});var ue,_e,pe,de,me,fe,ge,ye,ve,Re=e("a",u("cc.RenderRoot2D")(he=p(100)(he=d(L)(he=_(he=function(e){function t(){return e.apply(this,arguments)||this}b(t,e);var i=t.prototype;return i.onEnable=function(){N.director.root.batcher2D.addScreen(this)},i.onDisable=function(){N.director.root.batcher2D.removeScreen(this)},i.onDestroy=function(){N.director.root.batcher2D.removeScreen(this)},t}(x))||he)||he)||he)||he),Se=new m;!function(e){e[e.OVERLAY=0]="OVERLAY",e[e.INTERSPERSE=1]="INTERSPERSE"}(ve||(ve={}));var Ce,we=e("C",(ue=u("cc.Canvas"),_e=p(100),pe=f(W),de=f(W),ue(me=_e(me=_((fe=function(e){function t(){var t;return(t=e.call(this)||this)._cameraComponent=ge&&ge(),t._alignCanvasWithScreen=ye&&ye(),t._pos=new m,t._renderMode=ve.OVERLAY,t._thisOnCameraResized=t._onResizeCamera.bind(P(t)),t}b(t,e);var o=t.prototype;return o.__preload=function(){var e=this.getComponent("cc.Widget");e&&e.updateAlignment(),this._cameraComponent&&(this._cameraComponent._createCamera(),this._cameraComponent.node.on(X.TARGET_TEXTURE_CHANGE,this._thisOnCameraResized)),this._onResizeCamera(),le.on("canvas-resize",this._thisOnCameraResized,this),le.on("design-resolution-changed",this._thisOnCameraResized,this)},o.onEnable=function(){e.prototype.onEnable.call(this),this._cameraComponent&&this._cameraComponent.node.on(X.TARGET_TEXTURE_CHANGE,this._thisOnCameraResized)},o.onDisable=function(){e.prototype.onDisable.call(this),this._cameraComponent&&this._cameraComponent.node.off(X.TARGET_TEXTURE_CHANGE,this._thisOnCameraResized)},o.onDestroy=function(){e.prototype.onDestroy.call(this),le.off("canvas-resize",this._thisOnCameraResized,this),le.off("design-resolution-changed",this._thisOnCameraResized,this)},o._onResizeCamera=function(){if(this._cameraComponent&&this._alignCanvasWithScreen){if(this._cameraComponent.targetTexture)this._cameraComponent.orthoHeight=n.height/2;else{var e=i.windowSize;this._cameraComponent.orthoHeight=e.height/le.getScaleY()/2}this.node.getWorldPosition(Se),this._cameraComponent.node.setWorldPosition(Se.x,Se.y,1e3)}},o._getViewPriority=function(){if(this._cameraComponent){var e,t=null==(e=this.cameraComponent)?void 0:e.priority;return this._renderMode===ve.OVERLAY?t|1<<30:-1073741825&t}return 0},O(t,[{key:"renderMode",get:function(){return this._renderMode},set:function(e){this._renderMode=e,this._cameraComponent&&(this._cameraComponent.priority=this._getViewPriority())}},{key:"cameraComponent",get:function(){return this._cameraComponent},set:function(e){this._cameraComponent!==e&&(this._cameraComponent=e,this._onResizeCamera())}},{key:"alignCanvasWithScreen",get:function(){return this._alignCanvasWithScreen},set:function(e){this._alignCanvasWithScreen=e,this._onResizeCamera()}}]),t}(Re),D(fe.prototype,"cameraComponent",[pe],Object.getOwnPropertyDescriptor(fe.prototype,"cameraComponent"),fe.prototype),ge=g(fe.prototype,"_cameraComponent",[de],(function(){return null})),ye=g(fe.prototype,"_alignCanvasWithScreen",[y],(function(){return!0})),me=fe))||me)||me)||me));N.Canvas=we;var Ee,be,Te,Fe,ze,Ae,Ie,Oe,De,Pe,Me,Ne,xe=e("U",u("cc.UIComponent")(Ce=d(L)(Ce=p(110)(Ce=_(Ce=function(e){function t(){var t;return(t=e.call(this)||this)._lastParent=null,t.stencilStage=H.DISABLED,t}b(t,e);var i=t.prototype;return i.__preload=function(){this.node._uiProps.uiComp=this},i.onEnable=function(){},i.onDisable=function(){},i.onDestroy=function(){var e=this.node._uiProps;e.uiComp===this&&(e.uiComp=null)},i.postUpdateAssembler=function(){},i.markForUpdateRenderData=function(){},i.setNodeDirty=function(){},i.setTextureDirty=function(){},t}(x))||Ce)||Ce)||Ce)||Ce);v(xe.prototype,"UIComponent",[{name:"_visibility"},{name:"setVisibility"}]),R(we.prototype,"Canvas.prototype",[{name:"camera",newName:"cameraComponent.camera",customGetter:function(){var e;return null==(e=this._cameraComponent)?void 0:e.camera}},{name:"clearFlag",newName:"cameraComponent.clearFlags",customGetter:function(){return this._cameraComponent?this._cameraComponent.clearFlags:0},customSetter:function(e){this._cameraComponent&&(this._cameraComponent.clearFlags=e)}},{name:"color",newName:"cameraComponent.clearColor",customGetter:function(){return this._cameraComponent?this._cameraComponent.clearColor:S.BLACK},customSetter:function(e){this._cameraComponent&&(this._cameraComponent.clearColor=e)}},{name:"priority",newName:"cameraComponent.priority",customGetter:function(){return this._cameraComponent?this._cameraComponent.priority:0},customSetter:function(e){this._cameraComponent&&(this._cameraComponent.priority=e)}},{name:"targetTexture",newName:"cameraComponent.targetTexture",customGetter:function(){return this._cameraComponent?this._cameraComponent.targetTexture:null},customSetter:function(e){this._cameraComponent&&(this._cameraComponent.targetTexture=e)}},{name:"visibility",newName:"cameraComponent.visibility",customGetter:function(){return this._cameraComponent?this._cameraComponent.visibility:0}}]),C(L.prototype,"UITransform.prototype",[{name:"priority",suggest:"Please use setSiblingIndex to change index of the current node in its parent's children array."}]),N.UITransformComponent=L,M(L,"cc.UITransformComponent"),M(U,"cc.RenderComponent"),N.CanvasComponent=we,M(we,"cc.CanvasComponent"),N.internal.Renderable2D=U,M(U,"cc.Renderable2D"),function(e){e[e.SIMPLE=0]="SIMPLE",e[e.SLICED=1]="SLICED",e[e.TILED=2]="TILED"}(Ne||(Ne={})),e("S",(Ee=u("cc.SpriteRenderer"),be=p(100),Te=f(Y),Ee(Fe=be((ze=function(e){function t(){var t;return(t=e.call(this)||this)._spriteFrame=Ae&&Ae(),t._mode=Ie&&Ie(),t._color=Oe&&Oe(),t._flipX=De&&De(),t._flipY=Pe&&Pe(),t._size=Me&&Me(),t._model=null,t}b(t,e);var i=t.prototype;return i.onLoad=function(){this._spriteFrame&&(this._spriteFrame.mesh||this._spriteFrame.ensureMeshData(),this._spriteFrame.mesh.initialize()),this._updateModels()},i.onRestore=function(){this._updateModels(),this.enabledInHierarchy&&this._attachToScene()},i.onEnable=function(){e.prototype.onEnable.call(this),this._model||this._updateModels(),this._attachToScene()},i.onDisable=function(){this._model&&this._detachFromScene()},i.onDestroy=function(){this._model&&(N.director.root.destroyModel(this._model),this._model=null,this._models.length=0)},i._updateModels=function(){if(this._spriteFrame){var e=this._model;if(e?(e.destroy(),e.initialize(),e.node=e.transform=this.node):this._createModel(),this._model){var t=this._spriteFrame.mesh;this._model.createBoundingShape(t.struct.minPosition,t.struct.maxPosition),this._updateModelParams(),this._onUpdateLocalDescriptorSet()}}},i._createModel=function(){var e=this._model=N.director.root.createModel(B);e.visFlags=this.visibility,e.node=e.transform=this.node,this._models.length=0,this._models.push(this._model)},i._updateModelParams=function(){if(this._spriteFrame&&this._model){this._spriteFrame.ensureMeshData();var e=this._spriteFrame.mesh;this.node.hasChangedFlags|=j.POSITION,this._model.transform.hasChangedFlags|=j.POSITION;var t=e?e.renderingSubMeshes:null;if(t)for(var i=t.length,n=0;n<i;++n){var o=this.getRenderMaterial(n);o&&!o.isValid&&(o=null);var r=t[n];r&&this._model.initSubModel(n,r,o||this._getBuiltinMaterial())}this._model.enabled=!0}},i._getBuiltinMaterial=function(){return V.get("missing-material")},i._onMaterialModified=function(t,i){e.prototype._onMaterialModified.call(this,t,i),this._spriteFrame&&this._model&&this._model.inited&&this._onRebuildPSO(t,i||this._getBuiltinMaterial())},i._onRebuildPSO=function(e,t){this._model&&this._model.inited&&(this._model.setSubModelMaterial(e,t),this._onUpdateLocalDescriptorSet())},i._onUpdateLocalDescriptorSet=function(){if(this._spriteFrame&&this._model&&this._model.inited)for(var e=this._spriteFrame.getGFXTexture(),t=this._spriteFrame.getGFXSampler(),i=this._model.subModels,n=k.SAMPLER_SPRITE,o=0;o<i.length;o++){var r=i[o].descriptorSet;r.bindTexture(n,e),r.bindSampler(n,t),r.update()}},i._attachToScene=function(){if(this.node.scene&&this._model){var e=this._getRenderScene();null!==this._model.scene&&this._detachFromScene(),e.addModel(this._model)}},i._detachFromScene=function(){this._model&&this._model.scene&&this._model.scene.removeModel(this._model)},O(t,[{key:"spriteFrame",get:function(){return this._spriteFrame},set:function(e){this._spriteFrame!==e&&(this._spriteFrame,this._spriteFrame=e,this._spriteFrame&&(this._spriteFrame.ensureMeshData(),this._spriteFrame.mesh.initialize()),this._updateModels(),this.enabledInHierarchy&&this._attachToScene())}},{key:"model",get:function(){return this._model}}]),t}(Q),D(ze.prototype,"spriteFrame",[Te],Object.getOwnPropertyDescriptor(ze.prototype,"spriteFrame"),ze.prototype),Ae=g(ze.prototype,"_spriteFrame",[y],(function(){return null})),Ie=g(ze.prototype,"_mode",[y],(function(){return Ne.SIMPLE})),Oe=g(ze.prototype,"_color",[y],(function(){return S.WHITE.clone()})),De=g(ze.prototype,"_flipX",[y],(function(){return!1})),Pe=g(ze.prototype,"_flipY",[y],(function(){return!1})),Me=g(ze.prototype,"_size",[y],(function(){return new s})),Fe=ze))||Fe)||Fe))}}}));
