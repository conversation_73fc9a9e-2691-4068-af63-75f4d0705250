System.register(["./gc-object-D18ulfCO.js","./index-Y4La_nfG.js","./global-exports-CLZKKIY2.js","./debug-view-BP17WHcy.js","./component-CsuvAQKv.js","./factory-BOc5khhM.js","./scene-ArUG4OfI.js"],(function(t){"use strict";var e,i,n,r,s,h;return{setters:[function(t){e=t._,i=t.a},function(t){n=t.c,r=t.a,s=t.s},null,null,function(t){h=t.A},null,null],execute:function(){t("l",4),t("g",4),t("i",256);var a,o,l,f,u,g,_,c,y,w,v,I,b=t("b",32),d=(t("a",33),t("T",8),t("e",32768)),p=t("f",1/128),k=t("m",1/512),S=(t("k",-32768*p),t("j",(65535-d)*p),t("n",0),t("o",1),t("p",2),t("q",3),t("r",16842753)),m=t("s",16842754),z=t("t",16842755),M=t("u",16842756),B=t("h",16842757),V=t("v",16842758),A=t("w",16842759),D=t("x",16842760),F=t("y",16843025),C=function(){function t(){this.length=0,this.buffer=new Uint8Array(2048),this._buffView=new DataView(this.buffer.buffer),this._seekPos=0}var e=t.prototype;return e.reserve=function(t){if(!(this.buffer.byteLength>t)){for(var e=this.buffer.byteLength;e<t;)e+=e;for(var i=new Uint8Array(e),n=0;n<this.length;++n)i[n]=this.buffer[n];this.buffer=i,this._buffView=new DataView(this.buffer.buffer)}},e.assign=function(t){this.buffer=t,this.length=t.length,this._seekPos=t.byteOffset,this._buffView=new DataView(t.buffer)},e.writeInt8=function(t){this.reserve(this.length+1),this._buffView.setInt8(this.length,t),this.length+=1},e.writeInt16=function(t){this.reserve(this.length+2),this._buffView.setInt16(this.length,t,!0),this.length+=2},e.writeInt32=function(t){this.reserve(this.length+4),this._buffView.setInt32(this.length,t,!0),this.length+=4},e.writeIntArray=function(t){this.reserve(this.length+4*t.length);for(var e=0;e<t.length;++e)this._buffView.setInt32(this.length+4*e,t[e],!0);this.length+=4*t.length},e.writeFloat=function(t){this.reserve(this.length+4),this._buffView.setFloat32(this.length,t,!0),this.length+=4},e.writeFloatArray=function(t){this.reserve(this.length+4*t.length);for(var e=0;e<t.length;++e)this._buffView.setFloat32(this.length+4*e,t[e],!0);this.length+=4*t.length},e.writeDouble=function(t){this.reserve(this.length+8),this._buffView.setFloat64(this.length,t,!0),this.length+=8},e.writeDoubleArray=function(t){this.reserve(this.length+8*t.length);for(var e=0;e<t.length;++e)this._buffView.setFloat64(this.length+8*e,t[e],!0);this.length+=8*t.length},e.writeString=function(t){this.reserve(this.length+t.length+4),this._buffView.setInt32(this.length,t.length,!0);for(var e=0;e<t.length;++e)this._buffView.setInt8(this.length+4+e,t.charCodeAt(e));this.length+=t.length+4},e.readInt8=function(){var t=this._buffView.getInt8(this._seekPos);return this._seekPos+=1,t},e.readInt16=function(){var t=this._buffView.getInt16(this._seekPos,!0);return this._seekPos+=2,t},e.readInt=function(){var t=this._buffView.getInt32(this._seekPos,!0);return this._seekPos+=4,t},e.readIntArray=function(t){for(var e=0;e<t.length;++e)t[e]=this._buffView.getInt32(this._seekPos+4*e,!0);return this._seekPos+=4*t.length,t},e.readFloat=function(){var t=this._buffView.getFloat32(this._seekPos,!0);return this._seekPos+=4,t},e.readFloatArray=function(t){for(var e=0;e<t.length;++e)t[e]=this._buffView.getFloat32(this._seekPos+4*e,!0);return this._seekPos+=4*t.length,t},e.readDouble=function(){var t=this._buffView.getFloat64(this._seekPos,!0);return this._seekPos+=8,t},e.readDoubleArray=function(t){for(var e=0;e<t.length;++e)t[e]=this._buffView.getFloat64(this._seekPos+4*e,!0);return this._seekPos+=8*t.length,t},e.readString=function(){for(var t=this.readInt(),e="",i=0;i<t;++i)e+=String.fromCharCode(this.readInt8());return e},t}(),P=(t("d",n("cc.TerrainLayerInfo")((o=function(){this.slot=l&&l(),this.tileSize=f&&f(),this.detailMap=u&&u(),this.normalMap=g&&g(),this.roughness=_&&_(),this.metallic=c&&c()},l=r(o.prototype,"slot",[s],(function(){return 0})),f=r(o.prototype,"tileSize",[s],(function(){return 1})),u=r(o.prototype,"detailMap",[s],(function(){return null})),g=r(o.prototype,"normalMap",[s],(function(){return null})),_=r(o.prototype,"roughness",[s],(function(){return 1})),c=r(o.prototype,"metallic",[s],(function(){return 0})),a=o))||a),t("z",n("cc.TerrainLayerBinaryInfo")(y=function(){this.slot=0,this.tileSize=1,this.roughness=1,this.metallic=0,this.detailMapId="",this.normalMapId=""})||y));t("c",n("cc.TerrainAsset")((v=function(t){function n(){var e;return(e=t.call(this)||this)._version=0,e._data=null,e._tileSize=1,e._blockCount=[1,1],e._weightMapSize=128,e._lightMapSize=128,e._heights=new Uint16Array,e._normals=new Float32Array,e._weights=new Uint8Array,e._layerBuffer=[-1,-1,-1,-1],e._layerBinaryInfos=[],e._layerInfos=I&&I(),e}e(n,t);var r=n.prototype;return r.getLayer=function(t,e,i){var n=4*(e*this.blockCount[0]+t)+i;return t<this.blockCount[0]&&e<this.blockCount[1]&&n<this._layerBuffer.length?this._layerBuffer[n]:-1},r.getHeight=function(t,e){var i=this._blockCount[0]*b+1;return(this._heights[e*i+t]-d)*p},r.getVertexCountI=function(){return this._blockCount.length<1?0:this._blockCount[0]*b+1},r.getVertexCountJ=function(){return this._blockCount.length<2?0:this._blockCount[1]*b+1},r._setNativeData=function(t){this._data=t},r._loadNativeData=function(t){if(!t||0===t.length)return!1;var e=new C;if(e.assign(t),this._version=e.readInt(),this._version===F)return!0;if(this._version!==S&&this._version!==m&&this._version!==z&&this._version!==M&&this._version!==B&&this._version!==V&&this._version!==A&&this._version!==D)return!1;this._version>=A?this.tileSize=e.readDouble():this.tileSize=e.readFloat(),this.tileSize=Math.floor(100*this.tileSize)/100,e.readIntArray(this._blockCount),this.weightMapSize=e.readInt16(),this.lightMapSize=e.readInt16();var i=e.readInt();this.heights=new Uint16Array(i);for(var n=0;n<this.heights.length;++n)this.heights[n]=e.readInt16();if(this._version<D)for(var r=0;r<this.heights.length;++r){var s=(this._heights[r]-d)*k,h=d+s/p;this.heights[r]=h}if(this._version>=V){var a=e.readInt();this.normals=new Float32Array(a);for(var o=0;o<this.normals.length;++o)this.normals[o]=e.readFloat()}var l=e.readInt();this.weights=new Uint8Array(l);for(var f=0;f<this.weights.length;++f)this.weights[f]=e.readInt8();if(this._version>=m){var u=e.readInt();this.layerBuffer=new Array(u);for(var g=0;g<this.layerBuffer.length;++g)this.layerBuffer[g]=e.readInt16()}if(this._version>=z){var _=e.readInt();this._layerBinaryInfos=new Array(_);for(var c=0;c<this._layerBinaryInfos.length;++c)this._layerBinaryInfos[c]=new P,this._layerBinaryInfos[c].slot=e.readInt(),this._version>=A?this._layerBinaryInfos[c].tileSize=e.readDouble():this._layerBinaryInfos[c].tileSize=e.readFloat(),this._layerBinaryInfos[c].detailMapId=e.readString(),this._version>=M&&(this._layerBinaryInfos[c].normalMapId=e.readString(),this._version>=A?(this._layerBinaryInfos[c].roughness=e.readDouble(),this._layerBinaryInfos[c].metallic=e.readDouble()):(this._layerBinaryInfos[c].roughness=e.readFloat(),this._layerBinaryInfos[c].metallic=e.readFloat()))}return!0},r._exportNativeData=function(){var t=new C;t.writeInt32(D),t.writeDouble(this.tileSize),t.writeIntArray(this._blockCount),t.writeInt16(this.weightMapSize),t.writeInt16(this.lightMapSize),t.writeInt32(this.heights.length);for(var e=0;e<this.heights.length;++e)t.writeInt16(this.heights[e]);t.writeInt32(this.normals.length);for(var i=0;i<this.normals.length;++i)t.writeFloat(this.normals[i]);t.writeInt32(this.weights.length);for(var n=0;n<this.weights.length;++n)t.writeInt8(this.weights[n]);t.writeInt32(this.layerBuffer.length);for(var r=0;r<this.layerBuffer.length;++r)t.writeInt16(this.layerBuffer[r]);var s=[];s.length=this.layerInfos.length;for(var h=0;h<s.length;++h){var a=this.layerInfos[h],o=new P;o.slot=h,o.tileSize=a.tileSize,o.detailMapId=a.detailMap?a.detailMap._uuid:"",o.normalMapId=a.normalMap?a.normalMap._uuid:"",o.metallic=a.metallic,o.roughness=a.roughness,s[h]=o}t.writeInt32(s.length);for(var l=0;l<s.length;++l)t.writeInt32(s[l].slot),t.writeDouble(s[l].tileSize),t.writeString(s[l].detailMapId),t.writeString(s[l].normalMapId),t.writeDouble(s[l].roughness),t.writeDouble(s[l].metallic);return t.buffer},r._exportDefaultNativeData=function(){var t=new C;return t.writeInt32(F),t.buffer},i(n,[{key:"_nativeAsset",get:function(){return this._data.buffer},set:function(t){this._data&&this._data.byteLength===t.byteLength?this._data.set(new Uint8Array(t)):this._data=new Uint8Array(t),this._loadNativeData(this._data)}},{key:"version",get:function(){return this._version}},{key:"tileSize",get:function(){return this._tileSize},set:function(t){this._tileSize=t}},{key:"blockCount",get:function(){return this._blockCount},set:function(t){this._blockCount=t}},{key:"lightMapSize",get:function(){return this._lightMapSize},set:function(t){this._lightMapSize=t}},{key:"weightMapSize",get:function(){return this._weightMapSize},set:function(t){this._weightMapSize=t}},{key:"heights",get:function(){return this._heights},set:function(t){this._heights=t}},{key:"normals",get:function(){return this._normals},set:function(t){this._normals=t}},{key:"weights",get:function(){return this._weights},set:function(t){this._weights=t}},{key:"layerBuffer",get:function(){return this._layerBuffer},set:function(t){this._layerBuffer=t}},{key:"layerInfos",get:function(){return this._layerInfos},set:function(t){this._layerInfos=t}},{key:"layerBinaryInfos",get:function(){return this._layerBinaryInfos}}]),n}(h),I=r(v.prototype,"_layerInfos",[s],(function(){return[]})),w=v))||w)}}}));
