System.register(["./gc-object-CKHc4SnS.js","./index-C5lmLqDW.js","./global-exports-CR3GRnjt.js","./component-BaGvu7EF.js","./prefab-DH0xadMc.js","./scene-7MDSMR3j.js","./pipeline-state-manager-Cdpe3is6.js","./node-event-DTNosVQv.js","./touch-DB0AR-Sc.js","./device-manager-BvjvoelW.js","./buffer-barrier-CuX_5NUR.js","./factory-D9_8ZCqM.js"],(function(t){"use strict";var e,n,s,o,i,r,a,u,c,l,_,p,f,d,k,h,w;return{setters:[function(t){e=t._,n=t.a,s=t.b,o=t.C},function(t){i=t.v,r=t.c,a=t.t,u=t.d,c=t.V,l=t.a,_=t.s},function(t){p=t.c},function(t){f=t.C},null,function(t){d=t.N,k=t.T,h=t.a},null,function(t){w=t.N},null,null,null,null],execute:function(){var y,N,b,g,m,S,v,E,T=i();!function(t){t[t.NONE=0]="NONE",t[t.STANDARD=1]="STANDARD",t[t.ROTATIONAL=2]="ROTATIONAL"}(E||(E={}));var j=t("UISkew",(y=r("cc.UISkew"),N=a(o),b=a(c),y(g=u((m=function(t){function s(){var e;return(e=t.call(this)||this)._skew=S&&S(),e._rotational=v&&v(),e._skewEnabled=!1,e}e(s,t);var o=s.prototype;return o.isSkewEnabled=function(){return this._skewEnabled},o.__preload=function(){this.node._uiProps._uiSkewComp=this},o.onEnable=function(){this._skewEnabled=!0,d._incSkewCompCount(),this._syncNative(!0),this._updateNodeTransformFlags()},o.onDisable=function(){this._skewEnabled=!1,d._decSkewCompCount(),this._syncNative(!1),this._updateNodeTransformFlags()},o.onDestroy=function(){this._skewEnabled=!1,this._syncNative(!1),this.node._uiProps._uiSkewComp=null,this._updateNodeTransformFlags()},o._syncNative=function(){},o.setSkew=function(t,e){var n=this._skew;"number"==typeof t?T.set(t,e):c.copy(T,t),c.equals(n,T)||(n.set(T),this._skewEnabled&&this._updateNodeTransformFlags())},o.getSkew=function(t){return t||(t=new c),t.set(this._skew)},o._updateNodeTransformFlags=function(){var t=this.node;t.invalidateChildren(k.SKEW),t._eventMask&h&&t.emit(w.TRANSFORM_CHANGED,k.SKEW)},n(s,[{key:"rotational",get:function(){return this._rotational},set:function(t){this._rotational=t,this._skewEnabled&&this._updateNodeTransformFlags()}},{key:"x",get:function(){return this._skew.x},set:function(t){this._skew.x=t,this._skewEnabled&&this._updateNodeTransformFlags()}},{key:"y",get:function(){return this._skew.y},set:function(t){this._skew.y=t,this._skewEnabled&&this._updateNodeTransformFlags()}},{key:"skew",get:function(){return this._skew},set:function(t){this.setSkew(t)}}]),s}(f),S=l(m.prototype,"_skew",[_],(function(){return i()})),v=l(m.prototype,"_rotational",[_],(function(){return!1})),s(m.prototype,"rotational",[N],Object.getOwnPropertyDescriptor(m.prototype,"rotational"),m.prototype),s(m.prototype,"skew",[b],Object.getOwnPropertyDescriptor(m.prototype,"skew"),m.prototype),g=m))||g)||g));p.UISkew=j}}}));
