System.register(["./index-DoSzW704.js","./base.js","./index-C5lmLqDW.js","./global-exports-CR3GRnjt.js","./gc-object-CKHc4SnS.js","./collision-matrix-B7MK4XMK.js","./deprecated-T2yM9nsJ.js","./director-DIlqD2Nd.js","./debug-view-CKetkq9d.js","./device-manager-BvjvoelW.js","./buffer-barrier-CuX_5NUR.js","./pipeline-state-manager-Cdpe3is6.js","./scene-7MDSMR3j.js","./component-BaGvu7EF.js","./node-event-DTNosVQv.js","./touch-DB0AR-Sc.js","./factory-D9_8ZCqM.js","./prefab-DH0xadMc.js","./deprecated-Ca3AjUwj.js","./util-D0MMXf4o.js","./mesh-C8knhDLk.js","./rendering-sub-mesh-CowWLfXC.js","./wasm-minigame-DoCiKH-Y.js","./zlib.min-CyXMsivM.js","./capsule-sT4rQfGi.js","./skeleton-d3ONjcrt.js","./terrain-asset-CHGiv9LN.js","./deprecated-D4QUWou_.js","./render-types-DTmbYHic.js","./pipeline-scene-data-B9tBPl1_.js","./deprecated-C8l6Kwy8.js","./camera-component-Df61RNZm.js","./model-renderer-BcRDUYby.js","./renderer-9hfAnqUF.js","./instantiate-Bj9mBEzA.js","./move-BmfpEZyZ.js"],(function(e){"use strict";var n,o,l,i,t,r,s,a,p,d,C,m,c,u,y;return{setters:[function(m){n=m.P,o=m.C,l=m.a,i=m.B,t=m.S,r=m.b,s=m.R,a=m.c,p=m.M,d=m.d,C=m.p,e({BoxCharacterController:m.o,BoxCollider:m.B,BoxColliderComponent:m.B,CapsuleCharacterController:m.n,CapsuleCollider:m.b,CapsuleColliderComponent:m.b,CharacterController:m.e,Collider:m.C,ColliderComponent:m.C,ConeCollider:m.i,ConfigurableConstraint:m.l,ConstantForce:m.f,Constraint:m.a,CylinderCollider:m.d,CylinderColliderComponent:m.d,FixedConstraint:m.F,HingeConstraint:m.H,MeshCollider:m.M,MeshColliderComponent:m.M,PhysicMaterial:m.c,PhysicsLineStripCastResult:m.h,PhysicsMaterial:m.c,PhysicsRayResult:m.g,PhysicsSystem:m.P,PlaneCollider:m.k,PointToPointConstraint:m.m,RigidBody:m.R,RigidBodyComponent:m.R,SimplexCollider:m.j,SphereCollider:m.S,SphereColliderComponent:m.S,TerrainCollider:m.T,physics:m.p})},null,function(e){m=e.r,c=e.h},function(e){u=e.c},function(e){y=e.k},function(n){e({EAxisDirection:n.E,EColliderType:n.b,EPhysicsDrawFlags:n.c,ERigidBodyType:n.a})},null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){m(n,"PhysicsSystem",[{name:"ins",newName:"instance"},{name:"PHYSICS_AMMO",newName:"PHYSICS_BULLET"}]),m(n.prototype,"PhysicsSystem.prototype",[{name:"deltaTime",newName:"fixedTimeStep"},{name:"maxSubStep",newName:"maxSubSteps"}]),c(n.prototype,"PhysicsSystem.prototype",[{name:"useFixedTime"},{name:"useCollisionMatrix"},{name:"updateCollisionMatrix"},{name:"resetCollisionMatrix"},{name:"isCollisionGroup"},{name:"setCollisionGroup"}]),m(o.prototype,"Collider.prototype",[{name:"attachedRigidbody",newName:"attachedRigidBody"},{name:"TYPE",newName:"type"}]),m(o,"Collider",[{name:"EColliderType",newName:"Type"},{name:"EAxisDirection",newName:"Axis"}]),m(l,"Constraint",[{name:"EConstraintType",newName:"Type"}]),m(i.prototype,"BoxCollider.prototype",[{name:"boxShape",newName:"shape"}]),m(t.prototype,"SphereCollider.prototype",[{name:"sphereShape",newName:"shape"}]),m(r.prototype,"CapsuleCollider.prototype",[{name:"capsuleShape",newName:"shape"}]),m(s.prototype,"RigidBody.prototype",[{name:"rigidBody",newName:"body"}]),m(s,"RigidBody",[{name:"ERigidBodyType",newName:"Type"}]),c(s.prototype,"RigidBody.prototype",[{name:"fixedRotation"}]),u.RigidBodyComponent=s,y(s,"cc.RigidBodyComponent"),u.ColliderComponent=o,y(o,"cc.ColliderComponent"),u.BoxColliderComponent=i,y(i,"cc.BoxColliderComponent"),u.SphereColliderComponent=t,y(t,"cc.SphereColliderComponent"),y(r,"cc.CapsuleColliderComponent"),y(p,"cc.MeshColliderComponent"),y(d,"cc.CylinderColliderComponent"),u.PhysicMaterial=a,y(a,"cc.PhysicMaterial"),u.physics=C}}}));
