System.register(["./gc-object-D18ulfCO.js","./global-exports-CLZKKIY2.js","./scene-ArUG4OfI.js","./pipeline-state-manager-DQyhxoC_.js","./device-manager-B8lyUDPi.js","./buffer-barrier-q_79u36H.js","./index-Y4La_nfG.js","./debug-view-BP17WHcy.js","./component-CsuvAQKv.js","./factory-BOc5khhM.js"],(function(e){"use strict";var t,i,n,s,a,r,o,h,c,u,l,d,_,f,g,p,m,L,v,S,D,y,b,R,A,w,E,B,I,T,C,P,O,N,M,k,H,F,x,U,z,G,W,j,V,X,K,Y,Q,q,Z,J,$,ee,te,ie,ne,se,ae,re,oe,he,ce,ue,le,de,_e,fe,ge,pe,me,Le,ve,Se,De,ye,be,Re,Ae,we,Ee,Be,Ie,<PERSON>,Ce,Pe;return{setters:[function(e){t=e.a,i=e.h,n=e._,s=e.o,a=e.W,r=e.g,o=e.z},function(e){h=e.c},function(e){c=e.B,u=e.s,l=e.e,d=e.d,_=e.A,f=e.p,g=e.C,p=e.l,m=e.S,L=e.T,v=e.i,S=e.a9,D=e.a7},function(e){y=e.R,b=e.s,R=e.t,A=e.u,w=e.L,E=e.c,B=e.v,I=e.w,T=e.x,C=e.y,P=e.d},function(e){O=e.d},function(e){N=e.a7,M=e.c,k=e.A,H=e.u,F=e.ay,x=e.ad,U=e.v,z=e.ae,G=e.B,W=e.b,j=e.M,V=e.bc,X=e.T,K=e.d,Y=e.e,Q=e.p,q=e.F,Z=e.aK},function(e){J=e.M,$=e.L,ee=e.e,te=e.F,ie=e.x,ne=e.q,se=e.b,ae=e.Q,re=e.b1,oe=e.I,he=e.r,ce=e.h},function(e){ue=e.o,le=e.l,de=e.L,_e=e.t,fe=e.C},function(e){ge=e.e,pe=e.f,me=e.t,Le=e.a,ve=e.B,Se=e.A,De=e.h,ye=e.i,be=e.k,Re=e.d,Ae=e.b},function(e){we=e.c,Ee=e.e,Be=e.d,Ie=e.r,Te=e.p,Ce=e.f,Pe=e.s}],execute:function(){var Oe,Ne=new F(null),Me=e("b",function(){function e(){this._device=null,this._passes=null,this._shaders=null,this._subMesh=null,this._patches=null,this._priority=y.DEFAULT,this._inputAssembler=null,this._descriptorSet=null,this._worldBoundDescriptorSet=null,this._instancedAttributeBlock={buffer:null,views:[],attributes:[]},this._instancedWorldMatrixIndex=-1,this._instancedSHIndex=-1,this._useReflectionProbeType=0}var n=e.prototype;return n.initialize=function(e,t,i){void 0===i&&(i=null),h.director.root,this._device=O.gfxDevice,Ne.layout=t[0].localSetLayout,this._inputAssembler=this._device.createInputAssembler(e.iaInfo),this._descriptorSet=this._device.createDescriptorSet(Ne);var n=h.director.root.pipeline.pipelineSceneData.getOcclusionQueryPass();if(n){var s=new F(null);s.layout=n.localSetLayout,this._worldBoundDescriptorSet=this._device.createDescriptorSet(s)}this._subMesh=e,this._patches=i?i.sort():null,this._passes=t,this._flushPassInfo(),this.priority=y.DEFAULT},n.destroy=function(){this._descriptorSet.destroy(),this._descriptorSet=null,this._inputAssembler.destroy(),this._inputAssembler=null,this._worldBoundDescriptorSet&&this._worldBoundDescriptorSet.destroy(),this._worldBoundDescriptorSet=null,this.priority=y.DEFAULT,this._patches=null,this._subMesh=null,this._passes=null,this._shaders=null},n.update=function(){for(var e=0;e<this._passes.length;++e)this._passes[e].update();this._descriptorSet.update(),this._worldBoundDescriptorSet&&this._worldBoundDescriptorSet.update()},n._updatePasses=function(){var e=this._passes;e&&(e.forEach((function(e){e.beginChangeStatesSilently(),e.tryCompile(),e.endChangeStatesSilently()})),this._flushPassInfo())},n.onPipelineStateChanged=function(){this._updatePasses()},n.onMacroPatchesStateChanged=function(e){(e||this._patches)&&(e&&(e=e.sort(),this._patches&&e.length===this._patches.length&&JSON.stringify(e)===JSON.stringify(this._patches))||(this._patches=e,this._updatePasses()))},n.onGeometryChanged=function(){if(this._subMesh){var e=this._subMesh.drawInfo;if(this._inputAssembler&&e){var t=this._inputAssembler.drawInfo;Object.keys(e).forEach((function(i){t[i]=e[i]})),this._inputAssembler.drawInfo=t}}},n.getInstancedAttributeIndex=function(e){for(var t=this.instancedAttributeBlock.attributes,i=0;i<t.length;i++)if(t[i].name===e)return i;return-1},n.updateInstancedWorldMatrix=function(e,t){var i=this.instancedAttributeBlock.views,n=i[t],s=i[t+1],a=i[t+2];n[0]=e.m00,n[1]=e.m01,n[2]=e.m02,n[3]=e.m12,s[0]=e.m04,s[1]=e.m05,s[2]=e.m06,s[3]=e.m13,a[0]=e.m08,a[1]=e.m09,a[2]=e.m10,a[3]=e.m14},n.updateInstancedSH=function(e,t){for(var i=this.instancedAttributeBlock.views,n=(b.SH_QUADRATIC_R_OFFSET-b.SH_LINEAR_CONST_R_OFFSET)/4,s=0,a=t;a<t+n;a++)for(var r=0;r<4;r++)i[a][r]=e[s++]},n.UpdateInstancedAttributes=function(e){this.instancedWorldMatrixIndex=-1,this.instancedSHIndex=-1;var t=this.passes[0];if(t.device.hasFeature(N.INSTANCED_ARRAYS)){for(var i=0,n=0;n<e.length;n++){var s=e[n];s.isInstanced&&(i+=M[s.format].size)}var a=this.instancedAttributeBlock;a.buffer=new Uint8Array(i),a.views.length=a.attributes.length=0;for(var r=0,o=0;o<e.length;o++){var h=e[o];if(h.isInstanced){var u=new k;u.format=h.format,u.name=h.name,u.isNormalized=h.isNormalized,u.location=h.location,a.attributes.push(u);var l=M[h.format],d=new(H(l))(a.buffer.buffer,r,l.count);a.views.push(d),r+=l.size}}t.batchingScheme===c.INSTANCING&&t.getInstancedBuffer().destroy(),this.instancedWorldMatrixIndex=this.getInstancedAttributeIndex(R),this.instancedSHIndex=this.getInstancedAttributeIndex(A)}},n._flushPassInfo=function(){var e=this._passes;if(e){this._shaders||(this._shaders=[]),this._shaders.length=e.length;for(var t=0,i=e.length;t<i;t++)this._shaders[t]=e[t].getShaderVariant(this.patches)}},t(e,[{key:"passes",get:function(){return this._passes},set:function(e){e.length>8?i(12004,8):(this._passes=e,this._flushPassInfo(),this._descriptorSet&&(this._descriptorSet.destroy(),Ne.layout=e[0].localSetLayout,this._descriptorSet=this._device.createDescriptorSet(Ne)))}},{key:"shaders",get:function(){return this._shaders}},{key:"subMesh",get:function(){return this._subMesh},set:function(e){this._inputAssembler.destroy(),this._inputAssembler=this._device.createInputAssembler(e.iaInfo),this._subMesh=e}},{key:"priority",get:function(){return this._priority},set:function(e){this._priority=e}},{key:"inputAssembler",get:function(){return this._inputAssembler}},{key:"descriptorSet",get:function(){return this._descriptorSet}},{key:"worldBoundDescriptorSet",get:function(){return this._worldBoundDescriptorSet}},{key:"patches",get:function(){return this._patches}},{key:"instancedAttributeBlock",get:function(){return this._instancedAttributeBlock}},{key:"instancedWorldMatrixIndex",get:function(){return this._instancedWorldMatrixIndex},set:function(e){this._instancedWorldMatrixIndex=e}},{key:"instancedSHIndex",get:function(){return this._instancedSHIndex},set:function(e){this._instancedSHIndex=e}},{key:"useReflectionProbeType",get:function(){return this._useReflectionProbeType},set:function(e){this._useReflectionProbeType=e}}]),e}());e("R",Oe),function(e){e[e.NONE=0]="NONE",e[e.BAKED_CUBEMAP=1]="BAKED_CUBEMAP",e[e.PLANAR_REFLECTION=2]="PLANAR_REFLECTION",e[e.BLEND_PROBES=3]="BLEND_PROBES",e[e.BLEND_PROBES_AND_SKYBOX=4]="BLEND_PROBES_AND_SKYBOX"}(Oe||e("R",Oe={}));var ke,He=new J,Fe=[{name:"CC_RECEIVE_SHADOW",value:!0}],xe=[{name:"CC_USE_LIGHTMAP",value:1}],Ue=[{name:"CC_USE_LIGHTMAP",value:2}],ze=[{name:"CC_LIGHT_MAP_VERSION",value:2}],Ge=[{name:"CC_USE_LIGHT_PROBE",value:!0}];e("d",ke),function(e){e[e.DEFAULT=0]="DEFAULT",e[e.SKINNING=1]="SKINNING",e[e.BAKED_SKINNING=2]="BAKED_SKINNING",e[e.BATCH_2D=3]="BATCH_2D",e[e.PARTICLE_BATCH=4]="PARTICLE_BATCH",e[e.LINE=5]="LINE"}(ke||e("d",ke={}));var We,je=new x(U.LINEAR,U.LINEAR,U.NONE,z.CLAMP,z.CLAMP,z.CLAMP),Ve=new x(U.LINEAR,U.LINEAR,U.LINEAR,z.CLAMP,z.CLAMP,z.CLAMP),Xe=(e("M",function(){function e(){this.type=ke.DEFAULT,this.scene=null,this.isDynamicBatching=!1,this._worldBounds=null,this._modelBounds=null,this._subModels=[],this._node=null,this._transform=null,this._inited=!1,this._descriptorSetCount=1,this._updateStamp=-1,this._localDataUpdated=!0,this._localData=new Float32Array(E.COUNT),this._localBuffer=null,this._localSHData=null,this._localSHBuffer=null,this._lightmap=null,this._lightmapUVParam=ie(),this._tetrahedronIndex=-1,this._lastWorldBoundCenter=ne(1/0,1/0,1/0),this._useLightProbe=!1,this._worldBoundBuffer=null,this._receiveShadow=!1,this._castShadow=!1,this._receiveDirLight=!0,this._shadowBias=0,this._shadowNormalBias=0,this._reflectionProbeId=-1,this._reflectionProbeBlendId=-1,this._reflectionProbeBlendWeight=0,this._enabled=!0,this._visFlags=w.Enum.NONE,this._priority=0,this._bakeToReflectionProbe=!0,this._reflectionProbeType=Oe.NONE,this._device=O.gfxDevice}var i=e.prototype;return i.initialize=function(){this._inited||(this._receiveShadow=!0,this.castShadow=!1,this.enabled=!0,this.visFlags=w.Enum.NONE,this._inited=!0,this._bakeToReflectionProbe=!0,this._reflectionProbeType=Oe.NONE)},i.destroy=function(){for(var e=this._subModels,t=0;t<e.length;t++)this._subModels[t].destroy();this._localBuffer&&(this._localBuffer.destroy(),this._localBuffer=null),this._localSHBuffer&&(this._localSHBuffer.destroy(),this._localSHBuffer=null),this._worldBoundBuffer&&(this._worldBoundBuffer.destroy(),this._worldBoundBuffer=null),this._worldBounds=null,this._modelBounds=null,this._subModels.length=0,this._inited=!1,this._localDataUpdated=!0,this._transform=null,this._node=null,this.isDynamicBatching=!1},i.attachToScene=function(e){this.scene=e,this._localDataUpdated=!0},i.detachFromScene=function(){this.scene=null},i.updateTransform=function(){var e=this.transform;if(e.hasChangedFlags||e.isTransformDirty()){e.updateWorldTransform(),this._localDataUpdated=!0;var t=this._worldBounds;this._modelBounds&&t&&this._modelBounds.transform(e._mat,e._pos,e._rot,e._scale,t)}},i.updateWorldBound=function(){var e=this.transform;if(null!==e){e.updateWorldTransform(),this._localDataUpdated=!0;var t=this._worldBounds;this._modelBounds&&t&&this._modelBounds.transform(e._mat,e._pos,e._rot,e._scale,t)}},i.updateUBOs=function(e){for(var t=this._subModels,i=0;i<t.length;i++)t[i].update();this._updateStamp=e,this.updateSHUBOs();var n=this.node.scene.globals.shadows,s=n.enabled&&n.type===u.Planar;if(this._localDataUpdated){this._localDataUpdated=!1;for(var a=this.transform._mat,r=!1,o=0;o<t.length;o++){var h=t[o],c=h.instancedWorldMatrixIndex;c>=0?h.updateInstancedWorldMatrix(a,c):r=!0}(r||s)&&this._localBuffer&&(J.toArray(this._localData,a,E.MAT_WORLD_OFFSET),J.invert(He,a),J.transpose(He,He),J.toArray(this._localData,He,E.MAT_WORLD_IT_OFFSET),this._localBuffer.update(this._localData))}},i.invalidateLocalData=function(){this._localDataUpdated=!0},i.showTetrahedron=function(){return this.isLightProbeAvailable()},i.isLightProbeAvailable=function(){if(!this._useLightProbe)return!1;var e=l().lightProbes;return!(!e||e.empty()||!this._worldBounds)},i.updateSHBuffer=function(){if(this._localSHData){for(var e=this._subModels,t=!1,i=0;i<e.length;i++){var n=e[i],s=n.instancedSHIndex;s>=0?n.updateInstancedSH(this._localSHData,s):t=!0}t&&this._localSHBuffer&&this._localSHBuffer.update(this._localSHData)}},i.clearSHUBOs=function(){if(this._localSHData){for(var e=0;e<b.COUNT;e++)this._localSHData[e]=0;this.updateSHBuffer()}},i.updateSHUBOs=function(){if(this.isLightProbeAvailable()){var e=this._worldBounds.center;if(!e.equals(this._lastWorldBoundCenter,$)){var t=[],i=new ee,n=h.director.root.pipeline.pipelineSceneData.lightProbes;if(this._lastWorldBoundCenter.set(e),this._tetrahedronIndex=n.data.getInterpolationWeights(e,this._tetrahedronIndex,i),n.data.getInterpolationSHCoefficients(this._tetrahedronIndex,i,t)&&this._localSHData){var s=h.internal.SH;s.reduceRinging(t,n.reduceRinging),s.updateUBOData(this._localSHData,b.SH_LINEAR_CONST_R_OFFSET,t),this.updateSHBuffer()}}}},i.createBoundingShape=function(e,t){e&&t&&(this._modelBounds||(this._modelBounds=te.create()),this._worldBounds||(this._worldBounds=te.create()),te.fromPoints(this._modelBounds,e,t),this._worldBounds.copy(this._modelBounds))},i._createSubModel=function(){return new Me},i.initSubModel=function(e,t,i){this.initialize(),null==this._subModels[e]?this._subModels[e]=this._createSubModel():this._subModels[e].destroy(),this._subModels[e].initialize(t,i.passes,this.getMacroPatches(e)),this._updateAttributesAndBinding(e)},i.setSubModelMesh=function(e,t){this._subModels[e]&&(this._subModels[e].subMesh=t)},i.setSubModelMaterial=function(e,t){this._subModels[e]&&(this._subModels[e].passes=t.passes,this._updateAttributesAndBinding(e))},i.onGlobalPipelineStateChanged=function(){for(var e=this._subModels,t=0;t<e.length;t++)e[t].onPipelineStateChanged()},i.onMacroPatchesStateChanged=function(){for(var e=this._subModels,t=0;t<e.length;t++)e[t].onMacroPatchesStateChanged(this.getMacroPatches(t))},i.onGeometryChanged=function(){for(var e=this._subModels,t=0;t<e.length;t++)e[t].onGeometryChanged()},i.initLightingmap=function(e,t){this._lightmap=e,this._lightmapUVParam=t},i.updateLightingmap=function(e,t){ee.toArray(this._localData,t,E.LIGHTINGMAP_UVPARAM),this._localDataUpdated=!0,this._lightmap=e,this._lightmapUVParam=t,this.onMacroPatchesStateChanged(),e||(e=d.get("empty-texture"));var i=e.getGFXTexture();if(i)for(var n=this._device.getSampler(e.mipmaps.length>1?Ve:je),s=this._subModels,a=0;a<s.length;a++){var r=s[a].descriptorSet;r.bindTexture(B,i),r.bindSampler(B,n),r.update()}},i.updateReflectionProbeCubemap=function(e){this._localDataUpdated=!0,this.onMacroPatchesStateChanged(),e||(e=d.get("default-cube-texture"));var t=e.getGFXTexture();if(t)for(var i=this._device.getSampler(e.getSamplerInfo()),n=this._subModels,s=0;s<n.length;s++){var a=n[s].descriptorSet;a&&(a.bindSampler(I,i),a.bindTexture(I,t),a.update())}},i.updateReflectionProbeBlendCubemap=function(){},i.updateReflectionProbePlanarMap=function(e){this._localDataUpdated=!0,this.onMacroPatchesStateChanged();var t=this._device.getSampler(new x(U.LINEAR,U.LINEAR,U.NONE,z.CLAMP,z.CLAMP,z.CLAMP));if(e||(e=d.get("empty-texture").getGFXTexture()),e)for(var i=this._subModels,n=0;n<i.length;n++){var s=i[n].descriptorSet;s&&(s.bindTexture(T,e),s.bindSampler(T,t),s.update())}},i.updateReflectionProbeDataMap=function(e){this._localDataUpdated=!0,this.onMacroPatchesStateChanged(),e||(e=d.get("empty-texture"));var t=e.getGFXTexture();if(t)for(var i=this._subModels,n=0;n<i.length;n++){var s=i[n].descriptorSet;s&&(s.bindTexture(C,t),s.bindSampler(C,e.getGFXSampler()),s.update())}},i.updateLocalShadowBias=function(){var e=this._localData;e[E.LOCAL_SHADOW_BIAS+0]=this._shadowBias,e[E.LOCAL_SHADOW_BIAS+1]=this._shadowNormalBias,this._localDataUpdated=!0},i.updateReflectionProbeId=function(){var e=this._localData;e[E.LOCAL_SHADOW_BIAS+2]=this._reflectionProbeId,e[E.LOCAL_SHADOW_BIAS+3]=this._reflectionProbeBlendId;var t=null,i=null;if(h.internal.reflectionProbeManager&&(t=h.internal.reflectionProbeManager.getProbeById(this._reflectionProbeId),i=h.internal.reflectionProbeManager.getProbeById(this._reflectionProbeBlendId)),t){if(t.probeType===ue.PLANAR)e[E.REFLECTION_PROBE_DATA1]=t.node.up.x,e[E.REFLECTION_PROBE_DATA1+1]=t.node.up.y,e[E.REFLECTION_PROBE_DATA1+2]=t.node.up.z,e[E.REFLECTION_PROBE_DATA1+3]=1,e[E.REFLECTION_PROBE_DATA2]=1,e[E.REFLECTION_PROBE_DATA2+1]=0,e[E.REFLECTION_PROBE_DATA2+2]=0,e[E.REFLECTION_PROBE_DATA2+3]=1;else{e[E.REFLECTION_PROBE_DATA1]=t.node.worldPosition.x,e[E.REFLECTION_PROBE_DATA1+1]=t.node.worldPosition.y,e[E.REFLECTION_PROBE_DATA1+2]=t.node.worldPosition.z,e[E.REFLECTION_PROBE_DATA1+3]=0,e[E.REFLECTION_PROBE_DATA2]=t.size.x,e[E.REFLECTION_PROBE_DATA2+1]=t.size.y,e[E.REFLECTION_PROBE_DATA2+2]=t.size.z;var n=t.isRGBE()?1e3:0;e[E.REFLECTION_PROBE_DATA2+3]=t.cubemap?t.cubemap.mipmapLevel+n:1+n}if(this._reflectionProbeType===Oe.BLEND_PROBES||this._reflectionProbeType===Oe.BLEND_PROBES_AND_SKYBOX)if(i){e[E.REFLECTION_PROBE_BLEND_DATA1]=i.node.worldPosition.x,e[E.REFLECTION_PROBE_BLEND_DATA1+1]=i.node.worldPosition.y,e[E.REFLECTION_PROBE_BLEND_DATA1+2]=i.node.worldPosition.z,e[E.REFLECTION_PROBE_BLEND_DATA1+3]=this.reflectionProbeBlendWeight,e[E.REFLECTION_PROBE_BLEND_DATA2]=i.size.x,e[E.REFLECTION_PROBE_BLEND_DATA2+1]=i.size.y,e[E.REFLECTION_PROBE_BLEND_DATA2+2]=i.size.z;var s=i.isRGBE()?1e3:0;e[E.REFLECTION_PROBE_BLEND_DATA2+3]=i.cubemap?i.cubemap.mipmapLevel+s:1+s}else this._reflectionProbeType===Oe.BLEND_PROBES_AND_SKYBOX&&(e[E.REFLECTION_PROBE_BLEND_DATA1+3]=this.reflectionProbeBlendWeight)}this._localDataUpdated=!0},i.getMacroPatches=function(){var e=this.receiveShadow?Fe:null;if(null!=this._lightmap&&this.node&&this.node.scene){var t=this.node.scene.globals;if(!t.disableLightmap){var i=t.bakedWithStationaryMainLight?Ue:xe;e=e?e.concat(i):i,t.bakedWithHighpLightmap&&(e=e.concat(ze))}}this._useLightProbe&&(e=e?e.concat(Ge):Ge);var n=[{name:"CC_USE_REFLECTION_PROBE",value:this._reflectionProbeType}];e=e?e.concat(n):n;var s=[{name:"CC_DISABLE_DIRECTIONAL_LIGHT",value:!this._receiveDirLight}];return e?e.concat(s):s},i._updateAttributesAndBinding=function(e){var t=this._subModels[e];if(t){this._initLocalDescriptors(e),this._updateLocalDescriptors(e,t.descriptorSet),this._initLocalSHDescriptors(e),this._updateLocalSHDescriptors(e,t.descriptorSet),this._initWorldBoundDescriptors(e),t.worldBoundDescriptorSet&&this._updateWorldBoundDescriptors(e,t.worldBoundDescriptorSet);var i=[],n=new Set;t.passes.forEach((function(e){e.getShaderVariant(t.patches).attributes.forEach((function(e){n.has(e.name)||(i.push(e),n.add(e.name))}))})),this._updateInstancedAttributes(i,t)}},i._updateInstancedAttributes=function(e,t){t.UpdateInstancedAttributes(e),this._localDataUpdated=!0},i._initLocalDescriptors=function(){this._localBuffer||(this._localBuffer=this._device.createBuffer(new G(W.UNIFORM|W.TRANSFER_DST,j.DEVICE,E.SIZE,E.SIZE)))},i._initLocalSHDescriptors=function(){this._useLightProbe&&(this._localSHData||(this._localSHData=new Float32Array(b.COUNT)),this._localSHBuffer||(this._localSHBuffer=this._device.createBuffer(new G(W.UNIFORM|W.TRANSFER_DST,j.DEVICE,b.SIZE,b.SIZE))))},i._initWorldBoundDescriptors=function(){this._worldBoundBuffer||(this._worldBoundBuffer=this._device.createBuffer(new G(W.UNIFORM|W.TRANSFER_DST,j.DEVICE,P.SIZE,P.SIZE)))},i._updateLocalDescriptors=function(e,t){this._localBuffer&&t.bindBuffer(E.BINDING,this._localBuffer)},i._updateLocalSHDescriptors=function(e,t){this._localSHBuffer&&t.bindBuffer(b.BINDING,this._localSHBuffer)},i._updateWorldBoundDescriptors=function(e,t){this._worldBoundBuffer&&t.bindBuffer(P.BINDING,this._worldBoundBuffer)},t(e,[{key:"subModels",get:function(){return this._subModels}},{key:"inited",get:function(){return this._inited}},{key:"worldBounds",get:function(){return this._worldBounds}},{key:"modelBounds",get:function(){return this._modelBounds}},{key:"localBuffer",get:function(){return this._localBuffer}},{key:"localSHBuffer",get:function(){return this._localSHBuffer}},{key:"worldBoundBuffer",get:function(){return this._worldBoundBuffer}},{key:"updateStamp",get:function(){return this._updateStamp}},{key:"useLightProbe",get:function(){return this._useLightProbe},set:function(e){this._useLightProbe=e,this.onMacroPatchesStateChanged()}},{key:"tetrahedronIndex",get:function(){return this._tetrahedronIndex},set:function(e){this._tetrahedronIndex=e}},{key:"shadowBias",get:function(){return this._shadowBias},set:function(e){this._shadowBias=e}},{key:"shadowNormalBias",get:function(){return this._shadowNormalBias},set:function(e){this._shadowNormalBias=e}},{key:"receiveShadow",get:function(){return this._receiveShadow},set:function(e){this._receiveShadow=e,this.onMacroPatchesStateChanged()}},{key:"castShadow",get:function(){return this._castShadow},set:function(e){this._castShadow=e}},{key:"receiveDirLight",get:function(){return this._receiveDirLight},set:function(e){this._receiveDirLight=e,this.onMacroPatchesStateChanged()}},{key:"node",get:function(){return this._node},set:function(e){this._node=e}},{key:"transform",get:function(){return this._transform},set:function(e){this._transform=e}},{key:"visFlags",get:function(){return this._visFlags},set:function(e){this._visFlags=e}},{key:"enabled",get:function(){return this._enabled},set:function(e){this._enabled=e}},{key:"priority",get:function(){return this._priority},set:function(e){this._priority=e}},{key:"bakeToReflectionProbe",get:function(){return this._bakeToReflectionProbe},set:function(e){this._bakeToReflectionProbe=e}},{key:"reflectionProbeType",get:function(){return this._reflectionProbeType},set:function(e){this._reflectionProbeType=e;for(var t=this._subModels,i=0;i<t.length;i++)t[i].useReflectionProbeType=e;this.onMacroPatchesStateChanged()}},{key:"reflectionProbeId",get:function(){return this._reflectionProbeId},set:function(e){this._reflectionProbeId=e}},{key:"reflectionProbeBlendId",get:function(){return this._reflectionProbeBlendId},set:function(e){this._reflectionProbeBlendId=e}},{key:"reflectionProbeBlendWeight",get:function(){return this._reflectionProbeBlendWeight},set:function(e){this._reflectionProbeBlendWeight=e}}]),e}()),new se(0,0,-1)),Ke=new se,Ye=(e("D",function(e){function i(){var t;return(t=e.call(this)||this)._dir=new se(1,-1,-1),t._illuminanceHDR=_.SUN_ILLUM,t._illuminanceLDR=1,t._shadowEnabled=!1,t._shadowPcf=f.HARD,t._shadowBias=1e-5,t._shadowNormalBias=0,t._shadowSaturation=1,t._shadowDistance=50,t._shadowInvisibleOcclusionRange=200,t._csmLevel=g.LEVEL_4,t._csmNeedUpdate=!1,t._csmLayerLambda=.75,t._csmOptimizationMode=p.DisableRotationFix,t._csmLayersTransition=!1,t._csmTransitionRange=.05,t._shadowFixedArea=!1,t._shadowNear=.1,t._shadowFar=10,t._shadowOrthoSize=5,t._type=de.DIRECTIONAL,t}n(i,e);var s=i.prototype;return s.initialize=function(){e.prototype.initialize.call(this),this.illuminance=_.SUN_ILLUM,this.direction=new se(1,-1,-1)},s.update=function(){this._node&&this._node.hasChangedFlags&&(this.direction=se.transformQuat(Ke,Xe,this._node.worldRotation))},s.activate=function(){var e=h.director.root,t=e.pipeline;this._shadowEnabled?(this._shadowFixedArea||!t.pipelineSceneData.csmSupported?t.macros.CC_DIR_LIGHT_SHADOW_TYPE=1:this.csmLevel>1&&t.pipelineSceneData.csmSupported?(t.macros.CC_DIR_LIGHT_SHADOW_TYPE=2,t.macros.CC_CASCADED_LAYERS_TRANSITION=this._csmLayersTransition):t.macros.CC_DIR_LIGHT_SHADOW_TYPE=1,t.macros.CC_DIR_SHADOW_PCF_TYPE=this._shadowPcf):t.macros.CC_DIR_LIGHT_SHADOW_TYPE=0,e.onGlobalPipelineStateChanged()},t(i,[{key:"direction",get:function(){return this._dir},set:function(e){se.normalize(this._dir,e)}},{key:"illuminance",get:function(){return l().isHDR?this._illuminanceHDR:this._illuminanceLDR},set:function(e){l().isHDR?this.illuminanceHDR=e:this.illuminanceLDR=e}},{key:"illuminanceHDR",get:function(){return this._illuminanceHDR},set:function(e){this._illuminanceHDR=e}},{key:"illuminanceLDR",get:function(){return this._illuminanceLDR},set:function(e){this._illuminanceLDR=e}},{key:"shadowEnabled",get:function(){return this._shadowEnabled},set:function(e){this._shadowEnabled=e,this.activate()}},{key:"shadowPcf",get:function(){return this._shadowPcf},set:function(e){this._shadowPcf=e,this.activate()}},{key:"shadowBias",get:function(){return this._shadowBias},set:function(e){this._shadowBias=e}},{key:"shadowNormalBias",get:function(){return this._shadowNormalBias},set:function(e){this._shadowNormalBias=e}},{key:"shadowSaturation",get:function(){return this._shadowSaturation},set:function(e){this._shadowSaturation=e}},{key:"shadowDistance",get:function(){return this._shadowDistance},set:function(e){this._shadowDistance=Math.min(e,m.MAX_FAR)}},{key:"shadowInvisibleOcclusionRange",get:function(){return this._shadowInvisibleOcclusionRange},set:function(e){this._shadowInvisibleOcclusionRange=Math.min(e,m.MAX_FAR)}},{key:"csmLevel",get:function(){return this._csmLevel},set:function(e){this._csmLevel=e,this.activate()}},{key:"csmNeedUpdate",get:function(){return this._csmNeedUpdate},set:function(e){this._csmNeedUpdate=e}},{key:"csmLayerLambda",get:function(){return this._csmLayerLambda},set:function(e){this._csmLayerLambda=e}},{key:"csmOptimizationMode",get:function(){return this._csmOptimizationMode},set:function(e){this._csmOptimizationMode=e}},{key:"shadowFixedArea",get:function(){return this._shadowFixedArea},set:function(e){this._shadowFixedArea=e,this.activate()}},{key:"shadowNear",get:function(){return this._shadowNear},set:function(e){this._shadowNear=e}},{key:"shadowFar",get:function(){return this._shadowFar},set:function(e){this._shadowFar=Math.min(e,m.MAX_FAR)}},{key:"shadowOrthoSize",get:function(){return this._shadowOrthoSize},set:function(e){this._shadowOrthoSize=e}},{key:"csmLayersTransition",get:function(){return this._csmLayersTransition},set:function(e){this._csmLayersTransition=e,this.activate()}},{key:"csmTransitionRange",get:function(){return this._csmTransitionRange},set:function(e){this._csmTransitionRange=e}}]),i}(le)),e("g",function(e){function i(){var t;return(t=e.call(this)||this)._needUpdate=!1,t._size=.15,t._range=1,t._luminanceHDR=0,t._luminanceLDR=0,t._pos=new se,t._aabb=te.create(),t._type=de.SPHERE,t}n(i,e);var s=i.prototype;return s.initialize=function(){e.prototype.initialize.call(this),this.size=.15,this.range=1,this.luminanceHDR=1700/_e(.15),this.luminanceLDR=1},s.update=function(){if(this._node&&(this._node.hasChangedFlags||this._needUpdate)){this._node.getWorldPosition(this._pos);var e=this._range;te.set(this._aabb,this._pos.x,this._pos.y,this._pos.z,e,e,e),this._needUpdate=!1}},t(i,[{key:"position",get:function(){return this._pos}},{key:"size",get:function(){return this._size},set:function(e){this._size=e}},{key:"range",get:function(){return this._range},set:function(e){this._range=e,this._needUpdate=!0}},{key:"luminance",get:function(){return l().isHDR?this._luminanceHDR:this._luminanceLDR},set:function(e){l().isHDR?this.luminanceHDR=e:this.luminanceLDR=e}},{key:"luminanceHDR",get:function(){return this._luminanceHDR},set:function(e){this._luminanceHDR=e}},{key:"luminanceLDR",set:function(e){this._luminanceLDR=e}},{key:"aabb",get:function(){return this._aabb}}]),i}(le)),new se(0,0,-1)),Qe=new ae,qe=new J,Ze=new J,Je=new J,$e=new J,et=(e("S",function(e){function i(){var t;return(t=e.call(this)||this)._dir=new se(1,-1,-1),t._range=5,t._spotAngle=Math.cos(Math.PI/6),t._angleAttenuationStrength=0,t._pos=new se,t._aabb=te.create(),t._frustum=re.create(),t._angle=0,t._needUpdate=!1,t._size=.15,t._luminanceHDR=0,t._luminanceLDR=0,t._shadowEnabled=!1,t._shadowPcf=f.HARD,t._shadowBias=1e-5,t._shadowNormalBias=0,t._type=de.SPOT,t}n(i,e);var s=i.prototype;return s.initialize=function(){e.prototype.initialize.call(this),this.size=.15,this.luminanceHDR=1700/_e(.15),this.luminanceLDR=1,this.range=Math.cos(Math.PI/6),this._dir.set(new se(1,-1,-1))},s.update=function(){this._node&&(this._node.hasChangedFlags||this._needUpdate)&&(this._node.getWorldPosition(this._pos),se.transformQuat(this._dir,Ye,this._node.getWorldRotation(Qe)),se.normalize(this._dir,this._dir),te.set(this._aabb,this._pos.x,this._pos.y,this._pos.z,this._range,this._range,this._range),this._node.getWorldRT(qe),J.invert(qe,qe),J.perspective(Ze,this._angle,1,.001,this._range),J.multiply(Je,Ze,qe),this._frustum.update(Je,$e),this._needUpdate=!1)},t(i,[{key:"position",get:function(){return this._pos}},{key:"size",get:function(){return this._size},set:function(e){this._size=e}},{key:"range",get:function(){return this._range},set:function(e){this._range=e,this._needUpdate=!0}},{key:"luminance",get:function(){return l().isHDR?this._luminanceHDR:this._luminanceLDR},set:function(e){l().isHDR?this.luminanceHDR=e:this.luminanceLDR=e}},{key:"luminanceHDR",get:function(){return this._luminanceHDR},set:function(e){this._luminanceHDR=e}},{key:"luminanceLDR",get:function(){return this._luminanceLDR},set:function(e){this._luminanceLDR=e}},{key:"direction",get:function(){return this._dir}},{key:"spotAngle",get:function(){return this._spotAngle},set:function(e){this._angle=e,this._spotAngle=Math.cos(.5*e),this._needUpdate=!0}},{key:"angleAttenuationStrength",get:function(){return this._angleAttenuationStrength},set:function(e){this._angleAttenuationStrength=e,this._needUpdate=!0}},{key:"angle",get:function(){return this._angle}},{key:"aabb",get:function(){return this._aabb}},{key:"frustum",get:function(){return this._frustum}},{key:"shadowEnabled",get:function(){return this._shadowEnabled},set:function(e){this._shadowEnabled=e}},{key:"shadowPcf",get:function(){return this._shadowPcf},set:function(e){this._shadowPcf=e}},{key:"shadowBias",get:function(){return this._shadowBias},set:function(e){this._shadowBias=e}},{key:"shadowNormalBias",get:function(){return this._shadowNormalBias},set:function(e){this._shadowNormalBias=e}}]),i}(le)),e("P",function(e){function i(){var t;return(t=e.call(this)||this)._needUpdate=!1,t._range=1,t._luminanceHDR=0,t._luminanceLDR=0,t._pos=ne(),t._aabb=te.create(),t._type=de.POINT,t}n(i,e);var s=i.prototype;return s.initialize=function(){e.prototype.initialize.call(this),this.range=1,this.luminanceHDR=1700/_e(1),this.luminanceLDR=1},s.update=function(){if(this._node&&(this._node.hasChangedFlags||this._needUpdate)){this._node.getWorldPosition(this._pos);var e=this._range;te.set(this._aabb,this._pos.x,this._pos.y,this._pos.z,e,e,e),this._needUpdate=!1}},t(i,[{key:"position",get:function(){return this._pos}},{key:"range",get:function(){return this._range},set:function(e){this._range=e,this._needUpdate=!0}},{key:"luminance",get:function(){return l().isHDR?this._luminanceHDR:this._luminanceLDR},set:function(e){l().isHDR?this.luminanceHDR=e:this.luminanceLDR=e}},{key:"luminanceHDR",get:function(){return this._luminanceHDR},set:function(e){this._luminanceHDR=e}},{key:"luminanceLDR",set:function(e){this._luminanceLDR=e}},{key:"aabb",get:function(){return this._aabb}}]),i}(le)),new se(0,0,-1)),tt=(e("f",function(e){function i(){var t;return(t=e.call(this)||this)._dir=new se(0,0,-1),t._pos=new se(0,0,0),t._scale=new se(1,1,1),t._right=new se(1,0,0),t._illuminanceHDR=_.SUN_ILLUM,t._illuminanceLDR=1,t._type=de.RANGED_DIRECTIONAL,t}n(i,e);var s=i.prototype;return s.initialize=function(){e.prototype.initialize.call(this),this.illuminance=_.SUN_ILLUM},s.update=function(){this._node&&this._node.hasChangedFlags&&(this._node.getWorldPosition(this._pos),this._node.getWorldScale(this._scale),se.transformQuat(this._dir,et,this._node.worldRotation),se.transformQuat(this._right,se.RIGHT,this._node.worldRotation))},t(i,[{key:"direction",get:function(){return this._dir}},{key:"right",get:function(){return this._right}},{key:"position",get:function(){return this._pos}},{key:"scale",get:function(){return this._scale}},{key:"illuminance",get:function(){return l().isHDR?this._illuminanceHDR:this._illuminanceLDR},set:function(e){l().isHDR?this.illuminanceHDR=e:this.illuminanceLDR=e}},{key:"illuminanceHDR",get:function(){return this._illuminanceHDR},set:function(e){this._illuminanceHDR=e}},{key:"illuminanceLDR",get:function(){return this._illuminanceLDR},set:function(e){this._illuminanceLDR=e}}]),i}(le)),e("L",function(){function e(){this.screenUsagePercentage=1,this._models=[]}var i=e.prototype;return i.addModel=function(e){this._models.splice(0,0,e)},i.eraseModel=function(e){var t=this._models.indexOf(e);t>=0&&this._models.splice(t,1)},i.clearModels=function(){this._models.length=0},t(e,[{key:"models",get:function(){return this._models}}]),e}()),e("e",function(){function e(){this.scene=void 0,this.node=null,this.enabled=!0,this._localBoundaryCenter=ne(0,0,0),this._objectSize=1,this._lodDataArray=[],this._lockedLODLevelVec=[],this._isLockLevelChanged=!1,this._device=O.gfxDevice}var i=e.prototype;return i.attachToScene=function(e){this.scene=e},i.detachFromScene=function(){this.scene=null},i.lockLODLevels=function(e){if(e.length!==this._lockedLODLevelVec.length)this._isLockLevelChanged=!0;else for(var t=e.length,i=0;i<t;i++)if(e[i]!==this._lockedLODLevelVec[i]){this._isLockLevelChanged=!0;break}this._lockedLODLevelVec=e.slice()},i.isLockLevelChanged=function(){return this._isLockLevelChanged},i.resetLockChangeFlag=function(){this._isLockLevelChanged=!1},i.getLockedLODLevels=function(){return this._lockedLODLevelVec},i.clearLODs=function(){this._lodDataArray.length=0},i.insertLOD=function(e,t){this._lodDataArray.splice(e,0,t)},i.updateLOD=function(e,t){this._lodDataArray[e]=t},i.eraseLOD=function(e){this._lodDataArray.splice(e,1)},i.getVisibleLODLevel=function(e){for(var t=this.getScreenUsagePercentage(e),i=-1,n=0;n<this.lodCount;++n)if(t>=this.lodDataArray[n].screenUsagePercentage){i=n;break}return i},i.getScreenUsagePercentage=function(e){return this.node?(e.projectionType===fe.PERSPECTIVE&&(t=se.len(this.localBoundaryCenter.transformMat4(this.node.worldMatrix).subtract(e.node.worldPosition))),this.distanceToScreenUsagePercentage(e,t,this.getWorldSpaceSize())):0;var t},i.distanceToScreenUsagePercentage=function(e,t,i){return e.projectionType===fe.PERSPECTIVE?i*e.matProj.m05/(2*t):i*e.matProj.m05*.5},i.getWorldSpaceSize=function(){var e=this.node.scale;return Math.max(Math.abs(e.x),Math.abs(e.y),Math.abs(e.z))*this.objectSize},t(e,[{key:"localBoundaryCenter",get:function(){return this._localBoundaryCenter.clone()},set:function(e){this._localBoundaryCenter.set(e)}},{key:"lodCount",get:function(){return this._lodDataArray.length}},{key:"objectSize",get:function(){return this._objectSize},set:function(e){this._objectSize=e}},{key:"lodDataArray",get:function(){return this._lodDataArray}}]),e}()),e("a",function(){function e(e){this._name="",this._cameras=[],this._models=[],this._lodGroups=[],this._batches=[],this._directionalLights=[],this._sphereLights=[],this._spotLights=[],this._pointLights=[],this._rangedDirLights=[],this._mainLight=null,this._modelId=0,this._lodStateCache=null,this._root=e}e.registerCreateFunc=function(t){t._createSceneFun=function(t){return new e(t)}};var i=e.prototype;return i.initialize=function(e){return this._name=e.name,this._lodStateCache=new it(this),!0},i.update=function(e){var t=this._mainLight;t&&t.update();for(var i=this._sphereLights,n=0;n<i.length;n++)i[n].update();for(var s=this._spotLights,a=0;a<s.length;a++)s[a].update();for(var r=this._pointLights,o=0;o<r.length;o++)r[o].update();for(var h=this._rangedDirLights,c=0;c<h.length;c++)h[c].update();for(var u=this._models,l=0;l<u.length;l++){var d=u[l];d.enabled&&(d.updateTransform(e),d.updateUBOs(e))}this._lodStateCache.updateLodState()},i.destroy=function(){this.removeCameras(),this.removeSphereLights(),this.removeSpotLights(),this.removeRangedDirLights(),this.removeModels(),this.removeLODGroups(),this._lodStateCache.clearCache()},i.isCulledByLod=function(e,t){return this._lodStateCache.isLodModelCulled(e,t)},i.addCamera=function(e){e.attachToScene(this),this._cameras.push(e),this._lodStateCache.addCamera(e)},i.removeCamera=function(e){for(var t=0;t<this._cameras.length;++t)if(this._cameras[t]===e)return this._cameras.splice(t,1),e.detachFromScene(),void this._lodStateCache.removeCamera(e)},i.removeCameras=function(){var e=this;this._cameras.forEach((function(t){t.detachFromScene(),e._lodStateCache.removeCamera(t)})),this._cameras.length=0},i.setMainLight=function(e){this._mainLight=e,this._mainLight&&this._mainLight.activate()},i.unsetMainLight=function(e){if(this._mainLight===e){var t=this._directionalLights;if(t.length)return this.setMainLight(t[t.length-1]),void(this._mainLight.node&&(this._mainLight.node.hasChangedFlags|=L.ROTATION));this.setMainLight(null)}},i.addDirectionalLight=function(e){e.attachToScene(this),this._directionalLights.push(e)},i.removeDirectionalLight=function(e){for(var t=0;t<this._directionalLights.length;++t)if(this._directionalLights[t]===e)return e.detachFromScene(),void this._directionalLights.splice(t,1)},i.addSphereLight=function(e){e.attachToScene(this),this._sphereLights.push(e)},i.removeSphereLight=function(e){for(var t=0;t<this._sphereLights.length;++t)if(this._sphereLights[t]===e)return e.detachFromScene(),void this._sphereLights.splice(t,1)},i.addSpotLight=function(e){e.attachToScene(this),this._spotLights.push(e)},i.removeSpotLight=function(e){for(var t=0;t<this._spotLights.length;++t)if(this._spotLights[t]===e)return e.detachFromScene(),void this._spotLights.splice(t,1)},i.removeSphereLights=function(){for(var e=0;e<this._sphereLights.length;++e)this._sphereLights[e].detachFromScene();this._sphereLights.length=0},i.removeSpotLights=function(){for(var e=0;e<this._spotLights.length;++e)this._spotLights[e].detachFromScene();this._spotLights.length=0},i.addPointLight=function(e){e.attachToScene(this),this._pointLights.push(e)},i.removePointLight=function(e){for(var t=0;t<this._pointLights.length;++t)if(this._pointLights[t]===e)return e.detachFromScene(),void this._pointLights.splice(t,1)},i.removePointLights=function(){for(var e=0;e<this._pointLights.length;++e)this._pointLights[e].detachFromScene();this._pointLights.length=0},i.addRangedDirLight=function(e){e.attachToScene(this),this._rangedDirLights.push(e)},i.removeRangedDirLight=function(e){for(var t=0;t<this._rangedDirLights.length;++t)if(this._rangedDirLights[t]===e)return e.detachFromScene(),void this._rangedDirLights.splice(t,1)},i.removeRangedDirLights=function(){for(var e=0;e<this._rangedDirLights.length;++e)this._rangedDirLights[e].detachFromScene();this._rangedDirLights.length=0},i.addModel=function(e){e.attachToScene(this),this._models.push(e)},i.removeModel=function(e){for(var t=0;t<this._models.length;++t)if(this._models[t]===e)return this._lodStateCache.removeModel(e),e.detachFromScene(),void this._models.splice(t,1)},i.removeModels=function(){var e=this;this._models.forEach((function(t){e._lodStateCache.removeModel(t),t.detachFromScene(),t.destroy()})),this._models.length=0},i.addBatch=function(e){this._batches.push(e)},i.removeBatch=function(e){for(var t=0;t<this._batches.length;++t)if(this._batches[t]===e)return void this._batches.splice(t,1)},i.removeBatches=function(){this._batches.length=0},i.addLODGroup=function(e){this._lodGroups.push(e),e.attachToScene(this),this._lodStateCache.addLodGroup(e)},i.removeLODGroup=function(e){var t=this._lodGroups.indexOf(e);t>=0&&(this._lodGroups.splice(t,1),e.detachFromScene(),this._lodStateCache.removeLodGroup(e))},i.removeLODGroups=function(){var e=this;this._lodGroups.forEach((function(t){e._lodStateCache.removeLodGroup(t)})),this._lodGroups.length=0},i.onGlobalPipelineStateChanged=function(){this._models.forEach((function(e){e.onGlobalPipelineStateChanged()}))},i.generateModelId=function(){return this._modelId++},t(e,[{key:"root",get:function(){return this._root}},{key:"name",get:function(){return this._name}},{key:"cameras",get:function(){return this._cameras}},{key:"mainLight",get:function(){return this._mainLight}},{key:"sphereLights",get:function(){return this._sphereLights}},{key:"spotLights",get:function(){return this._spotLights}},{key:"pointLights",get:function(){return this._pointLights}},{key:"rangedDirLights",get:function(){return this._rangedDirLights}},{key:"models",get:function(){return this._models}},{key:"batches",get:function(){return this._batches}},{key:"lodGroups",get:function(){return this._lodGroups}}]),e}()),function(){this.usedLevel=-1,this.lastUsedLevel=-1,this.transformDirty=!0}),it=function(){function e(e){this._renderScene=null,this._modelsInLODGroup=new Map,this._lodStateInCamera=new Map,this._newAddedLodGroupVec=[],this._levelModels=new Map,this._renderScene=e}var t=e.prototype;return t.addCamera=function(e){for(var t=this._renderScene.lodGroups,i=0;i<t.length;i++){var n=t[i].node.layer;if((e.visibility&n)===n){this._lodStateInCamera.has(e)||this._lodStateInCamera.set(e,new Map);break}}},t.removeCamera=function(e){this._lodStateInCamera.has(e)&&this._lodStateInCamera.delete(e)},t.addLodGroup=function(e){this._newAddedLodGroupVec.push(e);for(var t=this._renderScene.cameras,i=0;i<t.length;i++){var n=t[i];if(!this._lodStateInCamera.has(n)){var s=e.node.layer;(n.visibility&s)===s&&this._lodStateInCamera.set(n,new Map)}}},t.removeLodGroup=function(e){for(var t=this,i=0;i<e.lodCount;i++)e.lodDataArray[i].models.forEach((function(e){t._modelsInLODGroup.delete(e)}));for(var n,a=s(this._lodStateInCamera);!(n=a()).done;)n.value[1].delete(e);this._levelModels.delete(e)},t.removeModel=function(e){this._modelsInLODGroup.has(e)&&this._modelsInLODGroup.delete(e)},t.updateLodState=function(){var e=this;this._newAddedLodGroupVec.forEach((function(t){var i=e._levelModels.get(t);i||(i=new Map,e._levelModels.set(t,i));for(var n=0;n<t.lodCount;n++){var s=i.get(n);s||(s=[]);for(var a=t.lodDataArray[n].models,r=0;r<a.length;r++){var o=a[r],h=e._modelsInLODGroup.get(o);h||(h=new Map),e._modelsInLODGroup.set(o,h),s.push(o)}i.set(n,s)}})),this._newAddedLodGroupVec.length=0;for(var t=this._renderScene.lodGroups,i=function(){var i=t[n];if(i.enabled){var a=i.getLockedLODLevels();if(a.length>0){if(i.node.hasChangedFlags>0)for(var r,o=s(e._lodStateInCamera);!(r=o()).done;){var h=r.value,c=h[1].get(i);c||(c=new tt,h[1].set(i,c)),c.transformDirty=!0}if(i.isLockLevelChanged()){i.resetLockChangeFlag();var u=e._levelModels.get(i);u&&(u.forEach((function(t){t.forEach((function(t){var i=e._modelsInLODGroup.get(t);i&&i.clear()}))})),a.forEach((function(t){var i=u.get(t);i&&i.forEach((function(t){var i=e._modelsInLODGroup.get(t);if(i&&t.node&&t.node.active)for(var n,a=s(e._lodStateInCamera);!(n=a()).done;){var r=n.value;i.set(r[0],!0)}}))})))}return 0}for(var l,d=!1,_=s(e._lodStateInCamera);!(l=_()).done;){var f=l.value,g=f[1].get(i);g||(g=new tt,f[1].set(i,g));var p=f[0].node.hasChangedFlags,m=i.node.hasChangedFlags;if(p>0||m>0||g.transformDirty){g.transformDirty&&(g.transformDirty=!1);var L=i.getVisibleLODLevel(f[0]);L!==g.usedLevel&&(g.lastUsedLevel=g.usedLevel,g.usedLevel=L,d=!0)}}var v=e._levelModels.get(i);if(!v)return 0;i.isLockLevelChanged()?(i.resetLockChangeFlag(),v.forEach((function(t){t.forEach((function(t){var i=e._modelsInLODGroup.get(t);i&&i.clear()}))})),d=!0):d&&e._lodStateInCamera.forEach((function(t){var n=t.get(i);if(n&&n.usedLevel!==n.lastUsedLevel){var s=v.get(n.lastUsedLevel);s&&s.forEach((function(t){var i=e._modelsInLODGroup.get(t);i&&i.clear()}))}})),d&&e._lodStateInCamera.forEach((function(t,n){var s=t.get(i);if(s){var a=s.usedLevel,r=v.get(a);r&&r.forEach((function(t){var i=e._modelsInLODGroup.get(t);i&&t.node&&t.node.active&&i.set(n,!0)}))}}))}},n=0;n<t.length;n++)i()},t.isLodModelCulled=function(e,t){var i=this._modelsInLODGroup.get(t);return!!i&&!i.has(e)},t.clearCache=function(){this._levelModels.clear(),this._modelsInLODGroup.clear(),this._lodStateInCamera.clear(),this._newAddedLodGroupVec.length=0},t.isLodGroupVisibleByCamera=function(e,t){var i=e.node.layer;return(t.visibility&i)===i},e}(),nt=((We={})[a.PORTRAIT]=V.IDENTITY,We[a.LANDSCAPE_RIGHT]=V.ROTATE_90,We[a.PORTRAIT_UPSIDE_DOWN]=V.ROTATE_180,We[a.LANDSCAPE_LEFT]=V.ROTATE_270,We),st=0,at=(e("c",function(){var e=i.prototype;function i(){this._title="",this._width=1,this._height=1,this._swapchain=null,this._renderPass=null,this._colorTextures=[],this._depthStencilTexture=null,this._cameras=[],this._hasOnScreenAttachments=!1,this._hasOffScreenAttachments=!1,this._framebuffer=null,this._device=null,this._renderWindowId=st++,this._isResized=!0,this._colorName="Color"+this._renderWindowId,this._depthStencilName="DepthStencil"+this._renderWindowId}return e.isRenderWindowResized=function(){return this._isResized},e.setRenderWindowResizeHandled=function(){this._isResized=!1},i.registerCreateFunc=function(e){e._createWindowFun=function(){return new i}},e.initialize=function(e,t){if(void 0!==t.title&&(this._title=t.title),void 0!==t.swapchain&&(this._swapchain=t.swapchain),this._width=t.width,this._height=t.height,this._device=e,this._renderPass=e.createRenderPass(t.renderPassInfo),t.swapchain)this._swapchain=t.swapchain,this._colorTextures.push(t.swapchain.colorTexture),this._depthStencilTexture=t.swapchain.depthStencilTexture;else{for(var i=0;i<t.renderPassInfo.colorAttachments.length;i++){var n=new X(K.TEX2D,Y.COLOR_ATTACHMENT|Y.SAMPLED|Y.TRANSFER_SRC,t.renderPassInfo.colorAttachments[i].format,this._width,this._height);t.externalFlag&&(t.externalFlag&Q.EXTERNAL_NORMAL||t.externalFlag&Q.EXTERNAL_OES)&&(n.flags|=t.externalFlag,n.externalRes=t.externalResLow?t.externalResLow:0),this._colorTextures.push(e.createTexture(n))}t.renderPassInfo.depthStencilAttachment&&t.renderPassInfo.depthStencilAttachment.format!==q.UNKNOWN&&(this._depthStencilTexture=e.createTexture(new X(K.TEX2D,Y.DEPTH_STENCIL_ATTACHMENT|Y.SAMPLED,t.renderPassInfo.depthStencilAttachment.format,this._width,this._height)),this._hasOffScreenAttachments=!0)}return this._framebuffer=e.createFramebuffer(new Z(this._renderPass,this._colorTextures,this._depthStencilTexture)),!0},e.destroy=function(){this.clearCameras(),this._framebuffer&&(this._framebuffer.destroy(),this._framebuffer=null),this._depthStencilTexture&&(this._depthStencilTexture.destroy(),this._depthStencilTexture=null);for(var e=0;e<this._colorTextures.length;e++){var t=this._colorTextures[e];t&&t.destroy()}this._colorTextures.length=0,this._device=null},e.resize=function(e,t){if(this._swapchain)this._swapchain.resize(e,t,nt[oe.orientation]),this._width=this._swapchain.width,this._height=this._swapchain.height;else{for(var i=0;i<this._colorTextures.length;i++)this._colorTextures[i].resize(e,t);this._depthStencilTexture&&this._depthStencilTexture.resize(e,t),this._width=e,this._height=t}this.framebuffer&&(this.framebuffer.destroy(),this._framebuffer=this._device.createFramebuffer(new Z(this._renderPass,this._colorTextures,this._depthStencilTexture))),this._cameras.forEach((function(i){i.resize(e,t)})),this._isResized=!0},e.extractRenderCameras=function(e){for(var t=0;t<this._cameras.length;t++){var i=this._cameras[t];i.enabled&&(i.update(),e.push(i))}},e.attachCamera=function(e){for(var t=0;t<this._cameras.length;t++)if(this._cameras[t]===e)return;this._cameras.push(e),this.sortCameras(),this._isResized=!0},e.detachCamera=function(e){for(var t=0;t<this._cameras.length;++t)if(this._cameras[t]===e)return void this._cameras.splice(t,1)},e.clearCameras=function(){this._cameras.length=0},e.sortCameras=function(){this._cameras.sort((function(e,t){return e.priority-t.priority}))},t(i,[{key:"width",get:function(){return this._width}},{key:"height",get:function(){return this._height}},{key:"swapchain",get:function(){return this._swapchain}},{key:"framebuffer",get:function(){return this._framebuffer}},{key:"cameras",get:function(){return this._cameras}},{key:"renderWindowId",get:function(){return this._renderWindowId}},{key:"colorName",get:function(){return this._colorName}},{key:"depthStencilName",get:function(){return this._depthStencilName}}]),i}()),[".png",".jpg",".bmp",".jpeg",".gif",".ico",".tiff",".webp",".image",".pvr",".pkm",".astc"]),rt=[".mp3",".ogg",".wav",".m4a"];function ot(){return!0}var ht={transformURL:function(e){var t=ye(e);if(!t)return e;var i=be.find((function(e){return!!e.getAssetInfo(t)}));if(!i)return e;var n,s=i.getAssetInfo(t);if(!(n=e.startsWith(i.base+i.config.nativeBase)?s.nativeVer||"":s.ver||"")||-1!==e.indexOf(n))return e;var a=!1;if(".ttf"===ge(e)&&(a=!0),a){var r=Re(e),o=Ae(e);e=r+"."+n+"/"+o}else e=e.replace(/.*[/\\][0-9a-fA-F]{2}[/\\]([0-9a-fA-F-@]{8,})/,(function(e){return e+"."+n}));return e}},ct=e("C",function(){function e(){this._autoReleaseSetting=Object.create(null),this._parseLoadResArgs=Te}var i=e.prototype;return i.load=function(e,t,i){void 0===i&&void 0!==t&&(i=t,t=null);for(var n=Array.isArray(e)?e:[e],s=0;s<n.length;s++){var a=n[s];"string"==typeof a?n[s]={url:a,__isNative__:!0}:(a.type&&(a.ext="."+a.type,a.type=void 0),a.url&&(a.__isNative__=!0))}var r=[],o=[];v.loadAny(n,null,(function(e,i,n){n.content&&(at.includes(n.ext)?r.push(n.content):rt.includes(n.ext)&&o.push(n.content)),t&&t(e,i,n)}),(function(e,t){var s=null;if(!e){t=Array.isArray(t)?t:[t];for(var a=function(e){var i=t[e];if(!(i instanceof Se)){var s=i,a=n[e].url;r.includes(s)?Ce.create(a,i,".png",{},(function(i,n){s=t[e]=n})):o.includes(s)&&Ce.create(a,i,".mp3",{},(function(i,n){s=t[e]=n})),pe.add(a,s)}},h=0;h<t.length;h++)a(h);if(t.length>1){var c=Object.create(null);t.forEach((function(e){c[e._uuid]=e})),s={isCompleted:ot,_map:c}}else s=t[0]}i&&i(e,s)}))},i.getXMLHttpRequest=function(){return new XMLHttpRequest},i.getItem=function(e){return v.assets.has(e)?{content:v.assets.get(e)}:null},i.loadRes=function(e,t,i,n){var s=this._parseLoadResArgs(t,i,n),a=s.type,r=s.onProgress,o=s.onComplete,h=ge(e);h&&!we.getInfoWithPath(e,a)&&(e=e.slice(0,-h.length)),we.load(e,a,r,o)},i.loadResArray=function(e,t,i,n){var s=this._parseLoadResArgs(t,i,n),a=s.type,r=s.onProgress,o=s.onComplete;e.forEach((function(t,i){var n=ge(t);n&&!we.getInfoWithPath(t,a)&&(e[i]=t.slice(0,-n.length))})),we.load(e,a,r,o)},i.loadResDir=function(e,t,i,n){var s=this._parseLoadResArgs(t,i,n),a=s.type,r=s.onProgress,o=s.onComplete;we.loadDir(e,a,r,(function(t,i){var n=[];t||(n=we.getDirWithPath(e,a).map((function(e){return e.path}))),o&&o(t,i,n)}))},i.getRes=function(e,t){return pe.has(e)?pe.get(e):we.get(e,t)},i.getResCount=function(){return pe.count},i.getDependsRecursively=function(e){if(!e)return[];var t="string"==typeof e?e:e._uuid;return Ee.getDepsRecursively(t).concat([t])},i.addDownloadHandlers=function(e){var t=Object.create(null),i=function(){var i=e[n];t["."+n]=function(e,t,n){i({url:e},n)}};for(var n in e)i();Be.register(t)},i.addLoadHandlers=function(e){var t=Object.create(null),i=function(){var i=e[n];t["."+n]=function(e,t,n){i({content:e},n)}};for(var n in e)i();S.register(t)},i.release=function(e){if(Array.isArray(e))for(var t=0;t<e.length;t++){var i=e[t];"string"==typeof i&&(i=pe.get(i)),v.releaseAsset(i)}else e&&("string"==typeof e&&(e=pe.get(e)),v.releaseAsset(e))},i.releaseAsset=function(e){v.releaseAsset(e)},i.releaseRes=function(e,t){we.release(e,t)},i.releaseAll=function(){v.releaseAll(),pe.clear()},i.removeItem=function(e){return!!pe.remove(e)},i.setAutoRelease=function(e,t){"object"==typeof e&&(e=e._uuid),this._autoReleaseSetting[e]=!!t},i.setAutoReleaseRecursively=function(e,t){"object"==typeof e&&(e=e._uuid),t=!!t,this._autoReleaseSetting[e]=t;for(var i=Ee.getDepsRecursively(e),n=0;n<i.length;n++)this._autoReleaseSetting[i[n]]=t},i.isAutoRelease=function(e){return"object"==typeof e&&(e=e._uuid),!!this._autoReleaseSetting[e]},t(e,[{key:"onProgress",set:function(e){Pe(e)}},{key:"_cache",get:function(){if(pe instanceof De)return pe.map;var e={};return pe.forEach((function(t,i){e[i]=t})),e}},{key:"md5Pipe",get:function(){return ht}},{key:"downloader",get:function(){return Be}},{key:"loader",get:function(){return v.parser}}]),e}()),ut=e("l",new ct),lt=e("A",{init:function(e){e.importBase=e.libraryPath,e.nativeBase=e.rawAssetsBase,v.init(e),e.rawAssets&&we.init({base:"",deps:[],scenes:{},redirect:[],debug:!0,packs:{},types:[],versions:{import:[],native:[]},name:ve.RESOURCES,importBase:e.importBase,nativeBase:e.nativeBase,paths:e.rawAssets.assets,uuids:Object.keys(e.rawAssets.assets),extensionMap:{}})},loadAsset:function(e,t){v.loadAny(e,t)}}),dt=e("u",{});he(dt,"url",[{name:"normalize",target:v.utils,targetName:"assetManager.utils",newName:"normalize"},{name:"raw",targetName:"Asset.prototype",newName:"nativeUrl",customFunction:function(e){return e.startsWith("resources/")?me({path:Le(e.substring(10)),bundle:ve.RESOURCES,__isNative__:!0,ext:ge(e)}):""}}]),ce(lt,"AssetLibrary",[{name:"getLibUrlNoExt",suggest:"AssetLibrary.getLibUrlNoExt was removed, if you want to transform url, please use assetManager.utils.getUrlWithUuid instead"},{name:"queryAssetInfo",suggest:"AssetLibrary.queryAssetInfo was removed"}]),ce(ut,"loader",[{name:"releaseResDir",suggest:"loader.releaseResDir was removed, please use assetManager.releaseAsset instead"},{name:"flowInDeps",suggest:"loader.flowInDeps was removed"},{name:"assetLoader",suggest:"loader.assetLoader was removed, assetLoader and md5Pipe were merged into assetManager.transformPipeline"}]),he(h,"cc",[{name:"loader",newName:"assetManager",logTimes:1,customGetter:function(){return ut}},{name:"AssetLibrary",newName:"assetManager",logTimes:1,customGetter:function(){return lt}},{name:"Pipeline",target:D,targetName:"AssetManager",newName:"Pipeline",logTimes:1},{name:"url",targetName:"assetManager",newName:"utils",logTimes:1,customGetter:function(){return dt}}]),ce(h,"cc",[{name:"LoadingItems",suggest:r(1400,"LoadingItems","AssetManager.Task")}]),he(o,"macro",[{name:"DOWNLOAD_MAX_CONCURRENT",target:Be,targetName:"assetManager.downloader",newName:"maxConcurrency"}]);var _t=Ie._autoRelease;Ie._autoRelease=function(e,t,i){_t.call(Ie,e,t,i);for(var n=ut._autoReleaseSetting,s=Object.keys(n),a=0;a<s.length;a++){var r=s[a];if(!0===n[r]){var o=pe.get(r);o&&Ie.tryRelease(o)}}}}}}));
