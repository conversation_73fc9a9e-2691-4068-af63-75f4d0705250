System.register(["./gc-object-CKHc4SnS.js","./index-C5lmLqDW.js","./global-exports-CR3GRnjt.js","./component-BaGvu7EF.js","./factory-D9_8ZCqM.js","./scene-7MDSMR3j.js","./device-manager-BvjvoelW.js","./buffer-barrier-CuX_5NUR.js","./deprecated-T2yM9nsJ.js","./director-DIlqD2Nd.js","./prefab-DH0xadMc.js","./create-mesh-hkbGggH3.js","./rendering-sub-mesh-CowWLfXC.js"],(function(t){"use strict";var e,i,s,n,r,a,h,o,u,_,c,l,f,x,d,p,g,m,v,y,S,U,F,w,T,V,P,I,k,b,z,A;return{setters:[function(t){e=t._,i=t.w,s=t.z,n=t.A,r=t.a,a=t.h,h=t.D},function(t){o=t.S,u=t.u,_=t.w,c=t.b,l=t.c,f=t.x,x=t.y,d=t.v,p=t.z,g=t.k},function(t){m=t.c,v=t.a},function(t){y=t.A},function(t){S=t.P,U=t.I},function(t){F=t.k},null,function(t){w=t.f,T=t.w,V=t.P,P=t.A,I=t.a,k=t.F},null,function(t){b=t.d,z=t.D},null,function(t){A=t._},null],execute:function(){function E(t,e,i,s){t.drawTextureAt(e,i,s)}var R=t("A",function(){function t(t,e){this._innerTextureInfos={},this._innerSpriteFrames=[],this._count=0;var i=new B;i.initWithSize(t,e),this._texture=i,this._width=t,this._height=e,this._x=2,this._y=2,this._nextY=2}var e=t.prototype;return e.insertSpriteFrame=function(t){var e=t.rect,i=t.texture,s=this._innerTextureInfos[i.getId()],n=e.x,r=e.y;if(s)n+=s.x,r+=s.y;else{var a=i.width,h=i.height;if(this._x+a+2>this._width&&(this._x=2,this._y=this._nextY),this._y+h+2>this._nextY&&(this._nextY=this._y+h+2),this._nextY>this._height)return null;var o=this._texture,u=i.image;m.internal.dynamicAtlasManager.textureBleeding&&((a<=8||h<=8)&&(E(o,u,this._x-1,this._y-1),E(o,u,this._x-1,this._y+1),E(o,u,this._x+1,this._y-1),E(o,u,this._x+1,this._y+1)),E(o,u,this._x-1,this._y),E(o,u,this._x+1,this._y),E(o,u,this._x,this._y-1),E(o,u,this._x,this._y+1)),E(o,u,this._x,this._y),this._innerTextureInfos[i.getId()]={x:this._x,y:this._y,texture:i},this._count++,n+=this._x,r+=this._y,this._x+=a+2}var _={x:n,y:r,texture:this._texture};return this._innerSpriteFrames.push(t),_},e.removeSpriteFrame=function(t){s(this._innerSpriteFrames,t)},e.deleteInnerTexture=function(t){t&&this._innerTextureInfos[t.getId()]&&(delete this._innerTextureInfos[t.getId()],this._count--)},e.isEmpty=function(){return this._count<=0},e.reset=function(){this._x=2,this._y=2,this._nextY=2;for(var t=this._innerSpriteFrames,e=0,i=t.length;e<i;e++){var s=t[e];s.isValid&&s._resetDynamicAtlasFrame()}this._innerSpriteFrames.length=0,this._innerTextureInfos={}},e.destroy=function(){this.reset(),this._texture.destroy()},t}()),B=function(t){function s(){return t.apply(this,arguments)||this}e(s,t);var n=s.prototype;return n.initWithSize=function(t,e,i){void 0===i&&(i=S.RGBA8888),this.reset({width:t,height:e,format:i})},n.drawTextureAt=function(t,e,s){var n=this.getGFXTexture();if(t&&n){var r=this._getGFXDevice();if(r){var a=new w;a.texOffset.x=e,a.texOffset.y=s,a.texExtent.width=t.width,a.texExtent.height=t.height,r.copyTexImagesToTexture([t.data],n,[a])}else i(16363)}},s}(F),Y=t("D",function(t){function i(){var e;return(e=t.call(this)||this)._atlases=[],e._atlasIndex=-1,e._maxAtlasCount=5,e._textureSize=2048,e._maxFrameSize=512,e._textureBleeding=!0,e._enabled=!1,e}e(i,t);var s=i.prototype;return s.newAtlas=function(){var t=this._atlases[++this._atlasIndex];return!t&&this._atlasIndex<this.maxAtlasCount&&(t=new R(this._textureSize,this._textureSize),this._atlases.push(t)),t},s.beforeSceneLoad=function(){this.reset()},s.init=function(){this.enabled=!n.CLEANUP_IMAGE_CACHE},s.insertSpriteFrame=function(t){if(!this._enabled||this._atlasIndex>=this._maxAtlasCount||!t||t.original)return null;if(!t.packable)return null;var e=t.texture.getSamplerInfo();if(e.minFilter!==T.LINEAR||e.magFilter!==T.LINEAR||e.mipFilter!==T.NONE)return null;var i=this._atlases[this._atlasIndex];i||(i=this.newAtlas());var s=i?i.insertSpriteFrame(t):null;return!s&&this._atlasIndex<this._maxAtlasCount?(i=this.newAtlas())?i.insertSpriteFrame(t):null:s},s.reset=function(){for(var t=0,e=this._atlases.length;t<e;t++)this._atlases[t].destroy();this._atlases.length=0,this._atlasIndex=-1},s.deleteAtlasSpriteFrame=function(t){if(t.original){for(var e=this._atlases.length-1;e>=0;e--)this._atlases[e].removeSpriteFrame(t);var i=t.original._texture;this.deleteAtlasTexture(i)}},s.deleteAtlasTexture=function(t){if(t)for(var e=this._atlases.length-1;e>=0;e--)this._atlases[e].deleteInnerTexture(t),this._atlases[e].isEmpty()&&(this._atlases[e].destroy(),this._atlases.splice(e,1),this._atlasIndex--)},s.packToDynamicAtlas=function(t,e){if(this._enabled&&e&&!e.original&&e.packable&&e.texture&&e.texture.width>0&&e.texture.height>0){var i=this.insertSpriteFrame(e);i&&e._setDynamicAtlasFrame(i)}},r(i,[{key:"enabled",get:function(){return this._enabled},set:function(t){this._enabled!==t&&(t?(this.reset(),b.on(z.BEFORE_SCENE_LAUNCH,this.beforeSceneLoad,this)):(this.reset(),b.off(z.BEFORE_SCENE_LAUNCH,this.beforeSceneLoad,this)),this._enabled=t)}},{key:"maxAtlasCount",get:function(){return this._maxAtlasCount},set:function(t){this._maxAtlasCount=t}},{key:"atlasCount",get:function(){return this._atlases.length}},{key:"textureBleeding",get:function(){return this._textureBleeding},set:function(t){this._textureBleeding=t}},{key:"textureSize",get:function(){return this._textureSize},set:function(t){this._textureSize=t}},{key:"maxFrameSize",get:function(){return this._maxFrameSize},set:function(t){this._maxFrameSize=t}}]),i}(o));Y.instance=void 0;var L,O,D=t("d",Y.instance=new Y);b.registerSystem("dynamicAtlasManager",D,0),m.internal.dynamicAtlasManager=D;var C,X=u(),M=_(),N=c.transformMat4,j=c.toArray;!function(t){t[t.RECT=0]="RECT",t[t.POLYGON=1]="POLYGON"}(C||(C={}));var G,H=[{u:0,v:0},{u:0,v:0},{u:0,v:0},{u:0,v:0}];t("a",G),function(t){t.UV_UPDATED="uv_updated"}(G||t("a",G={}));var W=t("S",l("cc.SpriteFrame")((O=function(t){function s(e){var i;return(i=t.call(this,e)||this).vertices=null,i.uv=[],i.unbiasUV=[],i.uvSliced=[],i._rect=f(),i._trimmedBorder=x(),i._offset=d(),i._originalSize=p(),i._rotated=!1,i._capInsets=[0,0,0,0],i._atlasUuid="",i._texture=void 0,i._isFlipUVY=!1,i._isFlipUVX=!1,i._original=null,i._packable=!0,i._pixelsToUnit=100,i._pivot=d(.5,.5),i._meshType=C.RECT,i._extrude=0,i._customOutLine=[],i._mesh=null,i._minPos=u(),i._maxPos=u(),i}e(s,t),s.createWithImage=function(t){var e=t instanceof U?t:new U(t),i=new F;i.image=e;var n=new s;return n.texture=i,n};var n=s.prototype;return n.textureLoaded=function(){return!!this.texture},n.isRotated=function(){return this._rotated},n.setRotated=function(t){this.rotated=t},n.getRect=function(t){return t?(t.set(this._rect),t):this._rect.clone()},n.setRect=function(t){this.rect=t},n.getOriginalSize=function(t){return t?(t.set(this._originalSize),t):this._originalSize.clone()},n.setOriginalSize=function(t){this.originalSize=t},n.getOffset=function(t){return t?(t.set(this._offset),t):this._offset.clone()},n.setOffset=function(t){this.offset=t},n.getGFXTexture=function(){return this._texture.getGFXTexture()},n.getGFXSampler=function(){return this._texture.getGFXSampler()},n.getHash=function(){return this._texture.getHash()},n.getSamplerInfo=function(){return this._texture.getSamplerInfo()},n.reset=function(t,e){void 0===e&&(e=!1);var i=this,s=!1;if(e&&(i._originalSize.set(0,0),i._rect.set(0,0,0,0),i._offset.set(0,0),i._capInsets=[0,0,0,0],i._rotated=!1,s=!0),t){t.texture&&(i._rect.set(0,0,t.texture.width,t.texture.height),i._refreshTexture(t.texture),i.checkRect(i._texture)),t.originalSize&&i._originalSize.set(t.originalSize),t.rect&&i._rect.set(t.rect),t.offset&&i._offset.set(t.offset);var n=i._capInsets;void 0!==t.borderTop&&(n[1]=t.borderTop),void 0!==t.borderBottom&&(n[3]=t.borderBottom),void 0!==t.borderLeft&&(n[0]=t.borderLeft),void 0!==t.borderRight&&(n[2]=t.borderRight),void 0!==t.isRotate&&(i._rotated=!!t.isRotate),void 0!==t.isFlipUv&&(i._isFlipUVY=!!t.isFlipUv),s=!0}s&&i.texture&&i._calculateUV(),i._calcTrimmedBorder()},n.checkRect=function(t){var e=this._rect,i=e.x,s=e.y;return this._rotated?(i+=e.height,s+=e.width):(i+=e.width,s+=e.height),i>t.width?(a(3300,this.name+"/"+t.name,i,t.width),!1):!(s>t.height&&(a(3301,this.name+"/"+t.name,s,t.height),1))},n._calcTrimmedBorder=function(){var t=this,e=t._originalSize.width,i=t._originalSize.height,s=.5*(e-t._rect.width),n=.5*(i-t._rect.height),r=t._offset,a=t._trimmedBorder;a.x=r.x+s,a.y=r.x-s,a.z=r.y+n,a.w=r.y-n},n.ensureMeshData=function(){this._mesh||(this._initVertices(),this._createMesh())},n.destroy=function(){return this._packable&&D&&D.deleteAtlasSpriteFrame(this),t.prototype.destroy.call(this)},n._calculateSlicedUV=function(){var t=this,e=t._rect,i=t.texture,s=t._capInsets,n=i.width,r=i.height,a=s[0],h=s[2],o=e.width-a-h,u=s[1],_=s[3],c=e.height-u-_,l=t.uvSliced;if(l.length=0,t._rotated){H[0].u=e.x/n,H[1].u=(e.x+_)/n,H[2].u=(e.x+_+c)/n,H[3].u=(e.x+e.height)/n,H[3].v=e.y/r,H[2].v=(e.y+a)/r,H[1].v=(e.y+a+o)/r,H[0].v=(e.y+e.width)/r;for(var f=0;f<4;++f)for(var x=H[f],d=0;d<4;++d){var p=H[3-d];l.push({u:x.u,v:p.v})}}else{H[0].u=e.x/n,H[1].u=(e.x+a)/n,H[2].u=(e.x+a+o)/n,H[3].u=(e.x+e.width)/n,H[3].v=e.y/r,H[2].v=(e.y+u)/r,H[1].v=(e.y+u+c)/r,H[0].v=(e.y+e.height)/r;for(var g=0;g<4;++g)for(var m=H[g],v=0;v<4;++v){var y=H[v];l.push({u:y.u,v:m.v})}}this.emit(G.UV_UPDATED,this)},n._calculateUV=function(){var t=h,e=this,i=e._rect,s=e.uv,n=e.unbiasUV,r=e.texture,a=r.width,o=r.height;if(e._rotated){var u=0===a?0:i.x/a,_=0===a?1:(i.x+i.height)/a,c=0===o?0:i.y/o,l=0===o?1:(i.y+i.width)/o;e._isFlipUVX&&e._isFlipUVY?t(s,_,l,_,c,u,l,u,c):e._isFlipUVX?t(s,_,c,_,l,u,c,u,l):e._isFlipUVY?t(s,u,l,u,c,_,l,_,c):t(s,u,c,u,l,_,c,_,l);var f=0===a?0:i.x/a,x=0===a?1:(i.x+i.height)/a,d=0===o?0:i.y/o,p=0===o?1:(i.y+i.width)/o;e._isFlipUVX&&e._isFlipUVY?t(n,x,p,x,d,f,p,f,d):e._isFlipUVX?t(n,x,d,x,p,f,d,f,p):e._isFlipUVY?t(n,f,p,f,d,x,p,x,d):t(n,f,d,f,p,x,d,x,p)}else{var g=0===a?0:i.x/a,m=0===a?1:(i.x+i.width)/a,v=0===o?1:(i.y+i.height)/o,y=0===o?0:i.y/o;e._isFlipUVX&&e._isFlipUVY?t(s,m,y,g,y,m,v,g,v):e._isFlipUVX?t(s,m,v,g,v,m,y,g,y):e._isFlipUVY?t(s,g,y,m,y,g,v,m,v):t(s,g,v,m,v,g,y,m,y);var S=0===a?0:i.x/a,U=0===a?1:(i.x+i.width)/a,F=0===o?1:(i.y+i.height)/o,w=0===o?0:i.y/o;e._isFlipUVX&&e._isFlipUVY?t(n,U,w,S,w,U,F,S,F):e._isFlipUVX?t(n,U,F,S,F,U,w,S,w):e._isFlipUVY?t(n,S,w,U,w,S,F,U,F):t(n,S,F,U,F,S,w,U,w)}e._calculateSlicedUV()},n._setDynamicAtlasFrame=function(t){t&&(this._original={_texture:this._texture,_x:this._rect.x,_y:this._rect.y},this._texture=t.texture,this._rect.x=t.x,this._rect.y=t.y,this._calculateUV())},n._resetDynamicAtlasFrame=function(){this._original&&(this._rect.x=this._original._x,this._rect.y=this._original._y,this._texture=this._original._texture,this._original=null,this._calculateUV())},n._checkPackable=function(){var t=D;if(t){var e=this._texture;if(e instanceof F&&!e.isCompressed){var i=this.width,s=this.height;if(!e.image||i>t.maxFrameSize||s>t.maxFrameSize)this._packable=!1;else{var n=v.HTMLCanvasElement;e.image&&e.image instanceof n&&(this._packable=!0)}}else this._packable=!1}},n._serialize=function(){return null},n._deserialize=function(t){var e=this,i=t,s=i.rect;s&&(e._rect=new g(s.x,s.y,s.width,s.height));var n=i.offset;i.offset&&(e._offset=d(n.x,n.y));var r=i.originalSize;i.originalSize&&(e._originalSize=p(r.width,r.height)),e._rotated=!!i.rotated,e._name=i.name,e._packable=!!i.packable,e._pixelsToUnit=i.pixelsToUnit;var a=i.pivot;a&&(e._pivot=d(a.x,a.y)),e._meshType=i.meshType;var h=i.capInsets;if(h){var o=e._capInsets;o[0]=h[0],o[1]=h[1],o[2]=h[2],o[3]=h[3]}var _=i.vertices;if(_){e.vertices||(e.vertices={rawPosition:[],positions:[],indexes:_.indexes,uv:_.uv,nuv:_.nuv,minPos:u(_.minPos.x,_.minPos.y,_.minPos.z),maxPos:u(_.maxPos.x,_.maxPos.y,_.maxPos.z)}),e.vertices.rawPosition.length=0;for(var c=_.rawPosition,l=0;l<c.length;l+=3)e.vertices.rawPosition.push(u(c[l],c[l+1],c[l+2]));e._updateMeshVertices()}},n.clone=function(){var t,e,i,n,r,a=this,h=new s,o=a.vertices;return h.vertices=o?{rawPosition:o.rawPosition.slice(0),positions:o.positions.slice(0),indexes:o.indexes.slice(0),uv:o.uv.slice(0),nuv:o.nuv.slice(0),minPos:o.minPos.clone(),maxPos:o.maxPos.clone()}:null,(t=h.uv).splice.apply(t,[0,h.uv.length].concat(a.uv)),(e=h.unbiasUV).splice.apply(e,[0,h.unbiasUV.length].concat(a.unbiasUV)),(i=h.uvSliced).splice.apply(i,[0,h.uvSliced.length].concat(a.uvSliced)),h._rect.set(a._rect),h._trimmedBorder.set(a._trimmedBorder),h._offset.set(a._offset),h._originalSize.set(a._originalSize),h._rotated=a._rotated,(n=h._capInsets).splice.apply(n,[0,h._capInsets.length].concat(a._capInsets)),h._atlasUuid=a._atlasUuid,h._texture=a._texture,h._isFlipUVX=a._isFlipUVX,h._isFlipUVY=a._isFlipUVY,a._original?h._original={_texture:a._original._texture,_x:a._original._x,_y:a._original._y}:h._original=null,h._packable=a._packable,h._pixelsToUnit=a._pixelsToUnit,h._pivot.set(a._pivot),h._meshType=a._meshType,h._extrude=a._extrude,(r=h._customOutLine).splice.apply(r,[0,h._customOutLine.length].concat(a._customOutLine)),h._minPos=a._minPos,h._maxPos=a._maxPos,a._mesh&&h._createMesh(),h},n._refreshTexture=function(t){var e=this;e._texture=t;var i=e._texture,s={},n=!1;0!==e._rect.width&&0!==e._rect.height&&e.checkRect(i)||(s.rect=f(0,0,i.width,i.height),n=!0),(0===e._originalSize.width||0===e._originalSize.height||n)&&(s.originalSize=p(i.width,i.height),n=!0),n&&e.reset(s),e._checkPackable(),e._mesh&&e._updateMesh()},n.onLoaded=function(){this._calcTrimmedBorder()},n.initDefault=function(e){t.prototype.initDefault.call(this,e);var i=new F;i.initDefault(),this._refreshTexture(i),this._calculateUV()},n.validate=function(){return this._texture&&this._rect&&0!==this._rect.width&&0!==this._rect.height},n._initVertices=function(){var t=this;if(t.vertices){var e=t.vertices;e.rawPosition.length=0,e.positions.length=0,e.indexes.length=0,e.uv.length=0,e.nuv.length=0,e.minPos.set(0,0,0),e.maxPos.set(0,0,0)}else t.vertices={rawPosition:[],positions:[],indexes:[],uv:[],nuv:[],minPos:u(),maxPos:u()};var i=t.vertices;if(t._meshType===C.POLYGON);else{var s=t.texture,n=s.width,r=s.height,a=t.rect,h=a.width,o=a.height,_=a.x,c=r-a.y-o,l=h/2,f=o/2,x=0===n?0:_/n,d=0===n?1:(_+h)/n,p=0===r?1:(c+o)/r,g=0===r?0:c/r,m=i.uv,v=i.nuv,y=i.rawPosition,S=i.indexes;X.set(-l,-f,0),y.push(X.clone()),m.push(_,c+o),v.push(x,g),i.minPos.set(X),X.set(l,-f,0),y.push(X.clone()),m.push(_+h,c+o),v.push(d,g),X.set(-l,f,0),y.push(X.clone()),m.push(_,c),v.push(x,p),X.set(l,f,0),y.push(X.clone()),m.push(_+h,c),v.push(d,p),i.maxPos.set(X),S.push(0,1,2,2,1,3)}this._updateMeshVertices()},n._updateMeshVertices=function(){M.identity();var t=1/this._pixelsToUnit,e=-(this._pivot.x-.5)*this.rect.width*t,i=-(this._pivot.y-.5)*this.rect.height*t,s=u(e,i,0);M.transform(s),s.set(t,t,1),M.scale(s);for(var n=this.vertices,r=0;r<n.rawPosition.length;r++){var a=n.rawPosition[r];N(s,a,M),j(n.positions,s,3*r)}N(this._minPos,n.minPos,M),N(this._maxPos,n.maxPos,M)},n._createMesh=function(){this._mesh=A({primitiveMode:V.TRIANGLE_LIST,positions:this.vertices.positions,uvs:this.vertices.nuv,indices:this.vertices.indexes,minPos:this._minPos,maxPos:this._maxPos,attributes:[new P(I.ATTR_POSITION,k.RGB32F),new P(I.ATTR_TEX_COORD,k.RG32F)]})},n._updateMesh=function(){this._mesh&&this._mesh.destroy(),this._initVertices(),this._createMesh()},r(s,[{key:"insetTop",get:function(){return this._capInsets[1]},set:function(t){this._capInsets[1]!==t&&(this._capInsets[1]=t,this._texture&&this._calculateSlicedUV())}},{key:"insetBottom",get:function(){return this._capInsets[3]},set:function(t){this._capInsets[3]!==t&&(this._capInsets[3]=t,this._texture&&this._calculateSlicedUV())}},{key:"insetLeft",get:function(){return this._capInsets[0]},set:function(t){this._capInsets[0]!==t&&(this._capInsets[0]=t,this._texture&&this._calculateSlicedUV())}},{key:"insetRight",get:function(){return this._capInsets[2]},set:function(t){this._capInsets[2]!==t&&(this._capInsets[2]=t,this._texture&&this._calculateSlicedUV())}},{key:"rect",get:function(){return this._rect},set:function(t){this._rect.equals(t)||(this._rect.set(t),this._texture&&this._calculateUV(),this._calcTrimmedBorder())}},{key:"originalSize",get:function(){return this._originalSize},set:function(t){this._originalSize.equals(t)||(this._originalSize.set(t),this._texture&&this._calculateUV(),this._calcTrimmedBorder())}},{key:"offset",get:function(){return this._offset},set:function(t){this._offset.set(t),this._calcTrimmedBorder()}},{key:"rotated",get:function(){return this._rotated},set:function(t){this._rotated!==t&&(this._rotated=t,this._texture&&this._calculateUV())}},{key:"texture",get:function(){return this._texture},set:function(t){t?t!==this._texture&&this.reset({texture:t},!0):i(3122,this.name)}},{key:"atlasUuid",get:function(){return this._atlasUuid},set:function(t){this._atlasUuid=t}},{key:"width",get:function(){return this._texture.width}},{key:"height",get:function(){return this._texture.height}},{key:"_textureSource",set:function(t){globalThis.Build?this._texture=t:t&&(this._refreshTexture(t),this._calculateUV())}},{key:"flipUVX",get:function(){return this._isFlipUVX},set:function(t){this._isFlipUVX=t,this._calculateUV()}},{key:"flipUVY",get:function(){return this._isFlipUVY},set:function(t){this._isFlipUVY=t,this._calculateUV()}},{key:"packable",get:function(){return this._packable},set:function(t){this._packable=t}},{key:"original",get:function(){return this._original}},{key:"pixelsToUnit",get:function(){return this._pixelsToUnit}},{key:"pivot",get:function(){return this._pivot}},{key:"mesh",get:function(){return this._mesh}},{key:"trimmedBorder",get:function(){return this._trimmedBorder}}]),s}(y),O.EVENT_UV_UPDATED=G.UV_UPDATED,O.MeshType=C,L=O))||L);m.SpriteFrame=W}}}));
