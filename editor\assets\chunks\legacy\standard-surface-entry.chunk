// Copyright (c) 2017-2020 Xiamen Yaji Software Co., Ltd.

#include <legacy/shading-standard-base>
#include <legacy/output-standard>
#include <legacy/fog-fs>
#include <legacy/shadow-map-fs>


#pragma define CC_STANDARD_SURFACE_ENTRY()                         \
#if CC_FORWARD_ADD                                                 \
  #include <legacy/shading-standard-additive>                      \
  #if CC_ENABLE_CLUSTERED_LIGHT_CULLING == 1                       \
  #include <legacy/shading-cluster-additive>                       \
  #endif                                                           \
                                                                   \
  layout(location = 0) out vec4 fragColorX;                        \
                                                                   \
  void main () {                                                   \
    StandardSurface s; surf(s);                                    \
    #if CC_ENABLE_CLUSTERED_LIGHT_CULLING == 1                     \
    vec4 color = CCClusterShadingAdditive(s, CC_SHADOW_POSITION);  \
    #else                                                          \
    vec4 color = CCStandardShadingAdditive(s, CC_SHADOW_POSITION); \
    #endif                                                         \
    fragColorX = CCFragOutput(color);                              \
  }                                                                \
                                                                   \
#elif (CC_PIPELINE_TYPE == CC_PIPELINE_TYPE_FORWARD || CC_FORCE_FORWARD_SHADING) \
                                                                   \
  layout(location = 0) out vec4 fragColorX;                        \
                                                                   \
  void main () {                                                   \
    StandardSurface s; surf(s);                                    \
    vec4 color = CCStandardShadingBase(s, CC_SHADOW_POSITION);     \
    #if CC_USE_FOG != CC_FOG_NONE                                  \
      #if CC_USE_FLOAT_OUTPUT                                      \
        CC_APPLY_FOG(color, s.position.xyz);                       \
      #elif !CC_FORWARD_ADD                                        \
        CC_APPLY_FOG(color, s.position.xyz);                       \
      #endif                                                       \
    #endif                                                         \
    fragColorX = CCFragOutput(color);                              \
  }                                                                \
                                                                   \
#elif CC_PIPELINE_TYPE == CC_PIPELINE_TYPE_DEFERRED                \
  #include <common/math/octahedron-transform>                      \
                                                                   \
  layout(location = 0) out vec4 albedoOut;                         \
  layout(location = 1) out vec4 emissiveOut;                       \
  layout(location = 2) out vec4 normalOut;                         \
                                                                   \
  void main () {                                                   \
    StandardSurface s; surf(s);                                    \
    albedoOut = s.albedo;                                          \
    normalOut = vec4(float32x3_to_oct(s.normal), s.roughness, s.metallic); \
    emissiveOut = vec4(s.emissive, s.occlusion);                   \
  }                                                                \
                                                                   \
#endif                                                             \
#pragma // empty pragma trick to get rid of trailing semicolons at effect compile time
