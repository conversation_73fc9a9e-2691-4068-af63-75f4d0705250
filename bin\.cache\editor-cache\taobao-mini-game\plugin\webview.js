System.register(["./gc-object-D18ulfCO.js","./index-Y4La_nfG.js","./global-exports-CLZKKIY2.js","./sprite-renderer-rngMmqx7.js","./ui-renderer-CboX9P_t.js","./prefab-BQYc0LyR.js","./scene-ArUG4OfI.js","./pipeline-state-manager-DQyhxoC_.js","./node-event-DTNosVQv.js","./component-CsuvAQKv.js","./director-8iUu7HD2.js","./deprecated-Bf8XgTPJ.js","./camera-component-X7pwLmnP.js","./debug-view-BP17WHcy.js","./device-manager-B8lyUDPi.js","./buffer-barrier-q_79u36H.js","./factory-BOc5khhM.js","./deprecated-D5UVm7fE.js","./sprite-frame-n8bfYych.js","./create-mesh-o_2FMF_K.js","./mesh-Ba1cTOGw.js","./wasm-minigame-DBi57dFz.js","./zlib.min-CyXMsivM.js","./deprecated-C_Nm0tQW.js","./model-renderer-D7qfPDfZ.js","./renderer-CZheciPr.js","./touch-B157r-vS.js"],(function(t){"use strict";var e,i,n,s,r,o,h,a,l,c,p,u,m,_,d,v,f,w,b;return{setters:[function(t){e=t.a,i=t._,n=t.T,s=t.K,r=t.F},function(t){o=t.u,h=t.I,a=t.c,l=t.t,c=t.a,p=t.J,u=t.s},function(t){m=t.l,_=t.a},null,function(t){d=t.c},null,null,null,null,function(t){v=t.E,f=t.C},function(t){w=t.d},function(t){b=t.g},null,null,null,null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){var E;!function(t){t.NONE="none",t.LOADING="loading",t.LOADED="loaded",t.ERROR="error"}(E||(E={}));var y=function(){function t(t){this._componentEventList=new Map,this._state=E.NONE,this._wrapper=void 0,this._webview=null,this._loaded=!1,this._forceUpdate=!1,this._component=null,this._uiTrans=null,this._node=null,this._w=0,this._h=0,this._m00=0,this._m01=0,this._m04=0,this._m05=0,this._m12=0,this._m13=0,this._component=t,this._node=t.node,this._uiTrans=t.node.getComponent(d),this.reset(),this.createWebView()}var i=t.prototype;return i.reset=function(){this._wrapper=null,this._webview=null,this._loaded=!1,this._w=0,this._h=0,this._m00=0,this._m01=0,this._m04=0,this._m05=0,this._m12=0,this._m13=0,this._state=E.NONE,this._forceUpdate=!1},i.dispatchEvent=function(t){var e=this._componentEventList.get(t);if(e){this._state=t;for(var i=arguments.length,n=new Array(i>1?i-1:0),s=1;s<i;s++)n[s-1]=arguments[s];e.call(this,n)}},i.destroy=function(){this.removeWebView(),this._wrapper=null,this._webview=null,this._loaded=!1,this._component=null,this._uiTrans=null,this._forceUpdate=!1,this._componentEventList.clear()},e(t,[{key:"loaded",get:function(){return this._loaded}},{key:"componentEventList",get:function(){return this._componentEventList}},{key:"webview",get:function(){return this._webview}},{key:"state",get:function(){return this._state}},{key:"UICamera",get:function(){return w.root.batcher2D.getFirstRenderCamera(this._node)}}]),t}();m.internal.WebViewImpl=y;var g,L,j,O,R,D,x,I,k=_.document,T=o(),N=function(t){function e(e){return t.call(this,e)||this}i(e,t);var o=e.prototype;return o._bindDomEvent=function(){var t=this;this.webview&&this.webview.addEventListener("load",(function(e){t._forceUpdate=!0,t.dispatchEvent(E.LOADED);var i=e.target,n=i.contentDocument&&i.contentDocument.body;n&&n.innerHTML.includes("404")&&t.dispatchEvent(E.ERROR,n.innerHTML)}))},o.loadURL=function(t){this.webview&&(this.webview.src=t,this.dispatchEvent(E.LOADING))},o.createWebView=function(){var t=k.createElement("div");this._wrapper=t,t.id="webview-wrapper";var e=t.style;e["-webkit-overflow"]="auto",e["-webkit-overflow-scrolling"]="touch",e.position="absolute",e.bottom="0px",e.left="0px",e.transformOrigin="0px 100% 0px",e["-webkit-transform-origin"]="0px 100% 0px",b.container.appendChild(t);var i=k.createElement("iframe");this._webview=i;var n=i.style;i.id="webview",n.border="none",n.width="100%",n.height="100%",t.appendChild(i),this._bindDomEvent()},o.removeWebView=function(){var t=this._wrapper;n(b.container,t)&&b.container.removeChild(t),this.reset()},o.enable=function(){this._wrapper&&(this._wrapper.style.visibility="visible")},o.disable=function(){this._wrapper&&(this._wrapper.style.visibility="hidden")},o.evaluateJS=function(t){if(this.webview){var e=this.webview.contentWindow;if(e)try{e.eval(t)}catch(t){this.dispatchEvent(E.ERROR,t),s(t)}}},o.setOnJSCallback=function(){r("The platform does not support")},o.setJavascriptInterfaceScheme=function(){r("The platform does not support")},o.syncMatrix=function(){if(this._wrapper&&this._uiTrans&&this._component&&"hidden"!==this._wrapper.style.visibility){var t=this.UICamera;if(t){this._component.node.getWorldMatrix(T),t.update(!0),t.worldMatrixToScreen(T,T,b.canvas.width,b.canvas.height);var e=this._uiTrans.contentSize,i=e.width,n=e.height;if(this._forceUpdate||this._m00!==T.m00||this._m01!==T.m01||this._m04!==T.m04||this._m05!==T.m05||this._m12!==T.m12||this._m13!==T.m13||this._w!==i||this._h!==n){this._m00=T.m00,this._m01=T.m01,this._m04=T.m04,this._m05=T.m05,this._m12=T.m12,this._m13=T.m13,this._w=i,this._h=n;var s=h.devicePixelRatio,r=1/s,o=1/s,a=b.container,l=T.m00*r,c=T.m01,p=T.m04,u=T.m05*o;this._wrapper.style.width=i+"px",this._wrapper.style.height=n+"px";var m=this._w*r,_=this._h*o,d=m*T.m00*this._uiTrans.anchorX,v=_*T.m05*this._uiTrans.anchorY,f=a&&a.style.paddingLeft?parseInt(a.style.paddingLeft):0,w=a&&a.style.paddingBottom?parseInt(a.style.paddingBottom):0,E="matrix("+l+","+-c+","+-p+","+u+","+(T.m12*r-d+f)+","+-(T.m13*o-v+w)+")";this._wrapper.style.transform=E,this._wrapper.style["-webkit-transform"]=E,this._forceUpdate=!1}}}},e}(y),S=function(){function t(){}return t.getImpl=function(t){return new N(t)},t}();m.internal.WebViewImplManager=S;var A=t("WebView",(g=a("cc.WebView"),L=p(d),j=l([v]),g(O=L((I=function(t){function n(){var e;return(e=t.call(this)||this)._url=D&&D(),e._impl=null,e.webviewEvents=x&&x(),e}i(n,t);var s=n.prototype;return s.setJavascriptInterfaceScheme=function(t){this._impl&&this._impl.setJavascriptInterfaceScheme(t)},s.setOnJSCallback=function(t){this._impl&&this._impl.setOnJSCallback(t)},s.evaluateJS=function(t){this._impl&&this._impl.evaluateJS(t)},s.__preload=function(){this._impl=S.getImpl(this);var t=this._impl.componentEventList;t.set(E.LOADING,this.onLoading.bind(this)),t.set(E.LOADED,this.onLoaded.bind(this)),t.set(E.ERROR,this.onError.bind(this)),this._impl.loadURL(this._url)},s.onLoading=function(){v.emitEvents(this.webviewEvents,this,E.LOADING),this.node.emit(E.LOADING,this)},s.onLoaded=function(){v.emitEvents(this.webviewEvents,this,E.LOADED),this.node.emit(E.LOADED,this)},s.onError=function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];v.emitEvents(this.webviewEvents,this,E.ERROR,e),this.node.emit(E.ERROR,this,e)},s.onEnable=function(){this._impl&&this._impl.enable()},s.onDisable=function(){this._impl&&this._impl.disable()},s.onDestroy=function(){this._impl&&(this._impl.destroy(),this._impl=null)},s.update=function(){this._impl&&this._impl.syncMatrix()},e(n,[{key:"url",get:function(){return this._url},set:function(t){this._url=t,this._impl&&this._impl.loadURL(t)}},{key:"nativeWebView",get:function(){return this._impl&&this._impl.webview||null}},{key:"state",get:function(){return this._impl?this._impl.state:E.NONE}}]),n}(f),I.EventType=E,D=c((R=I).prototype,"_url",[u],(function(){return"https://cocos.com"})),x=c(R.prototype,"webviewEvents",[u,j],(function(){return[]})),O=R))||O)||O));m.internal.WebView=A}}}));
