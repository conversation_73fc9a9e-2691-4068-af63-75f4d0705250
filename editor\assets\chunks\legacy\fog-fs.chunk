// Copyright (c) 2017-2020 Xiamen Yaji Software Co., Ltd.

#include <legacy/fog-base>

#if !CC_USE_ACCURATE_FOG
in mediump float v_fog_factor;
#endif

#pragma define CC_FOG_FACTOR v_fog_factor

// Just to be compatible with old projects only
// Do not support accurate fog
void CC_APPLY_FOG(inout vec4 color) {
#if !CC_USE_ACCURATE_FOG
    CC_APPLY_FOG_BASE(color, CC_FOG_FACTOR);
#endif
}

void CC_APPLY_FOG(inout vec4 color, vec3 worldPos) {
#if CC_USE_ACCURATE_FOG
    float factor;
    CC_TRANSFER_FOG_BASE(vec4(worldPos, 1.0), factor);
#else
    float factor = CC_FOG_FACTOR;
#endif
    CC_APPLY_FOG_BASE(color, factor);
}
