System.register(["./global-exports-CLZKKIY2.js","./gc-object-D18ulfCO.js","./component-CsuvAQKv.js","./debug-view-BP17WHcy.js","./deprecated-D5UVm7fE.js","./scene-ArUG4OfI.js","./index-Y4La_nfG.js","./device-manager-B8lyUDPi.js","./buffer-barrier-q_79u36H.js","./deprecated-6ty78xzL.js","./pipeline-state-manager-DQyhxoC_.js","./render-types-DTmbYHic.js","./prefab-BQYc0LyR.js","./node-event-DTNosVQv.js","./deprecated-C_Nm0tQW.js","./camera-component-X7pwLmnP.js","./model-renderer-D7qfPDfZ.js","./renderer-CZheciPr.js","./factory-BOc5khhM.js","./deprecated-Bf8XgTPJ.js","./director-8iUu7HD2.js","./instantiate-BgGIVvKs.js","./touch-B157r-vS.js","./move-Bit2D-fl.js"],(function(e){"use strict";var t,r,a,n,o,i,s,u,l,g,p,c,d,f,m,b,S,T,y,h,C,I,D,v,P,R,A,_,E,M,B,O,F,j,x,L,w,N,z,U,G,V,H,k,W,Q,K,X,Y,q,J,$,Z,ee,te,re,ae,ne,oe,ie,se,ue,le,ge,pe,ce,de,fe,me,be,Se,Te,ye,he,Ce,Ie,De,ve,Pe,Re,Ae,_e,Ee,Me,Be,Oe,Fe,je,xe,Le,we,Ne,ze,Ue,Ge,Ve,He,ke,We,Qe,Ke,Xe,Ye,qe,Je,$e,Ze,et,tt,rt,at,nt,ot,it,st,ut,lt,gt,pt,ct,dt,ft,mt,bt,St,Tt,yt,ht,Ct,It,Dt,vt,Pt,Rt,At,_t,Et,Mt,Bt,Ot,Ft,jt,xt,Lt,wt,Nt,zt,Ut,Gt,Vt,Ht,kt,Wt,Qt,Kt,Xt,Yt,qt,Jt,$t,Zt,er,tr,rr,ar,nr,or,ir,sr,ur,lr,gr,pr,cr,dr,fr,mr,br,Sr,Tr,yr,hr,Cr,Ir,Dr,vr,Pr,Rr,Ar,_r,Er,Mr,Br,Or,Fr,jr,xr,Lr,wr,Nr,zr,Ur,Gr,Vr,Hr,kr,Wr,Qr,Kr,Xr,Yr,qr,Jr,$r,Zr,ea,ta,ra,aa,na,oa,ia,sa,ua,la,ga,pa,ca,da,fa,ma,ba,Sa,Ta,ya,ha,Ca,Ia,Da,va,Pa,Ra;return{setters:[function(r){t=r.l,e({VERSION:r.e,cclegacy:r.c})},function(t){r=t.a6,a=t.P,n=t.a5,o=t.G,i=t.F,s=t.K,u=t.X,l=t.a7,g=t.Q,p=t.w,c=t.h,d=t.I,f=t.a8,m=t.o,e({AsyncDelegate:t.a2,BITMASK_TAG:t.ak,BitMask:t.B,CCBoolean:t.C,CCClass:t.ac,CCFloat:t.n,CCInteger:t.l,CCObject:t.J,CCObjectFlags:t.d,CCString:t.f,CachedArray:t.a6,CallbacksInvoker:t.al,DebugMode:t.$,ENUM_TAG:t.aj,Enum:t.E,EventTarget:t.m,Eventify:t.Y,GCObject:t.ag,Pool:t.P,RecyclePool:t.a5,Settings:t.M,SettingsCategory:t.S,ValueType:t.V,_resetDebugSetting:t.a0,assert:t.X,assertID:t.I,assertIsNonNullable:t.O,assertIsTrue:t.k,assertsArrayIndex:t.c,bits:t.a9,ccenum:t.r,debug:t.x,debugID:t.ad,error:t.K,errorID:t.h,garbageCollectionManager:t.a1,getError:t.g,isCCClassOrFastDefined:t.e,isCCObject:t.i,isDisplayStats:t.ae,isValid:t.D,js:t.aa,log:t.G,logID:t.Q,macro:t.z,misc:t.ab,setDisplayStats:t.af,setPropertyEnumType:t.ah,setPropertyEnumTypeOnAttrs:t.ai,settings:t.s,warn:t.F,warnID:t.w})},function(t){b=t.j,S=t.e,T=t.m,y=t.b,h=t.d,C=t.a,I=t.c,D=t._,v=t.s,P=t.g,e({Asset:t.A,Component:t.C,EventHandler:t.E,JavaScript:t.J,Script:t.S,TypeScript:t.T,path:t.p})},function(t){R=t.f,A=t.b,_=t.a,E=t.d,M=t.C,B=t.c,O=t.e,F=t.j,j=t.k,x=t.l,L=t.L,w=t.O,N=t.n,z=t.o,U=t.p,G=t.q,V=t.s,H=t.S,k=t.T,W=t.t,e({DebugView:t.D,PipelineEventProcessor:t.h,PipelineEventType:t.P,PipelineSceneData:t.u,RenderTexture:t.R,RenderingSubMesh:t.g,SceneAsset:t.i})},function(t){Q=t.D,K=t.L,X=t.e,Y=t.M,q=t.d,J=t.P,$=t.f,Z=t.g,ee=t.S,te=t.b,re=t.a,ae=t.c,e({AssetLibrary:t.A,CCLoader:t.C,loader:t.l,url:t.u})},function(t){ne=t.A,oe=t.C,ie=t.l,se=t.m,ue=t.F,le=t.n,ge=t.o,pe=t.p,ce=t.q,de=t.r,fe=t.s,me=t.S,be=t.t,Se=t.u,Te=t.B,ye=t.M,he=t.P,Ce=t.v,Ie=t.w,De=t.x,ve=t.y,Pe=t.z,Re=t.D,Ae=t.G,_e=t.H,Ee=t.I,Me=t.J,Be=t.K,Oe=t.L,Fe=t.O,je=t.Q,xe=t.R,e({AmbientInfo:t.W,AssetManager:t.a7,BaseNode:t.N,BuiltinResMgr:t.a8,DEFAULT_OCTREE_DEPTH:t.a0,DEFAULT_WORLD_MAX_POS:t.$,DEFAULT_WORLD_MIN_POS:t._,EffectAsset:t.E,FogInfo:t.Y,InstancedBuffer:t.U,LightProbeInfo:t.a4,Material:t.b,MobilityMode:t.c,Node:t.N,NodeSpace:t.V,OctreeInfo:t.a1,PostSettingsInfo:t.a3,Scene:t.j,SceneGlobals:t.a5,ShadowsInfo:t.Z,SkinInfo:t.a2,SkyboxInfo:t.X,Texture2D:t.h,TextureCube:t.a6,TransformBit:t.T,assetManager:t.i,builtinResMgr:t.d,getPhaseID:t.g})},function(t){e({AlphaKey:t.aM,Color:t.C,ColorKey:t.aL,CompactValueTypeArray:t.ay,EPSILON:t.L,EasingMethod:t.aZ,EditorExtendable:t.aD,ExtrapolationMode:t.aG,Gradient:t.aN,HALF_PI:t.a8,MATH_FLOAT_ARRAY:t.ap,Mat3:t.K,Mat4:t.M,MathBase:t.aq,ObjectCurve:t.aK,Quat:t.Q,QuatCurve:t.aI,QuatInterpolationMode:t.aJ,RealCurve:t.aE,RealInterpolationMode:t.aF,Rect:t.k,Scheduler:t.A,Size:t.z,System:t.S,SystemPriority:t.a2,TWO_PI:t.a9,TangentWeightMode:t.aH,Vec2:t.V,Vec3:t.b,Vec4:t.e,WorldNode3DToLocalNodeUI:t.au,WorldNode3DToWorldNodeUI:t.av,__checkObsoleteInNamespace__:t.at,__checkObsolete__:t.as,_decorator:t.ax,absMax:t.am,absMaxComponent:t.al,approx:t.$,bezier:t.aO,bezierByTime:t.aP,binarySearch:t.a_,binarySearchBy:t.b0,binarySearchEpsilon:t.a$,clamp:t.g,clamp01:t.O,color:t.a7,convertUtils:t.aw,deprecateModuleExportedName:t.U,deserializeTag:t.aA,disallowAnimation:t.aY,displayName:t.aT,displayOrder:t.aU,easing:t.a3,editable:t.aQ,editorExtrasTag:t.az,enumerableProps:t.an,equals:t.aa,floatToHalf:t.ao,formerlySerializedAs:t.j,geometry:t.a5,getSerializationMetadata:t.aC,halfToFloat:t.G,inverseLerp:t.ak,lerp:t.ab,markAsWarning:t.o,mat4:t.u,math:t.a4,nextPow2:t.D,override:t.H,pingPong:t.aj,preTransforms:t.l,pseudoRandom:t.af,pseudoRandomRange:t.ag,pseudoRandomRangeInt:t.ah,quat:t.a6,random:t.W,randomRange:t.ad,randomRangeInt:t.ae,range:t.aV,rangeStep:t.aW,rect:t.w,removeProperty:t.h,repeat:t.ai,replaceProperty:t.r,screen:t.B,serializable:t.s,serializeTag:t.aB,setDefaultLogTimes:t.ar,setRandGenerator:t.ac,size:t.y,slide:t.aX,sys:t.m,toDegree:t.X,toRadian:t.f,tooltip:t.aR,v2:t.v,v3:t.q,v4:t.x,visible:t.aS,visibleRect:t.N})},function(e){Le=e.D,we=e.L,Ne=e.R,ze=e.d},function(t){Ue=t.r,Ge=t.ak,Ve=t.ae,He=t.A,ke=t.a,We=t.al,Qe=t.n,Ke=t.af,Xe=t.ai,Ye=t.a4,qe=t.a5,Je=t.H,$e=t.am,Ze=t.an,et=t.B,tt=t.f,rt=t.b,at=t.ao,nt=t.C,ot=t.ab,it=t.w,st=t.aj,ut=t._,lt=t.ap,gt=t.aq,pt=t.ag,ct=t.ar,dt=t.as,ft=t.at,mt=t.au,bt=t.av,St=t.aw,Tt=t.ax,yt=t.x,ht=t.a6,Ct=t.W,It=t.ay,Dt=t.X,vt=t.g,Pt=t.az,Rt=t.h,At=t.E,_t=t.aA,Et=t.a9,Mt=t.aB,Bt=t.aC,Ot=t.D,Ft=t.aD,jt=t.aE,xt=t.aF,Lt=t.aG,wt=t.a7,Nt=t.v,zt=t.F,Ut=t.o,Gt=t.aH,Vt=t.c,Ht=t.aI,kt=t.aJ,Wt=t.aa,Qt=t.Q,Kt=t.aK,Xt=t.aL,Yt=t.a0,qt=t.aM,Jt=t.aN,$t=t.aO,Zt=t.N,er=t.I,tr=t.s,rr=t.aP,ar=t.aQ,nr=t.aR,or=t.l,ir=t.aS,sr=t.M,ur=t.aT,lr=t.aU,gr=t.aV,pr=t.aW,cr=t.Y,dr=t.aX,fr=t.Z,mr=t.t,br=t.aY,Sr=t.P,Tr=t.aZ,yr=t.a_,hr=t.$,Cr=t.a$,Ir=t.b0,Dr=t.a3,vr=t.ac,Pr=t.O,Rr=t.R,Ar=t.b1,_r=t.b2,Er=t.q,Mr=t.b3,Br=t.K,Or=t.ad,Fr=t.b4,jr=t.L,xr=t.b5,Lr=t.b6,wr=t.S,Nr=t.b7,zr=t.b8,Ur=t.b9,Gr=t.ah,Vr=t.y,Hr=t.ba,kr=t.bb,Wr=t.bc,Qr=t.G,Kr=t.a8,Xr=t.J,Yr=t.a1,qr=t.bd,Jr=t.be,$r=t.bf,Zr=t.p,ea=t.T,ta=t.bg,ra=t.bh,aa=t.d,na=t.e,oa=t.bi,ia=t.j,sa=t.i,ua=t.U,la=t.bj,ga=t.bk,pa=t.k,ca=t.bl,da=t.bm,fa=t.bn,ma=t.V,ba=t.bo,Sa=t.bp,Ta=t.bq,ya=t.br,ha=t.u,e("murmurhash2_32_gc",t.m)},function(e){Ca=e.C,Ia=e.P,Da=e.R,va=e.T,Pa=e.V,Ra=e.n},function(t){e({Layers:t.L,PipelineStateManager:t.P,pipeline:t.r})},function(t){e("PipelineInputAssemblerData",t.P)},function(t){e({Acceleration:t.A,Input:t.I,NodeActivator:t.N,Prefab:t.P,PrivateNode:t.a,SystemEvent:t.S,find:t.f,input:t.i,systemEvent:t.s})},function(t){e("NodeEventType",t.N)},function(t){e("PrefabLink",t.P)},function(t){e({Camera:t.C,CameraComponent:t.C})},function(t){e({ModelRenderer:t.M,RenderableComponent:t.M})},function(t){e("Renderer",t.R)},function(t){e({BufferAsset:t.B,Details:t.D,ImageAsset:t.I,JsonAsset:t.J,MissingScript:t.M,TextAsset:t.a,deserialize:t.b,resources:t.c})},function(t){e({Game:t.G,game:t.g})},function(t){e({Director:t.a,DirectorEvent:t.D,Root:t.R,director:t.d})},function(t){e("instantiate",t.i)},function(t){e({Event:t.E,EventAcceleration:t.a,EventGamepad:t.e,EventHMD:t.g,EventHandheld:t.h,EventHandle:t.f,EventKeyboard:t.b,EventMouse:t.c,EventTouch:t.d,KeyCode:t.K,SystemEventType:t.S,Touch:t.T})},function(t){e("shift",t.s)}],execute:function(){function Aa(e,t){for(var r,a=m(t);!(r=a()).done;){var n=r.value;Array.isArray(n)?Aa(e,n):e.push(n)}}e({applyMixins:function(e,t){t.forEach((function(t){Object.getOwnPropertyNames(t.prototype).forEach((function(r){"constructor"!==r&&Object.defineProperty(e.prototype,r,Object.getOwnPropertyDescriptor(t.prototype,r))}))}))},flattenCodeArray:function(e){var t=[];return Aa(t,e),t.join("")}}),e("memop",Object.freeze({__proto__:null,CachedArray:r,Pool:a,RecyclePool:n})),t.log=o,t.warn=i,t.error=s,t.assert=u,t._throw=l,t.logID=g,t.warnID=p,t.errorID=c,t.assertID=d,t.debug=f,t.path={join:b,extname:S,mainFileName:T,basename:y,dirname:h,changeExtname:C,changeBasename:I,_normalize:D,stripSep:v,get sep(){return P()}},e("gfx",Object.freeze({__proto__:null,get API(){return Ue},get AccessFlagBit(){return Ge},get Address(){return Ve},Attribute:He,get AttributeName(){return ke},get BarrierType(){return We},BindingMappingInfo:Qe,get BlendFactor(){return Ke},get BlendOp(){return Xe},BlendState:Ye,BlendTarget:qe,Buffer:Je,BufferBarrierInfo:$e,get BufferFlagBit(){return Ze},BufferInfo:et,BufferTextureCopy:tt,get BufferUsageBit(){return rt},BufferViewInfo:at,get ClearFlagBit(){return nt},Color:ot,ColorAttachment:it,get ColorMask(){return st},CommandBuffer:ut,CommandBufferInfo:lt,get CommandBufferType(){return gt},get ComparisonFunc(){return pt},get CullMode(){return ct},DESCRIPTOR_BUFFER_TYPE:dt,DESCRIPTOR_DYNAMIC_TYPE:ft,DESCRIPTOR_SAMPLER_TYPE:mt,DESCRIPTOR_STORAGE_BUFFER_TYPE:bt,DRAW_INFO_SIZE:St,DefaultResource:Tt,DepthStencilAttachment:yt,DepthStencilState:ht,DescriptorSet:Ct,DescriptorSetInfo:It,DescriptorSetLayout:Dt,DescriptorSetLayoutBinding:vt,DescriptorSetLayoutInfo:Pt,get DescriptorType(){return Rt},Device:At,DeviceCaps:_t,DeviceInfo:Et,DeviceManager:Le,DeviceOptions:Mt,DispatchInfo:Bt,DrawInfo:Ot,get DynamicStateFlagBit(){return Ft},DynamicStates:jt,DynamicStencilStates:xt,Extent:Lt,get Feature(){return wt},get Filter(){return Nt},get Format(){return zt},get FormatFeatureBit(){return Ut},FormatInfo:Gt,FormatInfos:Vt,FormatSize:Ht,FormatSurfaceSize:kt,get FormatType(){return Wt},Framebuffer:Qt,FramebufferInfo:Kt,GFXObject:Xt,GeneralBarrier:Yt,GeneralBarrierInfo:qt,GetTypeSize:Jt,IndirectBuffer:$t,InputAssembler:Zt,InputAssemblerInfo:er,InputState:tr,IsPowerOf2:rr,get LegacyRenderMode(){return we},get LoadOp(){return ar},MarkerInfo:nr,get MemoryAccessBit(){return or},MemoryStatus:ir,get MemoryUsageBit(){return sr},get ObjectType(){return ur},Offset:lr,get PassType(){return gr},get PipelineBindPoint(){return pr},PipelineLayout:cr,PipelineLayoutInfo:dr,PipelineState:fr,PipelineStateInfo:mr,get PolygonMode(){return br},get PrimitiveMode(){return Sr},QueryPoolInfo:Tr,get QueryType(){return yr},Queue:hr,QueueInfo:Cr,get QueueType(){return Ir},RasterizerState:Dr,Rect:vr,RenderPass:Pr,RenderPassInfo:Rr,get RenderType(){return Ne},get ResolveMode(){return Ar},ResourceRange:_r,get SampleCount(){return Er},get SampleType(){return Mr},Sampler:Br,SamplerInfo:Or,get ShadeModel(){return Fr},Shader:jr,ShaderInfo:xr,ShaderStage:Lr,get ShaderStageFlagBit(){return wr},Size:Nr,get Status(){return zr},get StencilFace(){return Ur},get StencilOp(){return Gr},get StoreOp(){return Vr},SubpassDependency:Hr,SubpassInfo:kr,get SurfaceTransform(){return Wr},Swapchain:Qr,SwapchainInfo:Kr,Texture:Xr,TextureBarrier:Yr,TextureBarrierInfo:qr,TextureBlit:Jr,TextureCopy:$r,get TextureFlagBit(){return Zr},TextureInfo:ea,TextureSubresLayers:ta,TextureSubresRange:ra,get TextureType(){return aa},get TextureUsageBit(){return na},TextureViewInfo:oa,get Type(){return ia},Uniform:sa,UniformBlock:ua,UniformInputAttachment:la,UniformSampler:ga,UniformSamplerTexture:pa,UniformStorageBuffer:ca,UniformStorageImage:da,UniformTexture:fa,get ViewDimension(){return ma},Viewport:ba,get VsyncMode(){return Sa},alignTo:Ta,deviceManager:ze,formatAlignment:ya,getTypedArrayConstructor:ha}));var _a=Object.freeze({__proto__:null,Ambient:ne,CSMLevel:oe,CSMOptimizationMode:ie,Camera:R,get CameraAperture(){return A},get CameraFOVAxis(){return _},get CameraISO(){return E},get CameraProjection(){return M},get CameraShutter(){return B},get CameraType(){return O},get CameraUsage(){return F},ColorTemperatureToRGB:j,DirectionalLight:Q,get EnvironmentLightingType(){return se},FOG_TYPE_NONE:ue,Fog:le,FogType:ge,LODData:K,LODGroup:X,Light:x,get LightType(){return L},Model:Y,get ModelType(){return q},Octree:w,PCFType:pe,PointLight:J,PostSettings:ce,get ProbeClearFlag(){return N},get ProbeType(){return z},RangedDirectionalLight:$,ReflectionProbe:U,SKYBOX_FLAG:G,ShadowSize:de,ShadowType:fe,Shadows:me,Skin:V,get SkyBoxFlagValue(){return H},Skybox:be,SphereLight:Z,SpotLight:ee,SubModel:te,ToneMappingType:Se,get TrackingType(){return k},nt2lm:W}),Ea=Object.freeze({__proto__:null,get BatchingSchemes(){return Te},CameraVisFlags:Ca,MaterialInstance:ye,Pass:he,PassInstance:Ce,get PassStage(){return Ia},get RenderQueue(){return Da},RenderScene:re,RenderWindow:ae,TextureBufferPool:va,VisibilityFlags:Pa,createIA:function(e,t){if(!t.positions)return c(16306),null;for(var r=[],a=t.positions.length/3,n=0;n<a;++n)r.push(t.positions[3*n],t.positions[3*n+1],t.positions[3*n+2]),t.normals&&r.push(t.normals[3*n],t.normals[3*n+1],t.normals[3*n+2]),t.uvs&&r.push(t.uvs[2*n],t.uvs[2*n+1]),t.colors&&r.push(t.colors[3*n],t.colors[3*n+1],t.colors[3*n+2]);var o=[];o.push(new He(ke.ATTR_POSITION,zt.RGB32F)),t.normals&&o.push(new He(ke.ATTR_NORMAL,zt.RGB32F)),t.uvs&&o.push(new He(ke.ATTR_TEX_COORD,zt.RG32F)),t.colors&&o.push(new He(ke.ATTR_COLOR,zt.RGB32F));var i=e.createBuffer(new et(rt.VERTEX|rt.TRANSFER_DST,sr.DEVICE,4*r.length,4*r.length/a));i.update(new Float32Array(r));var s=null;return t.indices&&(s=e.createBuffer(new et(rt.INDEX|rt.TRANSFER_DST,sr.DEVICE,2*t.indices.length,2))).update(new Uint16Array(t.indices)),e.createInputAssembler(new er(o,[i],s))},customizeType:Ie,genHandle:De,getBindingFromHandle:ve,getCountFromHandle:Pe,getDefaultFromType:Re,getDeviceShaderVersion:Ae,getOffsetFromHandle:_e,getStringFromType:Ee,getTypeFromHandle:Me,nearestPOT:Ra,overrideMacros:Be,programLib:Oe,scene:_a,type2reader:Fe,type2validator:je,type2writer:xe});e("renderer",Ea);var Ma,Ba,Oa=e("NodePool",function(){function e(e){this._pool=[],this.poolHandlerComp=e}var t=e.prototype;return t.size=function(){return this._pool.length},t.clear=function(){for(var e=this._pool.length,t=0;t<e;++t)this._pool[t].destroy();this._pool.length=0},t.put=function(e){if(e&&-1===this._pool.indexOf(e)){e.removeFromParent();var t=this.poolHandlerComp?e.getComponent(this.poolHandlerComp):null;t&&t.unuse&&t.unuse(),this._pool.push(e)}},t.get=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var a=this._pool.length-1;if(a<0)return null;var n=this._pool[a];this._pool.length=a;var o=this.poolHandlerComp?n.getComponent(this.poolHandlerComp):null;return o&&o.reuse&&o.reuse(arguments),n},e}());t.NodePool=Oa;var Fa=null!==(Ma=globalThis.jsb)&&void 0!==Ma?Ma:{};e("native",{DownloaderHints:Fa.DownloaderHints,Downloader:Fa.Downloader,zipUtils:Fa.zipUtils,fileUtils:Fa.fileUtils,DebugRenderer:Fa.DebugRenderer,copyTextToClipboard:null==(Ba=Fa.copyTextToClipboard)?void 0:Ba.bind(Fa),garbageCollect:Fa.garbageCollect,reflection:Fa.reflection,bridge:Fa.bridge,jsbBridgeWrapper:Fa.jsbBridgeWrapper,AssetsManager:Fa.AssetsManager,EventAssetsManager:Fa.EventAssetsManager,Manifest:Fa.Manifest,saveImageData:Fa.saveImageData,process:Fa.process,adpf:Fa.adpf}),t.renderer=Ea}}}));
