System.register(["./gc-object-D18ulfCO.js","./global-exports-CLZKKIY2.js","./debug-view-BP17WHcy.js","./index-Y4La_nfG.js","./prefab-BQYc0LyR.js","./deprecated-D5UVm7fE.js","./scene-ArUG4OfI.js","./device-manager-B8lyUDPi.js","./buffer-barrier-q_79u36H.js","./pipeline-state-manager-DQyhxoC_.js","./node-event-DTNosVQv.js","./component-CsuvAQKv.js","./factory-BOc5khhM.js"],(function(e){"use strict";var t,i,n,s,r,o,a,_,c,h,u,l,d,E,f,p,m,g,R,v,T,N,S,y,I,P,A,D,w,F,C,b,M,L,O,W,H,B,V,k,G,U,x,j,z,X,Y,J,q,K,Q,Z;return{setters:[function(e){t=e.s,i=e.S,n=e.h,s=e.G,r=e.z,o=e.P,a=e.w,_=e.a,c=e.o,h=e.H,u=e.y,l=e._,d=e.D,E=e.I,f=e.d,p=e.J,m=e.K,g=e.L,R=e.m},function(e){v=e.c},function(e){T=e.L,N=e.h,S=e.D,y=e.f,I=e.T,P=e.e,A=e.i},function(e){D=e.A,w=e.S},function(e){F=e.i,C=e.C,b=e.N},function(e){M=e.a,L=e.c},function(e){O=e.e,W=e.i,H=e.N,B=e.j},function(e){V=e.d,k=e.L},function(e){G=e.w,U=e.x,x=e.y,j=e.R},function(e){z=e.U,X=e.a,Y=e.b,J=e.c,q=e.d,K=e.l},function(e){Q=e.N},null,function(e){Z=e.r}],execute:function(){var $,ee,te;e("X",$),function(e){e[e.NONE=-1]="NONE",e[e.LEFT=0]="LEFT",e[e.RIGHT=1]="RIGHT"}($||e("X",$={})),function(e){e[e.SESSION_RUNNING=2]="SESSION_RUNNING",e[e.VIEW_COUNT=6]="VIEW_COUNT",e[e.SWAPCHAIN_WIDTH=7]="SWAPCHAIN_WIDTH",e[e.SWAPCHAIN_HEIGHT=8]="SWAPCHAIN_HEIGHT",e[e.DEVICE_IPD=37]="DEVICE_IPD",e[e.SPLIT_AR_GLASSES=42]="SPLIT_AR_GLASSES"}(ee||(ee={})),function(e){e[e.VIEW_LEFT=0]="VIEW_LEFT",e[e.HAND_LEFT=1]="HAND_LEFT",e[e.AIM_LEFT=2]="AIM_LEFT",e[e.VIEW_RIGHT=3]="VIEW_RIGHT",e[e.HAND_RIGHT=4]="HAND_RIGHT",e[e.AIM_RIGHT=5]="AIM_RIGHT",e[e.HEAD_MIDDLE=6]="HEAD_MIDDLE"}(te||(te={}));var ie=e("R",function(){function e(e){var t=this;this._createSceneFun=null,this._createWindowFun=null,this._windows=[],this._mainWindow=null,this._curWindow=null,this._tempWindow=null,this._usesCustomPipeline=!0,this._pipeline=null,this._pipelineEvent=new N,this._classicPipeline=null,this._customPipeline=null,this._batcher=null,this._scenes=[],this._modelPools=new Map,this._cameraPool=null,this._lightPools=new Map,this._debugView=new S,this._fpsTime=0,this._frameCount=0,this._fps=0,this._fixedFPS=0,this._useDeferredPipeline=!1,this._cumulativeTime=0,this._frameTime=0,this._cameraList=[],this._device=e,this._dataPoolMgr=v.internal.DataPoolManager&&new v.internal.DataPoolManager(e),M.registerCreateFunc(this),L.registerCreateFunc(this),this._cameraPool=new o((function(){return new y(t._device)}),4,(function(e){return e.destroy()}))}var h=e.prototype;return h.initialize=function(){var e,n=V.swapchain,s=new G;s.format=n.colorTexture.format;var r=new U;r.format=n.depthStencilTexture.format,r.depthStoreOp=x.DISCARD,r.stencilStoreOp=x.DISCARD;var o=new j([s],r);this._mainWindow=this.createWindow({title:"rootMainWindow",width:n.width,height:n.height,renderPassInfo:o,swapchain:n}),this._curWindow=this._mainWindow;var a=t.querySettings(i.ANIMATION,"customJointTextureLayouts")||[];null==(e=this._dataPoolMgr)||e.jointTexturePool.registerCustomTextureLayouts(a),this._resizeMaxJointForDS()},h.destroy=function(){this.destroyScenes(),this._pipeline&&(this._pipeline.destroy(),this._pipeline=null,this._pipelineEvent=null),this._batcher&&(this._batcher.destroy(),this._batcher=null),this._curWindow=null,this._mainWindow=null,this.dataPoolManager.clear(),v.rendering&&v.rendering.destroy()},h.resize=function(e,t){this._windows.forEach((function(i){i.swapchain&&i.resize(e,t)}))},h.setRenderPipeline=function(e){var o=v.internal,a=v.director,_=v.rendering,c=v.legacy_rendering;if(void 0===_&&void 0===c)return n(1223),!1;var h=!1;if(e)this._customPipeline=_.createCustomPipeline(),h=!0,this._pipeline=this._customPipeline,s("Using custom pipeline: "+r.CUSTOM_PIPELINE_NAME);else{var u=c.createDefaultPipeline();h=!0,s("Using legacy pipeline"),this._classicPipeline=u,this._pipeline=this._classicPipeline,this._pipelineEvent=this._classicPipeline,this._usesCustomPipeline=!1}if((t.querySettings(i.RENDERING,"renderMode")!==k.HEADLESS||this._classicPipeline)&&!this._pipeline.activate(this._mainWindow.swapchain))return h&&this._pipeline.destroy(),this._classicPipeline=null,this._customPipeline=null,this._pipeline=null,this._pipelineEvent=null,!1;var l=a.getScene();return l&&l.globals.activate(),this.onGlobalPipelineStateChanged(),!(!this._batcher&&o.Batcher2D&&(this._batcher=new o.Batcher2D(this),!this._batcher.initialize())&&(this.destroy(),1))},h.onGlobalPipelineStateChanged=function(){for(var e=0;e<this._scenes.length;e++)this._scenes[e].onGlobalPipelineStateChanged();O().skybox.enabled&&O().skybox.model.onGlobalPipelineStateChanged(),this._pipeline.onGlobalPipelineStateChanged()},h.activeWindow=function(e){this._curWindow=e},h.resetCumulativeTime=function(){this._cumulativeTime=0},h.frameMove=function(e){var t;this._frameTime=e,++this._frameCount,this._cumulativeTime+=e,this._fpsTime+=e,this._fpsTime>1&&(this._fps=this._frameCount,this._frameCount=0,this._fpsTime=0),null!=(t=globalThis.__globalXR)&&t.isWebXR?this._doWebXRFrameMove():(this._frameMoveBegin(),this._frameMoveProcess(),this._frameMoveEnd())},h.createWindow=function(e){var t=this._createWindowFun(this);return t.initialize(this.device,e),this._windows.push(t),t},h.destroyWindow=function(e){for(var t=0;t<this._windows.length;++t)if(this._windows[t]===e)return e.destroy(),void this._windows.splice(t,1)},h.destroyWindows=function(){this._windows.forEach((function(e){e.destroy()})),this._windows.length=0},h.createScene=function(e){var t=this._createSceneFun(this);return t.initialize(e),this._scenes.push(t),t},h.destroyScene=function(e){for(var t=0;t<this._scenes.length;++t)if(this._scenes[t]===e)return e.destroy(),void this._scenes.splice(t,1)},h.destroyScenes=function(){this._scenes.forEach((function(e){e.destroy()})),this._scenes.length=0},h.createModel=function(e){var t=this._modelPools.get(e);t||(this._modelPools.set(e,new o((function(){return new e}),10,(function(e){return e.destroy()}))),t=this._modelPools.get(e));var i=t.alloc();return i.initialize(),i},h.destroyModel=function(e){var t=this._modelPools.get(e.constructor);t?(t.free(e),e.scene&&e.scene.removeModel(e)):a(1300,e.constructor.name),e.destroy()},h.createCamera=function(){return this._cameraPool.alloc()},h.createLight=function(e){var t=this._lightPools.get(e);t||(this._lightPools.set(e,new o((function(){return new e}),4,(function(e){return e.destroy()}))),t=this._lightPools.get(e));var i=t.alloc();return i.initialize(),i},h.destroyLight=function(e){if(e.scene)switch(e.type){case T.DIRECTIONAL:e.scene.removeDirectionalLight(e);break;case T.SPHERE:e.scene.removeSphereLight(e);break;case T.SPOT:e.scene.removeSpotLight(e);break;case T.POINT:e.scene.removePointLight(e);break;case T.RANGED_DIRECTIONAL:e.scene.removeRangedDirLight(e)}e.destroy()},h.recycleLight=function(e){var t=this._lightPools.get(e.constructor);if(t&&(t.free(e),e.scene))switch(e.type){case T.DIRECTIONAL:e.scene.removeDirectionalLight(e);break;case T.SPHERE:e.scene.removeSphereLight(e);break;case T.SPOT:e.scene.removeSpotLight(e);break;case T.POINT:e.scene.removePointLight(e);break;case T.RANGED_DIRECTIONAL:e.scene.removeRangedDirLight(e)}},h._doWebXRFrameMove=function(){var e=this,t=globalThis.__globalXR;if(t){var i=this._windows,n=this._cameraList,s=t.webXRMatProjs?t.webXRMatProjs.length:1;t.webXRWindowMap||(t.webXRWindowMap=new Map);for(var r=[],o=t.webxrHmdPoseInfos,a=function(){for(var s,a=c(i);!(s=a()).done;){var h=s.value;r=r.concat(h.cameras),h.swapchain&&t.webXRWindowMap.set(h,_)}if(o){for(var u=[0,0,0],l=0;l<o.length;l++){var d=o[l];if(d.code===te.VIEW_LEFT&&_===$.LEFT||d.code===te.VIEW_RIGHT&&_===$.RIGHT){u[0]=d.position.x,u[1]=d.position.y,u[2]=d.position.z;break}}r.forEach((function(e){e.trackingType!==I.NO_TRACKING&&e.node&&(e.trackingType===I.ROTATION&&(u=[0,0,0]),e.node.setPosition(u[0],u[1],u[2]))}))}r.length=0,e._frameMoveBegin(),e._frameMoveProcess();for(var E=n.length-1;E>=0;E--){var f=n[E];(_===$.LEFT&&f.cameraType===P.RIGHT_EYE||_===$.RIGHT&&f.cameraType===P.LEFT_EYE)&&n.splice(E,1)}e._frameMoveEnd()},_=0;_<s;_++)a()}},h._frameMoveBegin=function(){for(var e=0;e<this._scenes.length;++e)this._scenes[e].removeBatches();this._cameraList.length=0},h._frameMoveProcess=function(){for(var e=v.director,t=this._windows,i=this._cameraList,n=0;n<t.length;n++)t[n].extractRenderCameras(i);if(this._pipeline&&i.length>0){this._device.acquire([V.swapchain]);var s=this._scenes,r=e.getTotalFrames();this._batcher&&(this._batcher.update(),this._batcher.uploadBuffers());for(var o=0;o<s.length;o++)s[o].update(r)}},h._frameMoveEnd=function(){var e=v.director,t=v.Director,i=this._cameraList;if(this._pipeline&&i.length>0){e.emit(t.EVENT_BEFORE_COMMIT),i.sort((function(e,t){return e.priority-t.priority}));for(var n=0;n<i.length;++n){var s;null==(s=i[n].geometryRenderer)||s.update()}e.emit(t.EVENT_BEFORE_RENDER),this._pipeline.render(i),e.emit(t.EVENT_AFTER_RENDER),this._device.present()}this._batcher&&this._batcher.reset()},h._resizeMaxJointForDS=function(){var e=Math.max((z.COUNT+X.COUNT+Y.COUNT+J.COUNT+q.COUNT)/4,100),t=Math.floor((V.gfxDevice.capabilities.maxVertexUniformVectors-e)/3);K(t=t<256?t:256)},_(e,[{key:"device",get:function(){return this._device}},{key:"mainWindow",get:function(){return this._mainWindow}},{key:"curWindow",get:function(){return this._curWindow},set:function(e){this._curWindow=e}},{key:"tempWindow",get:function(){return this._tempWindow},set:function(e){this._tempWindow=e}},{key:"windows",get:function(){return this._windows}},{key:"usesCustomPipeline",get:function(){return this._usesCustomPipeline}},{key:"pipeline",get:function(){return this._pipeline}},{key:"customPipeline",get:function(){return this._customPipeline}},{key:"pipelineEvent",get:function(){return this._pipelineEvent}},{key:"batcher2D",get:function(){return this._batcher}},{key:"scenes",get:function(){return this._scenes}},{key:"debugView",get:function(){return this._debugView}},{key:"cumulativeTime",get:function(){return this._cumulativeTime}},{key:"frameTime",get:function(){return this._frameTime}},{key:"frameCount",get:function(){return this._frameCount}},{key:"fps",get:function(){return this._fps}},{key:"fixedFPS",get:function(){return this._fixedFPS},set:function(e){e>0&&(this._fixedFPS=e)}},{key:"dataPoolManager",get:function(){return this._dataPoolMgr}},{key:"useDeferredPipeline",get:function(){return this._useDeferredPipeline}},{key:"cameraList",get:function(){return this._cameraList}}]),e}());v.Root=ie;var ne,se=function(){function e(){this._allRenderers=[],this._dirtyRenderers=[],this._dirtyVersion=0}var t=e.prototype;return t.addRenderer=function(e){-1===e._internalId&&(e._internalId=this._allRenderers.length,this._allRenderers.push(e))},t.removeRenderer=function(e){if(-1!==e._internalId){var t=e._internalId;this._allRenderers[this._allRenderers.length-1]._internalId=t,h(this._allRenderers,t),e._internalId=-1,e._dirtyVersion===this._dirtyVersion&&(u(this._dirtyRenderers,e),e._dirtyVersion=-1)}},t.markDirtyRenderer=function(e){e._dirtyVersion!==this._dirtyVersion&&-1!==e._internalId&&(this._dirtyRenderers.push(e),e._dirtyVersion=this._dirtyVersion)},t.updateAllDirtyRenderers=function(){for(var e=this._dirtyRenderers,t=0;t<this._dirtyRenderers.length;t++)e[t].updateRenderer();this._dirtyRenderers.length=0,this._dirtyVersion++},e}(),re=e("u",new se);e("D",ne),function(e){e.INIT="director_init",e.RESET="director_reset",e.BEFORE_SCENE_LOADING="director_before_scene_loading",e.BEFORE_SCENE_LAUNCH="director_before_scene_launch",e.AFTER_SCENE_LAUNCH="director_after_scene_launch",e.BEFORE_UPDATE="director_before_update",e.AFTER_UPDATE="director_after_update",e.BEFORE_DRAW="director_before_draw",e.AFTER_DRAW="director_after_draw",e.BEFORE_COMMIT="director_before_commit",e.BEFORE_RENDER="director_before_render",e.AFTER_RENDER="director_after_render",e.BEFORE_PHYSICS="director_before_physics",e.AFTER_PHYSICS="director_after_physics",e.BEGIN_FRAME="director_begin_frame",e.END_FRAME="director_end_frame"}(ne||e("D",ne={}));var oe=e("a",function(e){function t(){var t;return(t=e.call(this)||this)._compScheduler=new C,t._nodeActivator=new b,t._invalid=!1,t._paused=!1,t._root=null,t._loadingScene="",t._scene=null,t._totalFrames=0,t._scheduler=new D,t._systems=[],t._persistRootNodes={},t}l(t,e);var i=t.prototype;return i.end=function(){var e=this;this.once(ne.END_FRAME,(function(){e.purgeDirector()}))},i.pause=function(){this._paused=!0},i.purgeDirector=function(){this._scheduler.unscheduleAll(),this._compScheduler.unscheduleAll(),this._nodeActivator.reset(),d(this._scene)&&this._scene.destroy(),this._scene=null,this.stopAnimation(),W.releaseAll()},i.reset=function(){for(var e in this.purgeDirector(),this._persistRootNodes)this.removePersistRootNode(this._persistRootNodes[e]);var t=this.getScene();t&&t.destroy(),this.emit(ne.RESET),this.startAnimation()},i.runSceneImmediate=function(e,t,i){var n=this;e instanceof A&&(e=e.scene),E(e instanceof B,1216),e._load();for(var s=Object.keys(this._persistRootNodes).map((function(e){return n._persistRootNodes[e]})),r=0;r<s.length;r++){var o=s[r];o.emit(Q.SCENE_CHANGED_FOR_PERSISTS,e.renderScene);var a=e.uuid===o._originalSceneId&&e.getChildByUuid(o.uuid);if(a){var _=a.siblingIndex;o.hideFlags&=~f.DontSave,o.hideFlags|=f.DontSave&a.hideFlags,a._destroyImmediate(),e.insertChild(o,_)}else o.hideFlags|=f.DontSave,o.parent=e}var c=this._scene;d(c)&&c.destroy(),Z._autoRelease(c,e,this._persistRootNodes),this._scene=null,p._deferredDestroy(),t&&t(),this.emit(ne.BEFORE_SCENE_LAUNCH,e),this._scene=e,e._activate(),this._root&&this._root.resetCumulativeTime(),this.startAnimation(),i&&i(null,e),this.emit(ne.AFTER_SCENE_LAUNCH,e)},i.runScene=function(e,t,i){var n=this;e instanceof A&&(e=e.scene),E(Boolean(e),1205),E(e instanceof B,1216),this.once(ne.END_FRAME,(function(){n.runSceneImmediate(e,t,i)}))},i.loadScene=function(e,t,i){var s=this;if(this._loadingScene)return a(1208,e,this._loadingScene),!1;var r=W.bundles.find((function(t){return!!t.getSceneInfo(e)}));return r?(this.emit(ne.BEFORE_SCENE_LOADING,e),this._loadingScene=e,console.time("LoadScene "+e),r.loadScene(e,(function(n,r){console.timeEnd("LoadScene "+e),s._loadingScene="",n?(m(n),t&&t(n)):s.runSceneImmediate(r,i,t)})),!0):(n(1209,e),!1)},i.preloadScene=function(e,t,i){var n=W.bundles.find((function(t){return!!t.getSceneInfo(e)}));if(n)n.preloadScene(e,null,t,i);else{var s='Can not preload the scene "'+e+'" because it is not in the build settings.';i&&i(new Error(s)),m("preloadScene: "+s)}},i.resume=function(){this._paused=!1},i.getScene=function(){return this._scene},i.getDeltaTime=function(){return v.game.deltaTime},i.getTotalTime=function(){return v.game.totalTime},i.getCurrentTime=function(){return v.game.frameStartTime},i.getTotalFrames=function(){return this._totalFrames},i.isPaused=function(){return this._paused},i.getScheduler=function(){return this._scheduler},i.setScheduler=function(e){this._scheduler!==e&&(this.unregisterSystem(this._scheduler),this._scheduler=e,this.registerSystem(D.ID,e,200))},i.registerSystem=function(e,t,i){t.id=e,t.priority=i,this._systems.push(t),this._systems.sort(w.sortByPriority)},i.unregisterSystem=function(e){u(this._systems,e),this._systems.sort(w.sortByPriority)},i.getSystem=function(e){return this._systems.find((function(t){return t.id===e}))},i.getAnimationManager=function(){return this.getSystem(v.AnimationManager.ID)},i.startAnimation=function(){this._invalid=!1},i.stopAnimation=function(){this._invalid=!0},i.mainLoop=function(){var e;e=v.game._calculateDT(!1),this.tick(e)},i.tick=function(e){if(!this._invalid){if(this.emit(ne.BEGIN_FRAME),F._frameDispatchEvents(),!this._paused){this.emit(ne.BEFORE_UPDATE),this._compScheduler.startPhase(),this._compScheduler.updatePhase(e);for(var t=0;t<this._systems.length;++t)this._systems[t].update(e);this._compScheduler.lateUpdatePhase(e),this.emit(ne.AFTER_UPDATE),p._deferredDestroy();for(var i=0;i<this._systems.length;++i)this._systems[i].postUpdate(e)}this.emit(ne.BEFORE_DRAW),re.updateAllDirtyRenderers(),this._root.frameMove(e),this.emit(ne.AFTER_DRAW),H.resetHasChangedFlags(),H.clearNodeArray(),g.update(e),this.emit(ne.END_FRAME),this._totalFrames++}},i.buildRenderPipeline=function(){if(this._root){var e=this._root.customPipeline,t=this._root.cameraList;e.beginSetup();var i=v.rendering.getCustomPipeline(r.CUSTOM_PIPELINE_NAME);v.rendering.dispatchResizeEvents(t,i,e),i.setup(t,e),e.endSetup()}},i.setupRenderPipelineBuilder=function(){""!==r.CUSTOM_PIPELINE_NAME&&v.rendering&&this._root&&this._root.usesCustomPipeline&&this.on(ne.BEFORE_SCENE_LAUNCH,v.rendering.forceResizeAllWindows,v.rendering)},i.init=function(){this._totalFrames=0,this._paused=!1,this.registerSystem(D.ID,this._scheduler,200),this._root=new ie(V.gfxDevice),this._root.initialize({}),this.setupRenderPipelineBuilder();for(var e=0;e<this._systems.length;e++)this._systems[e].init();this.emit(ne.INIT)},i.addPersistRootNode=function(e){if(H.isNode(e)&&e.uuid){var t=e.uuid;if(!this._persistRootNodes[t]){var i=this._scene;if(d(i))if(e.parent){if(!(e.parent instanceof B))return void a(3801);if(e.parent!==i)return void a(3802);e._originalSceneId=i.uuid}else e.parent=i,e._originalSceneId=i.uuid;this._persistRootNodes[t]=e,e._persistNode=!0,Z._addPersistNodeRef(e)}}else a(3800)},i.removePersistRootNode=function(e){var t=e.uuid||"";e===this._persistRootNodes[t]&&(delete this._persistRootNodes[t],e._persistNode=!1,e._originalSceneId="",Z._removePersistNodeRef(e))},i.isPersistRootNode=function(e){return!!e._persistNode},_(t,[{key:"root",get:function(){return this._root}}]),t}(R));oe.EVENT_INIT=ne.INIT,oe.EVENT_RESET=ne.RESET,oe.EVENT_BEFORE_SCENE_LOADING=ne.BEFORE_SCENE_LOADING,oe.EVENT_BEFORE_SCENE_LAUNCH=ne.BEFORE_SCENE_LAUNCH,oe.EVENT_AFTER_SCENE_LAUNCH=ne.AFTER_SCENE_LAUNCH,oe.EVENT_BEFORE_UPDATE=ne.BEFORE_UPDATE,oe.EVENT_AFTER_UPDATE=ne.AFTER_UPDATE,oe.EVENT_BEFORE_DRAW=ne.BEFORE_DRAW,oe.EVENT_AFTER_DRAW=ne.AFTER_DRAW,oe.EVENT_BEFORE_COMMIT=ne.BEFORE_COMMIT,oe.EVENT_BEFORE_RENDER=ne.BEFORE_RENDER,oe.EVENT_AFTER_RENDER=ne.AFTER_RENDER,oe.EVENT_BEFORE_PHYSICS=ne.BEFORE_PHYSICS,oe.EVENT_AFTER_PHYSICS=ne.AFTER_PHYSICS,oe.EVENT_BEGIN_FRAME=ne.BEGIN_FRAME,oe.EVENT_END_FRAME=ne.END_FRAME,oe.instance=void 0,v.Director=oe,v.DirectorEvent=ne,e("d",oe.instance=v.director=new oe)}}}));
