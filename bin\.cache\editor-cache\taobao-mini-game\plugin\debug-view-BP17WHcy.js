System.register(["./gc-object-D18ulfCO.js","./global-exports-CLZKKIY2.js","./device-manager-B8lyUDPi.js","./buffer-barrier-q_79u36H.js","./index-Y4La_nfG.js","./pipeline-state-manager-DQyhxoC_.js","./scene-ArUG4OfI.js","./component-CsuvAQKv.js","./factory-BOc5khhM.js"],(function(t){"use strict";var e,i,r,n,s,o,a,h,u,c,l,_,d,f,p,m,w,y,g,v,T,R,E,I,b,x,S,P,C,F,A,M,V,k,O,N,B,D,L,j,z,U,G,W,H,Q,X,Y,K,Z,q,J,$,tt,et,it,rt,nt,st,ot,at,ht,ut,ct,lt;return{setters:[function(t){e=t.a,i=t.w,r=t._,n=t.h,s=t.a6,o=t.m},function(t){a=t.c},function(t){h=t.d},function(t){u=t.C,c=t.bc,l=t.ab,_=t.w,d=t.F,f=t.x,p=t.R,m=t.p,w=t.aM,y=t.ak,g=t.f,v=t.aa,T=t.c,R=t.A,E=t.B,I=t.b,b=t.M,x=t.I,S=t.a},function(t){P=t.q,C=t.u,F=t.k,A=t.M,M=t.l,V=t.b,k=t.R,O=t.ab,N=t.e,B=t.f,D=t.w,L=t.b1,j=t.c,z=t.g,U=t.F,G=t.V,W=t.Q,H=t.C,Q=t.a6,X=t.h,Y=t.r,K=t.m,Z=t.G,q=t.a,J=t.s},function(t){$=t.C},function(t){tt=t.f,et=t.T,it=t.j,rt=t.l,nt=t.C,st=t.b,ot=t.n,at=t.A,ht=t.t,ut=t.S,ct=t.q},function(t){lt=t.A},null],execute:function(){var _t,dt,ft,pt,mt,wt,yt,gt;t({k:Gt,m:Qt,r:function(t,e,i,r,n,s){void 0===e&&(e=d.R32F),void 0===i&&(i=0),void 0===r&&(r=t.byteLength-i),void 0===n&&(n=0),void 0===s&&(s=[]);var o=T[e];n||(n=o.size);for(var a="get"+Ht(o),h=o.size/o.count,u=Math.floor(r/n),c=K.isLittleEndian,l=0;l<u;++l)for(var _=i+n*l,f=0;f<o.count;++f){var p=_+h*f;s[o.count*l+f]=t[a](p,c)}return s},w:function(t,e,i,r,n){void 0===i&&(i=d.R32F),void 0===r&&(r=0),void 0===n&&(n=0);var s=T[i];n||(n=s.size);for(var o="set"+Ht(s),a=s.size/s.count,h=Math.floor(e.length/s.count),u=K.isLittleEndian,c=0;c<h;++c)for(var l=r+n*c,_=0;_<s.count;++_){var f=l+a*_;t[o](f,e[s.count*c+_],u)}}}),t("a",_t),function(t){t[t.VERTICAL=0]="VERTICAL",t[t.HORIZONTAL=1]="HORIZONTAL"}(_t||t("a",_t={})),t("C",dt),function(t){t[t.ORTHO=0]="ORTHO",t[t.PERSPECTIVE=1]="PERSPECTIVE"}(dt||t("C",dt={})),t("b",ft),function(t){t[t.F1_8=0]="F1_8",t[t.F2_0=1]="F2_0",t[t.F2_2=2]="F2_2",t[t.F2_5=3]="F2_5",t[t.F2_8=4]="F2_8",t[t.F3_2=5]="F3_2",t[t.F3_5=6]="F3_5",t[t.F4_0=7]="F4_0",t[t.F4_5=8]="F4_5",t[t.F5_0=9]="F5_0",t[t.F5_6=10]="F5_6",t[t.F6_3=11]="F6_3",t[t.F7_1=12]="F7_1",t[t.F8_0=13]="F8_0",t[t.F9_0=14]="F9_0",t[t.F10_0=15]="F10_0",t[t.F11_0=16]="F11_0",t[t.F13_0=17]="F13_0",t[t.F14_0=18]="F14_0",t[t.F16_0=19]="F16_0",t[t.F18_0=20]="F18_0",t[t.F20_0=21]="F20_0",t[t.F22_0=22]="F22_0"}(ft||t("b",ft={})),t("d",pt),function(t){t[t.ISO100=0]="ISO100",t[t.ISO200=1]="ISO200",t[t.ISO400=2]="ISO400",t[t.ISO800=3]="ISO800"}(pt||t("d",pt={})),t("c",mt),function(t){t[t.D1=0]="D1",t[t.D2=1]="D2",t[t.D4=2]="D4",t[t.D8=3]="D8",t[t.D15=4]="D15",t[t.D30=5]="D30",t[t.D60=6]="D60",t[t.D125=7]="D125",t[t.D250=8]="D250",t[t.D500=9]="D500",t[t.D1000=10]="D1000",t[t.D2000=11]="D2000",t[t.D4000=12]="D4000"}(mt||t("c",mt={})),t("e",wt),function(t){t[t.DEFAULT=-1]="DEFAULT",t[t.LEFT_EYE=0]="LEFT_EYE",t[t.RIGHT_EYE=1]="RIGHT_EYE",t[t.MAIN=2]="MAIN"}(wt||t("e",wt={})),t("T",yt),function(t){t[t.NO_TRACKING=0]="NO_TRACKING",t[t.POSITION_AND_ROTATION=1]="POSITION_AND_ROTATION",t[t.POSITION=2]="POSITION",t[t.ROTATION=3]="ROTATION"}(yt||t("T",yt={})),t("j",gt),function(t){t[t.EDITOR=0]="EDITOR",t[t.GAME_VIEW=1]="GAME_VIEW",t[t.SCENE_VIEW=2]="SCENE_VIEW",t[t.PREVIEW=3]="PREVIEW",t[t.GAME=100]="GAME"}(gt||t("j",gt={}));var vt,Tt=[1.8,2,2.2,2.5,2.8,3.2,3.5,4,4.5,5,5.6,6.3,7.1,8,9,10,11,13,14,16,18,20,22],Rt=[1,.5,1/4,1/8,1/15,1/30,1/60,.008,.004,.002,.001,5e-4,1/4e3],Et=[100,200,400,800],It=P(),bt=P(),xt=C();t("S",vt),function(t){t[t.VALUE=u.STENCIL<<1]="VALUE"}(vt||t("S",vt={})),t("q",vt.VALUE);var St,Pt=[],Ct=0,Ft=(t("f",function(){function t(t){if(this.isWindowSize=!0,this.screenScale=1,this.postProcess=null,this.usePostProcess=!1,this.pipeline="",this.pipelineSettings=null,this._scene=null,this._node=null,this._name=null,this._enabled=!1,this._proj=-1,this._aspect=1,this._orthoHeight=10,this._fovAxis=_t.VERTICAL,this._fov=B(45),this._nearClip=1,this._farClip=1e3,this._clearColor=new l(.2,.2,.2,1),this._viewport=D(0,0,1,1),this._orientedViewport=D(0,0,1,1),this._curTransform=c.IDENTITY,this._isProjDirty=!0,this._matView=C(),this._matProj=C(),this._matProjInv=C(),this._matViewProj=C(),this._matViewProjInv=C(),this._frustum=new L,this._forward=P(),this._position=P(),this._priority=0,this._aperture=ft.F16_0,this._shutter=mt.D125,this._shutterValue=0,this._iso=pt.ISO100,this._isoValue=0,this._window=null,this._width=1,this._height=1,this._clearFlag=u.NONE,this._clearDepth=1,this._visibility=$,this._exposure=0,this._clearStencil=0,this._geometryRenderer=null,this._windowId=0,this._cameraType=wt.DEFAULT,this._trackingType=yt.NO_TRACKING,this._usage=gt.GAME,this._cameraId=Ct++,this._device=t,this._apertureValue=Tt[this._aperture],this._shutterValue=Rt[this._shutter],this._isoValue=Et[this._iso],this._frustum.accurate=!0,!Pt.length){var e=t.capabilities.clipSpaceSignY;Pt[c.IDENTITY]=new A(1,0,0,0,0,e),Pt[c.ROTATE_90]=new A(0,1,0,0,-e,0),Pt[c.ROTATE_180]=new A(-1,0,0,0,0,-e),Pt[c.ROTATE_270]=new A(0,-1,0,0,e,0)}}var r=t.prototype;return r._updateAspect=function(t){if(void 0===t&&(t=!0),this._aspect=this.window.width*this._viewport.width/(this.window.height*this._viewport.height),t){var e=this.window.swapchain;(e&&e.surfaceTransform||c.IDENTITY)%2&&(this._aspect=1/this._aspect)}this._isProjDirty=!0},r.initialize=function(t){void 0!==t.usage?this._usage=t.usage:this.setDefaultUsage(),void 0!==t.trackingType&&(this._trackingType=t.trackingType),void 0!==t.cameraType&&(this._cameraType=t.cameraType),this.node=t.node,this._width=1,this._height=1,this.clearFlag=u.NONE,this.clearDepth=1,this.visibility=$,this._name=t.name,this._proj=t.projection,this._priority=t.priority||0,this._aspect=this.screenScale=1,this.updateExposure(),this.changeTargetWindow(t.window)},r.destroy=function(){var t;this._node=null,this.detachFromScene(),this._window&&(this._window.detachCamera(this),this.window=null),this._name=null,null==(t=this._geometryRenderer)||t.destroy()},r.attachToScene=function(t){this._enabled=!0,this._scene=t},r.detachFromScene=function(){this._enabled=!1,this._scene=null},r.resize=function(t,e){this._window&&(this._width=t,this._height=e,this._aspect=t*this._viewport.width/(e*this._viewport.height),this._isProjDirty=!0)},r.setFixedSize=function(t,e){this._width=t,this._height=e,this._updateAspect(),this.isWindowSize=!1},r.syncCameraEditor=function(){},r.update=function(t){var e;if(void 0===t&&(t=!1),this._node){var i=!1,r=globalThis.__globalXR;if(r&&r.isWebXR&&r.webXRWindowMap&&r.updateViewport){var n=r.webXRMatProjs?1/r.webXRMatProjs.length:1,s=r.webXRWindowMap.get(this._window);this.setViewportInOrientedSpace(new F(n*s,0,n,1))}var o=this._forward,a=this._matView,h=this._matProj;(this._node.hasChangedFlags||t)&&(A.invert(a,this._node.worldMatrix),o.x=-a.m02,o.y=-a.m06,o.z=-a.m10,A.multiply(a,(new A).scale(this._node.worldScale),a),this._node.getWorldPosition(this._position),i=!0);var u=null==(e=this.window)?void 0:e.swapchain,l=u&&u.surfaceTransform||c.IDENTITY;if(this._isProjDirty||this._curTransform!==l){this._curTransform=l;var _=this._device.capabilities.clipSpaceSignY;if(this._proj===dt.PERSPECTIVE)if(r&&r.isWebXR&&r.webXRWindowMap&&r.webXRMatProjs){var d=r.webXRWindowMap.get(this._window);h.set(r.webXRMatProjs[d])}else A.perspective(h,this._fov,this._aspect,this._nearClip,this._farClip,this._fovAxis===_t.VERTICAL,this._device.capabilities.clipSpaceMinZ,_,l);else{var f=this._orthoHeight*this._aspect,p=this._orthoHeight;A.ortho(h,-f,f,-p,p,this._nearClip,this._farClip,this._device.capabilities.clipSpaceMinZ,_,l)}A.invert(this._matProjInv,h),i=!0,this._isProjDirty=!1}i&&(A.multiply(this._matViewProj,h,a),A.invert(this._matViewProjInv,this._matViewProj),this._frustum.update(this._matViewProj,this._matViewProjInv))}},r.setViewportInOrientedSpace=function(t){var e,i=t.x,r=t.width,n=t.height,s=this._device.capabilities.screenSpaceSignY<0?1-t.y-n:t.y,o=null==(e=this.window)?void 0:e.swapchain;switch(o&&o.surfaceTransform||c.IDENTITY){case c.ROTATE_90:this._viewport.x=1-s-n,this._viewport.y=i,this._viewport.width=n,this._viewport.height=r;break;case c.ROTATE_180:this._viewport.x=1-i-r,this._viewport.y=1-s-n,this._viewport.width=r,this._viewport.height=n;break;case c.ROTATE_270:this._viewport.x=s,this._viewport.y=1-i-r,this._viewport.width=n,this._viewport.height=r;break;case c.IDENTITY:this._viewport.x=i,this._viewport.y=s,this._viewport.width=r,this._viewport.height=n}this._orientedViewport.x=i,this._orientedViewport.y=s,this._orientedViewport.width=r,this._orientedViewport.height=n,this.resize(this.width,this.height)},r.initGeometryRenderer=function(){if(!this._geometryRenderer){var t,e=a.internal.GeometryRenderer;this._geometryRenderer=e?new e:null,null==(t=this._geometryRenderer)||t.activate(this._device)}},r.changeTargetWindow=function(t){void 0===t&&(t=null),this._window&&this._window.detachCamera(this);var e=t||a.director.root.mainWindow;if(e){e.attachCamera(this),this.window=e;var i=e.swapchain;(i&&i.surfaceTransform||c.IDENTITY)%2?this.resize(e.height,e.width):this.resize(e.width,e.height)}},r.detachCamera=function(){this._window&&this._window.detachCamera(this)},r.screenPointToRay=function(t,e,i){if(!this._node)return null;var r=this.width,n=this.height,s=this._orientedViewport.x*r,o=this._orientedViewport.y*n,a=this._orientedViewport.width*r,h=this._orientedViewport.height*n,u=this._proj===dt.PERSPECTIVE,c=this._device.capabilities.clipSpaceSignY,l=M[this._curTransform];V.set(It,(e-s)/a*2-1,(i-o)/h*2-1,u?1:-1);var _=It.x,d=It.y;return It.x=_*l[0]+d*l[2]*c,It.y=_*l[1]+d*l[3]*c,V.transformMat4(u?It:t.o,It,this._matViewProjInv),u?(this._node.getWorldPosition(bt),k.fromPoints(t,bt,It)):V.transformQuat(t.d,V.FORWARD,this._node.worldRotation),t},r.screenToWorld=function(t,e){var i=this.width,r=this.height,n=this._orientedViewport.x*i,s=this._orientedViewport.y*r,o=this._orientedViewport.width*i,a=this._orientedViewport.height*r,h=this._device.capabilities.clipSpaceSignY,u=M[this._curTransform];if(this._proj===dt.PERSPECTIVE){V.set(t,(e.x-n)/o*2-1,(e.y-s)/a*2-1,1);var c=t.x,l=t.y;t.x=c*u[0]+l*u[2]*h,t.y=c*u[1]+l*u[3]*h,V.transformMat4(t,t,this._matViewProjInv),this._node&&this._node.getWorldPosition(It),V.lerp(t,It,t,O(this._nearClip/this._farClip,1,e.z))}else{V.set(t,(e.x-n)/o*2-1,(e.y-s)/a*2-1,2*e.z-1);var _=t.x,d=t.y;t.x=_*u[0]+d*u[2]*h,t.y=_*u[1]+d*u[3]*h,V.transformMat4(t,t,this._matViewProjInv)}return t},r.worldToScreen=function(t,e){var i=this._device.capabilities.clipSpaceSignY,r=M[this._curTransform];V.transformMat4(t,e,this._matViewProj);var n=t.x,s=t.y;t.x=n*r[0]+s*r[2]*i,t.y=n*r[1]+s*r[3]*i;var o=this.width,a=this.height,h=this._orientedViewport.x*o,u=this._orientedViewport.y*a,c=this._orientedViewport.width*o,l=this._orientedViewport.height*a;return t.x=h+.5*(t.x+1)*c,t.y=u+.5*(t.y+1)*l,t.z=.5*t.z+.5,t},r.worldMatrixToScreen=function(t,e,i,r){A.multiply(t,this._matViewProj,e),A.multiply(t,Pt[this._curTransform],t);var n=i/2,s=r/2;return A.identity(xt),A.transform(xt,xt,V.set(It,n,s,0)),A.scale(xt,xt,V.set(It,n,s,1)),A.multiply(t,xt,t),t},r.calculateObliqueMat=function(t){var e=new N(Math.sign(t.x),Math.sign(t.y),1,1).transformMat4(this._matProjInv),i=new N(this._matProj.m03,this._matProj.m07,this._matProj.m11,this._matProj.m15),r=2/N.dot(t,e),n=t.multiplyScalar(r).subtract(i);this._matProj.m02=n.x,this._matProj.m06=n.y,this._matProj.m10=n.z,this._matProj.m14=n.w},r.getClipSpaceMinz=function(){return this._device.capabilities.clipSpaceMinZ},r.setExposure=function(t){this._exposure=.833333/Math.pow(2,t)},r.updateExposure=function(){var t=Math.log2(this._apertureValue*this._apertureValue/this._shutterValue*100/this._isoValue);this.setExposure(t)},r.setDefaultUsage=function(){this._usage=gt.GAME},e(t,[{key:"name",get:function(){return this._name}},{key:"scene",get:function(){return this._scene}},{key:"node",get:function(){return this._node},set:function(t){this._node=t}},{key:"systemWindowId",get:function(){return this._windowId}},{key:"window",get:function(){return this._window},set:function(t){this._window=t}},{key:"enabled",get:function(){return this._enabled},set:function(t){this._enabled=t}},{key:"visibility",get:function(){return this._visibility},set:function(t){this._visibility=t}},{key:"priority",get:function(){return this._priority},set:function(t){this._priority=t}},{key:"width",get:function(){return this._width}},{key:"height",get:function(){return this._height}},{key:"position",get:function(){return this._position},set:function(t){this._position=t}},{key:"forward",get:function(){return this._forward},set:function(t){this._forward=t}},{key:"aperture",get:function(){return this._aperture},set:function(t){this._aperture=t,this._apertureValue=Tt[this._aperture],this.updateExposure()}},{key:"apertureValue",get:function(){return this._apertureValue}},{key:"shutter",get:function(){return this._shutter},set:function(t){this._shutter=t,this._shutterValue=Rt[this._shutter],this.updateExposure()}},{key:"shutterValue",get:function(){return this._shutterValue}},{key:"iso",get:function(){return this._iso},set:function(t){this._iso=t,this._isoValue=Et[this._iso],this.updateExposure()}},{key:"isoValue",get:function(){return this._isoValue}},{key:"exposure",get:function(){return this._exposure}},{key:"clearFlag",get:function(){return this._clearFlag},set:function(t){this._clearFlag=t}},{key:"clearColor",get:function(){return this._clearColor},set:function(t){this._clearColor.x=t.x,this._clearColor.y=t.y,this._clearColor.z=t.z,this._clearColor.w=t.w}},{key:"clearDepth",get:function(){return this._clearDepth},set:function(t){this._clearDepth=t}},{key:"clearStencil",get:function(){return this._clearStencil},set:function(t){this._clearStencil=t}},{key:"projectionType",get:function(){return this._proj},set:function(t){this._proj=t,this._isProjDirty=!0}},{key:"aspect",get:function(){return this._aspect}},{key:"orthoHeight",get:function(){return this._orthoHeight},set:function(t){this._orthoHeight=t,this._isProjDirty=!0}},{key:"fovAxis",get:function(){return this._fovAxis},set:function(t){this._fovAxis=t,this._isProjDirty=!0}},{key:"fov",get:function(){return this._fov},set:function(t){this._fov=t,this._isProjDirty=!0}},{key:"nearClip",get:function(){return this._nearClip},set:function(t){this._nearClip=t,this._isProjDirty=!0}},{key:"farClip",get:function(){return this._farClip},set:function(t){this._farClip=t,this._isProjDirty=!0}},{key:"viewport",get:function(){return this._viewport},set:function(t){i(8302),this.setViewportInOrientedSpace(t)}},{key:"frustum",get:function(){return this._frustum},set:function(t){this._frustum=t}},{key:"matView",get:function(){return this._matView}},{key:"matProj",get:function(){return this._matProj}},{key:"matProjInv",get:function(){return this._matProjInv}},{key:"matViewProj",get:function(){return this._matViewProj}},{key:"matViewProjInv",get:function(){return this._matViewProjInv}},{key:"cameraId",get:function(){return this._cameraId}},{key:"surfaceTransform",get:function(){return this._curTransform}},{key:"geometryRenderer",get:function(){return this._geometryRenderer}},{key:"cameraType",get:function(){return this._cameraType},set:function(t){this._cameraType=t}},{key:"trackingType",get:function(){return this._trackingType},set:function(t){this._trackingType=t}},{key:"cameraUsage",get:function(){return this._usage},set:function(t){this._usage=t}}],[{key:"standardExposureValue",get:function(){return 1/38400}},{key:"standardLightMeterScale",get:function(){return 1e4}}]),t}()),new _);Ft.format=d.RGBA8;var At=new f;At.format=d.DEPTH_STENCIL;var Mt,Vt,kt=new p([Ft],At),Ot={width:1,height:1,renderPassInfo:kt},Nt=t("R",j("cc.RenderTexture")(St=function(t){function i(e){var i;return(i=t.call(this,e)||this)._window=null,i}r(i,t);var s=i.prototype;return s.initialize=function(t){this._name=t.name||"",this._width=t.width,this._height=t.height,this._initWindow(t)},s.reset=function(t){this.initialize(t)},s.destroy=function(){if(this._window){var e=a.director.root;null==e||e.destroyWindow(this._window),this._window=null}return t.prototype.destroy.call(this)},s.resize=function(t,e){this._width=Math.floor(z(t,1,2048)),this._height=Math.floor(z(e,1,2048)),this._window&&this._window.resize(this._width,this._height),this.emit("resize",this._window)},s._serialize=function(){return{}},s._deserialize=function(e,i){var r=e;this._width=r.w,this._height=r.h,this._name=r.n,t.prototype._deserialize.call(this,r.base,i)},s.getGFXTexture=function(){return this._window&&this._window.framebuffer.colorTextures[0]},s.onLoaded=function(){this._initWindow()},s._initWindow=function(t){var e=a.director.root;Ot.title=this._name,Ot.width=this._width,Ot.height=this._height,Ot.renderPassInfo=t&&t.passInfo?t.passInfo:kt,Ot.externalResLow=t&&t.externalResLow?t.externalResLow:0,Ot.externalResHigh=t&&t.externalResHigh?t.externalResHigh:0,Ot.externalFlag=t&&t.externalFlag?t.externalFlag:m.NONE,Ot.renderPassInfo.colorAttachments.forEach((function(t){t.format=e.device.swapchainFormat})),Ft.barrier=h.gfxDevice.getGeneralBarrier(new w(y.FRAGMENT_SHADER_READ_TEXTURE,y.FRAGMENT_SHADER_READ_TEXTURE)),this._window?(this._window.destroy(),this._window.initialize(h.gfxDevice,Ot)):this._window=e.createWindow(Ot)},s.initDefault=function(e){t.prototype.initDefault.call(this,e),this._width=this._height=1,this._initWindow()},s.validate=function(){return this.width>=1&&this.width<=2048&&this.height>=1&&this.height<=2048},s.readPixels=function(t,e,i,r,s){t=t||0,e=e||0,i=i||this.width,r=r||this.height;var o=this.getGFXTexture();if(!o)return n(7606),null;var a=4*i*r;if(void 0===s)s=new Uint8Array(a);else if(s.length<a)return n(7607,a),null;var h=this._getGFXDevice(),u=[],c=[],l=new g;return l.texOffset.x=t,l.texOffset.y=e,l.texExtent.width=i,l.texExtent.height=r,c.push(l),u.push(s),null==h||h.copyTextureToBuffers(o,u,c),s},e(i,[{key:"window",get:function(){return this._window}}]),i}(tt))||St);a.RenderTexture=Nt,t("n",Mt),function(t){t[t.SKYBOX=vt.VALUE|u.DEPTH_STENCIL]="SKYBOX",t[t.SOLID_COLOR=u.ALL]="SOLID_COLOR"}(Mt||t("n",Mt={})),t("o",Vt),function(t){t[t.CUBE=0]="CUBE",t[t.PLANAR=1]="PLANAR"}(Vt||t("o",Vt={}));var Bt,Dt,Lt=[P(0,-90,0),P(0,90,0),P(90,0,0),P(-90,0,0),P(0,0,0),P(0,180,0)],jt=P(),zt=(t("p",function(){function t(t){this.bakedCubeTextures=[],this.realtimePlanarTexture=null,this._resolution=256,this._clearFlag=Mt.SKYBOX,this._backgroundColor=new H(0,0,0,255),this._visibility=$,this._probeType=Vt.CUBE,this._cubemap=null,this._size=P(1,1,1),this._camera=null,this._probeId=0,this._needRefresh=!1,this._needRender=!1,this._node=null,this._cameraNode=null,this._boundingBox=null,this._cameraWorldPos=P(),this._cameraWorldRotation=Q(),this._forward=P(),this._up=P(),this._previewSphere=null,this._previewPlane=null,this._probeId=t}var i=t.prototype;return i.initialize=function(t,e){this._node=t,this._cameraNode=e,this.node.getWorldPosition(jt);var i=this._size;this._boundingBox=U.create(jt.x,jt.y,jt.z,i.x,i.y,i.z),this._createCamera(e)},i.initBakedTextures=function(){if(0===this.bakedCubeTextures.length)for(var t=0;t<6;t++){var e=this._createTargetTexture(this._resolution,this._resolution);this.bakedCubeTextures.push(e)}},i.captureCubemap=function(){this.initBakedTextures(),this._resetCameraParams(),this._needRender=!0},i.renderPlanarReflection=function(t){if(t){if(!this.realtimePlanarTexture){var e=a.view.getDesignResolutionSize();this.realtimePlanarTexture=this._createTargetTexture(e.width,e.height),a.internal.reflectionProbeManager.updatePlanarMap(this,this.realtimePlanarTexture.getGFXTexture())}this._syncCameraParams(t),this._transformReflectionCamera(t),this._needRender=!0}},i.switchProbeType=function(t,e){t===Vt.CUBE?this._needRender=!1:null!==e&&this.renderPlanarReflection(e)},i.getProbeId=function(){return this._probeId},i.updateProbeId=function(t){this._probeId=t},i.renderArea=function(){return this._probeType===Vt.PLANAR?new G(this.realtimePlanarTexture.width,this.realtimePlanarTexture.height):new G(this.resolution,this.resolution)},i.isFinishedRendering=function(){return!0},i.validate=function(){return null!==this.cubemap},i.destroy=function(){this._camera&&(this._camera.destroy(),this._camera=null);for(var t=0;t<this.bakedCubeTextures.length;t++)this.bakedCubeTextures[t].destroy();this.bakedCubeTextures=[],this.realtimePlanarTexture&&(this.realtimePlanarTexture.destroy(),this.realtimePlanarTexture=null)},i.enable=function(){},i.disable=function(){},i.updateCameraDir=function(t){this.cameraNode.setRotationFromEuler(Lt[t]),this.camera.update(!0)},i.updateBoundingBox=function(){if(this.node){this.node.getWorldPosition(jt);var t=this._size;U.set(this._boundingBox,jt.x,jt.y,jt.z,t.x,t.y,t.z)}},i.hasFrameBuffer=function(t){if(this.probeType===Vt.PLANAR){var e;if(!this.realtimePlanarTexture)return!1;if((null==(e=this.realtimePlanarTexture.window)?void 0:e.framebuffer)===t)return!0}else{if(0===this.bakedCubeTextures.length)return!1;for(var i=0;i<this.bakedCubeTextures.length;i++){var r;if((null==(r=this.bakedCubeTextures[i].window)?void 0:r.framebuffer)===t)return!0}}return!1},i.isRGBE=function(){return!0},i._syncCameraParams=function(t){this.camera.projectionType=t.projectionType,this.camera.orthoHeight=t.orthoHeight,this.camera.nearClip=t.nearClip,this.camera.farClip=t.farClip,this.camera.fov=t.fov,this.camera.clearFlag=t.clearFlag,this.camera.clearColor=t.clearColor,this.camera.priority=t.priority-1,this.camera.resize(t.width,t.height)},i._createCamera=function(t){var e=a.director.root;if(!this._camera){if(this._camera=e.createCamera(),!this._camera)return null;this._camera.initialize({name:t.name,node:t,projection:dt.PERSPECTIVE,window:e&&e.tempWindow,priority:0,cameraType:wt.DEFAULT,trackingType:yt.NO_TRACKING})}return this._camera.setViewportInOrientedSpace(new F(0,0,1,1)),this._camera.fovAxis=_t.VERTICAL,this._camera.fov=B(90),this._camera.orthoHeight=10,this._camera.nearClip=1,this._camera.farClip=1e3,this._camera.clearColor=this._backgroundColor,this._camera.clearDepth=1,this._camera.clearStencil=0,this._camera.clearFlag=this._clearFlag,this._camera.visibility=this._visibility,this._camera.aperture=ft.F16_0,this._camera.shutter=mt.D125,this._camera.iso=pt.ISO100,this._camera},i._resetCameraParams=function(){this.camera.projectionType=dt.PERSPECTIVE,this.camera.orthoHeight=10,this.camera.nearClip=1,this.camera.farClip=1e3,this.camera.fov=B(90),this.camera.priority=0,this.camera.resize(this.resolution,this.resolution),this.camera.visibility=this._visibility,this.camera.clearFlag=this._clearFlag,this.camera.clearColor=this._backgroundColor,this.cameraNode.worldPosition=this.node.worldPosition,this.cameraNode.worldRotation=this.node.worldRotation,this.camera.update(!0)},i._createTargetTexture=function(t,e){var i=new Nt;return i.reset({width:t,height:e}),i},i._transformReflectionCamera=function(t){var e=V.dot(this.node.worldPosition,this.node.up);this._reflect(this._cameraWorldPos,t.node.worldPosition,this.node.up,e),this.cameraNode.worldPosition=this._cameraWorldPos,V.transformQuat(this._forward,V.FORWARD,t.node.worldRotation),this._reflect(this._forward,this._forward,this.node.up,0),this._forward.normalize(),this._forward.negative(),V.transformQuat(this._up,V.UP,t.node.worldRotation),this._reflect(this._up,this._up,this.node.up,0),this._up.normalize(),W.fromViewUp(this._cameraWorldRotation,this._forward,this._up),this.cameraNode.worldRotation=this._cameraWorldRotation,this.camera.update(!0);var i=new N(this.node.up.x,this.node.up.y,this.node.up.z,-V.dot(this.node.up,this.node.worldPosition));i.transformMat4(this.camera.matView.clone().invert().transpose()),this.camera.calculateObliqueMat(i)},i._reflect=function(t,e,i,r){var n=V.clone(i);n.normalize();var s=V.dot(n,e)-r;return n.multiplyScalar(2*s),V.subtract(t,e,n),t},e(t,[{key:"probeType",get:function(){return this._probeType},set:function(t){this._probeType=t}},{key:"resolution",get:function(){return this._resolution},set:function(t){t!==this._resolution&&this.bakedCubeTextures.forEach((function(e){e.resize(t,t)})),this._resolution=t}},{key:"clearFlag",get:function(){return this._clearFlag},set:function(t){this._clearFlag=t,this.camera.clearFlag=this._clearFlag}},{key:"backgroundColor",get:function(){return this._backgroundColor},set:function(t){this._backgroundColor=t,this.camera.clearColor=this._backgroundColor}},{key:"visibility",get:function(){return this._visibility},set:function(t){this._visibility=t,this._camera.visibility=this._visibility}},{key:"size",get:function(){return this._size},set:function(t){this._size.set(t),this.node.getWorldPosition(jt),U.set(this._boundingBox,jt.x,jt.y,jt.z,t.x,t.y,t.z)}},{key:"cubemap",get:function(){return this._cubemap},set:function(t){this._cubemap=t}},{key:"node",get:function(){return this._node}},{key:"camera",get:function(){return this._camera}},{key:"needRefresh",get:function(){return this._needRefresh},set:function(t){this._needRefresh=t}},{key:"needRender",get:function(){return this._needRender},set:function(t){this._needRender=t}},{key:"boundingBox",get:function(){return this._boundingBox}},{key:"cameraNode",get:function(){return this._cameraNode},set:function(t){this._cameraNode=t}},{key:"previewSphere",get:function(){return this._previewSphere},set:function(t){this._previewSphere=t}},{key:"previewPlane",get:function(){return this._previewPlane},set:function(t){this._previewPlane=t}}]),t}()),t("O",function(){function t(){this._enabled=!1,this._minPos=new V(0,0,0),this._maxPos=new V(0,0,0),this._depth=0}return t.prototype.initialize=function(t){this._enabled=t.enabled,this._minPos=t.minPos,this._maxPos=t.maxPos,this._depth=t.depth},e(t,[{key:"enabled",get:function(){return this._enabled},set:function(t){this._enabled=t}},{key:"minPos",get:function(){return this._minPos},set:function(t){this._minPos=t}},{key:"maxPos",get:function(){return this._maxPos},set:function(t){this._maxPos=t}},{key:"depth",get:function(){return this._depth},set:function(t){this._depth=t}}]),t}())),Ut=t("s",function(){function t(){this._enabled=!0,this._blurRadius=.01,this._sssIntensity=3}return t.prototype.initialize=function(t){this._enabled=t.enabled,this._blurRadius=t.blurRadius,this._sssIntensity=t.sssIntensity},e(t,[{key:"enabled",get:function(){return this._enabled},set:function(t){this._enabled=t}},{key:"blurRadius",get:function(){return this._blurRadius},set:function(t){this._blurRadius=t}},{key:"sssIntensity",get:function(){return this._sssIntensity},set:function(t){this._sssIntensity=t}}]),t}());function Gt(t,e){e<1e3?e=1e3:e>15e3&&(e=15e3);var i=e*e,r=(.860117757+.000154118254*e+1.28641212e-7*i)/(1+.000842420235*e+7.08145163e-7*i),n=(.317398726+422806245e-13*e+4.20481691e-8*i)/(1-289741816e-13*e+1.61456053e-7*i),s=2*r-8*n+4,o=3*r/s,a=2*n/s,h=1/a*o,u=1/a*(1-o-a);t.x=3.2404542*h-1.5371385+-.4985314*u,t.y=-.969266*h+1.8760108+.041556*u,t.z=.0556434*h-.2040259+1.0572252*u}t("L",Bt),function(t){t[t.DIRECTIONAL=0]="DIRECTIONAL",t[t.SPHERE=1]="SPHERE",t[t.SPOT=2]="SPOT",t[t.POINT=3]="POINT",t[t.RANGED_DIRECTIONAL=4]="RANGED_DIRECTIONAL",t[t.UNKNOWN=5]="UNKNOWN"}(Bt||t("L",Bt={})),t("t",(function(t){return 4*Math.PI*Math.PI*t*t})),t("l",function(){function t(){this._baked=!1,this._color=P(1,1,1),this._colorTemp=6550,this._colorTempRGB=P(1,1,1),this._finalColor=P(1,1,1),this._scene=null,this._node=null,this._name=null,this._useColorTemperature=!1,this._type=Bt.UNKNOWN,this._visibility=$}var i=t.prototype;return i.initialize=function(){this.color=P(1,1,1),this.colorTemperature=6550},i.attachToScene=function(t){this._scene=t},i.detachFromScene=function(){this._scene=null},i.destroy=function(){this._name=null,this._node=null},i.update=function(){},e(t,[{key:"baked",get:function(){return this._baked},set:function(t){this._baked=t}},{key:"color",get:function(){return this._color},set:function(t){this._color.set(t),this._useColorTemperature&&V.multiply(this._finalColor,this._color,this._colorTempRGB)}},{key:"useColorTemperature",get:function(){return this._useColorTemperature},set:function(t){this._useColorTemperature=t,t&&V.multiply(this._finalColor,this._color,this._colorTempRGB)}},{key:"colorTemperature",get:function(){return this._colorTemp},set:function(t){this._colorTemp=t,Gt(this._colorTempRGB,this._colorTemp),this._useColorTemperature&&V.multiply(this._finalColor,this._color,this._colorTempRGB)}},{key:"colorTemperatureRGB",get:function(){return this._colorTempRGB}},{key:"finalColor",get:function(){return this._finalColor}},{key:"visibility",get:function(){return this._visibility},set:function(t){this._visibility=t}},{key:"node",get:function(){return this._node},set:function(t){this._node=t,this._node&&(this._node.hasChangedFlags|=et.ROTATION)}},{key:"type",get:function(){return this._type}},{key:"name",get:function(){return this._name},set:function(t){this._name=t}},{key:"scene",get:function(){return this._scene}}]),t}()),X(tt.prototype,"TextureBase.prototype",[{name:"hasPremultipliedAlpha"},{name:"setPremultiplyAlpha"},{name:"setFlipY"}]),Y(Nt.prototype,"RenderTexture.prototype",[{name:"getGFXWindow",customFunction:function(){return this.window}}]);var Wt=((Dt={})[v.UNORM]="Uint",Dt[v.SNORM]="Int",Dt[v.UINT]="Uint",Dt[v.INT]="Int",Dt[v.UFLOAT]="Float",Dt[v.FLOAT]="Float",Dt.default="Uint",Dt);function Ht(t){return""+(Wt[t.type]||Wt.default)+t.size/t.count*8}function Qt(t,e,i,r,n,s,o){void 0===i&&(i=d.R32F),void 0===r&&(r=0),void 0===n&&(n=t.byteLength-r),void 0===s&&(s=0),o||(o=new DataView(t.buffer.slice(t.byteOffset,t.byteOffset+t.byteLength)));var a=T[i];s||(s=a.size);for(var h="set"+Ht(a),u="get"+Ht(a),c=a.size/a.count,l=Math.floor(n/s),_=K.isLittleEndian,f=0;f<l;++f)for(var p=r+s*f,m=0;m<a.count;++m){var w=p+c*m,y=t[u](w,_);o[h](w,e(y,m,t),_)}return o}var Xt,Yt,Kt,Zt={positions:new Float32Array,indices:new Uint8Array,boundingBox:{min:V.ZERO,max:V.ZERO}},qt=(t("g",function(){function t(t,e,i,r,n,s){void 0===r&&(r=null),void 0===n&&(n=null),void 0===s&&(s=!0),this.mesh=void 0,this.subMeshIdx=void 0,this._flatBuffers=[],this._jointMappedBuffers=void 0,this._jointMappedBufferIndices=void 0,this._vertexIdChannel=void 0,this._geometricInfo=void 0,this._vertexBuffers=void 0,this._drawInfo=null,this._attributes=e,this._vertexBuffers=t,this._indexBuffer=r,this._indirectBuffer=n,this._primitiveMode=i,this._iaInfo=new x(e,t,r,n),this._isOwnerOfIndexBuffer=s}var i=t.prototype;return i.invalidateGeometricInfo=function(){this._geometricInfo=void 0},i.genFlatBuffers=function(){if(!this._flatBuffers.length&&this.mesh&&void 0!==this.subMeshIdx){var t=this.mesh,e=0,i=t.struct.primitives[this.subMeshIdx];i.indexView&&(e=i.indexView.count);for(var r=0;r<i.vertexBundelIndices.length;r++){var n=i.vertexBundelIndices[r],s=t.struct.vertexBundles[n],o=i.indexView?i.indexView.count:s.view.count,a=s.view.stride,h=a*o,u=new Uint8Array(t.data.buffer,s.view.offset,s.view.length),c=new Uint8Array(i.indexView?h:s.view.length);if(i.indexView){for(var l=t.readIndices(this.subMeshIdx),_=0;_<e;++_)for(var d=_*a,f=l[_]*a,p=0;p<a;++p)c[d+p]=u[f+p];this._flatBuffers.push({stride:a,count:o,buffer:c})}else c.set(t.data.subarray(s.view.offset,s.view.offset+s.view.length)),this._flatBuffers.push({stride:a,count:o,buffer:c})}}},i.destroy=function(){for(var t=0;t<this.vertexBuffers.length;t++)this.vertexBuffers[t].destroy();if(this.vertexBuffers.length=0,this._indexBuffer&&(this._isOwnerOfIndexBuffer&&this._indexBuffer.destroy(),this._indexBuffer=null),this._jointMappedBuffers&&this._jointMappedBufferIndices){for(var e=0;e<this._jointMappedBufferIndices.length;e++)this._jointMappedBuffers[this._jointMappedBufferIndices[e]].destroy();this._jointMappedBuffers=void 0,this._jointMappedBufferIndices=void 0}this._indirectBuffer&&(this._indirectBuffer.destroy(),this._indirectBuffer=null)},i.enableVertexIdChannel=function(t){if(!this._vertexIdChannel){var e=this.vertexBuffers.length,i=this.attributes.length,r=this._allocVertexIdBuffer(t);this._vertexBuffers.push(r),this._attributes.push(new R("a_vertexId",d.R32F,!1,e)),this._iaInfo.attributes=this._attributes,this._iaInfo.vertexBuffers=this._vertexBuffers,this._vertexIdChannel={stream:e,index:i}}},i._allocVertexIdBuffer=function(t){for(var e=0===this.vertexBuffers.length||0===this.vertexBuffers[0].stride?0:this.vertexBuffers[0].size/this.vertexBuffers[0].stride,i=new Float32Array(e),r=0;r<e;++r)i[r]=r+.5;var n=t.createBuffer(new E(I.VERTEX|I.TRANSFER_DST,b.DEVICE,i.byteLength,i.BYTES_PER_ELEMENT));return n.update(i),n},e(t,[{key:"attributes",get:function(){return this._attributes}},{key:"vertexBuffers",get:function(){return this._vertexBuffers}},{key:"indexBuffer",get:function(){return this._indexBuffer}},{key:"indirectBuffer",get:function(){return this._indirectBuffer}},{key:"primitiveMode",get:function(){return this._primitiveMode}},{key:"geometricInfo",get:function(){if(this._geometricInfo)return this._geometricInfo;if(void 0===this.mesh)return Zt;if(void 0===this.subMeshIdx)return Zt;var t,e=this.mesh,i=this.subMeshIdx,r=this.attributes.find((function(t){return t.name===S.ATTR_POSITION}));if(!r)return Zt;switch(r.format){case d.RG32F:case d.RGB32F:if(!(t=e.readAttribute(i,S.ATTR_POSITION)))return Zt;break;case d.RGBA32F:var n=e.readAttribute(i,S.ATTR_POSITION);if(!n)return Zt;var s=n.length/4;t=new Float32Array(3*s);for(var o=0;o<s;++o){var a=3*o,h=4*o;t[a]=n[h],t[a+1]=n[h+1],t[a+2]=n[h+2]}break;case d.RG16F:case d.RGB16F:var u=e.readAttribute(i,S.ATTR_POSITION);if(!u)return Zt;t=new Float32Array(u.length);for(var c=0;c<u.length;++c)t[c]=Z(u[c]);break;case d.RGBA16F:var l=e.readAttribute(i,S.ATTR_POSITION);if(!l)return Zt;var _=l.length/4;t=new Float32Array(3*_);for(var f=0;f<_;++f){var p=3*f,m=4*f;t[p]=Z(l[m]),t[p+1]=Z(l[m+1]),t[p+2]=Z(l[m+2])}break;default:return Zt}var w=e.readIndices(i)||void 0,y=new V,g=new V,v=T[r.format].count;2===v?(y.set(t[0],t[1],0),g.set(t[0],t[1],0)):(y.set(t[0],t[1],t[2]),g.set(t[0],t[1],t[2]));for(var R=0;R<t.length;R+=v)2===v?(y.x=t[R]>y.x?t[R]:y.x,y.y=t[R+1]>y.y?t[R+1]:y.y,g.x=t[R]<g.x?t[R]:g.x,g.y=t[R+1]<g.y?t[R+1]:g.y):(y.x=t[R]>y.x?t[R]:y.x,y.y=t[R+1]>y.y?t[R+1]:y.y,y.z=t[R+2]>y.z?t[R+2]:y.z,g.x=t[R]<g.x?t[R]:g.x,g.y=t[R+1]<g.y?t[R+1]:g.y,g.z=t[R+2]<g.z?t[R+2]:g.z);return this._geometricInfo={positions:t,indices:w,boundingBox:{max:y,min:g}},this._geometricInfo}},{key:"drawInfo",get:function(){return this._drawInfo},set:function(t){this._drawInfo=t}},{key:"flatBuffers",get:function(){return this._flatBuffers}},{key:"jointMappedBuffers",get:function(){var t=this;if(this._jointMappedBuffers)return this._jointMappedBuffers;var e=this._jointMappedBuffers=[],i=this._jointMappedBufferIndices=[];if(!this.mesh||void 0===this.subMeshIdx)return this._jointMappedBuffers=this.vertexBuffers;var r,n,s=this.mesh.struct,o=s.primitives[this.subMeshIdx];if(!s.jointMaps||void 0===o.jointMapIndex||!s.jointMaps[o.jointMapIndex])return this._jointMappedBuffers=this.vertexBuffers;for(var h=a.director.root.device,u=function(){var a=s.vertexBundles[o.vertexBundelIndices[c]];n=0,r=d.UNKNOWN;for(var u=0;u<a.attributes.length;u++){var l=a.attributes[u];if(l.name===S.ATTR_JOINTS){r=l.format;break}n+=T[l.format].size}if(r){var _=new Uint8Array(t.mesh.data.buffer,a.view.offset,a.view.length),f=new DataView(_.slice().buffer),p=s.jointMaps[o.jointMapIndex];Qt(f,(function(t){return p.indexOf(t)}),r,n,a.view.length,a.view.stride,f);var m=h.createBuffer(new E(I.VERTEX|I.TRANSFER_DST,b.DEVICE,a.view.length,a.view.stride));m.update(f.buffer),e.push(m),i.push(c)}else e.push(t.vertexBuffers[o.vertexBundelIndices[c]])},c=0;c<o.vertexBundelIndices.length;c++)u();return this._vertexIdChannel&&e.push(this._allocVertexIdBuffer(h)),e}},{key:"iaInfo",get:function(){return this._iaInfo}}]),t}()),t("i",j("cc.SceneAsset")((Yt=function(t){function e(e){var i;return(i=t.call(this,e)||this).scene=Kt&&Kt(),i}r(e,t);var i=e.prototype;return i.initDefault=function(e){t.prototype.initDefault.call(this,e),this.scene=new it("New Scene")},i.validate=function(){return!!this.scene},e}(lt),Kt=q(Yt.prototype,"scene",[J],(function(){return null})),Xt=Yt))||Xt));a.SceneAsset=qt;var Jt,$t=new A,te=new A,ee=new A,ie=new A,re=new A,ne=new A,se=new A,oe=new V(0,0,0),ae=new V,he=new G,ue=new V,ce=new V,le=new V(1e7,1e7,1e7),_e=new V(-1e7,-1e7,-1e7),de=new V,fe=0,pe=0,me=function(){function t(t){this._shadowObjects=[],this._shadowCameraFar=0,this._matShadowView=new A,this._matShadowProj=new A,this._matShadowViewProj=new A,this._validFrustum=new L,this._splitFrustum=new L,this._lightViewFrustum=new L,this._castLightViewBoundingBox=new U,this._level=t,this._validFrustum.accurate=!0,this._splitFrustum.accurate=!0,this._lightViewFrustum.accurate=!0}var i=t.prototype;return i.copyToValidFrustum=function(t){L.copy(this._validFrustum,t)},i.calculateValidFrustumOrtho=function(t,e,i,r,n){L.createOrtho(this._validFrustum,t,e,i,r,n)},i.calculateSplitFrustum=function(t,e,i,r){this._splitFrustum.split(i,r,t.aspect,t.fov,e)},i.destroy=function(){this._shadowObjects.length=0},i.createMatrix=function(t,e,i){var r=a.director.root.device,n=t.shadowInvisibleOcclusionRange;L.copy(this._lightViewFrustum,this._splitFrustum),A.fromRT(te,t.node.rotation,oe),A.invert(ee,te);var s,o,h=ee.clone();this._lightViewFrustum.transform(ee),U.fromPoints(this._castLightViewBoundingBox,le,_e),this._castLightViewBoundingBox.mergeFrustum(this._lightViewFrustum),t.csmOptimizationMode===rt.DisableRotationFix?(s=2*this._castLightViewBoundingBox.halfExtents.x,o=2*this._castLightViewBoundingBox.halfExtents.y):s=o=V.distance(this._lightViewFrustum.vertices[0],this._lightViewFrustum.vertices[6]);var u=a.director.root.pipeline.pipelineSceneData.csmSupported?t.csmLevel:1;if(u>1&&t.csmOptimizationMode===rt.RemoveDuplicates)if(this._level>=u-1)pe=this._castLightViewBoundingBox.halfExtents.z,fe=this._castLightViewBoundingBox.center.z;else{var c=Math.abs(this._castLightViewBoundingBox.center.z-fe)+pe;this._castLightViewBoundingBox.halfExtents.z=Math.max(this._castLightViewBoundingBox.center.z,c)}var l=this._castLightViewBoundingBox.halfExtents.z;this._shadowCameraFar=2*l+n;var _=this._castLightViewBoundingBox.center;if(de.set(_.x,_.y,_.z+l+n),V.transformMat4(de,de,te),A.fromRT(te,t.node.rotation,de),A.invert(ee,te),!i){var d=.5*s,f=.5*o;A.ortho(ie,-d,d,-f,f,.1,this._shadowCameraFar,r.capabilities.clipSpaceMinZ,r.capabilities.clipSpaceSignY),A.multiply(ne,ie,h),V.transformMat4(ae,de,ne);var p=2/e;he.set(p,p);var m=ae.x%he.x,w=ae.y%he.y;ue.set(ae.x-m,ae.y-w,ae.z),A.invert(se,ne),V.transformMat4(ce,ue,se),A.fromRT(te,t.node.rotation,ce),A.invert(ee,te),A.multiply(re,ie,ee),A.copy(this._matShadowView,ee),A.copy(this._matShadowProj,ie),A.copy(this._matShadowViewProj,re)}L.createOrtho(this._validFrustum,s,o,.1,this._shadowCameraFar,te)},e(t,[{key:"level",get:function(){return this._level}},{key:"shadowObjects",get:function(){return this._shadowObjects}},{key:"shadowCameraFar",get:function(){return this._shadowCameraFar},set:function(t){this._shadowCameraFar=t}},{key:"matShadowView",get:function(){return this._matShadowView},set:function(t){this._matShadowView=t}},{key:"matShadowProj",get:function(){return this._matShadowProj},set:function(t){this._matShadowProj=t}},{key:"matShadowViewProj",get:function(){return this._matShadowViewProj},set:function(t){this._matShadowViewProj=t}},{key:"validFrustum",get:function(){return this._validFrustum}},{key:"splitFrustum",get:function(){return this._splitFrustum}},{key:"lightViewFrustum",get:function(){return this._lightViewFrustum}},{key:"castLightViewBoundingBox",get:function(){return this._castLightViewBoundingBox}}]),t}(),we=function(t){function i(e){var i;return(i=t.call(this,e)||this)._splitCameraNear=0,i._splitCameraFar=0,i._csmAtlas=new N,i._calculateAtlas(e),i}r(i,t);var n=i.prototype;return n.destroy=function(){t.prototype.destroy.call(this)},n._calculateAtlas=function(t){var e=a.director.root.device.capabilities.clipSpaceSignY,i=t%2-.5,r=(.5-Math.floor(t/2))*e;this._csmAtlas.set(.5,.5,i,r)},e(i,[{key:"splitCameraNear",get:function(){return this._splitCameraNear},set:function(t){this._splitCameraNear=t}},{key:"splitCameraFar",get:function(){return this._splitCameraFar},set:function(t){this._splitCameraFar=t}},{key:"csmAtlas",get:function(){return this._csmAtlas},set:function(t){this._csmAtlas=t}}]),i}(me),ye=function(){function t(){this._castShadowObjects=[],this._layerObjects=new s(64),this._layers=[],this._levelCount=0,this._specialLayer=new me(1),this._shadowDistance=0;for(var t=0;t<nt.LEVEL_4;t++)this._layers[t]=new we(t)}var i=t.prototype;return i.update=function(t,e){var i=e.scene.mainLight;if(null!==i){var r=t.shadows,n=a.director.root.pipeline.pipelineSceneData.csmSupported?i.csmLevel:1,s=i.shadowDistance;r.enabled&&i.shadowEnabled&&(i.shadowFixedArea?this._updateFixedArea(i):((i.csmNeedUpdate||this._levelCount!==n||this._shadowDistance!==s)&&(this._splitFrustumLevels(i),this._levelCount=n,this._shadowDistance=s),this._calculateCSM(e,i,r)))}},i.destroy=function(){this._castShadowObjects.length=0;for(var t=0;t<this._layers.length;t++)this._layers[t].destroy();this._layers.length=0},i._updateFixedArea=function(t){var e=a.director.root.device,i=t.shadowOrthoSize,r=t.shadowOrthoSize,n=t.shadowNear,s=t.shadowFar;A.fromRT(te,t.node.worldRotation,t.node.worldPosition),A.invert(ee,te),A.ortho(ie,-i,i,-r,r,n,s,e.capabilities.clipSpaceMinZ,e.capabilities.clipSpaceSignY),A.multiply(re,ie,ee),this._specialLayer.matShadowView=ee,this._specialLayer.matShadowProj=ie,this._specialLayer.matShadowViewProj=re,this._specialLayer.calculateValidFrustumOrtho(2*i,2*r,n,s,te)},i._splitFrustumLevels=function(t){var e=.1,i=t.shadowDistance,r=i/e,n=a.director.root.pipeline.pipelineSceneData.csmSupported?t.csmLevel:1,s=t.csmLayerLambda;this._layers[0].splitCameraNear=e;for(var o=1;o<n;o++){var h=o/n,u=s*e*Math.pow(r,h)+(1-s)*(e+(i-e)*h),c=1.005*u;this._layers[o].splitCameraNear=u,this._layers[o-1].splitCameraFar=c}this._layers[n-1].splitCameraFar=i,t.csmNeedUpdate=!1},i._calculateCSM=function(t,e,i){var r=a.director.root.pipeline.pipelineSceneData.csmSupported?e.csmLevel:1,n=r>1?.5*i.size.x:i.size.x;if(!(n<0)){this._getCameraWorldMatrix($t,t);for(var s=r-1;s>=0;s--){var o=this._layers[s],h=o.splitCameraNear,u=o.splitCameraFar;o.calculateSplitFrustum(t,$t,h,u),o.createMatrix(e,n,!1)}r===nt.LEVEL_1?(this._specialLayer.shadowCameraFar=this._layers[0].shadowCameraFar,A.copy(this._specialLayer.matShadowView,this._layers[0].matShadowView),A.copy(this._specialLayer.matShadowProj,this._layers[0].matShadowProj),A.copy(this._specialLayer.matShadowViewProj,this._layers[0].matShadowViewProj),this._specialLayer.copyToValidFrustum(this._layers[0].validFrustum)):(this._specialLayer.calculateSplitFrustum(t,$t,.1,e.shadowDistance),this._specialLayer.createMatrix(e,n,!0))}},i._getCameraWorldMatrix=function(t,e){if(e.node){var i=e.node,r=i.worldPosition,n=i.worldRotation;A.fromRT(t,n,r)}},e(t,[{key:"castShadowObjects",get:function(){return this._castShadowObjects}},{key:"layerObjects",get:function(){return this._layerObjects}},{key:"layers",get:function(){return this._layers}},{key:"specialLayer",get:function(){return this._specialLayer}}]),t}();t("u",function(){function t(){this.fog=new ot,this.ambient=new at,this.skybox=new ht,this.shadows=new ut,this.csmLayers=new ye,this.octree=new zt,this.skin=new Ut,this.postSettings=new ct,this.lightProbes=a.internal.LightProbes?new a.internal.LightProbes:null,this.validPunctualLights=[],this.renderObjects=[],this.shadowFrameBufferMap=new Map,this._geometryRendererMaterials=[],this._geometryRendererPasses=[],this._geometryRendererShaders=[],this._occlusionQueryVertexBuffer=null,this._occlusionQueryIndicesBuffer=null,this._occlusionQueryInputAssembler=null,this._occlusionQueryMaterial=null,this._occlusionQueryShader=null,this._isHDR=!0,this._shadingScale=1,this._csmSupported=!0,this._standardSkinMeshRenderer=null,this._standardSkinModel=null,this._skinMaterialModel=null,this._shadingScale=1}var i=t.prototype;return i.activate=function(t){return this._device=t,this.initGeometryRendererMaterials(),this.initOcclusionQuery(),!0},i.initGeometryRendererMaterials=function(){for(var t=0,e=this._geometryRendererMaterials,i=0;i<6;i++){e[i]=new st,e[i]._uuid="geometry-renderer-material-"+i,e[i].initialize({effectName:"internal/builtin-geometry-renderer",technique:i});for(var r=e[i].passes,n=0;n<r.length;++n)this._geometryRendererPasses[t]=r[n],this._geometryRendererShaders[t]=r[n].getShaderVariant(),t++}},i.initOcclusionQuery=function(){if(this._occlusionQueryInputAssembler||(this._occlusionQueryInputAssembler=this._createOcclusionQueryIA()),!this._occlusionQueryMaterial){var t=new st;t._uuid="default-occlusion-query-material",t.initialize({effectName:"internal/builtin-occlusion-query"}),this._occlusionQueryMaterial=t,t.passes.length>0&&(this._occlusionQueryShader=t.passes[0].getShaderVariant())}},i.getOcclusionQueryPass=function(){return this._occlusionQueryMaterial&&this._occlusionQueryMaterial.passes.length>0?this._occlusionQueryMaterial.passes[0]:null},i.updatePipelineSceneData=function(){},i.destroy=function(){var t,e,i;this.shadows.destroy(),this.csmLayers.destroy(),this.validPunctualLights.length=0,null==(t=this._occlusionQueryInputAssembler)||t.destroy(),this._occlusionQueryInputAssembler=null,null==(e=this._occlusionQueryVertexBuffer)||e.destroy(),this._occlusionQueryVertexBuffer=null,null==(i=this._occlusionQueryIndicesBuffer)||i.destroy(),this._occlusionQueryIndicesBuffer=null,this._standardSkinMeshRenderer=null,this._standardSkinModel=null,this._skinMaterialModel=null},i._createOcclusionQueryIA=function(){var t=this._device,e=new Float32Array([-1,-1,-1,1,-1,-1,-1,1,-1,1,1,-1,-1,-1,1,1,-1,1,-1,1,1,1,1,1]),i=3*Float32Array.BYTES_PER_ELEMENT,r=8*i;this._occlusionQueryVertexBuffer=t.createBuffer(new E(I.VERTEX|I.TRANSFER_DST,b.DEVICE,r,i)),this._occlusionQueryVertexBuffer.update(e);var n=new Uint16Array([0,2,1,1,2,3,4,5,6,5,7,6,1,3,7,1,7,5,0,4,6,0,6,2,0,1,5,0,5,4,2,6,7,2,7,3]),s=Uint16Array.BYTES_PER_ELEMENT,o=36*s;this._occlusionQueryIndicesBuffer=t.createBuffer(new E(I.INDEX|I.TRANSFER_DST,b.DEVICE,o,s)),this._occlusionQueryIndicesBuffer.update(n);var a=[new R("a_position",d.RGB32F)],h=new x(a,[this._occlusionQueryVertexBuffer],this._occlusionQueryIndicesBuffer);return t.createInputAssembler(h)},e(t,[{key:"isHDR",get:function(){return this._isHDR},set:function(t){this._isHDR=t}},{key:"shadingScale",get:function(){return this._shadingScale},set:function(t){this._shadingScale=t}},{key:"csmSupported",get:function(){return this._csmSupported},set:function(t){this._csmSupported=t}},{key:"standardSkinModel",get:function(){return this._standardSkinModel},set:function(t){this._standardSkinModel=t}},{key:"standardSkinMeshRenderer",get:function(){return this._standardSkinMeshRenderer},set:function(t){this._standardSkinMeshRenderer&&this._standardSkinMeshRenderer!==t&&this._standardSkinMeshRenderer.clearGlobalStandardSkinObjectFlag(),this._standardSkinMeshRenderer=t,this.standardSkinModel=t?t.model:null}},{key:"skinMaterialModel",get:function(){return this._skinMaterialModel},set:function(t){this._skinMaterialModel=t}},{key:"geometryRendererPasses",get:function(){return this._geometryRendererPasses}},{key:"geometryRendererShaders",get:function(){return this._geometryRendererShaders}}]),t}()),t("P",Jt),function(t){t.RENDER_FRAME_BEGIN="render-frame-begin",t.RENDER_FRAME_END="render-frame-end",t.RENDER_CAMERA_BEGIN="render-camera-begin",t.RENDER_CAMERA_END="render-camera-end",t.ATTACHMENT_SCALE_CAHNGED="attachment-scale-changed"}(Jt||t("P",Jt={})),t("h",function(t){function e(){var e;return(e=t.call(this)||this).eventTargetOn=t.prototype.on,e.eventTargetOnce=t.prototype.once,e}r(e,t);var i=e.prototype;return i.on=function(t,e,i,r){return this.eventTargetOn(t,e,i,r)},i.once=function(t,e,i){return this.eventTargetOnce(t,e,i)},e}(o));var ge=t("x",{NONE:0,VERTEX_COLOR:1,VERTEX_NORMAL:2,VERTEX_TANGENT:3,WORLD_POS:4,VERTEX_MIRROR:5,FACE_SIDE:6,UV0:7,UV1:8,UV_LIGHTMAP:9,PROJ_DEPTH:10,LINEAR_DEPTH:11,FRAGMENT_NORMAL:12,FRAGMENT_TANGENT:13,FRAGMENT_BINORMAL:14,BASE_COLOR:15,DIFFUSE_COLOR:16,SPECULAR_COLOR:17,TRANSPARENCY:18,METALLIC:19,ROUGHNESS:20,SPECULAR_INTENSITY:21,IOR:22,DIRECT_DIFFUSE:23,DIRECT_SPECULAR:24,DIRECT_ALL:25,ENV_DIFFUSE:26,ENV_SPECULAR:27,ENV_ALL:28,EMISSIVE:29,LIGHT_MAP:30,SHADOW:31,AO:32,FRESNEL:33,DIRECT_TRANSMIT_DIFFUSE:34,DIRECT_TRANSMIT_SPECULAR:35,ENV_TRANSMIT_DIFFUSE:36,ENV_TRANSMIT_SPECULAR:37,TRANSMIT_ALL:38,DIRECT_TRT:39,ENV_TRT:40,TRT_ALL:41,FOG:42}),ve=t("v",{DIRECT_DIFFUSE:0,DIRECT_SPECULAR:1,ENV_DIFFUSE:2,ENV_SPECULAR:3,EMISSIVE:4,LIGHT_MAP:5,SHADOW:6,AO:7,NORMAL_MAP:8,FOG:9,TONE_MAPPING:10,GAMMA_CORRECTION:11,FRESNEL:12,TRANSMIT_DIFFUSE:13,TRANSMIT_SPECULAR:14,TRT:15,TT:16,MAX_BIT_COUNT:17});t("D",function(){function t(){this._singleMode=ge.NONE,this._compositeModeValue=0,this._lightingWithAlbedo=!0,this._csmLayerColoration=!1,this._activate()}var i=t.prototype;return i.isCompositeModeEnabled=function(t){return!!(this._compositeModeValue&1<<t)},i.enableCompositeMode=function(t,e){this._enableCompositeMode(t,e),this._updatePipeline()},i.enableAllCompositeMode=function(t){this._enableAllCompositeMode(t),this._updatePipeline()},i.isEnabled=function(){return 0!==this._getType()},i.reset=function(){this._activate(),this._updatePipeline()},i._activate=function(){this._singleMode=ge.NONE,this._enableAllCompositeMode(!0),this._lightingWithAlbedo=!0,this._csmLayerColoration=!1},i._updatePipeline=function(){var t=a.director.root,e=t.pipeline,i=this._getType();e.macros.CC_USE_DEBUG_VIEW!==i&&(e.macros.CC_USE_DEBUG_VIEW=i,t.onGlobalPipelineStateChanged())},i._enableCompositeMode=function(t,e){e?this._compositeModeValue|=1<<t:this._compositeModeValue&=~(1<<t)},i._enableAllCompositeMode=function(t){for(var e=0;e<ve.MAX_BIT_COUNT;e++)t?this._compositeModeValue|=1<<e:this._compositeModeValue&=~(1<<e)},i._getType=function(){if(this._singleMode!==ge.NONE)return 1;if(!0!==this._lightingWithAlbedo||!1!==this._csmLayerColoration)return 2;for(var t=0;t<ve.MAX_BIT_COUNT;t++)if(!this.isCompositeModeEnabled(t))return 2;return 0},e(t,[{key:"singleMode",get:function(){return this._singleMode},set:function(t){this._singleMode=t,this._updatePipeline()}},{key:"lightingWithAlbedo",get:function(){return this._lightingWithAlbedo},set:function(t){this._lightingWithAlbedo=t,this._updatePipeline()}},{key:"csmLayerColoration",get:function(){return this._csmLayerColoration},set:function(t){this._csmLayerColoration=t,this._updatePipeline()}},{key:"debugViewType",get:function(){return this._getType()}}]),t}())}}}));
