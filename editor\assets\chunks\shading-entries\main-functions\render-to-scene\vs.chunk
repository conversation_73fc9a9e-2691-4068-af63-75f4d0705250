void main()
{
  SurfacesStandardVertexIntermediate In;

  // Local Space
  CCSurfacesVertexInput(In);
  CCSurfacesVertexAnimation(In);
  In.position.xyz = SurfacesVertexModifyLocalPos(In);
  In.normal.xyz = SurfacesVertexModifyLocalNormal(In);
  #if CC_SURFACES_USE_TANGENT_SPACE
    In.tangent = SurfacesVertexModifyLocalTangent(In);
  #endif
  SurfacesVertexModifyLocalSharedData(In);

  // World Space
  CCSurfacesVertexWorldTransform(In);
  In.worldPos = SurfacesVertexModifyWorldPos(In);

  // Clip Space
  In.clipPos = cc_matProj * cc_matView * vec4(In.worldPos, 1.0);
  In.clipPos = SurfacesVertexModifyClipPos(In);

  // Other Surfaces Function
  vec3 viewDirect = normalize(cc_cameraPos.xyz - In.worldPos);
  In.worldNormal.w = dot(In.worldNormal.xyz, viewDirect) < 0.0 ? -1.0 : 1.0;
  In.worldNormal.xyz = SurfacesVertexModifyWorldNormal(In);

  SurfacesVertexModifyUV(In);
  SurfacesVertexModifySharedData(In);

  // Other Data
  CCSurfacesVertexTransformUV(In);
  CCSurfacesVertexTransferFog(In);
  CCSurfacesVertexTransferLightMapUV(In);

  CCSurfacesVertexTransferShadow(In);
  #if CC_RECEIVE_SHADOW || CC_USE_REFLECTION_PROBE
    In.shadowBiasAndProbeId.xy = SurfacesVertexModifyShadowBias(In, In.shadowBiasAndProbeId.xy);
  #endif

  CCSurfacesVertexOutput(In);
}
