// Copyright (c) 2017-2024 Xiamen Yaji Software Co., Ltd.

#pragma rate Pipeline pass
uniform Pipeline {
    vec4 g_platform; // x: isMobile, y: unused, z: unused, w: graphicsAPI (0: vulkan, 1: metal, 2: unused, 3: opengl)
};

// Vulkan 1.0, Flip NDC Y
#pragma define FLIP_VULKAN_NDC(vec) (vec).y = g_platform.w == 0.0 ? -(vec).y : (vec).y

// GL_Like, Sample from RT
#pragma define FLIP_SAMPLE_FROM_RT(vec) (vec).y = (g_platform.w > 1.0 ? 1.0-(vec).y : (vec).y)
