System.register(["./global-exports-CR3GRnjt.js","./director-DIlqD2Nd.js","./deprecated-T2yM9nsJ.js","./gc-object-CKHc4SnS.js","./index-C5lmLqDW.js","./component-BaGvu7EF.js","./debug-view-CKetkq9d.js","./device-manager-BvjvoelW.js","./buffer-barrier-CuX_5NUR.js","./pipeline-state-manager-Cdpe3is6.js","./scene-7MDSMR3j.js","./node-event-DTNosVQv.js","./touch-DB0AR-Sc.js","./factory-D9_8ZCqM.js","./prefab-DH0xadMc.js","./deprecated-Ca3AjUwj.js"],(function(e){"use strict";var t,n,r,i,a,o,s,u,d,l,g,c,y,p,f,h,m;return{setters:[null,null,function(e){t=e.g,n=e.G},function(e){r=e.E,i=e.h,a=e.s,o=e.S,s=e._,u=e.w,d=e.a,l=e.b},function(e){g=e.c,c=e.t,y=e.d,p=e.a,f=e.g,h=e.s},function(e){m=e.C},null,null,null,null,null,null,null,null,null,null],execute:function(){t.on(n.EVENT_POST_SUBSYSTEM_INIT,(function(){x.init()}));var _,L,v,j,M,S,x=e("SortingLayers",function(){function e(){}return e.getSortingPriority=function(e,t){return void 0===e&&(e=0),void 0===t&&(t=0),(e+32768<<16|t+32768)>>>0},e.getLayerIndex=function(e){void 0===e&&(e=0);var t=0;return this.indexMap.has(e)?t=this.indexMap.get(e):i(2105),t},e.getLayerIndexByName=function(e){var t=this.getLayerByName(e);return this.getLayerIndex(t)},e.getLayerName=function(e){void 0===e&&(e=0);var t="";return this.nameMap.has(e)?t=this.nameMap.get(e):i(2105),t},e.getLayerByName=function(e){for(var t=this.nameMap.size,n=this.nameMap.keys(),r=0,a=0;a<t;a++)if(r=n.next().value,this.nameMap.get(r)===e)return r;return i(2106),0},e.isLayerValid=function(e){return!!this.indexMap.has(e)||(i(2105),!1)},e.getBuiltinLayers=function(){return[{id:0,name:"default",value:0}]},e.init=function(){var t=a.querySettings(o.ENGINE,"sortingLayers");t&&0!==t.length||(t=this.getBuiltinLayers()),e.resetState();for(var n=0;n<t.length;n++){var i=t[n];e.setLayer(i.id,i.name,i.value),e.Enum[i.name]=i.id}r.update(e.Enum),r.sortList(e.Enum,(function(t,n){return e.getLayerIndex(t.value)-e.getLayerIndex(n.value)}))},e.setLayer=function(e,t,n){this.nameMap.set(e,t),this.indexMap.set(e,n)},e.resetState=function(){for(var t=Object.keys(e.Enum),n=0;n<t.length;n++)delete e.Enum[e.Enum[t[n]]],delete e.Enum[t[n]];e.indexMap.clear(),e.nameMap.clear()},e}());x.nameMap=new Map,x.indexMap=new Map,x.Enum=r({default:0}),e("Sorting",(_=g("cc.Sorting"),L=c(x.Enum),_(v=y((j=function(e){function t(){var t;return(t=e.call(this)||this)._sortingLayer=M&&M(),t._sortingOrder=S&&S(),t._modelRenderer=null,t}s(t,e);var n=t.prototype;return n.__preload=function(){this._modelRenderer=this.getComponent("cc.ModelRenderer"),this._modelRenderer||u(16301,this.node.name),this._updateSortingPriority()},n._updateSortingPriority=function(){var e=x.getLayerIndex(this._sortingLayer),t=x.getSortingPriority(e,this._sortingOrder);this._modelRenderer&&this._modelRenderer.isValid&&(this._modelRenderer.priority=t)},d(t,[{key:"sortingLayer",get:function(){return this._sortingLayer},set:function(e){e!==this._sortingLayer&&x.isLayerValid(e)&&(this._sortingLayer=e,this._updateSortingPriority())}},{key:"sortingOrder",get:function(){return this._sortingOrder},set:function(e){e!==this._sortingOrder&&(this._sortingOrder=f(e,-32768,32767),this._updateSortingPriority())}}]),t}(m),l(j.prototype,"sortingLayer",[L],Object.getOwnPropertyDescriptor(j.prototype,"sortingLayer"),j.prototype),M=p(j.prototype,"_sortingLayer",[h],(function(){return x.Enum.default})),S=p(j.prototype,"_sortingOrder",[h],(function(){return 0})),v=j))||v)||v))}}}));
