System.register(["./gc-object-D18ulfCO.js","./index-Y4La_nfG.js","./global-exports-CLZKKIY2.js","./debug-view-BP17WHcy.js","./prefab-BQYc0LyR.js","./scene-ArUG4OfI.js","./pipeline-state-manager-DQyhxoC_.js","./node-event-DTNosVQv.js","./component-CsuvAQKv.js","./device-manager-B8lyUDPi.js","./buffer-barrier-q_79u36H.js"],(function(t){"use strict";var e,r,i,o,n,s,a,c,h,_,u,p,f,y,m,l,g,T,d,v,P,S,E,w,x,O,b,k,C,j;return{setters:[function(t){e=t._,r=t.a,i=t.b,o=t.E},function(t){n=t.c,s=t.d,a=t.a,c=t.p,h=t.s,_=t.b,u=t.t,p=t.R,f=t.f,y=t.C,m=t.k},function(t){l=t.c},function(t){g=t.S,T=t.C,d=t.a,v=t.b,P=t.c,S=t.d,E=t.R,w=t.e,x=t.T},null,function(t){O=t.T},function(t){b=t.L,k=t.C},null,function(t){C=t.C},null,function(t){j=t.C}],execute:function(){var A,D,R,F,I,L,H,N,W,G,z,U,V,M,X,Y,B,K,q,J,Q,Z,$,tt,et,rt,it,ot,nt,st,at,ct,ht,_t,ut,pt,ft,yt,mt,lt,gt,Tt,dt=t("P",n("cc.PostProcess")(A=s((L=function(t){function i(){var e;return(e=t.call(this)||this).global=R&&R(),e._shadingScale=F&&F(),e.enableShadingScaleInEditor=I&&I(),e.settings=new Map,e}e(i,t);var o=i.prototype;return o.addSetting=function(t){this.settings.set(t.constructor,t)},o.removeSetting=function(t){this.settings.delete(t.constructor)},o.getSetting=function(t){return this.settings.get(t)},o.onEnable=function(){i.all.push(this)},o.onDisable=function(){var t=i.all.indexOf(this);-1!==t&&i.all.splice(t,1)},r(i,[{key:"shadingScale",get:function(){return this._shadingScale},set:function(t){this._shadingScale=t}}]),i}(C),L.all=[],R=a((D=L).prototype,"global",[c,h],(function(){return!0})),F=a(D.prototype,"_shadingScale",[h],(function(){return 1})),i(D.prototype,"shadingScale",[c],Object.getOwnPropertyDescriptor(D.prototype,"shadingScale"),D.prototype),I=a(D.prototype,"enableShadingScaleInEditor",[c,h],(function(){return!1})),A=D))||A)||A),vt=new _,Pt=o(T),St=o(d),Et=o(v),wt=o(P),xt=o(S),Ot=o({SKYBOX:g.VALUE|j.DEPTH_STENCIL,SOLID_COLOR:j.ALL,DEPTH_ONLY:j.DEPTH_STENCIL,DONT_CLEAR:j.NONE});t("a",Tt),function(t){t.TARGET_TEXTURE_CHANGE="tex-change"}(Tt||t("a",Tt={}));var bt=t("C",(H=n("cc.Camera"),N=u(b.BitMask),W=u(Ot),G=u(Pt),z=u(St),U=u(Et),V=u(wt),M=u(xt),X=u(E),Y=u(dt),H((gt=function(t){function i(){var e;return(e=t.call(this)||this)._projection=q&&q(),e._priority=J&&J(),e._fov=Q&&Q(),e._fovAxis=Z&&Z(),e._orthoHeight=$&&$(),e._near=tt&&tt(),e._far=et&&et(),e._color=rt&&rt(),e._depth=it&&it(),e._stencil=ot&&ot(),e._clearFlags=nt&&nt(),e._rect=st&&st(),e._aperture=at&&at(),e._shutter=ct&&ct(),e._iso=ht&&ht(),e._screenScale=_t&&_t(),e._visibility=ut&&ut(),e._targetTexture=pt&&pt(),e._postProcess=ft&&ft(),e._usePostProcess=yt&&yt(),e._camera=null,e._inEditorMode=!1,e._flows=void 0,e._cameraType=mt&&mt(),e._trackingType=lt&&lt(),e}e(i,t);var o=i.prototype;return o.onLoad=function(){this._createCamera()},o.onEnable=function(){this.node.hasChangedFlags|=O.POSITION,this._camera&&this._attachToScene()},o.onDisable=function(){this._camera&&this._detachFromScene()},o.onDestroy=function(){this._camera&&(this._camera.destroy(),this._camera=null),this._targetTexture&&this._targetTexture.off("resize")},o.screenPointToRay=function(t,e,r){return r||(r=p.create()),this._camera&&this._camera.screenPointToRay(r,t,e),r},o.worldToScreen=function(t,e){return e||(e=new _),this._camera&&this._camera.worldToScreen(e,t),e},o.screenToWorld=function(t,e){return e||(e=this.node.getWorldPosition()),this._camera&&this._camera.screenToWorld(e,t),e},o.convertToUINode=function(t,e,r){if(r||(r=new _),!this._camera)return r;this.worldToScreen(t,vt);var i=e.getComponent("cc.UITransform"),o=l.view,n=o.getVisibleSize(),s=vt.x-.5*this._camera.width,a=vt.y-.5*this._camera.height;return vt.x=s/o.getScaleX()+.5*n.width,vt.y=a/o.getScaleY()+.5*n.height,i&&i.convertToNodeSpaceAR(vt,r),r},o._createCamera=function(){this._camera||(this._camera=l.director.root.createCamera(),this._camera.initialize({name:this.node.name,node:this.node,projection:this._projection,window:this._inEditorMode?l.director.root&&l.director.root.mainWindow:l.director.root&&l.director.root.tempWindow,priority:this._priority,cameraType:this.cameraType,trackingType:this.trackingType}),this._camera.setViewportInOrientedSpace(this._rect),this._camera.fovAxis=this._fovAxis,this._camera.fov=f(this._fov),this._camera.orthoHeight=this._orthoHeight,this._camera.nearClip=this._near,this._camera.farClip=this._far,this._camera.clearColor=this._color,this._camera.clearDepth=this._depth,this._camera.clearStencil=this._stencil,this._camera.clearFlag=this._clearFlags,this._camera.visibility=this._visibility,this._camera.aperture=this._aperture,this._camera.shutter=this._shutter,this._camera.iso=this._iso,this._camera.postProcess=this._postProcess,this._camera.usePostProcess=this._usePostProcess),this._updateTargetTexture()},o._attachToScene=function(){this.node.scene&&this._camera&&(this._camera&&this._camera.scene&&this._camera.scene.removeCamera(this._camera),this._getRenderScene().addCamera(this._camera))},o._detachFromScene=function(){this._camera&&this._camera.scene&&this._camera.scene.removeCamera(this._camera)},o._checkTargetTextureEvent=function(t){var e=this;t&&t.off("resize"),this._targetTexture&&this._targetTexture.on("resize",(function(t){e._camera&&e._camera.setFixedSize(t.width,t.height)}),this)},o._updateTargetTexture=function(){if(this._camera&&this._targetTexture){var t=this._targetTexture.window;this._camera.changeTargetWindow(t),this._camera.setFixedSize(t.width,t.height)}},r(i,[{key:"camera",get:function(){return this._camera}},{key:"priority",get:function(){return this._priority},set:function(t){this._priority=t,this._camera&&(this._camera.priority=t)}},{key:"visibility",get:function(){return this._visibility},set:function(t){this._visibility=t,this._camera&&(this._camera.visibility=t)}},{key:"clearFlags",get:function(){return this._clearFlags},set:function(t){this._clearFlags=t,this._camera&&(this._camera.clearFlag=t)}},{key:"clearColor",get:function(){return this._color},set:function(t){this._color.set(t),this._camera&&(this._camera.clearColor=this._color)}},{key:"clearDepth",get:function(){return this._depth},set:function(t){this._depth=t,this._camera&&(this._camera.clearDepth=t)}},{key:"clearStencil",get:function(){return this._stencil},set:function(t){this._stencil=t,this._camera&&(this._camera.clearStencil=t)}},{key:"projection",get:function(){return this._projection},set:function(t){this._projection=t,this._camera&&(this._camera.projectionType=t)}},{key:"fovAxis",get:function(){return this._fovAxis},set:function(t){t!==this._fovAxis&&(this._fovAxis=t,this._camera&&(this._camera.fovAxis=t,t===d.VERTICAL?this.fov=this._fov*this._camera.aspect:this.fov=this._fov/this._camera.aspect))}},{key:"fov",get:function(){return this._fov},set:function(t){this._fov=t,this._camera&&(this._camera.fov=f(t))}},{key:"orthoHeight",get:function(){return this._orthoHeight},set:function(t){this._orthoHeight=t,this._camera&&(this._camera.orthoHeight=t)}},{key:"near",get:function(){return this._near},set:function(t){this._near=t,this._camera&&(this._camera.nearClip=t)}},{key:"far",get:function(){return this._far},set:function(t){this._far=t,this._camera&&(this._camera.farClip=t)}},{key:"aperture",get:function(){return this._aperture},set:function(t){this._aperture=t,this._camera&&(this._camera.aperture=t)}},{key:"shutter",get:function(){return this._shutter},set:function(t){this._shutter=t,this._camera&&(this._camera.shutter=t)}},{key:"iso",get:function(){return this._iso},set:function(t){this._iso=t,this._camera&&(this._camera.iso=t)}},{key:"rect",get:function(){return this._rect},set:function(t){this._rect=t,this._camera&&this._camera.setViewportInOrientedSpace(t)}},{key:"targetTexture",get:function(){return this._targetTexture},set:function(t){if(this._targetTexture!==t){var e=this._targetTexture;this._targetTexture=t,this._checkTargetTextureEvent(e),this._updateTargetTexture(),!t&&this._camera&&(this._camera.changeTargetWindow(null),this._camera.isWindowSize=!0),this.node.emit(Tt.TARGET_TEXTURE_CHANGE,this)}}},{key:"usePostProcess",get:function(){return this._usePostProcess},set:function(t){this._usePostProcess=t,this._camera&&(this._camera.usePostProcess=t)}},{key:"postProcess",get:function(){return this._postProcess},set:function(t){this._postProcess=t,this._camera&&(this._camera.postProcess=t)}},{key:"screenScale",get:function(){return this._screenScale},set:function(t){this._screenScale=t,this._camera&&(this._camera.screenScale=t)}},{key:"inEditorMode",get:function(){return this._inEditorMode},set:function(t){if(this._inEditorMode=t,this._camera){var e=l.director.root;this._camera.changeTargetWindow(t?e&&e.mainWindow:e&&e.tempWindow)}}},{key:"cameraType",get:function(){return this._cameraType},set:function(t){this._cameraType!==t&&(this._cameraType=t,this.camera&&(this.camera.cameraType=t))}},{key:"trackingType",get:function(){return this._trackingType},set:function(t){this._trackingType!==t&&(this._trackingType=t,this.camera&&(this.camera.trackingType=t))}}]),i}(C),gt.ProjectionType=Pt,gt.FOVAxis=St,gt.ClearFlag=Ot,gt.Aperture=Et,gt.Shutter=wt,gt.ISO=xt,gt.TARGET_TEXTURE_CHANGE=Tt.TARGET_TEXTURE_CHANGE,q=a((K=gt).prototype,"_projection",[h],(function(){return Pt.PERSPECTIVE})),J=a(K.prototype,"_priority",[h],(function(){return 0})),Q=a(K.prototype,"_fov",[h],(function(){return 45})),Z=a(K.prototype,"_fovAxis",[h],(function(){return St.VERTICAL})),$=a(K.prototype,"_orthoHeight",[h],(function(){return 10})),tt=a(K.prototype,"_near",[h],(function(){return 1})),et=a(K.prototype,"_far",[h],(function(){return 1e3})),rt=a(K.prototype,"_color",[h],(function(){return new y("#333333")})),it=a(K.prototype,"_depth",[h],(function(){return 1})),ot=a(K.prototype,"_stencil",[h],(function(){return 0})),nt=a(K.prototype,"_clearFlags",[h],(function(){return Ot.SOLID_COLOR})),st=a(K.prototype,"_rect",[h],(function(){return new m(0,0,1,1)})),at=a(K.prototype,"_aperture",[h],(function(){return Et.F16_0})),ct=a(K.prototype,"_shutter",[h],(function(){return wt.D125})),ht=a(K.prototype,"_iso",[h],(function(){return xt.ISO100})),_t=a(K.prototype,"_screenScale",[h],(function(){return 1})),ut=a(K.prototype,"_visibility",[h],(function(){return k})),pt=a(K.prototype,"_targetTexture",[h],(function(){return null})),ft=a(K.prototype,"_postProcess",[h],(function(){return null})),yt=a(K.prototype,"_usePostProcess",[h],(function(){return!1})),mt=a(K.prototype,"_cameraType",[h],(function(){return w.DEFAULT})),lt=a(K.prototype,"_trackingType",[h],(function(){return x.NO_TRACKING})),i(K.prototype,"visibility",[N],Object.getOwnPropertyDescriptor(K.prototype,"visibility"),K.prototype),i(K.prototype,"clearFlags",[W],Object.getOwnPropertyDescriptor(K.prototype,"clearFlags"),K.prototype),i(K.prototype,"projection",[G],Object.getOwnPropertyDescriptor(K.prototype,"projection"),K.prototype),i(K.prototype,"fovAxis",[z],Object.getOwnPropertyDescriptor(K.prototype,"fovAxis"),K.prototype),i(K.prototype,"aperture",[U],Object.getOwnPropertyDescriptor(K.prototype,"aperture"),K.prototype),i(K.prototype,"shutter",[V],Object.getOwnPropertyDescriptor(K.prototype,"shutter"),K.prototype),i(K.prototype,"iso",[M],Object.getOwnPropertyDescriptor(K.prototype,"iso"),K.prototype),i(K.prototype,"targetTexture",[X],Object.getOwnPropertyDescriptor(K.prototype,"targetTexture"),K.prototype),i(K.prototype,"usePostProcess",[c],Object.getOwnPropertyDescriptor(K.prototype,"usePostProcess"),K.prototype),i(K.prototype,"postProcess",[Y],Object.getOwnPropertyDescriptor(K.prototype,"postProcess"),K.prototype),B=K))||B));l.Camera=bt}}}));
