System.register(["./gc-object-D18ulfCO.js","./index-Y4La_nfG.js","./global-exports-CLZKKIY2.js","./skeleton-BrWwQslM.js","./scene-ArUG4OfI.js","./mesh-renderer-CTCVb-Pf.js","./device-manager-B8lyUDPi.js","./buffer-barrier-q_79u36H.js","./pipeline-state-manager-DQyhxoC_.js","./deprecated-D5UVm7fE.js","./deprecated-6ty78xzL.js","./debug-view-BP17WHcy.js","./deprecated-Bf8XgTPJ.js","./director-8iUu7HD2.js","./factory-BOc5khhM.js","./component-CsuvAQKv.js","./mesh-Ba1cTOGw.js"],(function(t){"use strict";var e,i,n,r,o,s,a,u,l,f,h,d,c,p,_,m,v,T,g,y,x,b,M,w,I,A,k,B,N,E,<PERSON>,R,j,<PERSON>,O,C,D,F,U,J,H,z,G,L,W,V,K,X,Y,q,Z,Q,$,tt,et,it,nt,rt;return{setters:[function(t){e=t.a,i=t.o,n=t._,r=t.w,o=t.k,s=t.b,a=t.F,u=t.f,l=t.j},function(t){f=t.M,h=t.Q,d=t.b,c=t.F,p=t.c,_=t.t,m=t.a,v=t.i,T=t.H,g=t.V,y=t.s},function(t){x=t.c},function(t){b=t.S},function(t){M=t.B,w=t.h,I=t.N,A=t.b},function(t){k=t.a,B=t.M},null,function(t){N=t.ad,E=t.v,S=t.ae,R=t.F,j=t.o,P=t.c,O=t.B,C=t.b,D=t.M,F=t.A,U=t.a,J=t.f,H=t.j},function(t){z=t.m,G=t.n,L=t.o,W=t.p,V=t.q,K=t.I},function(t){X=t.d},function(t){Y=t.T},function(t){q=t.r,Z=t.w,Q=t.m},null,function(t){$=t.d},function(t){tt=t.P,et=t.I,it=t.T,nt=t.W},null,function(t){rt=t.M}],execute:function(){t({c:Mt,d:It,e:wt,f:function(t,e){for(var i=t,n="";null!==i&&i!==e;)n=i.name+"/"+n,i=i.parent;return n.slice(0,-1)},g:ut});var ot=t("B",Symbol("BakeNodeCurves")),st=t("S",function(){function t(){}return t.getOrExtract=function(e){var i=t.pool.get(e);if(!i||i.samples!==e.sample){i&&x.director.root.dataPoolManager.releaseAnimationClip(e);var n=Math.ceil(e.sample*e.duration)+1,r=e.sample;i=e[ot](0,r,n),t.pool.set(e,i)}return i},t.destroy=function(e){t.pool.delete(e)},t}());st.pool=new Map;var at=new f;function ut(t,e,i){for(f.identity(i);t!==e;)f.fromRTS(at,t.rotation,t.position,t.scale),f.multiply(i,at,i),t=t.parent;return i}var lt=new N(E.POINT,E.POINT,E.NONE,S.CLAMP,S.CLAMP,S.CLAMP),ft=function(t,e,i){t[e+0]=i.m00,t[e+1]=i.m01,t[e+2]=i.m02,t[e+3]=i.m12,t[e+4]=i.m04,t[e+5]=i.m05,t[e+6]=i.m06,t[e+7]=i.m13,t[e+8]=i.m08,t[e+9]=i.m09,t[e+10]=i.m10,t[e+11]=i.m14};function ht(t){return t.getFormatFeatures(R.RGBA32F)&j.SAMPLED_TEXTURE?R.RGBA32F:R.RGBA8}function dt(t,e){var i=4/Math.sqrt(e);return 12*Math.ceil(Math.max(480*i,t)/12)}new h,new h,new d,new h,new d;var ct=new d,pt=new d,_t=new d,mt=new d,vt=new f,Tt=new f,gt=new c,yt=Number.MAX_SAFE_INTEGER,xt=(t("J",function(){function t(t){this._device=void 0,this._pool=void 0,this._textureBuffers=new Map,this._formatSize=void 0,this._pixelsPerJoint=void 0,this._customPool=void 0,this._chunkIdxMap=new Map,this._device=t;var e=ht(this._device);this._formatSize=P[e].size,this._pixelsPerJoint=48/this._formatSize,this._pool=new Y(t),this._pool.initialize({format:e,roundUpFn:dt}),this._customPool=new Y(t),this._customPool.initialize({format:e,roundUpFn:dt})}var i=t.prototype;return i.clear=function(){this._pool.destroy(),this._textureBuffers.clear()},i.registerCustomTextureLayouts=function(t){for(var e=0;e<t.length;e++){var i=t[e],n=i.textureLength;this._device.getFormatFeatures(R.RGBA32F)&j.SAMPLED_TEXTURE||(n*=2);for(var r=this._customPool.createChunk(n),o=0;o<i.contents.length;o++){var s=i.contents[o],a=s.skeleton;this._chunkIdxMap.set(a,r);for(var u=0;u<s.clips.length;u++){var l=s.clips[u];this._chunkIdxMap.set(a^l,r)}}}},i.getDefaultPoseTexture=function(t,e,i){var n=0^t.hash,r=this._textureBuffers.get(n)||null;if(r&&r.bounds.has(e.hash))return r.refCount++,r;var o=t.joints,s=t.bindposes,a=null,u=!1,l=o.length;if(r)r.refCount++;else{var h=12*l,p=this._chunkIdxMap.get(n),_=void 0!==p?this._customPool.alloc(h*Float32Array.BYTES_PER_ELEMENT,p):this._pool.alloc(h*Float32Array.BYTES_PER_ELEMENT);if(!_)return r;r={pixelOffset:_.start/this._formatSize,refCount:1,bounds:new Map,skeletonHash:t.hash,clipHash:0,readyToBeDeleted:!1,handle:_},a=new Float32Array(h),u=!0}d.set(_t,yt,yt,yt),d.set(mt,-yt,-yt,-yt);for(var m=e.getBoneSpaceBounds(t),v=0,T=0;v<l;v++,T+=12){var g=i.getChildByPath(o[v]),y=g?ut(g,i,vt):t.inverseBindposes[v],x=m[v];x&&(c.transform(gt,x,y),gt.getBoundary(ct,pt),d.min(_t,_t,ct),d.max(mt,mt,pt)),u&&(g&&f.multiply(y,y,s[v]),ft(a,T,g?y:f.IDENTITY))}var b=[new c];return r.bounds.set(e.hash,b),c.fromPoints(b[0],_t,mt),u&&(this._pool.update(r.handle,a.buffer),this._textureBuffers.set(n,r)),r},i.getSequencePoseTexture=function(t,e,i,n){var r=t.hash^e.hash,o=this._textureBuffers.get(r)||null;if(o&&o.bounds.has(i.hash))return o.refCount++,o;var s=t.joints,a=t.bindposes,u=st.getOrExtract(e).frames,l=null,h=!1,p=s.length;if(o)o.refCount++;else{var _=12*p*u,m=this._chunkIdxMap.get(r),v=void 0!==m?this._customPool.alloc(_*Float32Array.BYTES_PER_ELEMENT,m):this._pool.alloc(_*Float32Array.BYTES_PER_ELEMENT);if(!v)return null;var T=this._createAnimInfos(t,e,n);o={pixelOffset:v.start/this._formatSize,refCount:1,bounds:new Map,skeletonHash:t.hash,clipHash:e.hash,readyToBeDeleted:!1,handle:v,animInfos:T},l=new Float32Array(_),h=!0}var g=i.getBoneSpaceBounds(t),y=[];o.bounds.set(i.hash,y);for(var x=0;x<u;x++)y.push(new c(yt,yt,yt,-yt,-yt,-yt));for(var b=0,M=0;b<u;b++){for(var w=y[b],I=0;I<p;I++,M+=12){var A=o.animInfos[I],k=A.curveData,B=A.downstream,N=A.bindposeIdx,E=A.bindposeCorrection,S=void 0,R=!0;k&&B?S=f.multiply(vt,k[b],B):k?S=k[b]:B?S=B:(S=t.inverseBindposes[N],R=!1);var j=g[I];if(j){var P=E?f.multiply(Tt,S,E):S;c.transform(gt,j,P),gt.getBoundary(ct,pt),d.min(w.center,w.center,ct),d.max(w.halfExtents,w.halfExtents,pt)}h&&(R&&f.multiply(vt,S,a[N]),ft(l,M,R?vt:f.IDENTITY))}c.fromPoints(w,w.center,w.halfExtents)}return h&&(this._pool.update(o.handle,l.buffer),this._textureBuffers.set(r,o)),o},i.releaseHandle=function(t){if(t.refCount>0&&t.refCount--,!t.refCount&&t.readyToBeDeleted){var e=t.skeletonHash^t.clipHash;(void 0!==this._chunkIdxMap.get(e)?this._customPool:this._pool).free(t.handle),this._textureBuffers.get(e)===t&&this._textureBuffers.delete(e)}},i.releaseSkeleton=function(t){for(var e=this._textureBuffers.values(),i=e.next();!i.done;){var n=i.value;n.skeletonHash===t.hash&&(n.readyToBeDeleted=!0,n.refCount?this._textureBuffers.delete(n.skeletonHash^n.clipHash):this.releaseHandle(n)),i=e.next()}},i.releaseAnimationClip=function(t){for(var e=this._textureBuffers.values(),i=e.next();!i.done;){var n=i.value;n.clipHash===t.hash&&(n.readyToBeDeleted=!0,n.refCount?this._textureBuffers.delete(n.skeletonHash^n.clipHash):this.releaseHandle(n)),i=e.next()}},i._createAnimInfos=function(t,e,i){for(var n=[],r=t.joints,o=t.bindposes,s=r.length,a=st.getOrExtract(e),u=0;u<s;u++){for(var l=r[u],h=a.joints[l],d=i.getChildByPath(l),c=void 0,p=void 0;!h;){var _=l.lastIndexOf("/");if(l=l.substring(0,_),h=a.joints[l],d?(c||(c=new f),f.fromRTS(vt,d.rotation,d.position,d.scale),f.multiply(c,vt,c),d=d.parent):p=l,_<0)break}var m=u,v=void 0;if(void 0!==p&&h){m=u-1;for(var T=0;T<s;T++)if(r[T]===p){m=T,v=new f,f.multiply(v,o[T],t.inverseBindposes[u]);break}}n.push({curveData:h&&h.transforms,downstream:c,bindposeIdx:m,bindposeCorrection:v})}return n},e(t,[{key:"pixelsPerJoint",get:function(){return this._pixelsPerJoint}}]),t}()),t("a",function(){function t(t){this._pool=new Map,this._device=void 0,this._device=t}var e=t.prototype;return e.getData=function(t){void 0===t&&(t="-1");var e=this._pool.get(t);if(e)return e;var i=this._device.createBuffer(new O(C.UNIFORM|C.TRANSFER_DST,D.HOST|D.DEVICE,z.SIZE,z.SIZE)),n=new Float32Array([0,0,0,0]);i.update(n);var r={buffer:i,data:n,dirty:!1,dirtyForJSB:new Uint8Array([0]),currentClip:null};return this._pool.set(t,r),r},e.destroy=function(t){var e=this._pool.get(t);e&&(e.buffer.destroy(),this._pool.delete(t))},e.switchClip=function(t,e){return t.currentClip=e,t.data[0]=0,t.buffer.update(t.data),t.dirty=!1,t},e.clear=function(){for(var t,e=i(this._pool.values());!(t=e()).done;)t.value.buffer.destroy();this._pool.clear()},t}()),[]),bt=new Map;function Mt(t,e){for(var i=0,n=f.IDENTITY;t;){if(t.stamp===e||t.stamp+1===e&&!t.node.hasChangedFlags){n=t.world,t.stamp=e;break}t.stamp=e,xt[i++]=t,t=t.parent}for(;i>0;){t=xt[--i],xt[i]=null;var r=t.node;f.fromRTS(t.local,r.rotation,r.position,r.scale),n=f.multiply(t.world,n,t.local)}return n}function wt(t,e){for(var i,n=null,r=0;t!==e;){var o=t.uuid;if(bt.has(o)){n=bt.get(o);break}n={node:t,local:new f,world:new f,stamp:-1,parent:null},bt.set(o,n),xt[r++]=n,t=t.parent,n=null}for(;r>0;)i=xt[--r],xt[r]=null,i.parent=n,n=i;return n}function It(t){for(var e=bt.get(t.uuid)||null;e;)bt.delete(e.node.uuid),e=e.parent}var At=[{name:"CC_USE_SKINNING",value:!0},{name:"CC_USE_REAL_TIME_JOINT_TEXTURE",value:!1}],kt=[{name:"CC_USE_SKINNING",value:!0},{name:"CC_USE_REAL_TIME_JOINT_TEXTURE",value:!0}];function Bt(t,e,i,n){for(var r=0;r<i.length;r++){for(var o=i[r],s=-1,a=0;a<o.length;a++)if(o[a]===n){s=a;break}s>=0&&(e.push(r),t.push(s))}}var Nt=new d,Et=new d,St=new d,Rt=new d,jt=new f,Pt=new c,Ot=function(){this._format=tt.RGBA32F,this._textures=[],this._buffers=[]};Ot.WIDTH=256,Ot.HEIGHT=3;var Ct,Dt,Ft,Ut,Jt,Ht,zt,Gt,Lt,Wt,Vt,Kt,Xt,Yt,qt,Zt,Qt,$t,te,ee,ie,ne,re,oe,se,ae,ue,le,fe,he,de,ce,pe=function(t){function e(){var e;return(e=t.call(this)||this)._buffers=[],e._dataArray=[],e._joints=[],e._bufferIndices=null,e._realTimeJointTexture=new Ot,e._realTimeTextureMode=!1,e.type=X.SKINNING,e}n(e,t);var i=e.prototype;return i.destroy=function(){if(this.bindSkeleton(),this._buffers.length){for(var e=0;e<this._buffers.length;e++)this._buffers[e].destroy();this._buffers.length=0}this._dataArray.length=0,this._realTimeJointTexture._textures.forEach((function(t){t.destroy()})),this._realTimeJointTexture._textures.length=0,this._realTimeJointTexture._buffers.length=0,t.prototype.destroy.call(this)},i.uploadAnimation=function(){},i.bindSkeleton=function(t,e,i){void 0===t&&(t=null),void 0===e&&(e=null),void 0===i&&(i=null);for(var n=0;n<this._joints.length;n++)It(this._joints[n].target);if(this._bufferIndices=null,this._joints.length=0,t&&e&&i){this._realTimeTextureMode=!1,G.JOINT_UNIFORM_CAPACITY<t.joints.length&&(this._realTimeTextureMode=!0),this.transform=e;var r=i.getBoneSpaceBounds(t),o=i.struct.jointMaps;this._ensureEnoughBuffers(o&&o.length||1),this._bufferIndices=i.jointBufferIndices,this._initRealTimeJointTexture();for(var s=0;s<t.joints.length;s++){var a=r[s],u=e.getChildByPath(t.joints[s]);if(a&&u){var l=wt(u,e),f=t.bindposes[s],h=[],d=[];o?Bt(h,d,o,s):(h.push(s),d.push(0)),this._joints.push({indices:h,buffers:d,bound:a,target:u,bindpose:f,transform:l})}}}},i.updateTransform=function(t){var e=this.transform;(e.hasChangedFlags||e.isTransformDirty())&&(e.updateWorldTransform(),this._localDataUpdated=!0),d.set(Nt,1/0,1/0,1/0),d.set(Et,-1/0,-1/0,-1/0);for(var i=0;i<this._joints.length;i++){var n=this._joints[i],r=n.bound,o=Mt(n.transform,t);c.transform(Pt,r,o),Pt.getBoundary(St,Rt),d.min(Nt,Nt,St),d.max(Et,Et,Rt)}var s=this._worldBounds;this._modelBounds&&s&&(c.fromPoints(this._modelBounds,Nt,Et),this._modelBounds.transform(e._mat,e._pos,e._rot,e._scale,this._worldBounds))},i.updateUBOs=function(e){t.prototype.updateUBOs.call(this,e);for(var i=0;i<this._joints.length;i++){var n=this._joints[i],r=n.indices,o=n.buffers,s=n.transform,a=n.bindpose;f.multiply(jt,s.world,a);for(var u=0;u<o.length;u++)ft(this._dataArray[o[u]],12*r[u],jt)}if(this._realTimeTextureMode)this._updateRealTimeJointTextureBuffer();else for(var l=0;l<this._buffers.length;l++)this._buffers[l].update(this._dataArray[l]);return!0},i.initSubModel=function(e,i,n){var r=i.vertexBuffers,o=i.iaInfo;o.vertexBuffers=i.jointMappedBuffers,t.prototype.initSubModel.call(this,e,i,n),o.vertexBuffers=r},i.getMacroPatches=function(e){var i=t.prototype.getMacroPatches.call(this,e),n=At;return this._realTimeTextureMode&&(n=kt),i?n.concat(i):n},i._updateLocalDescriptors=function(e,i){t.prototype._updateLocalDescriptors.call(this,e,i);var n=this._bufferIndices[e];if(this._realTimeTextureMode)this._bindRealTimeJointTexture(n,i);else{var r=this._buffers[n];r&&i.bindBuffer(G.BINDING,r)}},i._updateInstancedAttributes=function(e,i){i.passes[0].batchingScheme!==M.NONE&&r(3936,this.node.getPathInHierarchy()),t.prototype._updateInstancedAttributes.call(this,e,i)},i._ensureEnoughBuffers=function(t){if(this._buffers.length){for(var e=0;e<this._buffers.length;e++)this._buffers[e].destroy();this._buffers.length=0}if(this._dataArray.length&&(this._dataArray.length=0),this._realTimeTextureMode)for(var i=0;i<t;i++){var n=Ot.WIDTH;this._dataArray[i]=new Float32Array(12*n)}else for(var r=0;r<t;r++){this._buffers[r]=this._device.createBuffer(new O(C.UNIFORM|C.TRANSFER_DST,D.HOST|D.DEVICE,G.SIZE,G.SIZE));var o=G.JOINT_UNIFORM_CAPACITY;this._dataArray[r]=new Float32Array(12*o)}},i._initRealTimeJointTexture=function(){if(this._realTimeJointTexture._textures.length&&(this._realTimeJointTexture._textures.forEach((function(t){t.destroy()})),this._realTimeJointTexture._textures.length=0),this._realTimeJointTexture._buffers.length=0,this._realTimeTextureMode){var t=$.root.device,e=Ot.WIDTH,i=Ot.HEIGHT;!(t.getFormatFeatures(R.RGBA32F)&j.SAMPLED_TEXTURE)&&(this._realTimeJointTexture._format=tt.RGBA8888,e=4*Ot.WIDTH);for(var n=this._realTimeJointTexture._textures,r=this._realTimeJointTexture._buffers,o=this._realTimeJointTexture._format,s=0;s<this._dataArray.length;s++){r[s]=new Float32Array(4*Ot.HEIGHT*Ot.WIDTH);var a=r[s],u=o===tt.RGBA32F?a:new Uint8Array(a.buffer),l=new et({width:e,height:i,_data:u,_compressed:!1,format:o}),f=new w;f.setFilters(it.NEAREST,it.NEAREST),f.setMipFilter(it.NONE),f.setWrapMode(nt.CLAMP_TO_EDGE,nt.CLAMP_TO_EDGE,nt.CLAMP_TO_EDGE),f.image=l,n[s]=f}}},i._bindRealTimeJointTexture=function(t,e){if(this._realTimeTextureMode){var i=this._realTimeJointTexture._textures[t];if(i){var n=i.getGFXTexture(),r=i.getGFXSampler();e.bindTexture(L,n),e.bindSampler(L,r)}}},i._updateRealTimeJointTextureBuffer=function(){if(this._realTimeTextureMode)for(var t=this._realTimeJointTexture._textures,e=this._realTimeJointTexture._buffers,i=0;i<t.length;i++){for(var n=e[i],r=this._dataArray[i],o=r.length/12,s=0,a=0,u=0;u<o;u++)a=4*u,n[a++]=r[s++],n[a++]=r[s++],n[a++]=r[s++],n[a++]=r[s++],a=4*(u+Ot.WIDTH),n[a++]=r[s++],n[a++]=r[s++],n[a++]=r[s++],n[a++]=r[s++],a=4*(u+2*Ot.WIDTH),n[a++]=r[s++],n[a++]=r[s++],n[a++]=r[s++],n[a++]=r[s++];var l=this._realTimeJointTexture._format===tt.RGBA32F?n:new Uint8Array(n.buffer);t[i].uploadData(l)}},e}(k),_e=[{name:"CC_USE_SKINNING",value:!0},{name:"CC_USE_BAKED_ANIMATION",value:!0}],me=function(t){function e(){var e;(e=t.call(this)||this).uploadedAnim=void 0,e._jointsMedium=void 0,e._skeleton=null,e._mesh=null,e._dataPoolManager=void 0,e._instAnimInfoIdx=-1,e.type=X.BAKED_SKINNING,e._dataPoolManager=x.director.root.dataPoolManager;var i=new Float32Array(4),n=e._dataPoolManager.jointAnimationInfo.getData();return e._jointsMedium={buffer:null,jointTextureInfo:i,animInfo:n,texture:null,boundsInfo:null},e}n(e,t);var i=e.prototype;return i.destroy=function(){this.uploadedAnim=void 0,this._jointsMedium.boundsInfo=null,this._jointsMedium.buffer&&(this._jointsMedium.buffer.destroy(),this._jointsMedium.buffer=null),this._applyJointTexture(),t.prototype.destroy.call(this)},i.bindSkeleton=function(t,e,i){if(void 0===t&&(t=null),void 0===e&&(e=null),void 0===i&&(i=null),this._skeleton=t,this._mesh=i,t&&e&&i){this.transform=e;var n=this._dataPoolManager;this._jointsMedium.animInfo=n.jointAnimationInfo.getData(e.uuid),this._jointsMedium.buffer||(this._jointsMedium.buffer=this._device.createBuffer(new O(C.UNIFORM|C.TRANSFER_DST,D.DEVICE,W.SIZE,W.SIZE)))}},i.updateTransform=function(e){if(t.prototype.updateTransform.call(this,e),this.uploadedAnim){var i=this._jointsMedium,n=i.animInfo,r=i.boundsInfo[n.data[0]],o=this._worldBounds;if(o&&r){var s=this.transform;r.transform(s._mat,s._pos,s._rot,s._scale,o)}}},i.updateUBOs=function(e){t.prototype.updateUBOs.call(this,e);for(var i=this._jointsMedium.animInfo,n=!1,r=this._instAnimInfoIdx,o=0;o<this._subModels.length;o++){var s=this._subModels[o];r>=0?s.instancedAttributeBlock.views[r][0]=i.data[0]:n=!0}return n&&i.dirty&&(i.buffer.update(i.data),i.dirty=!1),!0},i.getMacroPatches=function(e){var i=t.prototype.getMacroPatches.call(this,e);return i?i.concat(_e):_e},i.uploadAnimation=function(t){if(this._skeleton&&this._mesh&&this.uploadedAnim!==t){this.uploadedAnim=t;var e=this._dataPoolManager,i=null;t?(i=e.jointTexturePool.getSequencePoseTexture(this._skeleton,t,this._mesh,this.transform),this._jointsMedium.boundsInfo=i&&i.bounds.get(this._mesh.hash),this._modelBounds=null):(i=e.jointTexturePool.getDefaultPoseTexture(this._skeleton,this._mesh,this.transform),this._jointsMedium.boundsInfo=null,this._modelBounds=i&&i.bounds.get(this._mesh.hash)[0]),this._applyJointTexture(i)}},i._applyJointTexture=function(t){void 0===t&&(t=null);var e=this._jointsMedium.texture;if(e&&e!==t&&this._dataPoolManager.jointTexturePool.releaseHandle(e),this._jointsMedium.texture=t,t){var i=this._jointsMedium,n=i.buffer,r=i.jointTextureInfo;r[0]=t.handle.texture.width,r[1]=this._skeleton.joints.length,r[2]=t.pixelOffset+.1,r[3]=1/r[0],this.updateInstancedJointTextureInfo(),n&&n.update(r);for(var o=t.handle.texture,s=0;s<this._subModels.length;++s)this._subModels[s].descriptorSet.bindTexture(V,o)}},i._updateLocalDescriptors=function(e,i){t.prototype._updateLocalDescriptors.call(this,e,i);var n=this._jointsMedium,r=n.buffer,o=n.texture,s=n.animInfo;if(i.bindBuffer(W.BINDING,r),i.bindBuffer(z.BINDING,s.buffer),o){var a=this._device.getSampler(lt);i.bindTexture(V,o.handle.texture),i.bindSampler(V,a)}},i._updateInstancedAttributes=function(e,i){t.prototype._updateInstancedAttributes.call(this,e,i),this._instAnimInfoIdx=i.getInstancedAttributeIndex(K),this.updateInstancedJointTextureInfo()},i.updateInstancedJointTextureInfo=function(){for(var t=this._jointsMedium,e=t.jointTextureInfo,i=t.animInfo,n=this._instAnimInfoIdx,r=0;r<this._subModels.length;r++){var o=this._subModels[r].instancedAttributeBlock.views;if(n>=0&&o.length>0){var s=o[n];s[0]=i.data[0],s[1]=e[1],s[2]=e[2]}}},e}(k),ve=t("b",(Ct=p("cc.SkinnedMeshRenderer"),Dt=v(100),Ft=_(b),Ut=_(I),Jt=_(b),Ht=_(I),Ct(zt=Dt((Gt=function(t){function i(){var e;return(e=t.call(this)||this)._skeleton=Lt&&Lt(),e._skinningRoot=Wt&&Wt(),e._clip=null,e.associatedAnimation=null,e._modelType=me,e}n(i,t);var r=i.prototype;return r.onLoad=function(){t.prototype.onLoad.call(this),this._tryBindAnimation()},r.onDestroy=function(){this.associatedAnimation&&(this.associatedAnimation.notifySkinnedMeshRemoved(this),o(null===this.associatedAnimation)),t.prototype.onDestroy.call(this)},r.uploadAnimation=function(t){this._clip=t,this.model&&this.model.uploadAnimation&&this.model.uploadAnimation(t)},r.setUseBakedAnimation=function(t,e){void 0===t&&(t=!0),void 0===e&&(e=!1);var i=t?me:pe;(e||this._modelType!==i)&&(this._modelType=i,this._model&&(x.director.root.destroyModel(this._model),this._model=null,this._models.length=0,this._updateModels(),this._updateCastShadow(),this._updateReceiveShadow(),this._updateUseLightProbe(),this.enabledInHierarchy&&this._attachToScene()))},r.setSharedMaterial=function(e,i){t.prototype.setSharedMaterial.call(this,e,i),this._modelType===pe&&this.getMaterialInstance(i)},r._updateModelParams=function(){this._update(),t.prototype._updateModelParams.call(this)},r._tryBindAnimation=function(){var t=this._skinningRoot;if(t){for(var e=!1,i=this.node;i;i=i.parent)if(i===t){e=!0;break}if(e){var n=t.getComponent("cc.SkeletalAnimation");n&&n.enabledInHierarchy?n.notifySkinnedMeshAdded(this):this.setUseBakedAnimation(!1)}}},r._update=function(){this.model&&(this.model.bindSkeleton(this._skeleton,this._skinningRoot,this._mesh),this.model.uploadAnimation&&this.model.uploadAnimation(this._clip))},e(i,[{key:"skeleton",get:function(){return this._skeleton},set:function(t){t!==this._skeleton&&(this._skeleton=t,this._update())}},{key:"skinningRoot",get:function(){return this._skinningRoot},set:function(t){t!==this._skinningRoot&&(this._skinningRoot=t,this._tryBindAnimation(),this._update())}},{key:"model",get:function(){return this._model}}]),i}(B),Lt=m(Gt.prototype,"_skeleton",[Ft],(function(){return null})),Wt=m(Gt.prototype,"_skinningRoot",[Ut],(function(){return null})),s(Gt.prototype,"skeleton",[Jt],Object.getOwnPropertyDescriptor(Gt.prototype,"skeleton"),Gt.prototype),s(Gt.prototype,"skinningRoot",[Ht],Object.getOwnPropertyDescriptor(Gt.prototype,"skinningRoot"),Gt.prototype),zt=Gt))||zt)||zt)),Te=new F(U.ATTR_BATCH_ID,R.R32F),ge=new F(U.ATTR_BATCH_UV,R.RG32F),ye=P[Te.format].size+P[ge.format].size,xe=t("i",(Vt=p("cc.SkinnedMeshUnit"),Kt=_(rt),Xt=_(b),Yt=_(A),qt=_(ve),Vt((Qt=function(){function t(){this.mesh=$t&&$t(),this.skeleton=te&&te(),this.material=ee&&ee(),this._localTransform=ie&&ie(),this._offset=ne&&ne(),this._size=re&&re()}return e(t,[{key:"offset",get:function(){return this._offset},set:function(t){g.copy(this._offset,t)}},{key:"size",get:function(){return this._size},set:function(t){g.copy(this._size,t)}},{key:"copyFrom",get:function(){return null},set:function(t){t&&(this.mesh=t.mesh,this.skeleton=t.skeleton,this.material=t.getSharedMaterial(0),t.skinningRoot&&ut(t.node,t.skinningRoot,this._localTransform))}}]),t}(),$t=m(Qt.prototype,"mesh",[Kt],(function(){return null})),te=m(Qt.prototype,"skeleton",[Xt],(function(){return null})),ee=m(Qt.prototype,"material",[Yt],(function(){return null})),ie=m(Qt.prototype,"_localTransform",[y],(function(){return new f})),ne=m(Qt.prototype,"_offset",[y],(function(){return new g(0,0)})),re=m(Qt.prototype,"_size",[y],(function(){return new g(1,1)})),s(Qt.prototype,"copyFrom",[qt],Object.getOwnPropertyDescriptor(Qt.prototype,"copyFrom"),Qt.prototype),Zt=Qt))||Zt)),be=new f;new f;var Me=new d,we=t("h",(oe=p("cc.SkinnedMeshBatchRenderer"),se=v(100),ae=_([u]),ue=_([xe]),oe(le=se((fe=function(t){function i(){var e;return(e=t.call(this)||this).atlasSize=he&&he(),e.batchableTextureNames=de&&de(),e.units=ce&&ce(),e._textures={},e._batchMaterial=null,e}n(i,t);var r=i.prototype;return r.onLoad=function(){t.prototype.onLoad.call(this),this.cook()},r.onDestroy=function(){for(var e in this._textures)this._textures[e].destroy();this._textures={},this._mesh&&(this._mesh.destroy(),this._mesh=null),t.prototype.onDestroy.call(this)},r._onMaterialModified=function(e){this.cookMaterials(),t.prototype._onMaterialModified.call(this,e,this.getMaterialInstance(e))},r.cook=function(){this.cookMaterials(),this.cookSkeletons(),this.cookMeshes()},r.cookMaterials=function(){var t=this;this._batchMaterial||(this._batchMaterial=this.getSharedMaterial(0));var e=this.getMaterialInstance(0);if(e&&this._batchMaterial&&this._batchMaterial.effectAsset){e.copy(this._batchMaterial),this.resizeAtlases();for(var i=e.effectAsset.techniques[e.technique],n=function(n){var r=i.passes[n];if(!r.properties)return 1;var o=function(i){if(r.properties[i].type>=H.SAMPLER1D){var o=null;t.batchableTextureNames.find((function(t){return t===i}))?((o=t._textures[i])||(o=t.createTexture(i)),t.cookTextures(o,i,n)):t.units.some((function(t){return o=t.material&&t.material.getProperty(i,n)})),o&&e.setProperty(i,o,n)}else{for(var s=[],a=0;a<t.units.length;a++){var u=t.units[a];u.material&&s.push(u.material.getProperty(i.slice(0,-3),n))}e.setProperty(i,s,n)}};for(var s in r.properties)o(s)},r=0;r<i.passes.length;r++)n(r)}else a("incomplete batch material!")},r.cookSkeletons=function(){if(this._skinningRoot){for(var t=[],e=[],i=0;i<this.units.length;i++){var n=this.units[i];if(n&&n.skeleton){var r=n.skeleton;f.invert(be,n._localTransform);for(var o=function(){var i=r.joints[s];if(t.findIndex((function(t){return t===i}))>=0)return 1;t.push(i),e.push(f.multiply(new f,r.bindposes[s]||f.IDENTITY,be))},s=0;s<r.joints.length;s++)o()}}var u=Array.from(Array(t.length).keys()).sort((function(e,i){return t[e]>t[i]?1:t[e]<t[i]?-1:0})),l=new b;l.joints=t.map((function(t,e,i){return i[u[e]]})),l.bindposes=e.map((function(t,e,i){return i[u[e]]})),this._skeleton&&this._skeleton.destroy(),this.skeleton=l}else a("no skinning root specified!")},r.cookMeshes=function(){for(var t=this,e=!1,i=0;i<this.units.length;i++)if(this.units[i].mesh){e=!0;break}if(e&&this._skinningRoot){this._mesh?this._mesh.destroyRenderingMesh():this._mesh=new rt;for(var n=0,r=R.UNKNOWN,o=0,s=R.UNKNOWN,a=0,u=R.UNKNOWN,l=0,h=R.UNKNOWN,c=0,p=R.UNKNOWN,_=new Array(this.units.length),m=this.units.length,v=0;v<m;v++){var T=this.units[v];T&&T.skeleton&&(_[v]=T.skeleton.joints.map((function(e){return t._skeleton.joints.findIndex((function(t){return e===t}))})))}for(var g=function(){var e=t.units[y];if(!e||!e.mesh||!e.mesh.data)return 1;var i=t._createUnitMesh(y,e.mesh),m=new DataView(i.data.buffer);f.invert(be,e._localTransform),f.transpose(be,be);for(var v=e.offset,T=e.size,g=function(){var t=i.struct.vertexBundles[x];n=t.view.offset,r=R.UNKNOWN;for(var f=0;f<t.attributes.length;f++){var g=t.attributes[f];if(g.name===U.ATTR_POSITION){r=g.format;break}n+=P[g.format].size}if(r){for(var b=q(m,r,n,t.view.length,t.view.stride),M=0;M<b.length;M+=3)d.fromArray(Me,b,M),d.transformMat4(Me,Me,e._localTransform),d.toArray(b,Me,M);Z(m,b,r,n,t.view.stride)}o=t.view.offset,s=R.UNKNOWN;for(var w=0;w<t.attributes.length;w++){var I=t.attributes[w];if(I.name===U.ATTR_NORMAL){s=I.format;break}o+=P[I.format].size}if(s){for(var A=q(m,s,o,t.view.length,t.view.stride),k=0;k<A.length;k+=3)d.fromArray(Me,A,k),d.transformMat4Normal(Me,Me,be),d.toArray(A,Me,k);Z(m,A,s,o,t.view.stride)}a=t.view.offset,u=R.UNKNOWN;for(var B=0;B<t.attributes.length;B++){var N=t.attributes[B];if(N.name===U.ATTR_TANGENT){u=N.format;break}a+=P[N.format].size}if(u){for(var E=q(m,u,a,t.view.length,t.view.stride),S=0;S<E.length;S+=3)d.fromArray(Me,E,S),d.transformMat4Normal(Me,Me,be),d.toArray(E,Me,S);Z(m,E,u,a,t.view.stride)}l=t.view.offset,h=R.UNKNOWN;for(var j=0;j<t.attributes.length;j++){var O=t.attributes[j];if(O.name===U.ATTR_BATCH_UV){h=O.format;break}l+=P[O.format].size}h&&Q(m,(function(t,e){var i,n=0===e?"x":"y";return(t=(i=t)-Math.floor(i))*T[n]+v[n]}),h,l,t.view.length,t.view.stride,m);var C=_[y];if(!C)return 1;c=t.view.offset,p=R.UNKNOWN;for(var D=0;D<t.attributes.length;D++){var F=t.attributes[D];if(F.name===U.ATTR_JOINTS){p=F.format;break}c+=P[F.format].size}p&&Q(m,(function(t){return C[t]}),p,c,t.view.length,t.view.stride,m)},x=0;x<i.struct.vertexBundles.length;x++)g();t._mesh.merge(i)},y=0;y<m;y++)g();this._onMeshChanged(this._mesh),this._updateModels()}},r.cookTextures=function(t,e,i){for(var n=[],r=[],o=[],s=[],a=0;a<this.units.length;a++){var u=this.units[a];if(u.material){var l=u.material.getProperty(e,i);if(l&&l.image&&l.image.data){var f=new J;f.texOffset.x=u.offset.x*this.atlasSize,f.texOffset.y=u.offset.y*this.atlasSize,f.texExtent.width=u.size.x*this.atlasSize,f.texExtent.height=u.size.y*this.atlasSize;var h=l.image.data;ArrayBuffer.isView(h)?(o.push(h),s.push(f)):(n.push(h),r.push(f))}}}var d=t.getGFXTexture(),c=x.director.root.device;o.length>0&&c.copyBuffersToTexture(o,d,s),n.length>0&&c.copyTexImagesToTexture(n,d,r)},r.createTexture=function(t){var e=new w;return e.setFilters(it.LINEAR,it.LINEAR),e.setMipFilter(it.NEAREST),e.reset({width:this.atlasSize,height:this.atlasSize,format:tt.RGBA8888}),this._textures[t]=e,e},r.resizeAtlases=function(){for(var t in this._textures)this._textures[t].reset({width:this.atlasSize,height:this.atlasSize,format:tt.RGBA8888})},r._createUnitMesh=function(t,e){for(var i=JSON.parse(JSON.stringify(e.struct)),n={},r=0;r<e.struct.primitives.length;r++){for(var o=e.struct.primitives[r],s=0,a=R.UNKNOWN,u=0;u<o.vertexBundelIndices.length;u++){var l=e.struct.vertexBundles[o.vertexBundelIndices[u]];s=l.view.offset,a=R.UNKNOWN;for(var f=0;f<l.attributes.length;f++){var h=l.attributes[f];if(h.name===U.ATTR_TEX_COORD){a=h.format;break}s+=P[h.format].size}if(a)break}if(void 0===n[u]){n[u]=[a,s];var d=i.vertexBundles[u];d.attributes.push(Te),d.attributes.push(ge),d.view.offset=0,d.view.length+=d.view.count*ye,d.view.stride+=ye}}for(var c=0,p=0;p<i.vertexBundles.length;p++)c+=i.vertexBundles[p].view.length;for(var _=0;_<i.primitives.length;_++){var m=i.primitives[_];m.indexView&&(m.indexView.offset=c,c+=m.indexView.length)}var v=new Uint8Array(c),T=e.data,g=new DataView(v.buffer),y=new DataView(T.buffer),b=x.sys.isLittleEndian;for(var M in n)for(var w=i.vertexBundles[M],I=e.struct.vertexBundles[M],A=n[M],k=A[0],B=A[1],N=q(y,k,B,I.view.length,I.view.stride),E=I.view,S=w.view,j=E.stride,O=S.stride,C=E.offset,D=S.offset,F=0;F<S.count;F++){var J=T.subarray(C,C+j);v.set(J,D),g.setFloat32(D+j,t),g.setFloat32(D+j+4,N[2*F],b),g.setFloat32(D+j+8,N[2*F+1],b),D+=O,C+=j}for(var H=0;H<i.primitives.length;H++){var z=e.struct.primitives[H],G=i.primitives[H];if(z.indexView&&G.indexView)for(var L=z.indexView.stride,W=G.indexView.stride,V=z.indexView.offset,K=G.indexView.offset,X=0;X<G.indexView.count;X++){var Y=T.subarray(V,V+L);v.set(Y,K),K+=W,V+=L}}var Z=new rt;return Z.reset({struct:i,data:v}),Z},e(i,[{key:"mesh",get:function(){return t.prototype.mesh},set:function(t){this.mesh=t}},{key:"skeleton",get:function(){return t.prototype.skeleton},set:function(t){this.skeleton=t}}]),i}(ve),he=m(fe.prototype,"atlasSize",[y],(function(){return 1024})),de=m(fe.prototype,"batchableTextureNames",[ae,y],(function(){return[]})),ce=m(fe.prototype,"units",[ue,y],(function(){return[]})),s(fe.prototype,"mesh",[T],Object.getOwnPropertyDescriptor(fe.prototype,"mesh"),fe.prototype),s(fe.prototype,"skeleton",[T],Object.getOwnPropertyDescriptor(fe.prototype,"skeleton"),fe.prototype),le=fe))||le)||le));x.SkinningModelComponent=ve,l(ve,"cc.SkinningModelComponent"),x.SkinningModelUnit=xe,l(xe,"cc.SkinningModelUnit"),x.BatchedSkinningModelComponent=we,l(we,"cc.BatchedSkinningModelComponent")}}}));
