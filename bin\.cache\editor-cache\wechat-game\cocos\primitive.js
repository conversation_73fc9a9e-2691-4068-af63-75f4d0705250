System.register(["./device-manager-BvjvoelW.js","./buffer-barrier-CuX_5NUR.js","./capsule-sT4rQfGi.js","./index-C5lmLqDW.js","./global-exports-CR3GRnjt.js","./gc-object-CKHc4SnS.js","./prefab-DH0xadMc.js","./create-mesh-hkbGggH3.js","./rendering-sub-mesh-CowWLfXC.js","./mesh-C8knhDLk.js","./scene-7MDSMR3j.js","./pipeline-state-manager-Cdpe3is6.js","./component-BaGvu7EF.js","./node-event-DTNosVQv.js","./touch-DB0AR-Sc.js","./factory-D9_8ZCqM.js","./debug-view-CKetkq9d.js","./wasm-minigame-DoCiKH-Y.js","./deprecated-T2yM9nsJ.js","./director-DIlqD2Nd.js","./deprecated-Ca3AjUwj.js","./zlib.min-CyXMsivM.js"],(function(n){"use strict";var i,t,o,r,s,e,a,u,v,l,c,f,d,h,p,m,M;return{setters:[null,function(n){i=n.P},function(n){t=n.c,o=n.a,r=n.b,s=n.d,e=n.p},function(n){a=n.b,u=n.c,v=n.t,l=n.a,c=n.s},function(n){f=n.c,d=n.l},function(n){h=n.E,p=n._},null,function(n){m=n._},null,function(n){M=n.M},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){function P(n){return(n=o(n)).segments=64,n}var g,y,x,j,b,E,z,I,L=Object.freeze({__proto__:null,applyDefaultGeometryOptions:o,box:r,capsule:s,circle:function(n){var t=P(n).segments,o=new Array(3*(t+1));o[0]=0,o[1]=0,o[2]=0;var r=new Array(1+2*t);r[0]=0;for(var s=2*Math.PI/t,e=0;e<t;++e){var a=s*e,u=Math.cos(a),v=Math.sin(a),l=3*(e+1);o[l+0]=u,o[l+1]=v,o[l+2]=0;var c=2*e;r[1+c]=e+1,r[1+(c+1)]=e+2}return t>0&&(r[r.length-1]=1),{positions:o,indices:r,minPos:{x:1,y:1,z:0},maxPos:{x:-1,y:-1,z:0},boundingRadius:1,primitiveMode:i.TRIANGLE_FAN}},cone:function(n,i,o){return void 0===n&&(n=.5),void 0===i&&(i=1),void 0===o&&(o={}),t(0,n,i,o)},cylinder:t,invWinding:function(n){for(var i=[],t=0;t<n.length;t+=3)i.push(n[t],n[t+2],n[t+1]);return i},normals:function(n,i,t){void 0===t&&(t=1);for(var o=new Array(2*n.length),r=0;r<n.length/3;++r){var s=3*r,e=6*r;o[e+0]=n[s+0],o[e+1]=n[s+1],o[e+2]=n[s+2],o[e+3]=n[s+0]+i[s+0]*t,o[e+4]=n[s+1]+i[s+1]*t,o[e+5]=n[s+2]+i[s+2]*t}return o},plane:e,quad:function(n){var i=o(n),t={positions:[-.5,-.5,0,-.5,.5,0,.5,.5,0,.5,-.5,0],indices:[0,3,1,3,2,1],minPos:{x:-.5,y:-.5,z:0},maxPos:{x:.5,y:.5,z:0},boundingRadius:Math.sqrt(.5)};return!1!==i.includeNormal&&(t.normals=[0,0,1,0,0,1,0,0,1,0,0,1]),!1!==i.includeUV&&(t.uvs=[0,0,0,1,1,1,1,0]),t},scale:function(n,i){for(var t,o,r,s=null!==(t=i.x)&&void 0!==t?t:1,e=null!==(o=i.y)&&void 0!==o?o:1,a=null!==(r=i.z)&&void 0!==r?r:1,u=Math.floor(n.positions.length/3),v=0;v<u;++v){var l=3*v,c=3*v+1,f=3*v+2;n.positions[l]*=s,n.positions[c]*=e,n.positions[f]*=a}var d=n.minPos,h=n.maxPos;if(d&&(d.x*=s,d.y*=e,d.z*=a),h&&(h.x*=s,h.y*=e,h.z*=a),d&&h){if(s<0){var p=d.x;d.x=h.x,h.x=p}if(e<0){var m=d.y;d.y=h.y,h.y=m}if(a<0){var M=d.z;d.z=h.z,h.z=M}}return void 0!==n.boundingRadius&&(n.boundingRadius*=Math.max(Math.max(Math.abs(s),Math.abs(e)),Math.abs(a))),n},sphere:function(n,i){void 0===n&&(n=.5),void 0===i&&(i={});for(var t=void 0!==i.segments?i.segments:32,o=[],r=[],s=[],e=[],u=new a(-n,-n,-n),v=new a(n,n,n),l=n,c=0;c<=t;++c)for(var f=c*Math.PI/t,d=Math.sin(f),h=-Math.cos(f),p=0;p<=t;++p){var m=2*p*Math.PI/t-Math.PI/2,M=Math.sin(m)*d,P=h,g=Math.cos(m)*d,y=p/t,x=c/t;if(o.push(M*n,P*n,g*n),r.push(M,P,g),s.push(y,x),c<t&&p<t){var j=t+1,b=j*c+p,E=j*(c+1)+p,z=j*(c+1)+p+1,I=j*c+p+1;e.push(b,I,E),e.push(I,z,E)}}return{positions:o,indices:e,normals:r,uvs:s,minPos:u,maxPos:v,boundingRadius:l}},toWavefrontOBJ:function(n,t){if(void 0===t&&(t=1),!n.indices||!n.uvs||!n.normals||void 0!==n.primitiveMode&&n.primitiveMode!==i.TRIANGLE_LIST)return"";for(var o=n.positions,r=n.uvs,s=n.normals,e=n.indices,a=function(n){return e[n]+1+"/"+(e[n]+1)+"/"+(e[n]+1)},u="",v=0;v<o.length;v+=3)u+="v "+o[v]*t+" "+o[v+1]*t+" "+o[v+2]*t+"\n";for(var l=0;l<r.length;l+=2)u+="vt "+r[l]+" "+r[l+1]+"\n";for(var c=0;c<s.length;c+=3)u+="vn "+s[c]+" "+s[c+1]+" "+s[c+2]+"\n";for(var f=0;f<e.length;f+=3)u+="f "+a(f)+" "+a(f+1)+" "+a(f+2)+"\n";return u},torus:function(n,i,t){void 0===n&&(n=.4),void 0===i&&(i=.1),void 0===t&&(t={});for(var o=t.radialSegments||32,r=t.tubularSegments||32,s=t.arc||2*Math.PI,e=[],u=[],v=[],l=[],c=new a(-n-i,-i,-n-i),f=new a(n+i,i,n+i),d=n+i,h=0;h<=o;h++)for(var p=0;p<=r;p++){var m=p/r,M=h/o,P=m*s,g=M*Math.PI*2,y=(n+i*Math.cos(g))*Math.sin(P),x=i*Math.sin(g),j=(n+i*Math.cos(g))*Math.cos(P),b=Math.sin(P)*Math.cos(g),E=Math.sin(g),z=Math.cos(P)*Math.cos(g);if(e.push(y,x,j),u.push(b,E,z),v.push(m,M),p<r&&h<o){var I=r+1,L=I*h+p,R=I*(h+1)+p,A=I*(h+1)+p+1,w=I*h+p+1;l.push(L,w,R),l.push(w,A,R)}}return{positions:e,normals:u,uvs:v,indices:l,minPos:c,maxPos:f,boundingRadius:d}},translate:function(n,i){for(var t=i.x||0,o=i.y||0,r=i.z||0,s=Math.floor(n.positions.length/3),e=0;e<s;++e){var a=3*e,u=3*e+1,v=3*e+2;n.positions[a]+=t,n.positions[u]+=o,n.positions[v]+=r}return n.minPos&&(n.minPos.x+=t,n.minPos.y+=o,n.minPos.z+=r),n.maxPos&&(n.maxPos.x+=t,n.maxPos.y+=o,n.maxPos.z+=r),n},wireframe:function(n){for(var i=[[0,1],[1,2],[2,0]],t=[],o={},r=0;r<n.length;r+=3)for(var s=0;s<3;++s){var e=n[r+i[s][0]],a=n[r+i[s][1]],u=e>a?a<<16|e:e<<16|a;void 0===o[u]&&(o[u]=0,t.push(e,a))}return t},wireframed:function(n){var t=n.indices;if(!t)return n;if(n.primitiveMode&&n.primitiveMode!==i.TRIANGLE_LIST)return n;for(var o=[[0,1],[1,2],[2,0]],r=[],s={},e=0;e<t.length;e+=3)for(var a=0;a<3;++a){var u=t[e+o[a][0]],v=t[e+o[a][1]],l=u>v?v<<16|u:u<<16|v;void 0===s[l]&&(s[l]=0,r.push(u,v))}return n.indices=r,n.primitiveMode=i.LINE_LIST,n}});n("primitives",L),function(n){n[n.BOX=0]="BOX",n[n.SPHERE=1]="SPHERE",n[n.CYLINDER=2]="CYLINDER",n[n.CONE=3]="CONE",n[n.CAPSULE=4]="CAPSULE",n[n.TORUS=5]="TORUS",n[n.PLANE=6]="PLANE",n[n.QUAD=7]="QUAD"}(I||(I={})),h(I);var R=n("Primitive",(g=u("cc.Primitive"),y=v(I),g((z=function(n){function i(i){var t;return void 0===i&&(i=I.BOX),(t=n.call(this)||this).type=b&&b(),t.info=E&&E(),t.type=i,t}return p(i,n),i.prototype.onLoaded=function(){var n=L[I[this.type].toLowerCase()];m(n(this.info),this)},i}(M),z.PrimitiveType=I,b=l((j=z).prototype,"type",[y],(function(){return I.BOX})),E=l(j.prototype,"info",[c],(function(){return{}})),x=j))||x));f.Primitive=R,d.primitives=L}}}));
